#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

######################################################################################################
#
# Here you can configure the rules for the proxy.
# This example is configuration of shadow rule.
#
######################################################################################################
#
#databaseName: shadow_db
#
#dataSources:
#  ds:
#    url: ******************************************
#    username: postgres
#    password: postgres
#    connectionTimeoutMilliseconds: 30000
#    idleTimeoutMilliseconds: 60000
#    maxLifetimeMilliseconds: 1800000
#    maxPoolSize: 50
#    minPoolSize: 1
#  shadow_ds:
#    url: ******************************************
#    username: postgres
#    password: postgres
#    connectionTimeoutMilliseconds: 30000
#    idleTimeoutMilliseconds: 60000
#    maxLifetimeMilliseconds: 1800000
#    maxPoolSize: 50
#    minPoolSize: 1
#
#rules:
#- !SHADOW
#  dataSources:
#    shadowDataSource:
#      productionDataSourceName: ds
#      shadowDataSourceName: shadow_ds
#  tables:
#    t_order:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_select_match_algorithm
#    t_order_item:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_update_match_algorithm
#        - user_id_select_match_algorithm
#    t_address:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_select_match_algorithm
#        - sql_hint_algorithm
#  shadowAlgorithms:
#    user_id_insert_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: insert
#        column: user_id
#        regex: "[1]"
#    user_id_update_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: update
#        column: user_id
#        regex: "[1]"
#    user_id_select_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: select
#        column: user_id
#        regex: "[1]"
#    sql_hint_algorithm:
#      type: SQL_HINT
#      props:
#        foo: bar

######################################################################################################
#
# If you want to connect to MySQL, you should manually copy MySQL driver to lib directory.
#
######################################################################################################
#
#databaseName: shadow_db
#
#dataSources:
#  ds:
#    url: *********************************************************************
#    username: root
#    password:
#    connectionTimeoutMilliseconds: 30000
#    idleTimeoutMilliseconds: 60000
#    maxLifetimeMilliseconds: 1800000
#    maxPoolSize: 50
#    minPoolSize: 1
#  shadow_ds:
#    url: *********************************************************************
#    username: root
#    password:
#    connectionTimeoutMilliseconds: 30000
#    idleTimeoutMilliseconds: 60000
#    maxLifetimeMilliseconds: 1800000
#    maxPoolSize: 50
#    minPoolSize: 1
#
#rules:
#- !SHADOW
#  dataSources:
#    shadowDataSource:
#      productionDataSourceName: ds
#      shadowDataSourceName: shadow_ds
#  tables:
#    t_order:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_select_match_algorithm
#    t_order_item:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_update_match_algorithm
#        - user_id_select_match_algorithm
#    t_address:
#      dataSourceNames:
#        - shadowDataSource
#      shadowAlgorithmNames:
#        - user_id_insert_match_algorithm
#        - user_id_select_match_algorithm
#        - sql_hint_algorithm
#  shadowAlgorithms:
#    user_id_insert_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: insert
#        column: user_id
#        regex: "[1]"
#    user_id_update_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: update
#        column: user_id
#        regex: "[1]"
#    user_id_select_match_algorithm:
#      type: REGEX_MATCH
#      props:
#        operation: select
#        column: user_id
#        regex: "[1]"
#    sql_hint_algorithm:
#      type: SQL_HINT
#      props:
#        foo: bar
