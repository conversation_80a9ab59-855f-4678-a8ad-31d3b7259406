worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    # 限制body大小
    client_max_body_size 100m;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    upstream server {
        ip_hash;
        # gateway 地址
        server 127.0.0.1:8080;
        # server 127.0.0.1:8081;
    }

    upstream fscrm {
        ip_hash;
        # gateway 地址
        server 127.0.0.1:8085;
        # server 127.0.0.1:8081;
    }

  	server{
        listen 80;
        server_name xwsf-filetest.xuexizhiwang.com;

	   # https配置参考 start
        listen       443 ssl;

        # 证书直接存放 /docker/nginx/cert/ 目录下即可 更改证书名称即可 无需更改证书路径
        ssl on;
        ssl_certificate      /etc/nginx/cert/xuexizhiwang.com/_.xuexizhiwang.comfull.pem; # /etc/nginx/cert/ 为docker映射路径 不允许更改
        ssl_certificate_key  /etc/nginx/cert/xuexizhiwang.com/_.xuexizhiwang.com.pem; # /etc/nginx/cert/ 为docker映射路径 不允许更改
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        # https配置参考 end


        location / {
           proxy_redirect off;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_pass http://*************:9000;
        }
	}


    server {
        listen       80;
        server_name  xwsf-test.xuexizhiwang.com;

        # https配置参考 start
        listen       443 ssl;

        # 证书直接存放 /docker/nginx/cert/ 目录下即可 更改证书名称即可 无需更改证书路径
        ssl on;
        ssl_certificate      /etc/nginx/cert/xuexizhiwang.com/_.xuexizhiwang.comfull.pem; # /etc/nginx/cert/ 为docker映射路径 不允许更改
        ssl_certificate_key  /etc/nginx/cert/xuexizhiwang.com/_.xuexizhiwang.com.pem; # /etc/nginx/cert/ 为docker映射路径 不允许更改
        ssl_session_timeout 5m;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        # https配置参考 end

        # 演示环境配置 拦截除 GET POST 之外的所有请求
        # if ($request_method !~* GET|POST) {
        #     rewrite  ^/(.*)$  /403;
        # }

        # location = /403 {
        #     default_type application/json;
        #     return 200 '{"msg":"演示模式，不允许操作","code":500}';
        # }

        #此处是微信公众号
        location /officialaccount {
            alias /usr/share/nginx/html/officialaccount/;
            try_files $uri $uri/ @router;
            index  index.html index.htm;
        }

        location @router {
            rewrite ^.*$ /officialaccount/index.html last;
        }


        #此处是微信小程序
        location /miniprogram {
            alias /usr/share/nginx/html/miniprogram/;
            try_files $uri $uri/ @router;
            index  index.html index.htm;
        }

	   location @router {
            rewrite ^.*$ /miniprogram/index.html last;
        }

        # 限制外网访问内网 actuator 相关路径
        location ~ ^(/[^/]*)?/actuator(/.*)?$ {
            return 403;
        }

        location / {
            root   /usr/share/nginx/html/index; # docker映射路径 不允许更改
            try_files $uri $uri/ /index.html;
            index  index.html index.htm;
        }

        location @router {
            rewrite ^.*$ /index.html last;
        }



        location /prod-api/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # websocket参数
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_pass http://server/;
        }


        location /minio {
            proxy_pass http://*************:9000;
        }

        # 解决 powerjob 代理之后静态文件无法访问的问题 请勿修改乱动
        #location ~ ^/(js|css|jpg|png|svg|woff|ttf|ico|img)/ {
        #    proxy_pass http://powerjob-server;
        #}

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }


}
