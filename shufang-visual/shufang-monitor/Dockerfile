#FROM findepi/graalvm:java17-native
FROM registry.cn-shanghai.aliyuncs.com/jxw-midsoftware/jxwopenjdk:17.0.6

MAINTAINER Lion Li

RUN mkdir -p /jxw/monitor/logs

WORKDIR /jxw/monitor

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Ddubbo.network.interface.preferred=eth0"

EXPOSE 9100

ADD ./target/shufang-monitor.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -jar app.jar \
           -XX:+HeapDumpOnOutOfMemoryError -Xlog:gc*,:time,tags,level -XX:+UseZGC ${JAVA_OPTS}
