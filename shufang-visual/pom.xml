<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>shufang-monitor</module>
        <module>shufang-sentinel-dashboard</module>
        <module>shufang-seata-server</module>
        <module>shufang-nacos</module>
        <module>shufang-powerjob-server</module>
    </modules>

    <artifactId>shufang-visual</artifactId>
    <packaging>pom</packaging>

    <description>
        shufang-visual图形化管理模块
    </description>

    <dependencies>
        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-prometheus</artifactId>-->
<!--        </dependency>-->
    </dependencies>

</project>
