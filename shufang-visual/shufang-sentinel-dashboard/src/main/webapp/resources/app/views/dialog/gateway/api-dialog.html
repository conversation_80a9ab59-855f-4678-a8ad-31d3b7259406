<div>
	<span class="brand" style="font-weight:bold;">{{gatewayApiDialog.title}}</span>
	<div class="card" style="margin-top: 20px;margin-bottom: 10px;">
		<div class="panel-body">
				<form role="form" class="form-horizontal">
					<div class="form-group">
						<label class="col-sm-2 control-label">API 名称</label>
						<div class="col-sm-9">
							<input type="text" ng-if="gatewayApiDialog.type == 'edit'" class="form-control" placeholder="请输入" ng-model='currentApi.apiName'
							 disabled="" />
							<input type="text" ng-if="gatewayApiDialog.type == 'add'" class="form-control highlight-border" placeholder="请输入" ng-model='currentApi.apiName' />
						</div>
					</div>

					<div class="form-group" ng-repeat="predicateItem in currentApi.predicateItems track by $index">
						<label class="col-sm-2 control-label">匹配模式</label>
						<div class="col-sm-4 control-label">
							<div class="form-control highlight-border" align="center">
								<input type="radio" value="0" checked ng-model="predicateItem.matchStrategy" title="精确" />&nbsp;精确&nbsp;&nbsp;
								<input type="radio" value="1" ng-model="predicateItem.matchStrategy" title="前缀" />&nbsp;前缀&nbsp&nbsp;
								<input type="radio" value="2" ng-model="predicateItem.matchStrategy" title="正则" />&nbsp;正则&nbsp&nbsp;
							</div>
						</div>
						<label class="col-sm-2 control-label">匹配串</label>
						<div class="col-sm-3 control-label">
							<input type='text' ng-model="predicateItem.pattern" class="form-control highlight-border" placeholder="请输入" />
						</div>
						<div class="col-sm-1 control-label" align="center">
							<button class="btn btn-outline-danger" ng-click="removeMatchPattern($index)"
									align="center" ng-if="currentApi.predicateItems.length > 1">X</button>
						</div>
					</div>

					<div class="form-group" style="text-align: center">
						<button class="btn btn-outline-primary" ng-click="addNewMatchPattern()">
							<i class="fa fa-plus"></i>&nbsp;新增匹配规则
						</button>
					</div>
				</form>
			<div class="separator"></div>
			<div clss="row" style="margin-top: 20px;">
				<button class="btn btn-outline-danger" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="closeThisDialog()">取消</button>
				<button class="btn btn-outline-success" style="float:right; height: 30px;font-size: 12px;margin-left: 10px;" ng-click="saveApi()">{{gatewayApiDialog.confirmBtnText}}</button>
				<button ng-if="gatewayApiDialog.saveAndContinueBtnText" class="btn btn-default" style="float:right; height: 30px;font-size: 12px;"
				 ng-click="saveApiAndContinue()">{{gatewayApiDialog.saveAndContinueBtnText}}</button>
			</div>
		</div>
	</div>
</div>
