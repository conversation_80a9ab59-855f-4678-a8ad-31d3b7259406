<div class="row" style="margin-left: 1px; margin-top:10px; height: 50px;">
    <div class="col-md-6" style="margin-bottom: 10px;">
      <span style="font-size: 30px;font-weight: bold;">{{app}}</span>
    </div>
    <div class="col-md-6" ng-if="!loadError">
      <button class="btn btn-default-inverse" style="float: right; margin-right: 10px;" ng-disabled="!macInputModel" ng-click="addNewRule()">
        <i class="fa fa-plus"></i>&nbsp;&nbsp;新增热点限流规则</button>
    </div>
  </div>
  
  <div class="separator"></div>
  
  <div class="container-fluid">
    <div class="row" style="margin-top: 20px; margin-bottom: 20px;">
      <div class="col-md-12">
        <div class="card">
          <div class="inputs-header">
            <span class="brand" style="font-size: 13px;">热点参数限流规则</span>
            <button class="btn btn-primary" style="float: right; margin-right: 10px; height: 30px;font-size: 12px;" ng-click="getMachineRules()">刷新</button>
            <input class="form-control witdh-200" placeholder="关键字" ng-model="searchKey">
            <div class="control-group" style="float:right;margin-right: 10px;margin-bottom: -10px;">
              <selectize id="gsInput" class="selectize-input-200" config="macsInputConfig" options="macsInputOptions" ng-model="macInputModel"
                placeholder="机器"></selectize>
            </div>
          </div>

            <!-- error panel -->
            <div class="row clearfix" ng-if="loadError">
                <div class="col-md-6 col-md-offset-3">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <center>
                                <p>{{loadError.message}}</p>
                            </center>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table and pagination start -->
                <!--.tools-header -->
                <div class="card-body" style="padding: 0px 0px;" ng-if="!loadError">
                    <table class="table" style="border-left: none; border-right:none;margin-top: 10px;">
                        <thead>
                        <tr style="background: #F3F5F7;">
                            <td style="width: 40%">
                                资源名
                            </td>
                            <td style="width: 10%;">
                                参数索引
                            </td>
                            <td style="width: 10%;">
                                流控模式
                            </td>
                            <td style="width: 10%;">
                                阈值
                            </td>
                            <td style="width: 8%;">
                                是否集群
                            </td>
                            <td style="width: 10%;">
                                例外项数目
                            </td>
                            <td style="width: 12%;">
                                操作
                            </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr dir-paginate="ruleEntity in rules | filter: searchKey | itemsPerPage: rulesPageConfig.pageSize " current-page="rulesPageConfig.currentPageIndex"
                            pagination-id="entriesPagination">
                            <td style="word-wrap:break-word;word-break:break-all;">{{ruleEntity.rule.resource}}</td>
                            <td style="word-wrap:break-word;word-break:break-all;">{{ruleEntity.rule.paramIdx}}</td>
                            <td>
                                {{ruleEntity.rule.grade == 1 ? 'QPS' : '未知'}}
                            </td>
                            <td style="word-wrap:break-word;word-break:break-all;">
                                {{ruleEntity.rule.count}}
                            </td>
                            <td>
                                <span ng-if="ruleEntity.rule.clusterMode">是</span>
                                <span ng-if="!ruleEntity.rule.clusterMode">否</span>
                            </td>
                            <td>
                                {{ruleEntity.rule.paramFlowItemList == undefined ? 0 : ruleEntity.rule.paramFlowItemList.length}}
                            </td>
                            <td>
                                <button class="btn btn-xs btn-default" type="button" ng-click="editRule(ruleEntity)" style="font-size: 12px; height:25px;">编辑</button>
                                <button class="btn btn-xs btn-default" type="button" ng-click="deleteRule(ruleEntity)" style="font-size: 12px; height:25px;">删除</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <!-- .card-body -->
                <div class="pagination-footer" ng-if="!loadError">
                    <dir-pagination-controls boundary-links="true" template-url="app/views/pagination.tpl.html" pagination-id="entriesPagination"
                                             on-page-change="">
                    </dir-pagination-controls>
                    <div class="tools" style="">
                        <span>共 {{rulesPageConfig.totalCount}} 条记录, </span>
                        <span>每页 <input class="form-control" ng-model="rulesPageConfig.pageSize"> 条记录</span>
                        <!--<span>第 {{rulesPageConfig.currentPageIndex}} / {{rulesPageConfig.totalPage}} 页</span>-->
                    </div>
                    <!-- .tools -->
                </div>
                <!-- pagination-footer -->
            <!-- Table and pagination end -->

        </div>
        <!-- .card -->
      </div>
      <!-- .col-md-12 -->
    </div>
    <!-- -->
  </div>
  <!-- .container-fluid -->
  