#FROM findepi/graalvm:java17-native
FROM registry.cn-shanghai.aliyuncs.com/jxw-midsoftware/jxwopenjdk:17.0.6

MAINTAINER Lion Li

RUN mkdir -p /jxw/sentinel-dashboard/logs \
    /jxw/skywalking/agent

WORKDIR /jxw/sentinel-dashboard

ENV LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Ddubbo.network.interface.preferred=eth0"

EXPOSE 8718

ADD ./target/shufang-sentinel-dashboard.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           #-Dskywalking.agent.service_name=shufang-sentinel-dashboard \
           #-javaagent:/jxw/skywalking/agent/skywalking-agent.jar \
           -jar app.jar ${JAVA_OPTS}
