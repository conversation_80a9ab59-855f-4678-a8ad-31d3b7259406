#FROM findepi/graalvm:java17-native
FROM registry.cn-shanghai.aliyuncs.com/jxw-midsoftware/jxwopenjdk:17.0.6

MAINTAINER Lion Li

RUN mkdir -p /jxw/seata-server/logs \
    /jxw/skywalking/agent

WORKDIR /jxw/seata-server

ENV TZ=PRC LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Ddubbo.network.interface.preferred=eth0"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 7091
EXPOSE 8091

ADD ./target/shufang-seata-server.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom \
           #-Dskywalking.agent.service_name=shufang-seata-server \
           #-Dskywalking.plugin.seata.server=true \
           #-javaagent:/jxw/skywalking/agent/skywalking-agent.jar \
           -jar app.jar ${JAVA_OPTS}
