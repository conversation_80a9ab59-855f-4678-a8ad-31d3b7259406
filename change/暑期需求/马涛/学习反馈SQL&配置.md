# SQL 配置
```sql 会员每日打卡登录情况表
CREATE TABLE `attendance_daily_activity` (
`attendance_daily_activity_id` bigint NOT NULL COMMENT '主键',
`student_id` bigint NOT NULL COMMENT '会员id',
`record_date` date NOT NULL COMMENT '每日日期',
`event_time` datetime NOT NULL COMMENT '打卡/登录时间',
`event_time_source` tinyint(1) NOT NULL COMMENT '事件时间来源（1：考勤打卡。2：设备登录）',
`create_by` varchar(50) NOT NULL COMMENT '创建人',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`attendance_daily_activity_id`) USING BTREE,
UNIQUE KEY `uk_student_record_date` (`student_id`,`record_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员每日打卡登录情况表';
```
```sql 会员每日学习反馈记录表
CREATE TABLE `attendance_daily_activity_feedback_record` (
`attendance_daily_activity_feedback_record_id` bigint NOT NULL COMMENT '主键',
`attendance_daily_activity_id` bigint NOT NULL COMMENT '每日会员打卡登录详情表id',
`feedback_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '反馈状态（ 1未反馈; 2已反馈; 3超时反馈;4 超时未反馈）',
`publish_status` tinyint(1) DEFAULT NULL COMMENT '发布状态（0:未发布；1：暂存；2：已发布）',
`create_by` varchar(50) DEFAULT '' COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`feedback_record_id` bigint DEFAULT '0' COMMENT '反馈记录表id',
PRIMARY KEY (`attendance_daily_activity_feedback_record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员每日学习反馈记录表';
```

```sql 学习反馈记录新增反馈状态以及修改原反馈状态为发布状态的备注信息
ALTER TABLE `feedback_record` ADD COLUMN `feedback_submit_status` tinyint(1) DEFAULT NULL COMMENT '反馈状态（ 1未反馈; 2已反馈; 3超时反馈;4 超时未反馈）' AFTER `feedback_status`;
ALTER TABLE `feedback_record`
MODIFY COLUMN `feedback_status` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发布状态（1暂存 2已发布）';
```
```sql 更新设备端token失效时间 1天
UPDATE `sys_client` SET `timeout` = 86400 WHERE  `client_key` = 'app_stu_user';
```

# nacos配置
```yaml student-auth.yml
rocketmq:
    name-server: rocketmq.test.jiumentongbu.com:9876
    producer:
        group: gid-shufang-student
        namespace: dev
    consumer:
        group: gid-shufang-student
        namespace: dev
```
```yaml student-student.yml
rocketmq:
    name-server: rocketmq.test.jiumentongbu.com:9876
    producer:
        group: gid-shufang-student
        namespace: dev
    consumer:
        group: gid-shufang-student
        namespace: dev
```

```yaml application-common.yml
# 定时任务配置
job:
    # 调度中心地址
    schedulerAddr: http://************:32337/xxl-job-admin/
    # 访问调度中心token
    accessToken: fec902ee-5eee-7d7e-7196-f913fde55243
    # 执行器IP，不配会默认获取
    # executorIp: **************
    # 执行器端口
    executorPort: 9898

```

