```sql
DROP TABLE IF EXISTS study_feedback_report;
DROP TABLE IF EXISTS study_planning_feedback_pending_relation;
DROP TABLE IF EXISTS study_planning_pending;
DROP TABLE IF EXISTS study_planning_pending_relation;
CREATE TABLE `study_feedback_report` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `period_start` date NOT NULL COMMENT '报告周期开始日期',
  `period_end` date NOT NULL COMMENT '报告周期结束日期',
  `summary` text NOT NULL COMMENT '总结内容',
  `issues` text NOT NULL COMMENT '存在问题',
  `focus_points` text NOT NULL COMMENT '需关注重点',
  `status` tinyint NOT NULL COMMENT '状态:1-草稿,2-已发布',
  `feedback_status` tinyint  NOT NULL DEFAULT '0' COMMENT '反馈状态:0-待反馈,1-已反馈',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint DEFAULT '0' COMMENT '删除标记:0-未删除,1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_student_period` (`student_id`,`period_start`,`period_end`)
) COMMENT='学习反馈报告表';
-- 学习规划反馈报告关联学习记录表
CREATE TABLE `study_planning_feedback_pending_relation` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `report_id` bigint NOT NULL COMMENT '反馈报告ID',
  `pending_id` bigint NOT NULL COMMENT '需学习规划学生记录ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_report_planning` (`report_id`,`pending_id`),
  KEY `idx_planning_record` (`pending_id`)
) COMMENT='反馈报告关联学习记录表';

CREATE TABLE `study_planning_pending` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `mode_type` tinyint NOT NULL COMMENT '模式类型:1-春秋模式,2-寒暑模式',
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date NOT NULL COMMENT '计划结束日期',
  `feedback_status` tinyint DEFAULT '0' COMMENT '反馈状态：例如0-待反馈，1-已反馈',
  `planning_status` tinyint DEFAULT '0' COMMENT '规划状态：例如0-待规划，1-已规划',
  `expected_planning_time` datetime DEFAULT NULL COMMENT '应规划时间',
  `expected_feedback_time` datetime DEFAULT NULL COMMENT '应反馈时间',
  `actual_planning_time` datetime DEFAULT NULL COMMENT '实际规划时间',
  `actual_feedback_time` datetime DEFAULT NULL COMMENT '实际反馈时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_student_plan` (`student_id`,`plan_start_date`) COMMENT '防止重复规划',
  KEY `idx_student` (`student_id`),
  KEY `idx_date_range` (`plan_start_date`,`plan_end_date`)
)COMMENT='学生学习规划情况汇总表';

CREATE TABLE `study_planning_pending_relation` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `pending_id` bigint NOT NULL COMMENT '需学习规划记录ID',
  `planning_id` bigint NOT NULL COMMENT '学习规划记录ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_planning_pending` (`planning_id`,`pending_id`) COMMENT '防止重复关联',
  KEY `idx_planning` (`planning_id`),
  KEY `idx_pending` (`pending_id`)
)COMMENT='学生学习规划情况与学习规划关联记录表';
```
# wxmp服务
 ## 待学习规划页面地址
     studyPlanningPageUrl: https://wxmp-dev.xuewangshufang.com/#/pages/study/plan/share
	
# gateway	
    - /student/h5/feedbackReport/*