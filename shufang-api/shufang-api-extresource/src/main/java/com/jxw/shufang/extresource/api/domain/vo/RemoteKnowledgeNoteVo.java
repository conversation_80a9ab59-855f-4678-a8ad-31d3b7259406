package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemoteKnowledgeNoteVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 知识点ID
     */
    private Integer knowledgeId;

    /**
     * 内容(富文本)
     */
    private String context;


    /**
     * 内容TTS音频
     */
    private String contentTtsUrl;

    /**
     * 内容(纯文本)
     */
    private String textContent;

}
