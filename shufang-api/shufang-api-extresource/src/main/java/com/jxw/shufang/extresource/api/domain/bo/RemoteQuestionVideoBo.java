package com.jxw.shufang.extresource.api.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class RemoteQuestionVideoBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private Long questionId;

    private List<Long> questionIdList;

    ///**
    // * 在线播放格式（mp4, MSKT, m3u8）指定分辨率：拼上 _720p 或 _480p 或 _1080p(不传默认mp4_720p)
    // */
    //private String onlineFormat;
    //
    ///**
    // * 是否需要多分辨率 boolean
    // */
    //private String multiResolutions;
}
