package com.jxw.shufang.extresource.api.domain.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class RemoteKnowledgeResourceBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 知识点ID
     */
    private Long knowledgeId;

    /**
     * 讲义：handout，测验试卷：test，测验试卷带解析：test_analysis，练习试卷：practice，练习试卷带解析：practice_analysis
     */
    private String type;

    private List<String> typeList;
    private List<Long> knowledgeIdList;
}
