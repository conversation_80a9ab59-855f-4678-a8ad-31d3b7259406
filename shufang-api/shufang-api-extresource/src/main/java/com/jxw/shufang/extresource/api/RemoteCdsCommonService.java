package com.jxw.shufang.extresource.api;

import com.jxw.shufang.extresource.api.domain.bo.ListVideoBO;
import com.jxw.shufang.extresource.api.domain.bo.RemoteOssBo;
import com.jxw.shufang.extresource.api.domain.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 试卷通用接口
 */
public interface RemoteCdsCommonService {

    List<RemoteSubjectVo> listSubjects(List<Integer> ids);

    Map<Integer, RemoteSubjectVo> getSubjectVoMap(List<Integer> list);

    /**
     * 获取年级
     */
    List<RemoteGradeVo> getGradeList(List<Integer> ids);


    List<RemoteGradeVo> getNewGradeList(List<Integer> ids);

    /**
     * 获取学册
     */
    List<RemoteGradeVolumeVo> findGradeVolumeList(List<Integer> ids);


    /**
     * 获取细分出版社
     */
    List<RemotePublisherVo> listPublishers(List<Integer> ids);


    List<RemoteOssVo> getOssUrl(List<RemoteOssBo> list);

    List<VideoDTO> listVideosV2(ListVideoBO videoRequest);
}
