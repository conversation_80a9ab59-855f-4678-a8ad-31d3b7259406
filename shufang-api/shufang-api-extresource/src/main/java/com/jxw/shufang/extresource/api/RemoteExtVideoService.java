package com.jxw.shufang.extresource.api;

import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVideoVo;

import java.util.List;

public interface RemoteExtVideoService {
    /**
     * 获取知识点视频列表
     *
     * @param remoteKnowledgeVideoBo 知识点视频查询参数
     * @return 知识点视频列表
     */
    List<RemoteKnowledgeVideoVo> getKnowledgeVideoList(RemoteKnowledgeVideoBo remoteKnowledgeVideoBo);

    /**
     * 通过知识点id获取知识点视频列表
     *
     * @param knowledgeId 知识点id
     * @return 知识点视频列表
     */
    RemoteKnowledgeVideoVo getKnowledgeVideoListById(Long knowledgeId);
}
