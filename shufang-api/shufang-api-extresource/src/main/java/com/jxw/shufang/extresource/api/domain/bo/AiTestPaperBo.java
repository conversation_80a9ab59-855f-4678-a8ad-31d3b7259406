package com.jxw.shufang.extresource.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 学王ai评测查询试卷参数
 */
@Data
public class AiTestPaperBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;    /**
     * 科目ID
     */
    private Integer subjectId;

    /**
     * 学段ID
     */
    private Integer phaseId;

    /**
     * 年级ID
     */
    private Integer gradeId;

    /**
     * 年纪册次ID
     */
    private Integer gradeVolumeId;

    /**
     * 出版社细分ID
     */
    private Integer editionId;
    /**
     * 试卷状态
     */
    private Integer status;

    /**
     * 试卷类型集合
     */
    private List<Integer> typeIds;

    /**
     * 试卷类型id
     */
    private Integer type;
}
