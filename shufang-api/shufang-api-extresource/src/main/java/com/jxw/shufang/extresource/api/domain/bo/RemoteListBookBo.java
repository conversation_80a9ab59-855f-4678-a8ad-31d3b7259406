package com.jxw.shufang.extresource.api.domain.bo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


@Data
public class RemoteListBookBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer flag;
    /**
     * 科目
     */
    private Integer subjectId;
    /**
     * 年级
     */
    private Integer gradeId;
    private Integer gradeVolumeId;
    /**
     * 出版社
     */
    private Integer publisherId;
    private Integer phaseId;
    private Integer editionId;
    /**
     * 这个默认为true，别的服务调用时不需要科目年级出版社对象时为false(不需要多次调用cds)
     */
    private Boolean needBookInfos = true;
}
