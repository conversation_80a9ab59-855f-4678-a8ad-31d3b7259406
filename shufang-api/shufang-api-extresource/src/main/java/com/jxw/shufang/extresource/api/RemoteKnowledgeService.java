package com.jxw.shufang.extresource.api;

import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeCompleteVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeNoteVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;

import java.util.List;

public interface RemoteKnowledgeService {

    RemoteKnowledgeNoteVo getKnowledgeNoteInfo(Integer knowledgeId);

    List<RemoteKnowledgeNoteVo> listKnowledgeNotes(List<Integer> knowledgeIds);
     List<RemoteVideoVo> queryKnowledge(List<Long> knowledgeIds);
}
