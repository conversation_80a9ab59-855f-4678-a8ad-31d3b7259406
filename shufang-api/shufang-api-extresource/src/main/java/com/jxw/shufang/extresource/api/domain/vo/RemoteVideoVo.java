package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 培优视频
 */
@NoArgsConstructor
@Data
public class RemoteVideoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *文件ID
     */
    private Long id;

    /**
     *视频唯一标识
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 视频展示名称
     */
    private String showName;

    /**
     * 视频缩略图
     */
    private String thumbUrl;

    /**
     * 视频url
     */
    private String playUrl;

    /**
     * 多分辨率列表
     */
    private List<RemotePlayResolutionsVo> playResolutions;

    /**
     *
     */
    private Object downloadUrl;

    /**
     *
     */
    //private List<RemotePlayResolutionsVo> downloadResolutions;

    /**
     * 文件大小（字节）
     */
    private Integer size;

    /**
     * 视频时长（秒）
     */
    private Long duration;

    /**
     * 总学习时长（秒）(与服务无关，纯展示用)
     */
    private Long totalStudyDuration;

    /**
     * 最后一天学习的一个分片记录,如：00:42-00:45(3''1.5)(与服务无关，纯展示用)
     */
    private String lastDayStudyVideoSlices;

    /**
     * 课程ID
     */
    private Long courseId;


}
