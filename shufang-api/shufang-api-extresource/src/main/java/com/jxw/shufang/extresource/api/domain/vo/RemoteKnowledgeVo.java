package com.jxw.shufang.extresource.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RemoteKnowledgeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer knowledgeId;

    /**
     * 知识点名称
     */
    private String knowledgeName;

    /**
     * 难度等级
     */
    private Integer difficulty;

    /**
     * 是否有学霸笔记（0：没有，1：有）
     */
    private Integer hasNote;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private RemoteKnowledgeNoteVo knowledgeNote;

    /**
     * 学科ID
     */
    @JsonIgnore
    private Integer subjectId;

    /**
     * 学段Id
     */
    @JsonIgnore
    private Integer phaseId;

    /**
     * 类型。0=知识点， 1=单词
     */
    @JsonIgnore
    private Integer type;

    /**
     * 是否有知识点视频（0：没有，1：有）
     * tips: 数据库命名有错误，切已发布生产修改有点麻烦，有点多
     */
    @JsonIgnore
    private Integer isVideo;

    /**
     * 是否有真题视频（0：没有，1：有）
     */
    @JsonIgnore
    private Integer hasExamVideo;

    /**
     * 是否考点
     */
    @JsonIgnore
    private Integer isExamPoint;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonIgnore
    private Date updateTime;

}
