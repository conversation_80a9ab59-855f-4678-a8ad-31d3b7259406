package com.jxw.shufang.extresource.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-08-16 17:39
 */
@Data
public class VideoDTO implements Serializable {

    private Long id;

    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 展示名称
     */
    private String showName;

    private String thumbUrl;

    private String playUrl;

    private String downloadUrl;

    /**
     * 文件大小，单位:字节
     */
    private Long size;

    /**
     * 音/视频时长 单位:秒
     */
    private Long duration;

    /**
     * 学习视频进度(百分比)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer studyProgress;

    /**
     * 播放视频进度（秒）
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long playProgress;


      /**
     * 文件Md5处理
     */
    private String contentMd5;
}
