package com.jxw.shufang.extresource.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemotePaperBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 科目ID
     */
    private Integer subjectId;

    /**
     * 学段ID
     */
    private Integer phaseId;

    /**
     * 年级ID
     */
    private Integer gradeId;

    /**
     * 年纪册次ID
     */
    private Integer gradeVolumeId;
    /**
     * 细分版本id
     */
    private Integer editionId;
    /**
     * 试卷类型
     */
    private Integer type;
    /**
     * 状态
     */
    private Integer status;

}
