package com.jxw.shufang.extresource.api.domain.bo;

/**
 * <AUTHOR>
 * @date 2023-09-25 16:46
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-09-22 17:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListVideoBO implements Serializable {

    private Set<Long> fileIds;
    private String onlineFormat = "mp4_720p";
    private String downloadFormat;
    private Boolean multiResolutions = false;

    /**
     * 是否需要视频进度
     */
    private Boolean needVideoProgress = false;
    /**
     * 第三方用户ID（一台设备多个账户记录区分）
     */
    private String thirdUserId;

    /**
     * 视频类型
     */
    private String bizType;

}

