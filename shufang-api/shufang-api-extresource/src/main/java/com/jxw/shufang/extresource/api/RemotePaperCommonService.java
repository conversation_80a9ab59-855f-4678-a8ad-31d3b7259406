package com.jxw.shufang.extresource.api;

import com.jxw.shufang.extresource.api.domain.bo.*;
import com.jxw.shufang.extresource.api.domain.vo.*;

import java.util.List;

/**
 * 试卷通用接口
 */
public interface RemotePaperCommonService {


    /**
     * 获取出版社版本--细分版本
     */
    List<RemoteIdNameVo> getEditionList(AiTestPaperBo bo);


    String getPhaseName(Integer phaseId);

    /**
     * 获取年级
     */
    List<RemoteIdNameVo> getGradeList(AiTestPaperBo bo);


    /**
     * 获取学册
     */
    List<RemoteIdNameVo> getGradeVolumeList(AiTestPaperBo bo);


    /**
     * 根据年级Id获取学段
     */
    List<Integer> getPhase(AiTestPaperBo bo);


    /**
     * 根据学段获取对应的科目
     */
    List<RemoteIdNameVo> getSubject(AiTestPaperBo bo);

    List<RemoteExtPaperVo> listPaper(AiTestPaperBo bo);

    List<RemoteAiPaperDetailVo> listPaperByIdList(List<Integer> ids);

    List<RemotePhaseSubjectVo> getSubjectPhase(RemotePhaseSubjectBo bo);
}
