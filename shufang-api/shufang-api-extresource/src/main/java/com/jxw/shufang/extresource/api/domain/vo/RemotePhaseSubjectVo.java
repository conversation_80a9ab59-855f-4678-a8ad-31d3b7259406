package com.jxw.shufang.extresource.api.domain.vo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemotePhaseSubjectVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 学段ID
     */
    private Integer phaseId;

    /**
     * 科目ID
     */
    private Integer subjectId;

    /**
     * 学段科目名称
     */
    private String name;

    private Integer sortOrder;


}
