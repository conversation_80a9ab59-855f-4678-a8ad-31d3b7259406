package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class RemoteQuestionVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 题目Id
     */
    private Long id;

    /**
     * 题目主干
     */
    private String title;

    /**
     * 题目类型
     */
    private RemoteQuestionTypeVo type;

    /**
     * 选择题选项,json数组
     */
    private List<String> options;

    /**
     * 答案,json数组(二维)
     */
    private String[][] answer;

    /**
     * 解析
     */
    private String analysis;

    /**
     * 是否经典例题 1-是 0-否
     */
    private Integer isClassic;

    /**
     * /是否主观题 1-是 0-否
     */
    private Integer isSubjective;

    /**
     * 父级题目ID
     */
    private Long pid;

    /**
     * 难度 1-易 2-较易 3-中等 4-较难 5-难
     */
    private Integer difficulty;

    /**
     * 科目ID
     */
    private Long subjectId;

    /**
     * 科目对象
     */
    private Object subject;

    /**
     * 学段ID
     */
    private Long phaseId;

    /**
     * 学段对象
     */
    private Object phase;

    /**
     * 题目年份
     */
    private Integer year;

    /**
     * 是否有子题 1-是 0-否
     */
    private Integer hasChildren;

    /**
     * 子题列表
     */
    private List<RemoteQuestionVo> children;

    /**
     * 是否有讲题视频 1-是， 0-否
     */
    private Integer hasVideo;

    /**
     *
     */
    private String updateTime;

    /**
     *
     */
    private Object isCollect;

    /**
     * 题目编号(跟服务无关)
     */
    private String questionNo;

    private List<RemoteIdNameVo> knowledges;

    private RemoteQuestionVo parentQuestion;
}
