package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
public class RemoteGroupResourceVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private KnowledgeResourceType type;

    private Long knowledgeId;

    private RemoteKnowledgeResourceVo knowledgeResource;

}
