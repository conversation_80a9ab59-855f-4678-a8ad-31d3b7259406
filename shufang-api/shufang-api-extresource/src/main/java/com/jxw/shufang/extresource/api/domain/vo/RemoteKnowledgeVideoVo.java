package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
public class RemoteKnowledgeVideoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 知识点ID
     */
    private Long knowledgeId;

    /**
     * 视频总时长
     */
    private Long videoTotalDuration;

    /**
     * 视频列表
     */
    private List<RemoteVideoVo> videoList;

    private RemoteTeacherVo teacher;
}
