package com.jxw.shufang.extresource.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 题目详细数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RemoteQuestionDetailVo extends RemoteQuestionSimpleVo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渲染模板ID (视频批注用，对应表tms_video_label_question的template_id)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer templateId;

    /**
     * 是否收藏(0:未收藏；1:已收藏)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer isCollection;

}
