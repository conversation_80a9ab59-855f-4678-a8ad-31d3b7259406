package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class RemoteAiPaperDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组卷类型。tms_dict.id
     */
    private Integer groupType;

    /**
     * 试卷类型ID。tms_dict.id
     */
    private Integer type;

    /**
     * 科目ID
     */
    private Integer subjectId;

    /**
     * 学段ID
     */
    private Integer phaseId;

    /**
     * 年级ID
     */
    private Integer gradeId;

    /**
     * 年纪册次ID
     */
    private Integer gradeVolumeId;

    /**
     * 出版社ID
     */
    private Integer editionId;

    /**
     * 教材ID
     */
    private Integer bookId;

    /**
     * 单元ID
     */
    private Integer chapterId;


    /**
     * 试卷国标ID。tms_dict.id
     */
    private Integer standardId;

    /**
     * 试卷名称
     */
    private String paperName;



    /**
     * 排序字段
     */
    private Integer sortOrder;


    /**
     * 组卷时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
