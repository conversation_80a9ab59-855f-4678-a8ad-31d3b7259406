package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public  class RemoteAudioVideoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
    private String thumbUrl;
    private Long duration;
    private Boolean isWatched;
    private Long watchedCount;
    private List<RemotePlayResolutionsVo> resolutions;
}
