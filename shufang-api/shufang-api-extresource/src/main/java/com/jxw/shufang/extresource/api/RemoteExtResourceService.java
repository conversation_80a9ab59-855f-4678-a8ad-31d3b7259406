package com.jxw.shufang.extresource.api;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.*;

import java.util.List;

public interface RemoteExtResourceService {

    List<RemoteGroupResourceVo> getKnowledgeResourceList(RemoteKnowledgeResourceBo query) throws ServiceException;

    RemoteGroupResourceVo getKnowledgeResourceById(Long knowledgeId,String type) throws ServiceException;

    List<RemoteExtCourseCatalogVo> getCourseCatalogTree(Long courseId, Long parentId) throws ServiceException;


    List<RemoteAppInfoVo> androidOtaCheckBySn(String extraIsbn, String sn);

    /**
     * 获取资源完整的性集合
     *
     * @param knowledgeIds
     * @return
     */
    RemoteKnowledgeCompleteVo getKnowledgeComplete(List<Long> knowledgeIds);
}
