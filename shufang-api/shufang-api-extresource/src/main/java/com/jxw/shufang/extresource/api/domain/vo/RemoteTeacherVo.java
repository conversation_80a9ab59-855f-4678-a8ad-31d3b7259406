package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class RemoteTeacherVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 教师编号
     */
    private String teacherNo;
    /**
     * 老师名称
     */
    private String name;
    /**
     * 性别
     */
    private String sex;

    private Long profilePhotoId;
    /**
     * 图像照片
     */
    private String profilePhotoUrl;
    /**
     * 标签
     */
    private String tag;
    /**
     * 证书编号
     */
    private String certificateNo;
    private Long certificatePhotoId;
    /**
     * 证书照片
     */
    private String certificatePhotoUrl;
    /**
     * 简介
     */
    private String intro;
}
