package com.jxw.shufang.extresource.api;

import com.jxw.shufang.extresource.api.domain.bo.RemotePaperQuestionBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionSimpleVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RemoteQuestionService {

    /**
     * 获取题目列表
     *
     * @param questionIds 题目id列表
     * @date 2024/05/09 04:34:34
     */
    List<RemoteQuestionVo> listQuestions(List<Long> questionIds);

    List<RemoteQuestionVo> listQuestionsWithKnowledge(List<Long> questionIds);

    /**
     * 获取知识问题列表
     *
     * @param remoteQuestionBo 远程问题bo
     * @date 2024/05/09 04:34:34
     */
    List<RemoteQuestionVo> getKnowledgeQuestionList(RemoteQuestionBo remoteQuestionBo);



    List<RemoteQuestionSimpleVo> getPaperQuestions(RemotePaperQuestionBo remotePaperQuestionBo);


    /**
     * 获取问题视频列表
     *
     * @param remoteQuestionVideoBo 远程问题视频bo
     * @date 2024/05/10 01:58:03
     */
    List<RemoteGroupVideoVo> getQuestionVideoList(RemoteQuestionVideoBo remoteQuestionVideoBo);
}
