package com.jxw.shufang.extresource.api.domain.vo;

import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
public class RemoteGroupResourceV2Vo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private KnowledgeResourceType resourceType;

    /**
     * 文件ID
     */
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 展示名称
     */
    private String showName;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 文件路径
     */
    private String path;

    private String pidPath;

    /**
     * 文件链接
     */
    private String url;

    /**
     * 文件大小
     */
    private Long size;

    private Long duration;

    private Long parentId;

    private String thumbUrl;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;


    /**
     * 完成状态 true 完成 false 未完成
     */
    private Boolean completionStatus;

    private Long knowledgeId;

}
