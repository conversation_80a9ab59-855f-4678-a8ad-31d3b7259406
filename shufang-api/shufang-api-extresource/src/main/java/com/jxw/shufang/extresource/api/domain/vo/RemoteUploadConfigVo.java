package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class RemoteUploadConfigVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String accessKeyId;
    private String accessKeySecret;
    private String securityToken;
    private Date startTime;
    private Date expiration;
    private String bucket;
    private String region;
    private String endpoint;
    private String basePath;
    private String ossType;
    private String service;

}
