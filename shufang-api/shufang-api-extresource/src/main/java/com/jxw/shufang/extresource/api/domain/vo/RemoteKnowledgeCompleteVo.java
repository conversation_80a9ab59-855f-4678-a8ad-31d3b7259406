package com.jxw.shufang.extresource.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * @author: cyj
 * @date: 2025/4/11
 */
@Data
@AllArgsConstructor
public class RemoteKnowledgeCompleteVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Map<Long, Boolean> knowledgeCompleteMap;
}
