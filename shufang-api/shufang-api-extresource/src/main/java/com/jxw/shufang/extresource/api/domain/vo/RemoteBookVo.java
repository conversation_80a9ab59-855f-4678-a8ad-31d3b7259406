package com.jxw.shufang.extresource.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemoteBookVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    private Integer bookId;
    /**
     * 课本编号
     */
    private String bookIsbn;
    /**
     * 课本名称
     */
    private String bookName;

    /**
     * 书本简介
     */
    private String intro;

    private Integer phaseId;

    private Integer subjectId;

    private Integer gradeId;

    private Integer gradeVolumeId;

    private Integer publisherId;

    /**
     * 出版社版本id
     */
    private Integer editionId;

//    private SubjectDTO subject;
//
//    private RemoteGradeVo grade;
//
//    private GradeVolumeDTO gradeVolume;
//
//    private PublisherDTO2 publisher;

    /**
     * 书本封面图ID
     */
    private Long coverId;

    private Long verticalCoverId;

    private Long verticalTextCoverId;

    private Long horizontalCoverId;

    /**
     * 书本封面图url
     */
    private String coverUrl;

    private String verticalCoverUrl;

    private String verticalTextCoverUrl;

    private String horizontalCoverUrl;

    /**
     * 视频个数 通过flag 判断是知识点/同步视频/单词视频/书法视频个数
     */
    private Integer videoNum;

    /**
     * 知识点视频简介
     */
    private String knowledgeVideoIntro;

    /**
     * 单词视频简介
     */
    private String wordVideoIntro;

    /**
     * 书法视频简介
     */
    private String calligraphyVideoIntro;

    /**
     * 同步视频简介
     */
    private String syncVideoIntro;

}
