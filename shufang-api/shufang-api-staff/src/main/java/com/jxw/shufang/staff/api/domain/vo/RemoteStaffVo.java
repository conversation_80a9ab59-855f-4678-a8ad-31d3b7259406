package com.jxw.shufang.staff.api.domain.vo;

import lombok.Data;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RemoteStaffVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工id
     */
    private Long branchStaffId;

    /**
     * 考勤id
     */
    private String checkWorkAttendanceId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 真实头像(图片ossid)
     */
    private Long realAvatar;

    private String realAvatarUrl;

    /**
     * 微信二维码(图片ossid)
     */
    private Long wechatQrCode;
    private String wechatQrCodeUrl;

    /**
     * sys用户信息
     */
    private RemoteUserVo user;

    private Long createBy;

    private Long createDept;

}




