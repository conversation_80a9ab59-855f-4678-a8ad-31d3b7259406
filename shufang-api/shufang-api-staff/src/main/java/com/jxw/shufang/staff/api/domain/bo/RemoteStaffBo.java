package com.jxw.shufang.staff.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 远程服务产品业务对象
 *
 *
 * @date 2024-02-21
 */
@Data
public class RemoteStaffBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工id
     */
    private Long branchStaffId;

    /**
     * 分店员工idList
     */
    private List<Long> branchStaffIds;

    /**
     * 考勤id
     */
    private String checkWorkAttendanceId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 真实头像(图片ossid)
     */
    private Long realAvatar;

    /**
     * 微信二维码(图片ossid)
     */
    private Long wechatQrCode;

    /**
     * 带有系统用户信息,默认不查
     */
    private Boolean withSysUserInfo;

    /**
     * 名称
     */
    private String name;

    /**
     * 门店员工对应sysUser表的idList
     */
    private List<Long> userIdList;

    private List<Long> roleIds;

    private List<Long> branchIdList;


}
