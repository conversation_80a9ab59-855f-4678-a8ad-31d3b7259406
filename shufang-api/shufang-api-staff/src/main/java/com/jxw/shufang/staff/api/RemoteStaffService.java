package com.jxw.shufang.staff.api;


import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;

import java.util.List;

/**
 * 远程员工服务
 *
 */
public interface RemoteStaffService {

    /**
     * 查询员工列表
     *
     * @param bo 查询条件
     * @date 2024/03/01 12:51:27
     */
    List<RemoteStaffVo> queryStaffList(RemoteStaffBo bo);

    /**
     * 根据用户id查询员工信息
     * @param userId
     * @return
     */
    RemoteStaffVo queryStaffByUserId(Long userId);



    /**
     * 查询员工id列表
     *
     * @param bo bo
     * @date 2024/03/04 03:15:38
     */
    List<Long> queryStaffIdList(RemoteStaffBo bo);
}
