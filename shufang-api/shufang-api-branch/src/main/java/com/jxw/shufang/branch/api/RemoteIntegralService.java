package com.jxw.shufang.branch.api;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 积分服务
 */
public interface RemoteIntegralService {

    /**
     * 根据会员id获取积分
     *
     * @param studentId 会员id
     * @date 2024/04/24 01:36:45
     */
    BigDecimal getIntegralByStudentId(Long studentId);


    /**
     * 根据会员idList获取积分
     *
     * @param studentIdList 会员id列表
     * @date 2024/04/24 01:36:45
     */
    Map<Long, BigDecimal> getIntegralByStudentIdList(List<Long> studentIdList);
}
