package com.jxw.shufang.branch.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 远程服务分店业务对象 branch
 * @date 2024-02-21
 */
@Data
public class RemoteBranchBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分店名称
     */
    private String branchName;

    /**
     * 分店状态（0正常 1停用）
     */
    private String branchStatus;

    /**
     * 分店授权记录id
     */
    private Long branchAuthRecordId;

    private Long deptParentId;

    private String adminUserName;

    private String adminNickName;

    /**
     * 创建部门IDs
     */
    private Set<Long> createDeptIds;

    /**
     * 门店授权类型id
     */
    private Long branchAuthTypeId;

    /**
     * 门店管理员用户id列表
     */
    private List<Long> branchAdminUserIdList;

    /**
     * 分店ids
     */
    private List<Long> branchIds;

}
