package com.jxw.shufang.branch.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RemoteBranchConfigVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 门店参数配置ID
     */
    private Long branchConfigId;
    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 参数配置类型
     */
    private Integer configType;
    /**
     * 配置参数json
     */
    private String configJson;

    public RemoteBranchConfigVo(Long branchConfigId,Long branchId,Integer configType){
        this.branchConfigId =branchConfigId;
        this.branchId =branchId;
        this.configType =configType;
    }

    private List<ProducePreferentialConfig> producePreferentialConfigList;

    /**
     * 正式会员卡优惠额度
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProducePreferentialConfig implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 产品id
         */
        private Long produceId;
        /**
         * 优惠金额
         */
        private BigDecimal preferentialAmount;
        /**
         * 冻结日期
         */
        private Integer frozenDay;
    }
}
