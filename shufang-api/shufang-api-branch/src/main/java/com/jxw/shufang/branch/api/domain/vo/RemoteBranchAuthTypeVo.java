package com.jxw.shufang.branch.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class RemoteBranchAuthTypeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权类型id
     */
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    private BigDecimal branchAuthTypeCost;

    /**
     * 课程ids
     */
    private String courseIds;

    /**
     * 产品ids
     */
    private String productIds;

    /**
     * 创建时间
     */
    private Date createTime;


}
