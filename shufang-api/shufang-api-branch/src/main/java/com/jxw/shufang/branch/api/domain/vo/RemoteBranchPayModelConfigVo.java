package com.jxw.shufang.branch.api.domain.vo;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.json.utils.JsonUtils;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/16 11:28
 * @Version 1
 * @Description
 */
@Data
public class RemoteBranchPayModelConfigVo  implements Serializable {
    @Serial
    private static final long serialVersionUID = 7877356102263669333L;
    /**
     * 门店参数配置ID
     */
    private Long branchConfigId;
    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 参数配置类型
     */
    private Integer configType;
    /**
     * 配置参数json
     */
    private String configJson;


    public Boolean existProductConfig(Long produceId){
        PayModelDetail payModelDetailId = findPayModelDetailId(produceId);
        return null != payModelDetailId;
    }

    public PayModelDetail findPayModelDetailId(Long produceId) {
        if (null == produceId) {
            return null;
        }
        List<PayModelDetail> payModelDetailList = this.getPayModelDetail();
        if (CollectionUtil.isEmpty(payModelDetailList)) {
            return null;
        }
        return payModelDetailList.stream()
            .filter(payModelDetail -> payModelDetail.getProduceId().equals(produceId))
            .findFirst().orElse(null);
    }

    public List<PayModelDetail> getPayModelDetail() {
        if (StringUtils.isEmpty(configJson)) {
            return Collections.emptyList();
        }
        List<PayModelDetail> payModelDetailList = JsonUtils.parseArray(configJson, PayModelDetail.class);
        if (CollectionUtil.isEmpty(payModelDetailList)) {
            return Collections.emptyList();
        }
        return payModelDetailList;
    }

    @Data
    public static class PayModelDetail {
        /**
         * 产品id
         */
        private Long produceId;
        /**
         * 产品名称
         */
        private String produceName;
        /**
         * 是否分期
         */
        private Boolean installmentFlag;
        /**
         * 分期期限天数
         */
        private Long installmentDeadlineDays;
    }
}
