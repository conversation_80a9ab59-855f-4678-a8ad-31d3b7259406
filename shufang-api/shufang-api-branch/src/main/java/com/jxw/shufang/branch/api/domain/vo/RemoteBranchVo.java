package com.jxw.shufang.branch.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class RemoteBranchVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分店名称
     */
    private String branchName;

    /**
     * 分店状态（0正常 1停用）
     */
    private String branchStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 分店授权记录id
     */
    private Long branchAuthRecordId;

    /**
     * 创建部门(同时也是门店对应的部门)
     */
    private Long createDept;

    /**
     * 平板端 自学系统状态（0关闭 1开启）
     */
    private Integer selfStudySystemStatus;

    /**
     * 平板端 学生自讲状态(0关闭 1开启)
     */
    private Integer studentSpeakingStatus;

    /**
     * 平板端 智慧中小学状态(0关闭  1开启)
     */
    private Integer smartPrimarySecondarySchoolStatus;
    /**
     * 平板端 倍速管控(0关闭 1开启)
     */
    private Integer speedControlStatus;
    /**
     * 平板端 拍照管控(0关闭 1开启)
     */
    private Integer photoStatus;

    private Integer remainTime;

    private String branchAuthTypeName;

    private String adminNickName;

    private String adminUserName;
}




