package com.jxw.shufang.branch.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Data
public class RemoteBranchConfigBo  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 参数配置类型
     */
    private Integer configType;
}
