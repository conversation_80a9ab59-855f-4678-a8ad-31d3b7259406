package com.jxw.shufang.branch.api;

import com.jxw.shufang.branch.api.domain.bo.RemoteBranchConfigBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchConfigVo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchPayModelConfigVo;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
public interface RemoteBranchConfigService {
    /**
     * 获取优惠额度配置
     *
     * @param bo @return
     */
    RemoteBranchConfigVo remoteGetPreferentialAmountConfig(RemoteBranchConfigBo bo);

    RemoteBranchPayModelConfigVo remoteGetPayModelConfig(Long branchId);
}
