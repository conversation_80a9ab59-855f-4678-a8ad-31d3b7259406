package com.jxw.shufang.branch.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 远程服务门店授权类型对象 branchAuthType
 * @date 2024-03-21
 */

@Data
public class RemoteBranchAuthTypeBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权类型id
     */
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    private BigDecimal branchAuthTypeCost;

    /**
     * 课程ids
     */
    private String courseIds;

    /**
     * 产品ids
     */
    private String productIds;


}
