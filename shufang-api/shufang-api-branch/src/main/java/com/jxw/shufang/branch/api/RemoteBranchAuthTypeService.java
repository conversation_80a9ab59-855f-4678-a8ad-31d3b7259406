package com.jxw.shufang.branch.api;

import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.List;

/**
 * 门店类型服务
 *
 */
public interface RemoteBranchAuthTypeService {


    RemoteBranchAuthTypeVo getAuthTypeByBranchId(Long branchId) throws ServiceException;

    List<RemoteBranchAuthTypeVo> getAuthTypeListByBranchIdList(List<Long> branchIdList) throws ServiceException;

}
