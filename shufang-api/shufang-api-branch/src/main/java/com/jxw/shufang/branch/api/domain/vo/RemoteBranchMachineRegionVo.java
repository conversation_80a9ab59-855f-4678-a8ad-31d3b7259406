package com.jxw.shufang.branch.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



@Data
public class RemoteBranchMachineRegionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店机位分区id
     */
    private Long branchMachineRegionId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分区名称
     */
    private String regionName;

    /**
     * 分区数量
     */
    private Long regionNum;

    /**
     * 机位图片（oss_id 每个分区的机位分布示意图）
     */
    private Long regionImg;


    private Date createTime;

    private RemoteBranchVo branch;
}
