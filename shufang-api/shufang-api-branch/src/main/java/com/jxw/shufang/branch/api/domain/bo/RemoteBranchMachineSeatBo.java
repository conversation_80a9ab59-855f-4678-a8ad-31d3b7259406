package com.jxw.shufang.branch.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


@Data
public class RemoteBranchMachineSeatBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机位id
     */
    private Long branchMachineSeatId;

    /**
     * 分店机位分区id
     */
    private Long branchMachineRegionId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 机位号（数字，如20号机位，则为20）
     */
    private Long seatNo;


    /**
     * 使用开始时间（对应学习规划中某课程的学习开始时间）
     */
    private Date useStartTime;

    /**
     * 使用结束时间（对应学习规划中某课程的学习结束时间）
     */
    private Date useEndTime;

    private List<Long> seatNoList;

    private List<Long> studentIdList;

    /**
     * 使用时间范围查询开始时间
     */
    private Date rangeStartTime;

    /**
     * 使用时间范围查询结束时间
     */
    private Date rangeEndTime;

}
