package com.jxw.shufang.branch.api;


import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemotePayMerchantConfigVO;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * 门店服务
 */
public interface RemoteBranchService {

    /**
     * 通过部门Id获取门店Id
     *
     * @param deptId 部门Id
     * @return 门店信息
     */
    RemoteBranchVo selectBranchByDeptId(Long deptId) throws ServiceException;

    /**
     * 通过部门Id获取门店Id
     *
     * @param branchId 门店ID
     * @return 门店信息
     */
    RemoteBranchVo selectBranchById(Long branchId) throws ServiceException;

    /**
     * 通过门店ID集合 查询门店信息列表
     * @param branchIds
     * @return
     */
    List<RemoteBranchVo> selectBranchList(List<Long> branchIds);

    /**
     * 通过部门Id获取门店授权情况
     *
     * @param deptId 部门Id
     * @return 授权情况
     */
    Boolean selectAuthStatusByDeptId(Long deptId) throws ServiceException;

    Long selectDeptIdByBranchId(Long branchId) throws ServiceException;

    List<RemoteBranchVo> selectBranchList(RemoteBranchBo remoteBranchBo);

    List<RemoteBranchVo> selectBranchList(RemoteBranchBo remoteBranchBo, boolean ignoreDataPermission);

    /**
     * 通过部门Id列表获取门店Id map
     * key: 部门Id, value: 门店Id
     * @param deptIdList
     * @param filterStopBranch 是否过滤停用的门店
     * @return
     */
    Map<Long, Long> selectBranchIdMapByDeptIdList(List<Long> deptIdList,Boolean filterStopBranch) throws ServiceException;

    /**
     * 获取门店的支付配置
     *
     * @param branchId
     * @return
     */
    String getCommonPayAppId(Long branchId);

    List<RemoteBranchVo> queryStaffBranchList(Long userId);

    RemotePayMerchantConfigVO getCommonPayConfig(Long branchId);

    /**
     * 修改门店有效天数，如果天数不足，则返回false
     */
    Boolean transfer(Long branchId, Integer amount);
}
