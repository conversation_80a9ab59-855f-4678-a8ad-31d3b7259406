package com.jxw.shufang.branch.api;


import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;

import java.util.List;

/**
 * 远程座位服务
 */
public interface RemoteBranchMachineSeatService {

    List<RemoteBranchMachineSeatVo> queryList(RemoteBranchMachineSeatBo remoteBranchMachineSeatBo);

    Boolean delBySeatIdList(List<Long> list);

    Boolean insertBatchByBo(List<RemoteBranchMachineSeatBo> seatInsertList);
}
