package com.jxw.shufang.crm.api;

import com.jxw.shufang.crm.api.domain.vo.RemoteCrmStudentBo;

public interface RemoteCrmStudentService {
    /**
     * 处理订单成功后的逻辑：转池动作
     *
     * @param remoteCrmStudentBo
     * @param operator
     * @param operateDept
     */
    void dealWIthPaySuccess(RemoteCrmStudentBo remoteCrmStudentBo, Long operator, Long operateDept);

    void updateUserInfo(RemoteCrmStudentBo remoteCrmStudentBo);
}
