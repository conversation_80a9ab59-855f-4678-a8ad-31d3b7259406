package com.jxw.shufang.crm.api.domain;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/5/21 18:01
 * @Version 1
 * @Description 季度维护数据
 */
@Data
public class QuarterDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1800969928517386242L;
    private String quarterName;

    /**
     * 季度开始时间
     */
    private Date startTime;

    /**
     * 季度结束时间
     */
    private Date endTime;
}
