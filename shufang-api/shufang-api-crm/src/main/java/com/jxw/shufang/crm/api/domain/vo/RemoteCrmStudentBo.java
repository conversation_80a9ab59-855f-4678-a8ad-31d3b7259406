package com.jxw.shufang.crm.api.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: cyj
 * @date: 2025/5/27
 */
@Data
public class RemoteCrmStudentBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 书房会员id
     */
    private Long mainStudentId;
    /**
     * 是否是体验卡，不是体验卡都当作正式卡处理， // TODO: 2025/5/27 CYJ 先购买什么卡转到什么池，待转换方案细则出来之后在调整
     */
    private boolean isExperience = true;

    private Long experienceStudentTypeId;
    /**
     * 会员名称
     */
    private String studentName;

    /**
     * 来源（对应字典值）
     */
    private String studentSource;
    /**
     * 性别（0男 1女）
     */
    private String studentSex;

    /**
     * 年级（对应字典值）
     */
    private String studentGrade;
    /**
     * 操作人
     */
    private Long operator;
    /**
     * 操作人所在部门
     */
    private Long operateDept;
}
