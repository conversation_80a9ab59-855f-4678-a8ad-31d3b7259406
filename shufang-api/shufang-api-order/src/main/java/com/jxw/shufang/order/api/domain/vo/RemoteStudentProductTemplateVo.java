package com.jxw.shufang.order.api.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 产品（课程模板）信息视图对象 student_product_course_template
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Data
public class RemoteStudentProductTemplateVo extends RemoteStudentBaseTemplateAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品模板信息-主键
     */
    private Long productTemplateId;

    /**
     * 产品模板名称
     */
    private String productTemplateName;

    /**
     * 产品模板描述
     */
    private String productTemplateDesc;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    private Integer productTemplateType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态 0-上架 1-下架
     */
    private Integer status;

    /**
     * 关联的资源ID集合
     */
    private List<Long> resIds;

    /**
     * 涉及的学段名称集合
     */
    private Set<String> stageNames = new HashSet<>();

    /**
     * 会员卡名称列表
     */
    private Set<String> memberNames = new HashSet<>();

    /**
     * 添加学段名称集合
     * @param stageName
     */
    public void addStageName(String stageName) {
        if (ObjectUtil.isEmpty(stageName)) return;
        stageNames.add(stageName);
    }

    /**
     * 添加会员卡名称
     */
    public void addMemberName(String memberName) {
        if (ObjectUtil.isEmpty(memberName)) return;
        memberNames.add(memberName);
    }

}
