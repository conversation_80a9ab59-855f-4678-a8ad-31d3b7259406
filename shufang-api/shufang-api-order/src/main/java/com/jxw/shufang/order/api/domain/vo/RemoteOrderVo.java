package com.jxw.shufang.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class RemoteOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    private Long salesPerson;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 经办日期
     */
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;

    /**
     * 订单操作id
     */
    private Long orderOperateId;

    /**
     * 订单操作对象
     */
    private RemoteOrderOperateVo orderOperate;

    /**
     * 订单产品名字组合，逗号分隔
     */
    private String productNameGroup;

    /**
     * 会员类型id组合，逗号分隔
     */
    private String studentTypeIdGroup;

    /**
     * 订单产品总价
     */
    private BigDecimal productPriceSum;

    /**
     * 优惠总价
     */
    private BigDecimal preferentialPriceSum;

    /**
     * 实际支付总价
     */
    private BigDecimal actualPayPrice;

    /**
     * 订单产品ID组合，逗号分隔
     */
    private String productIdGroup;

    /**
     * 订单操作列表
     */
    List<RemoteOrderOperateVo> orderOperateList;

    /**
     * 订单产品信息列表
     */
    List<RemoteOrderProductInfoVo> orderProductInfoList;

    /**
     * 支付操作信息
     */
    private RemoteOrderOperateVo paymentOperate;

    /**
     * 退款操作信息
     */
    private RemoteOrderOperateVo refundOperate;

    /**
     * 订单当前状态
     */
    private String orderStatus;

    /**
     * 剩余天数
     */
    private Long remainingDays;

    /**
     * 使用天数
     */
    private Long usedDays;

    /**
     * 订单数量
     */
    private Integer orderNum;

    /**
     * 是否分期付款 false-整笔支付, true-分期支付
     */
    private Boolean installmentFlag;

}




