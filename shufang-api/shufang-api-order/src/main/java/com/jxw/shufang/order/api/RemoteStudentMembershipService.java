package com.jxw.shufang.order.api;

import com.jxw.shufang.order.api.domain.bo.RemoteStudentMembershipBo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;

import java.util.Date;
import java.util.List;

/**
 * 远程学生会员卡服务
 *
 * @author: cyj
 * @date: 2025/5/28
 */
public interface RemoteStudentMembershipService {

    /**
     * 查询会员卡列表
     */
    List<RemoteStudentMembershipCardVo> listMembershipCard(RemoteStudentMembershipBo remoteStudentMembershipBo);

    /**
     * 查询指定时间段内的在籍会员
     * 根据会员卡有效期判断是否在籍
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 在籍会员的会员卡列表
     */
    List<RemoteStudentMembershipCardVo> getEnrolledStudentsInPeriod(Date startDate, Date endDate);

    /**
     * 查询所有在籍会员
     * 会员卡有效期大于当前时间的会员
     *
     * @return 在籍会员的会员卡列表
     */
    List<RemoteStudentMembershipCardVo> getAllEnrolledStudents();

    /**
     * 检查学生是否在籍
     *
     * @param studentId 学生ID
     * @return 是否在籍
     */
    Boolean isStudentEnrolled(Long studentId);

    /**
     * 检查学生在指定时间段内是否在籍
     *
     * @param studentId 学生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否在籍
     */
    Boolean isStudentEnrolledInPeriod(Long studentId, Date startDate, Date endDate);

    /**
     * 获取学生的会员卡过期时间
     *
     * @param studentId 学生ID
     * @return 过期时间
     */
    Date getStudentExpireTime(Long studentId);

    /**
     * 查询学生的会员卡
     * @param studentIds
     */
    List<RemoteStudentMembershipCardVo> queryStudentMembershipCard(List<Long> studentIds);
}
