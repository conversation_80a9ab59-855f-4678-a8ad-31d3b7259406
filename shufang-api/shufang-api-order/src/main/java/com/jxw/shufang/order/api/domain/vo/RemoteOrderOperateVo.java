package com.jxw.shufang.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



@Data
public class RemoteOrderOperateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单操作id
     */
    private Long orderOperateId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
    private String orderOperateStatus;

    /**
     * 支付方式（已支付才存在 对应字典值，比如 微信、支付宝）
     */
    private String paymentType;

    /**
     * 收款金额（已支付才存在）
     */
    private BigDecimal paymentAmount;

    /**
     * 退款方式（已退款才存在 对应字典值，比如 微信、支付宝）
     */
    private String refundType;

    /**
     * 退款金额（已退款才存在）
     */
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    private String orderOperateRemark;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;


}
