package com.jxw.shufang.order.api.domain.bo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: cyj
 * @date: 2025/5/28
 */
@Data
public class RemoteStudentMembershipBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     * 学生产品表ID
     */
    private Long studentMembershipCardId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 学生ID列表（批量查询）
     */
    private List<Long> studentIds;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品ID列表（批量查询）
     */
    private List<Long> productIds;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单ID列表（批量查询）
     */
    private List<Long> orderIds;

    /**
     * 会员卡类型ID
     */
    private Long studentTypeId;

    /**
     * 会员卡类型ID列表（批量查询）
     */
    private List<Long> studentTypeIds;

    /**
     * 会员卡状态(0失效--退费 1启用--付款)
     */
    private Integer cardStatus;

    /**
     * 会员卡状态列表（批量查询）
     */
    private List<Integer> cardStatusList;

    /**
     * 产品开始时间 - 查询起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productBeginDateStart;

    /**
     * 产品开始时间 - 查询结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productBeginDateEnd;

    /**
     * 产品结束时间 - 查询起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productEndDateStart;

    /**
     * 产品结束时间 - 查询结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productEndDateEnd;

    /**
     * 创建时间 - 查询起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    /**
     * 创建时间 - 查询结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    /**
     * 分支机构ID
     */
    private Long branchId;

    /**
     * 分支机构ID列表（批量查询）
     */
    private List<Long> branchIds;

    /**
     * 学生姓名（模糊查询）
     */
    private String studentName;

    /**
     * 学生账号（模糊查询）
     */
    private String studentAccount;

    /**
     * 产品名称（模糊查询）
     */
    private String productName;

    /**
     * 是否只查询在籍会员
     * true: 只查询当前时间在产品有效期内且状态为启用的会员卡
     */
    private Boolean onlyEnrolled;

    /**
     * 是否只查询启用状态的会员卡
     */
    private Boolean onlyEnabled;

    /**
     * 查询时间段内有效的会员卡
     * 会员卡的有效期与指定时间段有交集
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validPeriodStart;

    /**
     * 查询时间段内有效的会员卡
     * 会员卡的有效期与指定时间段有交集
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validPeriodEnd;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向：asc/desc
     */
    private String orderDirection;

    /**
     * 非该卡类型id
     * 页码
     */
    private Long neStudentTypeId;
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 获取默认的在籍会员查询条件
     */
    public static RemoteStudentMembershipBo createEnrolledQuery() {
        RemoteStudentMembershipBo bo = new RemoteStudentMembershipBo();
        bo.setOnlyEnrolled(true);
        bo.setCardStatus(1); // 启用状态
        return bo;
    }

    /**
     * 获取指定学生的在籍会员查询条件
     */
    public static RemoteStudentMembershipBo createEnrolledQueryForStudent(Long studentId) {
        RemoteStudentMembershipBo bo = createEnrolledQuery();
        bo.setStudentId(studentId);
        return bo;
    }

    /**
     * 获取指定时间段内的在籍会员查询条件
     */
    public static RemoteStudentMembershipBo createEnrolledQueryForPeriod(Date startDate, Date endDate) {
        RemoteStudentMembershipBo bo = createEnrolledQuery();
        bo.setValidPeriodStart(startDate);
        bo.setValidPeriodEnd(endDate);
        return bo;
    }
}
