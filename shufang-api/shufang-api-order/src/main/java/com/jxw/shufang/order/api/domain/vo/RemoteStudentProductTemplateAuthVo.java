package com.jxw.shufang.order.api.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 产品（会员卡模板）授权视图对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data

public class RemoteStudentProductTemplateAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板权限id
     */
    @ExcelProperty(value = "模板权限id")
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
    @ExcelProperty(value = "模板id")
    private Long templateId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long deptId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品有效天数
     */
    @ExcelProperty(value = "产品有效天数")
    private Long productValidDays;

    /**
     * 产品状态（0上架 1下架）
     */
    @ExcelProperty(value = "产品状态")
    private String productStatus;

    /**
     * 授权状态 0-未授权 1-已授权
     */
    private Integer authStatus;


}
