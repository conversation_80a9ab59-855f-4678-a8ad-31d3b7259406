package com.jxw.shufang.order.api.domain.bo;

import lombok.Data;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 远程服务订单业务对象
 *
 * @date 2024-02-21
 */
@Data
public class RemoteOrderBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    private Long salesPerson;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 经办日期
     */
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;

    /**
     * 订单操作id
     */
    private Long orderOperateId;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    private List<Long> studentIdList;

    private List<Long> notInStudentIdList;

    /**
     * 订单状态，枚举类{@link OrderStatusEnum}
     */
    private String orderStatus;

    private List<String> orderStatusList;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品IdList
     */
    private List<Long> productIdList;

    /**
     * 付款时间(订单操作时间)开始
     */
    private Date paymentStartTime;

    /**
     * 付款时间(订单操作时间)结束
     */
    private Date paymentEndTime;


    /**
     * 订单金额范围,开始
     */
    private String orderAmountStart;

    /**
     * 订单金额范围,结束
     */
    private String orderAmountEnd;


    private List<Long> salesPersonIdList;

    private Long excludeStudentTypeId;

    private Long singleStudentTypeId;

}
