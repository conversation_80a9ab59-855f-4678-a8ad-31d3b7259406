package com.jxw.shufang.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/5/28
 */
@Data
public class RemoteStudentMembershipCardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学生产品表ID
     */
    private Long studentMembershipCardId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 会员卡类型ID
     */
    private Long studentTypeId;

    /**
     * 会员卡状态(0失效--退费 1启用--付款)
     */
    private Integer cardStatus;

    /**
     * 产品开始时间
     */
    private Date productBeginDate;

    /**
     * 产品结束时间
     */
    private Date productEndDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分支机构ID
     */
    private Long branchId;

    /**
     * 学生姓名（冗余字段，便于显示）
     */
    private String studentName;

    /**
     * 学生账号（冗余字段，便于显示）
     */
    private String studentAccount;

    /**
     * 产品名称（冗余字段，便于显示）
     */
    private String productName;

    /**
     * 会员卡类型名称（冗余字段，便于显示）
     */
    private String studentTypeName;

    /**
     * 是否在籍（计算字段）
     * 根据当前时间和产品有效期计算
     */
    private Boolean isEnrolled;

    /**
     * 剩余天数（计算字段）
     * 产品结束时间 - 当前时间
     */
    private Integer remainingDays;

    /**
     * 会员卡状态描述
     */
    public String getCardStatusDesc() {
        if (cardStatus == null) {
            return "未知";
        }
        return switch (cardStatus) {
            case 0 -> "失效";
            case 1 -> "启用";
            default -> "未知";
        };
    }

    /**
     * 检查会员卡是否有效
     */
    public Boolean isValid() {
        if (cardStatus == null || cardStatus != 1) {
            return false;
        }

        Date now = new Date();
        return (productBeginDate == null || !productBeginDate.after(now)) &&
               (productEndDate == null || !productEndDate.before(now));
    }

    /**
     * 计算剩余天数
     */
    public Integer calculateRemainingDays() {
        if (productEndDate == null) {
            return null;
        }

        Date now = new Date();
        if (productEndDate.before(now)) {
            return 0;
        }

        long diffInMillies = productEndDate.getTime() - now.getTime();
        return (int) (diffInMillies / (1000 * 60 * 60 * 24));
    }
}
