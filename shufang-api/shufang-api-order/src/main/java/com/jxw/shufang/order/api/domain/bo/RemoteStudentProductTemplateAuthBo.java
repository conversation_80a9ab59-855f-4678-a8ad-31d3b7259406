package com.jxw.shufang.order.api.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 产品（会员卡模板）授权业务对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
public class RemoteStudentProductTemplateAuthBo implements Serializable {

    /**
     * 模板权限id
     */
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 代理商id
     */
    private Long deptId;

    /**
     * 代理商ID集合
     */
    private List<Long> deptIds;

    /**
     * 会员ID
     */
    private Long studentId;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    private Integer templateType = 0;

    /**
     * 是否只返回授权的模板列表，默认false
     */
    private Boolean showAuth;

    /**
     * 模板信息列表
     */
    private List<TemplateInfo> templateInfos = new ArrayList<>();

    /**
     * 模板信息对象
     */
    @Data
    public static class TemplateInfo {
        /**
         * 模板类型 0-会员卡模板 1-课程模板
         */
        private Integer templateType = 0;

        /**
         * 模板ID数组，为空将清除所有
         */
        private List<Long> templateIds;
    }


}
