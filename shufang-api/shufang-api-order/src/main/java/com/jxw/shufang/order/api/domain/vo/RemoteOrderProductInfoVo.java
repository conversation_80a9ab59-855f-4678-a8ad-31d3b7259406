package com.jxw.shufang.order.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;



@Data
public class RemoteOrderProductInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情id
     */
    private Long orderInfoId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 会员类型id
     */
    private Long studentTypeId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品有效天数
     */
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    private BigDecimal preferentialPrice;

}
