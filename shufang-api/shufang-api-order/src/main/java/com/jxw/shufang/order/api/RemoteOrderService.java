package com.jxw.shufang.order.api;


import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentProductTemplateVo;

import java.util.List;

/**
 * 远程订单服务
 */
public interface RemoteOrderService {


    /**
     * 获取有效订单会员ID
     *
     * @param studentIdList 会员id列表
     * @param productIdList 产品id列表(可为空)
     * @date 2024/03/05 11:43:46
     */
    List<Long> getEffectiveOrderStudentIds(List<Long> studentIdList, List<Long> productIdList);


    /**
     * 选择订单列表和信息
     *
     * @param remoteOrderBo        远程订单bo
     * @param ignoreDataPermission 忽略数据权限
     * @date 2024/03/10 04:00:52
     */
    List<RemoteOrderVo> selectOrderListAndInfo(RemoteOrderBo remoteOrderBo,boolean ignoreDataPermission);

    /**
     * 查询会员最后订单
     *
     * @param remoteOrderBo 远程订单bo
     * @date 2024/03/10 10:38:28
     */
    List<RemoteOrderVo> selectStudentLastOrder(RemoteOrderBo remoteOrderBo);

    /**
     * 获取某个订单类型的订单信息
     *
     * @param studentId
     * @param excludeStudentTypeId 排除
     * @return
     */
    RemoteOrderVo getRemoteOrder(Long studentId, Long excludeStudentTypeId);

    /**
     * 获取多个书房会员的最近一个订单信息
     *
     * @return
     */
    List<RemoteOrderVo> getBatchStudentLastOrder(RemoteOrderBo remoteOrderBo);

    /**
     * 获取多个书房会员的最近一个订单信息
     *
     * @return
     */
    List<RemoteOrderVo> listStudentOrder(RemoteOrderBo remoteOrderBo);


    /**
     * 查询代理商授权模板列表
     * @param bo
     * @return
     */
    List<RemoteStudentProductTemplateVo> queryAuthList(RemoteStudentProductTemplateAuthBo bo);

    /**
     * 查询代理商授权课程ID集合
     */
    List<Long> queryAuthCourseIds(RemoteStudentProductTemplateAuthBo bo);


}
