package com.jxw.shufang.report.api;

import com.jxw.shufang.report.api.domain.bo.RemotePaidMemberStaffsBo;
import com.jxw.shufang.report.api.domain.vo.RemotePaidMemberStaffsVo;

import java.util.Map;

/**
 * 远程会员卡统计业务接口
 */
public interface RemotePaidMemberService {

    /**
     * 查询 正式卡会员/体验卡会员ID集合 - 根据 顾问ID集合、开始时间、结束时间
     *
     * @param bo - 查询请求参数
     * @return Map集合，key：顾员ID，value：正式卡会员/体验卡会员ID集合
     */
    Map<Long, RemotePaidMemberStaffsVo> getPaidMemberMapByStaffs(RemotePaidMemberStaffsBo bo);
}
