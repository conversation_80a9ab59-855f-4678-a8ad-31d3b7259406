package com.jxw.shufang.report.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
public class RemotePaidMemberStaffsVo implements Serializable {

    /**
     * 正式卡会员ID集合
     */
    private Set<Long> paidMemberIdList;

    /**
     * 体验卡会员ID集合
     */
    private Set<Long> experienceMemberIdList;

    /**
     * 新签会员ID集合
     */
    private Set<Long> newSignMemberIdList;

    /**
     * 续费会员ID集合
     */
    private Set<Long> renewMemberIdList;

    /**
     * 续费次数
     */
    private Integer renewNum;

    /**
     * 有学习计划的人数
     */
    private Integer studyPersonNum;

    /**
     * 有批改记录的人数
     */
    private Integer correctionPersonNum;

    /**
     * 批改率
     */
    private Integer correntRate;

    /**
     * 反馈人数（正式 + 体验）
     */
    private Integer feedbackPersonNum;

    /**
     * 反馈率
     */
    private Integer feedbackRateAvg;

    /**
     * 跟进会员（正式反馈人数）
     */
    private Integer followPersonNum;

}
