package com.jxw.shufang.asynctask.api.domain;


import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 远程异步任务
 *
 * @date 2024/02/01 05:33:45
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class RemoteAsyncTask implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务标识
     */
    private String taskCode;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务参数
     */
    private String taskParams;

    /**
     * 任务参数
     */
    private String taskBody;

    /**
     * 任务参数
     */
    private String headerMap;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求url
     */
    private String url;

    /**
     * 1 http 2filedownload
     */
    private Integer type;

    private String loginId;

    private String tokenPrefix;

    private String clentid;

    private String clientSecret;

    private String tokenName;

    private String token;

    private String tokenType;

    private Long tenantId;

    private Long userId;

    private Long deptId;



}
