package com.jxw.shufang.asynctask.api;

import com.jxw.shufang.asynctask.api.domain.RemoteAsyncTask;

import java.util.Map;

/**
 * 异步任务服务
 *
 *
 */
public interface RemoteAsyncTaskService {


    /**
     * 获取唯一code
     *
     * @return 客户端对象
     */
    String getCode();


    /**
     * 添加任务
     *
     * @param remoteAsyncTask 远程异步任务
     * @date 2024/02/01 05:42:26
     */
    Long addTask(RemoteAsyncTask remoteAsyncTask);


    /**
     * 设置通行标志
     *
     * @param headerMap 标头映射
     * @date 2024/02/08 11:07:23
     */
    void setPassSign(Map<String,String> headerMap);


    /**
     * 拥有通过标志
     *
     * @param headerMap 标头映射
     * @date 2024/02/08 11:07:27
     */
    boolean isPass(Map<String,String> headerMap);


    void updateTaskSizeByTaskCode(String taskCode,Long size);

    void updateDoCountByTaskCode(String taskCode, Long doCount);
}
