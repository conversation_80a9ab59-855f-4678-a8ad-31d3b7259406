package com.jxw.shufang.resource.api;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.resource.api.domain.RemoteFile;

import java.util.List;

/**
 * 文件服务
 *
 */
public interface RemoteFileService {

    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    RemoteFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException;

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    String selectUrlByIds(String ossIds);

    /**
     * 通过ossId查询对应的名称
     *
     * @param ossId ossId
     * @return 文件名称
     */
    String selectNameById(String ossId);


    String selectUrlByIds(String ossIds, Integer second);

    /**
     * 通过ossId查询对应的文件
     *
     * @param ossIds oss-ids
     * @date 2024/02/26 12:11:31
     */
    List<RemoteFile> selectFileByIds(String ossIds);


    /**
     * 通过ossId查询对应的文件
     *
     * @param ossIdList oss-ids
     * @date 2024/02/26 12:11:31
     */
    List<RemoteFile> selectFileByIdList(List<Long> ossIdList);

}
