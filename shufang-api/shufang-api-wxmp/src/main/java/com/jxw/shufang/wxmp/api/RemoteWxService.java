package com.jxw.shufang.wxmp.api;

import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.Map;

/**
 * 微信远程服务
 */
public interface RemoteWxService {

    String getBoundQrCode(Long studentId);

    String getQrCode();

    String getFeedbackUrl(Long feedbackId);

    String getStudyPlanningUrl(Long studyPlanningId);

    /**
     * 发送反馈消息
     * @param openId
     * @param feedbackId
     * @param content
     * @param color hex颜色值
     * @return
     */
    Boolean sendFeedbackMessage(String openId,Long feedbackId,String title, String content,String color) throws ServiceException;


    Boolean sendFeedbackShareMessage(String openId, Long feedbackId, Map<String, String> dataMap) throws ServiceException;

    /**
     * 发送考勤消息
     * @param openId
     * @param content
     * @param color hex颜色值
     */
    Boolean sendAttendanceMessage(String openId,String title,String content,String color) throws ServiceException;



    String generateMiniProgramUrl(String query);

    String generatePayUrlByPath(String query, String path);

}
