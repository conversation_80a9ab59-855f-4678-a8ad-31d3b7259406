package com.jxw.shufang.system.api.domain.vo;


import lombok.Data;
import lombok.NoArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户信息业务对象 sys_user
 */

@Data
@NoArgsConstructor
public class RemoteUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phonenumber;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 头像ossId
     */
    private Long avatar;

    /**
     * 头像url
     */
    private String avatarUrl;



    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据权限 当前角色ID
     */
    private Long roleId;

    private Long[] roleIds;

    private List<RemoteRoleVo> roles;

    private Long[] postIds;

    /**
     * 是否已经绑定人脸
     */
    private Boolean faceBound = false;
    /**
     * 是否已经绑定人脸
     */
    private String faceUrl;

    public boolean isSuperAdmin() {
        return UserConstants.SUPER_ADMIN_ID.equals(this.userId);
    }

}
