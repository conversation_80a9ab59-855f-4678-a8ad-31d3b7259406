package com.jxw.shufang.system.api.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RemoteSysUserSignBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private Long userId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 版本
     */
    private String version;

    /**
     * 签署状态 0为否 1为是
     */
    private Integer signStatus;

}
