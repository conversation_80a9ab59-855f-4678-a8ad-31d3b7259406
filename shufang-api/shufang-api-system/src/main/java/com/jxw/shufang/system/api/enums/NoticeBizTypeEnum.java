package com.jxw.shufang.system.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NoticeBizTypeEnum {

    ZZPGSQ(1, "自主批改申请"),
    DAYINSQ(2, "打印申请"),
    LIUYAN(3, "留言"),
    ORDER_PAY(4, "支付通知"), ORDER_REFUND(5, "退款通知"),
    STUDY_FEEDBACK(6, "学习反馈"),
    STUDY_PLANNING(7, "学习规划"),
    STUDY_PLANNING_FEEDBACK(8, "学习规划反馈"),
    ;

    private final Integer code;
    private final String desc;

}
