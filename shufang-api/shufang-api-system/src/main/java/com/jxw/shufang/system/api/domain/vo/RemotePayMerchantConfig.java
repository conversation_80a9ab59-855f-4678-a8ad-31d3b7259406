package com.jxw.shufang.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/6/18 20:52
 * @Version 1
 * @Description
 */
@Data
public class RemotePayMerchantConfig implements Serializable {
    @Serial
    private static final long serialVersionUID = 8819275335151536959L;
    private String merchantName;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType;
    /**
     * 支付方式(unionpay-银联聚合支付)
     */
    private String payCode;
    /**
     * 聚合二维码的方式
     */
    private String wayCode;

    /**
     * 默认商户(0:否，1：是)
     */
    private Boolean defaultMerchant;

    /**
     * 配置json参数
     */
    private String configParamJson;
}
