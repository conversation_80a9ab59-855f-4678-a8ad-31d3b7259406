package com.jxw.shufang.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 属性管理视图对象 sys_client
 */
@Data
public class RemoteAttributeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 属性分组IDs
     */
    private String attributeGroupingIds;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)
     */
    private String inputType;

    /**
     * 属性可选值
     */
    private String attributeValues;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 排序
     */
    private Integer attributeSort;

    /**
     * 是否可以为空(0是 2否)
     */
    private String canNullStatus;

    /**
     * 父属性IDs
     */
    private String parentAttributeIds;

    /**
     * 是否需要列表展示（0是 2否）
     */
    private String showList;

    /**
     * 是否需要展示为搜索词（0是 2否）
     */
    private String search;

    /**
     * 页面列表中width
     */
    private Long width;

    /**
     * 页面列表中fixed
     */
    private String fixed;

    /**
     * 页面列表中sort
     */
    private Integer sort;

    /**
     * 页面列表中align
     */
    private String align;

    private RemoteAttributeRelationVo attributeRelation;
}
