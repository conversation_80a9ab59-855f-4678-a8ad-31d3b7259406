package com.jxw.shufang.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 远程部门对象
 */
@Data
public class RemoteDeptVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long deptId;

    private String deptName;

    private Boolean isStore;

    private Long parentId;

    /**
     * 负责人ID
     */
    private Long leader;
    /**
     * 机构logo对应resource文件ID
     */
    private Long systemLogo;

    private String systemLogoUrl;

    /**
     * 品牌名称
     */
    private String brandName;
}
