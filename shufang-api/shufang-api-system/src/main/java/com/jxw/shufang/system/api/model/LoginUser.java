package com.jxw.shufang.system.api.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 用户信息
 */
@Data
@NoArgsConstructor
public class LoginUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 菜单权限
     */
    private Set<String> menuPermission;

    /**
     * 角色权限
     */
    private Set<String> rolePermission;

    /**
     * 用户名
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 密码
     */
    private String password;

    /**
     * 角色对象
     */
    private List<RoleDTO> roles;

    /**
     * 数据权限 当前角色ID
     */
    private Long roleId;

    /**
     * 客户端
     */
    private String clientKey;

    /**
     * 设备类型
     */
    private String deviceType;


    /**
     * 用户登录时选择的部门id（业务需求）
     */
    private Long selectDeptId;

    /**
     * 用户登录时选择的角色id（业务需求）
     */
    private Long selectRoleId;

    /**
     * 用户的门店Id（业务需求,门店管理员才有）
     */
    private Long branchId;

    /**
     * 会员Id
     */
    private Long studentId;

    /**
     * 是否是门店管理员
     */
    private Boolean isBranchAdmin = false;

    /**
     * 是否是门店员工
     */
    private Boolean isBranchStaff = false;

    /**
     * 选中的角色标识
     */
    private String selectRoleSignKey;

    /**
     * 忽略数据权限范围校验
     */
    private Boolean ignoreDataScope = false;

    /**
     * 门店员工Id
     */
    private Long staffId;

    /**
     * 门店idList,一般在登录角色为门店管理员的时候会有，因为门店管理员可以管理多个门店
     */
    private List<Long> branchIdList;

    /**
     * 是否是会员顾问
     */
    private Boolean isConsultant = false;

    /**
     * 是否是销售顾问
     */
    private Boolean isSaleConsultant = false;

    /**
     * 是否是执行店长
     */
    private Boolean isExecutiveStoreManager = false;

    /**
     * 微信的openId
     */
    private String openId;
    /**
     * 是否绑定了人脸
     */
    private Boolean faceBound;


    /**
     * 获取登录id
     */
    public String getLoginId() {
        if (userType == null) {
            throw new IllegalArgumentException("用户类型不能为空");
        }
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userType + ":" + userId;
    }



    ///**
    // * 业务需求，如果有选角登录的场景下，选择选中的部门
    // *
    // * @date 2024/02/21 02:28:28
    // */
    //public Long getDeptId() {
    //    //if (selectDeptId != null) {
    //    //    return selectDeptId;
    //    //}
    //    return deptId;
    //}

}
