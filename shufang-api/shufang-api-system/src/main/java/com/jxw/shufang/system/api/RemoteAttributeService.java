package com.jxw.shufang.system.api;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.api.domain.vo.RemoteAttrRelationResultVo;
import com.jxw.shufang.system.api.domain.vo.RemoteAttributeVo;

import java.util.List;

/**
 * 客户端服务
 *
 */
public interface RemoteAttributeService {

    /**
     * 根据分组类型获取属性列表（不带值，只是拿到该有的属性列表）
     *
     * @param groupType 分组类型（表名）
     * @param branchId 分店id
     * @param withUniversally 是否包含通用属性
     * @return 属性列表
     */
    List<RemoteAttributeVo> getAttributeByGroupType(String groupType,Long branchId,boolean withUniversally);


    /**
     * 根据分组类型获取属性列表（带值）
     *
     * @param typeIdList          对应表的主键idList
     * @param groupType       组类型
     * @param branchId        分支id
     * @param withUniversally 具有普遍性
     * @date 2024/03/30 09:47:44
     */
    List<RemoteAttrRelationResultVo> getAttrWithValueByGroupType(List<Long> typeIdList, String groupType, Long branchId, boolean withUniversally);


    /**
     * 插入关系批
     *
     * @param remoteAttributeRelationBo 远程属性bo列表
     * @date 2024/04/01 10:27:16
     */
    boolean insertRelationBatch(List<RemoteAttributeRelationBo> remoteAttributeRelationBo) throws ServiceException;

    /**
     * 放入属性标头
     *
     * @param groupType       组类型
     * @param branchId        门店id
     * @param withUniversally 是否查询通用属性
     * @param headerList      表头列表
     * @param insertAttrIndex 从哪个位置插入属性表头
     * @date 2024/04/02 11:25:51
     */
    List<ExcelUtil.ExcelExportHeader> putAttrHeader(String groupType, Long branchId, boolean withUniversally,List<ExcelUtil.ExcelExportHeader> headerList,Integer insertAttrIndex);


    /**
     * 搜索(没找到会返回一个-1的list)
     *
     * @param searchJson 搜索json
     * @date 2024/04/10 07:22:40
     */
    List<Long> search(String searchJson,String groupType);


    /**
     * 更新类型关系
     *
     * @param type        类型
     * @param courseId    课程id
     * @param attrAddJson attr-add-json
     * @date 2024/04/16 07:55:00
     */
    void updateTypeRelation(String type, Long courseId, String attrAddJson) throws IllegalArgumentException;


    List<CourseAttributeDetailDTO> batchQueryAttributeByType(List<Long> courseId, String type);

    List<CourseAttributeDetailDTO> batchQueryAttributeByIdAndType(List<Long> courseIds, List<Long> typeAttributeIds,String type);
}
