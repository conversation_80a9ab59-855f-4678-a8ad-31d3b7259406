package com.jxw.shufang.system.api.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

@Data
public class RemoteNotifyMessageBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息模板id
     */
    @NotBlank(message = "消息模板不能为空", groups = {AddGroup.class})
    private String templateCode;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = {AddGroup.class})
    private String content;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空", groups = {AddGroup.class})
    private Integer bizType;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 通知类型 1-站内信
     */
    @NotNull(message = "通知类型不能为空", groups = {AddGroup.class})
    private Integer noticeType;

    /**
     * 发送人
     */
    @NotNull(message = "发送人不能为空", groups = {AddGroup.class})
    private Long fromUserId;

    /**
     * 发送人名字
     */
    private String fromUserName;

    /**
     * 接收人
     */
    @NotNull(message = "接收人不能为空", groups = {AddGroup.class})
    private Long toUserId;

    /**
     * 接收人名字
     */
    private String toUserName;

    /**
     * 发送内容
     */
    @NotEmpty(message = "发送内容不能为空", groups = {AddGroup.class})
    private Map<String, Object> paramMap;

}
