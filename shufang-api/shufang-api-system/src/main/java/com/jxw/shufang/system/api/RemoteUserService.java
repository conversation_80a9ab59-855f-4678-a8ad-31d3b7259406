package com.jxw.shufang.system.api;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.exception.user.UserException;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserStudentInfoVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.system.api.model.LoginUser;
import com.jxw.shufang.system.api.model.XcxLoginUser;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户服务
 */
public interface RemoteUserService {

    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfo(String username, String tenantId,boolean getBranchInfo) throws UserException,ServiceException;

    /**
     * 通过用户Id查询用户信息
     *
     * @param userId 用户Id
     * @return 结果
     */
    LoginUser getSpecifyUserInfo(Long userId) throws UserException,ServiceException;

    /**
     * 通过用户Id查询用户信息,通过覆盖的形式完成选择部门和角色的目的
     *
     * @param userId 用户Id
     * @return 结果
     */
    LoginUser getSpecifyUserInfo(Long userId,Long coverRoleId,Long coverDeptId) throws UserException,ServiceException;


    /**
     * 通过用户名查询用户信息,通过覆盖的形式完成选择部门和角色的目的
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @param coverRoleId 覆盖角色id
     * @param coverDeptId 覆盖部门id
     * @return 结果
     */
    LoginUser getUserInfo(String username, String tenantId, Long coverRoleId, Long coverDeptId) throws UserException,ServiceException;



    /**
     * 通过用户id查询用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfo(Long userId, String tenantId) throws UserException;

    /**
     * 通过手机号查询用户信息
     *
     * @param phonenumber 手机号
     * @param tenantId    租户id
     * @return 结果
     */
    LoginUser getUserInfoByPhonenumber(String phonenumber, String tenantId) throws UserException;

    /**
     * 通过邮箱查询用户信息
     *
     * @param email    邮箱
     * @param tenantId 租户id
     * @return 结果
     */
    LoginUser getUserInfoByEmail(String email, String tenantId) throws UserException;

    /**
     * 通过openid查询用户信息
     *
     * @param keyId keyId
     * @return 结果
     */
    XcxLoginUser getUserInfoByKeyId(String keyId) throws UserException;

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getUserInfoByOpenid(String openid) throws UserException;

    /**
     * 注册用户信息
     *
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    Boolean registerUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException;

    /**
     * 通过userId查询用户账户
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectUserNameById(Long userId);

    /**
     * 通过用户ID查询用户昵称
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectNicknameById(Long userId);

    /**
     * 更新用户信息登录信息
     *
     * @param userId 用户ID
     * @param ip     IP地址
     */
    void recordLoginInfo(Long userId, String ip);

    /**
     * 更新用户信息
     * @param remoteUserBo 用户信息
     * @return 结果
     */
    void updateUserInfo(RemoteUserBo remoteUserBo) throws ServiceException;

    /**
     * 更新用户信息
     * @param remoteUserBoList 用户信息列表
     * @return 结果
     */
    void batchUpdateUserInfo(List<RemoteUserBo> remoteUserBoList);

    /**
     * 通过用户名获取简单的用户信息
     *
     * @param username 用户名
     */
    RemoteUserVo getSimpleUserInfoByUsername(String username);

    /**
     * 通过用户id获取简单用户信息
     *
     * @param userId 用户id
     * @date 2024/03/02 02:24:28
     */
    RemoteUserVo getSimpleUserInfoByUserId(Long userId);

    /**
     * 插入用户,并返回id
     *
     * @param user 使用者
     * @throws ServiceException 业务异常
     * @date 2024/02/28 12:02:08
     */
    Long insertUser(RemoteUserBo user) throws ServiceException;

    /**
     * 获取门店管理员
     *
     * @param branchDeptIds 分支部门ID
     * @date 2024/02/23 04:47:53
     */
    Map<Long,RemoteUserVo> getBranchAdmin(Set<Long> branchDeptIds);

    /**
     * 获取管理员负责的门店的部门id
     *
     * @param username 用户名
     * @param nickname 昵称
     * @date 2024/02/23 04:47:32
     */
    Set<Long> getAdminBranchDeptIds(String username,String nickname);


    List<RemoteUserVo> queryUserList(RemoteUserBo bo,boolean ignoreDataPermission);

    /**
     * 根据查询条件，搜索符合条件的useriDs
     *
     * @param bo bo
     * @date 2024/02/28 03:34:51
     */
    List<Long> queryUserIds(RemoteUserBo bo,boolean ignoreDataPermission);

    /**
     * 按ID选择昵称
     *
     * @param userIds 订单操作由id创建
     * @date 2024/03/04 02:11:08
     */
    Map<Long, String> selectNicknameByIds(List<Long> userIds);

    /**
     * 查询指定角色的用户
     * @param deptId
     * @param roleId
     * @return
     */
    List<RemoteUserVo> queryUserOfRole(Long deptId, Long roleId);

    List<RemoteUserVo> queryUserList(List<String> nickNames,Long deptId);

    /**
     * 获取用户信息
     *
     * @param userId
     * @return
     */
    RemoteUserStudentInfoVo getUserInfoForStudent(Long userId);

    LoginUser getSpecifyAgentUserInfo(Long userId) throws ServiceException;

}
