package com.jxw.shufang.system.api.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Data
public class RemoteDeptBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long deptId;

    private String deptName;

    private Long parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人
     */
    private Long leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门状态:0正常,1停用
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 是否门店（ 0：不是 1：是）
     */
    private Boolean isStore;

    /**
     * 部门id列表
     */
    private List<Long> deptIdList;

    /**
     * 精准的部门名称，不like模糊查询
     */
    private String deptNameExact;

}
