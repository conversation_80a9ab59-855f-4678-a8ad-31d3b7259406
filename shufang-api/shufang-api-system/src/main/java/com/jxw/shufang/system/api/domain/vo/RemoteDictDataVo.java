package com.jxw.shufang.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 字典数据视图对象 sys_dict_data
 */
@Data
public class RemoteDictDataVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    private Long dictCode;

    /**
     * 字典排序
     */
//    @JsonIgnore
    private Integer dictSort;

    /**
     * 字典标签
     */
    private String dictLabel;

    /**
     * 字典键值
     */
    private String dictValue;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 样式属性（其他样式扩展）
     */
    @JsonIgnore
    private String cssClass;

    /**
     * 表格回显样式
     */
    @JsonIgnore
    private String listClass;

    /**
     * 是否默认（Y是 N否）
     */
    @JsonIgnore
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    @JsonIgnore
    private String status;

    /**
     * 备注
     */
    @JsonIgnore
    private String remark;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Date createTime;

}
