package com.jxw.shufang.system.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/5/16
 * @Description: sys_user userInfo
 */
@Data
public class RemoteUserStudentInfoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * sys_user_id
     */
    private Long userId;
    /**
     * 会员姓名
     */
    private String studentName;
    /**
     * 会员id
     */
    private Long studentId;
    /**
     * 性别
     */
    private String studentGender;
    /**
     * 会员账号
     */
    private String studentAccount;
    /**
     * 门店名称
     */
    private String branchName;
    /**
     * 门店id
     */
    private Long branchId;
    /**
     * 年级
     */
    private String grade;

    private StudentInfoVo studentInfo;

    @Data
    @AllArgsConstructor
    public static class StudentInfoVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 是否开通快叮岛权益
         */
        private Boolean hasKuaidingPrivilege;

        /**
         * 快叮岛权益过期时间
         */
        private Date kuaidingPrivilegeExpireTime;
    }

}
