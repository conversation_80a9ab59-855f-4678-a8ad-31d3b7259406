package com.jxw.shufang.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 属性关系视图对象 attribute_relation
 * @date 2024-03-26
 */
@Data
public class RemoteAttributeRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 类型（直接填表名）
     */
    private String type;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 值（|||隔开）
     */
    private String value;

}
