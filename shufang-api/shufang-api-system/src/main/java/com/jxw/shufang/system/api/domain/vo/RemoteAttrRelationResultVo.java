package com.jxw.shufang.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 远程属性关系结果vo
 * @date 2024/04/09 04:51:15
 */
@Data
public class RemoteAttrRelationResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 表主键Id
     */
    private Long typeId;

    /**
     * 属性列表
     */
    private List<RemoteAttributeVo> attributeList;

    /**
     * 已填列表
     */
    private List<RemoteAttributeRelationVo> relationList;
}




