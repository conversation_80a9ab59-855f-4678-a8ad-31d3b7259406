package com.jxw.shufang.system.api;

/**
 * 配置服务
 *
 */
public interface RemoteConfigService {

    /**
     * 获取注册开关
     * @param tenantId 租户id
     * @return true开启，false关闭
     */
    boolean selectRegisterEnabled(String tenantId);

    /**
     * 获取会员过期时间
     *
     * @date 2024/03/08 05:54:49
     */
    Integer selectStudentExpireTime();

    /**
     * 编辑会员过期时间
     *
     * @param expireDays 过期天数
     * @date 2024/03/11 11:03:00
     */
    void editStudentExpireTime(Integer expireDays);


    /**
     * 获取课程详情分隔符
     *
     * @date 2024/03/08 05:54:49
     */
    String selectCourseDetailSeparator();

    /**
     * 获取属性填写值分隔符
     *
     * @date 2024/03/08 05:54:49
     */
    String selectAttrValueSeparator();

    /**
     * 获取短信验证码模板id
     * @return
     */
    String selectSmsCaptchaTemplateId();


    /**
     * 获取同手机号 24小时内发送短信验证码最大次数，默认10次
     */
    Integer select24hSmsCaptchaMaxCount();

    /**
     *  24小时内连续错误多少次以后不可以接着使用的配置次数，默认5次
     */
    Integer select24hSmsCaptchaErrorMaxCount();

    /**
     * 获取短信验证码有效期,单位秒，默认300秒
     */
    Long selectSmsCaptchaExpireTime();

    /**
     * 打印记录过期时间，单位分钟,默认30分钟
     *
     * @date 2024/05/08 12:17:45
     */
    Integer selectPrintRecordExpireTime();

    /**
     * s获取crm在籍到期预警天数
     */
    Integer selectStudentExpireWarningDays();

}
