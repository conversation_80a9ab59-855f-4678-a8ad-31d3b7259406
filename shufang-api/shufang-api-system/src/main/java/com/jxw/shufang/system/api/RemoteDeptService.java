package com.jxw.shufang.system.api;

import cn.hutool.core.lang.tree.Tree;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;

import java.util.List;

/**
 * 部门服务
 *
 */
public interface RemoteDeptService {

    /**
     * 通过部门ID查询部门名称
     *
     * @param deptIds 部门ID串逗号分隔
     * @return 部门名称串逗号分隔
     */
    String selectDeptNameByIds(String deptIds);

    /**
     * 通过部门id查询本身部门及子部门中的门店部门
     */
    List<RemoteDeptVo> getSelfAndChildShopList(Long deptId);

    boolean checkDeptNameUnique(Long deptId, String deptName, Long parentId);

    /**
     * 检查部门id对应的部门是否是门店
     *
     * @param deptId
     * @return
     */
    boolean deptIsShop(Long deptId);

    /**
     * 检查 列表的部门是否全部都是门店
     *
     * @param deptIdList
     * @return
     */
    boolean deptListIsAllShop(List<Long> deptIdList);

    List<RemoteDeptVo> getDeptChildrenList(Long deptId);

    /**
     * 添加部门，返回部门id
     *
     * @param bo bo
     * @date 2024/02/22 12:54:42
     */
    Long insertDept(RemoteDeptBo bo) throws ServiceException;


    Long updateDept(RemoteDeptBo bo) throws ServiceException;

    /**
     * 获取部门列表(不包含自己的子部门)
     *
     * @param bo bo
     * @date 2024/03/19 03:20:52
     */
    List<RemoteDeptVo> getDeptList(RemoteDeptBo bo);

    List<RemoteDeptVo> getDeptListIgnore(RemoteDeptBo bo);

    List<Long> getAllParentDeptId(Long deptId);

    RemoteDeptVo getDeptSystemConfig(Long deptId);

    RemoteDeptVo selectDeptInfoById(Long createDept);

    List<RemoteDeptVo> getDeptListByPayMerchantConfigIds(List<Long> payMerchantConfigIds);
    List<RemoteDeptVo> getDeptListByPayMerchantConfigId(Long payMerchantConfigId);

    /**
     * 通过部门Id获取门店Id
     *
     * @param deptId 部门Id
     * @return 门店信息
     */
    RemoteDeptAccountVo selectDeptByIdWithoutCache(Long deptId);

    List<RemoteDeptVo> getDeptListWithoutPermission(RemoteDeptBo bo);

    Boolean transfer(Long deptId, Integer amount) throws ServiceException;

    RemoteDeptAccountVo selectDeptById(Long deptId);

    /**
     * 查询多个代理商的集合
     */
    List<RemoteDeptAccountVo> selectDeptByIds(List<Long> deptIds);

    /**
     * 通过代理商ID，查询直属上一级代理商信息
     */
    RemoteDeptVo getPreDeptByDeptId(Long deptId);

    List<Tree<Long>> selectDeptTreeList(RemoteDeptBo bo);

}
