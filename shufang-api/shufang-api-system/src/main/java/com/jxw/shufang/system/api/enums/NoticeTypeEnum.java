package com.jxw.shufang.system.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NoticeTypeEnum {

    INTERNAL(1, "站内信"),
    ;

    private final Integer code;
    private final String desc;

    public static NoticeTypeEnum getEnum(Integer code) {
        for (NoticeTypeEnum noticeTypeEnum : NoticeTypeEnum.values()) {
            if (noticeTypeEnum.getCode().equals(code)) {
                return noticeTypeEnum;
            }
        }
        return null;
    }

}
