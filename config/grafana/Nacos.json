{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Nacos grafana dashboard", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 13221, "graphTooltip": 0, "id": 9, "links": [{"icon": "external link", "tags": [], "targetBlank": true, "title": "Monitor Guide", "type": "link", "url": "https://nacos.io/zh-cn/docs/monitor-guide.html"}], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 80, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "nacos monitor", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 1}, "id": 89, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "count(nacos_monitor{name=\"configCount\"})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "UP", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 3, "y": 1}, "id": 90, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='serviceCount'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "service count", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 6, "y": 1}, "id": 93, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='ipCount'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "ip count", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 9, "y": 1}, "id": 92, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='configCount', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "config count", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 12, "y": 1}, "id": 91, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='longPolling'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "long polling", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 15, "y": 1}, "id": 88, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='getConfig', instance=~'$instance'}) by (name)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "config push total", "type": "stat"}, {"datasource": {"type": "prometheus"}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 82, "links": [], "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<center>\n  <a href=\"https://nacos.io\">\n    <img src=\"https://s1.ax1x.com/2020/10/22/BiuZRO.png\" style=\"height: 50px; margin-top:20px\" />\n  </a>\n</center>", "mode": "html"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "type": "text"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 50}, {"color": "#d44a3a", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 0, "y": 4}, "id": 33, "interval": "", "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(system_cpu_usage{instance=~'$instance'}) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "cpu", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 70, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 50}, {"color": "#d44a3a", "value": 70}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 9, "y": 4}, "id": 32, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{area=\"heap\", instance=~'$instance'})/sum(jvm_memory_max_bytes{area=\"heap\", instance=~'$instance'}) * 100", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "memory", "type": "gauge"}, {"dashboardFilter": "", "datasource": {"type": "prometheus"}, "gridPos": {"h": 16, "w": 6, "x": 18, "y": 4}, "id": 48, "limit": 10, "links": [], "nameFilter": "", "onlyAlertsOnDashboard": false, "options": {"alertInstanceLabelFilter": "", "alertName": "", "dashboardAlerts": false, "groupBy": [], "groupMode": "default", "maxItems": 20, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": false, "pending": true}, "viewMode": "list"}, "show": "current", "sortOrder": 1, "stateFilter": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "alert list", "type": "alertlist"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1500, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 800}, {"color": "#d44a3a", "value": 1500}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 0, "y": 8}, "id": 29, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(jvm_threads_daemon_threads{instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "threads", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 20, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 5}, {"color": "#d44a3a", "value": 10}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 9, "y": 8}, "id": 30, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(system_load_average_1m{instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "load", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 5000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3000}, {"color": "#d44a3a", "value": 5000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 0, "y": 12}, "id": 61, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_timer_seconds_sum{instance=~'$instance'}[1m]))/sum(rate(nacos_timer_seconds_count{instance=~'$instance'}[1m])) * 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "notify rt", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 5000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 3000}, {"color": "#d44a3a", "value": 5000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 9, "y": 12}, "id": 26, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_sum{instance=~'$instance'}[1m]))/sum(rate(http_server_requests_seconds_count{instance=~'$instance'}[1m])) * 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "rt", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 2000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1000}, {"color": "#d44a3a", "value": 2000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 0, "y": 16}, "id": 25, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_count{instance=~'$instance'}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "qps", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 5000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1000}, {"color": "#d44a3a", "value": 5000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 9, "x": 9, "y": 16}, "id": 70, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='avgPushCost', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "avgPushCost", "type": "gauge"}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 78, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "nacos detail", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 21}, "hiddenSeries": false, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_sum{uri=~'/v1/cs/configs|/nacos/v1/ns', instance=~'$instance'}[1m])/rate(http_server_requests_seconds_count{uri=~'/v1/cs/configs|/nacos/v1/ns/instance|/nacos/v1/ns/health', instance=~'$instance'}[1m])) by (method,uri) * 1000", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_sum{instance=~'$instance'}[1m]))/sum(rate(http_server_requests_seconds_count{instance=~'$instance'}[1m])) * 1000", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "all", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "rt", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 21}, "hiddenSeries": false, "id": 41, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "group", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='longPolling', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "long polling", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(system_load_average_1m{instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "load 1m", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 26}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_count{uri=~'/v1/cs/configs|/nacos/v1/ns/instance|/nacos/v1/ns/health', instance=~'$instance'}[1m])) by (method,uri)", "format": "time_series", "intervalFactor": 1, "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_count[1m]))", "format": "time_series", "intervalFactor": 1, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "qps", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 26}, "hiddenSeries": false, "id": 52, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='leaderStatus', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "leader<PERSON><PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 26}, "hiddenSeries": false, "id": 50, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='avgPushCost', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "avgPushCost", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 31}, "hiddenSeries": false, "id": 53, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='maxPushCost', instance=~'$instance'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "maxPushCost", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 31}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='publish', instance=~'$instance'}) by (name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "publish config", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='getConfig', instance=~'$instance'}) by (name)", "format": "time_series", "intervalFactor": 1, "legendFormat": "get config", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "config statistics", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 31}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_monitor{name=~'.*HealthCheck', instance=~'$instance'}[1m])) by (name) * 60", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "health check", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 74, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "nacos alert", "type": "row"}, {"alert": {"conditions": [{"evaluator": {"params": [50], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "for": "1m", "frequency": "1m", "handler": 1, "name": "cpu alert", "noDataState": "ok", "notifications": [{"id": 1}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 37}, "hiddenSeries": false, "id": 45, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(system_cpu_usage) * 100", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 50}], "timeRegions": [], "title": "cpu alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [15], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "load 1m alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 37}, "hiddenSeries": false, "id": 86, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(system_load_average_1m)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 15}], "timeRegions": [], "title": "load  alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [60], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "memory alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 37}, "hiddenSeries": false, "id": 46, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{area=\"heap\"})/sum(jvm_memory_max_bytes{area=\"heap\"}) * 100", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 60}], "timeRegions": [], "title": "memory alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [500], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "threads alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 42}, "hiddenSeries": false, "id": 39, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(jvm_threads_daemon_threads)", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 500}], "timeRegions": [], "title": "threads alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "for": "1m", "frequency": "1m", "handler": 1, "message": "too many full gc", "name": "gc alert", "noDataState": "ok", "notifications": [{"id": 1}]}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 42}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(rate(jvm_gc_pause_seconds_count{action=\"end of major GC\"}[5m])) * 300", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 5}], "timeRegions": [], "title": "gc alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [10], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "notify task alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 42}, "hiddenSeries": false, "id": 49, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='notifyTask'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 10}], "timeRegions": [], "title": "notify task alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [5000], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["B", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "rt alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 47}, "hiddenSeries": false, "id": 85, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(http_server_requests_seconds_sum[1m]))/sum(rate(http_server_requests_seconds_count[1m])) * 1000", "format": "time_series", "hide": false, "intervalFactor": 1, "refId": "B"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 5000}], "timeRegions": [], "title": "rt alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [5000], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "long polling alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 47}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "max(nacos_monitor{name='longPolling'})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 5000}], "timeRegions": [], "title": "long polling alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "config unhealth exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 47}, "hiddenSeries": false, "id": 56, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='unhealth'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "config unhealth exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "db exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 52}, "hiddenSeries": false, "id": 54, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='db'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "db exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "failedPush alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 52}, "hiddenSeries": false, "id": 51, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(nacos_monitor{name='failedPush'})", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "failed push alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "illegalArgument exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 52}, "hiddenSeries": false, "id": 59, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='illegalArgument'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "illegalArgument exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "naming disk exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 57}, "hiddenSeries": false, "id": 57, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='disk'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "naming disk exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "config notify exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 57}, "hiddenSeries": false, "id": 55, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='configNotify'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "config notify exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "naming leader send beat failed exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 57}, "hiddenSeries": false, "id": 58, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='leaderSendBeatFailed'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "naming leader send beat failed exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"alert": {"conditions": [{"evaluator": {"params": [1], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "keep_state", "frequency": "60s", "handler": 1, "name": "nacos_exception alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 62}, "hiddenSeries": false, "id": 60, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.2.1", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(rate(nacos_exception_total{name='nacos'}[1m])) * 60", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1}], "timeRegions": [], "title": "nacos exception alert", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "", "current": {"selected": false, "text": "127.0.0.1:8848", "value": "127.0.0.1:8848"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{job=\"Nacos\"},instance)", "hide": 0, "includeAll": false, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{job=\"Nacos\"},instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Nacos", "uid": "Bz_QALEiz1", "version": 7, "weekStart": ""}