{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Dashboard for Spring Boot2.1 Statistics(based on Spring Boot2 Statistic by micrometer-prometheus).", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 10280, "graphTooltip": 0, "id": 13, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 54, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "Basic Statistics", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "id": 52, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_uptime_seconds{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 6, "y": 1}, "id": 58, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 14400}], "title": "Heap Used", "type": "gauge"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"from": -1e+32, "result": {"text": "N/A"}, "to": 0}, "type": "range"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 11, "y": 1}, "id": 60, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "title": "Non-Heap Used", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "hiddenSeries": false, "id": 66, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_files_open_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Open Files", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "process_files_max_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max Files", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Process Open Files", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "locale", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 4}, "id": 56, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "process_start_time_seconds{application=\"$application\", instance=\"$instance\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Start time", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "system_cpu_usage{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "System CPU Usage", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "process_cpu_usage{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Process CPU Usage", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 96, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "system_load_average_1m{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Load Average [1m]", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "system_cpu_count{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "CPU Core Size", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Load Average", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 48, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "JVM Statistics - Memory", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 15}, "hiddenSeries": false, "id": 85, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "memory_pool_heap", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used", "refId": "C"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_memory_committed_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Commited", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_memory_max_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "$memory_pool_heap (heap)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 23}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "memory_pool_nonheap", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used", "refId": "C"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_memory_committed_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Commited", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_memory_max_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "$memory_pool_nonheap (non-heap)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 31}, "hiddenSeries": false, "id": 80, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(jvm_classes_unloaded_classes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Classes Unloaded", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Classes Unloaded", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 50, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_classes_loaded_classes{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Classes Loaded", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Classes Loaded", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "locale", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 39}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_buffer_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used Bytes", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_buffer_total_capacity_bytes{instance=\"$instance\", application=\"$application\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Capacity Bytes", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 46}, "hiddenSeries": false, "id": 78, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(jvm_gc_memory_allocated_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "allocated", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "irate(jvm_gc_memory_promoted_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "promoted", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Memory Allocate/Promote", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 47}, "hiddenSeries": false, "id": 82, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_buffer_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"direct\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used Bytes", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_buffer_total_capacity_bytes{instance=\"$instance\", application=\"$application\", id=\"direct\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Capacity Bytes", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 54}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "jvm_threads_daemon_threads{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Daemon", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_threads_live_threads{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Live", "refId": "B"}, {"datasource": {"type": "prometheus"}, "expr": "jvm_threads_peak_threads{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Peak", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 62}, "id": 72, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "JVM Statistics - GC", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 63}, "hiddenSeries": false, "id": 74, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(jvm_gc_pause_seconds_count{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{action}} [{{cause}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "GC Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "locale", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 63}, "hiddenSeries": false, "id": 76, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(jvm_gc_pause_seconds_sum{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{action}} [{{cause}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "GC Stop the World Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 73}, "id": 34, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "HikariCP Statistics", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 74}, "id": 44, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connections Size", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 20, "x": 4, "y": 74}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_active{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Active", "refId": "B"}, {"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_idle{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Idle", "refId": "A"}, {"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_pending{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pending", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 78}, "id": 46, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_timeout_total{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connection Timeout Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 82}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_creation_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_creation_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Creation Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Creation Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 82}, "hiddenSeries": false, "id": 42, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_usage_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_usage_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Usage Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Usage Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 82}, "hiddenSeries": false, "id": 40, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "hikaricp_connections_acquire_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_acquire_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Acquire Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Acquire Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 88}, "id": 18, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "HTTP Statistics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 89}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(http_server_requests_seconds_count{instance=\"$instance\", application=\"$application\", uri!~\".*actuator.*\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Request Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 96}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "irate(http_server_requests_seconds_sum{instance=\"$instance\", application=\"$application\", exception=\"None\", uri!~\".*actuator.*\"}[5m]) / irate(http_server_requests_seconds_count{instance=\"$instance\", application=\"$application\", exception=\"None\", uri!~\".*actuator.*\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Response Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 103}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "Undertow Statistics", "type": "row"}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 104}, "id": 28, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "undertow_connectors_requests_error_count{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "B"}], "title": "Total Error Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 4, "y": 104}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "undertow_sessions_active_current_sessions{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "active sessions", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Active Sessions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 11, "x": 13, "y": 104}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "irate(undertow_connectors_bytes_sent_bytes{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "irate(undertow_connectors_bytes_received_bytes{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Recieved Bytes", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "C"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "Sent & Recieved Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 0, "y": 108}, "id": 32, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "undertow_xwork_worker_pool_max_size{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "B"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "C"}], "title": "<PERSON><PERSON><PERSON> Config <PERSON>", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 13, "x": 0, "y": 111}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "undertow_xwork_worker_pool_size{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Current thread", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "undertow_xwork_worker_thread_busy_count{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Current thread busy", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "hide": false, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": {"type": "prometheus"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 118}, "id": 8, "panels": [], "targets": [{"datasource": {"type": "prometheus"}, "refId": "A"}], "title": "Logback Statistics", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 119}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus"}, "expr": "irate(logback_events_total{instance=\"$instance\", application=\"$application\", level=\"info\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "info", "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "INFO logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 119}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus"}, "expr": "irate(logback_events_total{instance=\"$instance\", application=\"$application\", level=\"error\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "error", "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "ERROR logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 126}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus"}, "expr": "irate(logback_events_total{instance=\"$instance\", application=\"$application\", level=\"warn\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "warn", "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WARN logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 126}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus"}, "expr": "irate(logback_events_total{instance=\"$instance\", application=\"$application\", level=\"debug\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "debug", "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "DEBUG logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 126}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus"}, "expr": "irate(logback_events_total{instance=\"$instance\", application=\"$application\", level=\"trace\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "trace", "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "TRACE logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "5s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": "shufang-auth", "value": "shufang-auth"}, "datasource": {"type": "prometheus"}, "definition": "label_values(application)", "hide": 0, "includeAll": false, "label": "Application", "multi": false, "name": "application", "options": [], "query": {"query": "label_values(application)", "refId": "Prometheus-application-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "**************:9211", "value": "**************:9211"}, "datasource": {"type": "prometheus"}, "definition": "label_values(jvm_classes_loaded_classes{application=\"$application\"}, instance)", "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(jvm_classes_loaded_classes{application=\"$application\"}, instance)", "refId": "Prometheus-instance-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": {"type": "prometheus"}, "definition": "", "hide": 0, "includeAll": false, "label": "HikariCP-Pool", "multi": false, "name": "hikaricp", "options": [], "query": {"query": "label_values(hikaricp_connections{instance=\"$instance\", application=\"$application\"}, pool)", "refId": "Prometheus-hikaricp-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "hide": 0, "includeAll": true, "label": "Memory Pool (heap)", "multi": false, "name": "memory_pool_heap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"},id)", "refId": "Prometheus-memory_pool_heap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus"}, "definition": "", "hide": 0, "includeAll": true, "label": "Memory Pool (nonheap)", "multi": false, "name": "memory_pool_nonheap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"},id)", "refId": "Prometheus-memory_pool_nonheap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Spring Boot 2.1 Statistics", "uid": "spring_boot_21", "version": 3, "weekStart": ""}