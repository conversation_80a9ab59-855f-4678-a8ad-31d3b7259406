forest:
  max-connections: 1000        # 连接池最大连接数
  connect-timeout: 3000        # 连接超时时间，单位为毫秒
  read-timeout: 3000           # 数据读取超时时间，单位为毫秒
  backend: okhttp3 # 配置后端HTTP API为 okhttp3
  max-request-queue-size: 1000  # [自v1.5.22版本起可用] 最大请求等待队列大小
  max-async-thread-size: 500   # [自v1.5.21版本起可用] 最大异步线程数
  max-async-queue-size: 2000     # [自v1.5.22版本起可用] 最大异步线程池队列大小
  max-retry-count: 3           # 请求失败后重试次数（默认为 0 次不重试）
  max-retry-interval: 10 # 为最大重试时间间隔, 单位为毫秒，默认为 0 毫秒
  ssl-protocol: TLS            # 单向验证的HTTPS的默认TLS协议（默认为 TLS）
  log-enabled: true            # 打开或关闭日志（默认为 true）
  log-request: true            # 打开/关闭Forest请求日志（默认为 true）
  log-response-status: true    # 打开/关闭Forest响应状态日志（默认为 true）
  log-response-content: true   # 打开/关闭Forest响应内容日志（默认为 false）
  async-mode: platform         # [自v1.5.27版本起可用] 异步模式（默认为 platform）
  interceptors:                   # 可配置1到多个拦截器
     - com.jxw.shufang.extresource.interceptor.HeaderInterceptor
  #  全局变量可以在任何模板表达式中进行数据绑定。


ext-resource:
    baseUrl: https://gw.test.xuexizhiwang.com
    appId: xwsfxt
    appSecret: jbwwoCsY33umWhY3nDF
    tokenHeaderName: token
    smsUrl: https://gw.test.xuexizhiwang.com
    smsAppId: xuewangstudyroom
    smsAppSecret: C9KkdDEbOsNQI3Ic
