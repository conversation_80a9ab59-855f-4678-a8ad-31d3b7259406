# 安全配置
security:
  # 防止XSS攻击
  xss:
    enabled: true
    excludeUrls:
      - /system/notice
      - /staff/management/followUpRecord
  # 不校验白名单
  ignore:
    whites:
      - /auth/code
      - /auth/logout
      - /auth/login
      - /auth/preLogin
      - /auth/binding/*
      - /auth/social/callback
      - /auth/register
      - /auth/tenant/list
      - /resource/sms/code
      - /*/v3/api-docs
      - /*/error
      - /csrf
      - /wxmp/wxmp/event/handle
      - /wxmp/wxmp/initJSSDKConfig
      - /student/android/student/sendSmsCode
      - /student/android/student/forgetPwd
      - /wxmp/wxUser/forward
      - /wxmp/wxUser/getWeChatInfo
      - /student/officialAccount/parentRecord/*
      - /student/officialAccount/studyPlanning/*
      - /system/client/timestamp
      - /system/dict/data/type/*
      - /system/version/latestVerInfo
      - /student/officialAccount/feedbackRecord/queryById
      - /student/management/iclock/*

spring:
  cloud:
    # 网关配置
    gateway:
      # 打印请求日志(自定义)
      requestLog: true
      discovery:
        locator:
          lowerCaseServiceId: true
          enabled: true
      routes:
        # 认证中心
        - id: shufang-auth
          uri: lb://shufang-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=1
        # 代码生成
        - id: shufang-gen
          uri: lb://shufang-gen
          predicates:
            - Path=/tool/**
          filters:
            - StripPrefix=1
        # 系统模块
        - id: shufang-system
          uri: lb://shufang-system
          predicates:
            - Path=/system/**,/monitor/**
          filters:
            - StripPrefix=1
        # 资源服务
        - id: shufang-resource
          uri: lb://shufang-resource
          predicates:
            - Path=/resource/**
          filters:
            - StripPrefix=1
        # 异步任务服务
        - id: shufang-async-task
          uri: lb://shufang-async-task
          predicates:
            - Path=/asyncTask/**
          filters:
            - StripPrefix=1
        #微信公众号模块
        - id: shufang-wxmp
          uri: lb://shufang-wxmp
          predicates:
            - Path=/wxmp/**
          filters:
            - StripPrefix=1

        # 门店服务
        - id: shufang-branch
          uri: lb://shufang-branch
          predicates:
            - Path=/branch/**
          filters:
            - StripPrefix=1

        # 订单服务
        - id: shufang-order
          uri: lb://shufang-order
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=1

        # 员工服务
        - id: shufang-staff
          uri: lb://shufang-staff
          predicates:
            - Path=/staff/**
          filters:
            - StripPrefix=1

        # 会员服务
        - id: shufang-student
          uri: lb://shufang-student
          predicates:
            - Path=/student/**
          filters:
            - StripPrefix=1

          # 外部资源
        - id: shufang-ext-resource
          uri: lb://shufang-ext-resource
          predicates:
            - Path=/ext-resource/**
          filters:
            - StripPrefix=1
    # sentinel 配置
    sentinel:
      filter:
        enabled: false
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${spring.cloud.nacos.server-addr}
            dataId: sentinel-${spring.application.name}.json
            groupId: ${spring.cloud.nacos.config.group}
            namespace: ${spring.profiles.active}
            data-type: json
            rule-type: gw-flow
