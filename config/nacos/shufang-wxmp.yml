#spring:
#  datasource:
#    dynamic:
#      # 设置默认的数据源或者数据源组,默认值即为 master
#      primary: master
#      datasource:
#        # 主库数据源
#        master:
#          type: ${spring.datasource.type}
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: ${datasource.system-master.url}
#          username: ${datasource.system-master.username}
#          password: ${datasource.system-master.password}
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: ${datasource.system-oracle.url}
#          username: ${datasource.system-oracle.username}
#          password: ${datasource.system-oracle.password}
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ${datasource.system-postgres.url}
#          username: ${datasource.system-postgres.username}
#          password: ${datasource.system-postgres.password}
# 公众号配置(必填)
wx:
  mp:
    appId: wx02feb7ab29813917
    appSecret: 428f8fa345dfvsdfg68fd17a682
    token: vsdf5tgdg2452345
    aesKey: anCL3lX4256456354634563Vm6JPs
    domainName: https://xwsf-test.xuexizhiwang.com/prod-api
    # 反馈页面地址
    feedbackPageUrl: https://xwsf-test.xuexizhiwang.com/officialaccount/pages/study/feedback
    # 反馈订阅模板id
    feedbackTemplateId: pfaM4lt6_NSk8lNInFrl2EornK-o9KT037mow0KogVE
    # 反馈模板场景值
    feedbackScene: 1000

    # 考勤订阅页面地址
    attendancePageUrl: https://xwsf-test.xuexizhiwang.com/officialaccount/pages/study/attendance
    # 考勤订阅模板id
    attendanceTemplateId: pfaM4lt6_NSk8lNInFrl2EornK-o9KT037mow0KogVE
    # 考勤模板场景值
    attendanceScene: 1001


  # 存储配置redis(可选)
  config-storage:
    type: Jedis                     # 配置类型: Memory(默认), Jedis, RedisTemplate
    key-prefix: wxmp                 # 相关redis前缀配置: wx(默认)
    redis.host: 127.0.0.1
    redis.port: 6379
#单机和sentinel同时存在时，优先使用sentinel配置
#wx.mp.config-storage.redis.sentinel-ips=127.0.0.1:16379,127.0.0.1:26379
#wx.mp.config-storage.redis.sentinel-name=mymaster
# http客户端配置
    http-client-type: httpclient      # http客户端类型: HttpClient(默认), OkHttp, JoddHttp
    http-proxy-host:
    http-proxy-port:
    http-proxy-username:
    http-proxy-password:
# 公众号地址host配置
#wx.mp.hosts.api-host=http://proxy.com/
#wx.mp.hosts.open-host=http://proxy.com/
#wx.mp.hosts.mp-host=http://proxy.com/
