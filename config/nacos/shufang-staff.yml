spring:
  datasource:
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      datasource:
        # 主库数据源
        master:
          type: ${spring.datasource.type}
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ${datasource.system-master.url}
          username: ${datasource.system-master.username}
          password: ${datasource.system-master.password}
#        oracle:
#          type: ${spring.datasource.type}
#          driverClassName: oracle.jdbc.OracleDriver
#          url: ${datasource.system-oracle.url}
#          username: ${datasource.system-oracle.username}
#          password: ${datasource.system-oracle.password}
#        postgres:
#          type: ${spring.datasource.type}
#          driverClassName: org.postgresql.Driver
#          url: ${datasource.system-postgres.url}
#          username: ${datasource.system-postgres.username}
#          password: ${datasource.system-postgres.password}
# powerjob 配置
powerjob:
  worker:
    # 如何开启调度中心请查看文档教程
    enabled: true
    # 需要先在 powerjob 登录页执行应用注册后才能使用
    app-name: shufang-staff
    allow-lazy-connect-server: false
    max-appended-wf-context-length: 4096
    max-result-length: 4096
    # 端口 随着主应用端口飘逸 避免集群冲突
    port: 2${server.port}
    protocol: http
    # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
    # server-address: 127.0.0.1:7700
    # 调度中心应用名 通过服务名连接调度中心(启用 server-name 会导致 server-address 不生效)
    server-name: shufang-powerjob-server
    store-strategy: disk
