<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.CourseMapper">

    <select id="getChindInfoList" resultType="com.jxw.shufang.student.domain.vo.CourseChildInfoVo">
        SELECT
            t.course_id,
            t.course_parent_id,
            REPLACE(
                GROUP_CONCAT(
                    IFNULL(CONCAT(sd1.course_id, ','), ''),
                    IFNULL(CONCAT(sd2.course_id, ','), ''),
                    IFNULL(CONCAT(sd3.course_id, ','), ''),
                    IFNULL(CONCAT(sd4.course_id, ','), ''),
                    IFNULL(CONCAT(sd5.course_id, ','), ''),
                    IFNULL(CONCAT(sd6.course_id, ','), ''),
                    IFNULL(CONCAT(sd7.course_id, ','), ''),
                    IFNULL(CONCAT(sd8.course_id, ','), ''),
                    IFNULL(CONCAT(sd9.course_id, ','), ''),
                    IFNULL(CONCAT(sd10.course_id, ','), ''),

                    IFNULL(CONCAT(sd11.course_id, ','), ''),
                    IFNULL(CONCAT(sd12.course_id, ','), ''),
                    IFNULL(CONCAT(sd13.course_id, ','), ''),
                    IFNULL(CONCAT(sd14.course_id, ','), ''),
                    IFNULL(CONCAT(sd15.course_id, ','), ''),
                    IFNULL(CONCAT(sd16.course_id, ','), ''),
                    IFNULL(CONCAT(sd17.course_id, ','), ''),
                    IFNULL(CONCAT(sd18.course_id, ','), ''),
                    IFNULL(CONCAT(sd19.course_id, ','), ''),
                    IFNULL(CONCAT(sd20.course_id, ','), ''),

                    IFNULL(CONCAT(sd21.course_id, ','), ''),
                    IFNULL(CONCAT(sd22.course_id, ','), ''),
                    IFNULL(CONCAT(sd23.course_id, ','), ''),
                    IFNULL(CONCAT(sd24.course_id, ','), ''),
                    IFNULL(CONCAT(sd25.course_id, ','), ''),
                    IFNULL(CONCAT(sd26.course_id, ','), ''),
                    IFNULL(CONCAT(sd27.course_id, ','), ''),
                    IFNULL(CONCAT(sd28.course_id, ','), ''),
                    IFNULL(CONCAT(sd29.course_id, ','), ''),
                    IFNULL(CONCAT(sd30.course_id, ','), ''),

                    IFNULL(CONCAT(sd31.course_id, ','), ''),
                    IFNULL(CONCAT(sd32.course_id, ','), ''),
                    IFNULL(CONCAT(sd33.course_id, ','), ''),
                    IFNULL(CONCAT(sd34.course_id, ','), ''),
                    IFNULL(CONCAT(sd35.course_id, ','), ''),
                    IFNULL(CONCAT(sd36.course_id, ','), ''),
                    IFNULL(CONCAT(sd37.course_id, ','), ''),
                    IFNULL(CONCAT(sd38.course_id, ','), ''),
                    IFNULL(CONCAT(sd39.course_id, ','), ''),
                    IFNULL(CONCAT(sd40.course_id, ','), ''),

                    IFNULL(CONCAT(sd41.course_id, ','), ''),
                    IFNULL(CONCAT(sd42.course_id, ','), ''),
                    IFNULL(CONCAT(sd43.course_id, ','), ''),
                    IFNULL(CONCAT(sd44.course_id, ','), ''),
                    IFNULL(CONCAT(sd45.course_id, ','), ''),
                    IFNULL(CONCAT(sd46.course_id, ','), ''),
                    IFNULL(CONCAT(sd47.course_id, ','), ''),
                    IFNULL(CONCAT(sd48.course_id, ','), ''),
                    IFNULL(CONCAT(sd49.course_id, ','), ''),
                    IFNULL(CONCAT(sd50.course_id, ','), '')
                )
                ,',,',',') child_ids,
            REPLACE(
                GROUP_CONCAT(
                    IF(sd1.knowledge_id is null,'',IFNULL(CONCAT(sd1.course_id, ','), '')),
                    IF(sd2.knowledge_id is null,'',IFNULL(CONCAT(sd2.course_id, ','), '')),
                    IF(sd3.knowledge_id is null,'',IFNULL(CONCAT(sd3.course_id, ','), '')),
                    IF(sd4.knowledge_id is null,'',IFNULL(CONCAT(sd4.course_id, ','), '')),
                    IF(sd5.knowledge_id is null,'',IFNULL(CONCAT(sd5.course_id, ','), '')),
                    IF(sd6.knowledge_id is null,'',IFNULL(CONCAT(sd6.course_id, ','), '')),
                    IF(sd7.knowledge_id is null,'',IFNULL(CONCAT(sd7.course_id, ','), '')),
                    IF(sd8.knowledge_id is null,'',IFNULL(CONCAT(sd8.course_id, ','), '')),
                    IF(sd9.knowledge_id is null,'',IFNULL(CONCAT(sd9.course_id, ','), '')),
                    IF(sd10.knowledge_id is null,'',IFNULL(CONCAT(sd10.course_id, ','), '')),
                    IF(sd11.knowledge_id is null,'',IFNULL(CONCAT(sd11.course_id, ','), '')),
                    IF(sd12.knowledge_id is null,'',IFNULL(CONCAT(sd12.course_id, ','), '')),
                    IF(sd13.knowledge_id is null,'',IFNULL(CONCAT(sd13.course_id, ','), '')),
                    IF(sd14.knowledge_id is null,'',IFNULL(CONCAT(sd14.course_id, ','), '')),
                    IF(sd15.knowledge_id is null,'',IFNULL(CONCAT(sd15.course_id, ','), '')),
                    IF(sd16.knowledge_id is null,'',IFNULL(CONCAT(sd16.course_id, ','), '')),
                    IF(sd17.knowledge_id is null,'',IFNULL(CONCAT(sd17.course_id, ','), '')),
                    IF(sd18.knowledge_id is null,'',IFNULL(CONCAT(sd18.course_id, ','), '')),
                    IF(sd19.knowledge_id is null,'',IFNULL(CONCAT(sd19.course_id, ','), '')),
                    IF(sd20.knowledge_id is null,'',IFNULL(CONCAT(sd20.course_id, ','), '')),
                    IF(sd21.knowledge_id is null,'',IFNULL(CONCAT(sd21.course_id, ','), '')),
                    IF(sd22.knowledge_id is null,'',IFNULL(CONCAT(sd22.course_id, ','), '')),
                    IF(sd23.knowledge_id is null,'',IFNULL(CONCAT(sd23.course_id, ','), '')),
                    IF(sd24.knowledge_id is null,'',IFNULL(CONCAT(sd24.course_id, ','), '')),
                    IF(sd25.knowledge_id is null,'',IFNULL(CONCAT(sd25.course_id, ','), '')),
                    IF(sd26.knowledge_id is null,'',IFNULL(CONCAT(sd26.course_id, ','), '')),
                    IF(sd27.knowledge_id is null,'',IFNULL(CONCAT(sd27.course_id, ','), '')),
                    IF(sd28.knowledge_id is null,'',IFNULL(CONCAT(sd28.course_id, ','), '')),
                    IF(sd29.knowledge_id is null,'',IFNULL(CONCAT(sd29.course_id, ','), '')),
                    IF(sd30.knowledge_id is null,'',IFNULL(CONCAT(sd30.course_id, ','), '')),
                    IF(sd31.knowledge_id is null,'',IFNULL(CONCAT(sd31.course_id, ','), '')),
                    IF(sd32.knowledge_id is null,'',IFNULL(CONCAT(sd32.course_id, ','), '')),
                    IF(sd33.knowledge_id is null,'',IFNULL(CONCAT(sd33.course_id, ','), '')),
                    IF(sd34.knowledge_id is null,'',IFNULL(CONCAT(sd34.course_id, ','), '')),
                    IF(sd35.knowledge_id is null,'',IFNULL(CONCAT(sd35.course_id, ','), '')),
                    IF(sd36.knowledge_id is null,'',IFNULL(CONCAT(sd36.course_id, ','), '')),
                    IF(sd37.knowledge_id is null,'',IFNULL(CONCAT(sd37.course_id, ','), '')),
                    IF(sd38.knowledge_id is null,'',IFNULL(CONCAT(sd38.course_id, ','), '')),
                    IF(sd39.knowledge_id is null,'',IFNULL(CONCAT(sd39.course_id, ','), '')),
                    IF(sd40.knowledge_id is null,'',IFNULL(CONCAT(sd40.course_id, ','), '')),
                    IF(sd41.knowledge_id is null,'',IFNULL(CONCAT(sd41.course_id, ','), '')),
                    IF(sd42.knowledge_id is null,'',IFNULL(CONCAT(sd42.course_id, ','), '')),
                    IF(sd43.knowledge_id is null,'',IFNULL(CONCAT(sd43.course_id, ','), '')),
                    IF(sd44.knowledge_id is null,'',IFNULL(CONCAT(sd44.course_id, ','), '')),
                    IF(sd45.knowledge_id is null,'',IFNULL(CONCAT(sd45.course_id, ','), '')),
                    IF(sd46.knowledge_id is null,'',IFNULL(CONCAT(sd46.course_id, ','), '')),
                    IF(sd47.knowledge_id is null,'',IFNULL(CONCAT(sd47.course_id, ','), '')),
                    IF(sd48.knowledge_id is null,'',IFNULL(CONCAT(sd48.course_id, ','), '')),
                    IF(sd49.knowledge_id is null,'',IFNULL(CONCAT(sd49.course_id, ','), '')),
                    IF(sd50.knowledge_id is null,'',IFNULL(CONCAT(sd50.course_id, ','), ''))
                )
                ,',,',',') has_knowledge_id_course_ids
        FROM course t
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd1 ON sd1.course_parent_id = t.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd2 ON sd2.course_parent_id = sd1.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd3 ON sd3.course_parent_id = sd2.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd4 ON sd4.course_parent_id = sd3.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd5 ON sd5.course_parent_id = sd4.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd6 ON sd6.course_parent_id = sd5.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd7 ON sd7.course_parent_id = sd6.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd8 ON sd8.course_parent_id = sd7.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd9 ON sd9.course_parent_id = sd8.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd10 ON sd10.course_parent_id = sd9.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd11 ON sd11.course_parent_id = sd10.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd12 ON sd12.course_parent_id = sd11.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd13 ON sd13.course_parent_id = sd12.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd14 ON sd14.course_parent_id = sd13.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd15 ON sd15.course_parent_id = sd14.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd16 ON sd16.course_parent_id = sd15.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd17 ON sd17.course_parent_id = sd16.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd18 ON sd18.course_parent_id = sd17.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd19 ON sd19.course_parent_id = sd18.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd20 ON sd20.course_parent_id = sd19.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd21 ON sd21.course_parent_id = sd20.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd22 ON sd22.course_parent_id = sd21.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd23 ON sd23.course_parent_id = sd22.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd24 ON sd24.course_parent_id = sd23.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd25 ON sd25.course_parent_id = sd24.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd26 ON sd26.course_parent_id = sd25.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd27 ON sd27.course_parent_id = sd26.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd28 ON sd28.course_parent_id = sd27.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd29 ON sd29.course_parent_id = sd28.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd30 ON sd30.course_parent_id = sd29.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd31 ON sd31.course_parent_id = sd30.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd32 ON sd32.course_parent_id = sd31.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd33 ON sd33.course_parent_id = sd32.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd34 ON sd34.course_parent_id = sd33.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd35 ON sd35.course_parent_id = sd34.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd36 ON sd36.course_parent_id = sd35.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd37 ON sd37.course_parent_id = sd36.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd38 ON sd38.course_parent_id = sd37.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd39 ON sd39.course_parent_id = sd38.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd40 ON sd40.course_parent_id = sd39.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd41 ON sd41.course_parent_id = sd40.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd42 ON sd42.course_parent_id = sd41.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd43 ON sd43.course_parent_id = sd42.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd44 ON sd44.course_parent_id = sd43.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd45 ON sd45.course_parent_id = sd44.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd46 ON sd46.course_parent_id = sd45.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd47 ON sd47.course_parent_id = sd46.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd48 ON sd48.course_parent_id = sd47.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd49 ON sd49.course_parent_id = sd48.course_id
                 LEFT JOIN (SELECT course_id,course_parent_id,knowledge_id FROM course WHERE del_flag = 0) sd50 ON sd50.course_parent_id = sd49.course_id
            ${ew.getCustomSqlSegment}
        GROUP BY t.course_id
    </select>
    <select id="getQueryList" resultType="com.jxw.shufang.student.domain.Course">
        select t.course_id,
               t.course_parent_id,
               t.knowledge_id
        from course t
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectCourseMaxSort" resultType="java.lang.Integer">
        SELECT MAX(sort) AS max_sort_order
        FROM course
        WHERE course_parent_id = #{courseId}
          and del_flag != 2
    </select>
    <select id="listGradeCourse" resultType="com.jxw.shufang.student.domain.Course">
        SELECT t.course_id,
               t.course_name,
               t.special_topic,
               t.quarter_type
        FROM course t
                 LEFT JOIN course_grade cg ON t.course_id = cg.course_id
            ${ew.getCustomSqlSegment}
        GROUP BY
            t.course_id
    </select>

    <select id="findCourseAncestors" resultType="com.jxw.shufang.student.domain.vo.CourseVo">
        select c.* from course c
            where find_in_set(#{courseId}, c.ancestors)
                and c.del_flag = 0
        order by c.sort
    </select>

    <select id="findCourseChildren" resultType="com.jxw.shufang.student.domain.vo.CourseVo">
        select c.* from course c
            where c.del_flag = 0
                and c.top_course_id in
            <foreach collection="courseIds" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

    <select id="getCourseInfoWithParentNameByCourseId" resultType="com.jxw.shufang.student.domain.vo.CourseVo">
        select c1.course_id, c1.course_name,c1.course_parent_id,c2.affiliation_subject, c2.course_name as course_parent_name from course c1 left join course c2 on c1.course_parent_id =
        c2.course_id
        where c1.course_id in
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </select>

    <select id="queryGradeCourse" resultType="com.jxw.shufang.student.domain.vo.CourseVo">
        select c.course_id, c.course_name,c.affiliation_subject,c.quarter_type,c.course_type,
               c.sort,c.special_topic,c.course_parent_id,c.course_source,c.course_introduction,
               c.course_no,c.course_thumbnail,c.course_parent_id,c.course_type,c.sort,c.special_topic,
               c.course_source,c.course_introduction,c.course_no,c.course_thumbnail,c.course_parent_id,cg.grade
        from course c
                 right join course_grade cg on c.course_id = cg.course_id
            ${ew.getCustomSqlSegment}
    </select>

    <update id="batchClearCourse">
        update course set knowledge_id = null
        where course_id in
        <foreach collection="courseIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

</mapper>
