<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AllowOwnCorrectionMapper">

    <resultMap id="allowOwnCorrectionResult" type="com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo">
        <id property="allowOwnCorrectionId" column="allow_own_correction_id"/>
        <result property="studentId" column="student_id"/>
        <association property="student" resultMap="studentResult"/>
    </resultMap>

    <resultMap id="studentResult" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="nameWithPhone" column="name_with_phone"/>
    </resultMap>

    <select id="selectAllowOwnCorrectionVoPage" resultMap="allowOwnCorrectionResult">
        select t.*,
               s.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone
        from allow_own_correction t
                 left join student s on s.student_id = t.student_id
        ${ew.customSqlSegment}
    </select>
</mapper>
