<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudentMapper">
    <resultMap id="StudentResult" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="nameWithPhone" column="name_with_phone"/>
        <result property="studentParentRecordId" column="student_parent_record_id"/>
        <result property="hasSupplementOrder" column="has_supplement_order"></result>
        <association property="studentInfo" resultMap="StudentInfoResult"/>
        <association property="studentParentRecord" column="student_parent_record_id"
                     resultMap="StudentParentRecordResult"/>
    </resultMap>

    <resultMap id="StudentInfoResult" type="com.jxw.shufang.student.domain.vo.StudentInfoVo">
    </resultMap>

    <resultMap id="StudentParentRecordResult" type="com.jxw.shufang.student.domain.vo.StudentParentRecordVo">
    </resultMap>

    <select id="queryOptionList" resultMap="StudentResult">
        select t.student_id,
               t.student_name,
               t.student_account,
               t.expire_time,
               t.branch_id,
               t.student_parent_record_id,
               concat(t.student_name, '(', right(t.student_account, 4), ')') as name_with_phone
        from student t
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectStudentList" resultMap="StudentResult">
        select t.student_id,
               t.branch_id,
               t.student_account,
               t.student_name,
               t.student_sex,
               t.student_grade,
               t.student_source,
               t.student_parent_phone,
               t.student_backup_phone,
               t.student_consultant_record_id,
               t.student_parent_record_id,
               t.student_ai_course_record_id,
               t.student_remark,
               t.last_login_time,
               t.create_by,
               t.create_time,
               t.create_dept,
               t.expire_time,
               concat(t.student_name, '(', right(t.student_account, 4), ')') as name_with_phone,
               t.purchased_card_flag
        from student t
            ${ew.getCustomSqlSegment}
    </select>


    <select id="queryStudentIdList" resultType="java.lang.Long">
        select t.student_id
        from student t
            ${ew.getCustomSqlSegment}
    </select>


    <select id="selectStudentPage" resultMap="StudentResult">
        select t.student_id,
               t.branch_id,
               t.student_account,
               t.student_name,
               t.student_sex,
               t.student_grade,
               t.student_source,
               t.student_parent_phone,
        t.student_backup_phone,
        t.student_consultant_record_id,
        t.student_parent_record_id,
        t.student_ai_course_record_id,
        t.student_remark,
        t.last_login_time,
        t.create_by,
        t.create_time,
        t.create_dept,
        t.expire_time,
        t.purchased_card_flag,
        concat(t.student_name, '(', right(t.student_account, 4), ')') as name_with_phone,
        si.student_info_id,
        si.province,
        si.city,
        si.county,
        si.attending_school,
        si.school_class,
        si.school_major,
        si.school_stay_type,
        si.student_address,
        scr.student_consultant_record_id,
               spr.student_parent_record_id,
               spr.parent_name,
               spr.parent_wechat_nickname,
               spr.parent_wechat_no,
               spr.parent_wechat_img,
               spr.parent_wechat_open_id,
               spr.parent_wechat_union_id,
               spr.is_follow,
               o_operate.has_supplement_order
        from student t
                LEFT JOIN student_info si on t.student_id = si.student_id
                LEFT JOIN student_consultant_record scr
                       on t.student_consultant_record_id = scr.student_consultant_record_id
                LEFT JOIN student_parent_record spr on t.student_parent_record_id = spr.student_parent_record_id
                <if test="bo.studentTypeId != null">
                JOIN
                    (select smc.student_id
                        from student_membership_card smc join student_type st on smc.student_type_id = st.student_type_id
                            and smc.card_status = 1
                            and smc.product_begin_date &lt;= current_timestamp
                            and smc.product_end_date >= current_timestamp
                            and st.student_type_id = ${bo.studentTypeId}
                        group by smc.student_id) st on t.student_id = st.student_id
                </if>
                LEFT JOIN (SELECT o.student_id,
                                MAX(CASE WHEN op.order_operate_status = 6 THEN 1 ELSE 0 END) AS has_supplement_order
                           FROM `order` o
                           LEFT JOIN order_operate op ON o.order_id = op.order_id
                            where op.create_time = (
                                        SELECT MAX(create_time)
                                        FROM order_operate
                                        WHERE order_id = o.order_id
                                    )
                GROUP BY o.student_id) o_operate ON t.student_id = o_operate.student_id
            ${ew.getCustomSqlSegment}
    </select>


    <select id="selectStudentById" resultMap="StudentResult">
        select t.student_id,
               t.branch_id,
               t.student_account,
               t.student_name,
               t.student_sex,
               t.student_grade,
               t.student_source,
               t.student_parent_phone,
               t.student_backup_phone,
               t.student_consultant_record_id,
               t.student_parent_record_id,
               t.student_ai_course_record_id,
               t.student_remark,
               t.last_login_time,
               t.create_by,
               t.create_time,
               t.create_dept,
               t.expire_time,
               t.preferential_amount,
               t.purchased_card_flag,
               concat(t.student_name, '(', right(t.student_account, 4), ')') as name_with_phone,
               si.student_info_id,
               si.province,
               si.city,
               si.county,
               si.attending_school,
               si.school_class,
               si.school_major,
               si.school_stay_type,
               si.student_address,
               scr.student_consultant_record_id,
               spr.student_parent_record_id,
               spr.parent_name,
               spr.parent_wechat_nickname,
               spr.parent_wechat_no,
               spr.parent_wechat_img,
               spr.parent_wechat_open_id,
               spr.parent_wechat_union_id,
               spr.is_follow
        from student t
                 LEFT JOIN student_info si on t.student_id = si.student_id
                 LEFT JOIN student_consultant_record scr
                           on t.student_consultant_record_id = scr.student_consultant_record_id
                 LEFT JOIN student_parent_record spr on t.student_parent_record_id = spr.student_parent_record_id
        where t.student_id = #{studentId}
    </select>


    <select id="queryStudentNameMap" resultType="java.util.Map">
        SELECT student_id, student_name
        FROM student
        WHERE
        student_id IN
        <foreach collection="studentIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="batchSelectStudentIdListByBranchId" resultType="java.lang.Long">
        select student_id
        from student
        where branch_id in
        <foreach collection="branchIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
