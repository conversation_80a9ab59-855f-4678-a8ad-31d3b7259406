<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AttendanceDailyActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.student.domain.AttendanceDailyActivity">
        <id column="attendance_daily_activity_id" property="attendanceDailyActivityId" />
        <result column="student_id" property="studentId" />
        <result column="record_date" property="recordDate" />
        <result column="event_time" property="eventTime" />
        <result column="event_time_source" property="eventTimeSource" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        attendance_daily_activity_id, student_id, record_date, event_time, event_time_source, create_by, create_time, update_time
    </sql>
    <select id="selectNotFeedbackStudentList"
            resultType="com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo">
        select a.*,
               s.student_name,s.student_account,
               adafr.*,
               fr.feedback_content,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone
        from attendance_daily_activity a
                 left join student s on s.student_id = a.student_id
                 left join branch b on b.branch_id = s.branch_id
                 left join attendance_daily_activity_feedback_record adafr on adafr.attendance_daily_activity_id = a.attendance_daily_activity_id
            left join feedback_record fr on fr.feedback_record_id = adafr.feedback_record_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectListByFeedbackStatus" resultType="com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo">
        select ada.*,
               adafr.feedback_status,
               adafr.feedback_record_id,
               adafr.attendance_daily_activity_feedback_record_id
        from attendance_daily_activity ada
                 left join attendance_daily_activity_feedback_record adafr
                           on adafr.attendance_daily_activity_id = ada.attendance_daily_activity_id
        where adafr.feedback_status = #{feedbackStatus}
    </select>

</mapper>
