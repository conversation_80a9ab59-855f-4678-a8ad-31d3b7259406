<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningPendingMapper">

    <resultMap type="com.jxw.shufang.student.domain.StudyPlanningPending" id="StudyPlanningPendingResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="modeType"    column="mode_type"    />
        <result property="planStartDate"    column="plan_start_date"    />
        <result property="planEndDate"    column="plan_end_date"    />
        <result property="feedbackStatus"    column="feedback_status"    />
        <result property="planningStatus"    column="planning_status"    />
        <result property="createTime"    column="created_at"    />
        <result property="updateTime"    column="updated_at"    />
    </resultMap>

    <sql id="selectStudyPlanningPendingVo">
        select id, student_id, branch_id, consultant_id, mode_type, plan_start_date, plan_end_date, feedback_status, planning_status, created_at, updated_at from study_planning_pending
    </sql>

    <resultMap type="com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo" id="StudyPlanningPendingVoWithJoinResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="modeType"    column="mode_type"    />
        <result property="planStartDate"    column="plan_start_date"    />
        <result property="planEndDate"    column="plan_end_date"    />
        <result property="feedbackStatus"    column="feedback_status"    />
        <result property="planningStatus"    column="planning_status"    />
        <result property="expectedPlanningTime"    column="expected_planning_time"    />
        <result property="expectedFeedbackTime"    column="expected_feedback_time"    />
        <result property="actualPlanningTime"    column="actual_planning_time"    />
        <result property="actualFeedbackTime"    column="actual_feedback_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="studentName"    column="student_name"    />
        <result property="consultantName"    column="consultant_name"    />
        <result property="loginDate"    column="login_date"    />
    </resultMap>

    <sql id="selectStudyPlanningPendingVoWithJoinBase">
        SELECT
        spp.id,
        spp.student_id,
        spp.mode_type,
        spp.plan_start_date,
        spp.plan_end_date,
        spp.feedback_status,
        spp.planning_status,
        spp.expected_planning_time,
        spp.expected_feedback_time,
        spp.actual_planning_time,
        spp.actual_feedback_time,
        spp.create_time,
        spp.update_time,
        s.student_name,
        su.nick_name AS consultant_name,
        su_student.login_date AS login_date
        FROM study_planning_pending spp
        LEFT JOIN student s ON spp.student_id = s.student_id
        LEFT JOIN sys_user su_student ON s.create_by = su_student.user_id
        INNER JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
        INNER JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
        INNER JOIN sys_user su ON bs.create_by = su.user_id
    </sql>

    <select id="selectVoPageWithJoin" resultMap="StudyPlanningPendingVoWithJoinResult">
        <include refid="selectStudyPlanningPendingVoWithJoinBase"/>
        ${ew.customSqlSegment}
    </select>

    <select id="selectVoListWithJoin" resultMap="StudyPlanningPendingVoWithJoinResult">
        <include refid="selectStudyPlanningPendingVoWithJoinBase"/>
        ${ew.customSqlSegment}
    </select>

    <resultMap type="com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo" id="StudyPlanningPendingVoWithTimeoutResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="modeType"    column="mode_type"    />
        <result property="planStartDate"    column="plan_start_date"    />
        <result property="planEndDate"    column="plan_end_date"    />
        <result property="feedbackStatus"    column="feedback_status"    />
        <result property="planningStatus"    column="planning_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="studentName"    column="student_name"    />
        <result property="consultantName"    column="consultant_name"    />
        <result property="loginDate"    column="login_date"    />
    </resultMap>



</mapper>
