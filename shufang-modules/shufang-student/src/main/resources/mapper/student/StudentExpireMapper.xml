<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudentExpireMapper">

    <resultMap id="studentExpireResult" type="com.jxw.shufang.student.domain.vo.StudentExpireVo">
        <id column="student_expire_id" property="studentExpireId"/>
        <result column="student_id" property="studentId"/>
        <association property="student" column="student_id" resultMap="studentResult"/>
    </resultMap>

    <resultMap id="studentResult" type="com.jxw.shufang.student.domain.vo.StudentVo">
    </resultMap>

    <select id="selectPageList" resultMap="studentExpireResult">
        select t.*,
               s.student_name,
               s.branch_id,
               s.expire_time
        from student_expire t
                 LEFT JOIN student s ON t.student_id = s.student_id
        ${ew.customSqlSegment}
    </select>


</mapper>
