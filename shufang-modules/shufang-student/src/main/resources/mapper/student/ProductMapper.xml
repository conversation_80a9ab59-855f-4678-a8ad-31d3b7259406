<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.ProductMapper">
    <resultMap id="productResult" type="com.jxw.shufang.student.domain.vo.ProductVo">
        <id column="product_id" property="productId" />
        <!--要加上这一行，不然下面的会把id吞掉-->
        <result property="studentTypeId" column="student_type_id"/>
        <association property="studentType" column="student_type_id" resultMap="studentTypeResult"/>
    </resultMap>

    <resultMap id="studentTypeResult" type="com.jxw.shufang.student.domain.vo.StudentTypeVo">
    </resultMap>

    <select id="queryOptionList" resultMap="productResult">
        select t.*
        from product t
            join student_type st
                on t.student_type_id = st.student_type_id
            ${ew.getCustomSqlSegment}
    </select>


    <select id="selectProductPageList"  resultMap="productResult">
        select t.*,
               st.student_type_name
        from product t
        LEFT JOIN student_type st on t.student_type_id = st.student_type_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectProductById" resultMap="productResult">
        select *,
               st.student_type_name
        from product t
        LEFT JOIN student_type st on t.student_type_id = st.student_type_id
        where t.product_id = #{productId}
    </select>
</mapper>
