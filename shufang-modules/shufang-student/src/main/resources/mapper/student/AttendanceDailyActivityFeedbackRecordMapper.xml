<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AttendanceDailyActivityFeedbackRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord">
        <id column="attendance_daily_activity_feedback_record_id" property="attendanceDailyActivityFeedbackRecordId" />
        <result column="attendance_daily_activity_id" property="attendanceDailyActivityId" />
        <result column="feedback_status" property="feedbackStatus" />
        <result column="publish_status" property="publishStatus" />
        <result column="feedback_record_id" property="feedbackRecordId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        attendance_daily_activity_feedback_record_id, feedback_record_id, attendance_daily_activity_id, feedback_status, publish_status, create_by, create_time, update_time
    </sql>

</mapper>
