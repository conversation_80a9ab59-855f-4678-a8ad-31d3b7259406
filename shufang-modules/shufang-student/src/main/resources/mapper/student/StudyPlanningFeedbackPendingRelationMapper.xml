<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningFeedbackPendingRelationMapper">

    <resultMap type="com.jxw.shufang.student.domain.StudyPlanningFeedbackPendingRelation" id="FeedbackReportPendingRelationResult">
        <result property="id"    column="id"    />
        <result property="reportId"    column="report_id"    />
        <result property="pendingId"    column="pending_id"    />
        <result property="creatorId"    column="creator_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updaterId"    column="updater_id"    />
        <result property="updatedTime"    column="updated_time"    />
    </resultMap>

    <sql id="selectFeedbackReportPendingRelationVo">
        select id, report_id, pending_id, creator_id, created_at, updater_id, updated_time from study_planning_feedback_pending_relation
    </sql>

</mapper>
