<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.FeedbackTemplateMapper">
    <resultMap id="FeedbackTemplateResult" type="com.jxw.shufang.student.domain.vo.FeedbackTemplateVo">

    </resultMap>

    <select id="selectFeedbackTemplatePage" resultMap="FeedbackTemplateResult">
        select t.feedback_template_id,
               t.feedback_type,
               t.feedback_template_content,
               t.feedback_template_source,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               t.template_use_count
        from feedback_template t
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
