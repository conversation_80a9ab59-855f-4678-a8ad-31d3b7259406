<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningRecordMapper">

    <resultMap id="studyPlanningRecordResult" type="com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo">
        <id property="studyPlanningRecordId" column="study_planning_record_id"/>
        <result property="courseId" column="course_id"/>
        <result property="studyPlanningId" column="study_planning_id"/>
        <association property="studyPlanning" resultMap="studyPlanningResult" />
        <association property="course" resultMap="courseResult" column="course_id"/>
    </resultMap>

    <resultMap id="studyPlanningResult" type="com.jxw.shufang.student.domain.vo.StudyPlanningVo">
        <id property="studyPlanningId" column="study_planning_id"/>
    </resultMap>

    <resultMap id="courseResult" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id"/>
    </resultMap>

    <resultMap id="studyPlanningRecordResultForDashboard"
               type="com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo">
        <id property="studyPlanningRecordId" column="study_planning_record_id"/>
        <result property="courseId" column="course_id"/>
        <result property="studyPlanningId" column="study_planning_id"/>
        <association property="studyPlanning" resultMap="studyPlanningResultForDashboard" columnPrefix="sp_"/>
        <association property="course" resultMap="courseResultForDashboard" columnPrefix="c_"/>
    </resultMap>

    <resultMap id="studyPlanningResultForDashboard" type="com.jxw.shufang.student.domain.vo.StudyPlanningVo">
        <id property="studyPlanningId" column="sp_study_planning_id"/>
    </resultMap>

    <resultMap id="courseResultForDashboard" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="c_course_id"/>
    </resultMap>

    <sql id="sql_study_planning_record">
        select t.*,
               sp.study_planning_id,
               sp.student_id,
               sp.study_planning_date,
               sp.study_planning_status,
               sp.create_dept,
               sp.create_by,
               sp.create_time,
               sp.update_by,
               sp.update_time,
               c.course_id,
               c.ancestors,
               c.stage,
               c.grade,
               c.affiliation_subject,
               c.special_topic,
               c.course_name,
               c.knowledge_id,
               c.course_introduction,
               c.course_no,
               c.course_thumbnail,
               c.course_parent_id,
               c.course_type,
               c.course_source,
               c.create_dept,
               c.create_by,
               c.create_time,
               c.update_by,
               c.update_time,
               c.del_flag,
               c.region
    </sql>

    <sql id="sql_study_planning_record_for_dashboard">
        SELECT t.*,
               sp.study_planning_id     AS sp_study_planning_id,
               sp.student_id            AS sp_student_id,
               sp.study_planning_date   AS sp_study_planning_date,
               sp.study_planning_status AS sp_study_planning_status,
               sp.create_dept           AS sp_create_dept,
               sp.create_by             AS sp_create_by,
               sp.create_time           AS sp_create_time,
               sp.update_by             AS sp_update_by,
               sp.update_time           AS sp_update_time,
               c.course_id              AS c_course_id,
               c.ancestors              AS c_ancestors,
               c.stage                  AS c_stage,
               c.grade                  AS c_grade,
               c.affiliation_subject    AS c_affiliation_subject,
               c.special_topic          AS c_special_topic,
               c.course_name            AS c_course_name,
               c.knowledge_id           AS c_knowledge_id,
               c.course_introduction    AS c_course_introduction,
               c.course_no              AS c_course_no,
               c.course_thumbnail       AS c_course_thumbnail,
               c.course_parent_id       AS c_course_parent_id,
               c.course_type            AS c_course_type,
               c.course_source          AS c_course_source,
               c.create_dept            AS c_create_dept,
               c.create_by              AS c_create_by,
               c.create_time            AS c_create_time,
               c.update_by              AS c_update_by,
               c.update_time            AS c_update_time,
               c.del_flag,
               c.region
    </sql>


    <select id="queryStudyPlanningRecordPage" resultMap="studyPlanningRecordResult">
        <include refid="sql_study_planning_record"></include>
        from study_planning_record t
        left join study_planning sp on t.study_planning_id = sp.study_planning_id
        left join course c on c.course_id = t.course_id
        ${ew.customSqlSegment}
    </select>

    <select id="queryStudyPlanningRecordList" resultMap="studyPlanningRecordResult">
        select t.*,
               sp.study_planning_id,
               sp.student_id,
               sp.study_planning_date,
               sp.study_planning_status,
               sp.create_dept,
               sp.create_by,
               sp.create_time,
               sp.update_by,
               sp.update_time,
               c.course_id,
               c.ancestors,
               c.stage,
               c.grade,
               c.affiliation_subject,
               c.special_topic,
               c.course_name,
               c.knowledge_id,
               c.course_introduction,
               c.course_no,
               c.course_thumbnail,
               c.course_parent_id,
               c.course_type,
               c.course_source,
               c.create_dept,
               c.create_by,
               c.create_time,
               c.update_by,
               c.update_time,
               c.del_flag,
               c.region
        from study_planning_record t
                 left join study_planning sp on t.study_planning_id = sp.study_planning_id
                 left join course c on c.course_id = t.course_id
            ${ew.customSqlSegment}
    </select>

    <resultMap id="studyPlanRecordGroupMap" type="com.jxw.shufang.student.domain.vo.StudyPlanRecordGroupVo">
        <id property="date" column="study_planning_date"/>
        <collection property="studyPlanningRecordIdList" ofType="Long" column="study_planning_record_id"/>
    </resultMap>

    <resultMap id="studyPlanRecordForDashboardMap"
               type="com.jxw.shufang.student.domain.vo.StudyPlanningRecordDashboardVo">
        <id property="studentId" column="student_id"/>

        <collection property="studyPlanningRecordList"
                    resultMap="studyPlanningRecordResultForDashboard"/>
    </resultMap>

    <sql id="queryStudyPlanDateGroupData">
        SELECT sp.study_planning_date 'date'
        FROM study_planning_record t
                 LEFT JOIN study_planning sp ON t.study_planning_id = sp.study_planning_id
                 LEFT JOIN course c ON c.course_id = t.course_id
            ${ew.customSqlSegment}
        GROUP BY
            sp.study_planning_date
        ORDER BY sp.study_planning_date DESC
    </sql>

    <select id="groupByStudyPlanDate" resultType="com.jxw.shufang.student.domain.vo.StudyPlanRecordGroupVo">
        <include refid="queryStudyPlanDateGroupData"></include>
    </select>

    <select id="queryPlanningRecordStudentIdPage" resultType="com.jxw.shufang.student.domain.dto.PlanningRecordStudentIdDTO">
        select
            planning.student_id,
            planning.create_time
        from study_planning_record record
        left join  study_planning  planning  on planning.study_planning_id = record.study_planning_id
        left join student student on student.student_id = record.student_id
        left join student_consultant_record  consultant_record on consultant_record.student_consultant_record_id = student.student_consultant_record_id
        <where>
            and  planning.study_planning_date = #{queryPlanningRecordDTO.studyPlanningDate}
            <if test="queryPlanningRecordDTO.consultantId != null ">
            and    consultant_record.student_consultant_id = #{queryPlanningRecordDTO.consultantId}
            </if>
            <if test="queryPlanningRecordDTO.studentName != null and  queryPlanningRecordDTO.studentName !='' ">
             and   student.student_name like concat('%',#{queryPlanningRecordDTO.studentName},'%')
            </if>
            <if test="queryPlanningRecordDTO.studentIdList != null  and queryPlanningRecordDTO.studentIdList.size() > 0 ">
            and    planning.student_id in
                <foreach collection="queryPlanningRecordDTO.studentIdList" item="studentId" open="(" separator="," close=")">
                        #{studentId}
                </foreach>
            </if>
            and record.study_record_status = 0
            and planning.study_planning_status = 2
        </where>
        group by planning.student_id
        order by planning.create_time desc
    </select>

    <select id="queryStudyPlanningWithStudentInfo" resultType="com.jxw.shufang.student.domain.dto.StudentWithPlanningRecordDTO">
        select record.study_planning_record_id,
               record.study_planning_id,
               record.course_id,
               record.study_start_time,
               record.study_end_time,
               record.download_status,
               student.student_id,
               student.student_account,
               student.student_name,
               student.create_time,
               planning.study_planning_date
        from study_planning_record record
        left join study_planning  planning  on planning.study_planning_id = record.study_planning_id
        left join student student on student.student_id = record.student_id
        <where>
            and  planning.study_planning_date = #{queryPlanningRecordDTO.studyPlanningDate}
            <if test="queryPlanningRecordDTO.studentName != null and  queryPlanningRecordDTO.studentName !='' ">
             and   student.student_name like concat('%',#{queryPlanningRecordDTO.studentName},'%')
            </if>
            <if test="queryPlanningRecordDTO.studentIdList != null  and queryPlanningRecordDTO.studentIdList.size() > 0 ">
            and    planning.student_id in
                <foreach collection="queryPlanningRecordDTO.studentIdList" item="studentId" open="(" separator="," close=")">
                        #{studentId}
                </foreach>
            </if>
            and record.study_record_status = 0
            and planning.study_planning_status = 2
        </where>
        order by planning.create_time desc
    </select>
    <select id="getGroupByStudyPlanDateData"
            resultMap="studyPlanRecordGroupMap">
        SELECT t.study_planning_record_id,
               sp.study_planning_id,
               sp.study_planning_date
        FROM study_planning_record t
                 LEFT JOIN study_planning sp ON t.study_planning_id = sp.study_planning_id
                 LEFT JOIN course c ON c.course_id = t.course_id
            ${ew.customSqlSegment}
        order by sp.study_planning_date desc
    </select>
    <select id="queryStudyPlanningRecordPageV3"
            resultMap="studyPlanRecordForDashboardMap">
        <include refid="sql_study_planning_record_for_dashboard"></include>
        from study_planning_record t
        left join study_planning sp on t.study_planning_id = sp.study_planning_id
        left join course c on c.course_id = t.course_id
        ${ew.customSqlSegment}
    </select>
    <select id="queryStudyPlanningStudentIdV3" resultType="java.lang.Long">
        SELECT distinct t.student_id
        from study_planning_record t
                 left join study_planning sp on t.study_planning_id = sp.study_planning_id
                 left join course c on c.course_id = t.course_id
            ${ew.customSqlSegment}
    </select>
</mapper>
