<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.PrintRecordMapper">
    <resultMap id="printRecordVoMap" type="com.jxw.shufang.student.domain.vo.PrintRecordVo">
        <id property="printRecordId" column="print_record_id"/>
        <result property="studentId" column="student_id"/>
        <association property="student" resultMap="studentVoMap" column="student_id"/>
    </resultMap>

    <resultMap id="studentVoMap" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="createDept" column="stu_create_dept"/>
        <result property="createBy" column="stu_create_by"/>
        <result property="createTime" column="stu_create_time"/>
        <result property="updateBy" column="stu_update_by"/>
        <result property="updateTime" column="stu_update_time"/>
    </resultMap>

    <select id="selectPrintRecordVoPage" resultMap="printRecordVoMap">
        select t.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone,
               s.student_id,
               s.branch_id,
               s.student_account,
               s.student_password,
               s.student_name,
               s.student_sex,
               s.student_grade,
               s.student_source,
               s.student_parent_phone,
               s.student_backup_phone,
               s.student_consultant_record_id,
               s.student_parent_record_id,
               s.student_remark,
               s.expire_time,
               s.last_login_time,
               s.create_dept as stu_create_dept,
               s.create_by as stu_create_by,
               s.create_time as stu_create_time,
               s.update_by as stu_update_by,
               s.update_time as stu_update_time,
               s.student_ai_course_record_id
        from print_record t
                 left join student s on s.student_id = t.student_id
        ${ew.customSqlSegment}
    </select>
</mapper>
