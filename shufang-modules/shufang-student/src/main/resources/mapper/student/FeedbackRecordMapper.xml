<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.FeedbackRecordMapper">

    <resultMap id="feedbackRecordResultMap" type="com.jxw.shufang.student.domain.vo.FeedbackRecordVo">
        <id column="feedback_record_id" property="feedbackRecordId" />
        <result column="student_id" property="studentId" />
        <association property="student" resultMap="studentResultMap" column="student_id"/>
    </resultMap>

    <resultMap id="studentResultMap" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id column="student_id" property="studentId" />
        <result property="nameWithPhone" column="name_with_phone"/>
    </resultMap>


    <select id="selectRecordPage" resultMap="feedbackRecordResultMap">
        select t.*,
               s.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone
        from feedback_record t
                 left join  student s on s.student_id = t.student_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectRecordList" resultMap="feedbackRecordResultMap">
        select t.*,
               s.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone
        from feedback_record t
                 left join  student s on s.student_id = t.student_id
            ${ew.customSqlSegment}
    </select>
    <select id="queryLatestFeedbackRecord" resultType="com.jxw.shufang.student.domain.vo.FeedbackRecordVo">
        select t.*
        from feedback_record t
        where t.student_id = #{studentId}
        <if test="feedbackRecordId != null">
            and t.feedback_record_id  &lt; #{feedbackRecordId}
        </if>
        order by t.feedback_record_id desc
        limit 1
    </select>
</mapper>
