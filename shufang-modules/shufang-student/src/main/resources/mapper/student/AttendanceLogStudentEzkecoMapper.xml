<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AttendanceLogStudentEzkecoMapper">

    <resultMap id="attendanceLogStudentEzkecoVo" type="com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo">
        <id property="attendanceLogStudentEzkecoId" column="attendance_log_student_ezkeco_id"/>
        <result property="studentId" column="student_id"/>
        <association property="student" column="student_id" resultMap="studentVo"/>
    </resultMap>

    <resultMap id="studentVo" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <association property="studentInfo" column="student_id" resultMap="studentInfoVo"/>
    </resultMap>

    <resultMap id="studentInfoVo" type="com.jxw.shufang.student.domain.vo.StudentInfoVo">
        <id property="studentInfoId" column="student_info_id"/>
        <result property="studentId" column="student_id"/>
    </resultMap>

    <select id="queryPageList" resultMap="attendanceLogStudentEzkecoVo">
        SELECT t.*,
               s.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone,
               si.*
        from attendance_log_student_ezkeco t
                 left join student s on s.student_id = t.student_id
                 left join student_info si on si.student_id = t.student_id
            ${ew.customSqlSegment}
    </select>
    <select id="getAllAttendanceDate" resultType="java.lang.String">
        select distinct date (checktime)
        from attendance_log_student_ezkeco
        where student_id = #{studentId}
    </select>
    <select id="countCheckDaysByStudentIds" resultType="com.jxw.shufang.student.domain.vo.StudentAttendanceCountVo">
        SELECT
        student_id AS studentId,
        COUNT(DISTINCT DATE_FORMAT(checktime, '%Y-%m-%d')) AS checkDays
        FROM
        attendance_log_student_ezkeco
        WHERE
        student_id IN
        <foreach collection="studentIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND checktime BETWEEN #{startTime} AND #{endTime}
        GROUP BY student_id
    </select>
</mapper>
