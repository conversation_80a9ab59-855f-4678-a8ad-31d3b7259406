<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningPendingRelationMapper">

    <resultMap type="com.jxw.shufang.student.domain.StudyPlanningPendingRelation" id="StudyPlanningPendingRelationResult">
        <result property="id"    column="id"    />
        <result property="pendingId"    column="pending_id"    />
        <result property="planningId"    column="planning_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudyPlanningPendingRelationVo">
        select id, pending_id, planning_id, created_by, create_time, update_time from study_planning_pending_relation
    </sql>

</mapper>
