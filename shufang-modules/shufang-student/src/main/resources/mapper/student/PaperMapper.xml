<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.PaperMapper">

    <resultMap id="paperTypeGroupVoResult" type="com.jxw.shufang.student.domain.vo.PaperTypeGroupVo">
        <result property="paperType" column="paper_type" />
        <result property="paperCount" column="paper_count" />
    </resultMap>

    <select id="selectPaperTypeGroupList"  resultMap="paperTypeGroupVoResult">
        SELECT paper_type, COUNT(*) AS paper_count
        FROM paper
        ${ew.customSqlSegment}
        GROUP BY paper_type
    </select>
</mapper>
