<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudentParentRecordMapper">

    <resultMap id="studentParentRecordMap" type="com.jxw.shufang.student.domain.vo.StudentParentRecordVo">
        <id property="studentParentRecordId" column="student_parent_record_id"/>
        <result property="studentId" column="student_id"/>
        <association resultMap="studentResultMap" property="student" column="student_id"/>
    </resultMap>

    <resultMap id="studentResultMap" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="studentParentRecordId" column="student_parent_record_id"/>
        <result property="nameWithPhone" column="name_with_phone"/>
        <result property="studentName" column="student_name"/>
        <result property="createBy" column="student_create_by"/>
        <result property="createTime" column="student_create_time"/>
        <result property="updateBy" column="student_update_by"/>
        <result property="updateTime" column="student_update_time"/>
    </resultMap>

    <select id="queryBindList" resultMap="studentParentRecordMap">
        select t.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone,
               s.branch_id,
               s.student_account,
               s.student_name,
               s.student_sex,
               s.student_grade,
               s.student_source,
               s.student_parent_phone,
               s.student_backup_phone,
               s.student_consultant_record_id,
               s.student_parent_record_id,
               s.student_remark,
               s.expire_time,
               s.last_login_time,
               s.create_dept as `student_create_dept`,
               s.create_by as `student_create_by`,
               s.create_time as `student_create_time`,
               s.update_by as `student_update_by`,
               s.update_time as `student_update_time`
        from student_parent_record t
                 inner join student s on s.student_parent_record_id = t.student_parent_record_id
        ${ew.customSqlSegment}
    </select>
</mapper>
