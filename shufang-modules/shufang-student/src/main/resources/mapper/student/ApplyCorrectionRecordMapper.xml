<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.ApplyCorrectionRecordMapper">

    <resultMap id="applyCorrectionRecordResult" type="com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo">
        <id property="applyCorrectionRecordId" column="apply_correction_record_id"/>
        <result property="studentId" column="student_id"/>
        <result property="studyPlanningRecordId" column="study_planning_record_id"/>
        <association property="student" resultMap="studentResult" column="student_id"/>
        <association property="course"  resultMap="courseResult" column="course_id"/>
    </resultMap>

    <resultMap id="studentResult" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="nameWithPhone" column="name_with_phone"/>
    </resultMap>

    <resultMap id="courseResult" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id"/>
    </resultMap>

    <select id="selectRecordVoPage" resultMap="applyCorrectionRecordResult">
        select t.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone,
               s.*,
               c.*
        from apply_correction_record t
                 left join student s on s.student_id = t.student_id
                 left join study_planning_record spr on spr.study_planning_record_id = t.study_planning_record_id
                 left join course c on c.course_id = spr.course_id
        ${ew.customSqlSegment}
    </select>
</mapper>
