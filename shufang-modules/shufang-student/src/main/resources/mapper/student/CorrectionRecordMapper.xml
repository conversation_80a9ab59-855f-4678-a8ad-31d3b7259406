<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.CorrectionRecordMapper">

    <resultMap id="CorrectionRecordResult" type="com.jxw.shufang.student.domain.vo.CorrectionRecordVo">
        <id property="correctionRecordId" column="correction_record_id" />
        <result property="rightCount" column="right_count" />
        <result property="wrongCount" column="wrong_count" />
        <result property="rightWrongCount" column="right_wrong_count" />
        <result property="notAnswerCount" column="not_answer_count" />
    </resultMap>

    <select id="queryRecordAndRightWrongInfo" resultMap="CorrectionRecordResult">
        select t.*,
               (select count(*)
                from correction_record_info c
                where c.correction_record_id = t.correction_record_id
                  and answer_result = '1')                                            as right_count,
               (select count(*)
                from correction_record_info c
                where c.correction_record_id = t.correction_record_id
                  and ( answer_result = '2' or LENGTH(IFNULL(answer_result, '')) = 0) ) as wrong_count,
               (select count(*)
                from correction_record_info c
                where c.correction_record_id = t.correction_record_id
                  and answer_result = '3')                                            as right_wrong_count,
               (select count(*)
                from correction_record_info c
                where c.correction_record_id = t.correction_record_id
                  and LENGTH(IFNULL(answer_result, '')) = 0)                          as not_answer_count
        from correction_record t
        ${ew.customSqlSegment}
    </select>
</mapper>
