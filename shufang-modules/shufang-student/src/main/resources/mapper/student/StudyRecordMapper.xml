<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyRecordMapper">

    <select id="selectMyRank" resultType="java.lang.Integer">
        select rank_value
        from (SELECT sr.student_id,
                     RANK() OVER (ORDER BY sum(sr.study_video_total_duration) DESC) AS rank_value
              FROM study_record sr
                       LEFT JOIN study_planning_record spr ON spr.study_planning_record_id = sr.study_planning_record_id
                       LEFT JOIN study_planning sp ON sp.study_planning_id = spr.study_planning_id
              where spr.study_record_status = 0
              <if test="bo.studyPlanningDateStart!=null and bo.studyPlanningDateEnd!=null">
                  and sp.study_planning_date between #{bo.studyPlanningDateStart} and #{bo.studyPlanningDateEnd}
              </if>
              group by student_id) t
        where t.student_id = #{studentId};
    </select>
    <select id="countStudentStudyTime" resultType="java.lang.Long">
        SELECT SUM(study_video_total_duration) FROM study_record
        WHERE student_id = #{studentId}
        <if test="startDate != null and endDate != null">
            AND create_time BETWEEN #{startDate} AND #{endDate}
        </if>
    </select>
</mapper>
