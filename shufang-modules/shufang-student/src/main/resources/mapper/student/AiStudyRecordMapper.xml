<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AiStudyRecordMapper">

    <resultMap id="aiStudyRecordMap" type="com.jxw.shufang.student.domain.vo.AiStudyRecordVo">
        <id property="aiStudyRecordId" column="ai_study_record_id" />
        <result property="studentId" column="student_id" />
        <result property="courseId" column="course_id" />
        <association property="student" resultMap="studentMap" column="student_id"/>
        <association property="course" resultMap="courseMap" column="course_id"/>
    </resultMap>

    <resultMap id="studentMap" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id" />
        <result property="nameWithPhone" column="name_with_phone" />
    </resultMap>

    <resultMap id="courseMap" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id" />
    </resultMap>

    <select id="selectStudyRecordPage" resultMap="aiStudyRecordMap">
        select t.*,
               s.*,
               c.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone
        from ai_study_record t
                 left join student s on s.student_id = t.student_id
                 left join course c on t.course_id = c.course_id
        ${ew.customSqlSegment}
    </select>
</mapper>
