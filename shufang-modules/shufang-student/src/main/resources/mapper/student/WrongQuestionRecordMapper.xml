<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.WrongQuestionRecordMapper">

    <resultMap id="wrongQuestionGroupResult" type="com.jxw.shufang.student.domain.vo.WrongQuestionGroupVo">

    </resultMap>

    <resultMap id="wrongQuestionRecordResult" type="com.jxw.shufang.student.domain.vo.WrongQuestionRecordVo">
        <id property="wrongQuestionRecordId" column="wrong_question_record_id"/>
        <result property="courseId" column="course_id"/>
        <result property="correctionScreenshots" column="correction_screenshots"/>
        <association property="course" column="course_id"  resultMap="courseResult"/>
    </resultMap>

    <resultMap id="wrongQuestionRecordResultV2" type="com.jxw.shufang.student.domain.vo.WrongQuestionRecordV2Vo">
        <id property="wrongQuestionRecordId" column="wrong_question_record_id"/>
        <result property="courseId" column="course_id"/>
        <result property="correctionScreenshots" column="correction_screenshots"/>
        <association property="course" column="course_id"  resultMap="courseResult"/>
    </resultMap>

    <resultMap id="courseResult" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id"/>
    </resultMap>

    <select id="listGroupByDate" resultMap="wrongQuestionGroupResult">
        select date(t.create_time) as `add_date`,
        GROUP_CONCAT(t.wrong_question_record_id) as `wrong_question_record_id_plural`
        from wrong_question_record t
        left join study_planning_record spr on t.study_planning_record_id = spr.study_planning_record_id
        where t.student_id = #{studentId}
        and spr.study_record_status = 0
        <if test="courseIdList!= null and courseIdList.size() > 0">
            and t.course_id in
            <foreach collection="courseIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by date(t.create_time)
    </select>
    <select id="listGroupByDateV2" resultMap="wrongQuestionGroupResult">
        select date(t.create_time) as `add_date`,
        GROUP_CONCAT(t.wrong_question_record_id) as `wrong_question_record_id_plural`
        from wrong_question_record t
        left join study_planning_record spr on t.study_planning_record_id = spr.study_planning_record_id
        where t.student_id = #{studentId}
        and spr.study_record_status = 0
        <if test="courseIdList!= null and courseIdList.size() > 0">
            and t.course_id in
            <foreach collection="courseIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="recordCreateTimeStart != null and recordCreateTimeStart != ''
        and recordCreateTimeEnd != null and recordCreateTimeEnd != ''">
            AND t.create_time BETWEEN #{recordCreateTimeStart} AND #{recordCreateTimeEnd}
        </if>
        group by date(t.create_time)
    </select>

    <select id="selectWrongQuestionRecordPage" resultMap="wrongQuestionRecordResult">
        select t.*,
               c.course_id,
               c.ancestors,
               c.stage,
               c.grade,
               c.affiliation_subject,
               c.special_topic,
               c.course_name,
               c.knowledge_id,
               c.course_introduction,
               c.course_no,
               c.course_thumbnail,
               c.course_parent_id,
               c.course_type,
               c.course_source,
               c.region,
               cr.correction_screenshots
        from wrong_question_record t
                 left join course c on t.course_id = c.course_id
                 left join correction_record cr on cr.study_planning_record_id = t.study_planning_record_id  and cr.correction_type = t.source_type
                 left join study_planning_record spr on t.study_planning_record_id = spr.study_planning_record_id
        where spr.study_record_status = 0
        <if test="ew.isNonEmptyOfWhere()">
            and ${ew.sqlSegment}
        </if>
    </select>


    <select id="selectWrongQuestionRecordPageV2" resultMap="wrongQuestionRecordResultV2">
        select t.*,
        c.course_id,
        c.ancestors,
        c.stage,
        c.grade,
        c.affiliation_subject,
        c.special_topic,
        c.course_name,
        c.knowledge_id,
        c.course_introduction,
        c.course_no,
        c.course_thumbnail,
        c.course_parent_id,
        c.course_type,
        c.course_source,
        c.region,
        cr.correction_screenshots
        from wrong_question_record t
        left join course c on t.course_id = c.course_id
        left join correction_record cr on cr.study_planning_record_id = t.study_planning_record_id  and cr.correction_type = t.source_type
        left join study_planning_record spr on t.study_planning_record_id = spr.study_planning_record_id
        ${ew.customSqlSegment}
    </select>


    <select id="selectRecordPageGroupByStudent" resultMap="wrongQuestionRecordResultV2">
        SELECT
        t_sub.wrong_count,
        t.wrong_question_record_id,
        t.question_id,
        t.question_no,
        t.student_id,
        t.source_type,
        t.create_time,
        t.study_planning_record_id,
        t.course_id,
        t.revise_status,
        t.revise_time,
        t.revise_screenshots,
        c.ancestors,
        c.stage,
        c.grade,
        c.affiliation_subject,
        c.special_topic,
        c.course_name,
        c.knowledge_id,
        c.course_introduction,
        c.course_no,
        c.course_thumbnail,
        c.course_parent_id,
        c.course_type,
        c.course_source,
        c.region,
        cr.correction_screenshots
        FROM (
        SELECT
        MAX(t.wrong_question_record_id)  as wrong_question_record_id,
        count(1) as wrong_count
        FROM wrong_question_record t
        ${ew.customSqlSegment}
        GROUP BY t.question_id,t.student_id
        ) t_sub
        LEFT JOIN wrong_question_record t on t.wrong_question_record_id =t_sub.wrong_question_record_id
        LEFT JOIN course c ON t.course_id = c.course_id
        LEFT JOIN correction_record cr ON cr.study_planning_record_id = t.study_planning_record_id and cr.correction_type = t.source_type
        left join study_planning_record spr on t.study_planning_record_id = spr.study_planning_record_id
        where spr.study_record_status = 0
        ORDER BY t.create_time DESC
    </select>
    <select id="wrongCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM wrong_question_record t
        WHERE t.question_id = #{questionId}
        and t.student_id = #{studentId}
        GROUP BY t.question_id,t.student_id
    </select>


</mapper>
