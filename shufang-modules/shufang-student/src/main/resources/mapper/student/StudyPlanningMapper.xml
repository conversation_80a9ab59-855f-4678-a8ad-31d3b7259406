<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningMapper">
    <resultMap id="studyPlanningResult" type="com.jxw.shufang.student.domain.vo.StudyPlanningVo" >
        <id column="study_planning_id" property="studyPlanningId" />
        <collection property="studyPlanningRecordList" column="studyPlanningId" resultMap="studyPlanningRecordResult"/>
    </resultMap>

    <resultMap id="studyPlanningRecordResult" type="com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo">
        <id column="study_planning_record_id" property="studyPlanningRecordId" />
        <result column="study_planning_id_copy" property="studyPlanningId" />
        <result column="student_id_copy" property="studentId" />
<!--        <result column="record_create_by" property="courseId" />-->
    </resultMap>

    <select id="queryPlanAndRecordList" resultMap="studyPlanningResult">
        select t.study_planning_id,
               t.student_id,
               t.study_planning_date,
               t.study_planning_status,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               spr.study_planning_id as study_planning_id_copy,
               spr.student_id        as student_id_copy,
               spr.study_planning_record_id,
               spr.course_id,
               spr.study_start_time,
               spr.study_end_time,
               spr.study_duration,
               spr.study_status,
               spr.study_record_status,
               spr.create_by         as record_create_by,
               spr.create_time       as record_create_time,
               spr.update_by         as record_update_by,
               spr.update_time       as record_update_time
        from study_planning t
                 left join study_planning_record spr on t.study_planning_id = spr.study_planning_id
            ${ew.getCustomSqlSegment}
    </select>
</mapper>
