<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.AiApplyCorrectionRecordMapper">
    <resultMap id="aiApplyCorrectionRecordResult" type="com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo">
        <id property="aiApplyCorrectionRecordId" column="ai_apply_correction_record_id"/>
        <result property="studentId" column="student_id"/>
        <result property="courseId" column="course_id"/>
        <association property="student" resultMap="studentResult" column="student_id"/>
        <association property="course"  resultMap="courseResult" column="course_id"/>
    </resultMap>

    <resultMap id="studentResult" type="com.jxw.shufang.student.domain.vo.StudentVo">
        <id property="studentId" column="student_id"/>
        <result property="nameWithPhone" column="name_with_phone"/>
    </resultMap>

    <resultMap id="courseResult" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id"/>
    </resultMap>

    <select id="selectRecordVoPage" resultMap="aiApplyCorrectionRecordResult">
        select t.*,
               concat(s.student_name, '(', right(s.student_account, 4), ')') as name_with_phone,
               s.*,
               c.*
        from ai_apply_correction_record t
                 left join student s on s.student_id = t.student_id
                 left join course c on c.course_id = t.course_id
            ${ew.customSqlSegment}
    </select>
</mapper>
