<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.MessageMapper">

    <select id="stuMessageInfoPage" resultType="com.jxw.shufang.student.domain.vo.MessageVo">
        select t.*, (select message_concat from message t1 where t1.message_id = t.last_id) last_message_concat
        from (select count(t.message_id) chat_count,
                max(t.create_time) last_create_time,
                max(t.message_id) last_id,
                sum(t.read_status = 2) has_unread,
                sum(IF(t.read_status = 2, 1, 0)) unread_count,
                t.message_student_id
                from message t
                ${ew.customSqlSegment}
        group by t.message_student_id) t
        where 1=1
        <if test="readStatus!= null and readStatus!= '' and readStatus== '1'.toString()">
            and t.has_unread = 0
        </if>
        <if test="readStatus!= null and readStatus!= '' and readStatus== '2'.toString()">
            and t.has_unread >= 1
        </if>


    </select>

    <select id="selectMessagePage" resultType="com.jxw.shufang.student.domain.vo.MessageVo">
        select t.*
        from message t
        ${ew.customSqlSegment}
    </select>

    <select id="staffShowMessageList" resultType="com.jxw.shufang.student.domain.vo.MessageVo">
        select t.*, (select message_concat from message t1 where t1.message_id = t.last_id) last_message_concat
        from (select count(t.message_id) chat_count,
        max(t.create_time) last_create_time,
        max(t.message_id) last_id,
        sum(t.read_status = 2 and t.send_user_type = 2) has_unread,
        sum(IF(t.read_status = 2 and t.send_user_type = 2, 1, 0)) unread_count,
        t.message_student_id
        from message t
        ${ew.customSqlSegment}
        group by t.message_student_id) t
        where 1=1
        <if test="readStatus!= null and readStatus!= '' and readStatus== '1'.toString()">
            and t.has_unread = 0
        </if>
        <if test="readStatus!= null and readStatus!= '' and readStatus== '2'.toString()">
            and t.has_unread >= 1
        </if>
    </select>
</mapper>
