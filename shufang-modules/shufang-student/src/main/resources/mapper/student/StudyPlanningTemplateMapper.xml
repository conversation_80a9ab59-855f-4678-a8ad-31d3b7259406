<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyPlanningTemplateMapper">

    <select id="selectByTemplateId" resultType="com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo">
        select t.*
        <if test="withTemplateInfo!= null and withTemplateInfo">
            ,
            (select count(distinct course_id) from study_planning_template_info t1 where t1.study_planning_template_id = t.study_planning_template_id) as chapter_count,
            (select count(distinct topmost_course_id) from study_planning_template_info t1 where t1.study_planning_template_id = t.study_planning_template_id) as course_count,
            (select count(distinct c.affiliation_subject) from study_planning_template_info t1 left join course c on c.course_id = t1.topmost_course_id where t.study_planning_template_id = t1.study_planning_template_id) as subject_count
        </if>
        from study_planning_template t
        where study_planning_template_id = #{studyPlanningTemplateId}
    </select>
    <select id="selectTemplatePage" resultType="com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo">
        select t.*,
               (select group_concat(affiliation_subject) from course c where c.course_id in (select sp.topmost_course_id from study_planning_template_info sp where sp.study_planning_template_id = t.study_planning_template_id)) affiliation_subjects
        from study_planning_template t
        ${ew.customSqlSegment}
    </select>
</mapper>
