<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyFeedbackReportMapper">

    <resultMap type="com.jxw.shufang.student.domain.StudyFeedbackReport" id="StudyFeedbackReportResult">
        <result property="id"    column="id"    />
        <result property="studentId"    column="student_id"    />
        <result property="consultantId"    column="consultant_id"    />
        <result property="periodStart"    column="period_start"    />
        <result property="periodEnd"    column="period_end"    />
        <result property="summary"    column="summary"    />
        <result property="issues"    column="issues"    />
        <result property="focusPoints"    column="focus_points"    />
        <result property="status"    column="status"    />
        <result property="feedbackStatus"    column="feedback_status"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectStudyFeedbackReportVo">
        select id, student_id, consultant_id, period_start, period_end, summary, issues, focus_points, status, feedback_status, create_dept, create_by, create_time, update_by, update_time, is_deleted from study_feedback_report
    </sql>

</mapper>
