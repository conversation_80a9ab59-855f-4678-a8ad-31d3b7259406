<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudyVideoRecordMapper">

    <resultMap id="studyVideoRecordResult" type="com.jxw.shufang.student.domain.vo.StudyVideoRecordVo">

    </resultMap>

    <resultMap id="studyVideoRecordFullResult"  type="com.jxw.shufang.student.domain.vo.StudyVideoRecordVo">
        <id  property="studyVideoRecordId" column="study_video_record_id" />
        <association property="course" column="course_id" resultMap="courseResult"/>
    </resultMap>

    <resultMap id="courseResult" type="com.jxw.shufang.student.domain.vo.CourseVo">
        <id property="courseId" column="course_id" />
    </resultMap>

    <select id="queryList" resultMap="studyVideoRecordResult">
        SELECT
            t.study_video_record_id,
            t.student_id,
            t.study_planning_record_id,
            t.video_id,
            t.course_id,
            t.study_video_duration,
            <if test="nonSelectStudyVideoSlicesField==null or !nonSelectStudyVideoSlicesField">
                t.study_video_slices,
            </if>
            t.create_dept,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time
        FROM study_video_record t
        LEFT JOIN study_planning_record spr ON t.study_planning_record_id = spr.study_planning_record_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectStudyVideoRecordPage" resultMap="studyVideoRecordFullResult">
        select t.*,
               c.course_id,
               c.ancestors,
               c.stage,
               c.grade,
               c.affiliation_subject,
               c.special_topic,
               c.course_name,
               c.knowledge_id,
               c.course_introduction,
               c.course_no,
               c.course_thumbnail,
               c.course_parent_id,
               c.course_type,
               c.course_source,
               c.del_flag,
               c.region
        from study_video_record t
                 left join course c on c.course_id = t.course_id
                 left join study_planning_record spr on spr.study_planning_record_id = t.study_planning_record_id
            ${ew.customSqlSegment}
    </select>

    <select id="queryLastStudyVideoRecord" resultMap="studyVideoRecordResult">
        select
        t.study_video_record_id,
        t.student_id,
        t.study_planning_record_id,
        t.video_id,
        t.course_id,
        t.study_video_duration,
        t.duration,
        <if test="showStudyVideoSlices">
            t.study_video_slices,
        </if>
        t.create_dept,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time
        from study_video_record t
        inner join (
            select max(study_video_record_id) study_video_record_id
            from study_video_record svr
            where svr.study_planning_record_id in
                <foreach collection="studyPlanningRecordIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            group by svr.study_planning_record_id
        ) t1 on t1.study_video_record_id = t.study_video_record_id
        where t.study_planning_record_id in
            <foreach collection="studyPlanningRecordIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="queryStudyDaysByStudentId" resultType="java.lang.Long">
        select count(distinct DATE(t.create_time))
        from study_video_record t
                 join study_planning_record spr on spr.study_planning_record_id = t.study_planning_record_id
        where spr.study_record_status = 0 and t.student_id = #{studentId}
    </select>


    <select id="selectRecordOne" resultMap="studyVideoRecordFullResult">
        SELECT t.*
        FROM study_video_record t
        LEFT JOIN study_planning_record spr ON t.study_planning_record_id = spr.study_planning_record_id
            ${ew.customSqlSegment}
    </select>
</mapper>
