<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudentIntroduceRecordMapper">

    <select id="pageStudentIntroduceRecord"
            resultType="com.jxw.shufang.student.domain.vo.StudentIntroduceRecordVo">
        select t.introduce_record_id,
               t.student_name,
               t.introduce_student_name,
               t.student_grade,
               t.branch_name,
               t.branch_auth_type_name,
               t.preferential_amount,
               t.un_frozen_time,
               t.remark,
               t.create_time
        from student_introduce_record t
            ${ew.getCustomSqlSegment}
    </select>
</mapper>
