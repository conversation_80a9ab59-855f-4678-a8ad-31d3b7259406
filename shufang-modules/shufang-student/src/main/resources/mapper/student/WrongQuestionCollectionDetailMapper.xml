<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.WrongQuestionCollectionDetailMapper">

    <resultMap id="WrongQuestionCollectionDetailResult" type="com.jxw.shufang.student.domain.WrongQuestionCollectionDetail">
        <id property="wrongQuestionCollectionDetailId" column="wrong_question_collection_detail_id"/>
        <result property="wrongQuestionCollectionId" column="wrong_question_collection_id"/>
        <result property="questionId" column="question_id"/>
        <result property="questionNo" column="question_no"/>
        <result property="answerResult" column="answer_result"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createDept" column="create_dept"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="WrongQuestionCollectionDetailVoResult" type="com.jxw.shufang.student.domain.vo.WrongQuestionCollectionDetailVo">
        <id property="wrongQuestionCollectionDetailId" column="wrong_question_collection_detail_id"/>
        <result property="wrongQuestionCollectionId" column="wrong_question_collection_id"/>
        <result property="questionId" column="question_id"/>
        <result property="questionNo" column="question_no"/>
        <result property="answerResult" column="answer_result"/>
        <result property="questionPid" column="question_pid"/>
        <result property="sort" column="sort" />
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="selectWrongQuestionCollectionDetailVo">
        select wrong_question_collection_detail_id, wrong_question_collection_id, question_id,
               question_no, answer_result, create_by, create_time, create_dept, update_by, update_time
        from wrong_question_collection_detail
    </sql>

    <!-- 查询错题合集详情列表 -->
    <select id="selectWrongQuestionCollectionDetailList" parameterType="com.jxw.shufang.student.domain.bo.WrongQuestionCollectionDetailBo" resultMap="WrongQuestionCollectionDetailVoResult">
        <include refid="selectWrongQuestionCollectionDetailVo"/>
        <where>
            <if test="wrongQuestionCollectionId != null">
                and wrong_question_collection_id = #{wrongQuestionCollectionId}
            </if>
            <if test="questionId != null">
                and question_id = #{questionId}
            </if>
            <if test="questionNo != null and questionNo != ''">
                and question_no like concat('%', #{questionNo}, '%')
            </if>
            <if test="answerResult != null and answerResult != ''">
                and answer_result = #{answerResult}
            </if>
        </where>
        order by question_no asc, create_time desc
    </select>

    <!-- 根据错题合集ID查询详情 -->
    <select id="selectByCollectionId" parameterType="Long" resultMap="WrongQuestionCollectionDetailVoResult">
        <include refid="selectWrongQuestionCollectionDetailVo"/>
        where wrong_question_collection_id = #{wrongQuestionCollectionId}
        order by question_no asc, create_time desc
    </select>

    <!-- 根据错题合集ID和题目ID查询详情 -->
    <select id="selectByCollectionIdAndQuestionId" resultMap="WrongQuestionCollectionDetailVoResult">
        <include refid="selectWrongQuestionCollectionDetailVo"/>
        where wrong_question_collection_id = #{wrongQuestionCollectionId} and question_id = #{questionId}
    </select>

    <!-- 统计错题合集详情数量 -->
    <select id="countByCollectionId" parameterType="Long" resultType="int">
        select count(*) from wrong_question_collection_detail where wrong_question_collection_id = #{wrongQuestionCollectionId}
    </select>

    <!-- 统计指定作答结果的详情数量 -->
    <select id="countByCollectionIdAndAnswerResult" resultType="int">
        select count(*) from wrong_question_collection_detail
        where wrong_question_collection_id = #{wrongQuestionCollectionId} and answer_result = #{answerResult}
    </select>

    <!-- 批量查询错题合集详情 -->
    <select id="selectByIds" resultMap="WrongQuestionCollectionDetailVoResult">
        <include refid="selectWrongQuestionCollectionDetailVo"/>
        where wrong_question_collection_detail_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by question_no asc, create_time desc
    </select>

    <!-- 根据错题合集ID删除详情 -->
    <delete id="deleteByCollectionId" parameterType="Long">
        delete from wrong_question_collection_detail where wrong_question_collection_id = #{wrongQuestionCollectionId}
    </delete>

    <!-- 批量插入错题合集详情 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into wrong_question_collection_detail (
        wrong_question_collection_detail_id, wrong_question_collection_id, question_id, question_no, answer_result,
        question_pid, sort, create_by, create_time, create_dept, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.wrongQuestionCollectionDetailId}, #{item.wrongQuestionCollectionId}, #{item.questionId},
              #{item.questionNo}, #{item.answerResult}, #{item.questionPid}, #{item.sort},
              #{item.createBy}, #{item.createTime}, #{item.createDept}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据题目ID查询相关的错题合集详情 -->
    <select id="selectByQuestionId" parameterType="Long" resultMap="WrongQuestionCollectionDetailVoResult">
        <include refid="selectWrongQuestionCollectionDetailVo"/>
        where question_id = #{questionId}
        order by create_time desc
    </select>

    <!-- 查询错题合集详情统计信息 -->
    <select id="selectStatisticsByCollectionId" parameterType="Long" resultType="java.util.Map">
        select
            answer_result,
            count(*) as count
        from wrong_question_collection_detail
        where wrong_question_collection_id = #{wrongQuestionCollectionId}
        group by answer_result
    </select>

</mapper>
