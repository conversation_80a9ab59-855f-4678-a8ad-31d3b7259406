<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.WrongQuestionCollectionMapper">

    <resultMap id="WrongQuestionCollectionResult" type="com.jxw.shufang.student.domain.WrongQuestionCollection">
        <id property="wrongQuestionCollectionId" column="wrong_question_collection_id"/>
        <result property="studentId" column="student_id"/>
        <result property="collectionStatus" column="collection_status"/>
        <result property="collectionType" column="collection_type"/>
        <result property="reviseScreenshots" column="revise_screenshots"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="createDept" column="create_dept"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="WrongQuestionCollectionVoResult" type="com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo">
        <id property="wrongQuestionCollectionId" column="wrong_question_collection_id"/>
        <result property="studentId" column="student_id"/>
        <result property="collectionStatus" column="collection_status"/>
        <result property="collectionType" column="collection_type"/>
        <result property="reviseScreenshots" column="revise_screenshots"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="selectWrongQuestionCollectionVo">
        select wrong_question_collection_id, student_id, collection_status, collection_type, 
               revise_screenshots, remark, create_by, create_time, create_dept, update_by, update_time
        from wrong_question_collection
    </sql>

    <!-- 查询错题合集列表 -->
    <select id="selectWrongQuestionCollectionList" parameterType="com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo" resultMap="WrongQuestionCollectionVoResult">
        <include refid="selectWrongQuestionCollectionVo"/>
        <where>
            <if test="studentId != null">
                and student_id = #{studentId}
            </if>
            <if test="collectionStatus != null">
                and collection_status = #{collectionStatus}
            </if>
            <if test="collectionType != null">
                and collection_type = #{collectionType}
            </if>
            <if test="remark != null and remark != ''">
                and remark like concat('%', #{remark}, '%')
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 根据学生ID查询错题合集 -->
    <select id="selectByStudentId" parameterType="Long" resultMap="WrongQuestionCollectionVoResult">
        <include refid="selectWrongQuestionCollectionVo"/>
        where student_id = #{studentId}
        order by create_time desc
    </select>

    <!-- 根据学生ID和类型查询错题合集 -->
    <select id="selectByStudentIdAndType" resultMap="WrongQuestionCollectionVoResult">
        <include refid="selectWrongQuestionCollectionVo"/>
        where student_id = #{studentId} and collection_type = #{collectionType}
        order by create_time desc
    </select>

    <!-- 统计学生错题合集数量 -->
    <select id="countByStudentId" parameterType="Long" resultType="int">
        select count(*) from wrong_question_collection where student_id = #{studentId}
    </select>

    <!-- 统计学生指定状态的错题合集数量 -->
    <select id="countByStudentIdAndStatus" resultType="int">
        select count(*) from wrong_question_collection 
        where student_id = #{studentId} and collection_status = #{collectionStatus}
    </select>

    <!-- 批量查询错题合集 -->
    <select id="selectByIds" resultMap="WrongQuestionCollectionVoResult">
        <include refid="selectWrongQuestionCollectionVo"/>
        where wrong_question_collection_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by create_time desc
    </select>

</mapper>
