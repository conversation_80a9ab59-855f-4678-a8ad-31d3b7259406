<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.QuestionCollectMapper">
    <select id="selectQuestionCollects" resultType="com.jxw.shufang.student.domain.vo.QuestionCollectVo">
        select *
        from question_collect
        <where>
            <if test="ew.isNonEmptyOfWhere()">
                and ${ew.sqlSegment}
            </if>
        </where>
    </select>

    <delete id="deleteBatchByBo" parameterType="list">
        DELETE FROM question_collect
        WHERE (student_id, question_id) IN
        <foreach collection="bos" item="item" separator="," open="(" close=")">
            (#{item.studentId}, #{item.questionId})
        </foreach>
    </delete>

</mapper>
