<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.student.mapper.StudentConsultantRecordMapper">

    <resultMap id="StudentConsultantRecordResultMap" type="com.jxw.shufang.student.domain.vo.StudentConsultantRecordVo">

    </resultMap>

    <select id="selectNewResponsibleRecord" resultMap="StudentConsultantRecordResultMap">
        select * from
        student_consultant_record t
        INNER JOIN (
                select max(student_consultant_record_id) student_consultant_record_id
                from student_consultant_record
                group by student_id
        ) t1 ON t.student_consultant_record_id = t1.student_consultant_record_id
        where 1=1
        <if test="staffIdList!= null and staffIdList.size() > 0">
            and t.student_consultant_id in
            <foreach collection="staffIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="studentIdList!= null and studentIdList.size() > 0">
            and t.student_id in
            <foreach collection="studentIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


    </select>

    <select id="queryConsultantRecordListByStudentId" resultType="com.jxw.shufang.student.domain.dto.StudentWithConsultantDTO">
        select
            consultant_record.student_id,
            consultant_record.student_consultant_id,
            consultant_record.create_time,
            sys_user.nick_name
        from student_consultant_record consultant_record
        INNER JOIN (
            SELECT
                student_id,
                MAX(create_time) AS max_create_time
            FROM
                student_consultant_record
            WHERE
                student_id IN
                <foreach collection="studentIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            GROUP BY
            student_id
        ) latest_cr ON consultant_record.student_id = latest_cr.student_id AND consultant_record.create_time = latest_cr.max_create_time
        left join branch_staff branch_staff on branch_staff.branch_staff_id = consultant_record.student_consultant_id
        left join sys_user sys_user on sys_user.user_id = branch_staff.create_by
        order by consultant_record.create_time desc
    </select>
</mapper>
