# Tomcat
server:
  port: 9800

# Spring
spring:
  application:
    # 应用名称
    name: shufang-student


--- # nacos 配置
spring:
  cloud:
    nacos:
      # nacos 服务地址
      server-addr: ${nacos.server}
      username: ${nacos.username}
      password: ${nacos.password}
      discovery:
        # 注册组
        group: shufang
        namespace: ${spring.profiles.active}
      config:
        # 配置组
        group: shufang
        namespace: ${spring.profiles.active}
  config:
    import:
      - optional:nacos:application-common.yml
      - optional:nacos:datasource.yml
      - optional:nacos:${spring.application.name}.yml
