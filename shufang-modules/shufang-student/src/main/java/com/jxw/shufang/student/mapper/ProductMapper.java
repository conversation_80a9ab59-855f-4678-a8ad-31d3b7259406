package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.Product;
import com.jxw.shufang.student.domain.vo.ProductVo;

import java.util.List;

/**
 * 产品（会员卡）Mapper接口
 *
 *
 * @date 2024-02-29
 */
public interface ProductMapper extends BaseMapperPlus<Product, ProductVo> {

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    List<ProductVo> queryOptionList(@Param(Constants.WRAPPER) QueryWrapper<Product> productLambdaQueryWrapper);

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept"),
        @DataColumn(key = "userName", value = "t.create_by")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    Page<ProductVo> selectProductPageList(@Param("page")Page<Object> build,@Param(Constants.WRAPPER) QueryWrapper<Product> lqw);

    ProductVo selectProductById(Long productId);
}
