package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.PrintRecord;
import com.jxw.shufang.student.domain.vo.PrintRecordVo;

/**
 * 打印记录Mapper接口
 *
 *
 * @date 2024-05-07
 */
public interface PrintRecordMapper extends BaseMapperPlus<PrintRecord, PrintRecordVo> {

    Page<PrintRecordVo> selectPrintRecordVoPage(@Param("page") Page<PrintRecord> build,@Param(Constants.WRAPPER) QueryWrapper<PrintRecord> qw);
}
