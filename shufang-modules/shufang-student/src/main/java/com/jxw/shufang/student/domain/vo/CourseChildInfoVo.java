package com.jxw.shufang.student.domain.vo;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class CourseChildInfoVo implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 父级ID
     */
    private Long courseParentId;

    /**
     * 自己的子节点id,逗号分隔
     */
    private String childIds;

    /**
     * 有知识点的课程id,逗号分隔
     */
    private String hasKnowledgeIdCourseIds;
}
