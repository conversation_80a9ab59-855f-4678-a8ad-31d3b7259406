package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.student.api.RemoteStudentCourseResourceService;
import com.jxw.shufang.student.api.domain.dto.RemoteCourseWithTopCourseDTO;
import com.jxw.shufang.student.domain.dto.CourseWithTopCourseDTO;
import com.jxw.shufang.student.domain.dto.DownloadMergeResourceDTO;
import com.jxw.shufang.student.service.ICourseResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/26 13:56
 * @Version 1
 * @Description 远程服务-学生课程服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentCourseResourceServiceImpl implements RemoteStudentCourseResourceService {
    @Resource
    private ICourseResourceService courseResourceService;

    @Override
    public Map<Long, RemoteCourseWithTopCourseDTO> getMergeCourseResource(Long studentId, List<Long> courseIds, List<String> resourceTypes)
        throws ServiceException {
        DownloadMergeResourceDTO downloadMergeResourceDTO = new DownloadMergeResourceDTO();
        downloadMergeResourceDTO.setStudentId(studentId);
        downloadMergeResourceDTO.setCourseIds(courseIds);
        downloadMergeResourceDTO.setResourceTypes(resourceTypes);
        Map<Long, CourseWithTopCourseDTO> courseMap = courseResourceService.getMergeCourseResource(downloadMergeResourceDTO);
        return courseMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                RemoteStudentCourseResourceServiceImpl::buildRemoteCourseWithTopCourseDTO,(v1, v2) -> v2,LinkedHashMap::new
            ));
    }

    private static RemoteCourseWithTopCourseDTO buildRemoteCourseWithTopCourseDTO(Map.Entry<Long, CourseWithTopCourseDTO> entry) {
        CourseWithTopCourseDTO source = entry.getValue();
        RemoteCourseWithTopCourseDTO courseDTO = new RemoteCourseWithTopCourseDTO();
        courseDTO.setCourseId(source.getCourseId());
        courseDTO.setTopCourseId(source.getTopCourseId());
        courseDTO.setCourseName(source.getCourseName());
        courseDTO.setTopCourseName(source.getTopCourseName());
        courseDTO.setCourseStage(source.getCourseStage());
        courseDTO.setCourseGrade(source.getCourseGrade());
        courseDTO.setSubject(source.getSubject());
        courseDTO.setCourseSpecialTopic(source.getCourseSpecialTopic());
        courseDTO.setCourseQuarterType(source.getCourseQuarterType());
        courseDTO.setValue(source.getValue());
        courseDTO.setKnowledgeId(source.getKnowledgeId());
        return courseDTO;
    }
}
