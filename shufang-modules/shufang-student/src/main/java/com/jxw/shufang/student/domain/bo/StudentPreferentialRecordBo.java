package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentPreferentialRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Data
@AutoMapper(target = StudentPreferentialRecord.class)
public class StudentPreferentialRecordBo extends BaseEntity {
    /**
     * 记录id
     */
    private Long recordId;
    /**
     * 会员ID
     */
    @NotNull(message = "请选择转赠的会员")
    private Long ownerStudentId;
    /**
     * 来源会员ID
     */
    @NotNull(message = "请选择接收转赠的会员")
    private Long fromStudentId;
    /**
     * 业务ID
     */
    private Long businessId;
    /**
     * 支出or入账 0-收入 1-支出
     */
    private Integer changeType;
    /**
     * 获取类别 1-转介绍 2-转赠
     */
    private Integer gainType;
    /**
     * 优惠额度变动金额
     */
    @NotNull(message = "请输入转赠金额")
    @Min(value = 0, message = "转赠优惠额度需要大于零")
    private BigDecimal changePreferentialAmount;
    /**
     * 优惠额度解冻时间
     */
    private Date unFrozenTime;
    /**
     * 过滤未生效的记录
     */
    private Boolean noFrozenRecord;
}
