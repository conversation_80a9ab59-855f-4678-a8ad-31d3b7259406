package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionQuestionRecordVo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * ai批改记录Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiCorrectionRecordService {

    /**
     * 查询ai批改记录
     */
    AiCorrectionRecordVo queryById(Long aiCorrectionRecordId);

    /**
     * 查询ai批改记录列表
     */
    TableDataInfo<AiCorrectionRecordVo> queryPageList(AiCorrectionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询ai批改记录列表
     */
    List<AiCorrectionRecordVo> queryList(AiCorrectionRecordBo bo);

    /**
     * 新增ai批改记录
     */
    BigDecimal insertByBo(AiCorrectionRecordBo bo);

    /**
     * 修改ai批改记录
     */
    Boolean updateByBo(AiCorrectionRecordBo bo);

    /**
     * 校验并批量删除ai批改记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AiCorrectionQuestionRecordVo queryQuestionRecord( Long courseId, Long studentId,  String correctionType);

    List<AiCorrectionRecordVo> queryRecordAndRightWrongInfo(AiCorrectionRecordBo aiCorrectionRecordBo);

    Boolean submitWithoutCorrection(AiCorrectionRecordBo correctionRecordBo);

    AiCorrectionRecordVo queryRecord(Long courseId, Long studentId, String correctionType);
}
