package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.*;
import com.jxw.shufang.extresource.api.domain.bo.QueryTestPaperFileRequest;
import com.jxw.shufang.extresource.api.domain.bo.RemoteOssBo;
import com.jxw.shufang.extresource.api.domain.bo.RemotePaperQuestionBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudentPaperRecord;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordBo;
import com.jxw.shufang.student.domain.dto.AiPaperOssUrlDTO;
import com.jxw.shufang.student.domain.dto.AiRecordOssUrlDTO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.StudentPaperRecordMapper;
import com.jxw.shufang.student.service.IStudentAnswerRecordService;
import com.jxw.shufang.student.service.IStudentPaperRecordService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.RemoteUserSignService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.system.api.model.RemoteSysUserSignBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * 用户答题试卷记录Service业务层处理
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentPaperRecordServiceImpl implements IStudentPaperRecordService, BaseService {

    private final StudentPaperRecordMapper baseMapper;
    private final IStudentService studentService;
    private final DictService dictService;
    private final IStudentAnswerRecordService studentAnswerRecordService;

    @DubboReference
    private RemoteCdsCommonService cdsCommonService;

    @DubboReference
    private RemoteUserSignService remoteUserSignService;

    @DubboReference
    private RemotePaperCommonService remotePaperCommonService;
    @DubboReference
    private RemoteQuestionService remoteQuestionService;
    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;
    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteTmsService remoteTmsService;

    /**
     * 查询用户答题试卷记录
     */
    @Override
    public StudentPaperRecordVo queryById(Long studentPaperRecordId) {
        StudentPaperRecordVo studentPaperRecordVo = baseMapper.selectVoById(studentPaperRecordId);
        if (ObjectUtils.isEmpty(studentPaperRecordVo)) {
            return null;
        }
        buildRecord(Collections.singletonList(studentPaperRecordVo), true);
        Integer integer = getLastRecordScore(studentPaperRecordVo);
        studentPaperRecordVo.setLastScore(integer);
        return studentPaperRecordVo;
    }


    @Override
    public String queryByReportIdUrl(String studentPaperRecordId) {
        StudentPaperRecordVo studentPaperRecordVo = baseMapper.selectVoById(studentPaperRecordId);
        if (ObjectUtils.isEmpty(studentPaperRecordVo)) {
            return null;
        }
        RemoteOssBo remoteOssBo = new RemoteOssBo();
        remoteOssBo.setKey(studentPaperRecordVo.getReportUrl());
        List<RemoteOssVo> ossUrl = cdsCommonService.getOssUrl(Collections.singletonList(remoteOssBo));
        if (CollectionUtils.isEmpty(ossUrl)) {
            return null;
        }

        return ossUrl.get(0).getUrl();
    }

    private Integer getLastRecordScore(StudentPaperRecordVo studentPaperRecordVo) {
        LambdaQueryWrapper<StudentPaperRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudentPaperRecord::getStudentId, studentPaperRecordVo.getStudentId()).eq(StudentPaperRecord::getTestPaperId, studentPaperRecordVo.getTestPaperId()).eq(StudentPaperRecord::getSource, studentPaperRecordVo.getSource()).ne(StudentPaperRecord::getStudentPaperRecordId, studentPaperRecordVo.getStudentPaperRecordId()).eq(StudentPaperRecord::getRecordStatus, 1).orderByDesc(StudentPaperRecord::getCreateTime).last("limit 1");

        List<StudentPaperRecordVo> studentPaperRecordVos = baseMapper.selectVoList(lqw);
        if (CollectionUtils.isEmpty(studentPaperRecordVos)) {
            return null;
        }
        return studentPaperRecordVos.get(0).getScore();
    }

    /**
     * 查询用户答题试卷记录列表
     */
    @Override
    public TableDataInfo<StudentPaperRecordVo> queryPageList(StudentPaperRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentPaperRecord> lqw = buildQueryWrapper(bo);
        Page<StudentPaperRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        List<StudentPaperRecordVo> records = result.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            buildRecord(records, false);
        }

        return TableDataInfo.build(result);
    }

    private void buildRecord(List<StudentPaperRecordVo> records, boolean isDetail) {
        buildStudent(records);
        buildSubject(records);
        buildGrade(records);
        if (Boolean.TRUE.equals(isDetail)) {
            buildTime(records);
            buildTestPaper(records);
            buildEdition(records);
            buildGradeVolume(records);
            buildParse(records);
        }
    }

    private void buildParse(List<StudentPaperRecordVo> records) {
        for (StudentPaperRecordVo record : records) {
            String phaseName = remotePaperCommonService.getPhaseName(record.getPhaseId());
            record.setPhaseName(phaseName);
        }
    }

    private void buildTime(List<StudentPaperRecordVo> records) {
        for (StudentPaperRecordVo record : records) {
            Date finishTime = record.getFinishTime();
            Date startTime = record.getStartTime();
            if (!ObjectUtils.isEmpty(startTime) && !ObjectUtils.isEmpty(finishTime)) {
                long durationMillis = finishTime.getTime() - startTime.getTime(); // 毫秒差
                if (durationMillis > 0) {
                    long seconds = durationMillis / 1000; // 转换为秒
                    record.setDurationMillis(seconds);
                }
            }
        }
    }

    private void buildGradeVolume(List<StudentPaperRecordVo> records) {
        List<Integer> collect = records.stream().filter(v -> v.getGradeVolumeId() != null).distinct().map(StudentPaperRecordVo::getGradeVolumeId).collect(Collectors.toList());

        Map<Integer, RemoteGradeVolumeVo> gradeVolumeMap = getGradeVolumeMap(collect);
        for (StudentPaperRecordVo record : records) {
            if (gradeVolumeMap.containsKey(record.getGradeVolumeId())) {
                record.setGradeVolumeVo(gradeVolumeMap.get(record.getGradeVolumeId()));
            }
        }
    }

    private void buildEdition(List<StudentPaperRecordVo> records) {
        List<Integer> collect = records.stream().filter(v -> v.getEditionId() != null).distinct().map(StudentPaperRecordVo::getEditionId).collect(Collectors.toList());

        Map<Integer, RemotePublisherVo> editionMap = getEditionMap(collect);
        for (StudentPaperRecordVo record : records) {
            if (editionMap.containsKey(record.getEditionId())) {
                record.setEditionVo(editionMap.get(record.getEditionId()));
            }
        }
    }

    private void buildTestPaper(List<StudentPaperRecordVo> records) {
        List<Integer> list = records.stream().filter(v -> !ObjectUtils.isEmpty(v)).map(StudentPaperRecordVo::getTestPaperId).map(Math::toIntExact).distinct().toList();
        //获取试卷 组装数据
        List<RemoteAiPaperDetailVo> remoteAiPaperDetailVos = remotePaperCommonService.listPaperByIdList(list);
        if (CollectionUtils.isEmpty(remoteAiPaperDetailVos)) {
            return;
        }

        Map<Long, RemoteAiPaperDetailVo> paperMap = remoteAiPaperDetailVos.stream().collect(Collectors.toMap(RemoteAiPaperDetailVo::getId, Function.identity()));
        for (StudentPaperRecordVo record : records) {
            if (!paperMap.containsKey(record.getTestPaperId())) {
                continue;
            }
            RemoteAiPaperDetailVo paper = paperMap.get(record.getTestPaperId());
            record.setEditionId(paper.getEditionId());
            record.setGradeVolumeId(paper.getGradeVolumeId());
            record.setPhaseId(paper.getPhaseId());
        }

    }

    private void buildGrade(List<StudentPaperRecordVo> records) {
        List<Integer> list = records.stream().map(StudentPaperRecordVo::getGradeId).toList();
        Map<Integer, RemoteGradeVo> gradeVoMap = getGradeVoMap(list);
        for (StudentPaperRecordVo record : records) {
            if (gradeVoMap.containsKey(Math.toIntExact(record.getGradeId()))) {
                record.setGradeVo(gradeVoMap.get(Math.toIntExact(record.getGradeId())));
            }
        }
    }

    private @NotNull Map<Integer, RemoteGradeVo> getGradeVoMap(List<Integer> list) {
        List<RemoteGradeVo> gradeList = cdsCommonService.getNewGradeList(list);
        if (CollectionUtils.isEmpty(gradeList)) {
            return Collections.emptyMap();
        }
        return gradeList.stream().collect(Collectors.toMap(RemoteGradeVo::getId, Function.identity()));
    }


    private @NotNull Map<Integer, RemoteGradeVolumeVo> getGradeVolumeMap(List<Integer> list) {
        List<RemoteGradeVolumeVo> gradeVolumeList = cdsCommonService.findGradeVolumeList(list);
        if (CollectionUtils.isEmpty(gradeVolumeList)) {
            return Collections.emptyMap();
        }
        return gradeVolumeList.stream().collect(Collectors.toMap(RemoteGradeVolumeVo::getId, Function.identity()));
    }


    private @NotNull Map<Integer, RemotePublisherVo> getEditionMap(List<Integer> list) {
        List<RemotePublisherVo> remotePublisherVos = cdsCommonService.listPublishers(list);
        if (CollectionUtils.isEmpty(remotePublisherVos)) {
            return Collections.emptyMap();
        }
        return remotePublisherVos.stream().collect(Collectors.toMap(RemotePublisherVo::getId, Function.identity()));
    }

    private @NotNull Map<Integer, RemotePublisherVo> getPhaseMap(List<Integer> list) {
        List<RemotePublisherVo> remotePublisherVos = cdsCommonService.listPublishers(list);
        if (CollectionUtils.isEmpty(remotePublisherVos)) {
            return Collections.emptyMap();
        }
        return remotePublisherVos.stream().collect(Collectors.toMap(RemotePublisherVo::getId, Function.identity()));
    }

    private void buildSubject(List<StudentPaperRecordVo> records) {
        List<Integer> list = records.stream().map(StudentPaperRecordVo::getSubjectId).toList();
        Map<Integer, RemoteSubjectVo> subjectVoMap = getSubjectVoMap(list);
        for (StudentPaperRecordVo record : records) {
            if (subjectVoMap.containsKey(record.getSubjectId())) {
                record.setSubjectVo(subjectVoMap.get(record.getSubjectId()));
            }
        }
    }

    @NotNull
    private Map<Integer, RemoteSubjectVo> getSubjectVoMap(List<Integer> list) {
        List<RemoteSubjectVo> subjectVoList = cdsCommonService.listSubjects(list);
        if (CollectionUtils.isEmpty(subjectVoList)) {
            return Collections.emptyMap();
        }
        return subjectVoList.stream().collect(Collectors.toMap(RemoteSubjectVo::getId, Function.identity()));
    }

    private void buildStudent(List<StudentPaperRecordVo> records) {
        List<Long> list = records.stream().map(StudentPaperRecordVo::getStudentId).distinct().toList();
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(list);
        studentBo.setWithConsultantInfo(true);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollectionUtils.isEmpty(studentVos)) {
            return;
        }

        Map<String, String> sysUserSex = dictService.getAllDictByDictType("sys_user_sex");

        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));
        for (StudentPaperRecordVo record : records) {
            if (studentVoMap.containsKey(record.getStudentId())) {
                Long studentId = record.getStudentId();
                StudentVo studentVo = studentVoMap.get(studentId);
                String studentSex = studentVo.getStudentSex();
                record.setSex(sysUserSex.getOrDefault(studentSex, null));

                RemoteStaffVo consultant = studentVo.getConsultant();
                String staffName = getStaffName(consultant);
                record.setStaffName(staffName);
            }
        }
    }

    private String getStaffName(RemoteStaffVo consultant) {
        if (!ObjectUtils.isEmpty(consultant)) {
            RemoteUserVo user = consultant.getUser();
            if (!ObjectUtils.isEmpty(user)) {
                return user.getNickName();
            }
        }
        return "";
    }

    /**
     * 查询用户答题试卷记录列表
     */
    @Override
    public List<StudentPaperRecordVo> queryList(StudentPaperRecordBo bo) {
        LambdaQueryWrapper<StudentPaperRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentPaperRecord> buildQueryWrapper(StudentPaperRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentPaperRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentPaperRecord::getStudentId, bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getStudentName()), StudentPaperRecord::getStudentName, bo.getStudentName());
        lqw.eq(bo.getGradeId() != null, StudentPaperRecord::getGradeId, bo.getGradeId());
        lqw.eq(bo.getTestPaperId() != null, StudentPaperRecord::getTestPaperId, bo.getTestPaperId());
        lqw.like(StringUtils.isNotBlank(bo.getTestPaperName()), StudentPaperRecord::getTestPaperName, bo.getTestPaperName());
        lqw.eq(bo.getTestPaperType() != null, StudentPaperRecord::getTestPaperType, bo.getTestPaperType());
        lqw.eq(bo.getSubjectId() != null, StudentPaperRecord::getSubjectId, bo.getSubjectId());
        lqw.eq(bo.getScore() != null, StudentPaperRecord::getScore, bo.getScore());
        lqw.eq(bo.getRecordStatus() != null, StudentPaperRecord::getRecordStatus, bo.getRecordStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getReportUrl()), StudentPaperRecord::getReportUrl, bo.getReportUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getSource()), StudentPaperRecord::getSource, bo.getSource());
        lqw.eq(bo.getStartTime() != null, StudentPaperRecord::getStartTime, bo.getStartTime());
        lqw.eq(bo.getFinishTime() != null, StudentPaperRecord::getFinishTime, bo.getFinishTime());

        lqw.ge(bo.getFinishTimeStart() != null, StudentPaperRecord::getFinishTime, bo.getFinishTimeStart());
        lqw.le(bo.getFinishTimeEnd() != null, StudentPaperRecord::getFinishTime, bo.getFinishTimeEnd());
        lqw.orderByDesc(StudentPaperRecord::getFinishTime);

        //当登录的是门店用户的时候，只能查看管理端的和自己门店的
        if (LoginHelper.isBranchUser()){
            Long deptId = LoginHelper.getSelectDeptId()!=null?LoginHelper.getSelectDeptId():LoginHelper.getDeptId();
            List<RemoteDeptVo> selfAndChildShopList = remoteDeptService.getSelfAndChildShopList(deptId);
            if (CollUtil.isNotEmpty(selfAndChildShopList)){
                List<Long> deptIdList = selfAndChildShopList.stream().map(RemoteDeptVo::getDeptId).toList();
                lqw.in(StudentPaperRecord::getCreateDept,deptIdList);
            }


        }

        return lqw;
    }

    /**
     * 新增用户答题试卷记录
     */
    @Override
    public StudentPaperRecordVo insertByBo(StudentPaperRecordBo bo) {
        StudentPaperRecord add = MapstructUtils.convert(bo, StudentPaperRecord.class);
        validEntityBeforeSave(add);
        baseMapper.insert(add);

        StudentPaperRecordVo convert = MapstructUtils.convert(add, StudentPaperRecordVo.class);
        convert.setStudentPaperRecordId(add.getStudentPaperRecordId());
        return convert;
    }

    /**
     * 修改用户答题试卷记录
     */
    @Override
    public Boolean updateByBo(StudentPaperRecordBo bo) {
        StudentPaperRecord update = MapstructUtils.convert(bo, StudentPaperRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    @Override
    public Boolean uploadFile(Long recordId, String url) {

        return baseMapper.update(null, Wrappers.<StudentPaperRecord>lambdaUpdate().set(StudentPaperRecord::getReportUrl, url).eq(StudentPaperRecord::getStudentPaperRecordId, recordId)) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentPaperRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除用户答题试卷记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 判断用户是否评测过
     */
    @Override
    public Boolean checkSignIsNotExist(Long userId) {
        RemoteSysUserSignBo sysUserSignBo = getRemoteSysUserSignBo(userId);
        return remoteUserSignService.checkSignIsNotExist(sysUserSignBo);
    }

    @Override
    public int finish(Long studentPaperRecordId) {
        StudentPaperRecordVo studentPaperRecordVo = baseMapper.selectVoById(studentPaperRecordId);
        if (ObjectUtils.isEmpty(studentPaperRecordVo)) {
            return 0;
        }
        return checkAndUpdate(studentPaperRecordVo);
    }

    private int checkAndUpdate(StudentPaperRecordVo studentPaperRecordVo) {
        checkSign(studentPaperRecordVo);
        return updateStatus(studentPaperRecordVo);
    }

    private int updateStatus(StudentPaperRecordVo studentPaperRecordVo) {
        return baseMapper.update(null, Wrappers.<StudentPaperRecord>lambdaUpdate().set(StudentPaperRecord::getRecordStatus, "1").eq(StudentPaperRecord::getStudentPaperRecordId, studentPaperRecordVo.getStudentPaperRecordId()));
    }

    private void checkSign(StudentPaperRecordVo studentPaperRecordId) {
        checkSign(studentPaperRecordId.getStudentId());
    }


    @Override
    public void checkSign(Long userId) {
        RemoteSysUserSignBo sysUserSignBo = getRemoteSysUserSignBo(userId);
        Boolean b = remoteUserSignService.checkSignIsNotExist(sysUserSignBo);
        if (Boolean.TRUE.equals(b)) {
            remoteUserSignService.sign(userId, 1, "1");
        }
    }

    private static @NotNull RemoteSysUserSignBo getRemoteSysUserSignBo(Long userId) {
        RemoteSysUserSignBo sysUserSignBo = new RemoteSysUserSignBo();
        sysUserSignBo.setUserId(userId);
        sysUserSignBo.setType(1);
        sysUserSignBo.setVersion("1");
        return sysUserSignBo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int finishAndCalculate(Long studentPaperRecordId) {
        StudentPaperRecordVo studentPaperRecordVo = baseMapper.selectVoById(studentPaperRecordId);
        if (ObjectUtils.isEmpty(studentPaperRecordVo)) {
            return 0;
        }

        //计算正确率保存到详情试卷
        Integer score = null;
        List<StudentAnswerRecordVo> studentAnswerRecordVos = studentAnswerRecordService.queryListByStudentPaperRecordId(studentPaperRecordId);
        if (!CollectionUtils.isEmpty(studentAnswerRecordVos)) {
            long count = studentAnswerRecordVos.stream().filter(v -> v.getIsMistake() == 0).count();
            if (count != 0) {
                int total = studentAnswerRecordVos.size();
                BigDecimal trueDecimal = BigDecimal.valueOf(count);
                BigDecimal totalDecimal = BigDecimal.valueOf(total);
                score = trueDecimal.divide(totalDecimal, 2, RoundingMode.HALF_DOWN).multiply(BigDecimal.valueOf(100)).intValue();
            }
        }
        return updateScore(studentPaperRecordId, score);
    }

    @Override
    public StudentPaperRecordQuestionsVo getQuestionsAndKnowledge(Long studentPaperRecordId) {
        StudentPaperRecordVo studentPaperRecordVo = baseMapper.selectVoById(studentPaperRecordId);
        if (ObjectUtils.isEmpty(studentPaperRecordVo)) {
            return null;
        }
        Long testPaperId = studentPaperRecordVo.getTestPaperId();


        StudentPaperRecordQuestionsVo studentPaperRecordQuestionsVo = new StudentPaperRecordQuestionsVo();

        //获取用户答题
        Map<Long, StudentAnswerRecordVo> answerRecordVoMap = getAnswerMap(studentPaperRecordId);
        List<RemoteQuestionSimpleVo> questionList = getQuestionList(testPaperId);

        List<StudentStudentPaperQuestionsVo> questionsVos = buildQuestion(questionList, answerRecordVoMap);

        studentPaperRecordQuestionsVo.setQuestionsList(questionsVos);

        buildKnowledge(questionsVos, studentPaperRecordQuestionsVo);
        afterSearchKnowledge(questionsVos);

        return studentPaperRecordQuestionsVo;
    }

    @Override
    public List<AiRecordOssUrlDTO> getStudentRecordInfo(List<String> studentPaperRecordIds) throws ServiceException{
        // 查看
        List<StudentPaperRecordVo> studentRecordsByIds = this.getStudentRecordsByIds(studentPaperRecordIds);
        List<StudentPaperRecordVo> existReportUrl = studentRecordsByIds.stream()
            .filter(f -> StrUtil.isNotEmpty(f.getReportUrl()))
            .toList();
        if (CollectionUtil.isEmpty(existReportUrl)) {
            throw new ServiceException("会员评测报告记录不存在");
        }
        // 2025.03.03 暂时注释，过滤掉不存在的url。
//        if(studentPaperRecordIds.size() != existReportUrl.size()){
//            throw new ServiceException("存在无效的评测报告，请检查");
//        }

        // 获取ossurl
        List<RemoteOssVo> tencentOssUrl = this.getRemoteTencentOssList(existReportUrl);

        // 报告url,评测内容
        Map<String, StudentPaperRecordVo> reportUrlMap = existReportUrl.parallelStream()
            .collect(Collectors.toMap(
                StudentPaperRecordVo::getReportUrl,
                Function.identity(),
                (existing, replacement) -> existing
            ));

        return this.getStudentIdReportMap(tencentOssUrl, reportUrlMap);
    }

    private  List<AiRecordOssUrlDTO> getStudentIdReportMap(List<RemoteOssVo> tencentOssUrl,
                                                           Map<String, StudentPaperRecordVo> reportUrlMap) {
        List<AiRecordOssUrlDTO> aiRecordOssUrlDTOS = new ArrayList<>();
        for (RemoteOssVo remoteTencentOssVo : tencentOssUrl) {
            StudentPaperRecordVo studentPaperRecordVo = reportUrlMap.get(remoteTencentOssVo.getKey());
            AiRecordOssUrlDTO aiRecordOssUrlDTO = new AiRecordOssUrlDTO();
            aiRecordOssUrlDTO.setStudentId(studentPaperRecordVo.getStudentId());
            aiRecordOssUrlDTO.setReportOssUrl(remoteTencentOssVo.getUrl());
            aiRecordOssUrlDTO.setStudentName(studentPaperRecordVo.getStudentName());
            aiRecordOssUrlDTO.setCreateTime(studentPaperRecordVo.getCreateTime());
            aiRecordOssUrlDTOS.add(aiRecordOssUrlDTO);
        }
        return aiRecordOssUrlDTOS.stream()
            .sorted(Comparator.comparing(AiRecordOssUrlDTO::getCreateTime).reversed())
            .collect(toList());
    }

    private List<RemoteOssVo> getRemoteTencentOssList(List<StudentPaperRecordVo> studentRecordsByIds) {
        List<RemoteOssBo> remoteTencentOssBos = studentRecordsByIds.parallelStream()
            .filter(Objects::nonNull)
            .filter(f -> StringUtils.isNotEmpty(f.getReportUrl()))
            .map(m -> {
                RemoteOssBo remoteTencentOssBo = new RemoteOssBo();
                remoteTencentOssBo.setKey(m.getReportUrl());
                return remoteTencentOssBo;
            })
            .toList();
        return cdsCommonService.getOssUrl(remoteTencentOssBos);
    }


    @Override
    public List<StudentPaperRecordVo> getStudentRecordsByIds(List<String> studentPaperRecordIds) {
        LambdaQueryWrapper<StudentPaperRecord> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(StudentPaperRecord::getStudentPaperRecordId, studentPaperRecordIds);
        queryWrapper.orderByDesc(StudentPaperRecord::getUpdateTime);
        return baseMapper.selectVoList(queryWrapper);
    }

    @Override
    public List<AiPaperOssUrlDTO> getAiPaperInfo(List<String> studentPaperRecordIds) {
        // 查询评测记录
        List<StudentPaperRecordVo> studentRecordsByIds = this.getStudentRecordsByIds(studentPaperRecordIds);
        List<StudentPaperRecordVo> existPaperRecords = studentRecordsByIds.stream()
            .filter(f -> null != f.getTestPaperId())
            .toList();
        List<Integer> paperId = existPaperRecords.stream()
            .map(m -> m.getTestPaperId().intValue())
            .toList();
        if (studentPaperRecordIds.size() != existPaperRecords.size()) {
            throw new ServiceException("存在无效的试卷，请检查");
        }

        // 查询试卷pdf地址
        List<TestPaperFileVO> testPaperFileVOS = this.getOssTestPaperFileList(paperId);
        if (CollectionUtils.isEmpty(testPaperFileVOS)) {
            return Collections.emptyList();
        }

        Map<Integer, TestPaperFileVO> paperIdMap = testPaperFileVOS.stream()
            .collect(Collectors.toMap(TestPaperFileVO::getPaperId, Function.identity(), (v1, v2) -> v2));
        return existPaperRecords.stream()
            .map(paperRecord -> buildAiPaperUrlDTO(paperIdMap, paperRecord))
            .filter(Objects::nonNull)
            .sorted(Comparator.comparing(AiPaperOssUrlDTO::getCreatetime).reversed())
            .collect(toList());
    }

    private static AiPaperOssUrlDTO buildAiPaperUrlDTO(Map<Integer, TestPaperFileVO> paperIdMap, StudentPaperRecordVo paperRecord) {
        Long testPaperId = paperRecord.getTestPaperId();
        TestPaperFileVO paperFileVOS = paperIdMap.get(testPaperId.intValue());
        if (null == paperFileVOS) {
            return null;
        } else {
            AiPaperOssUrlDTO aiPaperOssUrlDTO = new AiPaperOssUrlDTO();
            aiPaperOssUrlDTO.setStudentId(paperRecord.getStudentId());
            aiPaperOssUrlDTO.setStudentName(paperRecord.getStudentName());
            aiPaperOssUrlDTO.setPaperPdfOssUrl(paperFileVOS.getPaperPdfUrl());
            aiPaperOssUrlDTO.setPaperId(testPaperId);
            aiPaperOssUrlDTO.setCreatetime(paperRecord.getCreateTime());
            return aiPaperOssUrlDTO;
        }
    }

    private List<TestPaperFileVO> getOssTestPaperFileList(List<Integer> paperId) {
        String fileLabel = "ORIGINAL";
        String fileType = "PDF";
        QueryTestPaperFileRequest request = new QueryTestPaperFileRequest();
        request.setPaperIds(paperId);
        request.setFileLabel(fileLabel);
        request.setFileType(fileType);
        List<TestPaperFileVO> testPaperFileVOS = remoteTmsService.queryTestPaperFileByPaperId(request);
        return testPaperFileVOS;
    }

    private void afterSearchKnowledge(List<StudentStudentPaperQuestionsVo> questionsVos) {
        if (CollectionUtils.isEmpty(questionsVos)) {
            return;
        }
        List<StudentStudentPaperQuestionsVo> list = questionsVos.stream().filter(v -> v.getHasVideo() == 0).toList();
        for (StudentStudentPaperQuestionsVo entity : list) {
            List<StudentStudentPaperKnowledgeVo> knowledgeList = entity.getKnowledgeList();
            entity.setHasKnowledgeVideo(0);
            if (CollectionUtils.isEmpty(knowledgeList)) {
                continue;
            }
            List<Long> knowledgeIdList = knowledgeList.stream().map(StudentStudentPaperKnowledgeVo::getKnowledgeId).toList();
            List<RemoteVideoVo> remoteAudioVideoVos = remoteKnowledgeService.queryKnowledge(knowledgeIdList);
            if (!CollectionUtils.isEmpty(remoteAudioVideoVos)) {
                entity.setHasKnowledgeVideo(1);
                entity.setKnowledgeVideoList(remoteAudioVideoVos);
            }
        }
    }

    private void buildKnowledge(List<StudentStudentPaperQuestionsVo> questionsVoList, StudentPaperRecordQuestionsVo detail) {
        if (CollectionUtils.isEmpty(questionsVoList)) {
            detail.setNotMasteredList(List.of());
            detail.setMasteredList(List.of());
            detail.setEnhancedList(List.of());
            return;
        }
        Map<Long, List<StudentStudentPaperQuestionsVo>> mapForQuestionListByKnowledgeId = getMapForQuestionListByKnowledgeId(questionsVoList);

        Map<Long, StudentStudentPaperKnowledgeVo> knowledgeMap = getKnowledgeMap(questionsVoList);

        //获取未掌握知识点
        List<Long> setNotMasteredList = getKnowledge(mapForQuestionListByKnowledgeId, 0);
        //获取已掌握知识点
        List<Long> setEnhancedList = getKnowledge(mapForQuestionListByKnowledgeId, 1);
        //获取需强化知识点
        List<Long> setMasteredList = getKnowledge(mapForQuestionListByKnowledgeId, 2);


        detail.setMasteredList(buildKnowledgeVo(setMasteredList, knowledgeMap));
        detail.setEnhancedList(buildKnowledgeVo(setEnhancedList, knowledgeMap));
        detail.setNotMasteredList(buildKnowledgeVo(setNotMasteredList, knowledgeMap));
    }

    private List<StudentStudentPaperKnowledgeVo> buildKnowledgeVo(List<Long> setMasteredList, Map<Long, StudentStudentPaperKnowledgeVo> knowledgeMap) {
        return setMasteredList.stream().map(knowledgeMap::get).collect(toList());
    }

    private List<Long> getKnowledge(Map<Long, List<StudentStudentPaperQuestionsVo>> map, int i) {
        List<Long> knowledegIdList = Lists.newArrayList();
        for (Long l : map.keySet()) {
            List<StudentStudentPaperQuestionsVo> questionsVos = map.get(l);
            List<StudentStudentPaperQuestionsVo> right = questionsVos.stream().filter(v -> v.getIsMistake().equals(0)).toList();
            if (i == 0) {
                //未掌握
                if (right.isEmpty()) {
                    knowledegIdList.add(l);
                }
            }
            if (i == 1) {
                //需强化
                if (right.size() != questionsVos.size() && !right.isEmpty()) {
                    knowledegIdList.add(l);
                }
            }
            if (i == 2) {
                //已掌握
                if (right.size() == questionsVos.size()) {
                    knowledegIdList.add(l);
                }
            }
        }

        return knowledegIdList;
    }

    private Map<Long, StudentStudentPaperKnowledgeVo> getKnowledgeMap(List<StudentStudentPaperQuestionsVo> questionsVoList) {
        Map<Long, StudentStudentPaperKnowledgeVo> knowledgeVoMap = new HashMap<>();

        for (StudentStudentPaperQuestionsVo questionsVo : questionsVoList) {
            if (questionsVo.getKnowledgeList() != null) {
                for (StudentStudentPaperKnowledgeVo knowledgeVo : questionsVo.getKnowledgeList()) {
                    Long knowledgeId = knowledgeVo.getKnowledgeId();
                    // 假设我们总是想要最后一个（或第一个，取决于你的需求）
                    if (!knowledgeVoMap.containsKey(knowledgeId)) {
                        knowledgeVoMap.put(knowledgeId, knowledgeVo);
                    }
                }
            }
        }
        return knowledgeVoMap;
    }

    private Map<Long, List<StudentStudentPaperQuestionsVo>> getMapForQuestionListByKnowledgeId(List<StudentStudentPaperQuestionsVo> questionsVoList) {
        //根据知识点平铺
        Map<Long, List<StudentStudentPaperQuestionsVo>> result = new HashMap<>();
        for (StudentStudentPaperQuestionsVo questionsVo : questionsVoList) {
            if (questionsVo.getKnowledgeList() != null) {
                for (StudentStudentPaperKnowledgeVo knowledgeVo : questionsVo.getKnowledgeList()) {
                    Long knowledgeId = knowledgeVo.getKnowledgeId();
                    result.computeIfAbsent(knowledgeId, k -> new ArrayList<>()).add(questionsVo);
                }
            }
        }
        return result;
    }

    private List<StudentStudentPaperQuestionsVo> buildQuestion(List<RemoteQuestionSimpleVo> questionList, Map<Long, StudentAnswerRecordVo> answerRecordVoMap) {
        if (CollectionUtils.isEmpty(questionList)) {
            return Collections.emptyList();
        }

        return questionList.stream().map(v -> {
            StudentStudentPaperQuestionsVo studentStudentPaperQuestionsVo = new StudentStudentPaperQuestionsVo();

            StudentAnswerRecordVo studentAnswerRecordVo = answerRecordVoMap.get(v.getId());
            if (ObjectUtils.isEmpty(studentAnswerRecordVo)) {
                //默认错误
                studentStudentPaperQuestionsVo.setIsMistake(1);
                studentStudentPaperQuestionsVo.setStayTime(0);
            } else {
                studentStudentPaperQuestionsVo.setIsMistake(studentAnswerRecordVo.getIsMistake());
                studentStudentPaperQuestionsVo.setStayTime(studentAnswerRecordVo.getStayTime());
            }
            studentStudentPaperQuestionsVo.setQuestionId(v.getId());
            studentStudentPaperQuestionsVo.setHasVideo(v.getHasVideo());
            studentStudentPaperQuestionsVo.setDifficulty(v.getDifficulty());
            List<StudentStudentPaperKnowledgeVo> list = buildPaperKnowledgeList(v);
            studentStudentPaperQuestionsVo.setKnowledgeList(list);
            return studentStudentPaperQuestionsVo;
        }).collect(toList());
    }

    private List<StudentStudentPaperKnowledgeVo> buildPaperKnowledgeList(RemoteQuestionSimpleVo remoteQuestionSimpleVo) {
        if (CollectionUtils.isEmpty(remoteQuestionSimpleVo.getKnowledges())) {
            return Collections.emptyList();
        }

        return remoteQuestionSimpleVo.getKnowledges().stream().map(v -> {
            StudentStudentPaperKnowledgeVo studentStudentPaperKnowledgeVo = new StudentStudentPaperKnowledgeVo();
            studentStudentPaperKnowledgeVo.setKnowledgeId(v.getId());
            studentStudentPaperKnowledgeVo.setKnowledgeName(v.getName());
            return studentStudentPaperKnowledgeVo;
        }).toList();
    }

    private Map<Long, StudentAnswerRecordVo> getAnswerMap(Long studentPaperRecordId) {
        List<StudentAnswerRecordVo> studentAnswerRecordVos = studentAnswerRecordService.queryListByStudentPaperRecordId(studentPaperRecordId);
        if (CollectionUtils.isEmpty(studentAnswerRecordVos)) {
            return Collections.emptyMap();
        }
        return studentAnswerRecordVos.stream().collect(toMap(StudentAnswerRecordVo::getQuestionId, Function.identity()));
    }

    private List<RemoteQuestionSimpleVo> getQuestionList(Long testPaperId) {
        RemotePaperQuestionBo remotePaperQuestionBo = new RemotePaperQuestionBo();
        remotePaperQuestionBo.setPaperId(Math.toIntExact(testPaperId));
        List<RemoteQuestionSimpleVo> paperQuestions = remoteQuestionService.getPaperQuestions(remotePaperQuestionBo);
        if (CollectionUtils.isEmpty(paperQuestions)) {
            return Collections.emptyList();
        }

        return paperQuestions;
    }

    private int updateScore(Long studentPaperRecordId, Integer score) {
        if (ObjectUtils.isEmpty(score) || ObjectUtils.isEmpty(score)) {
            return 0;
        }
        return baseMapper.update(null, Wrappers.<StudentPaperRecord>lambdaUpdate().set(StudentPaperRecord::getScore, score).eq(StudentPaperRecord::getStudentPaperRecordId, studentPaperRecordId));
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
