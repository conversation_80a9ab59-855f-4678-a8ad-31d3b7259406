package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.student.domain.StudyFeedbackReport;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 学习反馈报告视图对象 study_feedback_report
 *
 * @date 2024-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyFeedbackReport.class)
public class StudyFeedbackReportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 学生ID
     */
    @ExcelProperty(value = "学生ID")
    private Long studentId;

    /**
     * 报告周期开始日期
     */
    @ExcelProperty(value = "报告周期开始日期")
    private Date periodStart;

    /**
     * 报告周期结束日期
     */
    @ExcelProperty(value = "报告周期结束日期")
    private Date periodEnd;

    /**
     * 总结内容
     */
    @ExcelProperty(value = "总结内容")
    private String summary;

    /**
     * 存在问题
     */
    @ExcelProperty(value = "存在问题")
    private String issues;

    /**
     * 需关注重点
     */
    @ExcelProperty(value = "需关注重点")
    private String focusPoints;

    /**
     * 状态:1-草稿,2-已发布
     */
    @ExcelProperty(value = "状态")
    private Integer status;
    /**
     * 学生姓名
     */
    @ExcelProperty(value = "学生姓名")
    private String studentName;

    /**
     * 顾问姓名
     */
    @ExcelProperty(value = "顾问姓名")
    private String consultantName;
    /**
     * 反馈状态:0-待反馈,1-已反馈
     */
    @ExcelProperty(value = "反馈状态")
    private Integer feedbackStatus;

    /**
     * 删除标记:0-未删除,1-已删除
     */
    @ExcelProperty(value = "删除标记")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 关联的学习规划记录列表
     */
    private List<StudyPlanningRecordVo> planningRecords;

    /**
     * 学习资料列表（完整信息）
     */
    private List<RemoteKnowledgeResourceVo> studyMaterials;

    /**
     * 学习资料URL列表（用于前端预览链接）
     */
    private List<String> materialUrls;
}
