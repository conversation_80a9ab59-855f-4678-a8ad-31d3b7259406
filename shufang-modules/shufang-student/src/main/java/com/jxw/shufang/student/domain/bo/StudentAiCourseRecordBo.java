package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentAiCourseRecord;

import java.util.List;

/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）业务对象 student_ai_course_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentAiCourseRecord.class, reverseConvertGenerate = false)
public class StudentAiCourseRecordBo extends BaseEntity {

    /**
     * 会员AI课程分配记录表id
     */
    @NotNull(message = "会员AI课程分配记录表id不能为空", groups = { EditGroup.class })
    private Long studentAiCourseRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;


    /**
     * 带有系统用户信息,默认不查
     */
    private Boolean withSysUserInfo;


    private List<Long> courseIdList;
}
