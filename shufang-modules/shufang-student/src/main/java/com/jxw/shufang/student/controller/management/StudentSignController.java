package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentSignBo;
import com.jxw.shufang.student.domain.vo.StudentSignVo;
import com.jxw.shufang.student.service.IStudentSignService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员标签
 * 前端访问路由地址为:/student/sign
 *
 *
 * @date 2024-03-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/sign")
public class StudentSignController extends BaseController {

    private final IStudentSignService studentSignService;

    /**
     * 查询会员标签列表
     */
    @SaCheckPermission("student:sign:list")
    @GetMapping("/list")
    public TableDataInfo<StudentSignVo> list(StudentSignBo bo, PageQuery pageQuery) {
        return studentSignService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询会员标签列表
     */
    @SaCheckPermission("student:sign:list")
    @GetMapping("/listAll")
    public R<List<StudentSignVo>> listAll(Long studentId) {
        StudentSignBo studentSignBo = new StudentSignBo();
        studentSignBo.setStudentId(studentId);
        return R.ok(studentSignService.queryList(studentSignBo));
    }

    /**
     * 导出会员标签列表
     */
    @SaCheckPermission("student:sign:export")
    @Log(title = "会员标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentSignBo bo, HttpServletResponse response) {
        List<StudentSignVo> list = studentSignService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员标签", StudentSignVo.class, response);
    }

    /**
     * 获取会员标签详细信息
     *
     * @param studentSignId 主键
     */
    @SaCheckPermission("student:sign:query")
    @GetMapping("/{studentSignId}")
    public R<StudentSignVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentSignId) {
        return R.ok(studentSignService.queryById(studentSignId));
    }

    /**
     * 新增会员标签
     */
    @SaCheckPermission("student:sign:add")
    @Log(title = "会员标签", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody StudentSignBo bo) {
        Boolean b = studentSignService.insertByBo(bo);
        if (b) {
            return R.ok(bo.getStudentSignId());
        }else {
            return R.fail("新增失败");
        }
    }

    /**
     * 修改会员标签
     */
    @SaCheckPermission("student:sign:edit")
    @Log(title = "会员标签", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentSignBo bo) {
        return toAjax(studentSignService.updateByBo(bo));
    }

    /**
     * 删除会员标签
     *
     * @param studentSignIds 主键串
     */
    @SaCheckPermission("student:sign:remove")
    @Log(title = "会员标签", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentSignIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentSignIds) {
        return toAjax(studentSignService.deleteWithValidByIds(List.of(studentSignIds), true));
    }
}
