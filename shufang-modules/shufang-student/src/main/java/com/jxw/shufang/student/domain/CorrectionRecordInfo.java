package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 批改记录详情对象 correction_record_info
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("correction_record_info")
public class CorrectionRecordInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批改记录详情id
     */
    @TableId(value = "correction_record_info_id")
    private Long correctionRecordInfoId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 批改记录id
     */
    private Long correctionRecordId;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对、全错、半错）
     */
    private String answerResult;


}
