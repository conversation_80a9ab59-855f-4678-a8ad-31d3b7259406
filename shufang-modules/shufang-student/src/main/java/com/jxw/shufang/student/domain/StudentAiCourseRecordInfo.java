package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 记录分配课程对象 student_ai_course_record_info
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_ai_course_record_info")
public class StudentAiCourseRecordInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录表分配课程id
     */
    @TableId(value = "student_ai_course_record_info_id")
    private Long studentAiCourseRecordInfoId;

    /**
     * 会员AI课程分配记录表id
     */
    private Long studentAiCourseRecordId;

    /**
     * 课程id
     */
    private Long courseId;


}
