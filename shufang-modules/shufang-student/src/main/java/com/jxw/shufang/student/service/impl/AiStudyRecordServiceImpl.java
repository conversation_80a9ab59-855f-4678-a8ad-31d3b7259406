package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordBo;
import com.jxw.shufang.student.domain.bo.AiStudyRecordBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordVo;
import com.jxw.shufang.student.domain.vo.AiStudyRecordVo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.mapper.AiStudyRecordMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ai学习记录Service业务层处理
 *
 *
 * @date 2024-05-21
 */
@RequiredArgsConstructor
@Service
public class AiStudyRecordServiceImpl implements IAiStudyRecordService, BaseService {

    private final AiStudyRecordMapper baseMapper;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentService studentService;

    private final ICourseService courseService;

    private final IAiCorrectionRecordService aiCorrectionRecordService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询ai学习记录
     */
    @Override
    public AiStudyRecordVo queryById(Long aiStudyRecordId) {
        return baseMapper.selectVoById(aiStudyRecordId);
    }

    /**
     * 查询ai学习记录列表
     */
    @Override
    public TableDataInfo<AiStudyRecordVo> queryPageList(AiStudyRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiStudyRecord> lqw = buildLambdaQueryWrapper(bo);
        Page<AiStudyRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai学习记录列表
     */
    @Override
    public List<AiStudyRecordVo> queryList(AiStudyRecordBo bo) {
        LambdaQueryWrapper<AiStudyRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiStudyRecord> buildLambdaQueryWrapper(AiStudyRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiStudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AiStudyRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, AiStudyRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudyVideoTotalDuration() != null, AiStudyRecord::getStudyVideoTotalDuration, bo.getStudyVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getPracticeState()), AiStudyRecord::getPracticeState, bo.getPracticeState());
        lqw.eq(bo.getPracticeRightNum() != null, AiStudyRecord::getPracticeRightNum, bo.getPracticeRightNum());
        lqw.eq(bo.getPracticeWrongNum() != null, AiStudyRecord::getPracticeWrongNum, bo.getPracticeWrongNum());
        lqw.eq(bo.getPracticeUnansweredNum() != null, AiStudyRecord::getPracticeUnansweredNum, bo.getPracticeUnansweredNum());
        lqw.eq(bo.getPracticeVideoTotalDuration() != null, AiStudyRecord::getPracticeVideoTotalDuration, bo.getPracticeVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getTestState()), AiStudyRecord::getTestState, bo.getTestState());
        lqw.eq(bo.getTestRightNum() != null, AiStudyRecord::getTestRightNum, bo.getTestRightNum());
        lqw.eq(bo.getTestWrongNum() != null, AiStudyRecord::getTestWrongNum, bo.getTestWrongNum());
        lqw.eq(bo.getTestUnansweredNum() != null, AiStudyRecord::getTestUnansweredNum, bo.getTestUnansweredNum());
        lqw.eq(bo.getTestVideoTotalDuration() != null, AiStudyRecord::getTestVideoTotalDuration, bo.getTestVideoTotalDuration());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), AiStudyRecord::getCourseId, bo.getCourseIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), AiStudyRecord::getStudentId, bo.getStudentIdList());
        return lqw;
    }

    private QueryWrapper<AiStudyRecord> buildQueryWrapper(AiStudyRecordBo bo) {
        QueryWrapper<AiStudyRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(bo.getStudyVideoTotalDuration() != null, "t.study_video_total_duration", bo.getStudyVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getPracticeState()), "t.practice_state", bo.getPracticeState());
        lqw.eq(bo.getPracticeRightNum() != null, "t.practice_right_num", bo.getPracticeRightNum());
        lqw.eq(bo.getPracticeWrongNum() != null, "t.practice_wrong_num", bo.getPracticeWrongNum());
        lqw.eq(bo.getPracticeUnansweredNum() != null, "t.practice_unanswered_num", bo.getPracticeUnansweredNum());
        lqw.eq(bo.getPracticeVideoTotalDuration() != null, "t.practice_video_total_duration", bo.getPracticeVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getTestState()), "t.test_state", bo.getTestState());
        lqw.eq(bo.getTestRightNum() != null, "t.test_right_num", bo.getTestRightNum());
        lqw.eq(bo.getTestWrongNum() != null, "t.test_wrong_num", bo.getTestWrongNum());
        lqw.eq(bo.getTestUnansweredNum() != null, "t.test_unanswered_num", bo.getTestUnansweredNum());
        lqw.eq(bo.getTestVideoTotalDuration() != null, "t.test_video_total_duration", bo.getTestVideoTotalDuration());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), "t.course_id", bo.getCourseIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        return lqw;
    }

    /**
     * 新增ai学习记录
     */
    @Override
    public Boolean insertByBo(AiStudyRecordBo bo) {
        AiStudyRecord add = MapstructUtils.convert(bo, AiStudyRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiStudyRecordId(add.getAiStudyRecordId());
        }
        return flag;
    }

    /**
     * 修改ai学习记录
     */
    @Override
    public Boolean updateByBo(AiStudyRecordBo bo) {
        AiStudyRecord update = MapstructUtils.convert(bo, AiStudyRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiStudyRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ai学习记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public AiStudyRecordVo queryOnce(AiStudyRecordBo aiStudyRecordBo) {
        LambdaQueryWrapper<AiStudyRecord> lqw = buildLambdaQueryWrapper(aiStudyRecordBo);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public AiStudyRecordVo queryByStudentIdAndCourseId(Long studentId, Long courseId) {
        LambdaQueryWrapper<AiStudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(AiStudyRecord::getStudentId, studentId);
        lqw.eq(AiStudyRecord::getCourseId, courseId);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public TableDataInfo<AiStudyRecordVo> queryStudyRecordPage(AiStudyRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<AiStudyRecord> lqw = buildQueryWrapper(bo);
        Page<AiStudyRecordVo> result = baseMapper.selectStudyRecordPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        //拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result.getRecords()) && Boolean.TRUE.equals(bo.getWithTopmostCourseInfo())) {
            List<CourseVo> courseVos = result.getRecords().stream().map(AiStudyRecordVo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE.equals(bo.getWithCourseDetail()));
            if (Boolean.TRUE.equals(bo.getWithCourseDetail())) {
                List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
                courseService.putCourseDetail(list, Boolean.FALSE);
            }
        }
        if (Boolean.TRUE.equals(bo.getWithAiTestCorrectionRecord()) || Boolean.TRUE.equals(bo.getWithAiPracticeCorrectionRecord())) {
            putAiCorrectionRecordInfo(result.getRecords(), bo.getWithAiTestCorrectionRecord(), bo.getWithAiPracticeCorrectionRecord());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    @Override
    public List<AiStudyRecord> batchQueryByStudentIdAndTestPaperId(List<Long> studentIds, List<Long> testPaperId) {
        if(CollUtil.isEmpty(studentIds) || CollUtil.isEmpty(testPaperId)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AiStudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(AiStudyRecord::getStudentId, studentIds);
        lqw.in(AiStudyRecord::getTestPaperId, testPaperId);
        return baseMapper.selectList(lqw);
    }

    @Override
    public List<AiStudyRecord> batchQueryByStudentIdAndTestCourseId(List<Long> studentIds, List<Long> courseIds) {
        if(CollUtil.isEmpty(studentIds) || CollUtil.isEmpty(courseIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AiStudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(AiStudyRecord::getStudentId, studentIds);
        lqw.in(AiStudyRecord::getCourseId, courseIds);
        return baseMapper.selectList(lqw);
    }

    @Override
    public void batchInsert(List<AiStudyRecord> studyProcessorInserts) {
        if(CollectionUtil.isEmpty(studyProcessorInserts)){
            return;
        }
        studyProcessorInserts.forEach(baseMapper::insert);
    }

    @Override
    public void batchUpdate(List<AiStudyRecord> studyProcessorUpdates) {
        if(CollectionUtil.isEmpty(studyProcessorUpdates)){
            return;
        }
        studyProcessorUpdates.forEach(baseMapper::updateById);
    }

    @Override
    public AiStudyRecord selectStudyAitestRecordByStudentCourse(Long studentId, Long courseId) {
        List<AiStudyRecord> aiStudyRecords = batchQueryByStudentIdAndTestCourseId(Collections.singletonList(studentId), Collections.singletonList(courseId));
        if (CollUtil.isEmpty(aiStudyRecords)) {
            return null;
        }
        return aiStudyRecords.get(0);
    }

    @Override
    public void updateBatchById(List<AiStudyRecord> updated) {
        baseMapper.updateBatchById(updated);
    }


    private void putAiCorrectionRecordInfo(List<AiStudyRecordVo> records, Boolean withAiTestCorrectionRecord, Boolean withAiPracticeCorrectionRecord) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<Long> courseIdList = records.stream().map(AiStudyRecordVo::getCourseId).filter(Objects::nonNull).distinct().toList();
        AiCorrectionRecordBo aiCorrectionRecordBo = new AiCorrectionRecordBo();
        aiCorrectionRecordBo.setCourseIdList(courseIdList);
        if (Boolean.TRUE.equals(withAiTestCorrectionRecord) && Boolean.TRUE.equals(withAiPracticeCorrectionRecord)) {
            aiCorrectionRecordBo.setCorrectionTypeList(List.of(UserConstants.CORRECTION_TYPE_PRACTICE, UserConstants.CORRECTION_TYPE_TEST));
        } else if (Boolean.TRUE.equals(withAiTestCorrectionRecord)) {
            aiCorrectionRecordBo.setCorrectionType(UserConstants.CORRECTION_TYPE_TEST);
        } else if (Boolean.TRUE.equals(withAiPracticeCorrectionRecord)) {
            aiCorrectionRecordBo.setCorrectionType(UserConstants.CORRECTION_TYPE_PRACTICE);
        }
        List<AiCorrectionRecordVo> aiCorrectionRecordVos = aiCorrectionRecordService.queryRecordAndRightWrongInfo(aiCorrectionRecordBo);
        if (CollUtil.isEmpty(aiCorrectionRecordVos)) {
            return;
        }
        //按照courseId和studentId分组
        Map<Long, Map<Long, List<AiCorrectionRecordVo>>> map = StreamUtils.groupBy2Key(aiCorrectionRecordVos, AiCorrectionRecordVo::getCourseId, AiCorrectionRecordVo::getStudentId);
        records.forEach(record -> {
            Long courseId = record.getCourseId();
            Long studentId = record.getStudentId();
            Map<Long, List<AiCorrectionRecordVo>> courseMap = map.get(courseId);
            if (courseMap == null) {
                return;
            }
            List<AiCorrectionRecordVo> correctionRecordVoList = courseMap.get(studentId);

            if (CollUtil.isEmpty(correctionRecordVoList)) {
                return;
            }
            for (AiCorrectionRecordVo aiCorrectionRecordVo : correctionRecordVoList) {
                if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(aiCorrectionRecordVo.getCorrectionType())) {
                    record.setAiPracticeCorrectionRecord(aiCorrectionRecordVo);
                } else if (UserConstants.CORRECTION_TYPE_TEST.equals(aiCorrectionRecordVo.getCorrectionType())) {
                    record.setAiTestCorrectionRecord(aiCorrectionRecordVo);
                }
            }
        });
    }

    private void putStudentSysUserInfo(List<AiStudyRecordVo> vos) {
        if (CollUtil.isEmpty(vos)) {
            return;
        }
        List<StudentVo> studentVos = vos.stream().map(AiStudyRecordVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(true);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    private void putConsultantInfo(List<AiStudyRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(AiStudyRecordVo::getStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(studentIdList);
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return;
        }
        Collection<Long> studentConsultantIdList = studentConsultantIdMap.values();
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(new ArrayList<>(studentConsultantIdList));
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        records.forEach(record -> {
            Long studentId = record.getStudentId();
            Long consultantId = studentConsultantIdMap.get(studentId);
            if (consultantId == null) {
                return;
            }
            RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(consultantId);
            if (remoteStaffVo == null) {
                return;
            }
            record.setConsultantInfo(remoteStaffVo);
        });

    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void handleQueryParam(AiStudyRecordBo record) {
        if (record.getStudentId() != null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }

        }
    }

}
