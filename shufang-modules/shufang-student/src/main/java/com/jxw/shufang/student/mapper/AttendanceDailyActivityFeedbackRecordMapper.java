package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityFeedbackRecordVo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 会员每日学习反馈记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 03:26:09
 */
@Mapper
public interface AttendanceDailyActivityFeedbackRecordMapper  extends BaseMapperPlus<AttendanceDailyActivityFeedbackRecord, AttendanceDailyActivityFeedbackRecordVo> {

}
