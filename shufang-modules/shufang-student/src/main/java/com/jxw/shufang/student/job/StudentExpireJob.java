package com.jxw.shufang.student.job;

import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentExpireBo;
import com.jxw.shufang.student.domain.vo.StudentExpireVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IStudentExpireService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class StudentExpireJob {

    private final IStudentExpireService studentExpireService;

    private final IStudentService studentService;
    @DubboReference
    private RemoteConfigService remoteConfigService;


    //过期指：有订单的会员，他所有订单里面的产品的有效期最大的那个订单的有效期已经过期，再加上过期迁移时限 大于等于 当前时间，及过期
    @XxlJob("studentExpireJob")
    public ReturnT<String> execute(String param) {
        log.info("过期会员扫描任务执行开始。");
        Date now = new Date();
        // 查询过期迁移时限
        Integer selectStudentExpireTime = remoteConfigService.selectStudentExpireTime();
        // 减去过期迁移时限，得出再某个时间点之前的会员，记为过期会员
        Date expireTime = DateUtils.addDays(now, -selectStudentExpireTime);
        // 先查过期表中的会员，不再去二次查，提高效率
        List<StudentExpireVo> studentExpireVoList =
            DataPermissionHelper.ignore(() -> studentExpireService.queryList(new StudentExpireBo()));
        List<Long> studentIdList = studentExpireVoList.stream().map(StudentExpireVo::getStudentId).toList();
        // 找出过期的
        StudentBo studentBo = new StudentBo();
        studentBo.setNotInStudentIds(studentIdList);
        studentBo.setLessThanExpireTime(expireTime);
        studentBo.setStudentStatus(UserConstants.USER_NORMAL);
        List<StudentVo> studentVos = DataPermissionHelper.ignore(() -> studentService.queryList(studentBo));
        if (CollUtil.isEmpty(studentVos)) {
            log.info("过期会员扫描任务执行完成，当前没有发现新的过期会员。");
            return ReturnT.SUCCESS;
        }
        int size = studentVos.size();
        log.info("过期会员数量：{}", size);
        // 批量插入过期会员
        List<StudentExpireBo> insertList = new ArrayList<>();
        for (StudentVo studentVo : studentVos) {
            StudentExpireBo studentExpireBo = new StudentExpireBo();
            studentExpireBo.setStudentId(studentVo.getStudentId());
            studentExpireBo.setIsShow(UserConstants.DEL_FLAG_NO);
            studentExpireBo.setCreateBy(studentVo.getCreateBy());
            studentExpireBo.setCreateDept(studentVo.getCreateDept());
            insertList.add(studentExpireBo);
        }
        Boolean b = studentExpireService.insertBatchByBo(insertList);
        if (!b) {
            log.info("过期会员扫描任务执行处理失败，插入过期会员记录失败。");
            return ReturnT.FAIL;
        } else {
            log.info("过期会员扫描任务执行完成，当前操作的会员ID：" + studentVos.stream().map(StudentVo::getStudentId).toList());
            return ReturnT.SUCCESS;
        }
    }
}
