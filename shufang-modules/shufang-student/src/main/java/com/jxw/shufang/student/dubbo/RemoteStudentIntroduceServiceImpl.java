package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.student.api.RemoteStudentIntroduceService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.service.IStudentIntroduceRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentIntroduceServiceImpl implements RemoteStudentIntroduceService {

    private final IStudentIntroduceRecordService studentIntroduceRecordService;

    @Override
    public Boolean remoteDealWithOrderSuccess(Long studentId, Long orderId, List<RemoteProductBo> productBo,
        Long operator, Long operateDept) {
        return studentIntroduceRecordService.remoteDealWithOrderSuccess(studentId, orderId, productBo, operator,
            operateDept);
    }

    @Override
    public Boolean remoteDealWithOrderRefund(Long studentId, Long orderId) {
        return studentIntroduceRecordService.remoteDealWithOrderRefund(studentId,orderId);
    }
}
