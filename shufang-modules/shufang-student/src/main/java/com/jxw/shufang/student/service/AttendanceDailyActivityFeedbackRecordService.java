package com.jxw.shufang.student.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityFeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityFeedbackRecordVo;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 会员每日学习反馈记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 03:26:09
 */
public interface AttendanceDailyActivityFeedbackRecordService extends IService<AttendanceDailyActivityFeedbackRecord> {

    /**
     * 根据反馈日期计算反馈状态
     * @param feedbackDate 用于计算的日期
     * @param isFlag true计算是否超时反馈状态，false计算是否超时未反馈
     * @return  1:未反馈 2:已反馈 3:超时反馈 4:超时未反馈
     *
     */
    Integer calculateFeedbackStatus(Date feedbackDate,Boolean isFlag);


    /**
     * 保存每日学习反馈记录
     */
    void saveAttendanceDailyActivityFeedbackRecord(AttendanceDailyActivityFeedbackRecordBo record);

    /**
     * 修改会员每日学习反馈记录表
     * @param bo
     */
    void updateByAttendanceDailyActivityFeedbackRecord(AttendanceDailyActivityFeedbackRecordBo bo);

    AttendanceDailyActivityFeedbackRecordVo queryByAttendanceDailyActivityId(Long attendanceDailyActivityId);

    AttendanceDailyActivityFeedbackRecordVo queryByFeedbackRecordId(Long feedbackRecordId);

    void saveBatchAttendanceDailyActivityFeedbackRecord(List<AttendanceDailyActivityFeedbackRecordBo> feedbackList);
}
