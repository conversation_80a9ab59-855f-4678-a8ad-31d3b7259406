package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.student.service.IRemoteMessageService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;
import com.jxw.shufang.student.service.IApplyCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 申请批改记录--平板端
 * 前端访问路由地址为:/student/android/applyCorrectionRecord
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/applyCorrectionRecord")
public class AApplyCorrectionRecordController extends BaseController {

    private final IApplyCorrectionRecordService applyCorrectionRecordService;
    private final IRemoteMessageService remoteMessageService;

    /**
     * 查询学习规划自主审批申请状态,如果为空就代表没有申请记录,1=允许,2=拒绝,0待审核
     *
     * @param applyType             申请批改类型 1练习  2测试
     * @param studyPlanningRecordId 学习计划记录id
     * @date 2024/05/08 03:26:20
     */
    @GetMapping("/applyResult")
    public R<String> applyResult(@NotBlank(message = "申请类型不能为空") String applyType, @NotNull(message = "学习计划记录id不能为空") Long studyPlanningRecordId) {
        Long studentId = LoginHelper.getStudentId();
        ApplyCorrectionRecordBo applyCorrectionRecordBo = new ApplyCorrectionRecordBo();
        applyCorrectionRecordBo.setStudentId(studentId);
        applyCorrectionRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        applyCorrectionRecordBo.setApplyType(applyType);
        ApplyCorrectionRecordVo applyCorrectionRecordVo = applyCorrectionRecordService.queryLastRecord(applyCorrectionRecordBo);
        return R.ok("查询成功",
            applyCorrectionRecordVo != null
                && applyCorrectionRecordService.wheatherApplyRecordVisiable(applyCorrectionRecordVo)
                    ? applyCorrectionRecordVo.getApplyResult() : null);
    }

    /**
     * 提交自主审批
     */
    @PostMapping()
    @Log(title = "申请批改--平板端", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    public R<Void> submit(@Validated(AddGroup.class) @RequestBody ApplyCorrectionRecordBo bo) {
        Long studentId = LoginHelper.getStudentId();
        bo.setStudentId(studentId);
        return toAjax(applyCorrectionRecordService.insertByBo(bo));
    }


}
