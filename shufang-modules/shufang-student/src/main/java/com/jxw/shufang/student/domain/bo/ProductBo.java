package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Product;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品（会员卡）业务对象 product
 *
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Product.class, reverseConvertGenerate = false)
public class ProductBo extends BaseEntity {

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空", groups = { EditGroup.class })
    private Long productId;

    /**
     * 会员类型id
     */
    @NotNull(message = "会员类型id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentTypeId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 产品有效天数
     */
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    @NotNull(message = "产品价格不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.01", message = "金额不能小于0.01", groups = { AddGroup.class, EditGroup.class })
    @DecimalMax(value = "999999999.99", message = "金额不能大于999999999.99", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal productPrice;

    /**
     * 产品状态（0上架 1下架）
     */
    private String productStatus;

    /**
     * 产品有效时限开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productValidTimeLimitStart;

    /**
     * 产品有效期限结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date productValidTimeLimitEnd;

    /**
     * 产品id列表
     */
    private List<Long> productIdList;

    /**
     * 会员类型id列表
     */
    private List<Long> studentTypeIdList;

    /*
     * 非会员类型id
     */
    private Long neStudentTypeId;

    /**
     * 是否是正式卡下的产品
     */
    private Boolean isOfficialCard;
    /**
     * 是否是区间卡
     */
    private Boolean isPeriod;

    /**
     * 部门id列表
     */
    private List<Long> deptIdList;

}
