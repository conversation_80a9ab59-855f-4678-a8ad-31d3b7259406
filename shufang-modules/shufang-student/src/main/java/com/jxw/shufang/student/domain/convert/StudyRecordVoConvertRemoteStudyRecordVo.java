package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyRecordVo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StudyRecordVoConvertRemoteStudyRecordVo extends BaseMapper<StudyRecordVo, RemoteStudyRecordVo> {

}
