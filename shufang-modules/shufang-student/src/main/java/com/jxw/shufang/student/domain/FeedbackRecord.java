package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 反馈记录对象 feedback_record
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback_record")
public class FeedbackRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈记录id
     */
    @TableId(value = "feedback_record_id")
    private Long feedbackRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 反馈内容
     */
    private String feedbackContent;

    /**
     * 反馈日期范围-开始
     */
    private Date feedbackStartDate;

    /**
     * 反馈日期范围-结束
     */
    private Date feedbackEndDate;

    /**
     * 反馈图片（oss_id，多个，逗号隔开）
     */
    private String feedbackImgs;

    /**
     * 表现分数（满分为5分，用星进行展示）
     */
    private Long showScore;

    /**
     * 发布状态 1暂存 2已发布
     */
    private String feedbackStatus;

    /**
     * 反馈提交状态 1.未反馈 2.已反馈 3.超时反馈 4.超时未反馈
     */
    private Integer feedbackSubmitStatus;

    /**
     * 1已通知 2未通知(默认) 3通知但失败
     */
    private String parentNotificationStatus;

    /**
     * 最近的一次通知时间
     */
    private Date notificationTime;

    /**
     * 最近一次通知的openid
     */
    private String notificationOpenid;

    /**
     * 家长是否已读 1 未读 2已读
     */
    private String parentReadStatus;


}
