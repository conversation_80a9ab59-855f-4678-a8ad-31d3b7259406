package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudyPlanningTemplateInfo;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateInfoBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateInfoVo;
import com.jxw.shufang.student.mapper.StudyPlanningTemplateInfoMapper;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.IStudyPlanningTemplateInfoService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习规划模板详情Service业务层处理
 *
 *
 * @date 2024-06-14
 */
@RequiredArgsConstructor
@Service
public class StudyPlanningTemplateInfoServiceImpl implements IStudyPlanningTemplateInfoService, BaseService {

    private final StudyPlanningTemplateInfoMapper baseMapper;

    private final ICourseService courseService;

    @DubboReference
    private RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    /**
     * 查询学习规划模板详情
     */
    @Override
    public StudyPlanningTemplateInfoVo queryById(Long studyPlanningTemplateInfoId){
        return baseMapper.selectVoById(studyPlanningTemplateInfoId);
    }

    /**
     * 查询学习规划模板详情列表
     */
    @Override
    public TableDataInfo<StudyPlanningTemplateInfoVo> queryPageList(StudyPlanningTemplateInfoBo bo, PageQuery pageQuery) {
        bo.setTopmostCourseIdList(getAuthCourseIdList());
        LambdaQueryWrapper<StudyPlanningTemplateInfo> lqw = buildQueryWrapper(bo);
        Page<StudyPlanningTemplateInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithCourseInfo())){
            putCourseInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithTopmostCourseInfo())){
            putTopmostCourseInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void putTopmostCourseInfo(List<StudyPlanningTemplateInfoVo> records) {
        if (CollUtil.isEmpty(records)){
            return;
        }
        Set<Long> set = StreamUtils.toSet(records, StudyPlanningTemplateInfoVo::getTopmostCourseId);
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(new ArrayList<>(set));
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)){
            return;
        }
        courseService.putCourseWithAttrInfo(courseVos);
        courseService.putCourseDetail(courseVos,false);

        Map<Long, CourseVo> courseVoMap = StreamUtils.toIdentityMap(courseVos, CourseVo::getCourseId);
        for (StudyPlanningTemplateInfoVo record : records) {
            record.setTopmostCourse(courseVoMap.get(record.getTopmostCourseId()));
        }

    }

    private void putCourseInfo(List<StudyPlanningTemplateInfoVo> records) {
        if (CollUtil.isEmpty(records)){
            return;
        }
        Set<Long> set = StreamUtils.toSet(records, StudyPlanningTemplateInfoVo::getCourseId);
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(new ArrayList<>(set));
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)){
            return;
        }
        Map<Long, CourseVo> courseVoMap = StreamUtils.toIdentityMap(courseVos, CourseVo::getCourseId);
        for (StudyPlanningTemplateInfoVo record : records) {
            record.setCourse(courseVoMap.get(record.getCourseId()));
        }
    }

    /**
     * 查询学习规划模板详情列表
     */
    @Override
    public List<StudyPlanningTemplateInfoVo> queryList(StudyPlanningTemplateInfoBo bo) {
        LambdaQueryWrapper<StudyPlanningTemplateInfo> lqw = buildQueryWrapper(bo);
        List<StudyPlanningTemplateInfoVo> studyPlanningTemplateInfoVos = baseMapper.selectVoList(lqw);
        if (Boolean.TRUE.equals(bo.getWithCourseInfo())){
            putCourseInfo(studyPlanningTemplateInfoVos);
        }
        if (Boolean.TRUE.equals(bo.getWithTopmostCourseInfo())){
            putTopmostCourseInfo(studyPlanningTemplateInfoVos);
        }
        return studyPlanningTemplateInfoVos;
    }

    private LambdaQueryWrapper<StudyPlanningTemplateInfo> buildQueryWrapper(StudyPlanningTemplateInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyPlanningTemplateInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudyPlanningTemplateId() != null, StudyPlanningTemplateInfo::getStudyPlanningTemplateId, bo.getStudyPlanningTemplateId());
        lqw.eq(bo.getCourseId() != null, StudyPlanningTemplateInfo::getCourseId, bo.getCourseId());
        lqw.eq(bo.getTopmostCourseId() != null, StudyPlanningTemplateInfo::getTopmostCourseId, bo.getTopmostCourseId());
        lqw.eq(bo.getStudyStartTime() != null, StudyPlanningTemplateInfo::getStudyStartTime, bo.getStudyStartTime());
        lqw.eq(bo.getStudyEndTime() != null, StudyPlanningTemplateInfo::getStudyEndTime, bo.getStudyEndTime());
        return lqw;
    }

    /**
     * 新增学习规划模板详情
     */
    @Override
    public Boolean insertByBo(StudyPlanningTemplateInfoBo bo) {
        StudyPlanningTemplateInfo add = MapstructUtils.convert(bo, StudyPlanningTemplateInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudyPlanningTemplateInfoId(add.getStudyPlanningTemplateInfoId());
        }
        return flag;
    }

    /**
     * 修改学习规划模板详情
     */
    @Override
    public Boolean updateByBo(StudyPlanningTemplateInfoBo bo) {
        StudyPlanningTemplateInfo update = MapstructUtils.convert(bo, StudyPlanningTemplateInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyPlanningTemplateInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学习规划模板详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public boolean insertBatchByBo(List<StudyPlanningTemplateInfoBo> studyPlanningTemplateInfoBoList) {
        List<StudyPlanningTemplateInfo> studyPlanningTemplateInfoList = MapstructUtils.convert(studyPlanningTemplateInfoBoList, StudyPlanningTemplateInfo.class);
        return baseMapper.insertBatch(studyPlanningTemplateInfoList);
    }

    @Override
    public boolean deleteByStudyPlanningTemplateId(Long studyPlanningTemplateId) {
        LambdaQueryWrapper<StudyPlanningTemplateInfo> query = Wrappers.lambdaQuery(StudyPlanningTemplateInfo.class);
        query.eq(StudyPlanningTemplateInfo::getStudyPlanningTemplateId, studyPlanningTemplateId);
        return baseMapper.delete(query) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private List<Long> getAuthCourseIdList(){
        //如果是门店，查该门店下全部
        if (null != LoginHelper.getBranchId() || CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<RemoteBranchAuthTypeVo> remoteBranchAuthTypeVos = new ArrayList<>();
            if (null != LoginHelper.getBranchId()) {
                RemoteBranchAuthTypeVo authTypeByBranchId = remoteBranchAuthTypeService.getAuthTypeByBranchId(LoginHelper.getBranchId());
                if (authTypeByBranchId != null) {
                    remoteBranchAuthTypeVos.add(authTypeByBranchId);
                }
            } else {
                List<RemoteBranchAuthTypeVo> authTypeListByBranchIdList = remoteBranchAuthTypeService.getAuthTypeListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isNotEmpty(authTypeListByBranchIdList)) {
                    remoteBranchAuthTypeVos.addAll(authTypeListByBranchIdList);
                }
            }
            if (CollUtil.isNotEmpty(remoteBranchAuthTypeVos)) {
                List<Long> allCourseIdList = remoteBranchAuthTypeVos.stream()
                    .map(RemoteBranchAuthTypeVo::getCourseIds)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(s -> Arrays.stream(s.split(",")))
                    .map(Long::parseLong)
                    .distinct()
                    .collect(Collectors.toList());

                return allCourseIdList;
            }

        }
        return List.of();
    }

}
