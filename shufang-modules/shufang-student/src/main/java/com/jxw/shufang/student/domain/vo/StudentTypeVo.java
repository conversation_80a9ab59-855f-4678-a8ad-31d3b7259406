package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.StudentType;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）视图对象 student_type
 *
 *
 * @date 2024-03-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentType.class)
public class StudentTypeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员类型id
     */
    private Long studentTypeId;

    /**
     * 会员类型名称
     */
    @ExcelProperty(value = "会员类型名称")
    private String studentTypeName;


    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Long studentTypeSort;

    /**
     * 是否是默认的类型
     */
    private Boolean isDefault;


    /**
     * 产品列表
     */
    private List<ProductVo> productList;

}
