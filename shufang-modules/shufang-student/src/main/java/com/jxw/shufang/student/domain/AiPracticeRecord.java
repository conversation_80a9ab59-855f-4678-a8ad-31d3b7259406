package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * Ai练习记录对象 ai_practice_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_practice_record")
public class AiPracticeRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 练习记录id
     */
    @TableId(value = "ai_practice_record_id")
    private Long aiPracticeRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 课程资源id
     */
    private Long courseResourceId;

    /**
     * 资源内容（oss_id）
     */
    private Long resourceContent;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对 全错 半错）
     */
    private String answerResult;

    /**
     * 作答图片（oss_id）
     */
    private Long answerImg;


}
