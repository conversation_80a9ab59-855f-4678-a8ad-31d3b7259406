package com.jxw.shufang.student.service;


import com.jxw.shufang.student.domain.bo.StudentAnswerRecordBo;
import com.jxw.shufang.student.domain.vo.StudentAnswerRecordVo;

import java.util.List;

public interface IStudentAnswerRecordService {

    StudentAnswerRecordVo queryById(Long studentAnswerRecordId);

    List<StudentAnswerRecordVo> queryList(StudentAnswerRecordBo bo);

    List<StudentAnswerRecordVo> queryListByStudentPaperRecordId(Long studentPaperRecordId);

    Boolean insertByBo(StudentAnswerRecordBo bo);

    Boolean batchInsertByBo(List<StudentAnswerRecordBo> bo);

    Boolean updateByBo(StudentAnswerRecordBo bo);
}
