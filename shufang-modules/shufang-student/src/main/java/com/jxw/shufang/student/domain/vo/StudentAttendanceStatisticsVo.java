package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


@Data
public class StudentAttendanceStatisticsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 累计打卡天数
     */
    private Integer totalCheckTimes = 0;


    /**
     * 缺少打卡天数
     */
    private Integer missingCheckTimes = 0;

    /**
     * 有学习记录但是没有打卡的天数
     */
    private Integer noCheckButStudyTimes = 0;
}
