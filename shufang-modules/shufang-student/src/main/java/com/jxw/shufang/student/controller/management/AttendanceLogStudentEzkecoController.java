package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo;
import com.jxw.shufang.student.domain.vo.StudentAttendanceStatisticsVo;
import com.jxw.shufang.student.service.IAttendanceLogStudentEzkecoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ezkeco学员考勤记录
 * 前端访问路由地址为:/student/attendanceLogStudentEzkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/attendanceLogStudentEzkeco")
public class AttendanceLogStudentEzkecoController extends BaseController {

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;

    /**
     * 查询ezkeco学员考勤记录列表
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:list")
    @GetMapping("/list")
    public TableDataInfo<AttendanceLogStudentEzkecoVo> list(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery) {
        return attendanceLogStudentEzkecoService.queryPageList(bo, pageQuery);
    }

    ///**
    // * 按日期分组来查询的考勤记录，只取最开始的和最后的一条记录
    // */
    //@SaCheckPermission("student:attendanceLogStudentEzkeco:list")
    //@GetMapping("/group")
    //public TableDataInfo<AttendanceLogStudentEzkecoVo> group(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery) {
    //    return attendanceLogStudentEzkecoService.queryGroupPageList(bo,pageQuery);
    //}

    /**
     * 导出ezkeco学员考勤记录列表
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:export")
    @Log(title = "ezkeco学员考勤记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttendanceLogStudentEzkecoBo bo, HttpServletResponse response) {
        List<AttendanceLogStudentEzkecoVo> list = attendanceLogStudentEzkecoService.queryList(bo);
        ExcelUtil.exportExcel(list, "ezkeco学员考勤记录", AttendanceLogStudentEzkecoVo.class, response);
    }

    /**
     * 获取ezkeco学员考勤记录详细信息
     *
     * @param attendanceLogStudentEzkecoId 主键
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:query")
    @GetMapping("/{attendanceLogStudentEzkecoId}")
    public R<AttendanceLogStudentEzkecoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attendanceLogStudentEzkecoId) {
        return R.ok(attendanceLogStudentEzkecoService.queryById(attendanceLogStudentEzkecoId));
    }

    /**
     * 新增ezkeco学员考勤记录
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:add")
    @Log(title = "ezkeco学员考勤记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttendanceLogStudentEzkecoBo bo) {
        return toAjax(attendanceLogStudentEzkecoService.insertByBo(bo));
    }

    /**
     * 修改ezkeco学员考勤记录
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:edit")
    @Log(title = "ezkeco学员考勤记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttendanceLogStudentEzkecoBo bo) {
        return toAjax(attendanceLogStudentEzkecoService.updateByBo(bo));
    }

    /**
     * 删除ezkeco学员考勤记录
     *
     * @param attendanceLogStudentEzkecoIds 主键串
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:remove")
    @Log(title = "ezkeco学员考勤记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attendanceLogStudentEzkecoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attendanceLogStudentEzkecoIds) {
        return toAjax(attendanceLogStudentEzkecoService.deleteWithValidByIds(List.of(attendanceLogStudentEzkecoIds), true));
    }


    /**
     * 考勤统计数据：累计打卡 x 天，其中 y 天缺少打卡；有 z 天有学习记录，但没有打卡记录
     */
    @SaCheckPermission("student:attendanceLogStudentEzkeco:list")
    @GetMapping("/statistics")
    public R<StudentAttendanceStatisticsVo> statistics(Long studentId) {
        return R.ok(attendanceLogStudentEzkecoService.statistics(studentId));
    }

}
