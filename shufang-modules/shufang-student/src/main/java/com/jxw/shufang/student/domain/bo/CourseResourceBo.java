package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.CourseResource;

/**
 * 课程资源（绑定到课程的资源）业务对象 course_resource
 *
 *
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CourseResource.class, reverseConvertGenerate = false)
public class CourseResourceBo extends BaseEntity {

    /**
     * 课程资源id
     */
    @NotNull(message = "课程资源id不能为空", groups = { EditGroup.class })
    private Long courseResourceId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 资源种类（1学习视频 2课程讲义 3会员练习 4练习解答视频 5会员测验 6测验解答视频 7试卷 8试卷解析 9试卷+解析）
     */
    @NotBlank(message = "资源种类（1学习视频 2课程讲义 3会员练习 4练习解答视频 5会员测验 6测验解答视频 7试卷 8试卷解析 9试卷+解析）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String courseResourceType;

    /**
     * 资源来源（1公共资源 2本地资源）
     */
    @NotBlank(message = "资源来源（1公共资源 2本地资源）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resourceSource;

    /**
     * 外部资料id（公共资源才有此项，可以为表加id与外部资源进行对应）
     */
    @NotBlank(message = "外部资料id（公共资源才有此项，可以为表加id与外部资源进行对应）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String externalInformationId;

    /**
     * 资料id（本地资源才有此项）
     */
    @NotNull(message = "资料id（本地资源才有此项）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long informationId;

    /**
     * 资源内容（资源地址）
     */
    @NotBlank(message = "资源内容（资源地址）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resourceContent;

    /**
     * 资源内容（oss_id）
     */
    @NotNull(message = "资源内容（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long resourceOssId;

    /**
     * 资源名称（可用于重命名，包含文件后缀）
     */
    @NotBlank(message = "资源名称（可用于重命名，包含文件后缀）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String resourceName;

    /**
     * 资源总大小（单位KB）
     */
    @NotNull(message = "资源总大小（单位KB）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long resourceSize;

    /**
     * 资源总时长（单位秒 学习视频才有此项）
     */
    @NotNull(message = "资源总时长（单位秒 学习视频才有此项）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long resourceDuration;

    /**
     * 题目序号（题目解答视频才有此项）
     */
    @NotBlank(message = "题目序号（题目解答视频才有此项）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionNo;

    /**
     * 题目详情（对应试卷中有哪些题型，数量多少）
     */
    @NotBlank(message = "题目详情（对应试卷中有哪些题型，数量多少）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionInfo;

    /**
     * 排序字段 可拼接desc或asc
     */
    private String orderBy;

    /**
     * 是否收藏 true：已收藏 false：未收藏
     */
    private Boolean isCollect;


}
