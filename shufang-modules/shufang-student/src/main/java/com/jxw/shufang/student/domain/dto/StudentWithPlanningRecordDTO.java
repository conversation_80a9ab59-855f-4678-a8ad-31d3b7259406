package com.jxw.shufang.student.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/15 14:44
 * @Version 1
 * @Description
 */
@Data
public class StudentWithPlanningRecordDTO {
    /**
     * 学习规划记录id
     */
    private Long studyPlanningRecordId;

    /**
     * 学习规划ID
     */
    private Long studyPlanningId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 学生账号
     */
    private String studentAccount;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 学习规划日期
     */
    private Date studyPlanningDate;


    /**
     * 开始时间
     */
    private String studyStartTime;

    /**
     * 结束时间
     */
    private String studyEndTime;

    private Date createTime;

    private Integer downloadStatus;
}
