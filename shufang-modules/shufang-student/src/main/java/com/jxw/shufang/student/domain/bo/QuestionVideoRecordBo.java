package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 题目视频记录（题目视频观看记录）业务对象 question_video_record
 *
 *
 * @date 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QuestionVideoRecord.class, reverseConvertGenerate = false)
public class QuestionVideoRecordBo extends BaseEntity {

    /**
     * 题目视频记录id
     */
    @NotNull(message = "题目视频记录id不能为空", groups = { EditGroup.class })
    private Long questionVideoRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 学习规划记录Id
     */
    @NotNull(message = "学习规划记录Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 对应外部资源的视频Id
     */
    @NotNull(message = "对应外部资源的视频Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long videoId;

    /**
     * 题目类型：1测试 2练习
     */
    @NotBlank(message = "题目类型：1测试 2练习不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionType;

    /**
     * 课程Id
     */
    @NotNull(message = "课程Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 题目Id
     */
    @NotNull(message = "题目Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 播放时长（单位秒 按日累加）
     */
    @NotNull(message = "播放时长（单位秒 按日累加）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyVideoDuration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    @NotBlank(message = "分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyVideoSlices;


    /**
     * 题目类型列表：1测试 2练习
     */
    private List<String> questionTypeList;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 创建日期列表
     */
    private List<String> createDateList;


    /**
     * 题目列表
     */
    private List<Long>  questionIdList;

    /**
     * 学习模块类型
     */
    private StudyModuleAndGroupEnum studyModuleType;
}
