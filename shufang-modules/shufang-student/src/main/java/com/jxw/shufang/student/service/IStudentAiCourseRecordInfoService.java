package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordInfoBo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 记录分配课程Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IStudentAiCourseRecordInfoService {

    /**
     * 查询记录分配课程
     */
    StudentAiCourseRecordInfoVo queryById(Long studentAiCourseRecordInfoId);

    /**
     * 查询记录分配课程列表
     */
    TableDataInfo<StudentAiCourseRecordInfoVo> queryPageList(StudentAiCourseRecordInfoBo bo, PageQuery pageQuery);

    /**
     * 查询记录分配课程列表
     */
    List<StudentAiCourseRecordInfoVo> queryList(StudentAiCourseRecordInfoBo bo);

    /**
     * 新增记录分配课程
     */
    Boolean insertByBo(StudentAiCourseRecordInfoBo bo);

    /**
     * 修改记录分配课程
     */
    Boolean updateByBo(StudentAiCourseRecordInfoBo bo);

    /**
     * 校验并批量删除记录分配课程信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
