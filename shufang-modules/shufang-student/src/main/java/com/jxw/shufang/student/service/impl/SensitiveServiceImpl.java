package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.enums.SensitiveGroup;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.Sensitive;
import com.jxw.shufang.student.domain.bo.SensitiveBo;
import com.jxw.shufang.student.domain.vo.SensitiveVo;
import com.jxw.shufang.student.mapper.SensitiveMapper;
import com.jxw.shufang.student.service.ISensitiveService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 敏感词Service业务层处理
 *
 *
 * @date 2024-06-15
 */
@RequiredArgsConstructor
@Service
public class SensitiveServiceImpl implements ISensitiveService, BaseService {

    private final SensitiveMapper baseMapper;

    /**
     * 查询敏感词
     */
    @Override
    public SensitiveVo queryById(Long sensitiveId){
        return baseMapper.selectVoById(sensitiveId);
    }

    /**
     * 查询敏感词列表
     */
    @Override
    public TableDataInfo<SensitiveVo> queryPageList(SensitiveBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Sensitive> lqw = buildQueryWrapper(bo);
        Page<SensitiveVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询敏感词列表
     */
    @Override
    public List<SensitiveVo> queryList(SensitiveBo bo) {
        LambdaQueryWrapper<Sensitive> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Sensitive> buildQueryWrapper(SensitiveBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Sensitive> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSensitiveGroupName()), Sensitive::getSensitiveGroupName, bo.getSensitiveGroupName());
        lqw.eq(StringUtils.isNotBlank(bo.getSensitiveContent()), Sensitive::getSensitiveContent, bo.getSensitiveContent());
        return lqw;
    }

    /**
     * 新增敏感词
     */
    @Override
    public Boolean insertByBo(SensitiveBo bo) {
        Sensitive add = MapstructUtils.convert(bo, Sensitive.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSensitiveId(add.getSensitiveId());
        }
        return flag;
    }

    /**
     * 修改敏感词
     */
    @Override
    public Boolean updateByBo(SensitiveBo bo) {
        Sensitive update = MapstructUtils.convert(bo, Sensitive.class);
        String sensitiveContent = update.getSensitiveContent();
        if (StringUtils.isNotBlank(sensitiveContent)) {
            // 去除敏感词内容中的中文逗号，替换成英文逗号，删除每个词左右的空格
            update.setSensitiveContent(sensitiveContent.replaceAll("，", ",").trim());
        }
        //再检测一遍是否为空
        if (StringUtils.isBlank(bo.getSensitiveGroupName())) {
            throw new ServiceException("敏感词不能为空");
        }
        //查出默认的那条数据 如果查不出来证明数据初始化有误
        List<Sensitive> sensitives = baseMapper.selectList();
        update.setSensitiveId(sensitives.get(0).getSensitiveId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Sensitive entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除敏感词
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Long checkHasSensitive(String content, SensitiveGroup group) {
        LambdaQueryWrapper<Sensitive> queryWrapper = Wrappers.lambdaQuery(Sensitive.class);
        queryWrapper.eq(Sensitive::getSensitiveGroupName, group.toString());
        queryWrapper.last("limit 1");
        Sensitive sensitive = baseMapper.selectOne(queryWrapper);
        if(sensitive==null||StringUtils.isEmpty(sensitive.getSensitiveContent())){
            return null;
        }
        String sensitiveContent = sensitive.getSensitiveContent();
        List<String> split = List.of(sensitiveContent.split(","));
        for (String key : split) {
            if (content.contains(key)) {
                return sensitive.getSensitiveId();
            }
        }
        return null;
    }

    @Override
    public Boolean updateByGroup(SensitiveBo bo) {
        LambdaQueryWrapper<Sensitive> queryWrapper = Wrappers.lambdaQuery(Sensitive.class);
        queryWrapper.eq(Sensitive::getSensitiveGroupName, bo.getSensitiveGroupName());
        Sensitive sensitive = baseMapper.selectOne(queryWrapper);
        if(sensitive==null){
            throw new ServiceException("敏感词组不存在");
        }
        Sensitive update = MapstructUtils.convert(bo, Sensitive.class);
        update.setSensitiveId(sensitive.getSensitiveId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
