package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StuCourseResourceCollect;

/**
 * 会员对课程资源的收藏业务对象 stu_course_resource_collect
 *
 *
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StuCourseResourceCollect.class, reverseConvertGenerate = false)
public class StuCourseResourceCollectBo extends BaseEntity {

    /**
     * 资源收藏id
     */
    @NotNull(message = "资源收藏id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long resourceCollectId;

    /**
     * 课程资源ID
     */
    @NotNull(message = "课程资源ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseResourceId;

    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;


}
