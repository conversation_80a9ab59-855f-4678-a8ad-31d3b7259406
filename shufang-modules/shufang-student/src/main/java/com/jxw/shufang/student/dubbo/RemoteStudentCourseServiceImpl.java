package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.student.api.RemoteStudentCourseService;
import com.jxw.shufang.student.service.ICourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentCourseServiceImpl implements RemoteStudentCourseService {

    private final ICourseService courseService;

    /**
     * 根据课程id集合获取对应的学段名称集合
     *
     * @param courseIds
     * @return
     */
    @Override
    public Map<Long, String> getStageListByCourseIds(List<Long> courseIds) {
        return courseService.getStageListByCourseIds(courseIds);
    }
}
