package com.jxw.shufang.student.service.studyduration.studentplan;

import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyVideoRecordDTO;
import com.jxw.shufang.student.domain.dto.StudyDurationProcessingContextDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.IStudyRecordService;
import com.jxw.shufang.student.service.studyduration.AbstractStudyRecordTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/2 15:38
 * @Version 1
 * @Description 学习规划 【预习，自讲】时长统计
 */
@Service
@Slf4j
public class StudyPlanDurationServiceImpl extends AbstractStudyRecordTime<StudyVideoRecordBo> {
    @Resource
    private IStudyRecordService studyRecordService;

    @Override
    public List<StudyVideoRecordBo> filterData(List<StudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return records;
    }

    @Override
    public StudyDurationProcessingContextDTO contextData(List<StudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<Long> planningIds = records.stream().map(StudyVideoRecordBo::getStudyPlanningRecordId).toList();
        List<Long> studentIds = records.stream().map(StudyVideoRecordBo::getStudentId).toList();
        List<Long> videoIds = records.stream().map(StudyVideoRecordBo::getVideoId).filter(Objects::nonNull).toList();

        StudyDurationProcessingContextDTO contextData = new StudyDurationProcessingContextDTO();
        contextData.setExistVideoRecordMap(super.batchQueryVideoRecords(records));
        contextData.setExistStudyRecordMap(studyRecordService.batchQueryMapByPlanningIds(planningIds));
        contextData.setStudentMap(super.studentMap(studentIds));
        contextData.setCourseDurationMap(this.courseDurationMap(videoIds));
        contextData.setModuleAndGroupEnum(moduleAndGroupEnum);
        return contextData;
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return List.of(StudyModelGroupEnum.STUDY_PLANNING);
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return List.of(StudyModuleTypeEnum.PREVIEW, StudyModuleTypeEnum.SELF_SPEECH);
    }

    @Override
    public SaveOrUpdateStudyVideoRecordDTO buildStudyVideoRecordProcessDTO(StudyVideoRecordBo recordBo) {
        return null;
    }

    @Override
    public SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(StudyVideoRecordBo recordBo) {
        SaveOrUpdateStudyRecordDTO saveOrUpdateStudyRecord = new SaveOrUpdateStudyRecordDTO();
        saveOrUpdateStudyRecord.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        saveOrUpdateStudyRecord.setCourseId(recordBo.getCourseId());
        saveOrUpdateStudyRecord.setStudentId(recordBo.getStudentId());
        saveOrUpdateStudyRecord.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        saveOrUpdateStudyRecord.setCommitTime(recordBo.getCommitTime());
        saveOrUpdateStudyRecord.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        saveOrUpdateStudyRecord.setVideoId(recordBo.getVideoId());
        return saveOrUpdateStudyRecord;
    }
}
