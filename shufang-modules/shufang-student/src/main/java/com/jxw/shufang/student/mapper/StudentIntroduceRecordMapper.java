package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import com.jxw.shufang.student.domain.vo.StudentIntroduceRecordVo;
import org.apache.ibatis.annotations.Param;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
public interface StudentIntroduceRecordMapper extends BaseMapperPlus<StudentIntroduceRecord, StudentIntroduceRecordVo> {
    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept"),
        @DataColumn(key = "userName", value = "t.create_by")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    IPage<StudentIntroduceRecordVo> pageStudentIntroduceRecord(@Param("page") Page<Object> build, @Param(Constants.WRAPPER) QueryWrapper<StudentIntroduceRecord> buildQueryMapper);
}
