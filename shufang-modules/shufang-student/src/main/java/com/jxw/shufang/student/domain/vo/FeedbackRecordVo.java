package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.FeedbackRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 反馈记录视图对象 feedback_record
 * @date 2024-05-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackRecord.class)
public class FeedbackRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈记录id
     */
    //@ExcelProperty(value = "反馈记录id")
    private Long feedbackRecordId;

    /**
     * 会员id
     */
    //@ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 反馈内容
     */
    @ExcelProperty(value = "反馈内容")
    private String feedbackContent;

    /**
     * 反馈日期范围-开始
     */
    //@ExcelProperty(value = "反馈日期范围-开始")
    private Date feedbackStartDate;

    /**
     * 反馈日期范围-结束
     */
    //@ExcelProperty(value = "反馈日期范围-结束")
    private Date feedbackEndDate;

    /**
     * 反馈图片（oss_id，多个，逗号隔开）
     */
    //@ExcelProperty(value = "反馈图片")
    private String feedbackImgs;

    /**
     * 表现分数（满分为5分，用星进行展示）
     */
    @ExcelProperty(value = "表现分数")
    private Long showScore;

    /**
     * 发布状态 1暂存 2已发布
     */
    @ExcelProperty(value = "发布状态",converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=暂存,2=已发布")
    private String feedbackStatus;

    /**
     * 反馈提交状态
     */
    @ExcelProperty(value = "反馈状态",converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=未反馈,2=已反馈,3=超时反馈,4=超时未反馈")
    private Integer feedbackSubmitStatus;

    /**
     * 反馈时间段，仅在导出表格时使用
     */
    @ExcelProperty(value = "时间段",order = 1)
    private String feedbackDateKLimit;

    /**
     * 会员名称，仅在导出表格时使用
     */
    @ExcelProperty(value = "会员名称",order = 2)
    private String studentName;

    /**
     * 会员顾问名称，仅在导出表格时使用
     */
    @ExcelProperty(value = "会员顾问",order = 3)
    private String consultantName;

    /**
     * 反馈人名称，仅在导出表格时使用
     */
    @ExcelProperty(value = "反馈人",order = 4)
    private String createStaffName;


    private Long createBy;

    @ExcelProperty(value = "反馈时间",order = 5)
    private Date createTime;


    /**
     * 1已通知 2未通知(默认) 3通知但失败
     */
    private String parentNotificationStatus;
    /**
     * 家长是否已读 1 未读 2已读
     */
    private String parentReadStatus;

    /**
     * 最近的一次通知时间
     */
    private Date notificationTime;

    /**
     * 最近一次通知的openid
     */
    private String notificationOpenid;


    /**
     * 创建者（员工）信息
     */
    private RemoteStaffVo createStaffInfo;

    /**
     * 会员顾问信息
     */
    private RemoteStaffVo consultantInfo;

    /**
     * 会员信息
     */
    private StudentVo student;

    private List<String> feedbackImgUrlList;

    private List<Long> unShowStudyRecordIdList;
    /**
     * 最近一次测试的准确率
     */
    private Long doLatestTestAccuracy;
    private Long doTestAccuracy;

    /**
     * 会员每日打卡登录情况表信息
     */
    private AttendanceDailyActivityFeedbackRecordVo attendanceDailyActivityFeedbackRecordVo;
}
