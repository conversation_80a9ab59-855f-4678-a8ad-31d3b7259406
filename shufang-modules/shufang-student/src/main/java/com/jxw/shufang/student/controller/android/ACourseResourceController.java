package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.ICourseResourceService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 课程资源（绑定到课程的资源）--平板端
 * 前端访问路由地址为:/student/android/courseResource
 *
 *
 * @date 2024-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/courseResource")
public class ACourseResourceController extends BaseController {

    private final ICourseResourceService courseResourceService;

    //@DubboReference
    //private RemoteExtResourceService remoteExtResourceService;




}
