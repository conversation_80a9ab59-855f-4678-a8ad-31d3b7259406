package com.jxw.shufang.student.controller.h5;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.service.IStudyFeedbackReportService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 学习反馈报告管理
 * 前端访问路由地址为:/student/management/feedbackReport
 *
 * @date 2024-06-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/h5/feedbackReport")
public class MpStudyFeedbackReportController extends BaseController {

    private final IStudyFeedbackReportService studyFeedbackReportService;

    /**
     * 获取学习反馈报告详细信息
     */
    @GetMapping("/{id}")
    public R<StudyFeedbackReportVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(studyFeedbackReportService.queryH5ById(id));
    }




}
