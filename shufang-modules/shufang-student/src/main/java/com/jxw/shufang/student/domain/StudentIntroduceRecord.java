package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_introduce_record")
public class StudentIntroduceRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员介绍记录ID
     */
    @TableId(value = "introduce_record_id")
    private Long introduceRecordId;
    /**
     * 介绍人ID
     */
    private Long introduceStudentId;
    /**
     * 介绍人名称
     */
    private String introduceStudentName;
    /**
     * 新会员ID
     */
    private Long studentId;
    /**
     * 会员姓名
     */
    private String studentName;
    /**
     * 年级
     */
    private String studentGrade;
    /**
     * 是否发放优惠
     */
    private Boolean doReferent;
    /**
     * 备注
     */
    private String remark;
    /**
     * 所属门店id
     */
    private Long branchId;
    /**
     * 所属门店名称
     */
    private String branchName;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 购买会员类型
     */
    private String branchAuthTypeName;
    /**
     * 赠送的优惠额度
     */
    private BigDecimal preferentialAmount;
    /**
     * 优惠额度解冻时间（优惠可使用时间）
     */
    private Date unFrozenTime;
}
