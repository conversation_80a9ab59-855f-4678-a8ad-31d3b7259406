package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * Ai学习错题记录对象 ai_wrong_question_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_wrong_question_record")
public class AiWrongQuestionRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ai学习错题id
     */
    @TableId(value = "ai_wrong_question_record_id")
    private Long aiWrongQuestionRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 来源类型（1测试 2练习）
     */
    private String sourceType;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    private String answerResult;


}
