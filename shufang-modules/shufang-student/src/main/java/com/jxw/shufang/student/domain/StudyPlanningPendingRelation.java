package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 需学习学生与学习规划关联记录对象 study_planning_pending_relation
 *
 * @date 2024-06-14
 */
@Data
@TableName("study_planning_pending_relation")
public class StudyPlanningPendingRelation implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 需学习规划记录ID
     */
    private Long pendingId;

    /**
     * 学习规划记录ID
     */
    private Long planningId;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
