package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据统计 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentDataStatisticsServiceImpl implements IStudentDataStatisticsService, BaseService {

    private final IStudyPlanningService studyPlanningService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IStudyRecordService studyRecordService;

    private final DictService dictService;

    private final ICourseService courseService;

    private final IStudentService studentService;

    private final IStudyVideoRecordService studyVideoRecordService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public StudyProcessVo getStudyProcess(Long studentId) {

        //初始化部分，保证一定有数据返回
        StudyProcessVo studyProcessVo = new StudyProcessVo();
        studyProcessVo.setTodayCumulativeStudyDur(0L);
        studyProcessVo.setTotalCumulativeStudyDur(0L);
        studyProcessVo.setTotalCumulativeCourseCount(0L);
        studyProcessVo.setTotalCumulativeStudyDays(0L);
        StudyProcessVo.XYVo xyVo = new StudyProcessVo.XYVo();
        xyVo.setXName("日期");
        xyVo.setYName("分钟");
        //近7天的学习时长
        List<StudyProcessVo.XYEntity> xyEntities = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 7; i++) {
            StudyProcessVo.XYEntity xyEntity = new StudyProcessVo.XYEntity();
            xyEntity.setX(calendar.get(Calendar.DAY_OF_MONTH) + "");
            xyEntity.setY("0");
            xyEntities.add(xyEntity);
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        //反转
        Collections.reverse(xyEntities);
        xyVo.setList(xyEntities);
        studyProcessVo.setXyVo(xyVo);

        //所有正常状态的学习规划
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudentId(studentId);
        studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);
        if (CollUtil.isEmpty(studyPlanningList)) {
            return studyProcessVo;
        }
        //按照日期分组，yyyy-MM-dd
        Map<String, StudyPlanningVo> stringStudyPlanningVoMap = StreamUtils.toIdentityMap(studyPlanningList, e -> DateUtils.dateTime(e.getStudyPlanningDate()));
        //按照id分组
        Map<Long, StudyPlanningVo> planningGroupById = StreamUtils.toIdentityMap(studyPlanningList, StudyPlanningVo::getStudyPlanningId);


        //查状态正常的学习规划详情
        List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudentId(studentId);
        studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
        studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
        List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordList)) {
            return studyProcessVo;
        }
        //按照学习规划id分组
        Map<Long, List<StudyPlanningRecordVo>> studyPlanningRecordMap = StreamUtils.groupByKey(studyPlanningRecordList, StudyPlanningRecordVo::getStudyPlanningId);
        //按照学习规划记录来分组
        Map<Long, StudyPlanningRecordVo> recordGroupById = StreamUtils.toIdentityMap(studyPlanningRecordList, StudyPlanningRecordVo::getStudyPlanningRecordId);

        //查学习记录
        List<Long> studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).toList();
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudentId(studentId);
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            studyRecordVos = new ArrayList<>();
        }
        //按照学习规划记录id分组
        Map<Long, StudyRecordVo> studyRecordVoMap = StreamUtils.toIdentityMap(studyRecordVos, StudyRecordVo::getStudyPlanningRecordId);


        //开始计算统计数据
        //今日累计学习时长（分钟）
        long todayCumulativeStudyDur = 0L;
        // 总累计学习时长（分钟）
        long totalCumulativeStudyDur = 0L;
        //总累计学习课程数
        long totalCumulativeCourseCount = 0L;
        //累计学习天数
        long totalCumulativeStudyDays = 0L;


        //今日累计学习时长
        String today = DateUtils.dateTime(new Date());
        StudyPlanningVo todayStudyPlanningVo = stringStudyPlanningVoMap.get(today);
        if (null != todayStudyPlanningVo) {
            List<StudyPlanningRecordVo> todayStudyPlanningRecordList = studyPlanningRecordMap.get(todayStudyPlanningVo.getStudyPlanningId());
            if (CollUtil.isNotEmpty(todayStudyPlanningRecordList)) {
                for (StudyPlanningRecordVo studyPlanningRecordVo : todayStudyPlanningRecordList) {
                    StudyRecordVo studyRecordVo = studyRecordVoMap.get(studyPlanningRecordVo.getStudyPlanningRecordId());
                    if (studyRecordVo != null && studyRecordVo.getStudyVideoTotalDuration() != null) {
                        todayCumulativeStudyDur += studyRecordVo.getStudyVideoTotalDuration();
                    }
                }
            }
        }
        //转化为分钟数,向下取整
        //todayCumulativeStudyDur = (long) Math.ceil(todayCumulativeStudyDur / 60.0);
        todayCumulativeStudyDur = (long) Math.floor(todayCumulativeStudyDur / 60.0);

        //总累计学习时长
        totalCumulativeStudyDur = studyRecordVos.stream().filter(e -> e.getStudyVideoTotalDuration() != null && e.getStudyVideoTotalDuration() > 0).mapToLong(StudyRecordVo::getStudyVideoTotalDuration).sum();
        //转化为分钟数,向下取整
        //totalCumulativeStudyDur = (long) Math.ceil(totalCumulativeStudyDur / 60.0);
        totalCumulativeStudyDur = (long) Math.floor(totalCumulativeStudyDur / 60.0);


        //总累计学习课程数(产生了学习记录的课程)
        totalCumulativeCourseCount = studyRecordVos.stream().filter(e -> e.getStudyVideoTotalDuration() != null && e.getStudyVideoTotalDuration() > 0).map(StudyRecordVo::getCourseId).distinct().count();

        //累计学习天数，产生了学习记录的课程的学习规划记录的学习规划日期数量
        Set<String> dateList = new HashSet<>();
        studyRecordVos.stream().filter(e -> e.getStudyVideoTotalDuration() != null && e.getStudyVideoTotalDuration() > 0).forEach(item -> {
            Long studyPlanningRecordId = item.getStudyPlanningRecordId();
            StudyPlanningRecordVo studyPlanningRecordVo = recordGroupById.get(studyPlanningRecordId);
            if (studyPlanningRecordVo == null) {
                return;
            }
            Long studyPlanningId = studyPlanningRecordVo.getStudyPlanningId();
            StudyPlanningVo studyPlanningVo = planningGroupById.get(studyPlanningId);
            if (studyPlanningVo == null) {
                return;
            }
            dateList.add(DateUtils.dateTime(studyPlanningVo.getStudyPlanningDate()));
        });
        totalCumulativeStudyDays = dateList.size();

        //近七天学习时长
        Map<String, StudyProcessVo.XYEntity> map = StreamUtils.toIdentityMap(xyEntities, StudyProcessVo.XYEntity::getX);

        calendar = Calendar.getInstance();
        for (int i = 0; i < 7; i++) {
            String key = calendar.get(Calendar.DAY_OF_MONTH) + "";
            String dateStr = DateUtils.dateTime(calendar.getTime());
            StudyPlanningVo studyPlanningVo = stringStudyPlanningVoMap.get(dateStr);
            if (null != studyPlanningVo) {
                List<StudyPlanningRecordVo> studyPlanningRecordVoList = studyPlanningRecordMap.get(studyPlanningVo.getStudyPlanningId());
                if (CollUtil.isNotEmpty(studyPlanningRecordVoList)) {
                    long dur = 0L;
                    for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordVoList) {
                        StudyRecordVo studyRecordVo = studyRecordVoMap.get(studyPlanningRecordVo.getStudyPlanningRecordId());
                        if (studyRecordVo != null && studyRecordVo.getStudyVideoTotalDuration() != null) {
                            dur += studyRecordVo.getStudyVideoTotalDuration();
                        }
                    }
                    //转化为分钟数,向下取整
                    //dur = (long) Math.ceil(dur / 60.0);
                    dur = (long) Math.floor(dur / 60.0);
                    map.get(key).setY(dur + "");
                }
            }
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        studyProcessVo.setTodayCumulativeStudyDur(todayCumulativeStudyDur);
        studyProcessVo.setTotalCumulativeStudyDur(totalCumulativeStudyDur);
        studyProcessVo.setTotalCumulativeCourseCount(totalCumulativeCourseCount);
        studyProcessVo.setTotalCumulativeStudyDays(totalCumulativeStudyDays);
        return studyProcessVo;
    }


    @Override
    public AnswerProcessVo getAnswerProcess(Long studentId) {

        //查询所有的科目字典列表
        Map<String, String> courseAffiliationSubjectMap = dictService.getAllDictByDictType("course_affiliation_subject");

        //先初始化返回数据，保证前端一定能拿到对应的数据
        AnswerProcessVo answerProcessVo = new AnswerProcessVo();
        List<AnswerProcessVo.AnswerEntity> answerEntities = new ArrayList<>();
        for (Map.Entry<String, String> entry : courseAffiliationSubjectMap.entrySet()) {
            AnswerProcessVo.AnswerEntity answerEntity = new AnswerProcessVo.AnswerEntity();
            answerEntity.setAffiliationSubject(entry.getKey());
            answerEntity.setProcess(0.0);
            answerEntities.add(answerEntity);
        }
        answerProcessVo.setAnswerEntities(answerEntities);
        //所有正常状态的学习规划
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudentId(studentId);
        studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);
        if (CollUtil.isEmpty(studyPlanningList)) {
            return answerProcessVo;
        }


        //查状态正常的学习规划详情
        List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudentId(studentId);
        studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
        studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
        List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordList)) {
            return answerProcessVo;
        }


        //查学习记录
        List<Long> studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).toList();
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudentId(studentId);
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return answerProcessVo;
        }
        studyRecordVos = studyRecordVos.stream().filter(e -> ObjectUtil.defaultIfNull(e.getTestRightNum(), 0L) > 0 || ObjectUtil.defaultIfNull(e.getTestWrongNum(), 0L) > 0).toList();
        if (CollUtil.isEmpty(studyRecordVos)) {
            return answerProcessVo;
        }
        //按照课程group
        Map<Long, List<StudyRecordVo>> studyRecordMap = StreamUtils.groupByKey(studyRecordVos, StudyRecordVo::getCourseId);


        //开始算一共做了多少测验题目
        long sum = studyRecordVos.stream().mapToLong(e -> {
            return ObjectUtil.defaultIfNull(e.getTestRightNum(), 0L)
                +
                ObjectUtil.defaultIfNull(e.getTestWrongNum(), 0L);
        }).sum();
        if (sum <= 0) {
            return answerProcessVo;
        }
        BigDecimal total = new BigDecimal(sum);


        //先查课程，我们这里只查有做测验的课程
        List<Long> courseIdList = studyRecordVos.stream()
            .map(StudyRecordVo::getCourseId)
            .distinct()
            .toList();
        if (CollUtil.isEmpty(courseIdList)) {
            return answerProcessVo;
        }
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVoList = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVoList)) {
            return answerProcessVo;
        }
        //科目的值只在顶级课程里有，所以这里拿一下顶级课程
        courseService.putTopmostCourseInfo(courseVoList, false);
        courseVoList = courseVoList.stream().filter(e -> e.getTopmostCourse() != null).toList();
        if (CollUtil.isEmpty(courseVoList)) {
            return answerProcessVo;
        }
        //按照顶级课程的科目分组
        Map<String, List<CourseVo>> courseVoMap = StreamUtils.groupByKey(courseVoList, e -> e.getTopmostCourse().getAffiliationSubject());

        //开始按照课程的科目统计
        for (AnswerProcessVo.AnswerEntity answerEntity : answerEntities) {
            String affiliationSubject = answerEntity.getAffiliationSubject();
            List<CourseVo> courseVos = courseVoMap.get(affiliationSubject);
            if (CollUtil.isEmpty(courseVos)) {
                continue;
            }
            long doNum = 0;
            for (CourseVo courseVo : courseVos) {
                List<StudyRecordVo> studyRecordVoList = studyRecordMap.get(courseVo.getCourseId());
                if (CollUtil.isEmpty(studyRecordVoList)) {
                    continue;
                }
                doNum += studyRecordVoList.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getTestRightNum(), 0L)).sum();
                doNum += studyRecordVoList.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getTestWrongNum(), 0L)).sum();
            }
            BigDecimal divide = new BigDecimal(doNum).divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            answerEntity.setProcess(divide.doubleValue());
        }
        return answerProcessVo;
    }

    @Override
    public LearnedCoursesRatesVo getLearnedCoursesRates(Long studentId) {
        //查询所有的科目字典列表
        Map<String, String> courseAffiliationSubjectMap = dictService.getAllDictByDictType("course_affiliation_subject");

        //先初始化返回数据，保证前端一定能拿到对应的数据
        LearnedCoursesRatesVo learnedCoursesRatesVo = new LearnedCoursesRatesVo();
        List<LearnedCoursesRatesVo.LearnedCoursesRatesEntity> learnedCoursesRatesEntities = new ArrayList<>();
        for (Map.Entry<String, String> entry : courseAffiliationSubjectMap.entrySet()) {
            LearnedCoursesRatesVo.LearnedCoursesRatesEntity learnedCoursesRatesEntity = new LearnedCoursesRatesVo.LearnedCoursesRatesEntity();
            learnedCoursesRatesEntity.setAffiliationSubject(entry.getKey());
            learnedCoursesRatesEntity.setRate(0.0);
            learnedCoursesRatesEntities.add(learnedCoursesRatesEntity);
        }
        learnedCoursesRatesVo.setLearnedRatesEntities(learnedCoursesRatesEntities);

        //所有正常状态的学习规划
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudentId(studentId);
        studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);
        if (CollUtil.isEmpty(studyPlanningList)) {
            return learnedCoursesRatesVo;
        }


        //查状态正常的学习规划详情
        List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudentId(studentId);
        studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
        studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
        List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordList)) {
            return learnedCoursesRatesVo;
        }
        //查学习记录
        List<Long> studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).toList();
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudentId(studentId);
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return learnedCoursesRatesVo;
        }
        studyRecordVos = studyRecordVos.stream()
            .filter(e -> e.getStudyVideoTotalDuration() != null && e.getStudyVideoTotalDuration() > 0)
            .toList();
        if (CollUtil.isEmpty(studyRecordVos)) {
            return learnedCoursesRatesVo;
        }
        //按照课程group
        Map<Long, List<StudyRecordVo>> studyRecordMap = StreamUtils.groupByKey(studyRecordVos, StudyRecordVo::getCourseId);

        //开始算一共学了多少规划
        long sum = studyRecordVos.stream().map(StudyRecordVo::getStudyPlanningRecordId).distinct().count();
        if (sum <= 0) {
            return learnedCoursesRatesVo;
        }
        BigDecimal total = new BigDecimal(sum);
        //先查课程，我们这里只查有做测验的课程
        List<Long> courseIdList = studyRecordVos.stream()
            .map(StudyRecordVo::getCourseId)
            .distinct()
            .toList();
        if (CollUtil.isEmpty(courseIdList)) {
            return learnedCoursesRatesVo;
        }
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVoList = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVoList)) {
            return learnedCoursesRatesVo;
        }
        //科目的值只在顶级课程里有，所以这里拿一下顶级课程
        courseService.putTopmostCourseInfo(courseVoList, false);
        courseVoList = courseVoList.stream().filter(e -> e.getTopmostCourse() != null).toList();
        if (CollUtil.isEmpty(courseVoList)) {
            return learnedCoursesRatesVo;
        }
        //按照顶级课程的科目分组
        Map<String, List<CourseVo>> courseVoMap = StreamUtils.groupByKey(courseVoList, e -> e.getTopmostCourse().getAffiliationSubject());

        //开始按照课程的科目统计
        for (LearnedCoursesRatesVo.LearnedCoursesRatesEntity learnedCoursesRatesEntity : learnedCoursesRatesEntities) {
            String affiliationSubject = learnedCoursesRatesEntity.getAffiliationSubject();
            List<CourseVo> courseVos = courseVoMap.get(affiliationSubject);
            if (CollUtil.isEmpty(courseVos)) {
                continue;
            }
            long doNum = 0;
            for (CourseVo courseVo : courseVos) {
                List<StudyRecordVo> studyRecordVoList = studyRecordMap.get(courseVo.getCourseId());
                if (CollUtil.isEmpty(studyRecordVoList)) {
                    continue;
                }
                doNum += studyRecordVoList.stream().map(StudyRecordVo::getStudyPlanningRecordId).distinct().count();
            }
            BigDecimal divide = new BigDecimal(doNum).divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            learnedCoursesRatesEntity.setRate(divide.doubleValue());
        }
        return learnedCoursesRatesVo;
    }

    /**
     * 获取学习时长排行榜 - 缓存版本
     *
     * @param durType   时间类型 可选：今日D， 本周W， 本月M
     * @param self      是否查询自己 可选：是：1, 否：0
     * @param pageQuery
     * @return StudyTimeRankVo
     */
    @Override
    public StudyTimeRankVo getStudyTimeRankListCache(String durType, Integer self, PageQuery pageQuery) {
        //构建缓存的key
        String cacheKey = "StudyRank_" + durType;
        //获取缓存结果
        StudyTimeRankVo cacheRes = RedisUtils.getCacheObject(cacheKey);
        if (ObjectUtil.isNotNull(cacheRes)) {
            //获取到缓存结果
            log.info("【排行榜数据】从缓存中获取数据...");
            return cacheRes;
        }

        //调用分布式锁的版本
        return SpringUtils.getAopProxy(this).getStudyTimeRankList(durType, self, pageQuery);
    }

    @Override
    @Lock4j(keys = "'StudyRank_' + #durType")
    public StudyTimeRankVo getStudyTimeRankList(String durType, Integer self, PageQuery pageQuery) {
        log.info("【排行榜数据】获得分布式锁...");
        StudyTimeRankVo res = new StudyTimeRankVo();

        //构建缓存的key
        String cacheKey = "StudyRank_" + durType;
        //获取缓存结果
        StudyTimeRankVo cacheRes = RedisUtils.getCacheObject(cacheKey);
        if (ObjectUtil.isNotNull(cacheRes)) {
            //获取到缓存结果
            log.info("【排行榜数据】从缓存中获取数据...");
            return cacheRes;
        }

        log.info("【排行榜数据】未从缓存中获取数据，开始查询数据库进行统计...");
        //按时间类型查出时间段 时间类型 今日D 本周W 本月M
        Date videoRecordCreateDateStart = null, videoRecordCreateDateEnd = null;
        Date videoRecordLastDateStart = null, videoRecordLastDateEnd = null;
        if ("D".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfToday();
            videoRecordCreateDateEnd = DateUtils.getEndOfToday();

//            videoRecordLastDateStart = DateUtils.getStartOfYesterday();
//            videoRecordLastDateEnd = DateUtils.getEndOfYesterday();
        } else if ("W".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfThisWeek();
            videoRecordCreateDateEnd = DateUtils.getEndOfThisWeek();

//            videoRecordLastDateStart = DateUtils.getStartOfLastWeek();
//            videoRecordLastDateEnd = DateUtils.getEndOfLastWeek();
        } else if ("M".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfThisMonth();
            videoRecordCreateDateEnd = DateUtils.getEndOfThisMonth();

//            videoRecordLastDateStart = DateUtils.getStartOfLastMonth();
//            videoRecordLastDateEnd = DateUtils.getEndOfLastMonth();
        }

        /*
         * 1、先查询当前时间维度（今日/本周/当月）的排序集合
         * 2、再查询上一个时间维度（昨日/上周/上月）的排序集合
         * 3、再根据两个时间维度，计算出每个用户的上升/下降趋势
         */
        //先查询当前时间维度（今日/本周/当月）的排序集合
        List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList = getStudyTimeRankListByDates(durType, videoRecordCreateDateStart, videoRecordCreateDateEnd);

//        //再查询上一个时间维度（昨日/上周/上月）的排序集合
//        List<StudyTimeRankVo.StudyTimeRankEntity> lastStudyTimeRankEntityList = getStudyTimeRankListByDates(durType, videoRecordLastDateStart, videoRecordLastDateEnd);
//
//        //将上一个时间维度的排序集合进行分组
//        Map<Long, List<StudyTimeRankVo.StudyTimeRankEntity>> lastGroupByStudentId = lastStudyTimeRankEntityList.stream().collect(Collectors.groupingBy(e -> e.getStudentId()));
//
//        //再查询上一个时间维度（昨日/上周/上月）的排序集合
//        if (null != studyTimeRankEntityList && studyTimeRankEntityList.size() > 0) {
//            studyTimeRankEntityList.parallelStream().forEach(e -> {
//                List<StudyTimeRankVo.StudyTimeRankEntity> lastEntity = null != lastGroupByStudentId.get(e.getStudentId()) ? lastGroupByStudentId.get(e.getStudentId()) : new ArrayList<>();
//
//                Integer lastRankNo = lastEntity.size() > 0 && null != lastEntity.get(0) && null != lastEntity.get(0).getRankNo() ? lastEntity.get(0).getRankNo() : 0;
//                if (lastRankNo > e.getRankNo()) {
//                    //1提升 2保持 3降低
//                    e.setDataStatus(3);
//                } else if (lastRankNo < e.getRankNo()) {
//                    e.setDataStatus(1);
//                } else {
//                    e.setDataStatus(2);
//                }
//            });
//        }

        //只取9条来展示
        studyTimeRankEntityList = studyTimeRankEntityList.size() > 9 ? new ArrayList<>(studyTimeRankEntityList.subList(0, 9)) : studyTimeRankEntityList;
        //设置排行榜中的会员相关信息
        putStudentInfo(studyTimeRankEntityList);
        //补充其他信息
        putOtherData(studyTimeRankEntityList, durType);
        //最终将排行榜信息设置到resp对象中
        res.setStudyTimeRankEntities(studyTimeRankEntityList);


        //计算当前时间距离凌晨整点的秒数
        long seconds = DateUtil.between(new Date(), DateUtil.endOfDay(new Date()), DateUnit.SECOND);
        //添加到缓存中
        log.info("【排行榜数据】将排行榜数据加入到缓存中：{}，过期时间：{}", res, seconds);
        RedisUtils.setCacheObject(cacheKey, res, Duration.ofSeconds(seconds));
        log.info("【排行榜数据】解除分布式锁...");
        return res;
    }

    /**
     * 获取学习时长排行榜基础信息，根据对应的日期范围
     *
     * @param durType   时间维度，今天、本周、当月
     * @param beginDate 开始时间
     * @param endDate   结束时间
     */
    @Override
    public List<StudyTimeRankVo.StudyTimeRankEntity> getStudyTimeRankListByDates(String durType, Date beginDate, Date endDate) {
        List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList = new ArrayList<>();
        //根据时间范围，查询当前（今天/本周/当月）已经完成了的学习规划列表
        //所有正常状态的学习规划
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        studyPlanningBo.setStudyPlanningDateStart(beginDate);
        studyPlanningBo.setStudyPlanningDateEnd(endDate);
        studyPlanningBo.setDontHandleQueryParam(true);
        List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

        List<Long> studyPlanningRecordIdList = List.of(-1L);
        if (CollUtil.isNotEmpty(studyPlanningList)) {
            //查状态正常的学习规划详情
            List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
            StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
            studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
            studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
            List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
            if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
            }
        }
        //按时间段 查询所有的学习记录，然后汇总
        StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
        studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
        List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

        //排行榜中不一定会有自己，如果没有就排到最后
//            Map<Long, List<StudyVideoRecordVo>> groupByStudentId = thisList.stream().collect(Collectors.groupingBy(StudyVideoRecordVo::getStudentId));
//            if (!groupByStudentId.containsKey(studentId)) {
//                StudyVideoRecordVo vo = new StudyVideoRecordVo();
//                vo.setStudentId(studentId);
//                vo.setStudyVideoDuration(0L);
//                thisList.add(vo);
//
//                flag = true;
//            }

        Map<Long, Long> thisMap = new HashMap<>();
        thisList.forEach(vo -> {
            Long total = 0L;
            if (thisMap.containsKey(vo.getStudentId())) {
                total = thisMap.get(vo.getStudentId());
                total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                thisMap.put(vo.getStudentId(), total);
            } else {
                total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                thisMap.put(vo.getStudentId(), total);
            }
        });

        List<Map<Long, List<Long>>> mapList = new ArrayList<>();

        // 遍历thisMap，收集值到键的映射
        for (Map.Entry<Long, Long> entry : thisMap.entrySet()) {
            boolean found = false; // 标记是否找到了对应的内部Map

            // 遍历mapList，查找是否已经存在对应的值
            for (Map<Long, List<Long>> innerMap : mapList) {
                if (innerMap.containsKey(entry.getValue())) {
                    // 如果找到了，就将键添加到对应的列表中
                    innerMap.get(entry.getValue()).add(entry.getKey());
                    found = true;
                    break;
                }
            }

            // 如果没有找到对应的值，就创建一个新的内部Map并添加进去
            if (!found) {
                Map<Long, List<Long>> newInnerMap = new HashMap<>();
                List<Long> keyList = new ArrayList<>();
                keyList.add(entry.getKey());
                newInnerMap.put(entry.getValue(), keyList);
                mapList.add(newInnerMap);
            }
        }

        //进行逆序排序
        Comparator<Map<Long, List<Long>>> comparator = (map1, map2) -> {
            long maxKey1 = Collections.max(map1.keySet());
            long maxKey2 = Collections.max(map2.keySet());
            return Long.compare(maxKey2, maxKey1); // 逆序排序，所以交换了maxKey1和maxKey2的位置
        };

        // 使用自定义比较器对mapList进行排序
        mapList.sort(comparator);

        //添加名次,并且排序
        for (int i = 0; i < mapList.size(); i++) {
            int finalI = i + 1;
            mapList.get(i).forEach((key, value) -> {
                value.forEach(v -> {
                    StudyTimeRankVo.StudyTimeRankEntity studyTimeRankEntity = new StudyTimeRankVo.StudyTimeRankEntity(v, key, durType, finalI);
                    studyTimeRankEntityList.add(studyTimeRankEntity);
                });
            });
        }
        return studyTimeRankEntityList;
    }

    @Override
    public StudyTimeRankVo getStudyTimeRankListForH5(Long studentId, String durType, Integer self) {
        StudyTimeRankVo res = new StudyTimeRankVo();


        //按时间类型查出时间段 时间类型 今日D 本周W 本月M
        Date videoRecordCreateDateStart = null, videoRecordCreateDateEnd = null;
        if ("D".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfToday();
            videoRecordCreateDateEnd = DateUtils.getEndOfToday();
        } else if ("W".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfThisWeek();
            videoRecordCreateDateEnd = DateUtils.getEndOfThisWeek();
        } else if ("M".equals(durType)) {
            videoRecordCreateDateStart = DateUtils.getStartOfThisMonth();
            videoRecordCreateDateEnd = DateUtils.getEndOfThisMonth();
        }

        List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList = new ArrayList<>();
        {
            //所有正常状态的学习规划
            StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
            studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
            studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
            studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
            studyPlanningBo.setDontHandleQueryParam(true);
            List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

            List<Long> studyPlanningRecordIdList = List.of(-1L);
            if (CollUtil.isNotEmpty(studyPlanningList)) {
                //查状态正常的学习规划详情
                List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                    studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                }
            }
            //按时间段 查询所有的学习记录，然后汇总
            StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
            studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
            studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
            List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

            //排行榜中不一定会有自己，如果没有就排到最后
            Map<Long, List<StudyVideoRecordVo>> groupByStudentId = thisList.stream().collect(Collectors.groupingBy(StudyVideoRecordVo::getStudentId));
            if (!groupByStudentId.containsKey(studentId)) {
                StudyVideoRecordVo vo = new StudyVideoRecordVo();
                vo.setStudentId(studentId);
                vo.setStudyVideoDuration(0L);
                thisList.add(vo);
            }

            Map<Long, Long> thisMap = new HashMap<>();
            thisList.forEach(vo -> {
                Long total = 0L;
                if (thisMap.containsKey(vo.getStudentId())) {
                    total = thisMap.get(vo.getStudentId());
                    total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                    thisMap.put(vo.getStudentId(), total);
                } else {
                    total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                    thisMap.put(vo.getStudentId(), total);
                }
            });

            List<Map<Long, List<Long>>> mapList = new ArrayList<>();

            // 遍历thisMap，收集值到键的映射
            for (Map.Entry<Long, Long> entry : thisMap.entrySet()) {
                boolean found = false; // 标记是否找到了对应的内部Map

                // 遍历mapList，查找是否已经存在对应的值
                for (Map<Long, List<Long>> innerMap : mapList) {
                    if (innerMap.containsKey(entry.getValue())) {
                        // 如果找到了，就将键添加到对应的列表中
                        innerMap.get(entry.getValue()).add(entry.getKey());
                        found = true;
                        break;
                    }
                }

                // 如果没有找到对应的值，就创建一个新的内部Map并添加进去
                if (!found) {
                    Map<Long, List<Long>> newInnerMap = new HashMap<>();
                    List<Long> keyList = new ArrayList<>();
                    keyList.add(entry.getKey());
                    newInnerMap.put(entry.getValue(), keyList);
                    mapList.add(newInnerMap);
                }
            }

            //进行逆序排序
            Comparator<Map<Long, List<Long>>> comparator = (map1, map2) -> {
                long maxKey1 = Collections.max(map1.keySet());
                long maxKey2 = Collections.max(map2.keySet());
                return Long.compare(maxKey2, maxKey1); // 逆序排序，所以交换了maxKey1和maxKey2的位置
            };

            // 使用自定义比较器对mapList进行排序
            mapList.sort(comparator);

            //添加名次,并且排序
            for (int i = 0; i < mapList.size(); i++) {
                int finalI = i + 1;
                List<StudyTimeRankVo.StudyTimeRankEntity> finalStudyTimeRankEntityList = studyTimeRankEntityList;
                mapList.get(i).forEach((key, value) -> {
                    value.forEach(v -> {
                        StudyTimeRankVo.StudyTimeRankEntity studyTimeRankEntity = new StudyTimeRankVo.StudyTimeRankEntity(v, key, durType, finalI);
                        finalStudyTimeRankEntityList.add(studyTimeRankEntity);
                    });
                });
            }

        }


        Map<Long, List<StudyTimeRankVo.StudyTimeRankEntity>> groupByStudentId = studyTimeRankEntityList.stream().collect(Collectors.groupingBy(e -> e.getStudentId()));

        List<StudyTimeRankVo.StudyTimeRankEntity> selfList = null;
        //取自己的排名
        if (Integer.valueOf(1).equals(self)) {
            // 排行榜中不一定会有自己，如果没有就排到最后
            selfList = groupByStudentId.get(studentId);
            if (null != selfList && selfList.size() > 0) {
                res.setMyRankNo(selfList.get(0).getRankNo());
                res.setMySex(selfList.get(0).getStudentSex());
            }
        }

        //只取9条来展示
        studyTimeRankEntityList = studyTimeRankEntityList.size() > 3 ? studyTimeRankEntityList.subList(0, 3) : studyTimeRankEntityList;
        //如果自己不在其中，则追加到最后一条
        groupByStudentId = studyTimeRankEntityList.stream().collect(Collectors.groupingBy(StudyTimeRankVo.StudyTimeRankEntity::getStudentId));
        //取自己的排名 排行榜中不一定会有自己，如果没有就排到最后
        List<StudyTimeRankVo.StudyTimeRankEntity> entities = groupByStudentId.get(studentId);
        if (null != entities && entities.size() > 0) {
        } else {
            //取前8条，然后补充自己为最后
            studyTimeRankEntityList = studyTimeRankEntityList.size() > 3 ? studyTimeRankEntityList.subList(0, 3) : studyTimeRankEntityList;
            if (Integer.valueOf(1).equals(self)&&null != selfList && !selfList.isEmpty()) {
                studyTimeRankEntityList.addAll(selfList);
            }
        }
        putStudentInfo(studyTimeRankEntityList);
        res.setStudyTimeRankEntities(studyTimeRankEntityList);

        return res;
    }

    private void putStudentInfo(List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList) {
        if (CollUtil.isEmpty(studyTimeRankEntityList)) {
            return;
        }
        List<Long> studentIdList = studyTimeRankEntityList.stream().map(StudyTimeRankVo.StudyTimeRankEntity::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));
        for (int i = 0; i < studyTimeRankEntityList.size(); i++) {
            StudentVo studentVo = studentVoMap.get(studyTimeRankEntityList.get(i).getStudentId());
            if (null != studentVo) {
                studyTimeRankEntityList.get(i).setStudentName(studentVo.getStudentName());
                studyTimeRankEntityList.get(i).setStudentSex(studentVo.getStudentSex());
                studyTimeRankEntityList.get(i).setSysUserId(studentVo.getCreateBy());
            } else {
                log.error("找不到对应的studentId：" + studyTimeRankEntityList.get(i).getStudentId());
                studyTimeRankEntityList.remove(i);
                i--;
            }
        }

        putSysUserInfo(studyTimeRankEntityList);
    }

    public void putSysUserInfo(List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList) {
        if (CollUtil.isEmpty(studyTimeRankEntityList)) {
            return;
        }
        List<Long> sysUserIdList = studyTimeRankEntityList.stream().map(StudyTimeRankVo.StudyTimeRankEntity::getSysUserId).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        remoteUserBo.setGetAvatarUrl(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studyTimeRankEntityList.forEach(entity -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(entity.getSysUserId());
            entity.setRealAvatar(remoteUserVo.getAvatarUrl());
        });
    }

    private void putOtherData(List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRankEntityList, String durType) {
        if (CollUtil.isEmpty(studyTimeRankEntityList)) {
            return;
        }

        List<Long> studentIdList = studyTimeRankEntityList.stream().map(StudyTimeRankVo.StudyTimeRankEntity::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }

        //按时间类型查出时间段 时间类型 今日D 本周W 本月M
        Date videoRecordCreateDateStart = null, videoRecordCreateDateEnd = null;
        //需要补充其他两个类型的数据
        if ("D".equals(durType)) {
            {
                //周
                videoRecordCreateDateStart = DateUtils.getStartOfThisWeek();
                videoRecordCreateDateEnd = DateUtils.getEndOfThisWeek();
                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }

                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setWeekCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }

            {
                //月
                videoRecordCreateDateStart = DateUtils.getStartOfThisMonth();
                videoRecordCreateDateEnd = DateUtils.getEndOfThisMonth();

                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }
                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setMonthCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }
        } else if ("W".equals(durType)) {
            {
                //日
                videoRecordCreateDateStart = DateUtils.getStartOfToday();
                videoRecordCreateDateEnd = DateUtils.getEndOfToday();

                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }


                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                studyVideoRecordBo.setStudentIdList(studentIdList);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setTodayCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }

            {
                //月
                videoRecordCreateDateStart = DateUtils.getStartOfThisMonth();
                videoRecordCreateDateEnd = DateUtils.getEndOfThisMonth();

                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }


                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                studyVideoRecordBo.setStudentIdList(studentIdList);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setMonthCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }
        } else if ("M".equals(durType)) {
            {
                //周
                videoRecordCreateDateStart = DateUtils.getStartOfThisWeek();
                videoRecordCreateDateEnd = DateUtils.getEndOfThisWeek();

                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }

                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                studyVideoRecordBo.setStudentIdList(studentIdList);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setWeekCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }

            {
                //日
                videoRecordCreateDateStart = DateUtils.getStartOfToday();
                videoRecordCreateDateEnd = DateUtils.getEndOfToday();


                //所有正常状态的学习规划
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentIdList(studentIdList);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setStudyPlanningDateStart(videoRecordCreateDateStart);
                studyPlanningBo.setStudyPlanningDateEnd(videoRecordCreateDateEnd);
                studyPlanningBo.setDontHandleQueryParam(true);
                List<StudyPlanningVo> studyPlanningList = studyPlanningService.queryList(studyPlanningBo);

                List<Long> studyPlanningRecordIdList = List.of(-1L);
                if (CollUtil.isNotEmpty(studyPlanningList)) {
                    //查状态正常的学习规划详情
                    List<Long> studyPlanningIdList = studyPlanningList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();
                    StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                    studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
                    if (CollUtil.isNotEmpty(studyPlanningRecordList)) {
                        studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
                    }
                }


                //按时间段 查询所有的学习记录，然后汇总
                StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
                studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
                studyVideoRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
                studyVideoRecordBo.setStudentIdList(studentIdList);
                List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

                Map<Long, Long> thisMap = new HashMap<>();
                thisList.forEach(vo -> {
                    Long total = 0L;
                    if (thisMap.containsKey(vo.getStudentId())) {
                        total = thisMap.get(vo.getStudentId());
                        total += null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    } else {
                        total = null != vo.getStudyVideoDuration() ? vo.getStudyVideoDuration() : 0L;
                        thisMap.put(vo.getStudentId(), total);
                    }
                });

                studyTimeRankEntityList.forEach(entity -> {
                    if (thisMap.containsKey(entity.getStudentId())) {
                        entity.setTodayCumulativeStudyDur((int) (thisMap.get(entity.getStudentId()) / 60));
                    }
                });
            }
        }

        //连续学习天数
        {
            //按时间段 查询所有的学习记录，然后汇总
            StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
            studyVideoRecordBo.setStudentIdList(studentIdList);
            studyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
            List<StudyVideoRecordVo> thisList = studyVideoRecordService.queryList(studyVideoRecordBo);

            if (null != thisList && thisList.size() > 0) {
                Map<Long, List<StudyVideoRecordVo>> groupByStudentId = thisList.stream().collect(Collectors.groupingBy(StudyVideoRecordVo::getStudentId));

                studyTimeRankEntityList.forEach(entity -> {
                    if (groupByStudentId.containsKey(entity.getStudentId())) {
                        List<StudyVideoRecordVo> studyVideoRecordVoList = groupByStudentId.get(entity.getStudentId());

                        List<String> createTimeList = studyVideoRecordVoList.stream().map(StudyVideoRecordVo::getDateByCreateTime).filter(StringUtils::isNotBlank).distinct().toList();

                        if (null != createTimeList && createTimeList.size() > 0) {
                            entity.setContinuousStudyDays(DateUtils.findLongestConsecutiveDays(createTimeList));
                        }
                    }
                });
            }
        }
    }


}
