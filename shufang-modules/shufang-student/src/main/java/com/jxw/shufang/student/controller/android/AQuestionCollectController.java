package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.DeleteGroup;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.domain.bo.QuestionCollectBo;
import com.jxw.shufang.student.domain.vo.QuestionCollectVo;
import com.jxw.shufang.student.service.IQuestionCollectService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/questionCollect")
public class AQuestionCollectController extends BaseController {

    private final IQuestionCollectService questionCollectService;
    private final RemoteCdsCommonService remoteCdsCommonService;

    @GetMapping("/page")
    public TableDataInfo<QuestionCollectVo> pageQuestionCollects(QuestionCollectBo questionCollectBo, PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        pageQuery.setSearchCount(false);
        questionCollectBo.setStudentId(LoginHelper.getStudentId());
        return questionCollectService.pageQuestionCollects(questionCollectBo, pageQuery);
    }

    @PostMapping("/insertBatch")
    @Log(title = "收藏题目", businessType = BusinessType.INSERT)
    public R<Void> insertBatchByBo(@Validated(AddGroup.class) @RequestBody List<QuestionCollectBo> bos) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        bos.forEach(bo -> bo.setStudentId(LoginHelper.getStudentId()));
        return toAjax(questionCollectService.insertBatchByBo(bos));
    }

    @PostMapping("/deleteBatch")
    @Log(title = "取消收藏", businessType = BusinessType.DELETE)
    public R<Void> deleteBatchByBo(@Validated(DeleteGroup.class) @RequestBody List<QuestionCollectBo> bos) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        bos.forEach(bo -> bo.setStudentId(LoginHelper.getStudentId()));
        return toAjax(questionCollectService.deleteBatchByBo(bos));
    }

    @GetMapping("/subject/list")
    public R<List<RemoteSubjectVo>> listSubjects() {
        return R.ok(remoteCdsCommonService.listSubjects(null));
    }

}
