package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiWrongQuestionRecordBo;
import com.jxw.shufang.student.domain.vo.AiWrongQuestionRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * Ai学习错题记录Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiWrongQuestionRecordService {

    /**
     * 查询Ai学习错题记录
     */
    AiWrongQuestionRecordVo queryById(Long aiWrongQuestionRecordId);

    /**
     * 查询Ai学习错题记录列表
     */
    TableDataInfo<AiWrongQuestionRecordVo> queryPageList(AiWrongQuestionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询Ai学习错题记录列表
     */
    List<AiWrongQuestionRecordVo> queryList(AiWrongQuestionRecordBo bo);

    /**
     * 新增Ai学习错题记录
     */
    Boolean insertByBo(AiWrongQuestionRecordBo bo);

    /**
     * 修改Ai学习错题记录
     */
    Boolean updateByBo(AiWrongQuestionRecordBo bo);

    /**
     * 校验并批量删除Ai学习错题记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<AiWrongQuestionRecordBo> wrongList);
}
