package com.jxw.shufang.student.dubbo;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import com.jxw.shufang.student.domain.bo.ProductBo;
import com.jxw.shufang.student.domain.vo.ProductVo;
import com.jxw.shufang.student.service.IProductService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteProductServiceImpl implements RemoteProductService {
    private final IProductService productService;

    @Override
    public List<RemoteProductVo> queryProductList(RemoteProductBo bo,boolean ignoreDataPermission) {
        ProductBo convert = MapstructUtils.convert(bo, ProductBo.class);
        List<ProductVo> productVos = null;
        if(ignoreDataPermission){
            productVos = DataPermissionHelper.ignore(()->productService.queryList(convert));
        }else {
            productVos = productService.queryList(convert);
        }
        return MapstructUtils.convert(productVos, RemoteProductVo.class);
    }

    @Override
    public List<RemoteProductVo> queryProductOption(RemoteProductBo bo) {
        ProductBo convert = MapstructUtils.convert(bo, ProductBo.class);
        List<ProductVo> productVos = productService.queryOptionList(convert);
        return MapstructUtils.convert(productVos, RemoteProductVo.class);
    }
}
