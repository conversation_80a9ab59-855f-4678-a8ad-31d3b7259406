package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.CorrectionRecord;

import java.util.Date;
import java.util.List;

/**
 * 批改记录业务对象 correction_record
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CorrectionRecord.class, reverseConvertGenerate = false)
public class CorrectionRecordBo extends BaseEntity {

    /**
     * 批改记录id
     */
    @NotNull(message = "批改记录id不能为空", groups = { EditGroup.class })
    private Long correctionRecordId;

    /**
     * 学习规划记录ID
     */
    @NotNull(message = "学习规划记录ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 批改类型（1测试 2练习）
     */
    @NotBlank(message = "批改类型（1测试 2练习 3预习 4反讲）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String correctionType;

    /**
     * 批改人类型（1顾问 2会员）
     */
    //@NotBlank(message = "批改人类型（1顾问 2会员）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String correctionPersonType;

    /**
     * 批改截图（oss_id，多个，逗号隔开）
     */
    @NotBlank(message = "批改截图不能为空", groups = { AddGroup.class, EditGroup.class })
    private String correctionScreenshots;

    /**
     * 会员Id
     */
    private Long studentId;

    @Valid
    @NotEmpty(message="批改记录信息不能为空",groups = {AddGroup.class})
    private List<CorrectionRecordInfoBo> correctionRecordInfoBoList;

    /**
     * 批改创建时间开始
     */
    private Date correctionRecordCreateTimeStart;

    /**
     * 批改创建时间结束
     */
    private Date correctionRecordCreateTimeEnd;

    private List<Long> studentIdList;

    private List<Long> studyPlanningRecordIdList;

    private List<String> correctionTypeList;


}
