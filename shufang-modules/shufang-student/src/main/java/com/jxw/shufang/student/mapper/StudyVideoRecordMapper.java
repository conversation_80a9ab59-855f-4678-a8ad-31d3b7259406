package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;

import java.util.List;

/**
 * 学习视频记录（视频观看记录）Mapper接口
 *
 *
 * @date 2024-05-06
 */
public interface StudyVideoRecordMapper extends BaseMapperPlus<StudyVideoRecord, StudyVideoRecordVo> {

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "create_dept"),
    //    @DataColumn(key = "userName", value = "create_by")
    //})
    //@BranchColumn(key = "deptName", value = "create_dept")
    List<StudyVideoRecordVo> queryList(@Param(Constants.WRAPPER) QueryWrapper<StudyVideoRecord> lqw,Boolean nonSelectStudyVideoSlicesField);

    Page<StudyVideoRecordVo> selectStudyVideoRecordPage(@Param("page") Page<StudyVideoRecord> build,@Param(Constants.WRAPPER)  QueryWrapper<StudyVideoRecord> lqw);

    List<StudyVideoRecordVo> queryLastStudyVideoRecord(List<Long> studyPlanningRecordIdList, boolean showStudyVideoSlices);

    Long queryStudyDaysByStudentId(Long studentId);

    StudyVideoRecordVo selectRecordOne(@Param(Constants.WRAPPER)QueryWrapper<StudyVideoRecord> queryWrapper);
}
