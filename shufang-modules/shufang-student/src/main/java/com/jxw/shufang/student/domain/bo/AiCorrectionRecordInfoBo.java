package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AiCorrectionRecordInfo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 批改记录详情业务对象 ai_correction_record_info
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiCorrectionRecordInfo.class, reverseConvertGenerate = false)
public class AiCorrectionRecordInfoBo extends BaseEntity {

    /**
     * 批改记录详情id
     */
    @NotNull(message = "Ai批改记录详情id不能为空", groups = { EditGroup.class })
    private Long aiCorrectionRecordInfoId;

    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 批改记录id
     */
    //@NotNull(message = "批改记录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long aiCorrectionRecordId;

    /**
     * 题目序号
     */
    @NotBlank(message = "题目序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对、全错、半错）
     */
    //@NotBlank(message = "答题对错情况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String answerResult;

    private List<Long> aiCorrectionRecordIds;


}
