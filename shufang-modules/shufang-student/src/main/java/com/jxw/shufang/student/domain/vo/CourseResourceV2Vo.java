package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceV2Vo;
import com.jxw.shufang.student.domain.CourseResource;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 课程资源（绑定到课程的资源）视图对象 course_resource
 *
 *
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CourseResource.class)
public class CourseResourceV2Vo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<RemoteGroupResourceV2Vo> knowledgeResourceList;

    /**
     * 关联课程
     */
    private CourseVo course;



}
