package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchMachineSeatService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudyPlanningRecord;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.StudyPlanningRecordMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学习规划记录Service业务层处理
 *
 * @date 2024-04-23
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class StudyPlanningRecordServiceImpl implements IStudyPlanningRecordService {

    private final StudyPlanningRecordMapper baseMapper;

    private final ICourseService courseService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentService studentService;

    private final IStudyRecordService studyRecordService;

    private final IFeedbackRecordService feedbackRecordService;

    private final ICorrectionRecordService correctionRecordService;

    private final ICorrectionRecordInfoService correctionRecordInfoService;

    private final IStudyVideoRecordService studyVideoRecordService;

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteBranchMachineSeatService remoteBranchMachineSeatService;

    @DubboReference
    private RemoteExtResourceService remoteExtResourceService;

    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;


    @DubboReference
    private RemoteUserService remoteUserService;


    /**
     * 查询学习规划记录
     */
    @Override
    public StudyPlanningRecordVo queryById(Long studyPlanningRecordId) {
        return baseMapper.selectVoById(studyPlanningRecordId);
    }

    /**
     * 查询学习规划记录列表
     */
    @Override
    public TableDataInfo<StudyPlanningRecordVo> queryPageList(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudyPlanningRecord> lqw = buildLambdaQueryWrapper(bo);
        Page<StudyPlanningRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询学习规划记录列表
     */
    @Override
    public List<StudyPlanningRecordVo> queryList(StudyPlanningRecordBo bo) {
        LambdaQueryWrapper<StudyPlanningRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    @Override
    public TableDataInfo<StudyPlanningRecordVo> queryStudyPlanningRecordPage(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<StudyPlanningRecord> queryWrapper = buildQueryWrapper(bo);
        Page<StudyPlanningRecordVo> result = baseMapper.queryStudyPlanningRecordPage(pageQuery.build(),queryWrapper);
        putBranchStaffInfo(result.getRecords());
        //拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<CourseVo> courseVos = result.getRecords().stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE);
            List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
            courseService.putCourseDetail(list, Boolean.FALSE);
        }
        if (Boolean.TRUE.equals(bo.getWithBranchMachineSeatInfo())) {
            putBranchMachineSeatInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithAttendanceInfo())) {
            putAttendanceInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudyRecord())) {
            putStudyRecord(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithTestCorrectionRecord()) || Boolean.TRUE.equals(bo.getWithPracticeCorrectionRecord())) {
            putCorrectionRecordInfo(result.getRecords(), bo.getWithTestCorrectionRecord(), bo.getWithPracticeCorrectionRecord());
        }
        if (Boolean.TRUE.equals(bo.getWithFeedbackRecord())) {
            putFeedbackRecord(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithLastStudyVideoRecord())) {
            putLastStudyVideoRecord(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(result.getRecords());
        }

        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<StudyPlanningRecordDashboardVo> queryStudyPlanningRecordPageV3(StudyPlanningRecordBo bo,
        PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<StudyPlanningRecord> queryWrapper = buildQueryWrapper(bo);

        // 根据会员id分页，先查出对应页码的会员id
        Page<Long> studentIdPage = baseMapper.queryStudyPlanningStudentIdV3(pageQuery.build(), queryWrapper);
        if (CollUtil.isEmpty(studentIdPage.getRecords())) {
            return TableDataInfo.build(new ArrayList<>(), studentIdPage.getTotal());
        }
        queryWrapper.in("t.student_id", studentIdPage.getRecords());
        List<StudyPlanningRecordDashboardVo> result = baseMapper.queryStudyPlanningRecordPageV3(queryWrapper);

        List<StudyPlanningRecordVo> collect =
            result.stream().map(StudyPlanningRecordDashboardVo::getStudyPlanningRecordList)
                .filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());

        putBranchStaffInfo(collect);
        // 拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result)) {
            List<CourseVo> courseVos =
                collect.stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE);
            List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
            courseService.putCourseDetail(list, Boolean.FALSE);
        }
        if (Boolean.TRUE.equals(bo.getWithBranchMachineSeatInfo())) {
            putBranchMachineSeatInfo(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithAttendanceInfo())) {
            putAttendanceInfo(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithStudyRecord())) {
            putStudyRecord(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithTestCorrectionRecord())
            || Boolean.TRUE.equals(bo.getWithPracticeCorrectionRecord())) {
            putCorrectionRecordInfo(collect, bo.getWithTestCorrectionRecord(), bo.getWithPracticeCorrectionRecord());
        }
        if (Boolean.TRUE.equals(bo.getWithFeedbackRecord())) {
            putFeedbackRecord(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithLastStudyVideoRecord())) {
            putLastStudyVideoRecord(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(collect);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(collect);
        }
        for (StudyPlanningRecordDashboardVo record : result) {
            if (CollUtil.isNotEmpty(record.getStudyPlanningRecordList())) {
                record.setStudent(record.getStudyPlanningRecordList().get(0).getStudent());
                record.setStaff(record.getStudyPlanningRecordList().get(0).getStaff());
            }
        }

        return TableDataInfo.build(result, studentIdPage.getTotal());
    }

    @Override
    public List<StudyPlanningRecordVo> queryStudyPlanningRecordList(StudyPlanningRecordBo bo) {
        handleQueryParam(bo);
        QueryWrapper<StudyPlanningRecord> queryWrapper = buildQueryWrapper(bo);
        List<StudyPlanningRecordVo> result = baseMapper.queryStudyPlanningRecordList(queryWrapper);
        putBranchStaffInfo(result);
        //拿出所有的课程，去找到各自的顶级课程
        if (CollUtil.isNotEmpty(result)) {
            List<CourseVo> courseVos = result.stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).toList();
            courseService.putTopmostCourseInfo(courseVos, Boolean.TRUE.equals(bo.getWithCourseDetail()));
            if (Boolean.TRUE.equals(bo.getWithCourseDetail())) {
                List<CourseVo> list = courseVos.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).toList();
                courseService.putCourseDetail(list, Boolean.FALSE);
            }

        }
        if (Boolean.TRUE.equals(bo.getWithBranchMachineSeatInfo())) {
            putBranchMachineSeatInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentInfo()) && Boolean.TRUE.equals(bo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithAttendanceInfo())) {
            putAttendanceInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithStudyRecord())) {
            putStudyRecord(result);
        }
        if (Boolean.TRUE.equals(bo.getWithTestCorrectionRecord())
            || Boolean.TRUE.equals(bo.getWithPracticeCorrectionRecord())
        ||Boolean.TRUE.equals(bo.getWithPreviewCorrectionRecord())
        ||Boolean.TRUE.equals(bo.getWithSpeakCorrectionRecord())) {
            putCorrectionRecordInfo(result, bo);
        }
        if (Boolean.TRUE.equals(bo.getWithFeedbackRecord())) {
            putFeedbackRecord(result);
        }
        if (Boolean.TRUE.equals(bo.getWithLastStudyVideoRecord())) {
            putLastStudyVideoRecord(result);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithCourseRepeatStatus())) {
            putCourseRepeatStatus(bo.getStudentId(),result);
        }
        return result;
    }
    private void putCourseRepeatStatus(Long studentId, List<StudyPlanningRecordVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> courseIdList = list.stream()
            .map(StudyPlanningRecordVo::getCourseId)
            .filter(courseId -> !ObjectUtils.isEmpty(courseId))
            .distinct()
            .toList();
        Map<Long, List<StudyPlanningRecordVo>> longListMap =
            this.checkCourseRepeatWithCorrection(courseIdList, studentId);

        for (StudyPlanningRecordVo studyPlanningRecordVo : list) {
            CourseVo studyRecordVo = studyPlanningRecordVo.getCourse();
            studyPlanningRecordVo.setRepeatStatus(false);
            if (!ObjectUtils.isEmpty(studyRecordVo)) {
                List<StudyPlanningRecordVo> studyList = longListMap.getOrDefault(studyPlanningRecordVo.getCourseId(), List.of());
                if (CollectionUtils.isEmpty(studyList) || studyList.size() == 1) {
                    continue;
                }
                 studyList
                     .stream()
                     .min(Comparator.comparing(StudyPlanningRecordVo::getLatestTime))
                    .ifPresent(v-> studyPlanningRecordVo.setRepeatStatus(!studyPlanningRecordVo.getStudyPlanningRecordId().equals(v.getStudyPlanningRecordId())));
            }
        }
    }

    @Override
    public boolean updateBatchByBo(List<StudyPlanningRecordBo> list) {
        if (CollUtil.isEmpty(list)) {
            return true;
        }
        List<StudyPlanningRecord> convert = MapstructUtils.convert(list, StudyPlanningRecord.class);
        return baseMapper.updateBatchById(convert);
    }

    @Transactional
    @Override
    public boolean updateBatchByVo(List<StudyPlanningRecordVo> list) {
        if (CollUtil.isEmpty(list)) {
            return true;
        }
        List<StudyPlanningRecord> convert = MapstructUtils.convert(list, StudyPlanningRecord.class);
        if (CollUtil.isNotEmpty(convert)) {
            return baseMapper.updateBatchById(convert);
        }
        return false;
    }

    @Override
    public boolean insertBatchByBo(List<StudyPlanningRecordBo> addStudyPlanningRecordBoList) {
        if (CollUtil.isEmpty(addStudyPlanningRecordBoList)) {
            return true;
        }
        List<StudyPlanningRecord> addStudyPlanningRecordList = MapstructUtils.convert(addStudyPlanningRecordBoList, StudyPlanningRecord.class);
        return baseMapper.insertBatch(addStudyPlanningRecordList);
    }

    @Override
    public List<Long> queryStudyPlanningIdListByRecordIdList(List<Long> list) {
        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningRecordId, list);
        wrapper.select(StudyPlanningRecord::getStudyPlanningId);
        List<StudyPlanningRecord> studyPlanningRecords = baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(studyPlanningRecords)) {
            return studyPlanningRecords.stream().map(StudyPlanningRecord::getStudyPlanningId).distinct().toList();
        }
        return List.of();
    }

    @Override
    public List<Long> queryStudyPlanningRecordIdListByPlanningIdList(List<Long> list) {
        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningId, list);
        wrapper.select(StudyPlanningRecord::getStudyPlanningRecordId);
        List<StudyPlanningRecord> studyPlanningRecords = baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(studyPlanningRecords)) {
            return studyPlanningRecords.stream().map(StudyPlanningRecord::getStudyPlanningRecordId).distinct().toList();
        }
        return List.of();
    }

    @Override
    public Map<Long, List<Long>> queryIdMapByStudyPlanningIdList(List<Long> studyPlanningIdList) {
        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningId, studyPlanningIdList);
        wrapper.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        wrapper.select(StudyPlanningRecord::getStudyPlanningRecordId, StudyPlanningRecord::getStudyPlanningId);
        List<StudyPlanningRecord> studyPlanningRecords = baseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(studyPlanningRecords)) {
            return studyPlanningRecords.stream().collect(Collectors.groupingBy(StudyPlanningRecord::getStudyPlanningId, Collectors.mapping(StudyPlanningRecord::getStudyPlanningRecordId, Collectors.toList())));
        }

        return Map.of();

    }

    @Override
    public Map<Long, List<Long>> queryRecordIdsByStudentIdMap(List<Long> studyPlanningIdList) {
        if (CollUtil.isEmpty(studyPlanningIdList)) {
            return new HashMap<>();
        }

        // 只查询需要的字段：studyPlanningRecordId 和 studentId
        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningId, studyPlanningIdList);
        wrapper.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        wrapper.select(StudyPlanningRecord::getStudyPlanningRecordId, StudyPlanningRecord::getStudentId);

        List<StudyPlanningRecord> records = baseMapper.selectList(wrapper);

        if (CollUtil.isNotEmpty(records)) {
            // 按学生ID分组学习规划记录ID
            return records.stream()
                .filter(record -> record.getStudentId() != null && record.getStudyPlanningRecordId() != null)
                .collect(Collectors.groupingBy(
                    StudyPlanningRecord::getStudentId,
                    Collectors.mapping(StudyPlanningRecord::getStudyPlanningRecordId, Collectors.toList())
                ));
        }

        return new HashMap<>();
    }

    @Override
    public StudyPlanningRecordVo queryFullInfoById(Long studyPlanningRecordId) {
        StudyPlanningRecordVo studyPlanningRecordVo = baseMapper.selectVoById(studyPlanningRecordId);
        if (studyPlanningRecordVo == null) {
            return null;
        }
        CourseVo courseVo = courseService.queryById(studyPlanningRecordVo.getCourseId());
        courseService.putTopmostCourseInfo(List.of(courseVo), Boolean.TRUE);
        CourseVo topmostCourse = courseVo.getTopmostCourse();
        courseService.putCourseDetail(List.of(topmostCourse), Boolean.TRUE);
        studyPlanningRecordVo.setCourse(courseVo);

        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(studyPlanningRecordVo.getStudentId());
        StudentVo studentVo = studentService.queryById(studentBo);
        studyPlanningRecordVo.setStudent(studentVo);

        return studyPlanningRecordVo;
    }

    @Override
    public TableDataInfo<StudyPlanRecordGroupVo> getStudyRecordByDate(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<StudyPlanningRecord> queryWrapper = buildQueryWrapper(bo);
        //按照时间来分组，并获得ids
        Page<StudyPlanRecordGroupVo> result = baseMapper.groupByStudyPlanDate(queryWrapper, pageQuery.build());
        List<StudyPlanRecordGroupVo> records = result.getRecords();
        if (CollUtil.isEmpty(records)) {
            return TableDataInfo.build(result);
        }
        bo.setStudyPlanningDateStart(records.get(records.size()-1).getDate());
        bo.setStudyPlanningDateEnd(records.get(0).getDate());
        wrapperAddTimeRange(queryWrapper,bo);
        result.setRecords(baseMapper.getGroupByStudyPlanDateData(queryWrapper));
        result.getRecords().sort((dateData1,dateData2)-> DateUtil.parse(dateData2.getDate(),DateUtils.YYYY_MM_DD).compareTo(DateUtil.parse(dateData1.getDate(),DateUtils.YYYY_MM_DD)));
        records = result.getRecords();
        List<Long> studyPlanningRecordIdList = records.stream().map(StudyPlanRecordGroupVo::getStudyPlanningRecordIdList).filter(List::isEmpty).flatMap(Collection::stream).distinct().toList();

        bo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = queryStudyPlanningRecordList(bo);
        if (CollUtil.isEmpty(studyPlanningRecordVos)) {
            return TableDataInfo.build(result);
        }
        Map<Long, StudyPlanningRecordVo> identityMap = StreamUtils.toIdentityMap(studyPlanningRecordVos, StudyPlanningRecordVo::getStudyPlanningRecordId);

        for (StudyPlanRecordGroupVo record : records) {
            if (CollectionUtils.isEmpty(record.getStudyPlanningRecordIdList())) {
                continue;
            }
            List<StudyPlanningRecordVo> studyPlanningRecordVoList = new ArrayList<>();
            long studyTotalTime = 0L;

            for (Long studyPlanningRecordId : record.getStudyPlanningRecordIdList()) {
                StudyPlanningRecordVo studyPlanningRecordVo = identityMap.get(studyPlanningRecordId);
                if (studyPlanningRecordVo == null) {
                    continue;
                }
                if (!studyPlanningRecordVoList.contains(studyPlanningRecordVo)) {
                    studyPlanningRecordVoList.add(studyPlanningRecordVo);
                }
                if (Boolean.TRUE.equals(bo.getWithStudyRecord())) {
                    CourseVo course = studyPlanningRecordVo.getCourse();
                    if (course == null) {
                        continue;
                    }
                    StudyRecordVo studyRecord = studyPlanningRecordVo.getStudyRecord();
                    if (studyRecord == null) {
                        continue;
                    }
                    studyTotalTime += ObjectUtil.defaultIfNull(studyRecord.getStudyVideoTotalDuration(), 0L);
                    //平板接口要求，把学习记录放到课程里
                    course.setStudyRecord(studyRecord);
                }
            }
            record.setStudyPlanningRecordList(studyPlanningRecordVoList);
            record.setTotalTime(studyTotalTime);

        }

        return TableDataInfo.build(result);
    }

    private void wrapperAddTimeRange(QueryWrapper<StudyPlanningRecord> queryWrapper, StudyPlanningRecordBo bo) {
        queryWrapper
            .ge(StringUtils.isNotEmpty(bo.getStudyPlanningDateStart()),"sp.study_planning_date",bo.getStudyPlanningDateStart())
            .le(StringUtils.isNotEmpty(bo.getStudyPlanningDateStart()),"sp.study_planning_date",bo.getStudyPlanningDateEnd());
        return;
    }

    @Override
    public List<StudyRecordStatisticsVo> queryStudyRecordStatisticsList(StudyPlanningRecordBo bo) {
        bo.setWithStudyRecord(Boolean.TRUE);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = queryStudyPlanningRecordList(bo);
        if (CollUtil.isEmpty(studyPlanningRecordVos)) {
            return List.of();
        }
        List<StudyRecordStatisticsVo> studyRecordStatisticsVos = new ArrayList<>();
        //按照会员分组
        Map<Long, List<StudyPlanningRecordVo>> studentMap = studyPlanningRecordVos.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getStudentId));

        //开始计算统计
        for (Map.Entry<Long, List<StudyPlanningRecordVo>> entry : studentMap.entrySet()) {
            StudyRecordStatisticsVo studyRecordStatisticsVo = new StudyRecordStatisticsVo();
            studyRecordStatisticsVo.setStudentId(entry.getKey());
            List<StudyPlanningRecordVo> studyPlanningRecordVoList = entry.getValue();
            long cumulativeStudyDuration = 0L;
            long cumulativeCompleteTestCount = 0L;
            long cumulativeCompleteExerciseCount = 0L;
            for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordVoList) {
                StudyRecordVo studyRecord = studyPlanningRecordVo.getStudyRecord();
                if (studyRecord == null) {
                    continue;
                }
                cumulativeStudyDuration += ObjectUtil.defaultIfNull(studyRecord.getStudyVideoTotalDuration(), 0L);
                cumulativeCompleteTestCount += (ObjectUtil.defaultIfNull(studyRecord.getTestRightNum(), 0L) + ObjectUtil.defaultIfNull(studyRecord.getTestWrongNum(), 0L));
                cumulativeCompleteExerciseCount += (ObjectUtil.defaultIfNull(studyRecord.getPracticeRightNum(), 0L) + ObjectUtil.defaultIfNull(studyRecord.getPracticeWrongNum(), 0L));
            }
            studyRecordStatisticsVo.setCumulativeStudyDuration(cumulativeStudyDuration);
            studyRecordStatisticsVo.setCumulativeCompleteTestCount(cumulativeCompleteTestCount);
            studyRecordStatisticsVo.setCumulativeCompleteExerciseCount(cumulativeCompleteExerciseCount);
            studyRecordStatisticsVos.add(studyRecordStatisticsVo);
        }
        return studyRecordStatisticsVos;
    }

    /**
     * 新增学习规划记录
     */
    @Override
    public Boolean insertByBo(StudyPlanningRecordBo bo) {
        StudyPlanningRecord add = MapstructUtils.convert(bo, StudyPlanningRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudyPlanningRecordId(add.getStudyPlanningRecordId());
        }
        return flag;
    }

    /**
     * 修改学习规划记录
     */
    @Override
    public Boolean updateByBo(StudyPlanningRecordBo bo) {
        StudyPlanningRecord update = MapstructUtils.convert(bo, StudyPlanningRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除学习规划记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void putCourseInfo(List<StudyPlanningRecordVo> recordList) {
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        List<Long> courseIdList = recordList.stream().map(StudyPlanningRecordVo::getCourseId).distinct().collect(Collectors.toList());
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            return;
        }
        Map<Long, CourseVo> courseMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, vo -> vo));
        recordList.forEach(record -> {
            CourseVo courseVo = courseMap.get(record.getCourseId());
            if (courseVo != null) {
                CourseVo copyBean = BeanUtils.copyBean(courseVo);
                record.setCourse(copyBean);
            }

        });
    }

    @Override
    public List<StudyPlanningRecordVo> getCourseInfoByRecordIdList(List<Long> studyPlanningRecordIdList) {
        LambdaQueryWrapper<StudyPlanningRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        lambdaQuery.in(StudyPlanningRecord::getStudyPlanningRecordId, studyPlanningRecordIdList);
        lambdaQuery.select(StudyPlanningRecord::getCourseId, StudyPlanningRecord::getStudyPlanningRecordId);
        List<StudyPlanningRecordVo> studyPlanningRecords = baseMapper.selectVoList(lambdaQuery);
        if (CollUtil.isEmpty(studyPlanningRecords)) {
            return null;
        }
        List<Long> courseIdList = studyPlanningRecords.stream().map(StudyPlanningRecordVo::getCourseId).distinct().collect(Collectors.toList());
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            return null;
        }
        Map<Long, CourseVo> courseMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, vo -> vo));

        Iterator<StudyPlanningRecordVo> iterator = studyPlanningRecords.iterator();
        while (iterator.hasNext()) {
            StudyPlanningRecordVo studyPlanningRecord = iterator.next();
            CourseVo courseVo = courseMap.get(studyPlanningRecord.getCourseId());
            if (courseVo == null) {
                iterator.remove();
            }
            studyPlanningRecord.setCourse(courseVo);
        }
        return studyPlanningRecords;
    }


    private void putLastStudyVideoRecord(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studyPlanningRecordIdList = records.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).filter(Objects::nonNull).distinct().toList();
        List<StudyVideoRecordVo> studyVideoRecordVos = studyVideoRecordService.queryLastStudyVideoRecord(studyPlanningRecordIdList, false);
        if (CollUtil.isEmpty(studyVideoRecordVos)) {
            return;
        }
        //按照study_planning_record_id分组
        Map<Long, StudyVideoRecordVo> studyVideoRecordVoMap = studyVideoRecordVos.stream().collect(Collectors.toMap(StudyVideoRecordVo::getStudyPlanningRecordId, Function.identity()));
        records.forEach(record -> {
            Long studyPlanningRecordId = record.getStudyPlanningRecordId();
            StudyVideoRecordVo studyVideoRecordVo = studyVideoRecordVoMap.get(studyPlanningRecordId);
            record.setLastStudyVideoRecord(studyVideoRecordVo);
        });
    }

    private void putStudentInfo(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));
        records.forEach(record -> {
            Long studentId = record.getStudentId();
            StudentVo studentVo = studentVoMap.get(studentId);
            record.setStudent(studentVo);
        });
    }

    @Override
    public void putCorrectionRecordInfo(List<StudyPlanningRecordVo> records, Boolean withTestCorrectionRecord, Boolean withPracticeCorrectionRecord) {
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setWithPracticeCorrectionRecord(withPracticeCorrectionRecord);
        studyPlanningRecordBo.setWithTestCorrectionRecord(withTestCorrectionRecord);
        putCorrectionRecordInfo(records,studyPlanningRecordBo);
    }

    @Override
    public Set<Long> checkCourseRepeat(Long courseId, List<Long> studentIdList) {
        if (CollectionUtils.isEmpty(studentIdList) || ObjectUtils.isEmpty(courseId)) {
            return Set.of();
        }
        // 查询已经选过该课程的学生ID
        return baseMapper.selectList(
                new QueryWrapper<StudyPlanningRecord>()
                    .eq("course_id", courseId)
                    .ne("study_record_status", UserConstants.STUDY_RECORD_STATUS_DELETE)
                    .in("student_id", studentIdList)
            ).stream()
            .map(StudyPlanningRecord::getStudentId)
            .collect(Collectors.toSet());
    }

    @Override
    public Map<Long, List<StudyPlanningRecordVo>> checkCourseRepeat(List<Long> courseIdList, Long studentId) {
        return checkCourseRepeatBase(courseIdList, studentId, false);
    }

    private Map<Long, List<StudyPlanningRecordVo>> checkCourseRepeatBase(List<Long> courseIdList, Long studentId, boolean withCorrectionRecord) {
        if (CollectionUtils.isEmpty(courseIdList) || ObjectUtils.isEmpty(studentId)) {
            return Map.of();
        }
        List<StudyPlanningRecordVo> result = getStudyPlanningRecordVos(courseIdList, studentId,withCorrectionRecord);
        if (CollectionUtils.isEmpty(result)){
            return Map.of();
        }
        // 查询已经选过该课程的学生ID
        return result.stream().peek(v -> {
                Date studyPlanningDate = v.getStudyPlanningDate();
                Date studyStartTime = v.getStudyStartTime();
                Date date = combineDates(studyPlanningDate, studyStartTime);
                v.setLatestTime(date);
            })
            .collect(Collectors.groupingBy(StudyPlanningRecordVo::getCourseId));
    }

    private List<StudyPlanningRecordVo> getStudyPlanningRecordVos(List<Long> courseIdList, Long studentId,boolean withCorrectionRecord) {
        QueryWrapper<StudyPlanningRecord> eq = new QueryWrapper<StudyPlanningRecord>()
            .in("t.course_id", courseIdList)
            .ne("t.study_record_status", UserConstants.STUDY_RECORD_STATUS_DELETE)
            .eq("t.student_id", studentId);
        List<StudyPlanningRecordVo> result = baseMapper.queryStudyPlanningRecordList(eq);
        if (CollUtil.isEmpty(result)){
            return result;
        }

        if (Boolean.TRUE.equals(withCorrectionRecord)) {
            putCorrectionRecordInfo(result,true,true);
        }

        return result;
    }

    @Override
    public Map<Long, List<StudyPlanningRecordVo>> checkCourseRepeatWithCorrection(List<Long> courseIdList, Long studentId) {
        return checkCourseRepeatBase(courseIdList, studentId, true);
    }

    public Date combineDates(Date studyPlanningDate, Date studyStartTime) {
        LocalDate planningDate = studyPlanningDate.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();

        LocalTime startTime = studyStartTime.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalTime();

        LocalDateTime combinedDateTime = LocalDateTime.of(planningDate, startTime);

        return Date.from(combinedDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private void putCorrectionRecordInfo(List<StudyPlanningRecordVo> records, StudyPlanningRecordBo bo) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Boolean withPracticeCorrectionRecord = bo.getWithPracticeCorrectionRecord();
        Boolean withTestCorrectionRecord = bo.getWithTestCorrectionRecord();

        List<Long> studyPlanningRecordIdList = records.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).filter(Objects::nonNull).distinct().toList();
        CorrectionRecordBo correctionRecordBo = new CorrectionRecordBo();
        correctionRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<String> correctionTypes = new ArrayList<>();
        if (Boolean.TRUE.equals(withTestCorrectionRecord)) {
            correctionTypes.add(UserConstants.CORRECTION_TYPE_TEST);
        }
        if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
            correctionTypes.add(UserConstants.CORRECTION_TYPE_PRACTICE);
        }
        if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
            correctionTypes.add(UserConstants.CORRECTION_TYPE_PREVIEW);
        }
        if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
            correctionTypes.add(UserConstants.CORRECTION_TYPE_SPEAK);
        }
        if (correctionTypes.size() > 1) {
            correctionRecordBo.setCorrectionTypeList(correctionTypes);
        } else if (correctionTypes.size() == 1) {
            correctionRecordBo.setCorrectionType(correctionTypes.get(0));
        }

        List<CorrectionRecordVo> correctionRecordVos = correctionRecordService.queryRecordAndRightWrongInfo(correctionRecordBo);
        if (CollUtil.isEmpty(correctionRecordVos)) {
            return;
        }
        //按照study_planning_record_id分组
        Map<Long, List<CorrectionRecordVo>> correctionRecordVoMap = correctionRecordVos.stream().collect(Collectors.groupingBy(CorrectionRecordVo::getStudyPlanningRecordId));
        records.forEach(record -> {
            Long studyPlanningRecordId = record.getStudyPlanningRecordId();
            List<CorrectionRecordVo> correctionRecordVoList = correctionRecordVoMap.get(studyPlanningRecordId);
            if (CollUtil.isEmpty(correctionRecordVoList)) {
                return;
            }
            for (CorrectionRecordVo correctionRecordVo : correctionRecordVoList) {
                if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(correctionRecordVo.getCorrectionType())) {
                    record.setPracticeCorrectionRecord(correctionRecordVo);
                } else if (UserConstants.CORRECTION_TYPE_TEST.equals(correctionRecordVo.getCorrectionType())) {
                    record.setTestCorrectionRecord(correctionRecordVo);
                }else if (UserConstants.CORRECTION_TYPE_PREVIEW.equals(correctionRecordVo.getCorrectionType())) {
                    record.setPreviewCorrectionRecord(correctionRecordVo);
                }else if (UserConstants.CORRECTION_TYPE_SPEAK.equals(correctionRecordVo.getCorrectionType())) {
                    record.setSpeakCorrectionRecord(correctionRecordVo);
                }
            }
        });

    }

    private void putFeedbackRecord(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        //找出
        //拿出最大日期和最小日期
        Date maxDate = records.stream().map(StudyPlanningRecordVo::getStudyPlanning).filter(Objects::nonNull).map(StudyPlanningVo::getStudyPlanningDate).max(Date::compareTo).get();
        Date minDate = records.stream().map(StudyPlanningRecordVo::getStudyPlanning).filter(Objects::nonNull).map(StudyPlanningVo::getStudyPlanningDate).min(Date::compareTo).get();

        List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).distinct().toList();
        //按照时间区间查找
        FeedbackRecordBo feedbackRecordBo = new FeedbackRecordBo();
        feedbackRecordBo.setFeedbackStartDateLimit(minDate);
        feedbackRecordBo.setFeedbackEndDate(maxDate);
        feedbackRecordBo.setStudentIdList(studentIdList);
        List<FeedbackRecordVo> feedbackRecordVos = feedbackRecordService.queryList(feedbackRecordBo);
        if (CollUtil.isEmpty(feedbackRecordVos)) {
            return;
        }
        for (StudyPlanningRecordVo record : records) {
            StudyPlanningVo studyPlanning = record.getStudyPlanning();
            if (studyPlanning == null) {
                continue;
            }
            Date studyPlanningDate = studyPlanning.getStudyPlanningDate();
            //找到第一个studyPlanningDate在feedbackStartDate和feedbackEndDate之间的值
            Optional<FeedbackRecordVo> first = feedbackRecordVos.stream().filter(e -> e.getFeedbackStartDate().before(studyPlanningDate) && e.getFeedbackEndDate().after(studyPlanningDate)).findFirst();
            first.ifPresent(record::setFeedbackRecord);
        }
    }

    private void putStudyRecord(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studyPlanningRecordIdList = records.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studyPlanningRecordIdList)) {
            return;
        }
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return;
        }

        //查知识点对应的视频资源
        List<Long> knowledgeIdList = records.stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).map(CourseVo::getKnowledgeId).filter(Objects::nonNull).distinct().toList();
        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        Map<Long, RemoteVideoVo> knowledgeVideoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(knowledgeVideoList)) {
            for (RemoteKnowledgeVideoVo remoteKnowledgeVideoVo : knowledgeVideoList) {
                List<RemoteVideoVo> videoList = remoteKnowledgeVideoVo.getVideoList();
                if (CollUtil.isEmpty(videoList)) {
                    continue;
                }
                //客户接口问题，只取第一个视频
                RemoteVideoVo remoteVideoVo = videoList.get(0);
                knowledgeVideoMap.put(remoteKnowledgeVideoVo.getKnowledgeId(), remoteVideoVo);
            }
        }

        Map<Long, StudyRecordVo> studyRecordVoMap = studyRecordVos.stream().collect(Collectors.toMap(StudyRecordVo::getStudyPlanningRecordId, vo -> vo));
        records.forEach(record -> {
            Long studyPlanningRecordId = record.getStudyPlanningRecordId();
            StudyRecordVo studyRecordVo = studyRecordVoMap.get(studyPlanningRecordId);
            if (studyRecordVo == null) {
                return;
            }
            record.setStudyRecord(studyRecordVo);

            CourseVo course = record.getCourse();
            if (course == null || course.getKnowledgeId() == null) {
                return;
            }
            RemoteVideoVo remoteVideoVo = knowledgeVideoMap.get(course.getKnowledgeId());
            record.setVideo(remoteVideoVo);
        });

    }


    private void putAttendanceInfo(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        //会员IdList
        List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        List<StudyPlanningVo> list = records.stream().map(StudyPlanningRecordVo::getStudyPlanning).filter(Objects::nonNull).filter(e -> e.getStudyPlanningDate() != null).toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        //拿出最大日期和最小日期
        Date maxDate = list.stream().map(StudyPlanningVo::getStudyPlanningDate).max(Date::compareTo).get();
        Date minDate = list.stream().map(StudyPlanningVo::getStudyPlanningDate).min(Date::compareTo).get();
        //查出对应的考勤记录
        AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo = new AttendanceLogStudentEzkecoBo();
        attendanceLogStudentEzkecoBo.setStudentIdList(studentIdList);
        attendanceLogStudentEzkecoBo.setRangeStartDate(DateUtils.dateTime(minDate));
        attendanceLogStudentEzkecoBo.setRangeEndDate(DateUtils.dateTime(maxDate));
        List<AttendanceLogStudentEzkecoVo> attendanceLogStudentEzkecoVos = attendanceLogStudentEzkecoService.queryList(attendanceLogStudentEzkecoBo);

        Map<Long, Map<String, List<AttendanceLogStudentEzkecoVo>>> map = StreamUtils.groupBy2Key(attendanceLogStudentEzkecoVos, AttendanceLogStudentEzkecoVo::getStudentId, e -> DateUtils.dateTime(e.getChecktime()));


        //按照会员分组
        Map<Long, List<StudyPlanningRecordVo>> recordMap = records.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getStudentId));

        for (Map.Entry<Long, List<StudyPlanningRecordVo>> entry : recordMap.entrySet()) {
            List<StudyPlanningRecordVo> recordList = entry.getValue();

            Map<String, List<AttendanceLogStudentEzkecoVo>> studentAttendanceMap = map.getOrDefault(entry.getKey(), Map.of());

            //recordList按照同一个学习规划主体分组
            Map<Long, List<StudyPlanningRecordVo>> studyPlanningRecordMap = StreamUtils.groupByKey(recordList, StudyPlanningRecordVo::getStudyPlanningId);

            for (Map.Entry<Long, List<StudyPlanningRecordVo>> listEntry : studyPlanningRecordMap.entrySet()) {

                //同一天的
                List<StudyPlanningRecordVo> recordVoList = listEntry.getValue();
                //按照开始时间排序,从小到大排序
                recordVoList.sort(Comparator.comparing(e -> DateUtils.timeToSeconds(e.getStudyStartTime())));


                //当天的考勤记录
                List<AttendanceLogStudentEzkecoVo> dayAttendanceList = studentAttendanceMap.getOrDefault(DateUtils.dateTime(recordVoList.get(0).getStudyPlanning().getStudyPlanningDate()), List.of());
                if (CollUtil.isEmpty(dayAttendanceList)) {
                    continue;
                }

                //按照checktime从小到大排序
                dayAttendanceList.sort(Comparator.comparing(AttendanceLogStudentEzkecoVo::getChecktime));


                //记录已经使用的记录id
                List<Long> attendanceUsedRecordIdList = new ArrayList<>();

                //遍历当天的每一节课
                for (int i = 0; i < recordVoList.size(); i++) {

                    StudyPlanningRecordVo studyPlanningRecordVo = recordVoList.get(i);
                    long studyStartTime = DateUtils.timeToSeconds(studyPlanningRecordVo.getStudyStartTime());
                    long studyEndTime = DateUtils.timeToSeconds(studyPlanningRecordVo.getStudyEndTime());


                    //来店记录处理
                    //第一节课处理
                    if (i == 0) {
                        //第一条直接当做来店记录
                        AttendanceLogStudentEzkecoVo attendanceLogStudentEzkecoVo = dayAttendanceList.get(0);
                        attendanceUsedRecordIdList.add(attendanceLogStudentEzkecoVo.getAttendanceLogStudentEzkecoId());
                        studyPlanningRecordVo.setInStoreAttendanceLog(attendanceLogStudentEzkecoVo);
                    } else {
                        //其他的到店时间处理
                        //上一门课结束时间和本门课开始之间如果存在时间，最接近开始时间的那一条为到店时间。
                        long preStudyEndTime = DateUtils.timeToSeconds(recordVoList.get(i - 1).getStudyEndTime());
                        Optional<AttendanceLogStudentEzkecoVo> max = dayAttendanceList.stream()
                            .filter(e ->
                                DateUtils.timeToSeconds(e.getChecktime()) > preStudyEndTime
                                    &&
                                    DateUtils.timeToSeconds(e.getChecktime()) <= studyStartTime
                            )
                            .filter(e -> !attendanceUsedRecordIdList.contains(e.getAttendanceLogStudentEzkecoId()))  //不能使用已经使用的记录
                            .max(Comparator.comparing(AttendanceLogStudentEzkecoVo::getChecktime)); //取最接近开始时间的那一条
                        //如果有
                        if (max.isPresent()) {
                            AttendanceLogStudentEzkecoVo attendanceLogStudentEzkecoVo = max.get();
                            attendanceUsedRecordIdList.add(attendanceLogStudentEzkecoVo.getAttendanceLogStudentEzkecoId());
                            studyPlanningRecordVo.setInStoreAttendanceLog(attendanceLogStudentEzkecoVo);
                        } else {
                            //没有的话，倒推一下前面的课有没有离店记录
                            AttendanceLogStudentEzkecoVo outStoreAttendanceLog = null;
                            for (int j = i - 1; j >= 0; j--) {
                                if (recordVoList.get(j).getOutStoreAttendanceLog() != null) {
                                    outStoreAttendanceLog = recordVoList.get(j).getOutStoreAttendanceLog();
                                    break;
                                }
                            }
                            //有离店的话，那么离店的下一次打卡时间大于5分钟，并且截止至课程结束时间的打卡记录的最后一条记录，作为本节课的到店记录
                            if (outStoreAttendanceLog != null) {
                                long outStoreTime = DateUtils.timeToSeconds(outStoreAttendanceLog.getChecktime());
                                long endTime = DateUtils.timeToSeconds(studyPlanningRecordVo.getStudyEndTime());
                                dayAttendanceList.stream()
                                    .filter(e -> DateUtils.timeToSeconds(e.getChecktime()) - outStoreTime > 300)
                                    .filter(e -> DateUtils.timeToSeconds(e.getChecktime()) <= endTime)
                                    .filter(e -> !attendanceUsedRecordIdList.contains(e.getAttendanceLogStudentEzkecoId()))  //不能使用已经使用的记录
                                    .max(Comparator.comparing(AttendanceLogStudentEzkecoVo::getChecktime))
                                    .ifPresent(attendanceLogStudentEzkecoVo -> {
                                        attendanceUsedRecordIdList.add(attendanceLogStudentEzkecoVo.getAttendanceLogStudentEzkecoId());
                                        studyPlanningRecordVo.setInStoreAttendanceLog(attendanceLogStudentEzkecoVo);
                                    });

                            }
                        }
                    }


                    //离店时间处理
                    //最后一节课处理
                    //先尝试从本节课开始时间到下一节课开始时间之间找离店记录，不行再从中间找

                    long lastStudyStartTime;
                    if (i == recordVoList.size() - 1) {
                        lastStudyStartTime = DateUtils.timeToSeconds("23:59:59");
                    } else {
                        lastStudyStartTime = DateUtils.timeToSeconds(recordVoList.get(i + 1).getStudyStartTime());
                    }

                    //先尝试从本节课开始时间到下一节课开始时间之间找离店记录，找最大的，且如果到店时间不为空，一定要大于到店时间5分钟以上
                    int finalI = i;
                    Optional<AttendanceLogStudentEzkecoVo> max = dayAttendanceList.stream()
                        .filter(e ->
                            DateUtils.timeToSeconds(e.getChecktime()) >= studyEndTime
                                &&
                                DateUtils.timeToSeconds(e.getChecktime()) <= lastStudyStartTime
                        )
                        .filter(e -> recordVoList.get(finalI).getInStoreAttendanceLog() == null || DateUtils.timeToSeconds(e.getChecktime()) - DateUtils.timeToSeconds(recordVoList.get(finalI).getInStoreAttendanceLog().getChecktime()) > 300)
                        .filter(e -> !attendanceUsedRecordIdList.contains(e.getAttendanceLogStudentEzkecoId()))  //不能使用已经使用的记录
                        .max(Comparator.comparing(AttendanceLogStudentEzkecoVo::getChecktime));//取最接近开始时间的那一条

                    //如果有
                    if (max.isPresent()) {
                        AttendanceLogStudentEzkecoVo attendanceLogStudentEzkecoVo = max.get();
                        attendanceUsedRecordIdList.add(attendanceLogStudentEzkecoVo.getAttendanceLogStudentEzkecoId());
                        studyPlanningRecordVo.setOutStoreAttendanceLog(attendanceLogStudentEzkecoVo);
                    } else {
                        //没有，就尝试找中间的，本门课的结束时间到下一节课开始时间之间找离店记录，找最大的。如果到店时间不为空，一定要大于到店时间5分钟以上
                        dayAttendanceList.stream()
                            .filter(e ->
                                DateUtils.timeToSeconds(e.getChecktime()) >= studyEndTime
                                    &&
                                    DateUtils.timeToSeconds(e.getChecktime()) <= studyStartTime
                            )
                            .filter(e -> recordVoList.get(finalI).getInStoreAttendanceLog() == null || DateUtils.timeToSeconds(e.getChecktime()) - DateUtils.timeToSeconds(recordVoList.get(finalI).getInStoreAttendanceLog().getChecktime()) > 300)
                            .filter(e -> !attendanceUsedRecordIdList.contains(e.getAttendanceLogStudentEzkecoId()))  //不能使用已经使用的记录
                            .max(Comparator.comparing(AttendanceLogStudentEzkecoVo::getChecktime))//取最接近开始时间的那一条
                            .ifPresent(attendanceLogStudentEzkecoVo -> {
                                attendanceUsedRecordIdList.add(attendanceLogStudentEzkecoVo.getAttendanceLogStudentEzkecoId());
                                studyPlanningRecordVo.setOutStoreAttendanceLog(attendanceLogStudentEzkecoVo);
                            });
                    }
                }


                //开始补全考勤记录到每一节课
                //正遍历当天的每一节课

                //先拿到最后一个有离店记录的索引
                int lastLeaveIndex = -1;
                for (int i = 0; i < recordVoList.size(); i++) {
                    //到达处理
                    StudyPlanningRecordVo studyPlanningRecordVo = recordVoList.get(i);
                    if (studyPlanningRecordVo.getOutStoreAttendanceLog()!=null) {
                        lastLeaveIndex = i;
                    }
                }


                for (int i = 0; i < recordVoList.size(); i++) {
                    //后面的就不用处理了
                    if (i > lastLeaveIndex) {
                        break;
                    }
                    //到达处理
                    StudyPlanningRecordVo studyPlanningRecordVo = recordVoList.get(i);
                    AttendanceLogStudentEzkecoVo inStoreAttendanceLog = studyPlanningRecordVo.getInStoreAttendanceLog();
                    if (inStoreAttendanceLog == null && i > 0) {
                        StudyPlanningRecordVo preRecord = recordVoList.get(i - 1);
                        if (preRecord.getInStoreAttendanceLog() != null){
                            studyPlanningRecordVo.setInStoreAttendanceLog(preRecord.getInStoreAttendanceLog());
                        }
                    }

                }

                //倒序遍历当天的每一节课
                for (int i = recordVoList.size() - 1; i >= 0; i--) {
                    //如果没有到店时间，均不做处理
                    if (recordVoList.get(i).getInStoreAttendanceLog() == null) {
                        continue;
                    }
                    //离开处理
                    StudyPlanningRecordVo studyPlanningRecordVo = recordVoList.get(i);
                    //如果没有，沿用下一节课的
                    if (studyPlanningRecordVo.getOutStoreAttendanceLog() == null && i < recordVoList.size() - 1) {
                        StudyPlanningRecordVo nextRecord = recordVoList.get(i + 1);
                        if (nextRecord.getOutStoreAttendanceLog() != null){
                            studyPlanningRecordVo.setOutStoreAttendanceLog(nextRecord.getOutStoreAttendanceLog());
                        }
                    }
                }

            }
        }

    }


    private void putBranchMachineSeatInfo(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudyPlanningVo> list = records.stream().map(StudyPlanningRecordVo::getStudyPlanning).filter(Objects::nonNull).filter(e -> e.getStudyPlanningDate() != null).toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        //会员IdList
        List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }

        //拿出最大日期和最小日期
        Date maxDate = list.stream().map(StudyPlanningVo::getStudyPlanningDate).max(Date::compareTo).get();
        Date minDate = list.stream().map(StudyPlanningVo::getStudyPlanningDate).min(Date::compareTo).get();
        //查对应的座位
        RemoteBranchMachineSeatBo remoteBranchMachineSeatBo = new RemoteBranchMachineSeatBo();
        remoteBranchMachineSeatBo.setStudentIdList(studentIdList);
        remoteBranchMachineSeatBo.setRangeStartTime(minDate);
        remoteBranchMachineSeatBo.setRangeEndTime(DateUtil.endOfDay(maxDate));
        List<RemoteBranchMachineSeatVo> remoteBranchMachineSeatVoList = remoteBranchMachineSeatService.queryList(remoteBranchMachineSeatBo);
        if (CollUtil.isEmpty(remoteBranchMachineSeatVoList)) {
            return;
        }
        Map<Long, List<RemoteBranchMachineSeatVo>> map = StreamUtils.groupByKey(remoteBranchMachineSeatVoList, RemoteBranchMachineSeatVo::getStudentId);
        records.forEach(record -> {
            Long studentId = record.getStudentId();
            List<RemoteBranchMachineSeatVo> seatList = map.get(studentId);
            if (CollUtil.isEmpty(seatList)) {
                return;
            }
            //先过滤同一天，然后再过滤同一节课的时间，直接用相等比较
            StudyPlanningVo studyPlanning = record.getStudyPlanning();
            if (studyPlanning == null || studyPlanning.getStudyPlanningDate() == null) {
                return;
            }
            String studyPlanningDateStr = DateUtils.dateTime(studyPlanning.getStudyPlanningDate());
            List<RemoteBranchMachineSeatVo> sameDaySeatList = seatList.stream().filter(e ->
                e.getUseStartTime() != null && DateUtils.dateTime(e.getUseStartTime()).equals(studyPlanningDateStr)
            ).toList();
            if (CollUtil.isEmpty(sameDaySeatList)) {
                return;
            }
            //过滤同一节课时间的
            Date studyStartTime = record.getStudyStartTime();
            Date studyEndTime = record.getStudyEndTime();
            if (studyStartTime == null || studyEndTime == null) {
                return;
            }
            Optional<RemoteBranchMachineSeatVo> first = sameDaySeatList.stream().filter(e -> e.getUseStartTime() != null
                && e.getUseEndTime() != null
                && DateUtils.getTime(e.getUseStartTime()).equals(DateUtils.getTime(studyStartTime))
                && DateUtils.getTime(e.getUseEndTime()).equals(DateUtils.getTime(studyEndTime))).findFirst();
            first.ifPresent(record::setBranchMachineSeat);
        });
    }

    private void putBranchStaffInfo(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(studentIdList);
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return;
        }
        Collection<Long> studentConsultantIdList = studentConsultantIdMap.values();
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(new ArrayList<>(studentConsultantIdList));
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        records.forEach(record -> {
            Long studentId = record.getStudentId();
            Long consultantId = studentConsultantIdMap.get(studentId);
            if (consultantId == null) {
                return;
            }
            RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(consultantId);
            if (remoteStaffVo == null) {
                return;
            }
            record.setStaff(remoteStaffVo);
        });
    }


    private LambdaQueryWrapper<StudyPlanningRecord> buildLambdaQueryWrapper(StudyPlanningRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyPlanningRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), StudyPlanningRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordIdList());
        lqw.eq(bo.getStudyPlanningId() != null, StudyPlanningRecord::getStudyPlanningId, bo.getStudyPlanningId());
        lqw.eq(bo.getStudentId() != null, StudyPlanningRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, StudyPlanningRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudyStartTime() != null, StudyPlanningRecord::getStudyStartTime, bo.getStudyStartTime());
        lqw.eq(bo.getStudyEndTime() != null, StudyPlanningRecord::getStudyEndTime, bo.getStudyEndTime());
        lqw.eq(bo.getStudyDuration() != null, StudyPlanningRecord::getStudyDuration, bo.getStudyDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyStatus()), StudyPlanningRecord::getStudyStatus, bo.getStudyStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyRecordStatus()), StudyPlanningRecord::getStudyRecordStatus, bo.getStudyRecordStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningIdList()), StudyPlanningRecord::getStudyPlanningId, bo.getStudyPlanningIdList());
        if (StringUtils.isBlank(bo.getStudyRecordStatus())) {
            lqw.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        }
        if (StringUtils.isNotBlank(bo.getStudentName()) || bo.getConsultantId() != null) {
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentName(bo.getStudentName());
            studentBo.setConsultantId(bo.getConsultantId());
            List<Long> studentIds = studentService.queryStudentIdList(studentBo);
            if (CollUtil.isNotEmpty(studentIds)) {
                lqw.in(StudyPlanningRecord::getStudentId, studentIds);
            } else {
                lqw.in(StudyPlanningRecord::getStudentId, -1);
            }
        }
        lqw.orderByAsc(StudyPlanningRecord::getStudyStartTime,  StudyPlanningRecord::getStudyEndTime);
        //if (StringUtils.isNotBlank(bo.getDateYearMonthLimitStart())&& StringUtils.isNotBlank(bo.getDateYearMonthLimitEnd())){
        //    lqw.apply("DATE_FORMAT(sp.study_planning_date, '%Y-%m') between {0} and {1}", bo.getDateYearMonthLimitStart(), bo.getDateYearMonthLimitEnd());
        //}
        return lqw;
    }

    private QueryWrapper<StudyPlanningRecord> buildQueryWrapper(StudyPlanningRecordBo bo) {
        QueryWrapper<StudyPlanningRecord> lqw = Wrappers.query();
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), "t.study_planning_record_id", bo.getStudyPlanningRecordIdList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudyPlanningRecordIdList()), "t.study_planning_record_id", bo.getNotInStudyPlanningRecordIdList());
        lqw.eq(bo.getStudyPlanningId() != null, "t.study_planning_id", bo.getStudyPlanningId());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(bo.getStudyStartTime() != null, "t.study_start_time", bo.getStudyStartTime());
        lqw.eq(bo.getStudyEndTime() != null, "t.study_end_time", bo.getStudyEndTime());
        lqw.eq(bo.getStudyDuration() != null, "t.study_duration", bo.getStudyDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyStatus()), "t.study_status", bo.getStudyStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyRecordStatus()), "t.study_record_status", bo.getStudyRecordStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        if (StringUtils.isBlank(bo.getStudyRecordStatus())) {
            lqw.eq("t.study_record_status", UserConstants.STUDY_RECORD_STATUS_NORMAL);
        } else {
            lqw.eq("t.study_record_status", bo.getStudyRecordStatus());
        }

        if (StringUtils.isBlank(bo.getStudyPlanningStatus())) {
            lqw.eq("sp.study_planning_status", UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        } else {
            lqw.eq("sp.study_planning_status", bo.getStudyPlanningStatus());
        }

        if (StringUtils.isNotBlank(bo.getDateYearMonthLimitStart()) && StringUtils.isNotBlank(bo.getDateYearMonthLimitEnd())) {
            if (bo.getDateYearMonthLimitStart().split("-").length == 3) {
                lqw.between("DATE_FORMAT(sp.study_planning_date, '%Y-%m-%d')", bo.getDateYearMonthLimitStart(), bo.getDateYearMonthLimitEnd());
            } else {
                lqw.between("DATE_FORMAT(sp.study_planning_date, '%Y-%m')", bo.getDateYearMonthLimitStart(), bo.getDateYearMonthLimitEnd());
            }
        }else if (StringUtils.isNotBlank(bo.getDateYearMonthLimitStart())){
            lqw.ge(true,"sp.study_planning_date",bo.getDateYearMonthLimitStart());
        }

        lqw.eq(bo.getStudyPlanningDate() != null, "DATE(sp.study_planning_date)", bo.getStudyPlanningDate() != null ? DateUtils.dateTime(bo.getStudyPlanningDate()) : null);
        lqw.eq(StringUtils.isNotBlank(bo.getStudyPlanningDateYearMonth()), "DATE_FORMAT(sp.study_planning_date, '%Y-%m')", bo.getStudyPlanningDateYearMonth());

        // 学习规划日期范围查询 (yyyy-MM-dd格式，不包含时分秒)
        lqw.ge(StringUtils.isNotBlank(bo.getStudyPlanningDateStart()), "DATE(sp.study_planning_date)", bo.getStudyPlanningDateStart());
        lqw.le(StringUtils.isNotBlank(bo.getStudyPlanningDateEnd()), "DATE(sp.study_planning_date)", bo.getStudyPlanningDateEnd());

        if (null != bo.getStudyPlanningIdList() && bo.getStudyPlanningIdList().size() > 0) {
            lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningIdList()), "t.study_planning_id", bo.getStudyPlanningIdList());
        }

        if (StringUtils.isNotBlank(bo.getStudentName()) || bo.getConsultantId() != null || StringUtils.isNotBlank(bo.getNameWithPhone())) {
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentName(bo.getStudentName());
            studentBo.setConsultantId(bo.getConsultantId());
            studentBo.setNameWithPhone(bo.getNameWithPhone());
            List<Long> studentIds = studentService.queryStudentIdList(studentBo);
            if (CollUtil.isNotEmpty(studentIds)) {
                lqw.in("t.student_id", studentIds);
            } else {
                lqw.in("t.student_id", -1);
            }
        }

        if (StringUtils.isNotBlank(bo.getAffiliationSubject())) {
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseType(UserConstants.TOP_COURSE_TYPE);
            courseBo.setAffiliationSubject(bo.getAffiliationSubject());
            List<CourseChildInfoVo> childInfoList = courseService.getChildInfoList(courseBo);
            List<Long> childInfoIdList;
            if (CollUtil.isEmpty(childInfoList)
                || CollectionUtils.isEmpty((childInfoIdList = childInfoList.stream().map(CourseChildInfoVo::getChildIds).filter(StringUtils::isNotBlank).map(e -> e.split(",")).flatMap(Arrays::stream).map(Long::parseLong).distinct().toList()))) {
                lqw.in("t.course_id", -1);
            } else {
                lqw.in("t.course_id", childInfoIdList);
            }
        }

        if (Boolean.TRUE.equals(bo.getStudyRecordHas())) {
            StudyRecordBo studyRecordBo = new StudyRecordBo();
            studyRecordBo.setGtStudyVideoTotalDuration(0L);
            studyRecordBo.setStudentIdList(bo.getStudentIdList());
            studyRecordBo.setCourseId(bo.getCourseId());
            if (null != bo.getStudentId()) {
                if (CollectionUtils.isEmpty(studyRecordBo.getStudentIdList())) {
                    studyRecordBo.setStudentIdList(Collections.singletonList(bo.getStudentId()));
                } else if (studyRecordBo.getStudentIdList().stream().noneMatch(id -> bo.getStudentId().equals(id))) {
                    studyRecordBo.getStudentIdList().add(bo.getStudentId());
                }
            }
            List<Long> longs = studyRecordService.queryStudyPlanningRecordIdList(studyRecordBo);
            if (Boolean.TRUE.equals(bo.getStudyRecordMustHas())) {
                if (CollUtil.isEmpty(longs)) {
                    lqw.apply("1=2");
                } else {
                    lqw.in("t.study_planning_record_id", longs);
                }
            } else {
                lqw.in(bo.getStudyRecordHas() && CollUtil.isNotEmpty(longs), "t.study_planning_record_id", longs);
            }
        }
        return lqw;
    }

    /**
     * 放入会员系统用户信息
     *
     * @param records
     */
    private void putStudentSysUserInfo(List<StudyPlanningRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(StudyPlanningRecordVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(true);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyPlanningRecord entity) {
        //做一些数据校验,如唯一约束
    }

    private void handleQueryParam(StudyPlanningRecordBo record) {
        if (record.getStudentId() != null) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else if (null != LoginHelper.getBranchId()) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else {
            record.setStudentIdList(List.of(-1L));
        }

    }

    @Override
    public List<StudyPlanningRecordVo> queryByPlanningIds(List<Long> planningIds) {
        if (CollUtil.isEmpty(planningIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningId, planningIds);
        wrapper.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        wrapper.orderByAsc(StudyPlanningRecord::getStudyStartTime);

        List<StudyPlanningRecordVo> records = baseMapper.selectVoList(wrapper);

        if (CollUtil.isNotEmpty(records)) {
            // 填充课程信息
            putCourseInfo(records);
        }

        return records;
    }

    @Override
    public List<StudyPlanningRecordVo> queryStudyPlanningRecordListByIds(List<Long> studyPlanningRecordIds) {
        if (CollUtil.isEmpty(studyPlanningRecordIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<StudyPlanningRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(StudyPlanningRecord::getStudyPlanningRecordId, studyPlanningRecordIds);
        wrapper.eq(StudyPlanningRecord::getStudyRecordStatus, UserConstants.STUDY_RECORD_STATUS_NORMAL);
        wrapper.orderByAsc(StudyPlanningRecord::getStudyStartTime);
        return baseMapper.selectVoList(wrapper);
    }

}
