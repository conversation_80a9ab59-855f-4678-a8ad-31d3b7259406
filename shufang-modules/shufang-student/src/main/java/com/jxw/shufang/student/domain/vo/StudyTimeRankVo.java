package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 数据统计-学习时长排行榜 studyTimeRank
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
public class StudyTimeRankVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 情况列表
     */
    private List<StudyTimeRankEntity> studyTimeRankEntities;

    /**
     * 我的排名
     */
    private Integer myRankNo;

    /**
     * 我的性别（0男 1女）
     */
    private String mySex;

    /**
     * 数据统计-学习时长排行榜-实体 StudyTimeRankEntity
     *
     * <AUTHOR>
     * @date 2024-05-13
     */
    @Data
    public static class StudyTimeRankEntity {

        public StudyTimeRankEntity(){

        }

        public StudyTimeRankEntity(Long id, Long value,String durType, Integer rank) {
            this.studentId = id;
            if("D".equals(durType)){
                this.todayCumulativeStudyDur = null != value?(int)(value/60):0;
            }else if("W".equals(durType)){
                this.weekCumulativeStudyDur = null != value?(int)(value/60):0;
            }else if("M".equals(durType)){
                this.monthCumulativeStudyDur = null != value?(int)(value/60):0;
            }
            this.rankNo = rank;
//            System.out.println("排名："+rank);
        }

        /**
         * 学员姓名
         */
        private String studentName;

        /**
         * 性别（0男 1女）
         */
        private String studentSex;

        /**
         * 学员头像
         */
        private String realAvatar;

        /**
         * 排名
         */
        private Integer rankNo;

        /**
         * 今日累计学习时长（分钟）
         */
        private Integer todayCumulativeStudyDur;

        /**
         * 本周累计学习时长（分钟）
         */
        private Integer weekCumulativeStudyDur;

        /**
         * 本月累计学习时长（分钟）
         */
        private Integer monthCumulativeStudyDur;

        /**
         * 连续学习天数（天）
         */
        private Integer continuousStudyDays;

        /**
         * 会员ID
         */
        private Long studentId;

        /**
         * 会员ID对应的系统ID
         */
        private Long sysUserId;

        /**
         * 数据状态：1提升 2保持 3降低
         */
        private Integer dataStatus;

    }
}
