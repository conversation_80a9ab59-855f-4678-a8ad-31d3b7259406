package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordInfoBo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordInfoVo;
import com.jxw.shufang.student.service.IStudentAiCourseRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 记录分配课程
 * 前端访问路由地址为:/student/management/studentAiCourseRecordInfo
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studentAiCourseRecordInfo")
public class StudentAiCourseRecordInfoController extends BaseController {

    private final IStudentAiCourseRecordInfoService studentAiCourseRecordInfoService;

    /**
     * 查询记录分配课程列表
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:list")
    @GetMapping("/list")
    public TableDataInfo<StudentAiCourseRecordInfoVo> list(StudentAiCourseRecordInfoBo bo, PageQuery pageQuery) {
        return studentAiCourseRecordInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出记录分配课程列表
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:export")
    @Log(title = "记录分配课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentAiCourseRecordInfoBo bo, HttpServletResponse response) {
        List<StudentAiCourseRecordInfoVo> list = studentAiCourseRecordInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "记录分配课程", StudentAiCourseRecordInfoVo.class, response);
    }

    /**
     * 获取记录分配课程详细信息
     *
     * @param studentAiCourseRecordInfoId 主键
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:query")
    @GetMapping("/{studentAiCourseRecordInfoId}")
    public R<StudentAiCourseRecordInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentAiCourseRecordInfoId) {
        return R.ok(studentAiCourseRecordInfoService.queryById(studentAiCourseRecordInfoId));
    }

    /**
     * 新增记录分配课程
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:add")
    @Log(title = "记录分配课程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentAiCourseRecordInfoBo bo) {
        return toAjax(studentAiCourseRecordInfoService.insertByBo(bo));
    }

    /**
     * 修改记录分配课程
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:edit")
    @Log(title = "记录分配课程", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentAiCourseRecordInfoBo bo) {
        return toAjax(studentAiCourseRecordInfoService.updateByBo(bo));
    }

    /**
     * 删除记录分配课程
     *
     * @param studentAiCourseRecordInfoIds 主键串
     */
    @SaCheckPermission("student:studentAiCourseRecordInfo:remove")
    @Log(title = "记录分配课程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentAiCourseRecordInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentAiCourseRecordInfoIds) {
        return toAjax(studentAiCourseRecordInfoService.deleteWithValidByIds(List.of(studentAiCourseRecordInfoIds), true));
    }
}
