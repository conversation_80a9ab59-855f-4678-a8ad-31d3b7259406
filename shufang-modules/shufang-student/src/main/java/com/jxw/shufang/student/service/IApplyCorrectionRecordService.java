package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 申请批改记录Service接口
 *
 *
 * @date 2024-05-07
 */
public interface IApplyCorrectionRecordService {

    /**
     * 查询申请批改记录
     */
    ApplyCorrectionRecordVo queryById(Long applyCorrectionRecordId);

    /**
     * 查询申请批改记录列表
     */
    TableDataInfo<ApplyCorrectionRecordVo> queryPageList(ApplyCorrectionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询申请批改记录列表
     */
    List<ApplyCorrectionRecordVo> queryList(ApplyCorrectionRecordBo bo);

    /**
     * 新增申请批改记录
     */
    Boolean insertByBo(ApplyCorrectionRecordBo bo);

    /**
     * 修改申请批改记录
     */
    Boolean updateByBo(ApplyCorrectionRecordBo bo);

    /**
     * 校验并批量删除申请批改记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询最后一条记录
     *
     * @param applyCorrectionRecordBo 应用更正记录bo
     *
     * @date 2024/05/08 12:39:52
     */
    ApplyCorrectionRecordVo queryLastRecord(ApplyCorrectionRecordBo applyCorrectionRecordBo);

    boolean updateApplyResult(Long applyCorrectionRecordId, String applyResult);

    /**
     * 计数
     *
     * @param bo bo
     *
     * @date 2024/06/06 10:34:16
     */
    Long count(ApplyCorrectionRecordBo bo);

    boolean updateBatchById(List<ApplyCorrectionRecordBo> updateList);

    /**
     * 当前的申请记录是否可见（当前系统只查询当天凌晨三点之后的记录）
     *
     * @param applyCorrectionRecordBo
     * @return
     */
    boolean wheatherApplyRecordVisiable(ApplyCorrectionRecordVo applyCorrectionRecordBo);
}
