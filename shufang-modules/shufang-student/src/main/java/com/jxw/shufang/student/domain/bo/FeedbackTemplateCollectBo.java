package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.FeedbackTemplateCollect;

/**
 * 反馈模板收藏业务对象 feedback_template_collect
 *
 *
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FeedbackTemplateCollect.class, reverseConvertGenerate = false)
public class FeedbackTemplateCollectBo extends BaseEntity {

    /**
     * 反馈模板收藏id
     */
    @NotNull(message = "反馈模板收藏id不能为空", groups = { EditGroup.class })
    private Long feedbackTemplateCollectId;

    /**
     * 反馈模板id
     */
    @NotNull(message = "反馈模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long feedbackTemplateId;


}
