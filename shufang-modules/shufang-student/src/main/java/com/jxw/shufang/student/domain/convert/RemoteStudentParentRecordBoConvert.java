package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentParentRecordBo;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteStudentParentRecordBoConvert extends BaseMapper<RemoteStudentParentRecordBo, StudentParentRecordBo> {
}
