package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.student.domain.vo.StuDailyShareStatisticVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.bo.FeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.FeedbackRecordVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IFeedbackRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 反馈记录
 * 前端访问路由地址为:/student/management/feedbackRecord
 *
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/feedbackRecord")
public class FeedbackRecordController extends BaseController {

    private final IFeedbackRecordService feedbackRecordService;

    /**
     * 查询反馈记录列表
     */
    @SaCheckPermission("student:feedbackRecord:list")
    @GetMapping("/list")
    public TableDataInfo<FeedbackRecordVo> list(FeedbackRecordBo bo, PageQuery pageQuery) {
        return feedbackRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出反馈记录列表
     */
    @SaCheckPermission("student:feedbackRecord:export")
    @Log(title = "反馈记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FeedbackRecordBo bo, HttpServletResponse response) {
        List<FeedbackRecordVo> list = feedbackRecordService.queryList(bo);
        if (CollUtil.isNotEmpty(list)){
            for (FeedbackRecordVo feedbackRecordVo : list) {
                RemoteStaffVo consultantInfo = feedbackRecordVo.getConsultantInfo();
                if (consultantInfo != null&&consultantInfo.getUser()!=null){
                    feedbackRecordVo.setConsultantName(consultantInfo.getUser().getNickName());
                }
                StudentVo student = feedbackRecordVo.getStudent();
                if (student != null){
                    feedbackRecordVo.setStudentName(student.getNameWithPhone());
                }
                RemoteStaffVo createStaffInfo = feedbackRecordVo.getCreateStaffInfo();
                if (createStaffInfo != null&&createStaffInfo.getUser()!=null){
                    feedbackRecordVo.setCreateStaffName(createStaffInfo.getUser().getNickName());
                }
                if (feedbackRecordVo.getFeedbackStartDate() != null&&feedbackRecordVo.getFeedbackEndDate()!=null){
                    feedbackRecordVo.setFeedbackDateKLimit(DateUtils.dateTime(feedbackRecordVo.getFeedbackStartDate())+"-"+DateUtils.dateTime(feedbackRecordVo.getFeedbackEndDate()));
                }
            }
        }
        ExcelUtil.exportExcel(list, "反馈记录", FeedbackRecordVo.class, response);
    }

    /**
     * 获取反馈记录详细信息
     *
     * @param feedbackRecordId 主键
     */
    @SaCheckPermission("student:feedbackRecord:query")
    @GetMapping("/{feedbackRecordId}")
    public R<FeedbackRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long feedbackRecordId) {
        return R.ok(feedbackRecordService.queryById(feedbackRecordId, false,false));
    }

    /**
     * 查询最近一次的反馈记录
     *
     * @param feedbackRecordId 如果传了，就查询该条记录的上一条记录，如果没传，就查询最新的记录
     * @param studentId        学生id
     * @return 统计结果
     */
    @GetMapping("/queryLatestStuDailyShare")
    public R<StuDailyShareStatisticVo> queryStuDailyShareStatistic(
        @RequestParam(value = "feedbackRecordId", required = false) Long feedbackRecordId,
        @RequestParam("studentId") Long studentId) {
        FeedbackRecordVo feedbackRecordVo = feedbackRecordService.queryLatestFeedbackRecord(feedbackRecordId, studentId);
        if (feedbackRecordVo == null) {
            return R.ok();
        }
        return R.ok(feedbackRecordService.queryStuDailyShareStatistic(feedbackRecordVo));
    }

    /**
     * 新增反馈记录
     */
    @SaCheckPermission("student:feedbackRecord:add")
    @Log(title = "反馈记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FeedbackRecordBo bo) {
        return toAjax(feedbackRecordService.insertByBo(bo));
    }

    /**
     * 修改反馈记录
     */
    @SaCheckPermission("student:feedbackRecord:edit")
    @Log(title = "反馈记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FeedbackRecordBo bo) {
        return toAjax(feedbackRecordService.updateByBo(bo));
    }

    /**
     * 删除反馈记录
     *
     * @param feedbackRecordIds 主键串
     */
    @SaCheckPermission("student:feedbackRecord:remove")
    @Log(title = "反馈记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{feedbackRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] feedbackRecordIds) {
        return toAjax(feedbackRecordService.deleteWithValidByIds(List.of(feedbackRecordIds), true));
    }


    @SaCheckPermission("student:feedbackRecord:edit")
    @Log(title = "反馈记录-重新发送通知", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/resend")
    public R<Void> resend(Long feedbackRecordId) {
        feedbackRecordService.notifyParent(feedbackRecordId);
        return R.ok();
    }

    /**
     * 修改反馈状态
     */
    @GetMapping("/changeStatus")
    public R<Void> changeStatus(@NotNull(message = "反馈记录id不能为空") @RequestParam Long feedbackRecordId) {
        feedbackRecordService.changeStatus(feedbackRecordId);
        return R.ok();
    }

}
