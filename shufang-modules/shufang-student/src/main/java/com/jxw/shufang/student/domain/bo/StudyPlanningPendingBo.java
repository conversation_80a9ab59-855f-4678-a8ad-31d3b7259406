package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanningPending;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 需学习规划学生业务对象 study_planning_pending
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanningPending.class, reverseConvertGenerate = false)
public class StudyPlanningPendingBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学生ID
     */
    @NotNull(message = "学生ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 分支机构ID
     */
    private Long branchId;

    /**
     * 顾问ID
     */
    private Long consultantId;

    /**
     * 模式类型:1-春秋模式,2-寒暑模式
     */
    @NotNull(message = "模式类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer modeType;

    /**
     * 计划开始日期
     */
    @NotNull(message = "计划开始日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @NotNull(message = "计划结束日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    /**
     * 反馈状态:0-待反馈,1-已反馈
     */
    private Integer feedbackStatus;

    /**
     * 规划状态:0-待规划,1-已规划
     */
    private Integer planningStatus;

    /**
     * 应规划时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedPlanningTime;

    /**
     * 应反馈时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectedFeedbackTime;

    /**
     * 实际规划时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualPlanningTime;

    /**
     * 实际反馈时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualFeedbackTime;

    /**
     * 学习规划状态（组合状态）多选:
     * 0-待规划且未超时, 1-待规划且超时, 2-已规划且未超时, 3-已规划且超时
     */
    private List<Integer> combinedPlanningStatus;

    /**
     * 学习规划反馈状态（组合状态）多选:
     * 0-待反馈且未超时, 1-待反馈且超时, 2-已反馈且未超时, 3-已反馈且超时
     */
    private List<Integer> combinedFeedbackStatus;

    /**
     * 查询条件 - 学生ID列表
     */
    private List<Long> studentIds;

    /**
     * 查询条件 - 顾问ID列表
     */
    private List<Long> consultantIds;


    /**
     * 查询条件 - 反馈状态列表
     */
    private List<Integer> feedbackStatusList;

    /**
     * 查询条件 - 规划状态列表
     */
    private List<Integer> planningStatusList;



    /**
     * 查询条件 - 会员姓名
     */
    private String studentName;

    /**
     * 查询条件 - 顾问姓名（sys_user表中的nickName）
     */
    private String consultantName;

}
