package com.jxw.shufang.student.dubbo;


import com.jxw.shufang.student.config.StudentIntroduceConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudentTypeService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentTypeBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentTypeVo;
import com.jxw.shufang.student.domain.bo.StudentTypeBo;
import com.jxw.shufang.student.domain.vo.StudentTypeVo;
import com.jxw.shufang.student.service.IStudentTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentTypeServiceImpl implements RemoteStudentTypeService {

    private final IStudentTypeService studentTypeService;

    private final StudentIntroduceConfig studentIntroduceConfig;

    @Override
    public List<RemoteStudentTypeVo> queryStudentTypeList(RemoteStudentTypeBo bo) {
        StudentTypeBo convert = MapstructUtils.convert(bo, StudentTypeBo.class);
        List<StudentTypeVo> studentTypeVos = studentTypeService.queryList(convert);
        return MapstructUtils.convert(studentTypeVos, RemoteStudentTypeVo.class);
    }

    @Override
    public RemoteStudentTypeVo getExperienceStudentType(boolean ignoreDataPermission) {
        StudentTypeVo studentTypeVo = null;
        if (ignoreDataPermission){
            studentTypeVo = DataPermissionHelper.ignore(studentTypeService::getExperienceStudentType);
        }else {
            studentTypeVo = studentTypeService.getExperienceStudentType();
        }
        return MapstructUtils.convert(studentTypeVo, RemoteStudentTypeVo.class);
    }

    @Override
    public Long getExperienceStudentTypeId() {
        return studentIntroduceConfig.getExperienceStudentTypeId();
    }
}
