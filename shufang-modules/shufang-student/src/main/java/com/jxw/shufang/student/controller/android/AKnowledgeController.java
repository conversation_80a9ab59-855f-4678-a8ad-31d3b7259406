package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeNoteVo;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/knowledge")
public class AKnowledgeController {
    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;

    @GetMapping("/note/info")
    public R<RemoteKnowledgeNoteVo> getKnowledgeNoteInfo(@NotNull(message = "知识点ID不能为空") Integer knowledgeId) {
        return R.ok(remoteKnowledgeService.getKnowledgeNoteInfo(knowledgeId));
    }

}
