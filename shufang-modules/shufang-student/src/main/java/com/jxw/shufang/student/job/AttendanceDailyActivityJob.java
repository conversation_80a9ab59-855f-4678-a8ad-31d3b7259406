package com.jxw.shufang.student.job;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.constant.FeedbackStatusConstant;
import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;
import com.jxw.shufang.student.service.AttendanceDailyActivityFeedbackRecordService;
import com.jxw.shufang.student.service.AttendanceDailyActivityService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AttendanceDailyActivityJob {

    private final AttendanceDailyActivityService attendanceDailyActivityService;

    private final AttendanceDailyActivityFeedbackRecordService attendanceDailyActivityFeedbackRecordService;

    @XxlJob("attendanceDailyActivityJob")
    public void execute() {
        log.info("=========【会员每日打卡登录情况表】 更新每日反馈状态 =========");
        // 查询所有未反馈的记录（不分页）
        List<AttendanceDailyActivityVo> attendanceDailyActivityVos = attendanceDailyActivityService.queryListByFeedbackStatus(FeedbackStatusConstant.NOT_FEEDBACK);
        if (CollUtil.isEmpty(attendanceDailyActivityVos)) {
            log.info("无未反馈的记录");
            return;
        }
        // 更新反馈状态
        List<AttendanceDailyActivityFeedbackRecord> list = new ArrayList<>();
        attendanceDailyActivityVos.forEach(attendanceDailyActivityVo -> {
            AttendanceDailyActivityFeedbackRecord attendanceDailyActivityFeedbackRecord = new AttendanceDailyActivityFeedbackRecord();
            // 计算最新的反馈状态
            Integer feedbackStatus = attendanceDailyActivityFeedbackRecordService.calculateFeedbackStatus(attendanceDailyActivityVo.getRecordDate(),false);
            // 只更新状态为超时未反馈的
            if (FeedbackStatusConstant.OVERTIME_NOT_FEEDBACK.equals(feedbackStatus)) {
                attendanceDailyActivityFeedbackRecord.setAttendanceDailyActivityFeedbackRecordId(attendanceDailyActivityVo.getAttendanceDailyActivityFeedbackRecordId());
                attendanceDailyActivityFeedbackRecord.setFeedbackStatus(feedbackStatus);
                list.add(attendanceDailyActivityFeedbackRecord);
            }
        });
        try {
            if (CollUtil.isNotEmpty(list)) {
                attendanceDailyActivityFeedbackRecordService.updateBatchById(list);
            }
            log.info("=========【会员每日打卡登录情况表】 更新每日反馈状态完成=========");
        } catch (Exception e) {
            log.error("【会员每日打卡登录情况表】 更新每日反馈状态异常", e);
        }
    }
}
