package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.AnswerResultTypeEnum;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.service.ICourseService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 课程（课程包含章节）--小程序端
 * 前端访问路由地址为:/student/miniProgram/course
 *
 *
 * @date 2024-03-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/course")
public class MpCourseController extends BaseController {

    private final ICourseService courseService;

    private final DictService dictService;


    /**
     * 获取答案结果类型
     *
     *
     * @date 2024/05/09 09:24:57
     */
    @GetMapping("/getAnswerResultType")
    public R<List<Map<String, String>>> getAnswerResultType() {
        List<Map<String, String>> res = new ArrayList<>();
        Map<String, String> answerResultType = dictService.getAllDictByDictType("answer_result_type");
        for (AnswerResultTypeEnum value : AnswerResultTypeEnum.values()) {
            Map<String, String> resMap = new HashMap<>();
            String type = value.getType();
            resMap.put("type", type);
            if (answerResultType.containsKey(type)) {
                resMap.put("name", answerResultType.get(type));
            } else {
                resMap.put("name", value.getDefaultName());
            }
            res.add(resMap);
        }
        return R.ok(res);
    }

    /**
     * 查询课程对应的课程外部资源（讲义，练习，测试，练习带解析，测试带解析）
     *
     * @param courseId              课程Id
     * @param resourceType          测试 TEST    练习 PRACTICE 练习带解析 PRACTICE_ANALYSIS 测试带解析 TEST_ANALYSIS
     *
     * @date 2024/05/09 03:20:51
     */
    @GetMapping("/getExtResource")
    public R<RemoteKnowledgeResourceVo> getExtResource(@NotNull(message = "课程Id不能为空") Long courseId, @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType) {
        return R.ok(courseService.getCourseResourceByCourseId(courseId, resourceType));
    }

    /**
     * 查询课程详情
     */
    @GetMapping("/query/{courseId}")
    public R<CourseVo> queryFullInfoById(@PathVariable @NotNull(message = "课程Id不能为空") Long courseId) {
        CourseVo courseVo = courseService.queryById(courseId);
        if (courseVo == null) {
            return R.fail("课程不存在");
        }
        courseService.putTopmostCourseInfo(List.of(courseVo),true);
        if (courseVo.getTopmostCourse() != null){
            courseService.putCourseDetail(List.of(courseVo.getTopmostCourse()),false);

        }
        return R.ok(courseVo);
    }


}
