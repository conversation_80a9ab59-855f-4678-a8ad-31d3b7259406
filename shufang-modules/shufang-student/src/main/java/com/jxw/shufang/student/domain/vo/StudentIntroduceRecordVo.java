package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentIntroduceRecord.class)
public class StudentIntroduceRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员介绍记录ID
     */
    @ExcelProperty(value = "转介绍记录ID")
    private Long introduceRecordId;
    /**
     * 介绍人ID
     */
    private Long introduceStudentId;
    /**
     * 介绍人姓名
     */
    @ExcelProperty(value = "介绍人")
    private String introduceStudentName;
    /**
     * 新会员ID
     */
    private Long studentId;
    /**
     * 会员姓名
     */
    @ExcelProperty(value = "会员姓名")
    private String studentName;
    /**
     * 年级
     */
    @ExcelProperty(value = "年级")
    private String studentGrade;
    /**
     * 入学时间
     */
    @ExcelProperty(value = "入学时间")
    private Date createTime;
    /**
     * 所属门店id
     */
    private Long branchId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 购买会员类型
     */
    @ExcelProperty(value = "购买套餐")
    private String branchAuthTypeName;
    /**
     * 是否发放了优惠
     */
    private Boolean doReferent;
    /**
     * 赠送的优惠额度
     */
    @ExcelProperty(value = "优惠额度")
    private BigDecimal preferentialAmount;
    /**
     * 优惠额度解冻时间（优惠可使用时间）
     */
    @ExcelProperty(value = "优惠额度可使用时间")
    private Date unFrozenTime;
    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 所属门店名称
     */
    @ExcelProperty(value = "所属门店")
    private String branchName;
}
