package com.jxw.shufang.student.domain.vo;

import com.jxw.shufang.student.domain.StudentAnswerRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@Data
@AutoMapper(target = StudentAnswerRecord.class)
public class StudentAnswerRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long studentAnswerRecordId;

    /**
     * 用户id
     */
    private Long studentId;

    /**
     * 试卷ID
     */
    private Long testPaperId;


    /**
     * 题目ID
     */
    private Long questionId;
    private Long studentPaperRecordId;
    /**
     * 0：false（否->不是错误->正确）
     * 1：true(是->是错误->错误)
     */
    private Integer isMistake;

    private String answer;
    private Integer stayTime;
    /**
     * 来源
     */
    private String source;

    private Date startTime;

    private Date finishTime;


}
