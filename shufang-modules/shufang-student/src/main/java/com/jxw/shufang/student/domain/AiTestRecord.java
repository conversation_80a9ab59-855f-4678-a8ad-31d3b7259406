package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * ai测验记录对象 ai_test_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_test_record")
public class AiTestRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ai测验记录id
     */
    @TableId(value = "ai_test_record_id")
    private Long aiTestRecordId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程资源id
     */
    private Long courseResourceId;

    /**
     * 资源内容（oss_id）
     */
    private Long resourceContent;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对 全错 半错）
     */
    private String answerResult;

    /**
     * 作答图片（oss_id）
     */
    private Long answerImg;


}
