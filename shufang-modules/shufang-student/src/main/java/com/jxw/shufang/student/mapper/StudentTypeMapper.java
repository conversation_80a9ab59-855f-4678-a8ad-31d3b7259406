package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudentType;
import com.jxw.shufang.student.domain.vo.StudentTypeVo;

import java.util.List;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）Mapper接口
 *
 *
 * @date 2024-03-01
 */
public interface StudentTypeMapper extends BaseMapperPlus<StudentType, StudentTypeVo> {

    @Override
    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "create_dept"),
    //    @DataColumn(key = "userName", value = "create_by")
    //})
    //@BranchColumn(key = "deptName", value = "create_dept")
    List<StudentType> selectList(IPage<StudentType> page, @Param(Constants.WRAPPER) Wrapper<StudentType> queryWrapper);


}
