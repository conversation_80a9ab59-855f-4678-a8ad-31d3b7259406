package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.IPaperCollectionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 试卷收藏
 * 前端访问路由地址为:/student/android/paperCollection
 *
 *
 * @date 2024-05-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paperCollection")
public class APaperCollectionController extends BaseController {

    private final IPaperCollectionService paperCollectionService;


    /**
     * 收藏或取消收藏试卷（会自动根据实际情况取反）
     * @param paperId 试卷id
     */
    @RepeatSubmit
    @PostMapping("/collectPaper/{paperId}")
    public R<Void> collectPaper(@PathVariable Long paperId) {
        paperCollectionService.collectPaper(paperId, LoginHelper.getStudentId());
        return R.ok();
    }
}
