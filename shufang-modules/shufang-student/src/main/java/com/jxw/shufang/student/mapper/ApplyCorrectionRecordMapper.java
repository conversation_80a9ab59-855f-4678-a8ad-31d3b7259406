package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.ApplyCorrectionRecord;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;

/**
 * 申请批改记录Mapper接口
 *
 *
 * @date 2024-05-07
 */
public interface ApplyCorrectionRecordMapper extends BaseMapperPlus<ApplyCorrectionRecord, ApplyCorrectionRecordVo> {

    Page<ApplyCorrectionRecordVo> selectRecordVoPage(@Param("page") Page<ApplyCorrectionRecord> build,@Param(Constants.WRAPPER) QueryWrapper<ApplyCorrectionRecord> lqw);
}
