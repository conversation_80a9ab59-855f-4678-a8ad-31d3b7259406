package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.bo.PracticeRecordBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CorrectionRecordInfoBoConvertPracticeRecordBo extends BaseMapper<CorrectionRecordInfoBo, PracticeRecordBo> {

    PracticeRecordBo convert(CorrectionRecordInfoBo source);
}
