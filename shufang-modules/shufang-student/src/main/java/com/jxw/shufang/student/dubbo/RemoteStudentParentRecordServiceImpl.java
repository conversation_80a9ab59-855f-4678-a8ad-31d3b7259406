package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.student.api.RemoteStudentParentRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentParentRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentParentRecordVo;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import com.jxw.shufang.student.domain.vo.StudentParentRecordVo;
import com.jxw.shufang.student.service.IStudentParentRecordService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentParentRecordServiceImpl implements RemoteStudentParentRecordService {

    private final IStudentParentRecordService studentParentRecordService;

    @DubboReference
    private RemoteStudentParentRecordService remoteStudentParentRecordService;

    @Override
    public List<RemoteStudentParentRecordVo> queryList(RemoteStudentParentRecordBo bo) {
        StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
        List<StudentParentRecordVo> studentParentRecordVos = studentParentRecordService.queryList(studentParentRecordBo);
        return MapstructUtils.convert(studentParentRecordVos, RemoteStudentParentRecordVo.class);
    }

    @Override
    public Boolean insertByBo(RemoteStudentParentRecordBo bo) {
        StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
        return studentParentRecordService.insertByBo(studentParentRecordBo);
    }

    @Override
    public Boolean updateByBo(RemoteStudentParentRecordBo bo) {
        StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
        return studentParentRecordService.updateByBo(studentParentRecordBo);
    }

    @Override
    public Boolean bind(Long studentId,String openId) {

        if(null == studentId){
            throw new ServiceException("找不到绑定的学生!");
        }

        if(null == openId){
            throw new ServiceException("获取微信授权的openId失败!");
        }

        {
            //判断是否存在有openId

            RemoteStudentParentRecordBo bo = new RemoteStudentParentRecordBo();
            bo.setParentWechatOpenId(openId);
            StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
            List<StudentParentRecordVo> voList = studentParentRecordService.queryList(studentParentRecordBo);
            if(null != voList && voList.size()>0){ }else {
                bo.setParentWechatOpenId(openId);
                remoteStudentParentRecordService.insertByBo(bo);
            }

        }

        {
            RemoteStudentParentRecordBo bo = new RemoteStudentParentRecordBo();
            bo.setStudentId(studentId);
            bo.setParentWechatOpenId(openId);
            StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
            List<StudentParentRecordVo> voList = studentParentRecordService.queryList(studentParentRecordBo);
            if(null != voList && voList.size()>0){
                //已经绑定了

                return true;
            }
        }

        RemoteStudentParentRecordBo bo = new RemoteStudentParentRecordBo();
        bo.setParentWechatOpenId(openId);
        StudentParentRecordBo studentParentRecordBo = MapstructUtils.convert(bo, StudentParentRecordBo.class);
        List<StudentParentRecordVo> voList = studentParentRecordService.queryList(studentParentRecordBo);
        if(null != voList && voList.size()>0){
            //根据创建时间排序，取最新一条
            Collections.sort(voList, (o1, o2) -> o1.getCreateTime().compareTo(o2.getCreateTime()));
            StudentParentRecordVo vo = voList.get(voList.size()-1);
            //是否已经绑定了其他学生
            if(null != vo.getStudentId()){
                //复制并且新增一条数据
                studentParentRecordBo.setParentWechatUnionId(vo.getParentWechatUnionId());
                studentParentRecordBo.setParentWechatNickname(vo.getParentWechatNickname());
                studentParentRecordBo.setParentWechatImg(vo.getParentWechatImg());
                studentParentRecordBo.setParentWechatNo(vo.getParentWechatNo());
                studentParentRecordBo.setStudentId(studentId);
                return studentParentRecordService.insertByBo(studentParentRecordBo);
            }else {
                //更新数据
                studentParentRecordBo.setStudentParentRecordId(vo.getStudentParentRecordId());
                studentParentRecordBo.setStudentId(studentId);
                return studentParentRecordService.updateByBo(studentParentRecordBo);
            }
        }else {
            //新增一条数据
            studentParentRecordBo.setStudentId(studentId);
            return studentParentRecordService.insertByBo(studentParentRecordBo);
        }
    }

}
