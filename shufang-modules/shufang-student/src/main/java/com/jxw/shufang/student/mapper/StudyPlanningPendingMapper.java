package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyPlanningPending;
import com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 需学习规划学生Mapper接口
 *
 * @date 2024-06-14
 */
public interface StudyPlanningPendingMapper extends BaseMapperPlus<StudyPlanningPending, StudyPlanningPendingVo> {

    /**
     * 连表查询需学习规划学生列表（包含学生信息和顾问信息）
     */
    Page<StudyPlanningPendingVo> selectVoPageWithJoin(Page<StudyPlanningPending> page,
                                                       @Param(Constants.WRAPPER) Wrapper<StudyPlanningPending> wrapper);

    /**
     * 连表查询需学习规划学生列表（包含学生信息和顾问信息）
     */
    List<StudyPlanningPendingVo> selectVoListWithJoin(@Param(Constants.WRAPPER) Wrapper<StudyPlanningPending> wrapper);

}
