package com.jxw.shufang.student.domain.vo;

import com.jxw.shufang.student.domain.AttendanceUser;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 考勤关联用户视图对象 attendance_user
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AttendanceUser.class)
public class AttendanceUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long attendanceUserId;

    /**
     * 考勤机域名或IP
     */
    @ExcelProperty(value = "考勤机域名或IP")
    private String ip;

    /**
     * 系统用户id
     */
    @ExcelProperty(value = "系统用户id")
    private Long userId;


}
