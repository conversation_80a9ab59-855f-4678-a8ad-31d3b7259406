package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）对象 student_type
 *
 *
 * @date 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_type")
public class StudentType extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员类型id
     */
    @TableId(value = "student_type_id")
    private Long studentTypeId;

    /**
     * 会员类型名称
     */
    private String studentTypeName;

    /**
     * 排序
     */
    private Long studentTypeSort;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
