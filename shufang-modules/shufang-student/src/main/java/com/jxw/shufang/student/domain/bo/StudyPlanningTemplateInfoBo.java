package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanningTemplateInfo;

import java.util.Date;
import java.util.List;

/**
 * 学习规划模板详情业务对象 study_planning_template_info
 *
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanningTemplateInfo.class, reverseConvertGenerate = false)
public class StudyPlanningTemplateInfoBo extends BaseEntity {

    /**
     * 学习规划模板详情id
     */
    //@NotNull(message = "学习规划模板详情id不能为空", groups = { EditGroup.class })
    private Long studyPlanningTemplateInfoId;

    /**
     * 学习规划模板id
     */
    //@NotNull(message = "学习规划模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningTemplateId;

    /**
     * 课程id（章节）
     */
    @NotNull(message = "课程id（章节）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 课程名称(最顶层的课程id)
     */
    //@NotNull(message = "课程名称(最顶层的课程id)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long topmostCourseId;

    /**
     * 学习开始时间
     */
    //@NotNull(message = "学习开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    //@NotNull(message = "学习结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date studyEndTime;

    private Boolean withCourseInfo;

    private Boolean withTopmostCourseInfo;

    private List<Long> topmostCourseIdList;


}
