package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>考勤机 获取人员信息返回的实体</p>
 * <p></p>
 * <p>此接口提供给获取人员信息使用</p>
 * <p>可根据人员编号或部门编号获取</p>
 * <p>可查询多个</p>
 * <p>不支持模糊查询</p>
 * <p>无参数则返回所有人员信息</p>
 * <p></p>
 * <p>返回码说明</p>
 * <p>返回码	描述</p>
 * <p>0    请求成功</p>
 * <p>1    请求失败</p>
 *
 * <AUTHOR>
 * @date 2024-05-18
 */
@Data
@ExcelIgnoreUnannotated
public class EZKEcoEmployeeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 名字
     */
    private String name;

    /**
     * 部门编号
     */
    private String deptnumber;

    /**
     * 部门名字
     */
    private String deptname;

    /**
     * 办公电话
     */
    private String telephone;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 人员卡号
     */
    private String Card;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 人员是否在离职。0为在职，1为离职
     */
    private String status;
}
