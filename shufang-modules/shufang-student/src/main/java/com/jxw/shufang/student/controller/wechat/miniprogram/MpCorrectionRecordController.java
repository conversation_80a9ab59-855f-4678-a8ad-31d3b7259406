package com.jxw.shufang.student.controller.wechat.miniprogram;

import com.jxw.shufang.student.domain.bo.ResetStudentAnswerRecordBo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.CorrectionQuestionRecordVo;
import com.jxw.shufang.student.service.ICorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 批改记录---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/correctionRecord
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/correctionRecord")
public class MpCorrectionRecordController extends BaseController {

    private final ICorrectionRecordService correctionRecordService;


    /**
     * 提交批改记录
     */
    @PostMapping()
    @RepeatSubmit
    @Log(title = "提交批改记录--小程序端", businessType = BusinessType.INSERT)
    public R<BigDecimal> submitCorrectionRecord(@RequestBody @Validated(AddGroup.class) CorrectionRecordBo correctionRecordBo, Long studyPlanningRecordId) {
        correctionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STAFF);
        correctionRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        return R.ok(correctionRecordService.insertByBo(correctionRecordBo));
    }

    /**
     * 查询批改的题目列表列表
     *
     * @param studyPlanningRecordId 研究计划记录id
     * @param correctionType        批改类型 1=测试,2=练习
     */
    @GetMapping("/queryQuestionRecord")
    public R<CorrectionQuestionRecordVo> queryQuestionRecord(@NotNull(message = "学习规划记录ID不能为空") Long studyPlanningRecordId,
                                                             @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(correctionRecordService.queryQuestionRecord(studyPlanningRecordId, correctionType));
    }


    /**
     * 重置学生作答记录
     */
    @PostMapping("/resetStudentAnswerRecord")
    public R<Void> resetStudentAnswerRecord(@RequestBody  ResetStudentAnswerRecordBo bo) {
        boolean result = correctionRecordService.resetStudentAnswerRecord(bo.getCorrectionRecordIdList(),bo.getType());
        return toAjax(result);

    }


}
