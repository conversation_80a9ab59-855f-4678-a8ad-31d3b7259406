package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordVo;
import com.jxw.shufang.student.service.IAiCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * ai批改记录
 * 前端访问路由地址为:/student/aiCorrectionRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiCorrectionRecord")
public class AiCorrectionRecordController extends BaseController {

    private final IAiCorrectionRecordService aiCorrectionRecordService;

    /**
     * 查询ai批改记录列表
     */
    @SaCheckPermission("student:aiCorrectionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiCorrectionRecordVo> list(AiCorrectionRecordBo bo, PageQuery pageQuery) {
        return aiCorrectionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai批改记录列表
     */
    @SaCheckPermission("student:aiCorrectionRecord:export")
    @Log(title = "ai批改记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiCorrectionRecordBo bo, HttpServletResponse response) {
        List<AiCorrectionRecordVo> list = aiCorrectionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai批改记录", AiCorrectionRecordVo.class, response);
    }

    /**
     * 获取ai批改记录详细信息
     *
     * @param aiCorrectionRecordId 主键
     */
    @SaCheckPermission("student:aiCorrectionRecord:query")
    @GetMapping("/{aiCorrectionRecordId}")
    public R<AiCorrectionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiCorrectionRecordId) {
        return R.ok(aiCorrectionRecordService.queryById(aiCorrectionRecordId));
    }

    /**
     * 新增ai批改记录
     */
    @SaCheckPermission("student:aiCorrectionRecord:add")
    @Log(title = "ai批改记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<BigDecimal> add(@Validated(AddGroup.class) @RequestBody AiCorrectionRecordBo bo) {
        BigDecimal bigDecimal = aiCorrectionRecordService.insertByBo(bo);
        return R.ok(bigDecimal);
    }

    /**
     * 修改ai批改记录
     */
    @SaCheckPermission("student:aiCorrectionRecord:edit")
    @Log(title = "ai批改记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiCorrectionRecordBo bo) {
        return toAjax(aiCorrectionRecordService.updateByBo(bo));
    }

    /**
     * 删除ai批改记录
     *
     * @param aiCorrectionRecordIds 主键串
     */
    @SaCheckPermission("student:aiCorrectionRecord:remove")
    @Log(title = "ai批改记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiCorrectionRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiCorrectionRecordIds) {
        return toAjax(aiCorrectionRecordService.deleteWithValidByIds(List.of(aiCorrectionRecordIds), true));
    }
}
