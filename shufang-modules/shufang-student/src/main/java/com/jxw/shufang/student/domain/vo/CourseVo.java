package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVideoVo;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.system.api.domain.vo.RemoteAttrRelationResultVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 课程（课程包含章节）视图对象 course
 *
 *
 * @date 2024-03-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Course.class)
public class CourseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 祖先节点（最顶层默认0）
     */
    @ExcelProperty(value = "祖先节点", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "最=顶层默认0")
    private String ancestors;


    /**
     * 年级（对应字典值 course_grade）（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "年级", converter = ExcelDictConvert.class)
    private String grade;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String gradeName;

    /**
     * 学段（对应字典值 course_stage）（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "学段", converter = ExcelDictConvert.class)
    private String stage;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stageName;

    /**
     * 归属学科（对应字典值 course_affiliation_subject）（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "归属学科")
    //@ExcelDictFormat(readConverterExp = "对=应字典值")
    private String affiliationSubject;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String affiliationSubjectName;


    /**
     * 与外部资源知识点id关联（只有最叶子节点的课程才会存）
     */
    @ExcelProperty(value = "知识点ID")
    private Long knowledgeId;

    /**
     * 课程名称
     */
    @ExcelProperty(value = "课程名称")
    private String courseName;

    /**
     * 课程简介（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "课程简介")
    private String courseIntroduction;

    /**
     * 课程代码（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "课程代码")
    private String courseNo;

    /**
     * 课程缩略图（oss_id）（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "课程缩略图", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id")
    private Long courseThumbnail;

    /**
     * 课程父节点id（顶级课程默认为0）
     */
    @ExcelProperty(value = "课程父节点id")
    private Long courseParentId;

    /**
     * 顶级课程id
     */
    private Long topCourseId;

    /**
     * 类型(1课程 2章 3节 4小节 5小小节 以此类推)
     */
    @ExcelProperty(value = "类型(1课程 2章 3节 4小节 5小小节 以此类推)")
    private Integer courseType;

    /**
     * 课程来源（对应字典值 course_source）（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "课程来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String courseSource;

    /**
     * 课程专题，对应字典course_special_topic（只有最顶层的课程才会存）
     */
    @ExcelProperty(value = "课程专题")
    //@ExcelDictFormat(readConverterExp = "对=应字典值")
    private String specialTopic;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Integer sort;

    /**
     * 属性相关的
     */
    private RemoteAttrRelationResultVo attrRelationResult;

    /**
     * 课程详情
     */
    private String courseDetail;

    /**
     * 自己这棵树最顶级的课程
     */
    private CourseVo topmostCourse;

    /**
     * 知识点视频
     */
    private RemoteKnowledgeVideoVo remoteKnowledgeVideoVo;

    /**
     * 课程视频学习记录
     */
    private StudyRecordVo studyRecord;

    /**
     * AI学习记录
     */
    private AiStudyRecordVo aiStudyRecord;

    /**
     * 讲义
     */
    private RemoteKnowledgeResourceVo handout;

    /**
     * 测验
     */
    private RemoteKnowledgeResourceVo test;

    /**
     * 练习
     */
    private RemoteKnowledgeResourceVo practice;

    /**
     * true:已经开始或者已经完成练习了 false:反之
     */
    private Boolean practiceStatus;
    /**
     * 该课程是否重复
     */
    private Boolean repeatStatus;

    /**
     * true:已经开始或者已经完成课程 false:反之
     */
    private Boolean studyCompleteStatus;

    /**
     * true:已经开始或者已经完成测试了 false:反之
     */
    private Boolean testStatus;
    /**
     * 地区，省市县之间用空格分割，理应与试卷关联，但是试卷来自第三方，并且没有返回地区数据，这里只能先跟课程关联
     */
    private String region;

    /**
     * AI学习完成的数量
     */
    private Integer aiStudyFinishNum;

    /**
     * AI学习总数量
     */
    private Integer aiStudyTotalNum;


    /**
     * 季度类型code2（ 字典code）
     */
    private String quarterType;
    /**
     * 可使用课程
     */
    private List<String> applicableGrade;

    /**
     * 课程父节点名称
     */
    private String courseParentName;

    /**
     * 需要完成课次数
     */
    private Long catalogCount;

    /**
     * 已完成课次数
     */
    private Long catalogCompletedCount;

}
