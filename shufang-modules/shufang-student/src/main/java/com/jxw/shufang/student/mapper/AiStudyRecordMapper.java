package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.vo.AiStudyRecordVo;

/**
 * ai学习记录Mapper接口
 *
 *
 * @date 2024-05-21
 */
public interface AiStudyRecordMapper extends BaseMapperPlus<AiStudyRecord, AiStudyRecordVo> {

    Page<AiStudyRecordVo> selectStudyRecordPage(@Param("page") Page<AiStudyRecord> build,@Param(Constants.WRAPPER) QueryWrapper<AiStudyRecord> lqw);
}
