package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.PaperCollection;

import java.util.List;

/**
 * 试卷收藏业务对象 paper_collection
 *
 *
 * @date 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PaperCollection.class, reverseConvertGenerate = false)
public class PaperCollectionBo extends BaseEntity {

    /**
     * 收藏ID
     */
    @NotNull(message = "收藏ID不能为空", groups = { EditGroup.class })
    private Long collectionId;

    /**
     * 试卷id
     */
    @NotNull(message = "试卷id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long paperId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;


    private List<Long> paperIdList;


}
