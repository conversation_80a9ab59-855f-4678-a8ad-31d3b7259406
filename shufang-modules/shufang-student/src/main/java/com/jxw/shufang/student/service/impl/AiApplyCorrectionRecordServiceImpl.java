package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AiApplyCorrectionRecord;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.mapper.AiApplyCorrectionRecordMapper;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ai申请批改记录Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class AiApplyCorrectionRecordServiceImpl implements IAiApplyCorrectionRecordService, BaseService {

    private final AiApplyCorrectionRecordMapper baseMapper;

    private final IAllowOwnCorrectionService allowOwnCorrectionService;

    private final ICourseService courseService;
    private final IStudentConsultantRecordService studentConsultantRecordService;
    private final IAiStudyRecordService aiStudyRecordService;
    private final IStudentService studentService;
    private final RocketMQTemplate rocketMQTemplate;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    /**
     *  查询ai申请批改记录
     */
    @Override
    public AiApplyCorrectionRecordVo queryById(Long aiApplyCorrectionRecordId) {
        return baseMapper.selectVoById(aiApplyCorrectionRecordId);
    }

    /**
     * 查询ai申请批改记录列表
     */
    @Override
    public TableDataInfo<AiApplyCorrectionRecordVo> queryPageList(AiApplyCorrectionRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<AiApplyCorrectionRecord> lqw = buildQueryWrapper(bo);
        Page<AiApplyCorrectionRecordVo> result = baseMapper.selectRecordVoPage(pageQuery.build(), lqw);

        putTopCourseInfo(result.getRecords(), true);

        if (Boolean.TRUE.equals(bo.getWithAllowOwnCorrection())) {
            putAllowOwnCorrection(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentUserInfo())) {
            putSysUserInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithTPState())) {
            putTPState(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai申请批改记录列表
     */
    @Override
    public List<AiApplyCorrectionRecordVo> queryList(AiApplyCorrectionRecordBo bo) {
        LambdaQueryWrapper<AiApplyCorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiApplyCorrectionRecord> buildLambdaQueryWrapper(AiApplyCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiApplyCorrectionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AiApplyCorrectionRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, AiApplyCorrectionRecord::getCourseId, bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), AiApplyCorrectionRecord::getApplyType, bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyResult()), AiApplyCorrectionRecord::getApplyResult, bo.getApplyResult());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), AiApplyCorrectionRecord::getStudentId, bo.getStudentIdList());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.apply(StringUtils.isNotBlank(bo.getTimeLimit()), "create_time BETWEEN {0} AND {1}", StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[0] : null, StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[1] : null);
        return lqw;
    }

    private QueryWrapper<AiApplyCorrectionRecord> buildQueryWrapper(AiApplyCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<AiApplyCorrectionRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), "t.apply_type", bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyResult()), "t.apply_result", bo.getApplyResult());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        if (StringUtils.isNotBlank(bo.getAllowOwnType()) && StringUtils.isNotBlank(bo.getApplyType())) {
            AllowOwnCorrectionBo allowOwnCorrectionBo = new AllowOwnCorrectionBo();
            allowOwnCorrectionBo.setType("2");
            allowOwnCorrectionBo.setAllowType(bo.getApplyType());
            List<AllowOwnCorrectionVo> voList = allowOwnCorrectionService.queryList(allowOwnCorrectionBo);
            List<Long> studentIdList = voList.stream().map(AllowOwnCorrectionVo::getStudentId).filter(Objects::nonNull).collect(Collectors.toList());
            if ("1".equals(bo.getAllowOwnType())) {
                if (null != studentIdList && studentIdList.size() > 0) {
                    lqw.in(StringUtils.isNotBlank(bo.getAllowOwnType()), "t.student_id", studentIdList);
                }
            } else if ("2".equals(bo.getAllowOwnType())) {
                if (null != studentIdList && studentIdList.size() > 0) {
                    lqw.notIn(StringUtils.isNotBlank(bo.getAllowOwnType()), "t.student_id", studentIdList);
                }
            }
        }
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.apply(StringUtils.isNotBlank(bo.getTimeLimit()),
            "t.apply_time is not null and t.apply_time BETWEEN {0} AND {1}",
            StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[0] : null,
            StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[1] : null);
        return lqw;
    }

    /**
     * 新增ai申请批改记录
     */
    @Override
    public Boolean insertByBo(AiApplyCorrectionRecordBo bo) {
        Long studentId = LoginHelper.getStudentId();
        AiApplyCorrectionRecord add = MapstructUtils.convert(bo, AiApplyCorrectionRecord.class);
        AiApplyCorrectionRecordVo invisiableAiApplyCorrectionRecordVo = validEntityBeforeSave(add, true);
        if (null != invisiableAiApplyCorrectionRecordVo) {
            // 重新申请时，更新订单的申请时间
            if (updateApplyTime(invisiableAiApplyCorrectionRecordVo)) {
                return true;
            } else {
                throw new ServiceException("重新申请失败");
            }
        }

        //始终允许自己批改
        AllowOwnCorrectionVo allowOwnCorrectionVo = allowOwnCorrectionService.queryByStudentId(bo.getStudentId(), "2", bo.getApplyType());

        if (allowOwnCorrectionVo != null) {
            add.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
        } else {
            add.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT.equals(add.getApplyResult())) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put(NotifyMessageConstant.CONTENT, NoticeBizTypeEnum.ZZPGSQ.getDesc());

                RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder()
                    .templateCode(NoticeBizTypeEnum.ZZPGSQ.name())
                    .bizType(NoticeBizTypeEnum.ZZPGSQ.getCode())
                    .noticeType(NoticeTypeEnum.INTERNAL.getCode())
                    .content("向您申请进行自主批改申请审核操作")
                    .studentId(studentId)
                    .paramMap(paramMap)
                    .build();
                rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);
            }
            bo.setAiApplyCorrectionRecordId(add.getAiApplyCorrectionRecordId());
        }
        return flag;
    }

    /**
     * 重新申请更新申请记录的申请时间
     *
     * @param aiApplyCorrectionRecordVo
     * @return
     */
    private boolean updateApplyTime(AiApplyCorrectionRecordVo aiApplyCorrectionRecordVo) {
        if (aiApplyCorrectionRecordVo == null || aiApplyCorrectionRecordVo.getAiApplyCorrectionRecordId() == null) {
            return false;
        }
        AiApplyCorrectionRecordBo updateBo = new AiApplyCorrectionRecordBo();
        updateBo.setAiApplyCorrectionRecordId(aiApplyCorrectionRecordVo.getAiApplyCorrectionRecordId());
        updateBo.setApplyTime(new Date());
        return updateByBo(updateBo);
    }

    /**
     * 修改ai申请批改记录
     */
    @Override
    public Boolean updateByBo(AiApplyCorrectionRecordBo bo) {
        AiApplyCorrectionRecord update = MapstructUtils.convert(bo, AiApplyCorrectionRecord.class);
        validEntityBeforeSave(update, false);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity
     * @param forAdd true-添加时教研 false-更新时校验
     * @return 当前”不可见“的自主批改申请记录
     */
    private AiApplyCorrectionRecordVo validEntityBeforeSave(AiApplyCorrectionRecord entity, boolean forAdd) {
        //做一些数据校验,如唯一约束
        if (entity.getAiApplyCorrectionRecordId() == null && entity.getStudentId() != null && entity.getCourseId() != null && StringUtils.isNotBlank(entity.getApplyType())) {
            LambdaQueryWrapper<AiApplyCorrectionRecord> lqw = Wrappers.lambdaQuery();
            lqw.eq(AiApplyCorrectionRecord::getStudentId, entity.getStudentId());
            lqw.eq(AiApplyCorrectionRecord::getCourseId, entity.getCourseId());
            lqw.eq(AiApplyCorrectionRecord::getApplyType, entity.getApplyType());
            lqw.in(AiApplyCorrectionRecord::getApplyResult, UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT, UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
            lqw.orderByDesc(AiApplyCorrectionRecord::getAiApplyCorrectionRecordId).last("limit 1");
            AiApplyCorrectionRecordVo recordVo = baseMapper.selectVoOne(lqw);
            if (recordVo != null) {
                // 存在订单并且是新增记录，并且当前旧记录不为空的情况下，返回标识不报错
                if (forAdd && !wheatherApplyRecordVisiable(recordVo)) {
                    return recordVo;
                }
                throw new ServiceException("存在已通过或待审核的申请记录，不允许再新增申请");
            }
        }
        return null;
    }

    /**
     * 批量删除ai申请批改记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public AiApplyCorrectionRecordVo queryLastRecord(AiApplyCorrectionRecordBo aiApplyCorrectionRecordBo) {
        LambdaQueryWrapper<AiApplyCorrectionRecord> applyCorrectionRecordLambdaQueryWrapper = buildLambdaQueryWrapper(aiApplyCorrectionRecordBo);
        applyCorrectionRecordLambdaQueryWrapper.orderByDesc(AiApplyCorrectionRecord::getCreateTime);
        applyCorrectionRecordLambdaQueryWrapper.last("limit 1");
        return baseMapper.selectVoOne(applyCorrectionRecordLambdaQueryWrapper);
    }

    @Override
    public Long count(AiApplyCorrectionRecordBo bo) {
        handleQueryParam(bo);
        LambdaQueryWrapper<AiApplyCorrectionRecord> queryWrapper = buildLambdaQueryWrapper(bo);
        return baseMapper.selectCount(queryWrapper);

    }

    @Override
    public boolean updateApplyResult(Long aiApplyCorrectionRecordId, String applyResult) {
        AiApplyCorrectionRecord aiApplyCorrectionRecord = new AiApplyCorrectionRecord();
        aiApplyCorrectionRecord.setAiApplyCorrectionRecordId(aiApplyCorrectionRecordId);
        aiApplyCorrectionRecord.setApplyResult(applyResult);
        return baseMapper.updateById(aiApplyCorrectionRecord) > 0;
    }

    @Override
    public boolean updateBatchById(List<AiApplyCorrectionRecordBo> updateList) {
        List<AiApplyCorrectionRecord> updateRecords = MapstructUtils.convert(updateList, AiApplyCorrectionRecord.class);
        return baseMapper.updateBatchById(updateRecords);
    }

    @Override
    public boolean wheatherApplyRecordVisiable(AiApplyCorrectionRecordVo aiApplyCorrectionRecordVo) {
        if (aiApplyCorrectionRecordVo == null || (aiApplyCorrectionRecordVo.getCreateTime() == null
            && aiApplyCorrectionRecordVo.getApplyTime() == null)) {
            return false;
        }
        // 获取最近一次申请时间
        Date lastApplyTime = aiApplyCorrectionRecordVo.getApplyTime() == null
            ? aiApplyCorrectionRecordVo.getCreateTime() : aiApplyCorrectionRecordVo.getApplyTime();
        // 获取今天凌晨三点的时间
        LocalDateTime today3AM = LocalDate.now().atTime(3, 0);
        Date today3AMDate = Date.from(today3AM.atZone(ZoneId.systemDefault()).toInstant());
        // 比较 申请时间 和今天凌晨三点
        return !lastApplyTime.before(today3AMDate);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void putTopCourseInfo(List<AiApplyCorrectionRecordVo> records, boolean queryDetailInfo) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<CourseVo> courseList = records.stream().map(AiApplyCorrectionRecordVo::getCourse).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(courseList)) {
            return;
        }
        courseService.putTopmostCourseInfo(courseList, queryDetailInfo);
        if (queryDetailInfo) {
            List<CourseVo> list = courseList.stream().map(CourseVo::getTopmostCourse).toList();
            courseService.putCourseDetail(list, true);
        }
    }

    //会员顾问记录详情
    private void putConsultantInfo(List<AiApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(AiApplyCorrectionRecordVo::getStudent).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentConsultantRecordId = studentVos.stream().map(StudentVo::getStudentConsultantRecordId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantRecordId)) {
            return;
        }
        List<StudentConsultantRecordVo> studentConsultantRecordVos = studentConsultantRecordService.queryByIdList(studentConsultantRecordId);
        if (CollUtil.isEmpty(studentConsultantRecordVos)) {
            return;
        }
        List<Long> studentConsultantIdList = studentConsultantRecordVos.stream().map(StudentConsultantRecordVo::getStudentConsultantId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(studentConsultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        studentVos.forEach(studentVo -> {
            StudentConsultantRecordVo studentConsultantRecordVo = studentConsultantRecordVos.stream().filter(vo -> vo.getStudentConsultantRecordId().equals(studentVo.getStudentConsultantRecordId())).findFirst().orElse(null);
            if (studentConsultantRecordVo != null) {
                RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(studentConsultantRecordVo.getStudentConsultantId());
                studentVo.setConsultant(remoteStaffVo);
            }
        });

    }

    private void putAllowOwnCorrection(List<AiApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(AiApplyCorrectionRecordVo::getStudentId).toList();
        AllowOwnCorrectionBo allowOwnCorrectionBo = new AllowOwnCorrectionBo();
        allowOwnCorrectionBo.setStudentIdList(studentIdList);
        allowOwnCorrectionBo.setType("2");
        List<AllowOwnCorrectionVo> allowOwnCorrectionList = allowOwnCorrectionService.queryList(allowOwnCorrectionBo);
        if (CollUtil.isEmpty(allowOwnCorrectionList)) {
            return;
        }

        Map<String, AllowOwnCorrectionVo> allowOwnCorrectionMap = StreamUtils.toIdentityMap(allowOwnCorrectionList, e -> e.getStudentId() + "_" + e.getAllowType());
        for (AiApplyCorrectionRecordVo record : records) {
            AllowOwnCorrectionVo vo = allowOwnCorrectionMap.get(record.getStudentId() + "_" + record.getApplyType());
            record.setAllowOwnCorrection(vo);
            record.setAllowOwnType(ObjectUtil.isEmpty(vo) ? "2" : "1");//是否允许自主批改（1允许自主批改 2不允许自主批改）
        }
    }

    public void putTPState(List<AiApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        for (AiApplyCorrectionRecordVo record : records) {

            AiStudyRecordVo aiStudyRecordVo = aiStudyRecordService.queryByStudentIdAndCourseId(record.getStudentId(), record.getCourseId());

            record.setTestState(null != aiStudyRecordVo && StringUtils.isNotBlank(aiStudyRecordVo.getTestState()) ? aiStudyRecordVo.getTestState() : "1");

            record.setPracticeState(null != aiStudyRecordVo && StringUtils.isNotBlank(aiStudyRecordVo.getPracticeState()) ? aiStudyRecordVo.getPracticeState() : "1");
        }
    }

    public void putSysUserInfo(List<AiApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(AiApplyCorrectionRecordVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        remoteUserBo.setGetAvatarUrl(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    private void handleQueryParam(AiApplyCorrectionRecordBo record) {
        if (record.getStudentId() != null) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else if (null != LoginHelper.getBranchId()) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else {
            record.setStudentIdList(List.of(-1L));
        }

    }


}
