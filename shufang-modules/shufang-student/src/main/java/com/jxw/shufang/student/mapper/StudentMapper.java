package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 会员Mapper接口
 *
 *
 * @date 2024-02-27
 */
public interface StudentMapper extends BaseMapperPlus<Student, StudentVo> {


    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
        Page<StudentVo> queryOptionList(@Param("page") Page<Student> build,@Param(Constants.WRAPPER) QueryWrapper<Student> lqw);

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    List<StudentVo> selectStudentList(@Param(Constants.WRAPPER)LambdaQueryWrapper<Student> lqw);

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    List<Long> queryStudentIdList(@Param(Constants.WRAPPER) QueryWrapper<Student> lqw);

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    Page<StudentVo> selectStudentPage(@Param("page") Page<Student> page,
                                      @Param(Constants.WRAPPER)  QueryWrapper<Student> lqw,
                                      @Param("bo") StudentBo bo);


    StudentVo selectStudentById(Long studentId);

    @MapKey("student_id")
    List<Map<String, Object>> queryStudentNameMap(@Param("studentIdList") List<Long> studentIdList);

    List<Long> batchSelectStudentIdListByBranchId(@Param("branchIdList") List<Long> branchIdList);
}
