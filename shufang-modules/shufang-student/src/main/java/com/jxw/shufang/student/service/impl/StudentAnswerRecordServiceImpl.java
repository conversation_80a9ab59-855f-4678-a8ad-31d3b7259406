package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentAnswerRecord;
import com.jxw.shufang.student.domain.bo.StudentAnswerRecordBo;
import com.jxw.shufang.student.domain.vo.StudentAnswerRecordVo;
import com.jxw.shufang.student.mapper.StudentAnswerRecordMapper;
import com.jxw.shufang.student.service.IStudentAnswerRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;


@RequiredArgsConstructor
@Service
public class StudentAnswerRecordServiceImpl implements IStudentAnswerRecordService, BaseService {

    private final StudentAnswerRecordMapper baseMapper;


    @Override
    public StudentAnswerRecordVo queryById(Long studentAnswerRecordId) {
        return baseMapper.selectVoById(studentAnswerRecordId);
    }

    /**
     * 查询用户答题记录列表
     */
    @Override
    public List<StudentAnswerRecordVo> queryList(StudentAnswerRecordBo bo) {
        LambdaQueryWrapper<StudentAnswerRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    /**
     * 查询用户答题记录列表
     */
    @Override
    public List<StudentAnswerRecordVo> queryListByStudentPaperRecordId(Long studentPaperRecordId) {
        StudentAnswerRecordBo bo = new StudentAnswerRecordBo();
        bo.setStudentPaperRecordId(studentPaperRecordId);
        LambdaQueryWrapper<StudentAnswerRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    private LambdaQueryWrapper<StudentAnswerRecord> buildLambdaQueryWrapper(StudentAnswerRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentAnswerRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentAnswerRecordId() != null, StudentAnswerRecord::getStudentAnswerRecordId, bo.getStudentAnswerRecordId());
        lqw.eq(bo.getStudentId() != null, StudentAnswerRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getQuestionId() != null, StudentAnswerRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getStudentPaperRecordId() != null, StudentAnswerRecord::getStudentPaperRecordId, bo.getStudentPaperRecordId());
        lqw.eq(bo.getTestPaperId() != null, StudentAnswerRecord::getTestPaperId, bo.getTestPaperId());
        lqw.eq(bo.getSource() != null, StudentAnswerRecord::getSource, bo.getSource());

        lqw.eq(bo.getCreateDept() != null, StudentAnswerRecord::getCreateDept, bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, StudentAnswerRecord::getCreateBy, bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, StudentAnswerRecord::getCreateTime, bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, StudentAnswerRecord::getUpdateBy, bo.getUpdateBy());
        lqw.eq(bo.getUpdateTime() != null, StudentAnswerRecord::getUpdateTime, bo.getUpdateTime());
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(StudentAnswerRecordBo bo) {
        StudentAnswerRecord add = MapstructUtils.convert(bo, StudentAnswerRecord.class);
        validEntityBeforeSave(add);

        LambdaQueryWrapper<StudentAnswerRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudentAnswerRecord::getStudentPaperRecordId, bo.getStudentPaperRecordId())
            .eq(StudentAnswerRecord::getTestPaperId, bo.getTestPaperId())
            .eq(StudentAnswerRecord::getQuestionId, bo.getQuestionId());
        //目前业务同一试卷答题，更新处理
        StudentAnswerRecord studentAnswerRecord = baseMapper.selectOne(lqw, false);

        if (!ObjectUtils.isEmpty(studentAnswerRecord)) {
            bo.setStudentAnswerRecordId(studentAnswerRecord.getStudentAnswerRecordId());
            return this.updateByBo(bo);
        }
        //todo 错题会进入错题库
        return baseMapper.insert(add) > 0;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchInsertByBo(List<StudentAnswerRecordBo> bo) {
        if (CollectionUtils.isEmpty(bo)) {
            return false;
        }
        //如果是批量提交答案 说明流程是试卷结束后统一提交，之前是没有答案的
        List<StudentAnswerRecord> insertList = MapstructUtils.convert(bo, StudentAnswerRecord.class);

        //todo 错题会进入错题库
        return baseMapper.insertBatch(insertList);

    }

    private void validEntityBeforeSave(StudentAnswerRecord add) {

    }

    /**
     * 修改ai申请批改记录
     */
    @Override
    public Boolean updateByBo(StudentAnswerRecordBo bo) {
        StudentAnswerRecord update = MapstructUtils.convert(bo, StudentAnswerRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


}
