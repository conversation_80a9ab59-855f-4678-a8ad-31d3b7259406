package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.domain.dto.PlanningRecordStudentIdDTO;
import com.jxw.shufang.student.domain.dto.QueryPlanningRecordDTO;
import com.jxw.shufang.student.domain.dto.StudentWithPlanningRecordDTO;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordDashboardVo;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyPlanningRecord;
import com.jxw.shufang.student.domain.vo.StudyPlanRecordGroupVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;

import java.util.List;

/**
 * 学习规划记录Mapper接口
 * @date 2024-04-23
 */
public interface StudyPlanningRecordMapper extends BaseMapperPlus<StudyPlanningRecord, StudyPlanningRecordVo> {

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    Page<StudyPlanningRecordVo> queryStudyPlanningRecordPage(@Param("page") Page<StudyPlanningRecord> build, @Param(Constants.WRAPPER) QueryWrapper<StudyPlanningRecord> queryWrapper);

    List<StudyPlanningRecordVo> queryStudyPlanningRecordList(@Param(Constants.WRAPPER) QueryWrapper<StudyPlanningRecord> queryWrapper);

    Page<StudyPlanRecordGroupVo> groupByStudyPlanDate(@Param(Constants.WRAPPER)QueryWrapper<StudyPlanningRecord> queryWrapper, @Param("page")  Page<StudyPlanRecordGroupVo> pageQuery);

    Page<PlanningRecordStudentIdDTO> queryPlanningRecordStudentIdPage(@Param("page") Page<StudyPlanningRecord> build, @Param("queryPlanningRecordDTO") QueryPlanningRecordDTO queryPlanningRecordDTO);

    List<StudentWithPlanningRecordDTO> queryStudyPlanningWithStudentInfo(@Param("queryPlanningRecordDTO") QueryPlanningRecordDTO queryPlanningRecordDTO);

    List<StudyPlanRecordGroupVo> getGroupByStudyPlanDateData(@Param(Constants.WRAPPER)QueryWrapper<StudyPlanningRecord> queryWrapper);

    List<StudyPlanningRecordDashboardVo>
        queryStudyPlanningRecordPageV3(@Param(Constants.WRAPPER) QueryWrapper<StudyPlanningRecord> queryWrapper);

    /**
     * 查询学习计划工作台需要查询的会员id
     *
     * @param build
     * @param queryWrapper
     * @return
     */
    Page<Long> queryStudyPlanningStudentIdV3(@Param("page") Page<StudyPlanningRecord> build,
        @Param(Constants.WRAPPER) QueryWrapper<StudyPlanningRecord> queryWrapper);

}
