package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.bo.RemoteCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteCorrectionRecordInfoBoConvertCorrectionRecordInfoBo extends BaseMapper<RemoteCorrectionRecordInfoBo, CorrectionRecordInfoBo> {

}
