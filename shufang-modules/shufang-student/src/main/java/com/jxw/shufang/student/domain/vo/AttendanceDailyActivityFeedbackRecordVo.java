package com.jxw.shufang.student.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = AttendanceDailyActivityFeedbackRecord.class)
public class AttendanceDailyActivityFeedbackRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceDailyActivityFeedbackRecordId;

    /**
     * 每日会员打卡登录详情表id
     */
    private Long attendanceDailyActivityId;

    /**
     * 反馈状态
     */
    private Integer feedbackStatus;

    /**
     * 发布状态（1：暂存；2：未发布）
     */
    private Integer publishStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
