package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.CorrectionQuestionRecordVo;
import com.jxw.shufang.student.service.ICorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 批改记录--平板端
 * 前端访问路由地址为:/android/correctionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/correctionRecord")
public class ACorrectionRecordController extends BaseController {

    private final ICorrectionRecordService correctionRecordService;


    /**
     * 提交批改记录
     */
    @RepeatSubmit
    @PostMapping()
    @Log(title = "提交批改记录--平板端", businessType = BusinessType.INSERT)
    public R<BigDecimal> submitCorrectionRecord(@RequestBody @Validated(AddGroup.class) CorrectionRecordBo correctionRecordBo) {
        correctionRecordBo.setStudentId(LoginHelper.getStudentId());
        correctionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STUDENT);
        return R.ok(correctionRecordService.insertByBo(correctionRecordBo));
    }

    /**
     * 提交批改记录
     */
    @RepeatSubmit
    @PostMapping("/withoutInfo")
    @Log(title = "预习、自讲记录--平板端", businessType = BusinessType.INSERT)
    public R<Boolean> submitWithoutCorrectionRecordInfo(@RequestBody  CorrectionRecordBo correctionRecordBo) {
        correctionRecordBo.setStudentId(LoginHelper.getStudentId());
        correctionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STUDENT);
        return R.ok(correctionRecordService.submitWithoutCorrection(correctionRecordBo));
    }

    /**
     * 查询批改的题目列表列表
     */
    @GetMapping("/queryQuestionRecord")
    public R<CorrectionQuestionRecordVo> queryQuestionRecord(@NotNull(message = "学习规划记录ID不能为空") Long studyPlanningRecordId,
                                                             @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(correctionRecordService.queryQuestionRecord(studyPlanningRecordId, correctionType));
    }
    /**
     * 查询批改列表
     */
    @GetMapping("/queryRecord")
    public R<CorrectionRecordVo> queryRecord(@NotNull(message = "学习规划记录ID不能为空") Long studyPlanningRecordId,
                                             @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(correctionRecordService.queryRecord(studyPlanningRecordId, correctionType));
    }

}
