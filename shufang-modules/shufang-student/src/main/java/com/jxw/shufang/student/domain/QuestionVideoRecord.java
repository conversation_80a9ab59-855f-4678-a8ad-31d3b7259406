package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 题目视频记录（题目视频观看记录）对象 question_video_record
 * @date 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("question_video_record")
public class QuestionVideoRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目视频记录id
     */
    @TableId(value = "question_video_record_id")
    private Long questionVideoRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 学习规划记录Id
     */
    private Long studyPlanningRecordId;

    /**
     * 对应外部资源的视频Id
     */
    private Long videoId;

    /**
     * 题目类型：1测试 2练习
     */
    private String questionType;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 题目Id
     */
    private Long questionId;

    /**
     * 模块组类型 study_plan-学习规划,ai_study-Ai学习
     */
    private String moduleGroup;

    /**
     * 播放时长（单位秒 按日累加）
     */
    private Long studyVideoDuration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    private String studyVideoSlices;

    /**
     * 创建部门(和会员id对应的部门一致)
     */
    private Long createDept;

    /**
     * 创建人(和会员id对应的创建人一致)
     */
    private Long createBy;

    /**
     * 创建时间（日期）
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
