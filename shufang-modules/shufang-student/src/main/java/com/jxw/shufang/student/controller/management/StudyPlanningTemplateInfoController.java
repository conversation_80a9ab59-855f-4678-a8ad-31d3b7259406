package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateInfoBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateInfoVo;
import com.jxw.shufang.student.service.IStudyPlanningTemplateInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习规划模板详情
 * 前端访问路由地址为:/student/management/studyPlanningTemplateInfo
 *
 *
 * @date 2024-06-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studyPlanningTemplateInfo")
public class StudyPlanningTemplateInfoController extends BaseController {

    private final IStudyPlanningTemplateInfoService studyPlanningTemplateInfoService;

    /**
     * 查询学习规划模板详情列表
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:list")
    @GetMapping("/list")
    public TableDataInfo<StudyPlanningTemplateInfoVo> list(StudyPlanningTemplateInfoBo bo, PageQuery pageQuery) {
        return studyPlanningTemplateInfoService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询学习规划模板详情列表
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:list")
    @GetMapping("/queryList")
    public R<List<StudyPlanningTemplateInfoVo>> queryList(StudyPlanningTemplateInfoBo bo) {
        return R.ok(studyPlanningTemplateInfoService.queryList(bo));
    }

    /**
     * 导出学习规划模板详情列表
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:export")
    @Log(title = "学习规划模板详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyPlanningTemplateInfoBo bo, HttpServletResponse response) {
        List<StudyPlanningTemplateInfoVo> list = studyPlanningTemplateInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习规划模板详情", StudyPlanningTemplateInfoVo.class, response);
    }

    /**
     * 获取学习规划模板详情详细信息
     *
     * @param studyPlanningTemplateInfoId 主键
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:query")
    @GetMapping("/{studyPlanningTemplateInfoId}")
    public R<StudyPlanningTemplateInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                                  @PathVariable Long studyPlanningTemplateInfoId) {
        return R.ok(studyPlanningTemplateInfoService.queryById(studyPlanningTemplateInfoId));
    }

    /**
     * 新增学习规划模板详情
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:add")
    @Log(title = "学习规划模板详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyPlanningTemplateInfoBo bo) {
        return toAjax(studyPlanningTemplateInfoService.insertByBo(bo));
    }

    /**
     * 修改学习规划模板详情
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:edit")
    @Log(title = "学习规划模板详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyPlanningTemplateInfoBo bo) {
        return toAjax(studyPlanningTemplateInfoService.updateByBo(bo));
    }

    /**
     * 删除学习规划模板详情
     *
     * @param studyPlanningTemplateInfoIds 主键串
     */
    @SaCheckPermission("student:studyPlanningTemplateInfo:remove")
    @Log(title = "学习规划模板详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyPlanningTemplateInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyPlanningTemplateInfoIds) {
        return toAjax(studyPlanningTemplateInfoService.deleteWithValidByIds(List.of(studyPlanningTemplateInfoIds), true));
    }
}
