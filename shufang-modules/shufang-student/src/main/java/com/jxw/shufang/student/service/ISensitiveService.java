package com.jxw.shufang.student.service;

import com.jxw.shufang.common.core.enums.SensitiveGroup;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.SensitiveBo;
import com.jxw.shufang.student.domain.vo.SensitiveVo;

import java.util.Collection;
import java.util.List;

/**
 * 敏感词Service接口
 *
 *
 * @date 2024-06-15
 */
public interface ISensitiveService {

    /**
     * 查询敏感词
     */
    SensitiveVo queryById(Long sensitiveId);

    /**
     * 查询敏感词列表
     */
    TableDataInfo<SensitiveVo> queryPageList(SensitiveBo bo, PageQuery pageQuery);

    /**
     * 查询敏感词列表
     */
    List<SensitiveVo> queryList(SensitiveBo bo);

    /**
     * 新增敏感词
     */
    Boolean insertByBo(SensitiveBo bo);

    /**
     * 修改敏感词
     */
    Boolean updateByBo(SensitiveBo bo);

    /**
     * 校验并批量删除敏感词信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 检查是否有敏感词，并返回对应的敏感词id，如果返回为null，则没有敏感词
     */
    Long checkHasSensitive(String content, SensitiveGroup group);

    Boolean updateByGroup(SensitiveBo bo);
}
