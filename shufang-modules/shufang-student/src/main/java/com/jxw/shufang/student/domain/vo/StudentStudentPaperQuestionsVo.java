package com.jxw.shufang.student.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


@Data
public class StudentStudentPaperQuestionsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private Long questionId;
    private Integer stayTime;
    private Integer isMistake;
    /**
     * 难度 1-易 2-较易 3-中等 4-较难 5-难
     */
    private Integer difficulty;
    /**
     * 是否有视频 1-是， 0-否
     */
    private Integer hasVideo;
    private List<StudentStudentPaperKnowledgeVo> knowledgeList;

    private List<RemoteVideoVo> knowledgeVideoList;
    /**
     * 是否有知识点视频 1-是， 0-否
     */
    private Integer hasKnowledgeVideo;
    private String url;

}
