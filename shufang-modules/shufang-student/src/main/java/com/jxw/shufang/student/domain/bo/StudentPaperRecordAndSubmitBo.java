package com.jxw.shufang.student.domain.bo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 用户答题试卷记录业务对象 student_paper_record
 *
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
public class StudentPaperRecordAndSubmitBo {
    @NotNull(message = "评测报告不能为空")
    @Valid
    private StudentPaperRecordBo studentPaperRecord;
    @NotNull(message = "答题列表不能为空")
    @Valid
    private List<StudentAnswerRecordBo> studentAnswerList;


}
