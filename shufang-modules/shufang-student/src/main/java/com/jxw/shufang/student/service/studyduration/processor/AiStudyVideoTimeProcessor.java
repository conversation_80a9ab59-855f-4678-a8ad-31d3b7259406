package com.jxw.shufang.student.service.studyduration.processor;

import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.dto.AiStudyDurationProcessingContextDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyVideoRecordDTO;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/4/7 10:02
 * @Version 1
 * @Description AI伴学视频时长统计
 */
public class AiStudyVideoTimeProcessor {
    private AiStudyDurationProcessingContextDTO context;
    private List<AiStudyVideoRecord> updates = new ArrayList<>();
    private List<AiStudyVideoRecord> inserts = new ArrayList<>();

    public AiStudyVideoTimeProcessor(AiStudyDurationProcessingContextDTO context) {
        this.context = context;
    }

    public List<AiStudyVideoRecord> getUpdates() {
        return updates;
    }

    public List<AiStudyVideoRecord> getInserts() {
        return inserts;
    }

    /**
     * 处理视频记录
     *
     * @param recordBo
     */
    public void process(SaveOrUpdateAiStudyVideoRecordDTO recordBo) {
        if (null == recordBo) {
            return;
        }
        AiStudyVideoRecord existRecord = context.getExistAiVideoRecord().stream().findFirst().orElse(null);
        if (existRecord != null) {
            Optional.ofNullable(this.buildUpdateVideoRecord(existRecord, recordBo)).ifPresent(updates::add);
        } else {
            Optional.of(this.buildInsertVideoRecord(recordBo)).ifPresent(inserts::add);
        }
    }

    /**
     * 构造新增视频记录对象
     *
     * @param recordBo
     * @return
     */
    private AiStudyVideoRecord buildInsertVideoRecord(SaveOrUpdateAiStudyVideoRecordDTO recordBo) {
        Student student = context.getStudentMap().get(recordBo.getStudentId());
        StudyModuleAndGroupEnum moduleTypeEnum = context.getStudyModuleTypeEnum();
        AiStudyVideoRecord insertBean = new AiStudyVideoRecord();
        insertBean.setStudentId(recordBo.getStudentId());
        insertBean.setVideoId(recordBo.getVideoId());
        insertBean.setCourseId(recordBo.getCourseId());
        insertBean.setStudyModuleType(moduleTypeEnum.getModuleEnum().getModuleCode());
        insertBean.setCreateBy(student.getCreateBy());
        insertBean.setCreateDept(student.getCreateDept());
        insertBean.setCreateTime(recordBo.getCommitTime());
        insertBean.setUpdateTime(recordBo.getCommitTime());
        insertBean.setUpdateBy(student.getCreateBy());
        insertBean.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        this.setStudyVideoDurationTime(recordBo, insertBean);
        return insertBean;
    }

    private void setStudyVideoDurationTime(SaveOrUpdateAiStudyVideoRecordDTO recordDTO, AiStudyVideoRecord insertRecord) {
        Long sliceSeconds = this.getSliceSeconds(recordDTO.getStudyVideoDuration(), recordDTO.getStudyVideoSlices());
        Long courseDuration = Optional.ofNullable(context.getCourseDurationMap())
            .map(map -> map.get(recordDTO.getVideoId())).orElse(0L);

        boolean existCourseDuration = courseDuration > 0;
        boolean overCourseMaxTime = existCourseDuration && sliceSeconds >= courseDuration;
        long remainDuration = courseDuration - sliceSeconds;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;

        insertRecord.setStudyVideoDuration(useCourseDuration ? courseDuration : sliceSeconds);
        // 增加视频原时长
        insertRecord.setDuration(courseDuration);
    }

    /**
     * 构造更新视频记录对象
     *
     * @param existing
     * @param recordBo
     * @return
     */
    private AiStudyVideoRecord buildUpdateVideoRecord(AiStudyVideoRecord existing, SaveOrUpdateAiStudyVideoRecordDTO recordBo) {
        Student student = context.getStudentMap().get(recordBo.getStudentId());
        Long courseDuration = Optional.ofNullable(context.getCourseDurationMap().get(recordBo.getVideoId())).orElse(0L);

        Long oldDurationTime = existing.getStudyVideoDuration();
        String oldSlices = existing.getStudyVideoSlices();
        Long newVideoDuration = recordBo.getStudyVideoDuration();
        String newVideoSlices = recordBo.getStudyVideoSlices();

        String mergedSlices = VideoSlicesUtils.mergeVideoSlices(oldSlices, newVideoSlices);
        Long maxDurationTime = this.getMaxDurationTime(mergedSlices, newVideoSlices, newVideoDuration, oldDurationTime);

        boolean updateRecordFlag = maxDurationTime > oldDurationTime;
        if (updateRecordFlag) {
            AiStudyVideoRecord updateBean = new AiStudyVideoRecord();
            updateBean.setAiStudyVideoRecordId(existing.getAiStudyVideoRecordId());
            // 更新视频原时长
            long remainDuration = courseDuration - maxDurationTime;
            boolean overCourseMaxTime = maxDurationTime > courseDuration;
            boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;
            updateBean.setStudyVideoDuration(useCourseDuration ? courseDuration : maxDurationTime);
            updateBean.setDuration(courseDuration);
            updateBean.setStudyVideoSlices(mergedSlices);
            updateBean.setUpdateBy(student.getCreateBy());
            updateBean.setUpdateTime(recordBo.getCommitTime());
            return updateBean;
        }
        return null;
    }

    /**
     * 计算最大时长
     *
     * @param mergedSlices
     * @param studyVideoSlices
     * @param videoDuration
     * @param oldDurationTime
     * @return
     */
    public Long getMaxDurationTime(String mergedSlices, String studyVideoSlices, Long videoDuration, Long oldDurationTime) {
        Long itemAddSeconds = 0L;
        if (StringUtils.isNotEmpty(studyVideoSlices)) {
            itemAddSeconds = VideoSlicesUtils.calVideoSliceSeconds(mergedSlices);
        } else {
            itemAddSeconds = Optional.ofNullable(videoDuration).orElse(0L) + oldDurationTime;
        }
        return itemAddSeconds;
    }

    /**
     * 根据视频片计算时长
     *
     * @param studyVideoDuration
     * @param videoSlice
     * @return
     */
    private Long getSliceSeconds(Long studyVideoDuration, String videoSlice) {
        if (null != studyVideoDuration && studyVideoDuration > 0) {
            return studyVideoDuration;
        }
        return getSliceSecondsBySlice(videoSlice);
    }

    /**
     * 根据视频片计算时长
     *
     * @param videoSlice
     * @return
     */
    private Long getSliceSecondsBySlice(String videoSlice) {
        return VideoSlicesUtils.calVideoSliceSeconds(videoSlice);
    }

}
