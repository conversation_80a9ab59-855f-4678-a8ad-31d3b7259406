package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PaperBo;
import com.jxw.shufang.student.domain.vo.PaperDocVo;
import com.jxw.shufang.student.domain.vo.PaperVo;
import com.jxw.shufang.student.service.IPaperService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 试卷
 * 前端访问路由地址为:/student/management/paper
 *
 *
 * @date 2024-05-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/paper")
public class PaperController extends BaseController {

    private final IPaperService paperService;

    /**
     * 查询试卷列表
     */
    @SaCheckPermission("student:paper:list")
    @GetMapping("/list")
    public TableDataInfo<PaperVo> list(PaperBo bo, PageQuery pageQuery) {
        pageQuery.setOrderByColumn("create_time");
        pageQuery.setIsAsc("desc");
        if (LoginHelper.getBranchId() != null||CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setBranchIdList(LoginHelper.getBranchIdList());
            bo.setContainManagement(true);
        }
        return paperService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出试卷列表
     */
    @SaCheckPermission("student:paper:export")
    @Log(title = "试卷", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PaperBo bo, HttpServletResponse response) {
        List<PaperVo> list = paperService.queryList(bo);
        ExcelUtil.exportExcel(list, "试卷", PaperVo.class, response);
    }

    /**
     * 获取试卷详细信息
     *
     * @param paperId 主键
     */
    @SaCheckPermission("student:paper:query")
    @GetMapping("/{paperId}")
    public R<PaperVo> getInfo(@NotNull(message = "主键不能为空")
                              @PathVariable Long paperId) {
        return R.ok(paperService.queryById(paperId));
    }

    /**
     * 新增试卷
     */
    @SaCheckPermission("student:paper:add")
    @Log(title = "试卷", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PaperBo bo) {
        if (LoginHelper.isBranchAdmin() && LoginHelper.getBranchId() == null) {
            return R.fail("门店管理登录时请选择门店登录,才能新增试卷");
        }
        if (StringUtils.isBlank(bo.getPaperTitle())){
            return R.fail("试卷标题不能为空");
        }
        if (bo.getOriginal()==null){
            return R.fail("原卷ossId不能为空");
        }
        if (bo.getAnalysis()==null){
            return R.fail("解析卷ossId不能为空");
        }
        //if (bo.getOriginalWithAnalysis()==null){
        //    return R.fail("原卷带解析ossId不能为空");
        //}
        if (LoginHelper.getBranchId() != null) {
            bo.setPaperSource("2");
            bo.setBranchId(LoginHelper.getBranchId());
        } else {
            bo.setPaperSource("1");
        }
        return toAjax(paperService.insertByBo(bo));
    }

    /**
     * 修改试卷
     */
    @SaCheckPermission("student:paper:edit")
    @Log(title = "试卷", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PaperBo bo) {
        if (LoginHelper.isBranchAdmin() && LoginHelper.getBranchId() == null) {
            return R.fail("门店管理登录时请选择门店登录,才能修改试卷");
        }
        if (StringUtils.isBlank(bo.getPaperTitle())){
            return R.fail("试卷标题不能为空");
        }
        if (bo.getOriginal()==null){
            return R.fail("原卷ossId不能为空");
        }
        if (bo.getAnalysis()==null){
            return R.fail("解析卷ossId不能为空");
        }
        //if (bo.getOriginalWithAnalysis()==null){
        //    return R.fail("原卷带解析ossId不能为空");
        //}
        if (LoginHelper.getBranchId() != null) {
            bo.setPaperSource("2");
            bo.setBranchId(LoginHelper.getBranchId());
        } else {
            bo.setPaperSource("1");
        }
        return toAjax(paperService.updateByBo(bo));
    }

    /**
     * 删除试卷
     *
     * @param paperIds 主键串
     */
    @SaCheckPermission("student:paper:remove")
    @Log(title = "试卷", businessType = BusinessType.DELETE)
    @DeleteMapping("/{paperIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] paperIds) {
        return toAjax(paperService.deleteWithValidByIds(List.of(paperIds), true));
    }

    /**
     * 批量上传前的文件名检查
     */

    @PostMapping("/check")
    public R<List<PaperDocVo>> checkUploadFileNameList(@RequestBody List<String> fileNames) {
        return R.ok(paperService.checkUploadFileNameList(fileNames));
    }

    /**
     * 批量上传试卷
     */
    @SaCheckPermission("student:paper:add")
    @Log(title = "试卷", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/batch")
    public R<Void> batch(@Validated(AddGroup.class) @RequestBody PaperBo bo) {
        if (LoginHelper.isBranchAdmin() && LoginHelper.getBranchId() == null) {
            return R.fail("门店管理登录时请选择门店登录,才能新增试卷");
        }
        if (CollUtil.isEmpty(bo.getPaperDocList())){
            return R.fail("试卷文件不能为空");
        }
        if (LoginHelper.getBranchId() != null) {
            bo.setPaperSource("2");
            bo.setBranchId(LoginHelper.getBranchId());
        } else {
            bo.setPaperSource("1");
        }
        return toAjax(paperService.batchInsert(bo));
    }
}
