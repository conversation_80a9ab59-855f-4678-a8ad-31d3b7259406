package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 学生日常反馈分享
 */
@Data
@ExcelIgnoreUnannotated
public class StuDailyShareVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 学生姓名
     */
    private String studentName;
    /**
     * 分支名称
     */
    private String branchName;
    /**
     *  学生家长关系表id
     */
    private Long studentParentRecordId;
    /**
     * 学生创建时间
     */
    private Date studentCreateTime;

    /**
     * 反馈列表
     */
    private FeedbackRecordShareVo feedbackRecordVo;
    /**
     * 总学习时长
     */
    private Long totalStudyTime;
    /**
     * 这周学习时间
     */
    private Long thisWeekStudyTime;
    /**
     * 这次学习反馈学习了多少时间
     */
    private Long thisTimeStudyTime;

    /**
     * 完成学习次数
     */
    private Long doStudyTime;
    private Long doTestTime;
    private Long doTestAccuracy;
    private Long doLatestTestAccuracy;
    private Long doPracticeTime;
    /**
     * 排行榜
     */
    private List<StudyTimeRankVo.StudyTimeRankEntity> studyTimeRank;


    /**
     * 最近一次的学习规划
     */

    private StuShareLatestCourseVo latestCourseVo;
    /**
     * 这周完成的学习规划
     */
    private List<StudyPlanningRecordVo> thisTimePlanningList;
    /**
     * 资源列表
     */
    private RemoteKnowledgeResourceVo resourceVo;

    private List<RemoteKnowledgeResourceVo> resourceList;

    private RemoteStaffVo staffVo;

}
