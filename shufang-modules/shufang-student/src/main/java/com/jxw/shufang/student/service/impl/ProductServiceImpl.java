package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentProductTemplateVo;
import com.jxw.shufang.student.config.StudentIntroduceConfig;
import com.jxw.shufang.student.domain.Product;
import com.jxw.shufang.student.domain.bo.ProductBo;
import com.jxw.shufang.student.domain.vo.ProductVo;
import com.jxw.shufang.student.mapper.ProductMapper;
import com.jxw.shufang.student.service.IProductService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 产品（会员卡）Service业务层处理
 *
 *
 * @date 2024-02-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ProductServiceImpl implements IProductService, BaseService {

    private final ProductMapper baseMapper;

    private final StudentIntroduceConfig studentIntroduceConfig;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询产品（会员卡）
     */
    @Override
    public ProductVo queryById(Long productId) {
        return baseMapper.selectProductById(productId);
    }

    /**
     * 查询产品（会员卡）列表
     */
    @Override
    public TableDataInfo<ProductVo> queryPageList(ProductBo bo, PageQuery pageQuery) {
        QueryWrapper<Product> lqw = buildQueryWrapper(bo);
        Page<ProductVo> result = baseMapper.selectProductPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }



    /**
     * 查询产品（会员卡）列表
     */
    @Override
    public List<ProductVo> queryList(ProductBo bo) {
        LambdaQueryWrapper<Product> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    private LambdaQueryWrapper<Product> buildLambdaQueryWrapper(ProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Product> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentTypeId() != null, Product::getStudentTypeId, bo.getStudentTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Product::getProductName, bo.getProductName());
        lqw.eq(bo.getProductValidDays() != null, Product::getProductValidDays, bo.getProductValidDays());
        lqw.eq(StringUtils.isNotBlank(bo.getProductValidTimeLimit()), Product::getProductValidTimeLimit, bo.getProductValidTimeLimit());
        lqw.eq(bo.getProductPrice() != null, Product::getProductPrice, bo.getProductPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getProductStatus()), Product::getProductStatus, bo.getProductStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getProductIdList()), Product::getProductId, bo.getProductIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentTypeIdList()), Product::getStudentTypeId, bo.getStudentTypeIdList());
        lqw.ne(bo.getNeStudentTypeId() != null, Product::getStudentTypeId, bo.getNeStudentTypeId());
        lqw.in(bo.getDeptIdList() != null, Product::getCreateDept, bo.getDeptIdList());
        lqw.ne(Boolean.TRUE.equals(bo.getIsOfficialCard()), Product::getStudentTypeId,
            studentIntroduceConfig.getExperienceStudentTypeId());
        if (Boolean.TRUE.equals(bo.getIsPeriod())) {
            // 帮我优化一下，调整为这个字段不为空而且不能为''
            lqw.isNotNull(Product::getProductValidTimeLimit).ne(Product::getProductValidTimeLimit, "");
        }

        return lqw;
    }

    private QueryWrapper<Product> buildQueryWrapper(ProductBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<Product> lqw = Wrappers.query();
        lqw.eq(bo.getStudentTypeId() != null, "t.student_type_id", bo.getStudentTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), "t.product_name", bo.getProductName());
        lqw.eq(bo.getProductValidDays() != null, "t.product_valid_days", bo.getProductValidDays());
        lqw.eq(StringUtils.isNotBlank(bo.getProductValidTimeLimit()), "t.product_valid_time_limit", bo.getProductValidTimeLimit());
        lqw.eq(bo.getProductPrice() != null, "t.product_price", bo.getProductPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getProductStatus()),"t.product_status", bo.getProductStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getProductIdList()), "t.product_id", bo.getProductIdList());
        lqw.eq(ObjectUtil.isNotNull(bo.getStudentTypeId()), "st.student_type_id", bo.getStudentTypeId());

        if (bo.getProductValidTimeLimitStart()!=null&&bo.getProductValidTimeLimitEnd()!=null){
            lqw.and(wrapper->{
                wrapper.ge("SUBSTRING_INDEX(t.product_valid_time_limit,' 至 ',1)",bo.getProductValidTimeLimitStart())
                    .le("SUBSTRING_INDEX(t.product_valid_time_limit,' 至 ',-1)",bo.getProductValidTimeLimitEnd());
            });
        }

        return lqw;
    }

    /**
     * 新增产品（会员卡）
     */
    @Override
    public Boolean insertByBo(ProductBo bo) {
        if(StringUtils.isNotEmpty(bo.getProductValidTimeLimit())){
            this.processRangeTime(bo);
        }
        Product add = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(add);
        add.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProductId(add.getProductId());
        }
        return flag;
    }

    private void processRangeTime(ProductBo bo) {
        String[] timeRangeArray = bo.getProductValidTimeLimit().split(" 至 ");
        String startTimeStr = timeRangeArray[0];
        String endTimeStr = timeRangeArray[1];
        Date startDate = DateUtils.parseDate(startTimeStr);
        Date endDate = DateUtils.parseDate(endTimeStr);
        Date setStartOfDay = DateUtils.setStartOfDay(startDate);
        Date setEndOfDay = DateUtils.setEndOfDay(endDate);
        bo.setProductValidTimeLimit(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,setStartOfDay)+
            " 至 "+DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,setEndOfDay));
    }

    /**
     * 修改产品（会员卡）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ProductBo bo) {
        if(StringUtils.isNotEmpty(bo.getProductValidTimeLimit())){
            this.processRangeTime(bo);
        }
        Product update = MapstructUtils.convert(bo, Product.class);
        validEntityBeforeSave(update);
        int i = baseMapper.updateById(update);
        if (i <= 0) {
            return false;
        }
        //修改的时候，如果productValidTimeLimit改成productValidDays，productValidTimeLimit需要置空，反之亦然
        LambdaUpdateWrapper<Product> updateWrapper = Wrappers.lambdaUpdate(Product.class);
        updateWrapper.eq(Product::getProductId, bo.getProductId());
        if (StringUtils.isBlank(bo.getProductValidTimeLimit()) && bo.getProductValidDays() != null) {
            updateWrapper.set(Product::getProductValidTimeLimit, null);
        } else {
            updateWrapper.set(Product::getProductValidDays, null);
        }
        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Product entity) {
        //做一些数据校验,如唯一约束
        LambdaQueryWrapper<Product> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Product::getProductName, entity.getProductName());
        queryWrapper.ne(entity.getProductId() != null, Product::getProductId, entity.getProductId());
        if (baseMapper.selectCount(queryWrapper) > 0) {
            throw new ServiceException("产品名称已存在");
        }

        //productValidTimeLimit和productValidDays不能同时存在和不能同时为空
        if (StringUtils.isNotBlank(entity.getProductValidTimeLimit()) && entity.getProductValidDays() != null) {
            throw new ServiceException("产品有效期和产品有效天数不能同时存在");
        }
        if (StringUtils.isBlank(entity.getProductValidTimeLimit()) && entity.getProductValidDays() == null) {
            throw new ServiceException("产品有效期和产品有效天数不能同时为空");
        }
    }

    /**
     * 批量删除产品（会员卡）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<ProductVo> queryOptionList(ProductBo bo) {
        //如果是门店用户，只查询自己拥有的产品
        log.info("是否为门店用户：{}", LoginHelper.isBranchUser());

        if (LoginHelper.isBranchUser()){
            if (LoginHelper.getBranchId()==null&&CollUtil.isEmpty(bo.getProductIdList())){
                return List.of();
            }
            List<Long> productIdList = new ArrayList<>();

            //本店ID集合
            List<Long> branchIdList = ObjectUtil.isNotNull(LoginHelper.getBranchId()) ? ListUtil.toList(LoginHelper.getBranchId()) : LoginHelper.getBranchIdList();
            log.info("门店ID集合：{}", branchIdList);

            //根据门店ID查询本店对象
            List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(branchIdList);
            if (ObjectUtil.isEmpty(remoteBranchVos)) return List.of();
            //获取门店所属代理商ID集合
            List<Long> deptIds = remoteBranchVos.stream().map(remoteBranchVo -> remoteBranchVo.getCreateDept()).toList();
            //查询门店所属代理商的上级代理商ID集合
            List<RemoteDeptAccountVo> remoteDeptAccountVos = remoteDeptService.selectDeptByIds(deptIds);
            //获取上级代理商的ID集合
            List<Long> parentDeptIds = remoteDeptAccountVos.stream().map(remoteDeptAccountVo -> remoteDeptAccountVo.getParentId()).toList();


            //2B2C合并，授权模式更改，根据门店ID，查询该门店上级代理商的会员卡授权模板对象
            RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
            remoteStudentProductTemplateAuthBo.setTemplateType(0);//会员卡类型的授权模板
            remoteStudentProductTemplateAuthBo.setDeptIds(parentDeptIds);//所属代理商ID
            remoteStudentProductTemplateAuthBo.setShowAuth(true);//只返回授权的产品ID
            List<RemoteStudentProductTemplateVo> remoteStudentBaseTemplateAuthVos = remoteOrderService.queryAuthList(remoteStudentProductTemplateAuthBo);
            log.info("查询到上级代理商授权模板对象集合：{}", remoteStudentBaseTemplateAuthVos);
            if (ObjectUtil.isNotEmpty(remoteStudentBaseTemplateAuthVos)) {
                remoteStudentBaseTemplateAuthVos.stream()
                        .map(studentProductCourseTemplateAuthVo -> studentProductCourseTemplateAuthVo.getResIds())
                        .filter(ObjectUtil::isNotEmpty)
                        .flatMap(Collection::stream)
                        .forEach(productIdList::add);
            }
            log.info("获得产品ID集合：{}", productIdList);


            if (CollUtil.isEmpty(productIdList)){
                return List.of();
            }
            bo.setProductIdList(productIdList);
        }

        //只查询上架的产品
        bo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
        QueryWrapper<Product> productLambdaQueryWrapper = buildQueryWrapper(bo);
        return baseMapper.queryOptionList(productLambdaQueryWrapper);
    }

    @Override
    public int changeProductStatus(Long productId, String productStatus) {
        Product product = new Product();
        product.setProductId(productId);
        product.setProductStatus(productStatus);
        return baseMapper.updateById(product);
    }

    @Cacheable(value = "product", key = "#productId",condition = "#productId != null")
    @Override
    public Product queryProductById(Long productId) {
        return baseMapper.selectById(productId);
    }


    @CacheEvict(value = "product",allEntries= true)
    public void cleanCache(){
        log.info("===========productService cleanCache===========");
    }

    @Override
    public List<Long> queryStudentTypeIdByProductIds(List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)){
            return List.of();
        }
        LambdaQueryWrapper<Product> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(Product::getProductId, productIds);
        queryWrapper.select(Product::getStudentTypeId);
        List<Product> ignore = DataPermissionHelper.ignore(() -> baseMapper.selectList(queryWrapper));
        if (CollUtil.isEmpty(ignore)){
            return List.of();
        }
        return ignore.stream().map(Product::getStudentTypeId).distinct().toList();

    }

    @Override
    public ProductVo queryRecentPeriodCard(Long branchId) {
        RemoteBranchAuthTypeVo authTypeByBranchId = remoteBranchAuthTypeService.getAuthTypeByBranchId(branchId);
        List<Long> productIdList =
            authTypeByBranchId != null && StringUtils.isNotBlank(authTypeByBranchId.getProductIds())
                ? Arrays.stream(authTypeByBranchId.getProductIds().split(",")).map(Long::parseLong).toList() : null;
        if (CollUtil.isEmpty(productIdList)) {
            return null;
        }
        ProductBo bo = new ProductBo();
        bo.setProductIdList(productIdList);
        bo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
        bo.setIsPeriod(true);
        List<ProductVo> productVos = queryList(bo);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();
        return productVos.stream().filter(vo -> {
            String validTimeLimit = vo.getProductValidTimeLimit();
            return validTimeLimit != null && validTimeLimit.contains("至");
        }).min(Comparator.comparing(vo -> {
            String validTimeLimit = vo.getProductValidTimeLimit();
            String endStr = validTimeLimit.split("至")[1].trim();
            LocalDateTime endTime = LocalDateTime.parse(endStr, formatter);
            return Math.abs(Duration.between(now, endTime).toMillis());
        })).orElse(null);
    }


    @Override
    public void init() {
        IProductService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========productService init===========");
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Product::getProductId);
        List<Product> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========productService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryProductById(item.getProductId());
        });
        log.info("===========productService init end===========");
    }


}
