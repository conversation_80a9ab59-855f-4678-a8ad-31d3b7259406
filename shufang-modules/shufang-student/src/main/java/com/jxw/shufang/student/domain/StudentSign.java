package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员标签对象 student_sign
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_sign")
public class StudentSign extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员标签id
     */
    @TableId(value = "student_sign_id")
    private Long studentSignId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 标签内容
     */
    private String signContent;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
