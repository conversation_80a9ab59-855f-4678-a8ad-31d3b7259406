package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.StudyPlanningConflictVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningCourseRepeatVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;
import com.jxw.shufang.student.service.IStudyPlanningService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习规划
 * 前端访问路由地址为:/student/management/studyPlanning
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studyPlanning")
public class StudyPlanningController extends BaseController {

    private final IStudyPlanningService studyPlanningService;

    /**
     * 查询学习规划列表
     */
    @SaCheckPermission("student:studyPlanning:list")
    @GetMapping("/list")
    public TableDataInfo<StudyPlanningVo> list(StudyPlanningBo bo, PageQuery pageQuery) {
        return studyPlanningService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出学习规划列表
     */
    @SaCheckPermission("student:studyPlanning:export")
    @Log(title = "学习规划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyPlanningBo bo, HttpServletResponse response) {
        List<StudyPlanningVo> list = studyPlanningService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习规划", StudyPlanningVo.class, response);
    }

    /**
     * 获取学习规划详细信息
     *
     * @param studyPlanningId 主键
     */
    @SaCheckPermission("student:studyPlanning:query")
    @GetMapping("/{studyPlanningId}")
    public R<StudyPlanningVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long studyPlanningId) {
        return R.ok(studyPlanningService.queryById(studyPlanningId));
    }

    /**
     * 新增学习规划
     */
    @SaCheckPermission("student:studyPlanning:add")
    @Log(title = "学习规划", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyPlanningBo bo) {
        return toAjax(studyPlanningService.insertByBo(bo));
    }

    /**
     * 修改学习规划
     */
    @SaCheckPermission("student:studyPlanning:edit")
    @Log(title = "学习规划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyPlanningBo bo) {
        return toAjax(studyPlanningService.updateByBo(bo));
    }
    /**
     * 修改学习规划
     */
    @SaCheckPermission("student:studyPlanning:edit")
    @Log(title = "学习规划", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/v2")
    public R<Void> editV2(@Validated(EditGroup.class) @RequestBody StudyPlanningUpdateBoV2 bo) {
        return toAjax(studyPlanningService.updateByBoV2(bo));
    }


    /**
     * 删除学习规划
     *
     * @param studyPlanningRecordIds 主键串
     */
    @SaCheckPermission("student:studyPlanning:remove")
    @Log(title = "学习规划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyPlanningRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyPlanningRecordIds) {
        return toAjax(studyPlanningService.deleteWithValidByIds(List.of(studyPlanningRecordIds), true));
    }

    @PostMapping("/checkStudyPlanningConflict")
    public R<List<StudyPlanningConflictVo>> checkStudyPlanningConflict(@RequestBody List<StudyPlanningRecordBo> studyPlanningRecordBoList) {
        return R.ok(studyPlanningService.checkStudyPlanningConflict(studyPlanningRecordBoList));
    }

    @PostMapping("/checkStudyPlanningRepeatCourse")
    public R<List<StudyPlanningCourseRepeatVo>> checkStudyPlanningRepeatCourse(@RequestBody StudyCheckCourseRepeatBo repeatBo) {
        return R.ok(studyPlanningService.checkStudyPlanningRepeatCourse(repeatBo));
    }

    /**
     * 排课
     *
     * @param list 列表
     * @param arrangementMode 模式 1单人排课 2多人排课
     *
     * @date 2024/06/02 05:18:38
     */
    @SaCheckPermission("student:studyPlanning:add")
    @Log(title = "学习规划-排课", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/arrangementStudyPlanning")
    public R<Void> arrangementStudyPlanning(@RequestBody List<ArrangementStudyPlanningBo> list,Integer arrangementMode,Boolean noRepeatLessons) {
        //检查参数
        for (ArrangementStudyPlanningBo arrangementStudyPlanningBo : list) {
            List<StudyPlanningRecordBo> studyPlanningRecordList = arrangementStudyPlanningBo.getStudyPlanningRecordList();
            if(CollUtil.isEmpty(studyPlanningRecordList)){
                continue;
            }
            for (StudyPlanningRecordBo studyPlanningRecordBo : studyPlanningRecordList) {
                if (studyPlanningRecordBo.getStudyPlanningDate() == null) {
                    return R.fail("请检查学习规划的日期是否为空");
                }
                if (studyPlanningRecordBo.getStudyStartTime() == null || studyPlanningRecordBo.getStudyEndTime() == null) {
                    return R.fail("请检查学习规划详情的学习时间是否为空");
                }
                if (studyPlanningRecordBo.getCourseId() == null) {
                    return R.fail("请检查学习规划详情的课次是否为空");
                }
            }
        }
        if (arrangementMode == null||(arrangementMode!= 1 && arrangementMode!= 2)){
            return R.fail("请选择排课模式");
        }
        studyPlanningService.arrangementStudyPlanning(list,arrangementMode,noRepeatLessons);
        return R.ok();
    }

    /**
     * 查学习规划详情数量和安排了规划的学生数量
     */
    @SaCheckPermission("student:studyPlanning:list")
    @GetMapping("/recodeCountAndStudentCount")
    public R<StudyPlanningVo> recodeCountAndStudentCount(StudyPlanningBo bo) {
        return R.ok(studyPlanningService.recodeCountAndStudentCount(bo));
    }


}
