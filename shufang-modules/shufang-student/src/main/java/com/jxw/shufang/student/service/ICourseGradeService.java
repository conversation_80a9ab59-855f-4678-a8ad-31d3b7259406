package com.jxw.shufang.student.service;

import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.CourseGrade;
import com.jxw.shufang.student.domain.vo.CourseGradeVo;

import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/28
 */
public interface ICourseGradeService {
    /**
     * 保存课程年级记录
     *
     * @param courseGradeList
     * @return
     */
    boolean saveBatch(List<CourseGrade> courseGradeList);

    /**
     * 获取课程的年级列表
     *
     * @param courseId
     * @return
     */
    List<CourseGradeVo> listByCourseId(Long courseId);

    /**
     * 更新course的年级记录
     *
     * @param course
     * @param courseGradeList
     * @return
     */
    Boolean updateCourseGrade(Course course, List<CourseGrade> courseGradeList);

    List<CourseGrade> listByCourseIds(List<Long> courseIds);
}
