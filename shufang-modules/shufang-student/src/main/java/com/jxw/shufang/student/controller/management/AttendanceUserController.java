package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AttendanceUserBo;
import com.jxw.shufang.student.domain.vo.AttendanceUserVo;
import com.jxw.shufang.student.service.IAttendanceUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考勤关联用户
 * 前端访问路由地址为:/student/attendanceUser
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attendanceUser")
public class AttendanceUserController extends BaseController {

    private final IAttendanceUserService attendanceUserService;

    /**
     * 查询考勤关联用户列表
     */
    @SaCheckPermission("student:attendanceUser:list")
    @GetMapping("/list")
    public TableDataInfo<AttendanceUserVo> list(AttendanceUserBo bo, PageQuery pageQuery) {
        return attendanceUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出考勤关联用户列表
     */
    @SaCheckPermission("student:attendanceUser:export")
    @Log(title = "考勤关联用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttendanceUserBo bo, HttpServletResponse response) {
        List<AttendanceUserVo> list = attendanceUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "考勤关联用户", AttendanceUserVo.class, response);
    }

    /**
     * 获取考勤关联用户详细信息
     *
     * @param attendanceUserId 主键
     */
    @SaCheckPermission("student:attendanceUser:query")
    @GetMapping("/{attendanceUserId}")
    public R<AttendanceUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attendanceUserId) {
        return R.ok(attendanceUserService.queryById(attendanceUserId));
    }

    /**
     * 新增考勤关联用户
     */
    @SaCheckPermission("student:attendanceUser:add")
    @Log(title = "考勤关联用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttendanceUserBo bo) {
        return toAjax(attendanceUserService.insertByBo(bo));
    }

    /**
     * 修改考勤关联用户
     */
    @SaCheckPermission("student:attendanceUser:edit")
    @Log(title = "考勤关联用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttendanceUserBo bo) {
        return toAjax(attendanceUserService.updateByBo(bo));
    }

    /**
     * 删除考勤关联用户
     *
     * @param attendanceUserIds 主键串
     */
    @SaCheckPermission("student:attendanceUser:remove")
    @Log(title = "考勤关联用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attendanceUserIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attendanceUserIds) {
        return toAjax(attendanceUserService.deleteWithValidByIds(List.of(attendanceUserIds), true));
    }
}
