package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.student.domain.vo.AnswerProcessVo;
import com.jxw.shufang.student.domain.vo.LearnedCoursesRatesVo;
import com.jxw.shufang.student.domain.vo.StudyProcessVo;
import com.jxw.shufang.student.domain.vo.StudyTimeRankVo;

import java.util.Date;
import java.util.List;

/**
 * 数据统计Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface IStudentDataStatisticsService {

    /**
     * 获取学习情况
     */
    StudyProcessVo getStudyProcess(Long studentId);

    /**
     * 获取答题情况，只统计测验
     */
    AnswerProcessVo getAnswerProcess(Long studentId);

    /**
     * 获取已学课程分布
     */
    LearnedCoursesRatesVo getLearnedCoursesRates(Long studentId);

    /**
     * 获取学习时长排行榜 - 缓存版本
     * @param durType   时间类型 可选：今日D， 本周W， 本月M
     * @param self  是否查询自己 可选：是：1, 否：0
     * @return StudyTimeRankVo
     */
    StudyTimeRankVo getStudyTimeRankListCache(String durType, Integer self, PageQuery pageQuery);

    /**
     * 获取学习时长排行榜
     * @param durType   时间类型 可选：今日D， 本周W， 本月M
     * @param self  是否查询自己 可选：是：1, 否：0
     * @return StudyTimeRankVo
     */
    StudyTimeRankVo getStudyTimeRankList(String durType, Integer self, PageQuery pageQuery);

    /**
     * 获取学习时长排行榜基础信息，根据对应的日期范围
     * @param beginDate 开始时间
     * @param endDate 结束时间
     */
    List<StudyTimeRankVo.StudyTimeRankEntity> getStudyTimeRankListByDates(String durType, Date beginDate, Date endDate);


    StudyTimeRankVo getStudyTimeRankListForH5(Long studentId, String durType, Integer self);
}
