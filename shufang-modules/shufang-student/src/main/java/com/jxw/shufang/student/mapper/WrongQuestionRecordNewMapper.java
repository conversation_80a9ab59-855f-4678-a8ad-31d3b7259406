package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.WrongQuestionRecordNew;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordNewVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface WrongQuestionRecordNewMapper extends BaseMapperPlus<WrongQuestionRecordNew, WrongQuestionRecordNewVo> {

    Page<WrongQuestionRecordNewVo> selectWrongQuestionRecordNewPage(@Param("page") Page<WrongQuestionRecordNewVo> build, @Param(Constants.WRAPPER) LambdaQueryWrapper<WrongQuestionRecordNew> lqw);

}
