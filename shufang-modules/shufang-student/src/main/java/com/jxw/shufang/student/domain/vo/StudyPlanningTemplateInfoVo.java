package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudyPlanningTemplateInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 学习规划模板详情视图对象 study_planning_template_info
 *
 *
 * @date 2024-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanningTemplateInfo.class)
public class StudyPlanningTemplateInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划模板详情id
     */
    @ExcelProperty(value = "学习规划模板详情id")
    private Long studyPlanningTemplateInfoId;

    /**
     * 学习规划模板id
     */
    @ExcelProperty(value = "学习规划模板id")
    private Long studyPlanningTemplateId;

    /**
     * 课程id（章节）
     */
    @ExcelProperty(value = "课程id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "章=节")
    private Long courseId;

    /**
     * 课程名称(最顶层的课程id)
     */
    @ExcelProperty(value = "课程名称(最顶层的课程id)")
    private Long topmostCourseId;

    /**
     * 学习开始时间
     */
    @ExcelProperty(value = "学习开始时间")
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    @ExcelProperty(value = "学习结束时间")
    private Date studyEndTime;


    private CourseVo course;

    private CourseVo topmostCourse;


}
