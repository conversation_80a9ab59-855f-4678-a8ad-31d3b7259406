package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.PaperBo;
import com.jxw.shufang.student.domain.vo.PaperDocVo;
import com.jxw.shufang.student.domain.vo.PaperTypeGroupVo;
import com.jxw.shufang.student.domain.vo.PaperVo;

import java.util.Collection;
import java.util.List;

/**
 * 试卷Service接口
 *
 *
 * @date 2024-05-14
 */
public interface IPaperService {

    /**
     * 查询试卷
     */
    PaperVo queryById(Long paperId);

    /**
     * 查询试卷列表
     */
    TableDataInfo<PaperVo> queryPageList(PaperBo bo, PageQuery pageQuery);

    /**
     * 查询试卷列表
     */
    List<PaperVo> queryList(PaperBo bo);

    /**
     * 新增试卷
     */
    Boolean insertByBo(PaperBo bo);

    /**
     * 修改试卷
     */
    Boolean updateByBo(PaperBo bo);

    /**
     * 校验并批量删除试卷信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询总的试卷数量
     */
    Long queryTotalPaperCount(PaperBo paperBo);

    /**
     * 获取试卷分类及其试卷份数
     * @param paperBo
     */
    List<PaperTypeGroupVo> queryPaperTypeAndCount(PaperBo paperBo);

    List<PaperDocVo> checkUploadFileNameList(List<String> fileNames);

    Boolean batchInsert(PaperBo bo);
}
