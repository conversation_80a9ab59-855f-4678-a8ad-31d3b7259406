package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.FeedbackTemplateCollect;

import java.io.Serial;
import java.io.Serializable;


/**
 * 反馈模板收藏视图对象 feedback_template_collect
 *
 *
 * @date 2024-05-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackTemplateCollect.class)
public class FeedbackTemplateCollectVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈模板收藏id
     */
    @ExcelProperty(value = "反馈模板收藏id")
    private Long feedbackTemplateCollectId;

    /**
     * 反馈模板id
     */
    @ExcelProperty(value = "反馈模板id")
    private Long feedbackTemplateId;


}
