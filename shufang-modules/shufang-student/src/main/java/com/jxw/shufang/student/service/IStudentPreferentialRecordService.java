package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentPreferentialRecordBo;
import com.jxw.shufang.student.domain.vo.StudentPreferentialRecordVo;

import java.math.BigDecimal;

public interface IStudentPreferentialRecordService {
    /**
     * 保存会员优惠额度变动记录
     *
     * @param saveRecordBo
     * @return
     */
    boolean saveRecordByBo(StudentPreferentialRecordBo saveRecordBo);

    /**
     * 获取某个会员的冻结余额
     *
     * @param studentId
     * @return
     */
    BigDecimal getFrozenAmount(Long studentId);

    /**
     * 获取某条冻结的优惠额度变动记录
     *
     * @param bo
     * @return
     */
    StudentPreferentialRecordVo getFrozenRecord(StudentPreferentialRecordBo bo);

    /**
     * 清楚某条记录的冻结时间
     *
     * @param frozenQueryBo
     * @return
     */
    Boolean removeFrozenTime(StudentPreferentialRecordBo frozenQueryBo);

    /**
     * 获取优惠额度
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<StudentPreferentialRecordVo.PreferentialRecordVo> listRecord(StudentPreferentialRecordBo bo, PageQuery pageQuery);

    /**
     * 优惠额度转赠
     * @param bo
     * @return
     */
    Boolean transfer(StudentPreferentialRecordBo bo);
}
