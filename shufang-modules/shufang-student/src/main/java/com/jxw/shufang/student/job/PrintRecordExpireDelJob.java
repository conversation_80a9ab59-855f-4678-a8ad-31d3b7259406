package com.jxw.shufang.student.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.student.domain.PrintRecord;
import com.jxw.shufang.student.mapper.PrintRecordMapper;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class PrintRecordExpireDelJob implements BasicProcessor {

    private final PrintRecordMapper printRecordMapper;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    @Override
    public ProcessResult process(TaskContext context) {
        Date now = new Date();
        //查询配置的过期时间
        Integer printRecordExpireTime = remoteConfigService.selectPrintRecordExpireTime();
        //减去过期时间，得出再某个时间点之前的记录都是过期记录
        Date expireTime = DateUtils.addDays(now, - printRecordExpireTime);
        LambdaQueryWrapper<PrintRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.le(PrintRecord::getCreateTime, expireTime);
        lambdaQuery.select(PrintRecord::getPrintRecordId);
        List<PrintRecord> printRecords = printRecordMapper.selectList(lambdaQuery);
        if (printRecords.isEmpty()) {
            return new ProcessResult(true, "没有过期的打印记录");
        }
        List<Long> idList = printRecords.stream().map(PrintRecord::getPrintRecordId).collect(Collectors.toList());
        boolean b = printRecordMapper.deleteBatchIds(idList) > 0;
        if (!b) {
            return new ProcessResult(false, "处理失败，删除过期打印记录失败，id如下"+ idList);
        }
        return new ProcessResult(true, "处理成功"+ idList.size() + "条过期打印记录,id如下"+ idList);

    }
}
