package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.StudentAiCourseRecordInfo;

import java.io.Serial;
import java.io.Serializable;


/**
 * 记录分配课程视图对象 student_ai_course_record_info
 *
 *
 * @date 2024-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentAiCourseRecordInfo.class)
public class StudentAiCourseRecordInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录表分配课程id
     */
    @ExcelProperty(value = "记录表分配课程id")
    private Long studentAiCourseRecordInfoId;

    /**
     * 会员AI课程分配记录表id
     */
    @ExcelProperty(value = "会员AI课程分配记录表id")
    private Long studentAiCourseRecordId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;


    private CourseVo course;
}
