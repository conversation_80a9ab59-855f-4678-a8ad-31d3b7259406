package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudentInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）视图对象 student_info
 *
 *
 * @date 2024-03-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentInfo.class)
public class StudentInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员信息id
     */
    @ExcelProperty(value = "会员信息id")
    private Long studentInfoId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 省
     */
    @ExcelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @ExcelProperty(value = "市")
    private String city;

    /**
     * 县
     */
    @ExcelProperty(value = "县")
    private String county;

    /**
     * 就读学校（对应字典值） attending_school
     */
    @ExcelProperty(value = "就读学校", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String attendingSchool;

    /**
     * 在校班级（对应字典值） school_class
     */
    @ExcelProperty(value = "在校班级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String schoolClass;

    /**
     * 文理科（对应字典值） school_major
     */
    @ExcelProperty(value = "文理科", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String schoolMajor;

    /**
     * 住校情况（对应字典值） school_stay_type
     */
    @ExcelProperty(value = "住校情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String schoolStayType;

    /**
     * 家庭住址
     */
    @ExcelProperty(value = "家庭住址")
    private String studentAddress;

    /**
     * 是否开通快叮岛权益
     */
    private Boolean hasKuaidingPrivilege;

    /**
     * 快叮岛权益过期时间
     */
    private Date kuaidingPrivilegeExpireTime;

}
