package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）对象 student_parent_record
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_parent_record")
public class StudentParentRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员绑定家长记录id
     */
    @TableId(value = "student_parent_record_id")
    private Long studentParentRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 家长姓名
     */
    private String parentName;

    /**
     * 家长微信昵称
     */
    private String parentWechatNickname;

    /**
     * 家长微信号
     */
    private String parentWechatNo;

    /**
     * 家长微信头像
     */
    private String parentWechatImg;

    /**
     * 家长微信openId
     */
    private String parentWechatOpenId;

    /**
     * 家长微信unionId
     */
    private String parentWechatUnionId;

    /**
     * 是否关注公众号（0是 1否）
     */
    private String isFollow;




}
