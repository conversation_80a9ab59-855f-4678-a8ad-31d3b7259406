package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）对象 student_info
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_info")
public class StudentInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员信息id
     */
    @TableId(value = "student_info_id")
    private Long studentInfoId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 就读学校（对应字典值）
     */
    private String attendingSchool;

    /**
     * 在校班级（对应字典值）
     */
    private String schoolClass;

    /**
     * 文理科（对应字典值）
     */
    private String schoolMajor;

    /**
     * 住校情况（对应字典值）
     */
    private String schoolStayType;

    /**
     * 家庭住址
     */
    private String studentAddress;

    /**
     * 是否开通快叮岛权益
     */
    private Boolean hasKuaidingPrivilege;

    /**
     * 快叮岛权益过期时间
     */
    private Date kuaidingPrivilegeExpireTime;

}
