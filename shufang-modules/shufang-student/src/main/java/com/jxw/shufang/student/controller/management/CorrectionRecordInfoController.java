package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordInfoVo;
import com.jxw.shufang.student.service.ICorrectionRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 批改记录详情
 * 前端访问路由地址为:/student/correctionRecordInfo
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/correctionRecordInfo")
public class CorrectionRecordInfoController extends BaseController {

    private final ICorrectionRecordInfoService correctionRecordInfoService;

    /**
     * 查询批改记录详情列表
     */
    @SaCheckPermission("student:correctionRecordInfo:list")
    @GetMapping("/list")
    public TableDataInfo<CorrectionRecordInfoVo> list(CorrectionRecordInfoBo bo, PageQuery pageQuery) {
        return correctionRecordInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出批改记录详情列表
     */
    @SaCheckPermission("student:correctionRecordInfo:export")
    @Log(title = "批改记录详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CorrectionRecordInfoBo bo, HttpServletResponse response) {
        List<CorrectionRecordInfoVo> list = correctionRecordInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "批改记录详情", CorrectionRecordInfoVo.class, response);
    }

    /**
     * 获取批改记录详情详细信息
     *
     * @param correctionRecordInfoId 主键
     */
    @SaCheckPermission("student:correctionRecordInfo:query")
    @GetMapping("/{correctionRecordInfoId}")
    public R<CorrectionRecordInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long correctionRecordInfoId) {
        return R.ok(correctionRecordInfoService.queryById(correctionRecordInfoId));
    }

    /**
     * 新增批改记录详情
     */
    @SaCheckPermission("student:correctionRecordInfo:add")
    @Log(title = "批改记录详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CorrectionRecordInfoBo bo) {
        return toAjax(correctionRecordInfoService.insertByBo(bo));
    }

    /**
     * 修改批改记录详情
     */
    @SaCheckPermission("student:correctionRecordInfo:edit")
    @Log(title = "批改记录详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CorrectionRecordInfoBo bo) {
        return toAjax(correctionRecordInfoService.updateByBo(bo));
    }

    /**
     * 删除批改记录详情
     *
     * @param correctionRecordInfoIds 主键串
     */
    @SaCheckPermission("student:correctionRecordInfo:remove")
    @Log(title = "批改记录详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{correctionRecordInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] correctionRecordInfoIds) {
        return toAjax(correctionRecordInfoService.deleteWithValidByIds(List.of(correctionRecordInfoIds), true));
    }
}
