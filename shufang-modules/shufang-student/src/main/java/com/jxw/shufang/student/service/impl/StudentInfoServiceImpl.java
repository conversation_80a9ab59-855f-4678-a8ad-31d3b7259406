package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentInfo;
import com.jxw.shufang.student.domain.bo.StudentInfoBo;
import com.jxw.shufang.student.domain.vo.StudentInfoVo;
import com.jxw.shufang.student.mapper.StudentInfoMapper;
import com.jxw.shufang.student.service.IStudentInfoService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）Service业务层处理
 *
 *
 * @date 2024-03-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentInfoServiceImpl implements IStudentInfoService, BaseService {

    private final StudentInfoMapper baseMapper;

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @Override
    public StudentInfoVo queryById(Long studentInfoId){
        return baseMapper.selectVoById(studentInfoId);
    }

    @Override
    public StudentInfoVo queryByStudentId(Long studentId) {
        return null == studentId ? null : baseMapper.selectVoOne(
            Wrappers.lambdaQuery(StudentInfo.class).eq(StudentInfo::getStudentId, studentId).last(" limit 1 "));
    }

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    @Override
    public TableDataInfo<StudentInfoVo> queryPageList(StudentInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentInfo> lqw = buildQueryWrapper(bo);
        Page<StudentInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    @Override
    public List<StudentInfoVo> queryList(StudentInfoBo bo) {
        LambdaQueryWrapper<StudentInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentInfo> buildQueryWrapper(StudentInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentInfo::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getProvince()), StudentInfo::getProvince, bo.getProvince());
        lqw.eq(StringUtils.isNotBlank(bo.getCity()), StudentInfo::getCity, bo.getCity());
        lqw.eq(StringUtils.isNotBlank(bo.getCounty()), StudentInfo::getCounty, bo.getCounty());
        lqw.eq(StringUtils.isNotBlank(bo.getAttendingSchool()), StudentInfo::getAttendingSchool, bo.getAttendingSchool());
        lqw.eq(StringUtils.isNotBlank(bo.getSchoolClass()), StudentInfo::getSchoolClass, bo.getSchoolClass());
        lqw.eq(StringUtils.isNotBlank(bo.getSchoolMajor()), StudentInfo::getSchoolMajor, bo.getSchoolMajor());
        lqw.eq(StringUtils.isNotBlank(bo.getSchoolStayType()), StudentInfo::getSchoolStayType, bo.getSchoolStayType());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentAddress()), StudentInfo::getStudentAddress, bo.getStudentAddress());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), StudentInfo::getStudentId, bo.getStudentIdList());
        return lqw;
    }

    /**
     * 新增会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @Override
    public Boolean insertByBo(StudentInfoBo bo) {
        StudentInfo add = MapstructUtils.convert(bo, StudentInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentInfoId(add.getStudentInfoId());
        }
        return flag;
    }

    /**
     * 修改会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @Override
    public Boolean updateByBo(StudentInfoBo bo) {
        StudentInfo update = MapstructUtils.convert(bo, StudentInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }



    @Cacheable(value = "studentInfo", key = "#studentInfoId",condition = "#studentInfoId != null")
    @Override
    public StudentInfo queryStudentInfoById(Long studentInfoId) {
        return baseMapper.selectById(studentInfoId);
    }

    @CacheEvict(value = "studentInfo",allEntries= true)
    public void cleanCache(){
        log.info("===========studentInfoService cleanCache===========");
    }

    @Override
    public boolean updateKuaidingPrivilege(Collection<Long> studentIdList, Boolean hasKuaidingPrivilege,
        Date kuaidingPrivilegeExpireTime) {
        if (CollUtil.isEmpty(studentIdList)) {
            return false;
        }
        return baseMapper.update(Wrappers.lambdaUpdate(StudentInfo.class).in(StudentInfo::getStudentId, studentIdList)
            .set(StudentInfo::getHasKuaidingPrivilege, hasKuaidingPrivilege)
            .set(StudentInfo::getKuaidingPrivilegeExpireTime, kuaidingPrivilegeExpireTime)) > 0;
    }


    @Override
    public void init() {
        IStudentInfoService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentInfoService init===========");
        LambdaQueryWrapper<StudentInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentInfo::getStudentInfoId);
        List<StudentInfo> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========studentInfoService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentInfoById(item.getStudentInfoId());
        });
        log.info("===========studentInfoService init end===========");
    }


}
