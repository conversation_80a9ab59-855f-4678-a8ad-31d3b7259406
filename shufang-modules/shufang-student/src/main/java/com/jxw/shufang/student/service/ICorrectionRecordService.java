package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.CorrectionQuestionRecordVo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 批改记录Service接口
 *
 *
 * @date 2024-05-09
 */
public interface ICorrectionRecordService {

    /**
     * 查询批改记录
     */
    CorrectionRecordVo queryById(Long correctionRecordId);

    /**
     * 查询批改记录列表
     */
    TableDataInfo<CorrectionRecordVo> queryPageList(CorrectionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询批改记录列表
     */
    List<CorrectionRecordVo> queryList(CorrectionRecordBo bo);

    /**
     * 新增批改记录,并返回正确率(小数)
     */
    BigDecimal insertByBo(CorrectionRecordBo bo);

    /**
     * 修改批改记录
     */
    Boolean updateByBo(CorrectionRecordBo bo);

    /**
     * 校验并批量删除批改记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    CorrectionRecordVo getOnce(CorrectionRecordBo bo);

    /**
     * 查询问题记录
     *
     * @param studyPlanningRecordId 研究计划记录id
     * @param correctionType        批改类型 1=测试,2=练习
     *
     * @date 2024/05/10 02:32:28
     */
    CorrectionQuestionRecordVo queryQuestionRecord(Long studyPlanningRecordId,String correctionType);

    /**
     * 查询批改记录,会携带正确题目数量，错误题目数量，半对错题目数量，未做题目数量
     * @param correctionRecordBo
     * @return
     */
    List<CorrectionRecordVo> queryRecordAndRightWrongInfo(CorrectionRecordBo correctionRecordBo);

    Long count(CorrectionRecordBo convert);

    Boolean submitWithoutCorrection(CorrectionRecordBo correctionRecordBo);

    CorrectionRecordVo queryRecord(Long studyPlanningRecordId, String correctionType);

    boolean resetStudentAnswerRecord(List<Long> correctionRecordIds, Integer type);
}
