package com.jxw.shufang.student.domain.bo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/8 13:47
 * @Version 1
 * @Description
 */
@Data
public class BatchAiReportProcessBO {
    @NotEmpty(message = "学习记录不能为空")
    @Size(max = 20, message = "记录数量不能超过20条")
    private List<RecordProcess> records;

    @Data
    @Valid
    public static class RecordProcess {
        @NotBlank(message = "学习类型不能为空")
        private String studyType;
        /**
         * 课程id
         */
        @NotNull(message = "课程id不能为空")
        private Long courseId;
        /**
         * 视频id
         */
        @NotNull(message = "视频id不能为空")
        private Long videoId;
        /**
         * 问题id
         */
        private Long questionId;
        /**
         * 倍数
         */
        @NotNull(message = "倍数不能为空")
        private Double multiple;
        /**
         * 视频进度点
         */
        @NotBlank(message = "视频分片数据不能为空")
        private String spliceItem;
    }
}
