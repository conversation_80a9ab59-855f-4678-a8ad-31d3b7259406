package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.StudentSign;

import java.io.Serial;
import java.io.Serializable;


/**
 * 会员标签视图对象 student_sign
 *
 *
 * @date 2024-03-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentSign.class)
public class StudentSignVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员标签id
     */
    @ExcelProperty(value = "会员标签id")
    private Long studentSignId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 标签内容
     */
    @ExcelProperty(value = "标签内容")
    private String signContent;


}
