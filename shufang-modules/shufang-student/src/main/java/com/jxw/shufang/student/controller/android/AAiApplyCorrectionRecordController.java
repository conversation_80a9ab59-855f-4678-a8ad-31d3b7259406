package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.student.service.IRemoteMessageService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;
import com.jxw.shufang.student.service.IAiApplyCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI申请批改记录--平板端
 * 前端访问路由地址为:/student/android/aiApplyCorrectionRecord
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/aiApplyCorrectionRecord")
public class AAiApplyCorrectionRecordController extends BaseController {

    private final IAiApplyCorrectionRecordService aiApplyCorrectionRecordService;

    private final IRemoteMessageService remoteMessageService;


    /**
     * 查询学习规划自主审批申请状态,如果为空就代表没有申请记录,1=允许,2=拒绝,0待审核
     *
     * @param applyType           申请批改类型 1练习  2测试
     * @param courseId 课程Id
     *
     * @date 2024/05/08 03:26:20
     */
    @GetMapping("/applyResult")
    public R<String> applyResult(@NotBlank(message = "申请类型不能为空") String applyType, @NotNull(message = "课程Id不能为空") Long courseId) {
        Long studentId = LoginHelper.getStudentId();
        AiApplyCorrectionRecordBo aiApplyCorrectionRecordBo = new AiApplyCorrectionRecordBo();
        aiApplyCorrectionRecordBo.setStudentId(studentId);
        aiApplyCorrectionRecordBo.setCourseId(courseId);
        aiApplyCorrectionRecordBo.setApplyType(applyType);
        AiApplyCorrectionRecordVo applyCorrectionRecordVo = aiApplyCorrectionRecordService.queryLastRecord(aiApplyCorrectionRecordBo);
        return R.ok("查询成功",
            applyCorrectionRecordVo != null
                && aiApplyCorrectionRecordService.wheatherApplyRecordVisiable(applyCorrectionRecordVo)
                    ? applyCorrectionRecordVo.getApplyResult() : null);
    }

    /**
     * 提交自主审批
     */
    @PostMapping()
    @Log(title = "Ai申请批改--平板端", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    public R<Void> submit(@Validated(AddGroup.class) @RequestBody AiApplyCorrectionRecordBo bo) {
        Long studentId = LoginHelper.getStudentId();
        bo.setStudentId(studentId);
        return toAjax(aiApplyCorrectionRecordService.insertByBo(bo));
    }


}
