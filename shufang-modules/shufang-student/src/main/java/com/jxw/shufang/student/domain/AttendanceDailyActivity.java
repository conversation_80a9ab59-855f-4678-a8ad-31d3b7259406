package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员每日打卡登录情况表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04 11:57:25
 */
@Data
@Accessors(chain = true)
@TableName("attendance_daily_activity")
public class AttendanceDailyActivity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_daily_activity_id")
    private Long attendanceDailyActivityId;

    /**
     * 会员id
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 每日日期
     */
    @TableField("record_date")
    private Date recordDate;

    /**
     * 打卡/登录时间
     */
    @TableField("event_time")
    private Date eventTime;

    /**
     * 事件时间来源（1：考勤打卡。2：设备登录）
     */
    @TableField("event_time_source")
    private Integer eventTimeSource;


    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
