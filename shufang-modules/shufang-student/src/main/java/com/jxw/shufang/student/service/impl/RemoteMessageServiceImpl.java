package com.jxw.shufang.student.service.impl;

import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IRemoteMessageService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteNotifyMessageService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.domain.bo.RemoteNotifyMessageBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class RemoteMessageServiceImpl implements IRemoteMessageService {

    @DubboReference
    private RemoteNotifyMessageService remoteNotifyMessageService;
    @DubboReference
    private RemoteUserService remoteUserService;

    private final IStudentService studentService;

    @Override
    public void sendMessage(RemoteMessageDTO messageDTO) {
        RemoteNotifyMessageBo notice = new RemoteNotifyMessageBo();
        notice.setTemplateCode(messageDTO.getTemplateCode());
        notice.setBizType(messageDTO.getBizType());
        notice.setNoticeType(messageDTO.getNoticeType());
        notice.setContent(messageDTO.getContent());

        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(messageDTO.getStudentId());
        studentBo.setWithConsultantInfo(Boolean.TRUE);
        StudentVo studentVo = studentService.queryById(studentBo);

        Long toUserId = studentVo.getConsultant().getUser().getUserId();
        notice.setFromUserId(messageDTO.getStudentId());
        notice.setFromUserName(studentVo.getStudentName());
        notice.setToUserId(toUserId);
        notice.setToUserName(studentVo.getConsultant().getUser().getUserName());

        Map<String, Object> paramMap = messageDTO.getParamMap();
        if (Objects.nonNull(paramMap)) {
            paramMap.put(NotifyMessageConstant.USER_ID, messageDTO.getStudentId());
            paramMap.put(NotifyMessageConstant.USER_NAME, studentVo.getStudentName());
            notice.setParamMap(paramMap);
        }
        remoteNotifyMessageService.sendMessage(notice);

        // 给执行店长发通知消息
        remoteUserService.queryUserOfRole(
                studentVo.getConsultant().getUser().getDeptId(), StaffRole.EXECUTIVE_STORE_MANAGER.getRoleId())
                .forEach(i -> {
                    if (!i.getUserId().equals(toUserId)) {
                        notice.setToUserId(i.getUserId());
                        notice.setToUserName(i.getNickName());
                        remoteNotifyMessageService.sendMessage(notice);
                    }
                });

    }

}
