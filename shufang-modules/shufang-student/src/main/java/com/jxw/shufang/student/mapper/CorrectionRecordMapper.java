package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.CorrectionRecord;
import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;

import java.util.List;

/**
 * 批改记录Mapper接口
 *
 *
 * @date 2024-05-09
 */
public interface CorrectionRecordMapper extends BaseMapperPlus<CorrectionRecord, CorrectionRecordVo> {

    List<CorrectionRecordVo> queryRecordAndRightWrongInfo(@Param(Constants.WRAPPER) QueryWrapper<CorrectionRecord> correctionRecordQueryWrapper);
}
