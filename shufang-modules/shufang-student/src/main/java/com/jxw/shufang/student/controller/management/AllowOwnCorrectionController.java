package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo;
import com.jxw.shufang.student.service.IAllowOwnCorrectionService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 允许自主批改
 * 前端访问路由地址为:/student/management/allowOwnCorrection
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/allowOwnCorrection")
public class AllowOwnCorrectionController extends BaseController {

    private final IAllowOwnCorrectionService allowOwnCorrectionService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    /**
     * 查询允许自主批改数量
     */
    @GetMapping("/countAllowOwn")
    public R<Long> countAllowOwn(AllowOwnCorrectionBo bo) {
        bo.setType(StringUtils.isNotBlank(bo.getType())?bo.getType():"1");//默认为 1学习规划
        return R.ok(allowOwnCorrectionService.count(bo));
    }

    /**
     * 查询允许自主批改列表
     */
    @SaCheckPermission("student:allowOwnCorrection:list")
    @GetMapping("/list")
    public TableDataInfo<AllowOwnCorrectionVo> list(AllowOwnCorrectionBo bo, PageQuery pageQuery) {
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return allowOwnCorrectionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出允许自主批改列表
     */
    @SaCheckPermission("student:allowOwnCorrection:export")
    @Log(title = "允许自主批改", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AllowOwnCorrectionBo bo, HttpServletResponse response) {
        List<AllowOwnCorrectionVo> list = allowOwnCorrectionService.queryList(bo);
        ExcelUtil.exportExcel(list, "允许自主批改", AllowOwnCorrectionVo.class, response);
    }

    /**
     * 获取允许自主批改详细信息
     *
     * @param allowOwnCorrectionId 主键
     */
    @SaCheckPermission("student:allowOwnCorrection:query")
    @GetMapping("/{allowOwnCorrectionId}")
    public R<AllowOwnCorrectionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long allowOwnCorrectionId) {
        return R.ok(allowOwnCorrectionService.queryById(allowOwnCorrectionId));
    }

    /**
     * 新增允许自主批改
     */
    @SaCheckPermission("student:allowOwnCorrection:add")
    @Log(title = "允许自主批改", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AllowOwnCorrectionBo bo) {
        if(null == bo.getType()){
            bo.setType("1");
        }
        return toAjax(allowOwnCorrectionService.insertByBo(bo));
    }

//    /**
//     * 修改允许自主批改
//     */
//    @SaCheckPermission("student:allowOwnCorrection:edit")
//    @Log(title = "允许自主批改", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AllowOwnCorrectionBo bo) {
//        return toAjax(allowOwnCorrectionService.updateByBo(bo));
//    }

    /**
     * 修改允许自主批改
     *
     * @param studentId          学生id
     * @param type               应用类型，1学习规划  2ai学习
     *
     * @date 2024/06/06 04:45:15
     */
    @SaCheckPermission("student:allowOwnCorrection:edit")
    @Log(title = "修改允许自主批改", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/changeAllowOwnCorrection")
    public R<Void> changeAllowOwnCorrection(@NotNull(message = "会员id不能为空") Long studentId, @NotBlank(message = "type不能为空") String type,@NotBlank(message = "allowType不能为空") String allowType) {
        if (!type.equals("1") && !type.equals("2")) {
            return R.fail("type参数错误");
        }
        if (!allowType.equals("1") && !allowType.equals("2")) {
            return R.fail("allowType参数错误");
        }
        return toAjax(allowOwnCorrectionService.changeAllowOwnCorrection(studentId,type,allowType));
    }

    /**
     * 删除允许自主批改
     *
     * @param allowOwnCorrectionIds 主键串
     */
    @SaCheckPermission("student:allowOwnCorrection:remove")
    @Log(title = "允许自主批改", businessType = BusinessType.DELETE)
    @DeleteMapping("/{allowOwnCorrectionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] allowOwnCorrectionIds) {
        return toAjax(allowOwnCorrectionService.deleteWithValidByIds(List.of(allowOwnCorrectionIds), true));
    }
}
