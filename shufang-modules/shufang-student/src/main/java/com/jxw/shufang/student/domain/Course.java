package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 课程（课程包含章节）对象 course
 *
 *
 * @date 2024-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("course")
public class Course extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程id
     */
    @TableId(value = "course_id")
    private Long courseId;

    /**
     * 祖先节点（最顶层默认0）
     */
    private String ancestors;

    /**
     * 年级（对应字典值 course_grade）（只有最顶层的课程才会存）
     */
    private String grade;

    /**
     * 学段（对应字典值 course_stage）（只有最顶层的课程才会存）
     */
    private String stage;

    /**
     * 归属学科（对应字典值 course_affiliation_subject）（只有最顶层的课程才会存）
     */
    private String affiliationSubject;

    /**
     * 与外部资源知识点id关联（只有最叶子节点的课程才会存）
     */
    private Long knowledgeId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程简介（只有最顶层的课程才会存）
     */
    private String courseIntroduction;

    /**
     * 课程代码（只有最顶层的课程才会存）
     */
    private String courseNo;

    /**
     * 课程缩略图（oss_id）（只有最顶层的课程才会存）
     */
    private Long courseThumbnail;

    /**
     * 课程父节点id（顶级课程默认为0）
     */
    private Long courseParentId;

    /**
     * 顶级课程id
     */
    private Long topCourseId;

    /**
     * 类型(1课程 2章 3节 4小节 5小小节 以此类推)
     */
    private Integer courseType;

    /**
     * 课程来源（对应字典值 course_source）（只有最顶层的课程才会存）
     */
    private String courseSource;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 课程专题，对应字典course_special_topic （只有最顶层的课程才会存）
     */
    private String specialTopic;

    /**
     * 地区，省市县之间用空格分割，理应与试卷关联，但是试卷来自第三方，并且没有返回地区数据，这里只能先跟课程关联 （只有最顶层的课程才会存）
     */
    private String region;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 季度类型凑得（ 字典code）
     */
    private String quarterType;



}
