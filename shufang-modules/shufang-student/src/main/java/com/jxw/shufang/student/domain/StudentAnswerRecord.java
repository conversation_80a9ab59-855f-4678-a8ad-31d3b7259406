package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_answer_record")
public class StudentAnswerRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "student_answer_record_id")
    private Long studentAnswerRecordId;

    /**
     * 用户id
     */
    private Long studentId;

    /**
     * 试卷ID
     */
    private Long testPaperId;


    /**
     * 题目ID
     */
    private Long questionId;
    private Long studentPaperRecordId;
    /**
     * 0：false（否->不是错误->正确）
     * 1：true(是->是错误->错误)
     */
    @TableField("is_mistake")
    private Integer isMistake;

    private String answer;

    private Integer stayTime;
    /**
     * 来源
     */
    private String source;

    private Date startTime;

    private Date finishTime;


}
