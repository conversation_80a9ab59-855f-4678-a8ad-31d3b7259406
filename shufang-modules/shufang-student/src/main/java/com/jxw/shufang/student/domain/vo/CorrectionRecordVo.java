package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.CorrectionRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 批改记录视图对象 correction_record
 *
 *
 * @date 2024-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CorrectionRecord.class)
public class CorrectionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批改记录id
     */
    @ExcelProperty(value = "批改记录id")
    private Long correctionRecordId;

    /**
     * 学习规划记录ID
     */
    @ExcelProperty(value = "学习规划记录ID")
    private Long studyPlanningRecordId;

    /**
     * 批改类型（1测试 2练习）
     */
    @ExcelProperty(value = "批改类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=测试,2=练习")
    private String correctionType;

    /**
     * 批改人类型（1顾问 2会员）
     */
    @ExcelProperty(value = "批改人类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=顾问,2=会员")
    private String correctionPersonType;

    /**
     * 批改截图（oss_id，多个，逗号隔开）
     */
    @ExcelProperty(value = "批改截图", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id，多个，逗号隔开")
    private String correctionScreenshots;

    private List<String> correctionScreenshotsUrl;


    /**
     * 会员Id
     */
    @ExcelProperty(value = "会员ID")
    private Long studentId;

    /**
     * 正确数量
     */
    private Integer rightCount;

    /**
     * 错误数量（包含未做题目）(不包含半对错)
     */
    private Integer wrongCount;

    /**
     * 半对错
     */
    private Integer rightWrongCount;

    /**
     * 未做题目数量
     */
    private Integer notAnswerCount;


}
