package com.jxw.shufang.student;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.Import;

/**
 * 订单模块
 *
 *
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = "com.jxw.shufang")
@Import(RocketMQAutoConfiguration.class)
public class ShufangStudentApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangStudentApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  会员模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
