package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 敏感词对象 sensitive
 *
 *
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("`sensitive`")
public class Sensitive extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 敏感词id
     */
    @TableId(value = "sensitive_id")
    private Long sensitiveId;

    /**
     * 敏感词组名
     */
    private String sensitiveGroupName;

    /**
     * 敏感词内容（多个，换行符隔开）
     */
    private String sensitiveContent;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
