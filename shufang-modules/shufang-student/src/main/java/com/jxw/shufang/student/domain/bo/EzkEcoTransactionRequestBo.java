package com.jxw.shufang.student.domain.bo;

import lombok.Data;

/**
 * EZKEco考勤记录业务对象 ezk_eco_transaction
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
public class EzkEcoTransactionRequestBo {

//    /**
//     * 调用接口凭证
//     */
//    private String key;

    /**
     * 开始时间
     * 开始时间结束时间跨度不得超过一个月
     * 格式：%Y-%m-%d %H:%M:%S
     * 示例：2018-04-25 00:00:01
     */
    private String starttime;

    /**
     * 结束时间
     * 格式：%Y-%m-%d %H:%M:%S
     * 示例：2018-04-25 23:00:01
     */
    private String endtime;

    /**
     * 人员编号（可选）
     */
    private String pin;

    /**
     * 设备序列号（可选）
     */
    private String sn;

    /**
     * 记录流水号（可选）
     * 记录流水号自动递增
     * 存在id时，开始时间和结束时间可为非必选参数
     */
    private Long id;

    /**
     * 条数（可选）
     * 每次请求获取的记录数，建议控制在每次2000条以内
     */
    private Integer number;

    /**
     * 是否传考勤照片（可选）
     * 1 表示为是
     * 0 表示为否
     */
    private Integer uploadPic;

    /**
     * 是否传体温与是否佩戴口罩（可选）
     * 1 表示为是
     * 0 表示为否
     */
    private Integer getTemperature;

}
