package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.CorrectionRecordInfo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordInfoVo;
import com.jxw.shufang.student.mapper.CorrectionRecordInfoMapper;
import com.jxw.shufang.student.service.ICorrectionRecordInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 批改记录详情Service业务层处理
 *
 *
 * @date 2024-05-09
 */
@RequiredArgsConstructor
@Service
public class CorrectionRecordInfoServiceImpl implements ICorrectionRecordInfoService, BaseService {

    private final CorrectionRecordInfoMapper baseMapper;

    /**
     * 查询批改记录详情
     */
    @Override
    public CorrectionRecordInfoVo queryById(Long correctionRecordInfoId){
        return baseMapper.selectVoById(correctionRecordInfoId);
    }

    /**
     * 查询批改记录详情列表
     */
    @Override
    public TableDataInfo<CorrectionRecordInfoVo> queryPageList(CorrectionRecordInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CorrectionRecordInfo> lqw = buildQueryWrapper(bo);
        Page<CorrectionRecordInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询批改记录详情列表
     */
    @Override
    public List<CorrectionRecordInfoVo> queryList(CorrectionRecordInfoBo bo) {
        LambdaQueryWrapper<CorrectionRecordInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CorrectionRecordInfo> buildQueryWrapper(CorrectionRecordInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CorrectionRecordInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, CorrectionRecordInfo::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getCorrectionRecordId() != null, CorrectionRecordInfo::getCorrectionRecordId, bo.getCorrectionRecordId());
        lqw.in(CollUtil.isNotEmpty(bo.getCorrectionRecordIdList()), CorrectionRecordInfo::getCorrectionRecordId, bo.getCorrectionRecordIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), CorrectionRecordInfo::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), CorrectionRecordInfo::getAnswerResult, bo.getAnswerResult());
        return lqw;
    }

    /**
     * 新增批改记录详情
     */
    @Override
    public Boolean insertByBo(CorrectionRecordInfoBo bo) {
        CorrectionRecordInfo add = MapstructUtils.convert(bo, CorrectionRecordInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCorrectionRecordInfoId(add.getCorrectionRecordInfoId());
        }
        return flag;
    }

    /**
     * 修改批改记录详情
     */
    @Override
    public Boolean updateByBo(CorrectionRecordInfoBo bo) {
        CorrectionRecordInfo update = MapstructUtils.convert(bo, CorrectionRecordInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CorrectionRecordInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除批改记录详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<CorrectionRecordInfoBo> correctionRecordInfoBoList) {
        List<CorrectionRecordInfo> correctionRecordInfoList = MapstructUtils.convert(correctionRecordInfoBoList, CorrectionRecordInfo.class);
        return baseMapper.insertBatch(correctionRecordInfoList);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
