package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 试卷收藏对象 paper_collection
 *
 *
 * @date 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paper_collection")
public class PaperCollection extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收藏ID
     */
    @TableId(value = "collection_id")
    private Long collectionId;

    /**
     * 试卷id
     */
    private Long paperId;

    /**
     * 会员id
     */
    private Long studentId;


}
