package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentType;
import com.jxw.shufang.student.domain.bo.StudentTypeBo;
import com.jxw.shufang.student.domain.vo.StudentTypeVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）Service接口
 *
 *
 * @date 2024-03-01
 */
public interface IStudentTypeService {

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    StudentTypeVo queryById(Long studentTypeId);

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    TableDataInfo<StudentTypeVo> queryPageList(StudentTypeBo bo, PageQuery pageQuery);

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    List<StudentTypeVo> queryList(StudentTypeBo bo);

    /**
     * 新增会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    Boolean insertByBo(StudentTypeBo bo);

    /**
     * 修改会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    Boolean updateByBo(StudentTypeBo bo);

    /**
     * 校验并批量删除会员类型（会员卡的类型，默认有一个 体验卡 类型）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<StudentTypeVo> options(StudentTypeBo bo);

    StudentType queryStudentTypeById(Long studentTypeId);

    void cleanCache();

    StudentTypeVo getExperienceStudentType();

    List<StudentTypeVo> auth(StudentTypeBo bo);
}
