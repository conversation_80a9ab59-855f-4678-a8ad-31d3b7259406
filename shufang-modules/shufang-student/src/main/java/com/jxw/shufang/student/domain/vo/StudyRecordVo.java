package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudyRecord;

import java.io.Serial;
import java.io.Serializable;


/**
 * 学习记录视图对象 study_record
 *
 * @date 2024-04-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyRecord.class)
public class StudyRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习记录id
     */
    @ExcelProperty(value = "学习记录id")
    private Long studyRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 学习规划记录Id
     */
    private Long studyPlanningRecordId;

    /**
     * 学习视频时长（单位为秒 此为多个视频总计）
     */
    @ExcelProperty(value = "学习视频时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位为秒,此=为多个视频总计")
    private Long studyVideoTotalDuration;

    /**
     * 练习状态（1未批改 2批改中 3已批改）
     */
    @ExcelProperty(value = "练习状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=未批改,2=批改中,3=已批改")
    private String practiceState;

    /**
     * 练习-正确题目数量
     */
    @ExcelProperty(value = "练习-正确题目数量")
    private Long practiceRightNum;

    /**
     * 练习-错误题目数量
     */
    @ExcelProperty(value = "练习-错误题目数量")
    private Long practiceWrongNum;

    /**
     * 练习-未答题目数量
     */
    @ExcelProperty(value = "练习-未答题目数量")
    private Long practiceUnansweredNum;

    /**
     * 练习视频时长（单位为秒 此为多个视频总计）
     */
    @ExcelProperty(value = "练习视频时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位为秒,此=为多个视频总计")
    private Long practiceVideoTotalDuration;

    /**
     * 测验状态（1未批改 2批改中 3已批改）
     */
    @ExcelProperty(value = "测验状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=未批改,2=批改中,3=已批改")
    private String testState;

    /**
     * 测验-正确题目数量
     */
    @ExcelProperty(value = "测验-正确题目数量")
    private Long testRightNum;

    /**
     * 测验-错误题目数量
     */
    @ExcelProperty(value = "测验-错误题目数量")
    private Long testWrongNum;

    /**
     * 测验-未答题目数量
     */
    @ExcelProperty(value = "测验-未答题目数量")
    private Long testUnansweredNum;

    /**
     * 测验视频时长（单位为秒 此为多个视频总计）
     */
    @ExcelProperty(value = "测验视频时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位为秒,此=为多个视频总计")
    private Long testVideoTotalDuration;


    public void cleanTestState() {
        this.testState = "1";
        this.testRightNum = 0L;
        this.testWrongNum = 0L;
        this.testUnansweredNum = 0L;
//        this.testVideoTotalDuration = 0L;

    }

    public void cleanPracticeState() {
        this.practiceState = "1";
        this.practiceRightNum = 0L;
        this.practiceWrongNum = 0L;
        this.practiceUnansweredNum = 0L;
//        this.practiceVideoTotalDuration = 0L;
    }
}
