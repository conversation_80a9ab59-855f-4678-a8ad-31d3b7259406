package com.jxw.shufang.student.enums;

import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Getter
@AllArgsConstructor
public enum PreferentialInOrOutEnum {
    IN(0, "入账"),
    OUT(1, "出账");

    private final Integer type;
    private final String typeName;

    public static PreferentialInOrOutEnum getByType(Integer type){
        for (PreferentialInOrOutEnum inOrOutEnum : values()) {
            if (inOrOutEnum.getType().equals(type)) {
                return inOrOutEnum;
            }
        }
        throw new ServiceException("优惠额度出入帐类型异常");
    }
}
