package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentAnswerRecordBo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordAndSubmitBo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordBo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordUrlBo;
import com.jxw.shufang.student.domain.vo.StudentPaperRecordVo;
import com.jxw.shufang.student.service.IStudentAnswerRecordService;
import com.jxw.shufang.student.service.IStudentPaperRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户答题试卷记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paperRecord")
public class AStudentPaperRecordController extends BaseController {

    private final IStudentPaperRecordService studentPaperRecordService;
    private final IStudentAnswerRecordService answerRecordService;


    /**
     * 获取用户答题试卷记录详细信息
     *
     * @param studentPaperRecordId 主键
     */
    @GetMapping("/{studentPaperRecordId}")

    public R<StudentPaperRecordVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long studentPaperRecordId) {
        return R.ok(studentPaperRecordService.queryById(studentPaperRecordId));
    }


    /**
     * true为存在
     *
     * @param userId 主键
     */
    @GetMapping("/checkSignIsExist")

    public R<Boolean> checkSignIsExist(@NotNull(message = "主键不能为空") Long userId) {
        return R.ok(!studentPaperRecordService.checkSignIsNotExist(userId));
    }


    /**
     * 新增用户答题试卷记录
     */
    @Log(title = "用户答题试卷记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<StudentPaperRecordVo> add(@Validated(AddGroup.class) @RequestBody StudentPaperRecordBo bo) {
        return R.ok(studentPaperRecordService.insertByBo(bo));
    }

    /**
     * 修改用户答题试卷记录
     */
    @Log(title = "用户答题试卷记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentPaperRecordBo bo) {
        return toAjax(studentPaperRecordService.updateByBo(bo));
    }


    /**
     * 修改用户答题试卷记录
     */
    @Log(title = "用户答题上传pdf", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/uploadPdf")
    public R<Void> uploadPdf(@Validated @RequestBody StudentPaperRecordUrlBo bo) {
        return toAjax(studentPaperRecordService.uploadFile(bo.getStudentPaperRecordId(), bo.getReportUrl()));
    }


    /**
     * 修改用户答题试卷记录
     */
    @Log(title = "用户完成答题并计算正确率", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/finishAndCalculate")
    public R<Void> finishAndCalculate(@NotNull(message = "主键不能为空") Long studentPaperRecordId) {
        return toAjax(studentPaperRecordService.finishAndCalculate(studentPaperRecordId));
    }

    /**
     * 修改用户答题试卷记录
     */
    @Log(title = "用户完成答题", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/finish")
    public R<Void> finish(@NotNull(message = "主键不能为空") Long studentPaperRecordId) {
        return toAjax(studentPaperRecordService.finish(studentPaperRecordId));
    }


    /**
     * 修改用户答题试卷记录
     */
    @Log(title = "上报用户答题", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sign")
    public R<Void> checkSign(@NotNull(message = "主键不能为空") Long userId) {
        studentPaperRecordService.checkSign(userId);
        return R.ok();
    }

    @Log(title = "用户答题并生产评测报告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/submitAndFinish")
    @Transactional(rollbackFor = Exception.class)
    public R<StudentPaperRecordVo> addAndSubmit(@Validated @RequestBody StudentPaperRecordAndSubmitBo bo) {
        StudentPaperRecordBo studentPaperRecord = bo.getStudentPaperRecord();
        StudentPaperRecordVo studentPaperRecordVo = studentPaperRecordService.insertByBo(studentPaperRecord);
        List<StudentAnswerRecordBo> studentAnswerList = bo.getStudentAnswerList();
        for (StudentAnswerRecordBo answerRecordBo : studentAnswerList) {
            answerRecordBo.setStudentPaperRecordId(studentPaperRecordVo.getStudentPaperRecordId());
        }
        answerRecordService.batchInsertByBo(studentAnswerList);
        //强退不更新
        if (studentPaperRecordVo.getRecordStatus().equals(1)) {
            studentPaperRecordService.finishAndCalculate(studentPaperRecordVo.getStudentPaperRecordId());
        }
        return R.ok(studentPaperRecordVo);
    }


    /**
     * 获取用户答题情况
     */
    @GetMapping("/getQuestionsAndKnowledge")
    public R<Object> getQuestionsAndKnowledge(@NotNull(message = "主键不能为空") Long studentPaperRecordId) {
        return R.ok(studentPaperRecordService.getQuestionsAndKnowledge(studentPaperRecordId));
    }


}
