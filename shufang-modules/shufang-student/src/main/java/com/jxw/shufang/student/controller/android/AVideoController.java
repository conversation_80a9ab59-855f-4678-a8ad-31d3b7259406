package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.RemoteVideoLabelService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoLabelVo;
import com.jxw.shufang.student.service.IVideoService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/video")
public class AVideoController {
    private final IVideoService videoService;
    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @GetMapping("/label/list")
    public R<List<RemoteVideoLabelVo>> listVideoLabels(@NotNull(message = "视频ID不能为空") Long videoId,
                                                       @RequestParam(required = false, defaultValue = "false") Boolean needKnowledgeNote) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        List<RemoteVideoLabelVo> remoteVideoLabelVos = videoService.listVideoLabels(videoId, needKnowledgeNote);
        return R.ok(remoteVideoLabelVos);
    }

    @GetMapping("/question/list")
    public R<List<RemoteGroupVideoVo>> listVideoQuestions(@NotNull(message = "题目ID不能为空") Long questionId) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        RemoteQuestionVideoBo remoteQuestionVideoBo = new RemoteQuestionVideoBo();
        remoteQuestionVideoBo.setQuestionId(questionId);
        List<RemoteGroupVideoVo> questionVideoVos = remoteQuestionService.getQuestionVideoList(remoteQuestionVideoBo);
        return R.ok(questionVideoVos);
    }

}
