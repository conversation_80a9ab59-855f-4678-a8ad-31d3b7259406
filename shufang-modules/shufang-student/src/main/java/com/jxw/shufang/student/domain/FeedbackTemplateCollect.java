package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 反馈模板收藏对象 feedback_template_collect
 *
 *
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback_template_collect")
public class FeedbackTemplateCollect extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈模板收藏id
     */
    @TableId(value = "feedback_template_collect_id")
    private Long feedbackTemplateCollectId;

    /**
     * 反馈模板id
     */
    private Long feedbackTemplateId;


}
