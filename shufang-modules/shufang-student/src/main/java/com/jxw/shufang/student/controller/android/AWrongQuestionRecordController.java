package com.jxw.shufang.student.controller.android;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.WrongQuestionGroupVo;
import com.jxw.shufang.student.service.IWrongQuestionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 错题记录--平板端
 * 前端访问路由地址为:/student/wrongQuestionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/wrongQuestionRecord")
public class AWrongQuestionRecordController extends BaseController {

    private final IWrongQuestionRecordService wrongQuestionRecordService;

    /**
     * 分页按照日期分组查询错题列表（不会查总数），使用滑动到底加载更多的方式
     */
    @GetMapping("/listGroupByDate")
    public TableDataInfo<WrongQuestionGroupVo> listGroupByDate(String affiliationSubject,PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        pageQuery.setSearchCount(false);
        return wrongQuestionRecordService.listGroupByDate(LoginHelper.getStudentId(),affiliationSubject,pageQuery);
    }

    /**
     * 删除错题记录
     *
     * @param wrongQuestionRecordId 主键
     */
    @RepeatSubmit
    @Log(title = "错题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wrongQuestionRecordId}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long wrongQuestionRecordId) {
        return toAjax(wrongQuestionRecordService.deleteWithValidByIds(List.of(wrongQuestionRecordId), true));
    }
}
