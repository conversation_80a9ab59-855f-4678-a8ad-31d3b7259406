package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StuCourseResourceCollectBo;
import com.jxw.shufang.student.domain.vo.StuCourseResourceCollectVo;
import com.jxw.shufang.student.service.IStuCourseResourceCollectService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员对课程资源的收藏
 * 前端访问路由地址为:/student/management/stuCourseResourceCollect
 *
 *
 * @date 2024-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/stuCourseResourceCollect")
public class StuCourseResourceCollectController extends BaseController {

    private final IStuCourseResourceCollectService stuCourseResourceCollectService;

    /**
     * 查询会员对课程资源的收藏列表
     */
    @SaCheckPermission("student:stuCourseResourceCollect:list")
    @GetMapping("/list")
    public TableDataInfo<StuCourseResourceCollectVo> list(StuCourseResourceCollectBo bo, PageQuery pageQuery) {
        return stuCourseResourceCollectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员对课程资源的收藏列表
     */
    @SaCheckPermission("student:stuCourseResourceCollect:export")
    @Log(title = "会员对课程资源的收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StuCourseResourceCollectBo bo, HttpServletResponse response) {
        List<StuCourseResourceCollectVo> list = stuCourseResourceCollectService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员对课程资源的收藏", StuCourseResourceCollectVo.class, response);
    }

    /**
     * 获取会员对课程资源的收藏详细信息
     *
     * @param resourceCollectId 主键
     */
    @SaCheckPermission("student:stuCourseResourceCollect:query")
    @GetMapping("/{resourceCollectId}")
    public R<StuCourseResourceCollectVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long resourceCollectId) {
        return R.ok(stuCourseResourceCollectService.queryById(resourceCollectId));
    }

    /**
     * 新增会员对课程资源的收藏
     */
    @SaCheckPermission("student:stuCourseResourceCollect:add")
    @Log(title = "会员对课程资源的收藏", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StuCourseResourceCollectBo bo) {
        return toAjax(stuCourseResourceCollectService.insertByBo(bo));
    }

    /**
     * 修改会员对课程资源的收藏
     */
    @SaCheckPermission("student:stuCourseResourceCollect:edit")
    @Log(title = "会员对课程资源的收藏", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StuCourseResourceCollectBo bo) {
        return toAjax(stuCourseResourceCollectService.updateByBo(bo));
    }

    /**
     * 删除会员对课程资源的收藏
     *
     * @param resourceCollectIds 主键串
     */
    @SaCheckPermission("student:stuCourseResourceCollect:remove")
    @Log(title = "会员对课程资源的收藏", businessType = BusinessType.DELETE)
    @DeleteMapping("/{resourceCollectIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] resourceCollectIds) {
        return toAjax(stuCourseResourceCollectService.deleteWithValidByIds(List.of(resourceCollectIds), true));
    }
}
