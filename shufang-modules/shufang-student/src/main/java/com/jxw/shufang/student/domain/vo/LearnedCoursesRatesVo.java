package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 数据统计-已学课程分布 learnedCoursesRates
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
public class LearnedCoursesRatesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 已学课程分布列表
     */
    private List<LearnedCoursesRatesEntity> learnedRatesEntities;

    /**
     * 数据统计-已学课程分布-实体 LearnedCoursesRatesEntity
     *
     * <AUTHOR>
     * @date 2024-05-13
     */
    @Data
    public static class LearnedCoursesRatesEntity {

        /**
         * 科目，对应字典
         */
        private String affiliationSubject;

        /**
         * 占比
         */
        private Double rate;

    }
}
