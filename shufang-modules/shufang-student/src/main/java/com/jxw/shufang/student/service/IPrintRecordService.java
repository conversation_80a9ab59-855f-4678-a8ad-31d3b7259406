package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.PrintRecordBo;
import com.jxw.shufang.student.domain.vo.PrintRecordInfoVo;
import com.jxw.shufang.student.domain.vo.PrintRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 打印记录Service接口
 *
 *
 * @date 2024-05-07
 */
public interface IPrintRecordService {

    /**
     * 查询打印记录
     */
    PrintRecordVo queryById(Long printRecordId);

    /**
     * 查询打印记录列表
     */
    TableDataInfo<PrintRecordVo> queryPageList(PrintRecordBo bo, PageQuery pageQuery);

    /**
     * 查询打印记录列表
     */
    List<PrintRecordVo> queryList(PrintRecordBo bo);

    /**
     * 新增打印记录
     */
    Boolean insertByBo(PrintRecordBo bo);

    /**
     * 修改打印记录
     */
    Boolean updateByBo(PrintRecordBo bo);

    /**
     * 校验并批量删除打印记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<PrintRecordInfoVo> queryInfo(Long printRecordId);
}
