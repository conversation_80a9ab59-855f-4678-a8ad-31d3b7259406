package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.AttendanceLogStudentEzkeco;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * ezkeco学员考勤记录视图对象 attendance_log_student_ezkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AttendanceLogStudentEzkeco.class)
public class AttendanceLogStudentEzkecoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long attendanceLogStudentEzkecoId;

    /**
     * 流水号
     */
    private Long logId;

    /**
     * 验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)
     */
    @ExcelProperty(value = "验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)")
    private String verify;

    /**
     * 打卡时间
     */
    @ExcelProperty(value = "打卡时间")
    private Date checktime;

    /**
     * 设备序列号
     */
    @ExcelProperty(value = "设备序列号")
    private String sn;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String alias;

    /**
     * 人员编号
     */
    @ExcelProperty(value = "人员编号")
    private String pin;

    /**
     * 考勤状态说明
     */
    @ExcelProperty(value = "考勤状态说明")
    private String state;

    /**
     * 考勤关联用户_id
     */
    @ExcelProperty(value = "考勤关联用户_id")
    private Long attendanceUserId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 学员id
     */
    @ExcelProperty(value = "学员id")
    private Long studentId;

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名")
    private String nickName;

    /**
     * 照片地址
     */
    @ExcelProperty(value = "照片地址")
    private String photograph;

    /**
     * 是否通知家长（数据字典：sys_yes_no）
     */
    @ExcelProperty(value = "是否通知家长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=据字典：sys_yes_no")
    private String notifyParents;

    /**
     * 是否绑定家长（数据字典：sys_yes_no）
     */
    @ExcelProperty(value = "是否绑定家长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=据字典：sys_yes_no")
    private String bindParents;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    private StudentVo student;


}
