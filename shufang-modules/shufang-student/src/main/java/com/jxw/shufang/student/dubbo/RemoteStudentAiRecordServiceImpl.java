package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.student.api.RemoteStudentAiRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteDownLoadMergeRecordBO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiPaperOssUrlDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiRecordOssUrlDTO;
import com.jxw.shufang.student.domain.dto.AiPaperOssUrlDTO;
import com.jxw.shufang.student.domain.dto.AiRecordOssUrlDTO;
import com.jxw.shufang.student.service.IStudentPaperRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/26 15:12
 * @Version 1
 * @Description 远程服务-学生AI评测记录
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentAiRecordServiceImpl implements RemoteStudentAiRecordService {

    @Resource
    private IStudentPaperRecordService studentPaperRecordService;

    @Override
    public List<RemoteAiRecordOssUrlDTO> getStudentRecordInfo(RemoteDownLoadMergeRecordBO recordBO) throws ServiceException {
        List<AiRecordOssUrlDTO> studentRecordInfos = studentPaperRecordService.getStudentRecordInfo(recordBO.getStudentPaperRecordIds());
        return studentRecordInfos.stream().map(m -> {
            RemoteAiRecordOssUrlDTO remoteAiRecordOssUrlDTO = new RemoteAiRecordOssUrlDTO();
            remoteAiRecordOssUrlDTO.setStudentId(m.getStudentId());
            remoteAiRecordOssUrlDTO.setStudentName(m.getStudentName());
            remoteAiRecordOssUrlDTO.setReportOssUrl(m.getReportOssUrl());
            return remoteAiRecordOssUrlDTO;
        }).toList();
    }

    @Override
    public List<RemoteAiPaperOssUrlDTO> getAiPaperInfo(List<String> studentPaperRecordIds) throws ServiceException {
        List<AiPaperOssUrlDTO> aiPaperInfos = studentPaperRecordService.getAiPaperInfo(studentPaperRecordIds);
        return aiPaperInfos.stream().map(m -> {
            RemoteAiPaperOssUrlDTO remoteAiPaperOssUrlDTO = new RemoteAiPaperOssUrlDTO();
            remoteAiPaperOssUrlDTO.setStudentId(m.getStudentId());
            remoteAiPaperOssUrlDTO.setStudentName(m.getStudentName());
            remoteAiPaperOssUrlDTO.setPaperId(m.getPaperId());
            remoteAiPaperOssUrlDTO.setPaperPdfOssUrl(m.getPaperPdfOssUrl());
            return remoteAiPaperOssUrlDTO;
        }).collect(Collectors.toList());
    }
}
