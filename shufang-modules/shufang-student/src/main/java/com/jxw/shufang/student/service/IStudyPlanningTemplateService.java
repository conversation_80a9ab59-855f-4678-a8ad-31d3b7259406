package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 学习规划模板Service接口
 *
 *
 * @date 2024-06-14
 */
public interface IStudyPlanningTemplateService {

    /**
     * 查询学习规划模板
     */
    StudyPlanningTemplateVo queryById(Long studyPlanningTemplateId,Boolean  withTemplateInfo);

    /**
     * 查询学习规划模板列表
     */
    TableDataInfo<StudyPlanningTemplateVo> queryPageList(StudyPlanningTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询学习规划模板列表
     */
    List<StudyPlanningTemplateVo> queryList(StudyPlanningTemplateBo bo);

    /**
     * 新增学习规划模板
     */
    Boolean insertByBo(StudyPlanningTemplateBo bo);

    /**
     * 修改学习规划模板
     */
    Boolean updateByBo(StudyPlanningTemplateBo bo);

    /**
     * 校验并批量删除学习规划模板信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean changeStatus(StudyPlanningTemplateBo bo);
}
