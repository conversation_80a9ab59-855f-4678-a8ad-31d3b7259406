package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习视频记录（视频观看记录）对象 study_video_record
 *
 * @date 2024-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_video_record")
public class StudyVideoRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习视频记录id
     */
    @TableId(value = "study_video_record_id")
    private Long studyVideoRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 学习规划记录Id
     */
    private Long studyPlanningRecordId;

    /**
     * 对应外部资源的视频Id
     */
    private Long videoId;

    /**
     * 章节ID
     */
    private Long courseId;

    /**
     * 学习模块类型(studyModuleEnum)
     */
    private String studyModuleType;

    /**
     * 播放时长（单位秒 按日累加）
     */
    private Long studyVideoDuration;

    /**
     * 原音/视频时长（秒)  取自cds.cds_oss_file
     */
    private Long duration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    private String studyVideoSlices;

    /**
     * 创建部门(和会员id对应的部门一致)
     */
    private Long createDept;

    /**
     * 创建人(和会员id对应的创建人一致)
     */
    private Long createBy;

    /**
     * 创建时间（日期）
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
