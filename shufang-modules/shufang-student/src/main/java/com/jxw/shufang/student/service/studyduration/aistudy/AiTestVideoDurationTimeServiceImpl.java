package com.jxw.shufang.student.service.studyduration.aistudy;

import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.AiStudyDurationProcessingContextDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyRecordDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyVideoRecordDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.studyduration.AbstractAiStudyRecordTime;
import com.jxw.shufang.student.service.studyduration.StudyRecordTimeContentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/8 16:52
 * @Version 1
 * @Description AI评测【错题视频】 时长统计
 */
@Service
public class AiTestVideoDurationTimeServiceImpl extends AbstractAiStudyRecordTime<AiStudyVideoRecordBo> {
    @Resource
    private StudyRecordTimeContentService contextService;

    @Override
    public List<AiStudyVideoRecordBo> filterData(List<AiStudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return records;
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return List.of(StudyModelGroupEnum.AI_STUDY);
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return List.of(StudyModuleTypeEnum.AI_TEST_ERROR_VIDEO);
    }

    @Override
    public SaveOrUpdateAiStudyRecordDTO buildRecordProcessDTO(AiStudyVideoRecordBo recordBo) {
        SaveOrUpdateAiStudyRecordDTO saveOrUpdate = new SaveOrUpdateAiStudyRecordDTO();
        saveOrUpdate.setStudentId(recordBo.getStudentId());
        saveOrUpdate.setVideoId(recordBo.getVideoId());
        saveOrUpdate.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        saveOrUpdate.setCommitTime(recordBo.getCommitTime());
        saveOrUpdate.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        saveOrUpdate.setTestPaperId(recordBo.getTestPaperId());
        saveOrUpdate.setCourseId(recordBo.getCourseId());
        return saveOrUpdate;
    }

    @Override
    public SaveOrUpdateAiStudyVideoRecordDTO buildVideoRecordProcessDTO(AiStudyVideoRecordBo recordBo) {
        SaveOrUpdateAiStudyVideoRecordDTO savedRecordDTO = new SaveOrUpdateAiStudyVideoRecordDTO();
        savedRecordDTO.setStudentId(recordBo.getStudentId());
        savedRecordDTO.setVideoId(recordBo.getVideoId());
        savedRecordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        savedRecordDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        savedRecordDTO.setCommitTime(recordBo.getCommitTime());
        savedRecordDTO.setCourseId(recordBo.getCourseId());
        return savedRecordDTO;
    }

    @Override
    public AiStudyDurationProcessingContextDTO contextData(List<AiStudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<Long> studentIds = records.stream().map(AiStudyVideoRecordBo::getStudentId).toList();
        List<Long> testPaperId = records.stream().map(AiStudyVideoRecordBo::getTestPaperId).collect(Collectors.toList());
        List<Long> videoIds = records.stream().map(AiStudyVideoRecordBo::getVideoId).filter(Objects::nonNull).toList();
        AiStudyDurationProcessingContextDTO contextDataDTO = new AiStudyDurationProcessingContextDTO();
        contextDataDTO.setExistAiVideoRecord(contextService.getAiVideoRecord(studentIds, moduleAndGroupEnum.getModuleEnum(), videoIds));
        contextDataDTO.setExistAiStudyRecord(contextService.getAiStudyRecord(studentIds, testPaperId));
        contextDataDTO.setStudentMap(contextService.studentMap(studentIds));
        contextDataDTO.setStudyModuleTypeEnum(moduleAndGroupEnum);
        contextDataDTO.setCourseDurationMap(contextService.courseDurationMap(videoIds));
        return contextDataDTO;
    }
}
