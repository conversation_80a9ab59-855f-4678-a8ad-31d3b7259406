package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiTestRecordBo;
import com.jxw.shufang.student.domain.vo.AiTestRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * ai测验记录Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiTestRecordService {

    /**
     * 查询ai测验记录
     */
    AiTestRecordVo queryById(Long aiTestRecordId);

    /**
     * 查询ai测验记录列表
     */
    TableDataInfo<AiTestRecordVo> queryPageList(AiTestRecordBo bo, PageQuery pageQuery);

    /**
     * 查询ai测验记录列表
     */
    List<AiTestRecordVo> queryList(AiTestRecordBo bo);

    /**
     * 新增ai测验记录
     */
    Boolean insertByBo(AiTestRecordBo bo);

    /**
     * 修改ai测验记录
     */
    Boolean updateByBo(AiTestRecordBo bo);

    /**
     * 校验并批量删除ai测验记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<AiTestRecordBo> convert);
}
