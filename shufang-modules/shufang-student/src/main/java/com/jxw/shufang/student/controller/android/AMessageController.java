package com.jxw.shufang.student.controller.android;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.MessageBo;
import com.jxw.shufang.student.domain.vo.MessageGroupVo;
import com.jxw.shufang.student.service.IMessageService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息控制器--平板端
 * 前端访问路由地址为:/student/android/message
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/message")
public class AMessageController extends BaseController {

    private final IMessageService messageService;

    private final IStudentConsultantRecordService studentConsultantRecordService;
    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 查询未读消息数量
     */
    @GetMapping("/unreadCount")
    public R<Long> unreadCount() {
        Long studentId = LoginHelper.getStudentId();
        //因为是会员查询，所以只查员工（以及门店管理员）发的即可
        return R.ok(messageService.unreadCount(studentId, UserConstants.MESSAGE_SENDER_TYPE_STAFF));
    }

    /**
     * 分页倒序查询消息列表
     */

    @GetMapping("/descMessageList")
    public TableDataInfo<MessageGroupVo> descMessageList(Long ltMessageId,PageQuery pageQuery) {
        MessageBo messageBo = new MessageBo();
        messageBo.setLtMessageId(ltMessageId);
        messageBo.setMessageStudentId(LoginHelper.getStudentId());
        //如果按照默认的雪花算法，新数据永远比旧数据的ID大
        pageQuery.setOrderByColumn("t.message_id");
        pageQuery.setIsAsc("desc");
        pageQuery.setSearchCount(Boolean.FALSE);
        TableDataInfo<MessageGroupVo> tableDataInfo = messageService.messageGroupPage(messageBo, pageQuery);
        List<MessageGroupVo> rows = tableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是平板端的消息，也就是会员接受的，我们只需要把员工（门店管理员）的消息标记为已读即可
            messageService.readMessagesOfStudent(LoginHelper.getStudentId(), UserConstants.MESSAGE_SENDER_TYPE_STAFF);
        }
        return tableDataInfo;
    }

    /**
     * 获取新消息列表，不分页，一次性获取所有新消息
     * @param gtMessageId 大于，最新的消息ID
     */
    @GetMapping("/newMessageList")
    public R<List<MessageGroupVo>> newMessageList(Long gtMessageId) {
        MessageBo messageBo = new MessageBo();
        messageBo.setGtMessageId(gtMessageId);
        messageBo.setMessageStudentId(LoginHelper.getStudentId());
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        pageQuery.setOrderByColumn("t.message_id");
        pageQuery.setIsAsc("desc");
        pageQuery.setSearchCount(Boolean.FALSE);
        TableDataInfo<MessageGroupVo> tableDataInfo = messageService.messageGroupPage(messageBo, pageQuery);
        List<MessageGroupVo> rows = tableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是平板端的消息，也就是会员接受的，我们只需要把员工（门店管理员）的消息标记为已读即可
            List<Long> messageIdList = rows.stream().map(MessageGroupVo::getMessageId).toList();
            messageService.readMessages(messageIdList, UserConstants.MESSAGE_SENDER_TYPE_STAFF);
        }
        return R.ok(rows);
    }

    /**
     * 发送消息
     */
    @PostMapping("/sendMessage")
    @RepeatSubmit(interval = 1000, message = "请勿发送消息过快")
    @Log(title = "平板端-发送顾问消息", businessType = BusinessType.INSERT)
    public R<Boolean> sendMessage(@RequestBody @Validated(AddGroup.class) MessageGroupVo messageGroupVo) {
        Long studentId = LoginHelper.getStudentId();
        //发给自己的会员顾问
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(List.of(studentId));
        if (CollUtil.isEmpty(studentConsultantIdMap) || studentConsultantIdMap.get(studentId) == null) {
            return R.fail("当前无会员顾问与您绑定，请联系管理员");
        }
        Long consultantId = studentConsultantIdMap.get(studentId);
        MessageBo messageBo = new MessageBo();
        messageBo.setMessageStudentId(studentId);
        messageBo.setMessageStaffId(consultantId);

        if (UserConstants.MESSAGE_TYPE_TEXT.equals(messageGroupVo.getContentType()) && StringUtils.isBlank(messageGroupVo.getContent())) {
            return R.fail("文本消息内容不能为空");
        }
        if (UserConstants.MESSAGE_TYPE_IMAGE.equals(messageGroupVo.getContentType()) && StringUtils.isBlank(messageGroupVo.getContent())) {
            return R.fail("图片消息资源不能为空");
        }
        messageBo.setContentType(messageGroupVo.getContentType());
        messageBo.setSendUserType(UserConstants.MESSAGE_SENDER_TYPE_STUDENT);
        if (UserConstants.MESSAGE_TYPE_TEXT.equals(messageGroupVo.getContentType())) {
            messageBo.setMessageConcat(messageGroupVo.getContent());
        } else if (UserConstants.MESSAGE_TYPE_IMAGE.equals(messageGroupVo.getContentType())) {
            messageBo.setMessageResources(Convert.toLong(messageGroupVo.getContent()));
        }
        String s = messageService.insertByBo(messageBo);
        if (StringUtils.isNotBlank(s)) {
            return R.fail(s);
        }

        String msg = StringUtils.isBlank(messageBo.getMessageConcat()) ? "图片" : messageBo.getMessageConcat();
        String content = msg.length() > 1024 ? msg.substring(0, 1024) : msg;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(NotifyMessageConstant.CONTENT, content);
        RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder()
            .templateCode(NoticeBizTypeEnum.LIUYAN.name())
            .bizType(NoticeBizTypeEnum.LIUYAN.getCode())
            .noticeType(NoticeTypeEnum.INTERNAL.getCode())
            .content(content)
            .studentId(studentId)
            .paramMap(paramMap)
            .build();
        rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);

        return R.ok(Boolean.TRUE);

    }

}
