package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.vo.CourseChildInfoVo;
import com.jxw.shufang.student.domain.vo.CourseVo;

import java.util.List;

/**
 * 课程（课程包含章节）Mapper接口
 *
 *
 * @date 2024-03-30
 */
public interface CourseMapper extends BaseMapperPlus<Course, CourseVo> {

    /**
     * 查询课程子节点id，这里不做数据权限的控制，没必要，查出来的childIds中不包含自己
     *
     * @param queryWrapper
     */
    List<CourseChildInfoVo> getChindInfoList(@Param(Constants.WRAPPER) QueryWrapper<Course> queryWrapper);

    List<Course> getQueryList(@Param(Constants.WRAPPER) QueryWrapper<Course> queryWrapper);

    Integer selectCourseMaxSort(@Param("courseId") Long courseId);

    List<Course> listGradeCourse(@Param(Constants.WRAPPER) QueryWrapper<Course> queryWrapper);

    List<CourseVo> findCourseAncestors(@Param("courseId") Long courseId);

    List<CourseVo> findCourseChildren(@Param("courseIds") List<Long> courseIds);


    List<CourseVo> getCourseInfoWithParentNameByCourseId(@Param("courseIds") List<Long> courseIds);


    List<CourseVo>  queryGradeCourse(@Param(Constants.WRAPPER) QueryWrapper<Course> queryWrapper);
    Integer batchClearCourse(@Param("courseIds") List<Long> courseIds);
}
