package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 练习记录对象 practice_record
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("practice_record")
public class PracticeRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划记录ID
     */
    private Long studyPlanningRecordId;

    /**
     * 练习记录id
     */
    @TableId(value = "practice_record_id")
    private Long practiceRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 课程资源id
     */
    private Long courseResourceId;

    /**
     * 资源内容（oss_id）
     */
    private Long resourceContent;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对 全错 半错）
     */
    private String answerResult;

    /**
     * 作答图片（oss_id）
     */
    private Long answerImg;


}
