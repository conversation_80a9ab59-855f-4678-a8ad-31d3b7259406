package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PracticeRecordBo;
import com.jxw.shufang.student.domain.vo.PracticeRecordVo;
import com.jxw.shufang.student.service.IPracticeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 练习记录
 * 前端访问路由地址为:/student/practiceRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/practiceRecord")
public class PracticeRecordController extends BaseController {

    private final IPracticeRecordService practiceRecordService;

    /**
     * 查询练习记录列表
     */
    @SaCheckPermission("student:practiceRecord:list")
    @GetMapping("/list")
    public TableDataInfo<PracticeRecordVo> list(PracticeRecordBo bo, PageQuery pageQuery) {
        return practiceRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出练习记录列表
     */
    @SaCheckPermission("student:practiceRecord:export")
    @Log(title = "练习记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PracticeRecordBo bo, HttpServletResponse response) {
        List<PracticeRecordVo> list = practiceRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "练习记录", PracticeRecordVo.class, response);
    }

    /**
     * 获取练习记录详细信息
     *
     * @param practiceRecordId 主键
     */
    @SaCheckPermission("student:practiceRecord:query")
    @GetMapping("/{practiceRecordId}")
    public R<PracticeRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long practiceRecordId) {
        return R.ok(practiceRecordService.queryById(practiceRecordId));
    }

    /**
     * 新增练习记录
     */
    @SaCheckPermission("student:practiceRecord:add")
    @Log(title = "练习记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PracticeRecordBo bo) {
        return toAjax(practiceRecordService.insertByBo(bo));
    }

    /**
     * 修改练习记录
     */
    @SaCheckPermission("student:practiceRecord:edit")
    @Log(title = "练习记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PracticeRecordBo bo) {
        return toAjax(practiceRecordService.updateByBo(bo));
    }

    /**
     * 删除练习记录
     *
     * @param practiceRecordIds 主键串
     */
    @SaCheckPermission("student:practiceRecord:remove")
    @Log(title = "练习记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{practiceRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] practiceRecordIds) {
        return toAjax(practiceRecordService.deleteWithValidByIds(List.of(practiceRecordIds), true));
    }
}
