package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.QuestionVideoRecord;

import java.io.Serial;
import java.io.Serializable;


/**
 * 题目视频记录（题目视频观看记录）视图对象 question_video_record
 * @date 2024-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = QuestionVideoRecord.class)
public class QuestionVideoRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目视频记录id
     */
    @ExcelProperty(value = "题目视频记录id")
    private Long questionVideoRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 学习规划记录Id
     */
    @ExcelProperty(value = "学习规划记录Id")
    private Long studyPlanningRecordId;

    /**
     * 对应外部资源的视频Id
     */
    @ExcelProperty(value = "对应外部资源的视频Id")
    private Long videoId;

    /**
     * 题目类型：1测试 2练习
     */
    @ExcelProperty(value = "题目类型：1测试 2练习")
    private String questionType;

    /**
     * 课程Id
     */
    @ExcelProperty(value = "课程Id")
    private Long courseId;

    /**
     * 题目Id
     */
    @ExcelProperty(value = "题目Id")
    private Long questionId;

    /**
     * 播放时长（单位秒 按日累加）
     */
    @ExcelProperty(value = "播放时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位秒,按=日累加")
    private Long studyVideoDuration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    @ExcelProperty(value = "分片值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,0=0:18-00:41(23’’),00:42-00:45(3’’),，=非需要展示情况下，尽可能不查此字段")
    private String studyVideoSlices;


}
