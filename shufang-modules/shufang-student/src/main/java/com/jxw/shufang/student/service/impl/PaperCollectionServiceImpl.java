package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.PaperCollection;
import com.jxw.shufang.student.domain.bo.PaperCollectionBo;
import com.jxw.shufang.student.domain.vo.PaperCollectionVo;
import com.jxw.shufang.student.domain.vo.PaperVo;
import com.jxw.shufang.student.mapper.PaperCollectionMapper;
import com.jxw.shufang.student.service.IPaperCollectionService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 试卷收藏Service业务层处理
 *
 *
 * @date 2024-05-14
 */
@RequiredArgsConstructor
@Service
public class PaperCollectionServiceImpl implements IPaperCollectionService, BaseService {

    private final PaperCollectionMapper baseMapper;

    /**
     * 查询试卷收藏
     */
    @Override
    public PaperCollectionVo queryById(Long collectionId){
        return baseMapper.selectVoById(collectionId);
    }

    /**
     * 查询试卷收藏列表
     */
    @Override
    public TableDataInfo<PaperCollectionVo> queryPageList(PaperCollectionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PaperCollection> lqw = buildQueryWrapper(bo);
        Page<PaperCollectionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询试卷收藏列表
     */
    @Override
    public List<PaperCollectionVo> queryList(PaperCollectionBo bo) {
        LambdaQueryWrapper<PaperCollection> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PaperCollection> buildQueryWrapper(PaperCollectionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PaperCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPaperId() != null, PaperCollection::getPaperId, bo.getPaperId());
        lqw.eq(bo.getStudentId() != null, PaperCollection::getStudentId, bo.getStudentId());
        lqw.in(CollUtil.isNotEmpty(bo.getPaperIdList()), PaperCollection::getPaperId, bo.getPaperIdList());
        return lqw;
    }

    /**
     * 新增试卷收藏
     */
    @Override
    public Boolean insertByBo(PaperCollectionBo bo) {
        PaperCollection add = MapstructUtils.convert(bo, PaperCollection.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCollectionId(add.getCollectionId());
        }
        return flag;
    }

    /**
     * 修改试卷收藏
     */
    @Override
    public Boolean updateByBo(PaperCollectionBo bo) {
        PaperCollection update = MapstructUtils.convert(bo, PaperCollection.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PaperCollection entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除试卷收藏
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void putPaperCollection(List<PaperVo> paperList,Long studentId) {
        if (paperList == null || paperList.isEmpty()){
            return;
        }
        List<Long> paperIdList = paperList.stream().map(PaperVo::getPaperId).toList();
        PaperCollectionBo paperCollectionBo = new PaperCollectionBo();
        paperCollectionBo.setStudentId(studentId);
        paperCollectionBo.setPaperIdList(paperIdList);
        List<PaperCollectionVo> paperCollectionVos = queryList(paperCollectionBo);
        Map<Long, PaperCollectionVo> map = StreamUtils.toMap(paperCollectionVos, PaperCollectionVo::getPaperId, Function.identity());
        for (PaperVo paperVo : paperList) {
            PaperCollectionVo paperCollectionVo = map.get(paperVo.getPaperId());
            paperVo.setIsCollect(paperCollectionVo!= null);
        }
    }

    @Override
    public List<Long> queryCollectionPaperIdList(Long studentId) {
        LambdaQueryWrapper<PaperCollection> lqw = Wrappers.lambdaQuery();
        lqw.eq(PaperCollection::getStudentId, studentId);
        lqw.select(PaperCollection::getPaperId);
        List<PaperCollection> paperCollections = baseMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(paperCollections)){
            return paperCollections.stream().map(PaperCollection::getPaperId).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public void collectPaper(Long paperId,Long studentId) {
        PaperCollectionBo paperCollectionBo = new PaperCollectionBo();
        paperCollectionBo.setPaperId(paperId);
        paperCollectionBo.setStudentId(studentId);
        PaperCollectionVo paperCollectionVo = baseMapper.selectVoOne(buildQueryWrapper(paperCollectionBo));
        if (paperCollectionVo == null){
            PaperCollection paperCollection = new PaperCollection();
            paperCollection.setPaperId(paperId);
            paperCollection.setStudentId(studentId);
            baseMapper.insert(paperCollection);
        }else {
            //删除
            baseMapper.deleteById(paperCollectionVo.getCollectionId());
        }

    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
