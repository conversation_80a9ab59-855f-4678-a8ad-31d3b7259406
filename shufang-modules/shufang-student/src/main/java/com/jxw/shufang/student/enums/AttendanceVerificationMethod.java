package com.jxw.shufang.student.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 附录：考勤记录验证方式表
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Getter
@RequiredArgsConstructor
public enum AttendanceVerificationMethod {

    // 使用枚举常量名和注释来描述验证方式
    FINGERPRINT_OR_PASSWORD_OR_CARD(0, "指纹或密码或卡"),
    FINGERPRINT(1, "指纹"),
    ATTENDANCE_NUMBER(2, "考勤号"),
    PASSWORD(3, "密码"),
    CARD(4, "卡"),
    FINGERPRINT_OR_PASSWORD(5, "指纹或密码"),
    FINGERPRINT_OR_CARD(6, "指纹或卡"),
    PASSWORD_OR_CARD(7, "密码或卡"),
    ATTENDANCE_NUMBER_AND_FINGERPRINT(8, "考勤号和指纹"),
    FINGERPRINT_AND_PASSWORD(9, "指纹和密码"),
    // ... 省略其他枚举常量，按照你的列表继续添加 ...
    FACE(15, "面部"),
    FACE_AND_FINGERPRINT(16, "面部加指纹"),
    FACE_AND_PASSWORD(17, "面部加密码"),
    FACE_AND_CARD(18, "面部加卡"),
    FACE_AND_FINGERPRINT_AND_CARD(19, "面部加指纹加卡"),
    FACE_AND_FINGERPRINT_AND_PASSWORD(20, "面部加指纹加密码");

    // 枚举的私有字段
    private final int code;
    private final String description;


    // 通过code搜索名称的方法
    public static String getDescriptionByCode(int code) {
        for (AttendanceVerificationMethod method : AttendanceVerificationMethod.values()) {
            if (method.getCode() == code) {
                return method.getDescription();
            }
        }
        // 如果没有找到对应的code，返回null或默认值
        return null;
    }
}
