package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import com.jxw.shufang.student.domain.bo.StudentIntroduceRecordBo;
import com.jxw.shufang.student.domain.vo.StudentIntroduceRecordVo;

import java.util.List;
import java.util.Map;

public interface IStudentIntroduceRecordService {
    /**
     * 查询会员介绍记录列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<StudentIntroduceRecordVo> queryPage(StudentIntroduceRecordBo bo, PageQuery pageQuery);

    /**
     * 保存会员介绍记录
     *
     * @param studentIntroduceRecordBo
     * @return
     */
    boolean insertByBo(StudentIntroduceRecordBo studentIntroduceRecordBo);

    /**
     * 查询符合条件的所有学生转介绍记录
     *
     * @param bo
     * @return
     */
    List<StudentIntroduceRecordVo> listAllRecord(StudentIntroduceRecordBo bo);

    /**
     * 获取会员的被转介绍记录
     *
     * @param studentId 被转介绍（新会员）id
     * @return
     */
    StudentIntroduceRecordVo getByStudentId(Long studentId);

    /**
     * 处理会员首次购买会员，发放优惠额度
     *
     * @param studentId
     * @param orderId
     * @param productBoList
     * @param operator
     * @param operateDept
     * @return
     */
    Boolean remoteDealWithOrderSuccess(Long studentId, Long orderId, List<RemoteProductBo> productBoList,Long operator, Long operateDept);

    /**
     * 处理介绍会员退款
     *
     * @param studentId
     * @param orderId
     * @return
     */
    Boolean remoteDealWithOrderRefund(Long studentId, Long orderId);

    /**
     * 获取转介绍记录map
     *
     * @param studentIds
     * @return
     */
    Map<Long, StudentIntroduceRecord> getIntroduceMapByStudentIdList(List<Long> studentIds);

    /**
     * 更新转介绍记录
     *
     * @param updateBo
     * @return
     */
    boolean updateByBo(StudentIntroduceRecordBo updateBo);

    boolean deleteById(Long introduceRecordId);
}
