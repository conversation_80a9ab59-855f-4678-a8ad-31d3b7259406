package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.common.translation.annotation.Translation;
import com.jxw.shufang.common.translation.constant.TransConstant;
import com.jxw.shufang.student.domain.Paper;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 试卷视图对象 paper
 *
 *
 * @date 2024-05-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Paper.class)
public class PaperVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 试卷Id
     */
    @ExcelProperty(value = "试卷Id")
    private Long paperId;

    /**
     * 学段，对应字典 paper_stage
     */
    @ExcelProperty(value = "学段，对应字典")
    private String paperStage;

    /**
     * 分类，对应字典 paper_type
     */
    @ExcelProperty(value = "分类，对应字典")
    private String paperType;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private String paperGrade;

    /**
     * 归属学科(科目)（对应字典值） paper_affiliation_subject
     */
    @ExcelProperty(value = "归属学科(科目)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String paperAffiliationSubject;

    private String paperAffiliationSubjectName;

    /**
     * 地区，省市县之间用空格分割
     */
    @ExcelProperty(value = "地区，省市县之间用空格分割")
    private String paperRegion;

    /**
     * 试卷标题
     */
    @ExcelProperty(value = "试卷标题")
    private String paperTitle;

    /**
     * 原卷ossId
     */
    @ExcelProperty(value = "原卷ossId")
    private Long original;

    /**
     * 原卷ossUrl
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "original")
    private String originalUrl;
    @Translation(type = TransConstant.OSS_ID_TO_NAME,mapper = "original")
    private String originalName;

    /**
     * 解析ossId
     */
    @ExcelProperty(value = "解析ossId")
    private Long analysis;

    /**
     * 解析ossUrl
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "analysis")
    private String analysisUrl;


    /**
     * 原卷带解析ossId
     */
    @ExcelProperty(value = "原卷带解析ossId")
    private Long originalWithAnalysis;

    /**
     * 原卷带解析ossUrl
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "originalWithAnalysis")
    private String originalWithAnalysisUrl;

    /**
     * 原卷缩略图ossId
     */
    private Long originalThumbnail;


    /**
     * 原卷缩略图ossUrl
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "originalThumbnail")
    private String originalThumbnailUrl;

    /**
     * 试卷来源 1管理端 2店铺端
     */
    @ExcelProperty(value = "试卷来源 1管理端 2店铺端")
    private String paperSource;

    /**
     * 门店Id
     */
    @ExcelProperty(value = "门店Id")
    private Long branchId;

    /**
     * 创建时间(上传时间）
     */
    private Date createTime;

    /**
     * 是否收藏 true 已收藏 false 未收藏
     */
    private Boolean isCollect;

    private RemoteBranchVo branch;

}
