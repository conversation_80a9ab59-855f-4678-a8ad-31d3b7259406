package com.jxw.shufang.student.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/19 9:52
 * @Version 1
 * @Description
 */
@Data
@AllArgsConstructor
public class MergePdfDTO {

    /**
     * 文件下载路径
     */
    private List<String> filePath;

    /**
     * 水印
     */
    private String watermark;

    /**
     * 首行添加的文字
     */
    private String firstLineText;

    private MergePdfDTO() {
    }

    public static MergePdfDTO of(List<String> filePath, String watermark, String firstLineText) {
        return new MergePdfDTO(filePath, watermark, firstLineText);
    }
}
