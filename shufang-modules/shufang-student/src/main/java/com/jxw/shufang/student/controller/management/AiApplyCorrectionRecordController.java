package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;
import com.jxw.shufang.student.service.IAiApplyCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * ai申请批改记录
 * 前端访问路由地址为:/student/aiApplyCorrectionRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiApplyCorrectionRecord")
public class AiApplyCorrectionRecordController extends BaseController {

    private final IAiApplyCorrectionRecordService aiApplyCorrectionRecordService;


    /**
     * 查询ai申请批改记录列表
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiApplyCorrectionRecordVo> list(AiApplyCorrectionRecordBo bo, PageQuery pageQuery) {
        //bo.setApplyType("2");//默认查测验的
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        return aiApplyCorrectionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai申请批改记录列表
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:export")
    @Log(title = "ai申请批改记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiApplyCorrectionRecordBo bo, HttpServletResponse response) {
        List<AiApplyCorrectionRecordVo> list = aiApplyCorrectionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai申请批改记录", AiApplyCorrectionRecordVo.class, response);
    }

    /**
     * 获取ai申请批改记录详细信息
     *
     * @param aiApplyCorrectionRecordId 主键
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:query")
    @GetMapping("/{aiApplyCorrectionRecordId}")
    public R<AiApplyCorrectionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiApplyCorrectionRecordId) {
        return R.ok(aiApplyCorrectionRecordService.queryById(aiApplyCorrectionRecordId));
    }

    /**
     * 新增ai申请批改记录
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:add")
    @Log(title = "ai申请批改记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiApplyCorrectionRecordBo bo) {
        return toAjax(aiApplyCorrectionRecordService.insertByBo(bo));
    }

    /**
     * 修改ai申请批改记录
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:edit")
    @Log(title = "ai申请批改记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiApplyCorrectionRecordBo bo) {
        return toAjax(aiApplyCorrectionRecordService.updateByBo(bo));
    }

    /**
     * 删除ai申请批改记录
     *
     * @param aiApplyCorrectionRecordIds 主键串
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:remove")
    @Log(title = "ai申请批改记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiApplyCorrectionRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiApplyCorrectionRecordIds) {
        return toAjax(aiApplyCorrectionRecordService.deleteWithValidByIds(List.of(aiApplyCorrectionRecordIds), true));
    }

    /**
     * 查询申请批改待审核的数量
     */
    @GetMapping("/countWaitAudit")
    public R<Long> countWaitAudit(AiApplyCorrectionRecordBo bo) {
        //bo.setApplyType("2");//默认查测验的
        bo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        return R.ok(aiApplyCorrectionRecordService.count(bo));
    }

    /**
     * 修改申请批改状态
     * @param aiApplyCorrectionRecordId 申请批改记录id
     * @param applyResult 申请结果（1允许 2拒绝 0待审核）
     */
    @SaCheckPermission("student:aiApplyCorrectionRecord:edit")
    @Log(title = "修改Ai申请批改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@NotNull(message = "申请Ai批改记录id不能为空")Long aiApplyCorrectionRecordId,
                                @NotBlank(message = "申请结果不能为空")String applyResult) {
        if (!applyResult.equals("1") && !applyResult.equals("2")) {
            return R.fail("申请结果只能为同意或拒绝");
        }
        return toAjax(aiApplyCorrectionRecordService.updateApplyResult(aiApplyCorrectionRecordId, applyResult));
    }



}
