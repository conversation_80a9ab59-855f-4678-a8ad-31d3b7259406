package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.student.domain.AllowOwnCorrection;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;
import com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.mapper.AllowOwnCorrectionMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 允许自主批改Service业务层处理
 *
 *
 * @date 2024-05-07
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class AllowOwnCorrectionServiceImpl implements IAllowOwnCorrectionService, BaseService {

    private final AllowOwnCorrectionMapper baseMapper;
    private final IStudentConsultantRecordService studentConsultantRecordService;
    private final IStudentService studentService;

    private final IApplyCorrectionRecordService applyCorrectionRecordService;

    private final IAiApplyCorrectionRecordService aiApplyCorrectionRecordService;

    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteStaffService remoteStaffService;

    /**
     * 查询允许自主批改
     */
    @Override
    public AllowOwnCorrectionVo queryById(Long allowOwnCorrectionId) {
        return baseMapper.selectVoById(allowOwnCorrectionId);
    }

    /**
     * 查询允许自主批改列表
     */
    @Override
    public TableDataInfo<AllowOwnCorrectionVo> queryPageList(AllowOwnCorrectionBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<AllowOwnCorrection> lqw = buildQueryWrapper(bo);
        Page<AllowOwnCorrectionVo> result = baseMapper.selectAllowOwnCorrectionVoPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithStudentUserInfo())) {
            putSysUserInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithCreateUserInfo())) {
            putCreateUserInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    public void putSysUserInfo(List<AllowOwnCorrectionVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(AllowOwnCorrectionVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(true);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    public void putCreateUserInfo(List<AllowOwnCorrectionVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> sysUserIdList = records.stream().map(AllowOwnCorrectionVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(true);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        records.forEach(allowOwnCorrectionVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(allowOwnCorrectionVo.getCreateBy());
            allowOwnCorrectionVo.setCreateByUser(remoteUserVo);
        });
    }


    /**
     * 查询允许自主批改列表
     */
    @Override
    public List<AllowOwnCorrectionVo> queryList(AllowOwnCorrectionBo bo) {
        handleQueryParam(bo);
        LambdaQueryWrapper<AllowOwnCorrection> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AllowOwnCorrection> buildLambdaQueryWrapper(AllowOwnCorrectionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AllowOwnCorrection> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AllowOwnCorrection::getStudentId, bo.getStudentId());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), AllowOwnCorrection::getStudentId, bo.getStudentIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AllowOwnCorrection::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getAllowType()), AllowOwnCorrection::getAllowType, bo.getAllowType());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        return lqw;
    }

    private QueryWrapper<AllowOwnCorrection> buildQueryWrapper(AllowOwnCorrectionBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<AllowOwnCorrection> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), "t.type", bo.getType());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.eq(StringUtils.isNotBlank(bo.getAllowType()), "t.allow_type", bo.getAllowType());
        return lqw;
    }

    /**
     * 新增允许自主批改
     */
    @Override
    public boolean insertByBo(AllowOwnCorrectionBo bo) {
        AllowOwnCorrection add = MapstructUtils.convert(bo, AllowOwnCorrection.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAllowOwnCorrectionId(add.getAllowOwnCorrectionId());
        }
        return flag;
    }

    /**
     * 修改允许自主批改
     */
    @Override
    public Boolean updateByBo(AllowOwnCorrectionBo bo) {
        AllowOwnCorrection update = MapstructUtils.convert(bo, AllowOwnCorrection.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AllowOwnCorrection entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除允许自主批改
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public AllowOwnCorrectionVo queryByStudentId(Long studentId, String type, String allowType) {
        LambdaQueryWrapper<AllowOwnCorrection> lqw = Wrappers.lambdaQuery();
        lqw.eq(AllowOwnCorrection::getStudentId, studentId);
        lqw.eq(AllowOwnCorrection::getType, type);
        lqw.eq(AllowOwnCorrection::getAllowType, allowType);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changeAllowOwnCorrection(Long studentId, String type, String allowType) {
        AllowOwnCorrectionBo allowOwnCorrection = new AllowOwnCorrectionBo();
        allowOwnCorrection.setStudentId(studentId);
        allowOwnCorrection.setType(type);
        allowOwnCorrection.setAllowType(allowType);
        List<AllowOwnCorrectionVo> allowOwnCorrectionVos = queryList(allowOwnCorrection);
        IAllowOwnCorrectionService self = SpringUtils.getBean(IAllowOwnCorrectionService.class);
        if (CollUtil.isEmpty(allowOwnCorrectionVos)) {
            Boolean b = self.insertByBo(allowOwnCorrection);
            if (!b) {
                return false;
            }
            boolean b1 = true;
            if ("1".equals(type)) {
                b1 = self.updateApplyCorrectionRecord(allowType, studentId);
            } else {
                b1 =self.updateAiApplyCorrectionRecord(allowType, studentId);
            }
            if (!b1){
                throw new RuntimeException("更新历史申请记录失败");
            }
        } else {
            List<Long> list = allowOwnCorrectionVos.stream().map(AllowOwnCorrectionVo::getAllowOwnCorrectionId).toList();
            return self.deleteWithValidByIds(list, true);
        }
        return true;
    }

    /**
     * 更新申请批改记录
     */
    @Override
    public boolean updateApplyCorrectionRecord(String allowType, Long studentId) {
        //查询是否有未审核的记录
        ApplyCorrectionRecordBo bo = new ApplyCorrectionRecordBo();
        bo.setStudentId(studentId);
        bo.setApplyType(allowType);
        bo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        List<ApplyCorrectionRecordVo> applyCorrectionRecordVos = applyCorrectionRecordService.queryList(bo);
        if (CollUtil.isEmpty(applyCorrectionRecordVos)) {
            return true;
        }

        //更新申请批改记录
        List<ApplyCorrectionRecordBo> updateList = new ArrayList<>();
        for (ApplyCorrectionRecordVo vo : applyCorrectionRecordVos) {
            ApplyCorrectionRecordBo update = new ApplyCorrectionRecordBo();
            update.setApplyCorrectionRecordId(vo.getApplyCorrectionRecordId());
            update.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
            updateList.add(update);
        }
        return applyCorrectionRecordService.updateBatchById(updateList);
    }

    /**
     * 更新ai申请批改记录
     */
    @Override
    public boolean updateAiApplyCorrectionRecord(String allowType, Long studentId) {
        AiApplyCorrectionRecordBo bo = new AiApplyCorrectionRecordBo();
        bo.setStudentId(studentId);
        bo.setApplyType(allowType);
        bo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        List<AiApplyCorrectionRecordVo> aiApplyCorrectionRecordBos = aiApplyCorrectionRecordService.queryList(bo);
        if (CollUtil.isEmpty(aiApplyCorrectionRecordBos)) {
            return true;
        }
        //更新ai申请批改记录
        List<AiApplyCorrectionRecordBo> updateList = new ArrayList<>();
        for (AiApplyCorrectionRecordVo vo : aiApplyCorrectionRecordBos) {
            AiApplyCorrectionRecordBo update = new AiApplyCorrectionRecordBo();
            update.setAiApplyCorrectionRecordId(vo.getAiApplyCorrectionRecordId());
            update.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
            updateList.add(update);
        }

        return aiApplyCorrectionRecordService.updateBatchById(updateList);
    }


    @Override
    public Long count(AllowOwnCorrectionBo bo) {
        handleQueryParam(bo);
        LambdaQueryWrapper<AllowOwnCorrection> queryWrapper = buildLambdaQueryWrapper(bo);
        return baseMapper.selectCount(queryWrapper);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void handleQueryParam(AllowOwnCorrectionBo record) {
        if (record.getStudentId() != null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }

        }
    }

}
