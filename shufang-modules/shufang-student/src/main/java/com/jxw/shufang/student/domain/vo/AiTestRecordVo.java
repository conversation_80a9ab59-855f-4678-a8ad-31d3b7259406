package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.AiTestRecord;

import java.io.Serial;
import java.io.Serializable;


/**
 * ai测验记录视图对象 ai_test_record
 *
 *
 * @date 2024-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiTestRecord.class)
public class AiTestRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ai测验记录id
     */
    @ExcelProperty(value = "ai测验记录id")
    private Long aiTestRecordId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 问题ID
     */
    @ExcelProperty(value = "问题ID")
    private Long questionId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 课程资源id
     */
    @ExcelProperty(value = "课程资源id")
    private Long courseResourceId;

    /**
     * 资源内容（oss_id）
     */
    @ExcelProperty(value = "资源内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id")
    private Long resourceContent;

    /**
     * 题目序号
     */
    @ExcelProperty(value = "题目序号")
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对 全错 半错）
     */
    @ExcelProperty(value = "作答结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如全对,全=错,半=错")
    private String answerResult;

    /**
     * 作答图片（oss_id）
     */
    @ExcelProperty(value = "作答图片", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id")
    private Long answerImg;


}
