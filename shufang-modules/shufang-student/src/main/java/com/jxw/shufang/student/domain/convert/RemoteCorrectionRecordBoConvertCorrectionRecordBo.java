package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.bo.RemoteCorrectionRecordBo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteCorrectionRecordBoConvertCorrectionRecordBo extends BaseMapper<RemoteCorrectionRecordBo, CorrectionRecordBo> {

}
