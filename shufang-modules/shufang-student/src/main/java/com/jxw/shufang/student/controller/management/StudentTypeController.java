package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentTypeBo;
import com.jxw.shufang.student.domain.vo.StudentTypeVo;
import com.jxw.shufang.student.service.IStudentTypeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）
 * 前端访问路由地址为:/student/type
 *
 *
 * @date 2024-03-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/type")
public class StudentTypeController extends BaseController {

    private final IStudentTypeService studentTypeService;

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    @SaCheckPermission("student:type:list")
    @GetMapping("/list")
    public TableDataInfo<StudentTypeVo> list(StudentTypeBo bo, PageQuery pageQuery) {
        return studentTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    @SaCheckPermission("student:type:export")
    @Log(title = "会员类型（会员卡的类型，默认有一个 体验卡 类型）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentTypeBo bo, HttpServletResponse response) {
        List<StudentTypeVo> list = studentTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员类型（会员卡的类型，默认有一个 体验卡 类型）", StudentTypeVo.class, response);
    }

    /**
     * 获取会员类型（会员卡的类型，默认有一个 体验卡 类型）详细信息
     *
     * @param studentTypeId 主键
     */
    @SaCheckPermission("student:type:query")
    @GetMapping("/{studentTypeId}")
    public R<StudentTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentTypeId) {
        return R.ok(studentTypeService.queryById(studentTypeId));
    }

    /**
     * 新增会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @SaCheckPermission("student:type:add")
    @Log(title = "会员类型（会员卡的类型，默认有一个 体验卡 类型）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentTypeBo bo) {
        return toAjax(studentTypeService.insertByBo(bo));
    }

    /**
     * 修改会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @SaCheckPermission("student:type:edit")
    @Log(title = "会员类型（会员卡的类型，默认有一个 体验卡 类型）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentTypeBo bo) {
        return toAjax(studentTypeService.updateByBo(bo));
    }

    /**
     * 删除会员类型（会员卡的类型，默认有一个 体验卡 类型）
     *
     * @param studentTypeIds 主键串
     */
    @SaCheckPermission("student:type:remove")
    @Log(title = "会员类型（会员卡的类型，默认有一个 体验卡 类型）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentTypeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentTypeIds) {
        return toAjax(studentTypeService.deleteWithValidByIds(List.of(studentTypeIds), true));
    }

    /**
     * 获取会员类型（会员卡的类型，默认有一个 体验卡 类型）下拉框列表
     */
    @SaCheckPermission("student:type:options")
    @GetMapping("/options")
    public R<List<StudentTypeVo>> options(StudentTypeBo bo) {
        return R.ok(studentTypeService.options(bo));
    }

    /**
     * 获取可授权会员卡信息
     */
    @SaCheckPermission("student:type:options")
    @GetMapping("/auth")
    public R<List<StudentTypeVo>> auth(StudentTypeBo bo) {
        return R.ok(studentTypeService.auth(bo));
    }


}
