package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.PrintRecord;

import java.util.List;

/**
 * 打印记录业务对象 print_record
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PrintRecord.class, reverseConvertGenerate = false)
public class PrintRecordBo extends BaseEntity {

    /**
     * 打印id
     */
    @NotNull(message = "打印id不能为空", groups = { EditGroup.class })
    private Long printRecordId;

    /**
     * 打印类型（1单份 2合并）
     */
    @NotBlank(message = "打印类型（1单份 2合并）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printType;


    /**
     * 来源课程id
     */
    //@NotNull(message = "来源课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 打印内容,KnowledgeResourceType枚举，多个用数组分开
     */
    @NotBlank(message = "打印内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String printContent;

    /**
     * 会员ID
     */
    private Long studentId;

    private List<Long> studentIdList;

    /**
     * 学习规划记录id
     */
    private Long studyPlanRecordId;

    /**
     * 试卷id,打印试卷时会用得上
     */
    private Long paperId;

    /**
     * 1代表原卷，2代表解析卷，3代表原卷+解析卷
     */
    private String paperType;

    /**
     * 门店id
     */
    private Long branchId;

    /**
     * 创建时间小于xx分钟
     */
    private Integer createTimeLessThanMinute;

    /**
     * taskType (最近打印任务 显示最近30分钟内提交的打印任务 1)(历史打印任务 打印任务提交超过30分钟,将自动加入“历史打印任务”;历史打印任务保留至次日18:00 2)
     */
    private Integer taskType;

    private List<Long> branchIdList;

}
