package com.jxw.shufang.student.dubbo;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.ValidatorUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.student.api.domain.vo.*;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.dto.StudentMerchantConfigDTO;
import com.jxw.shufang.student.domain.vo.ProductVo;
import com.jxw.shufang.student.domain.vo.StudentInfoVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IProductService;
import com.jxw.shufang.student.service.IStudentInfoService;
import com.jxw.shufang.student.service.IStudentService;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentServiceImpl implements RemoteStudentService {

    private final IStudentService studentService;
    private final IStudentInfoService studentInfoService;

    private final IProductService productService;

    @Override
    public List<RemoteStudentVo> queryStudentList(RemoteStudentBo bo) {
        StudentBo studentBo = MapstructUtils.convert(bo, StudentBo.class);
        List<StudentVo> studentVos = studentService.queryList(studentBo, bo.getIgnoreUserDataScope());
        return MapstructUtils.convert(studentVos, RemoteStudentVo.class);
    }

    @Override
    public List<Long> queryStudentIdList(RemoteStudentBo bo) {
        StudentBo studentBo = MapstructUtils.convert(bo, StudentBo.class);
        return studentService.queryStudentIdList(studentBo);
    }

    @Override
    public void updateStudentExpireTime(Long studentId, Date maxExpireTime) {
        studentService.updateStudentExpireTime(studentId, maxExpireTime);
    }

    @Override
    public Long queryStudentIdByUserId(Long userId) {
        return studentService.queryStudentIdByUserId(userId);
    }

    @Override
    public RemoteStudentVo queryStudentByUserId(Long userId) {
        StudentVo studentVo = studentService.queryStudentByUserId(userId);
        return MapstructUtils.convert(studentVo, RemoteStudentVo.class);
    }

    @Override
    public RemoteStudentVo queryStudentByUserId(Long userId, boolean withStudentInfo) {
        RemoteStudentVo remoteStudentVo = queryStudentByUserId(userId);
        if (remoteStudentVo != null) {
            // 查询会员info
            StudentInfoVo studentInfoVo = studentInfoService.queryByStudentId(remoteStudentVo.getStudentId());
            if (null != studentInfoVo) {
                RemoteStudentInfoVo info = new RemoteStudentInfoVo();
                info.setHasKuaidingPrivilege(studentInfoVo.getHasKuaidingPrivilege());
                info.setKuaidingPrivilegeExpireTime(studentInfoVo.getKuaidingPrivilegeExpireTime());
                remoteStudentVo.setStudentInfo(info);
            }
        }
        return remoteStudentVo;
    }

    @Override
    public RemoteStudentVo queryById(Long studentId) {
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(studentId);
        StudentVo studentVo = studentService.queryById(studentBo);
        return MapstructUtils.convert(studentVo, RemoteStudentVo.class);
    }

    @Override
    public List<RemoteStudentVo> getStudentListByStaffId(Long branchStaffId,String nameWithPhone) {
        List<StudentVo> studentVos = studentService.getStudentListByStaffId(branchStaffId,nameWithPhone);
        return MapstructUtils.convert(studentVos, RemoteStudentVo.class);
    }

    @Override
    public List<Long> getStudentIdListByBranchId(Long branchId,boolean ignoreDataPermission) {
        List<Long> resList = null;
        if(ignoreDataPermission){
            resList = DataPermissionHelper.ignore(()-> studentService.getStudentIdListByBranchId(branchId));
        }else{
            resList = studentService.getStudentIdListByBranchId(branchId);
        }
        return resList;
    }

    @Override
    public List<Long> getStudentIdListByBranchIdList(List<Long> branchIdList,boolean ignoreDataPermission) {
        List<Long> resList = null;
        if(ignoreDataPermission){
            resList = DataPermissionHelper.ignore(()-> studentService.getStudentIdListByBranchIdList(branchIdList));
        }else{
            resList = studentService.getStudentIdListByBranchIdList(branchIdList);
        }
        return resList;
    }

    @Override
    public List<RemoteStudentSimpleVO> batchQueryStudentById(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<Student> students = studentService.batchQueryStudentById(ids);
        return students.stream()
            .map(student -> {
                RemoteStudentSimpleVO remoteStudentVo = new RemoteStudentSimpleVO();
                remoteStudentVo.setStudentId(student.getStudentId());
                remoteStudentVo.setBranchId(student.getBranchId());
                remoteStudentVo.setStudentAccount(student.getStudentAccount());
                remoteStudentVo.setStudentName(student.getStudentName());
                remoteStudentVo.setStudentSex(student.getStudentSex());
                remoteStudentVo.setStudentGrade(student.getStudentGrade());
                return remoteStudentVo;
            }).collect(Collectors.toList());
    }

    @Override
    public RemoteStudentSimpleVO queryStudentById(Long studentId) {
        List<RemoteStudentSimpleVO> studentSimpleVOList = batchQueryStudentById(Collections.singletonList(studentId));
        return studentSimpleVOList.stream().findFirst().orElse(null);
    }

    @Override
    public String getCommonPayAppId(Long studentId) {
        return studentService.getCommonPayAppId(studentId);
    }

    @Override
    public RemoteStudentMerchantConfigDTO getCommonPayConfigByStudent(Long studentId) {
        StudentMerchantConfigDTO studentMerchantConfig = studentService.getStudentMerchantConfig(studentId);
        if(null == studentMerchantConfig){
            return null;
        }
        RemoteStudentMerchantConfigDTO merchantConfigDTO = new RemoteStudentMerchantConfigDTO();
        merchantConfigDTO.setAppId(studentMerchantConfig.getAppId());
        merchantConfigDTO.setMerchantName(studentMerchantConfig.getMerchantName());
        merchantConfigDTO.setPayType(studentMerchantConfig.getPayType());
        merchantConfigDTO.setPayCode(studentMerchantConfig.getPayCode());
        merchantConfigDTO.setWayCode(studentMerchantConfig.getWayCode());
        merchantConfigDTO.setConfigParamJson(studentMerchantConfig.getConfigParamJson());
        return merchantConfigDTO;
    }

    @Override
    public RemoteProductVo queryStudentRecentPeriodCard(Long studentId) {
        Student student = studentService.queryStudentById(studentId);
        ProductVo productVo = productService.queryRecentPeriodCard(student.getBranchId());
        return MapstructUtils.convert(productVo, RemoteProductVo.class);
    }

    @Override
    public Long insertByRemoteBo(RemoteStudentBo remoteStudentBo) throws ServiceException {
        StudentBo studentBo = MapstructUtils.convert(remoteStudentBo, StudentBo.class);
        if (remoteStudentBo.getIntroduceStudent() != null) {
            studentBo
                .setIntroduceStudent(MapstructUtils.convert(remoteStudentBo.getIntroduceStudent(), StudentBo.class));
        }
        ValidatorUtils.validate(studentBo, AddGroup.class);
        if (studentBo.getBranchId() == null) {
            throw new ServiceException("请选择门店");
        }
        if (StringUtils.isNotBlank(studentBo.getStudentPassword()) && studentBo.getStudentPassword().length() < 6) {
            throw new ServiceException("密码长度不能小于6位");
        }
        if (null != studentBo.getIntroduceStudent()) {
            if (null == studentBo.getIntroduceStudent().getStudentId()) {
                throw new ServiceException("请选择介绍人信息");
            }
        }
        return studentService.insertByBo(studentBo);
    }

    @Override
    public void updateBuyCardFlag(Long studentId, Integer updateFlag) throws ServiceException {
        studentService.updateBuyCardFlag(studentId, updateFlag);
    }

    @Override
    public RemoteStudentVo queryStudentWithBranchById(Long studentId) {
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(studentId);
        studentBo.setWithBranchInfo(true);
        StudentVo studentVo = studentService.queryById(studentBo);
        return MapstructUtils.convert(studentVo, RemoteStudentVo.class);
    }

}
