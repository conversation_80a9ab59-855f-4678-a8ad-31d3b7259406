package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentIntroduceRecordBo;
import com.jxw.shufang.student.domain.vo.StudentIntroduceRecordVo;
import com.jxw.shufang.student.service.IStudentIntroduceRecordService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studentIntroduce")
public class StudentIntroduceRecordController {

    private final IStudentIntroduceRecordService studentIntroduceService;

    /**
     * 查询会员转介绍列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/list")
    @SaCheckPermission("student:introduce:list")
    public TableDataInfo<StudentIntroduceRecordVo> list(StudentIntroduceRecordBo bo, PageQuery pageQuery) {
        if(null != bo.getCheckInTimeEnd()){
            bo.setCheckInTimeEnd(DateUtil.endOfDay(bo.getCheckInTimeEnd()));
        }
        return studentIntroduceService.queryPage(bo, pageQuery);
    }

    /**
     * 导出会员转介绍列表
     *
     * @param bo
     * @return
     */
    @PostMapping("/export")
    @SaCheckPermission("student:introduce:export")
    public void export(StudentIntroduceRecordBo bo, HttpServletResponse response) {
        List<StudentIntroduceRecordVo> StudentIntroduceRecordVos = studentIntroduceService.listAllRecord(bo);
        ExcelUtil.exportExcel(StudentIntroduceRecordVos, "会员转介绍记录", StudentIntroduceRecordVo.class, response);
    }
}
