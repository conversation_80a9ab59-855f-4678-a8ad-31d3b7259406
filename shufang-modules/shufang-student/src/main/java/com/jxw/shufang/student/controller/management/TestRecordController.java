package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.TestRecordBo;
import com.jxw.shufang.student.domain.vo.TestRecordVo;
import com.jxw.shufang.student.service.ITestRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测验记录
 * 前端访问路由地址为:/student/testRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/testRecord")
public class TestRecordController extends BaseController {

    private final ITestRecordService testRecordService;

    /**
     * 查询测验记录列表
     */
    @SaCheckPermission("student:testRecord:list")
    @GetMapping("/list")
    public TableDataInfo<TestRecordVo> list(TestRecordBo bo, PageQuery pageQuery) {
        return testRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出测验记录列表
     */
    @SaCheckPermission("student:testRecord:export")
    @Log(title = "测验记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TestRecordBo bo, HttpServletResponse response) {
        List<TestRecordVo> list = testRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "测验记录", TestRecordVo.class, response);
    }

    /**
     * 获取测验记录详细信息
     *
     * @param testRecordId 主键
     */
    @SaCheckPermission("student:testRecord:query")
    @GetMapping("/{testRecordId}")
    public R<TestRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long testRecordId) {
        return R.ok(testRecordService.queryById(testRecordId));
    }

    /**
     * 新增测验记录
     */
    @SaCheckPermission("student:testRecord:add")
    @Log(title = "测验记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TestRecordBo bo) {
        return toAjax(testRecordService.insertByBo(bo));
    }

    /**
     * 修改测验记录
     */
    @SaCheckPermission("student:testRecord:edit")
    @Log(title = "测验记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TestRecordBo bo) {
        return toAjax(testRecordService.updateByBo(bo));
    }

    /**
     * 删除测验记录
     *
     * @param testRecordIds 主键串
     */
    @SaCheckPermission("student:testRecord:remove")
    @Log(title = "测验记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{testRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] testRecordIds) {
        return toAjax(testRecordService.deleteWithValidByIds(List.of(testRecordIds), true));
    }
}
