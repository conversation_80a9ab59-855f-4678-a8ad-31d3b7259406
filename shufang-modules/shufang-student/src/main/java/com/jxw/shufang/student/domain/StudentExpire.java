package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）对象 student_expire
 *
 *
 * @date 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_expire")
public class StudentExpire extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员过期id
     */
    @TableId(value = "student_expire_id")
    private Long studentExpireId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 是否显示（0是 1否 用于假删除）
     */
    private String isShow;


}
