package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AttendanceUser;

import java.util.List;

/**
 * 考勤关联用户业务对象 attendance_user
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AttendanceUser.class, reverseConvertGenerate = false)
public class AttendanceUserBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long attendanceUserId;

    /**
     * 考勤机域名或IP
     */
    @NotBlank(message = "考勤机域名或IP不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ip;

    /**
     * 系统用户id
     */
    @NotNull(message = "系统用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 多个系统用户id
     */
    private List<Long> userIds;


}
