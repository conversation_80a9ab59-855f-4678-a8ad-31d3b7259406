package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentSign;

import java.util.List;

/**
 * 会员标签业务对象 student_sign
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentSign.class, reverseConvertGenerate = false)
public class StudentSignBo extends BaseEntity {

    /**
     * 会员标签id
     */
    @NotNull(message = "会员标签id不能为空", groups = { EditGroup.class })
    private Long studentSignId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 标签内容
     */
    @NotBlank(message = "标签内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signContent;

    private List<Long> studentIds;


}
