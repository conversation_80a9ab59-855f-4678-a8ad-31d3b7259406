package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentConsultantRecord;
import com.jxw.shufang.student.domain.bo.StudentConsultantRecordBo;
import com.jxw.shufang.student.domain.dto.StudentWithConsultantDTO;
import com.jxw.shufang.student.domain.vo.StudentConsultantRecordVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）Service接口
 *
 *
 * @date 2024-02-29
 */
public interface IStudentConsultantRecordService {

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    StudentConsultantRecordVo queryById(Long studentConsultantRecordId);

    /**
     * 查询会员顾问记录
     */
    List<StudentConsultantRecordVo> queryByIdList(List<Long> studentConsultantRecordIdList);

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    TableDataInfo<StudentConsultantRecordVo> queryPageList(StudentConsultantRecordBo bo, PageQuery pageQuery);

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    List<StudentConsultantRecordVo> queryList(StudentConsultantRecordBo bo);

    /**
     * 新增会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean insertByBo(StudentConsultantRecordBo bo);

    /**
     * 修改会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean updateByBo(StudentConsultantRecordBo bo);

    /**
     * 校验并批量删除会员顾问记录（时间逆序的最后一条记录和会员中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取员工负责会员ID
     *
     * @param staffIdList 员工id列表
     *
     * @date 2024/03/05 09:06:19
     */
    Map<Long,List<Long>> getStaffResponsibleStudentIdMap(List<Long> staffIdList);

    /**
     * 获取员工负责会员IDList
     *
     * @param staffId 员工id
     *
     * @date 2024/03/05 09:06:19
     */
    List<Long> getStaffResponsibleStudentIdList(Long staffId);

    /**
     * 获取会员对应的顾问ID，不一定有记录
     *
     * @param studentIdList 会员id列表
     *
     * @date 2024/03/05 09:06:19
     */
    Map<Long,Long> getStudentConsultantIdMap(List<Long> studentIdList);

    StudentConsultantRecord queryStudentConsultantRecordById(Long studentConsultantRecordId);

    void cleanCache();

    /**
     * 查询会员顾问记录
     *
     * @param studentIds
     * @return
     */
    List<StudentWithConsultantDTO> queryConsultantRecordListByStudentId(List<Long> studentIds);
}
