package com.jxw.shufang.student.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.QuestionCollect;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = QuestionCollect.class)
public class QuestionCollectVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 题目收藏id
     */
    private Long questionCollectId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 题目ID
     */
    private Long questionId;

    private RemoteQuestionVo question;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;

    private Date createTime;

    private Date updateTime;
}
