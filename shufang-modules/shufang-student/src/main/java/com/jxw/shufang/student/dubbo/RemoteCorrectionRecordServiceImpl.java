package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteCorrectionRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteCorrectionRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteCorrectionRecordVo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;
import com.jxw.shufang.student.service.ICorrectionRecordService;
import org.springframework.stereotype.Service;

import java.util.List;



@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteCorrectionRecordServiceImpl implements RemoteCorrectionRecordService {

    private final ICorrectionRecordService correctionRecordService;
    @Override
    public List<RemoteCorrectionRecordVo> queryList(RemoteCorrectionRecordBo correctionRecordBo,boolean ignoreDataPermission) {
        CorrectionRecordBo convert = MapstructUtils.convert(correctionRecordBo, CorrectionRecordBo.class);
        List<CorrectionRecordVo> list = null;
        if(ignoreDataPermission){
            list = DataPermissionHelper.ignore(()-> correctionRecordService.queryList(convert));
        }else{
            list = correctionRecordService.queryList(convert);
        }
        return MapstructUtils.convert(list, RemoteCorrectionRecordVo.class);
    }

    @Override
    public Long count(RemoteCorrectionRecordBo correctionRecordBo,boolean ignoreDataPermission) {
        CorrectionRecordBo convert = MapstructUtils.convert(correctionRecordBo, CorrectionRecordBo.class);
        if(ignoreDataPermission){
            return DataPermissionHelper.ignore(()-> correctionRecordService.count(convert));
        }else {
            return correctionRecordService.count(convert);
        }

    }
}
