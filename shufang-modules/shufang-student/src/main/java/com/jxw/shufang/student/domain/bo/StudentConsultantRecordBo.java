package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentConsultantRecord;

import java.util.List;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）业务对象 student_consultant_record
 *
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentConsultantRecord.class, reverseConvertGenerate = false)
public class StudentConsultantRecordBo extends BaseEntity {

    /**
     * 会员顾问记录id
     */
    @NotNull(message = "会员顾问记录id不能为空", groups = { EditGroup.class })
    private Long studentConsultantRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 会员顾问id
     */
    @NotNull(message = "会员顾问id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentConsultantId;

    /**
     * 记录说明
     */
    //@NotBlank(message = "记录说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recordRemark;

    private List<Long> studetnIdLsit;

    /**
     * 是否携带员工信息
     */
    private Boolean withStaffInfo;


}
