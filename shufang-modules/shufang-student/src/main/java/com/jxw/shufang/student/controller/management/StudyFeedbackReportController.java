package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyFeedbackReportBo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.service.IStudyFeedbackReportService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习反馈报告管理
 * 前端访问路由地址为:/student/management/feedbackReport
 *
 * @date 2024-06-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/feedbackReport")
public class StudyFeedbackReportController extends BaseController {

    private final IStudyFeedbackReportService studyFeedbackReportService;

    /**
     * 查询学习反馈报告列表
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudyFeedbackReportVo> list(StudyFeedbackReportBo bo, PageQuery pageQuery) {
        return studyFeedbackReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出学习反馈报告列表
     */
    @SaCheckPermission("student:studyPlanningRecord:export")
    @Log(title = "学习反馈报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyFeedbackReportBo bo, HttpServletResponse response) {
        List<StudyFeedbackReportVo> list = studyFeedbackReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习反馈报告", StudyFeedbackReportVo.class, response);
    }

    /**
     * 获取学习反馈报告详细信息
     */
    @SaCheckPermission("student:studyPlanningRecord:query")
    @GetMapping("/{id}")
    public R<StudyFeedbackReportVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long id) {
        return R.ok(studyFeedbackReportService.queryById(id));
    }

    /**
     * 新增学习反馈报告
     */
    @SaCheckPermission("student:studyPlanningRecord:add")
    @Log(title = "学习反馈报告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody StudyFeedbackReportBo bo) {
        Boolean result = studyFeedbackReportService.insertByBo(bo);
        if (result && bo.getId() != null) {
            return R.ok("新增成功", bo.getId());
        } else {
            return R.fail("新增失败");
        }
    }

    /**
     * 修改学习反馈报告
     */
    @SaCheckPermission("student:studyPlanningRecord:edit")
    @Log(title = "学习反馈报告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyFeedbackReportBo bo) {
        return toAjax(studyFeedbackReportService.updateByBo(bo));
    }

    /**
     * 删除学习反馈报告
     */
    @SaCheckPermission("student:studyPlanningRecord:remove")
    @Log(title = "学习反馈报告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(studyFeedbackReportService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据学生ID和时间范围查询报告
     */
    @SaCheckPermission("student:studyPlanningRecord:query")
    @GetMapping("/queryByStudent")
    public R<StudyFeedbackReportVo> queryByStudent(@RequestParam Long studentId,
                                                   @RequestParam String periodStart,
                                                   @RequestParam String periodEnd) {
        return R.ok(studyFeedbackReportService.queryByStudentAndPeriod(studentId, periodStart, periodEnd));
    }

    /**
     * 获取学习规划H5地址
     * @param studyPlanningId 学习规划ID
     * @return 学习规划H5地址
     */
    @GetMapping("/getStudyPlanningUrl")
    public R<String> getStudyPlanningUrl(@NotNull(message = "学习规划ID不能为空") Long studyPlanningId) {
        return R.ok("操作成功", studyFeedbackReportService.getStudyPlanningUrl(studyPlanningId));
    }

    /**
     * 学习反馈确认接口
     *
     * <p>当学生或家长确认已查看学习反馈报告后，调用此接口标记反馈状态为已反馈。
     * 该操作会触发以下业务逻辑：</p>
     *
     * <ul>
     *   <li>1. 更新学习反馈报告的反馈状态：从待反馈(0)变更为已反馈(1)</li>
     *   <li>2. 查找与该反馈报告关联的学习规划待处理记录</li>
     *   <li>3. 更新关联的学习规划待处理记录的反馈状态为已反馈</li>
     *   <li>4. 设置实际反馈时间为当前时间，用于统计和超时判断</li>
     * </ul>
     *
     * <p><strong>业务流程说明：</strong></p>
     * <p>学习反馈报告通过 study_planning_feedback_pending_relation 关联表
     * 与学习规划待处理记录(study_planning_pending)建立关联关系。
     * 当反馈成功后，需要同步更新相关学习规划记录的状态，
     * 以确保学习规划待处理列表中的反馈状态能够正确显示。</p>
     *
     * @param id 学习反馈报告ID
     * @return 操作结果
     */
    @GetMapping("/doFeedback")
    public R<Void> updateFeedbackStatus(@RequestParam Long id) {
        Boolean result = studyFeedbackReportService.updateFeedbackStatus(id, 1);
        if (result) {
            return R.ok("反馈确认成功");
        } else {
            return R.fail("反馈确认失败");
        }
    }


}
