package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.QuestionCollect;
import com.jxw.shufang.student.domain.bo.QuestionCollectBo;
import com.jxw.shufang.student.domain.vo.QuestionCollectVo;
import com.jxw.shufang.student.mapper.QuestionCollectMapper;
import com.jxw.shufang.student.service.IQuestionCollectService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class QuestionCollectServiceImpl implements IQuestionCollectService, BaseService {
    private final QuestionCollectMapper baseMapper;
    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @Override
    public TableDataInfo<QuestionCollectVo> pageQuestionCollects(QuestionCollectBo questionCollectBo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionCollect> lqw = buildLambdaQueryWrapper(questionCollectBo);
        Page<QuestionCollectVo> result = baseMapper.selectQuestionCollects(pageQuery.build(), lqw);
        List<QuestionCollectVo> collectVos = result.getRecords();
        List<Long> questionIds = collectVos.stream().map(QuestionCollectVo::getQuestionId).toList();
        Map<Long, RemoteQuestionVo> questionIdMap = getQuestionIdMap(questionIds);
        collectVos.forEach(questionCollectVo -> questionCollectVo.setQuestion(questionIdMap.getOrDefault(questionCollectVo.getQuestionId(), null)));
        return TableDataInfo.build(result);
    }

    @Override
    public List<QuestionCollectVo> listQuestionCollectsByQuestionIds(List<Long> questionIds) {
        LambdaQueryWrapper<QuestionCollect> lqw = Wrappers.lambdaQuery();
        lqw.eq(QuestionCollect::getStudentId, LoginHelper.getStudentId());
        lqw.in(QuestionCollect::getQuestionId, questionIds);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Boolean insertBatchByBo(List<QuestionCollectBo> bos) {
        List<QuestionCollect> questionCollects = MapstructUtils.convert(bos, QuestionCollect.class);
        List<Long> questionIds = questionCollects.stream().map(QuestionCollect::getQuestionId).toList();
        Map<Long, RemoteQuestionVo> questionIdMap = getQuestionIdMap(questionIds);
        questionCollects.forEach(questionCollect -> questionCollect.setSubjectId(questionIdMap.get(questionCollect.getQuestionId()).getSubjectId().intValue()));
        return baseMapper.insertBatch(questionCollects);
    }

    @Override
    public Boolean deleteBatchByBo(List<QuestionCollectBo> bos) {
        return baseMapper.deleteBatchByBo(bos) > 0;
    }

    private LambdaQueryWrapper<QuestionCollect> buildLambdaQueryWrapper(QuestionCollectBo questionCollectBo) {
        LambdaQueryWrapper<QuestionCollect> lqw = Wrappers.lambdaQuery();
        lqw.eq(QuestionCollect::getStudentId, questionCollectBo.getStudentId());
        if (null != questionCollectBo.getSubjectId() && 0 != questionCollectBo.getSubjectId()) {
            lqw.eq(QuestionCollect::getSubjectId, questionCollectBo.getSubjectId());
        }
        lqw.orderByDesc(QuestionCollect::getCreateTime);
        return lqw;
    }

    public Map<Long, RemoteQuestionVo> getQuestionIdMap(List<Long> questionIds) {
        List<RemoteQuestionVo> remoteQuestionVos = remoteQuestionService.listQuestions(questionIds);
        return remoteQuestionVos.stream().collect(Collectors.toMap(RemoteQuestionVo::getId, Function.identity()));
    }
}
