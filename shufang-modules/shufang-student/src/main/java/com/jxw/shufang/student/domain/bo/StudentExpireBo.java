package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentExpire;

import java.util.List;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）业务对象 student_expire
 *
 *
 * @date 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentExpire.class, reverseConvertGenerate = false)
public class StudentExpireBo extends BaseEntity {

    /**
     * 会员过期id
     */
    @NotNull(message = "会员过期id不能为空", groups = { EditGroup.class })
    private Long studentExpireId;

    /**
     * 会员id
     */
//    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 是否显示（0是 1否 用于假删除）
     */
//    @NotBlank(message = "是否显示（0是 1否 用于假删除）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isShow;

    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 门店id
     */
    private Long branchId;

    /**
     * 大于等于过期天数
     */
    private Integer leEqExpireDays;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 是否携带门店信息，默认不携带
     */
    private Boolean withBranchInfo = Boolean.FALSE;

    /**
     * 带有会员订单信息
     */
    private Boolean withStudentOrderInfo = Boolean.FALSE;

    private Boolean withNotFinishPlanCount = Boolean.FALSE;

    private List<Long> studentIdList;

}
