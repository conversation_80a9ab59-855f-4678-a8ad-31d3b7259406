package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentConsultantRecordBo;
import com.jxw.shufang.student.domain.vo.StudentConsultantRecordVo;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）
 * 前端访问路由地址为:/student/consultantRecord
 *
 *
 * @date 2024-03-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/consultantRecord")
public class StudentConsultantRecordController extends BaseController {

    private final IStudentConsultantRecordService studentConsultantRecordService;

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:consultantRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudentConsultantRecordVo> list(StudentConsultantRecordBo bo, PageQuery pageQuery) {
        return studentConsultantRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:consultantRecord:export")
    @Log(title = "会员顾问记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentConsultantRecordBo bo, HttpServletResponse response) {
        List<StudentConsultantRecordVo> list = studentConsultantRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员顾问记录（时间逆序的最后一条记录和会员中的对应）", StudentConsultantRecordVo.class, response);
    }

    /**
     * 获取会员顾问记录（时间逆序的最后一条记录和会员中的对应）详细信息
     *
     * @param studentConsultantRecordId 主键
     */
    @SaCheckPermission("student:consultantRecord:query")
    @GetMapping("/{studentConsultantRecordId}")
    public R<StudentConsultantRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentConsultantRecordId) {
        return R.ok(studentConsultantRecordService.queryById(studentConsultantRecordId));
    }

    /**
     * 新增会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:consultantRecord:add")
    @Log(title = "会员顾问记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentConsultantRecordBo bo) {
        return toAjax(studentConsultantRecordService.insertByBo(bo));
    }

    /**
     * 修改会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:consultantRecord:edit")
    @Log(title = "会员顾问记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentConsultantRecordBo bo) {
        return toAjax(studentConsultantRecordService.updateByBo(bo));
    }

    /**
     * 删除会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     *
     * @param studentConsultantRecordIds 主键串
     */
    @SaCheckPermission("student:consultantRecord:remove")
    @Log(title = "会员顾问记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentConsultantRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentConsultantRecordIds) {
        return toAjax(studentConsultantRecordService.deleteWithValidByIds(List.of(studentConsultantRecordIds), true));
    }


}
