package com.jxw.shufang.student.service.studyduration.processor;

import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.dto.AiStudyDurationProcessingContextDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyRecordDTO;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/4/7 10:02
 * @Version 1
 * @Description AI伴学时长统计
 */
public class AiStudyTimeProcessor {
    private List<AiStudyRecord> updates = new ArrayList<>();
    private List<AiStudyRecord> inserts = new ArrayList<>();
    private AiStudyDurationProcessingContextDTO context;

    public AiStudyTimeProcessor(AiStudyDurationProcessingContextDTO context) {
        this.context = context;
    }

    public List<AiStudyRecord> getUpdates() {
        return updates;
    }

    public List<AiStudyRecord> getInserts() {
        return inserts;
    }

    /**
     * 执行
     *
     * @param recordBo
     */
    public void process(SaveOrUpdateAiStudyRecordDTO recordBo) {
        if (null == recordBo) {
            return;
        }
        StudyModuleAndGroupEnum studyModuleTypeEnum = context.getStudyModuleTypeEnum();
        AiStudyRecord existRecord = this.getExistRecord(recordBo, studyModuleTypeEnum);
        if (existRecord != null) {
            Optional.of(this.buildUpdateRecord(existRecord, recordBo)).ifPresent(updates::add);
        } else {
            Optional.of(this.buildInsertRecord(recordBo)).ifPresent(inserts::add);
        }
    }

    private AiStudyRecord getExistRecord(SaveOrUpdateAiStudyRecordDTO recordBo, StudyModuleAndGroupEnum studyModuleTypeEnum) {
        AiStudyRecord existRecord;
        if (studyModuleTypeEnum.equals(StudyModuleAndGroupEnum.AI_ERROR_VIDEO)) {
            existRecord = context.getExistAiStudyRecord().stream()
                .filter(r -> r.getTestPaperId().equals(recordBo.getTestPaperId()) && r.getStudentId().equals(recordBo.getStudentId()))
                .findFirst().orElse(null);
        } else {
            existRecord = context.getExistAiStudyRecord().stream()
                .filter(r -> r.getCourseId().equals(recordBo.getCourseId()) && r.getStudentId().equals(recordBo.getStudentId()))
                .findFirst().orElse(null);
        }
        return existRecord;
    }

    private AiStudyRecord buildInsertRecord(SaveOrUpdateAiStudyRecordDTO recordDTO) {
        Student student = context.getStudentMap().get(recordDTO.getStudentId());

        AiStudyRecord studyVideoRecord = new AiStudyRecord();
        studyVideoRecord.setCourseId(recordDTO.getCourseId());
        studyVideoRecord.setStudentId(recordDTO.getStudentId());
        studyVideoRecord.setTestPaperId(recordDTO.getTestPaperId());
        studyVideoRecord.setCreateBy(student.getCreateBy());
        studyVideoRecord.setCreateDept(student.getCreateDept());
        studyVideoRecord.setCreateTime(new Date());
        studyVideoRecord.setUpdateBy(student.getCreateBy());
        studyVideoRecord.setUpdateTime(student.getUpdateTime());
        Long sliceSeconds = this.getSliceSeconds(recordDTO.getStudyVideoDuration(), recordDTO.getStudyVideoSlices());
        this.setDurationTimeByModelType(sliceSeconds, studyVideoRecord, recordDTO.getVideoId());
        return studyVideoRecord;
    }

    private AiStudyRecord buildUpdateRecord(AiStudyRecord existRecord, SaveOrUpdateAiStudyRecordDTO recordDTO) {
        Student student = context.getStudentMap().get(recordDTO.getStudentId());

        String studyVideoSlices = recordDTO.getStudyVideoSlices();
        Long studyVideoDuration = recordDTO.getStudyVideoDuration();
        Long sliceSeconds = this.getSliceSeconds(studyVideoDuration, studyVideoSlices);

        AiStudyRecord updateRecord = new AiStudyRecord();
        updateRecord.setAiStudyRecordId(existRecord.getAiStudyRecordId());
        updateRecord.setUpdateBy(student.getCreateBy());
        updateRecord.setUpdateTime(new Date());
        long totalDurationTime = this.getTotalDurationTime(existRecord, sliceSeconds);
        this.setDurationTimeByModelType(totalDurationTime, updateRecord, recordDTO.getVideoId());
        return updateRecord;
    }

    private long getTotalDurationTime(AiStudyRecord existingRecord, Long sliceSeconds) {
        StudyModuleTypeEnum moduleEnum = context.getStudyModuleTypeEnum().getModuleEnum();
        boolean noVideoSpliceModuleType = StudyModuleTypeEnum.PREVIEW.equals(moduleEnum) || StudyModuleTypeEnum.SELF_SPEECH.equals(moduleEnum);
        long totalDurationTime = 0;
        Long existVideoTotalDuration = this.getExistVideoTotalDuration(existingRecord);
        // 预和自讲没有视频分片，无法判断视频分片进度，无法判断是否累加学习，使用取最大值的方式
        if (noVideoSpliceModuleType) {
            totalDurationTime = Math.max(existVideoTotalDuration, sliceSeconds);
        } else {
            totalDurationTime = existVideoTotalDuration + sliceSeconds;
        }
        return totalDurationTime;
    }

    private Long getExistVideoTotalDuration(AiStudyRecord existRecord) {
        StudyModuleTypeEnum moduleTypeEnum = context.getStudyModuleTypeEnum().getModuleEnum();

        Long totalDurationTime = 0L;
        if (moduleTypeEnum.equals(StudyModuleTypeEnum.PREVIEW)) {
            totalDurationTime = existRecord.getPreviewTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.SELF_SPEECH)) {
            totalDurationTime = existRecord.getSelfSpeechTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.TEST)) {
            totalDurationTime = existRecord.getTestVideoTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.PRACTICE)) {
            totalDurationTime = existRecord.getPracticeVideoTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.STUDY)) {
            totalDurationTime = existRecord.getStudyVideoTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.AI_TEST_ERROR_VIDEO)) {
            totalDurationTime = existRecord.getAiExplainTotalDuration();
        } else {
            totalDurationTime = existRecord.getStudyVideoTotalDuration();
        }
        return Optional.ofNullable(totalDurationTime).orElse(0L);
    }

    private void setDurationTimeByModelType(Long totalDuration, AiStudyRecord updateRecord, Long videoId) {
        StudyModuleTypeEnum moduleTypeEnum = context.getStudyModuleTypeEnum().getModuleEnum();
        Long courseDuration = Optional.ofNullable(context.getCourseDurationMap()).map(m->m.get(videoId)).orElse(0L);
        // 是否超过课程时长
        boolean overCourseMaxTime = totalDuration >= courseDuration;
        long remainDuration = courseDuration - totalDuration;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;

        if (moduleTypeEnum.equals(StudyModuleTypeEnum.PREVIEW)) {
            updateRecord.setPreviewTotalDuration(overCourseMaxTime ? courseDuration : totalDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.SELF_SPEECH)) {
            Long selfSpeechMaxDurationTime = TimeUnit.MINUTES.toSeconds(15);
            boolean overMaxSelfSpeechDurationTime = totalDuration >= selfSpeechMaxDurationTime;
            updateRecord.setSelfSpeechTotalDuration(overMaxSelfSpeechDurationTime ? selfSpeechMaxDurationTime : totalDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.TEST)) {
            updateRecord.setTestVideoTotalDuration(useCourseDuration ? courseDuration : totalDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.PRACTICE)) {
            updateRecord.setPracticeVideoTotalDuration(useCourseDuration ? courseDuration : totalDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.STUDY)) {
            updateRecord.setStudyVideoTotalDuration(useCourseDuration ? courseDuration : totalDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.AI_TEST_ERROR_VIDEO)) {
            updateRecord.setAiExplainTotalDuration(useCourseDuration ? courseDuration : totalDuration);
        } else {
            updateRecord.setStudyVideoTotalDuration(totalDuration);
        }
    }

    private Long getSliceSeconds(Long studyVideoDuration, String videoSlice) {
        if (null != studyVideoDuration && studyVideoDuration > 0) {
            return studyVideoDuration;
        }
        return this.getSliceSecondsBySlice(videoSlice);
    }

    private Long getSliceSecondsBySlice(String videoSlice) {
        return VideoSlicesUtils.calVideoSliceSeconds(videoSlice);
    }
}
