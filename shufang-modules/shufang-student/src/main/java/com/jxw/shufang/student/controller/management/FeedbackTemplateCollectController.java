package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateCollectBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateCollectVo;
import com.jxw.shufang.student.service.IFeedbackTemplateCollectService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 反馈模板收藏
 * 前端访问路由地址为:/student/management/feedbackTemplateCollect
 *
 *
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/feedbackTemplateCollect")
public class FeedbackTemplateCollectController extends BaseController {

    private final IFeedbackTemplateCollectService feedbackTemplateCollectService;

    /**
     * 查询反馈模板收藏列表
     */
    @SaCheckPermission("student:feedbackTemplateCollect:list")
    @GetMapping("/list")
    public TableDataInfo<FeedbackTemplateCollectVo> list(FeedbackTemplateCollectBo bo, PageQuery pageQuery) {
        return feedbackTemplateCollectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出反馈模板收藏列表
     */
    @SaCheckPermission("student:feedbackTemplateCollect:export")
    @Log(title = "反馈模板收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FeedbackTemplateCollectBo bo, HttpServletResponse response) {
        List<FeedbackTemplateCollectVo> list = feedbackTemplateCollectService.queryList(bo);
        ExcelUtil.exportExcel(list, "反馈模板收藏", FeedbackTemplateCollectVo.class, response);
    }

    /**
     * 获取反馈模板收藏详细信息
     *
     * @param feedbackTemplateCollectId 主键
     */
    @SaCheckPermission("student:feedbackTemplateCollect:query")
    @GetMapping("/{feedbackTemplateCollectId}")
    public R<FeedbackTemplateCollectVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long feedbackTemplateCollectId) {
        return R.ok(feedbackTemplateCollectService.queryById(feedbackTemplateCollectId));
    }

    /**
     * 新增反馈模板收藏
     */
    @SaCheckPermission("student:feedbackTemplateCollect:add")
    @Log(title = "反馈模板收藏", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FeedbackTemplateCollectBo bo) {
        return toAjax(feedbackTemplateCollectService.insertByBo(bo));
    }

    /**
     * 修改反馈模板收藏
     */
    @SaCheckPermission("student:feedbackTemplateCollect:edit")
    @Log(title = "反馈模板收藏", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FeedbackTemplateCollectBo bo) {
        return toAjax(feedbackTemplateCollectService.updateByBo(bo));
    }

    /**
     * 删除反馈模板收藏
     *
     * @param feedbackTemplateCollectIds 主键串
     */
    @SaCheckPermission("student:feedbackTemplateCollect:remove")
    @Log(title = "反馈模板收藏", businessType = BusinessType.DELETE)
    @DeleteMapping("/{feedbackTemplateCollectIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] feedbackTemplateCollectIds) {
        return toAjax(feedbackTemplateCollectService.deleteWithValidByIds(List.of(feedbackTemplateCollectIds), true));
    }

    /**
     * 收藏模版
     *
     * @param feedbackTemplateId 主键
     */
    @SaCheckPermission("student:feedbackTemplateCollect:edit")
    @Log(title = "反馈模板收藏", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/collect")
    public R<Void> collect(@NotNull(message = "主键不能为空") Long feedbackTemplateId) {
        return toAjax(feedbackTemplateCollectService.collect(feedbackTemplateId,LoginHelper.getUserId()));
    }
}
