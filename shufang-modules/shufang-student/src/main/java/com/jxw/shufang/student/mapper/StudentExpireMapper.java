package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudentExpire;
import com.jxw.shufang.student.domain.vo.StudentExpireVo;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）Mapper接口
 *
 *
 * @date 2024-03-08
 */
public interface StudentExpireMapper extends BaseMapperPlus<StudentExpire, StudentExpireVo> {

    Page<StudentExpireVo> selectPageList(@Param("page") Page<StudentExpire> build,@Param(Constants.WRAPPER) QueryWrapper<StudentExpire> lqw);
}
