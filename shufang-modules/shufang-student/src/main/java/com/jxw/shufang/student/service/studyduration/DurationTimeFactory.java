package com.jxw.shufang.student.service.studyduration;

import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/8 0:40
 * @Version 1
 * @Description
 */
@Service
public class DurationTimeFactory<T> {
    @Resource
    private List<AbstractQuestionDurationTime> questionSlicesTimes;
    @Resource
    private List<AbstractStudyRecordTime> studyRecordTimes;
    @Resource
    private List<AbstractAiStudyRecordTime> aiStudyRecordTimes;

    public AbstractQuestionDurationTime getQuestionService(StudyModuleAndGroupEnum moduleTypeEnum) {
        return findFirstMatchingService(questionSlicesTimes, moduleTypeEnum);
    }

    public AbstractStudyRecordTime getStudyRecordService(StudyModuleAndGroupEnum moduleTypeEnum) {
        return findFirstMatchingService(studyRecordTimes, moduleTypeEnum);
    }

    public AbstractAiStudyRecordTime getAiStudyRecordService(StudyModuleAndGroupEnum moduleTypeEnum) {
        return findFirstMatchingService(aiStudyRecordTimes, moduleTypeEnum);
    }

    private <T extends ModuleGroupProvider<T>> T findFirstMatchingService(List<T> services, StudyModuleAndGroupEnum moduleTypeEnum) {
        return services.stream()
            .filter(service -> this.matchesModuleAndGroup(service, moduleTypeEnum))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("未找到" + services.getClass().getSimpleName() + "类型的服务"));
    }

    private <T extends ModuleGroupProvider<T>> boolean matchesModuleAndGroup(T service, StudyModuleAndGroupEnum moduleTypeEnum) {
        return service.matchStudyModuleType().contains(moduleTypeEnum.getModuleEnum()) &&
            service.matchStudyModelGroup().contains(moduleTypeEnum.getGroupEnum());
    }
}
