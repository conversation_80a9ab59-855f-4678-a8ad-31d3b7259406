package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.RemotePaperCommonService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.*;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.student.domain.vo.StudentEtxPaperSyncVo;
import com.jxw.shufang.student.enums.PaperTypeConstant;
import com.jxw.shufang.student.service.IStudentExtPaperService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paper/common")
public class AStudentExtPaperController {

    private final IStudentExtPaperService iStudentExtPaperService;

    @DubboReference
    private RemotePaperCommonService commonService;

    @DubboReference
    private RemoteQuestionService remoteQuestionService;
    @DubboReference
    private RemoteCdsCommonService remoteCdsCommonService;

    /**
     * 获取出版社细分版本
     */
    @GetMapping("/getEdition")
    public R<List<RemoteIdNameVo>> getEditionList(AiTestPaperBo bo) {
        beforeRequest(bo);
        return R.ok(commonService.getEditionList(bo));
    }

    /**
     * 获取年级
     */
    @GetMapping("/getGradeList")
    public R<List<RemoteIdNameVo>> getGradeList(AiTestPaperBo bo) {
        beforeRequest(bo);
        return R.ok(commonService.getGradeList(bo));
    }


    /**
     * 获取学册
     */
    @GetMapping("/getGradeVolumeList")
    public R<List<RemoteIdNameVo>> getGradeVolumeList(AiTestPaperBo bo) {
        beforeRequest(bo);
        return R.ok(commonService.getGradeVolumeList(bo));
    }


    /**
     * 根据年级Id获取学段
     */
    @GetMapping("/getPhase")
    public R<List<Integer>> getPhase(AiTestPaperBo bo) {
        return R.ok(commonService.getPhase(bo));
    }


    /**
     * 根据学段获取对应的科目
     */
    @GetMapping("/getSubject")
    public R<List<RemoteIdNameVo>> getSubject(AiTestPaperBo bo) {
        beforeRequest(bo);
        return R.ok(commonService.getSubject(bo));
    }

    /**
     * 根据学段获取对应的科目
     */
    @GetMapping("/getSubjectPhase")
    public R<List<RemotePhaseSubjectVo>> getSubjectPhase(RemotePhaseSubjectBo bo) {
        return R.ok(iStudentExtPaperService.getSubjectPhase(bo));
    }

    /**
     * 获取同步卷
     */
    @GetMapping("/getSync")
    public R<List<RemoteExtPaperVo>> getSync(AiTestPaperBo bo) {
        return getPaperReturnR(bo, PaperTypeConstant.SYNC_PAPER_TYPE);
    }

    /**
     * 获取期中卷
     */
    @GetMapping("/getInterim")
    public R<List<RemoteExtPaperVo>> getMid(AiTestPaperBo bo) {
        return getPaperReturnR(bo, PaperTypeConstant.INTERIM_PAPER_TYPE);
    }

    /**
     * 获取期末卷
     */
    @GetMapping("/getFinal")
    public R<List<RemoteExtPaperVo>> getFinal(AiTestPaperBo bo) {
        return getPaperReturnR(bo, PaperTypeConstant.FINAL_PAPER_TYPE);
    }


    @GetMapping("/getSyncAndInterimAndFinal")
    public R<StudentEtxPaperSyncVo> getSyncAndInterimAndFinal(AiTestPaperBo bo) {
        StudentEtxPaperSyncVo studentEtxPaperSyncVo = new StudentEtxPaperSyncVo();
        studentEtxPaperSyncVo.setSyncList(getRemoteExtPaperVos(bo, PaperTypeConstant.SYNC_PAPER_TYPE));
        studentEtxPaperSyncVo.setInterimList(getRemoteExtPaperVos(bo, PaperTypeConstant.INTERIM_PAPER_TYPE));
        studentEtxPaperSyncVo.setFinalList(getRemoteExtPaperVos(bo, PaperTypeConstant.FINAL_PAPER_TYPE));
        return R.ok(studentEtxPaperSyncVo);
    }

    /**
     * 获取试卷题目
     */
    @GetMapping("/getPaperQuestions")
    public R<List<RemoteQuestionSimpleVo>> getPaperQuestions(@RequestParam Integer paperId) {
        RemotePaperQuestionBo remotePaperQuestionBo = new RemotePaperQuestionBo();
        remotePaperQuestionBo.setPaperId(paperId);
        List<RemoteQuestionSimpleVo> paperQuestions = remoteQuestionService.getPaperQuestions(remotePaperQuestionBo);

        return R.ok(paperQuestions);
    }

    private R<List<RemoteExtPaperVo>> getPaperReturnR(AiTestPaperBo bo, Integer finalPaperType) {
        List<RemoteExtPaperVo> remoteExtPaperVos = getRemoteExtPaperVos(bo, finalPaperType);
        return R.ok(remoteExtPaperVos);
    }

    private List<RemoteExtPaperVo> getRemoteExtPaperVos(AiTestPaperBo bo, Integer finalPaperType) {
        beforeRequest(bo, Collections.singletonList(finalPaperType));
        return commonService.listPaper(bo);
    }

    private void beforeRequest(AiTestPaperBo bo) {
        beforeRequest(bo, List.of(PaperTypeConstant.SYNC_PAPER_TYPE,
            PaperTypeConstant.INTERIM_PAPER_TYPE,
            PaperTypeConstant.FINAL_PAPER_TYPE));
    }


    private void beforeRequest(AiTestPaperBo bo, List<Integer> integerList) {
        bo.setStatus(1);
        bo.setTypeIds(integerList);
    }


}
