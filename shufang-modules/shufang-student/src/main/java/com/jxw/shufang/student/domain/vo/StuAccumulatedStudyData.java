package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 会员累计学习数据
 *
 *
 * @date 2024-02-29
 */
@Data
public class StuAccumulatedStudyData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 累计学习天数
     */
    private Long accumulateStudyDays;

    /**
     * 累计学习时长（单位秒）
     */

    private Long accumulateStudyTime;

    /**
     * 累计学习课程节数
     */
    private Long accumulateStudyChapterCount;
}
