package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.PaperCollection;

import java.io.Serial;
import java.io.Serializable;


/**
 * 试卷收藏视图对象 paper_collection
 *
 *
 * @date 2024-05-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PaperCollection.class)
public class PaperCollectionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收藏ID
     */
    @ExcelProperty(value = "收藏ID")
    private Long collectionId;

    /**
     * 试卷id
     */
    @ExcelProperty(value = "试卷id")
    private Long paperId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;


}
