package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;
import com.jxw.shufang.student.service.IApplyCorrectionRecordService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 申请批改记录
 * 前端访问路由地址为:/student/management/applyCorrectionRecord
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/applyCorrectionRecord")
public class ApplyCorrectionRecordController extends BaseController {

    private final IApplyCorrectionRecordService applyCorrectionRecordService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    /**
     * 查询申请批改待审核的数量
     */
    @GetMapping("/countWaitAudit")
    public R<Long> countWaitAudit(ApplyCorrectionRecordBo bo) {
        //bo.setApplyType("2");//默认查测验的
        bo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        return R.ok(applyCorrectionRecordService.count(bo));
    }

    /**
     * 查询申请批改记录列表
     */
    @SaCheckPermission("student:applyCorrectionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<ApplyCorrectionRecordVo> list(ApplyCorrectionRecordBo bo, PageQuery pageQuery) {
        //bo.setApplyType("2");//默认查测验的
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        return applyCorrectionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出申请批改记录列表
     */
    @SaCheckPermission("student:applyCorrectionRecord:export")
    @Log(title = "申请批改记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ApplyCorrectionRecordBo bo, HttpServletResponse response) {

        bo.setApplyType("2");//默认查测验的

        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");

        List<ApplyCorrectionRecordVo> list = applyCorrectionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "申请批改记录", ApplyCorrectionRecordVo.class, response);
    }

    /**
     * 获取申请批改记录详细信息
     *
     * @param applyCorrectionRecordId 主键
     */
    @SaCheckPermission("student:applyCorrectionRecord:query")
    @GetMapping("/{applyCorrectionRecordId}")
    public R<ApplyCorrectionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long applyCorrectionRecordId) {
        return R.ok(applyCorrectionRecordService.queryById(applyCorrectionRecordId));
    }

    /**
     * 新增申请批改记录
     */
    @SaCheckPermission("student:applyCorrectionRecord:add")
    @Log(title = "申请批改记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ApplyCorrectionRecordBo bo) {
        return toAjax(applyCorrectionRecordService.insertByBo(bo));
    }

//    /**
//     * 修改申请批改记录
//     */
//    @SaCheckPermission("student:applyCorrectionRecord:edit")
//    @Log(title = "申请批改记录", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
//    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ApplyCorrectionRecordBo bo) {
//        return toAjax(applyCorrectionRecordService.updateByBo(bo));
//    }

    /**
     * 修改申请批改状态
     * @param applyCorrectionRecordId 申请批改记录id
     * @param applyResult 申请结果（1允许 2拒绝 0待审核）
     */
    @SaCheckPermission("student:applyCorrectionRecord:edit")
    @Log(title = "修改申请批改状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@NotNull(message = "申请批改记录id不能为空")Long applyCorrectionRecordId,
                                @NotBlank(message = "申请结果不能为空")String applyResult) {
        if (!applyResult.equals("1") && !applyResult.equals("2")) {
            return R.fail("申请结果只能为同意或拒绝");
        }
        return toAjax(applyCorrectionRecordService.updateApplyResult(applyCorrectionRecordId, applyResult));
    }


    /**
     * 删除申请批改记录
     *
     * @param applyCorrectionRecordIds 主键串
     */
    @SaCheckPermission("student:applyCorrectionRecord:remove")
    @Log(title = "申请批改记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{applyCorrectionRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] applyCorrectionRecordIds) {
        return toAjax(applyCorrectionRecordService.deleteWithValidByIds(List.of(applyCorrectionRecordIds), true));
    }
}
