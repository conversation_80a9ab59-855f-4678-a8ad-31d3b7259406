package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudentConsultantRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）视图对象 student_consultant_record
 *
 *
 * @date 2024-02-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentConsultantRecord.class)
public class StudentConsultantRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员顾问记录id
     */
    @ExcelProperty(value = "会员顾问记录id")
    private Long studentConsultantRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 会员顾问id
     */
    @ExcelProperty(value = "会员顾问id")
    private Long studentConsultantId;

    /**
     * 记录说明
     */
    @ExcelProperty(value = "记录说明")
    private String recordRemark;

    /**
     * 会员顾问信息
     */
    private RemoteStaffVo staff;

    private Date createTime;


}
