package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.TestRecord;
import com.jxw.shufang.student.domain.bo.TestRecordBo;
import com.jxw.shufang.student.domain.vo.TestRecordVo;
import com.jxw.shufang.student.mapper.TestRecordMapper;
import com.jxw.shufang.student.service.ITestRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 测验记录Service业务层处理
 *
 *
 * @date 2024-05-09
 */
@RequiredArgsConstructor
@Service
public class TestRecordServiceImpl implements ITestRecordService, BaseService {

    private final TestRecordMapper baseMapper;

    /**
     * 查询测验记录
     */
    @Override
    public TestRecordVo queryById(Long testRecordId){
        return baseMapper.selectVoById(testRecordId);
    }

    /**
     * 查询测验记录列表
     */
    @Override
    public TableDataInfo<TestRecordVo> queryPageList(TestRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TestRecord> lqw = buildQueryWrapper(bo);
        Page<TestRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询测验记录列表
     */
    @Override
    public List<TestRecordVo> queryList(TestRecordBo bo) {
        LambdaQueryWrapper<TestRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TestRecord> buildQueryWrapper(TestRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TestRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudyPlanningRecordId() != null, TestRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(bo.getQuestionId() != null, TestRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getStudentId() != null, TestRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseResourceId() != null, TestRecord::getCourseResourceId, bo.getCourseResourceId());
        lqw.eq(bo.getResourceContent() != null, TestRecord::getResourceContent, bo.getResourceContent());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), TestRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), TestRecord::getAnswerResult, bo.getAnswerResult());
        lqw.eq(bo.getAnswerImg() != null, TestRecord::getAnswerImg, bo.getAnswerImg());
        return lqw;
    }

    /**
     * 新增测验记录
     */
    @Override
    public Boolean insertByBo(TestRecordBo bo) {
        TestRecord add = MapstructUtils.convert(bo, TestRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTestRecordId(add.getTestRecordId());
        }
        return flag;
    }

    /**
     * 修改测验记录
     */
    @Override
    public Boolean updateByBo(TestRecordBo bo) {
        TestRecord update = MapstructUtils.convert(bo, TestRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TestRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除测验记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<TestRecordBo> convert) {
        List<TestRecord> testRecordList = MapstructUtils.convert(convert, TestRecord.class);
        return baseMapper.insertBatch(testRecordList);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
