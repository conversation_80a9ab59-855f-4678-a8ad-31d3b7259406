package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.AllowOwnCorrection;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 允许自主批改视图对象 allow_own_correction
 *
 *
 * @date 2024-05-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AllowOwnCorrection.class)
public class AllowOwnCorrectionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 允许自主批改id
     */
    @ExcelProperty(value = "允许自主批改id")
    private Long allowOwnCorrectionId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 应用类型，1学习规划  2ai学习
     */
    private String type;

    /**
     * 允许类型，1练习  2测试
     */
    private String allowType;

    private StudentVo student;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建人系统信息
     */
    private RemoteUserVo createByUser;

}
