package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.AiWrongQuestionRecord;

import java.io.Serial;
import java.io.Serializable;


/**
 * Ai学习错题记录视图对象 ai_wrong_question_record
 *
 *
 * @date 2024-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiWrongQuestionRecord.class)
public class AiWrongQuestionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ai学习错题id
     */
    @ExcelProperty(value = "ai学习错题id")
    private Long aiWrongQuestionRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 课程ID
     */
    @ExcelProperty(value = "课程ID")
    private Long courseId;

    /**
     * 问题ID
     */
    @ExcelProperty(value = "问题ID")
    private Long questionId;

    /**
     * 题目序号
     */
    @ExcelProperty(value = "题目序号")
    private String questionNo;

    /**
     * 来源类型（1测试 2练习）
     */
    @ExcelProperty(value = "来源类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=测试,2=练习")
    private String sourceType;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    @ExcelProperty(value = "作答结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如全错,半=错")
    private String answerResult;


}
