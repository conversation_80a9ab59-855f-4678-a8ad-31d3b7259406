package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentAiCourseRecordInfo;

/**
 * 记录分配课程业务对象 student_ai_course_record_info
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentAiCourseRecordInfo.class, reverseConvertGenerate = false)
public class StudentAiCourseRecordInfoBo extends BaseEntity {

    /**
     * 记录表分配课程id
     */
    @NotNull(message = "记录表分配课程id不能为空", groups = { EditGroup.class })
    private Long studentAiCourseRecordInfoId;

    /**
     * 会员AI课程分配记录表id
     */
    @NotNull(message = "会员AI课程分配记录表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentAiCourseRecordId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;


    /**
     * 是否包含课次信息
     */
    private Boolean withCourseInfo;
}
