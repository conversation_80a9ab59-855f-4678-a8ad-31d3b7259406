package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.PaperCollectionBo;
import com.jxw.shufang.student.domain.vo.PaperCollectionVo;
import com.jxw.shufang.student.domain.vo.PaperVo;

import java.util.Collection;
import java.util.List;

/**
 * 试卷收藏Service接口
 *
 *
 * @date 2024-05-14
 */
public interface IPaperCollectionService {

    /**
     * 查询试卷收藏
     */
    PaperCollectionVo queryById(Long collectionId);

    /**
     * 查询试卷收藏列表
     */
    TableDataInfo<PaperCollectionVo> queryPageList(PaperCollectionBo bo, PageQuery pageQuery);

    /**
     * 查询试卷收藏列表
     */
    List<PaperCollectionVo> queryList(PaperCollectionBo bo);

    /**
     * 新增试卷收藏
     */
    Boolean insertByBo(PaperCollectionBo bo);

    /**
     * 修改试卷收藏
     */
    Boolean updateByBo(PaperCollectionBo bo);

    /**
     * 校验并批量删除试卷收藏信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 把试卷收藏信息放入列表
     */
    void putPaperCollection(List<PaperVo> paperList,Long studentId);

    /**
     * 根据学生id查询收藏试卷id列表
     *
     * @param studentId 学生id
     *
     * @date 2024/05/20 04:13:56
     */
    List<Long> queryCollectionPaperIdList(Long studentId);

    /**
     * 收藏试卷，如果试卷已经被收藏了，则表现为取消收藏
     *
     * @param paperId 纸张id
     * @param studentId 学生id
     *
     * @date 2024/05/22 12:18:14
     */
    void collectPaper(Long paperId,Long studentId);
}
