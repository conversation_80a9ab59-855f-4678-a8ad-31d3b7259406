package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.MessageBo;
import com.jxw.shufang.student.domain.vo.MessageGroupVo;
import com.jxw.shufang.student.domain.vo.MessageVo;

import java.util.Collection;
import java.util.List;

/**
 * 消息Service接口
 *
 *
 * @date 2024-06-15
 */
public interface IMessageService {

    /**
     * 查询消息
     */
    MessageVo queryById(Long messageId);

    /**
     * 查询消息列表
     */
    TableDataInfo<MessageVo> queryPageList(MessageBo bo, PageQuery pageQuery);

    /**
     * 查询消息列表
     */
    List<MessageVo> queryList(MessageBo bo);

    /**
     * 新增消息，如果返回的结果字符串不为空，表示存在敏感词，或者插入失败
     * 返回具体的错误
     */
    String insertByBo(MessageBo bo);

    /**
     * 修改消息
     */
    Boolean updateByBo(MessageBo bo);

    /**
     * 校验并批量删除消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<MessageVo> stuMessageInfoPage(MessageBo bo, PageQuery pageQuery);

    List<MessageVo> staffShowMessageList(MessageBo bo);

    /**
     * 查询消息分组列表（返回MessageGroupVo的，主要是为了兼容平板端）
     * @param messageBo
     * @param pageQuery
     * @return
     */
    TableDataInfo<MessageGroupVo> messageGroupPage(MessageBo messageBo, PageQuery pageQuery);

    /**
     * 查询未读消息数量
     * @param id
     * @return
     */
    Long unreadCount(Long id,String sendUserType);

    /**
     * 通过消息id列表，把消息置为已读，并通过发送人类型同时过滤
     * @param ids
     * @return
     */
    Boolean readMessages(List<Long> ids, String sendUserType);

    Boolean readMessagesOfStudent(Long messageStudentId, String sendUserType);
}
