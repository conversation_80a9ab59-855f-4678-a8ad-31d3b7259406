package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentAiCourseRecordInfo;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordInfoBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordInfoVo;
import com.jxw.shufang.student.mapper.StudentAiCourseRecordInfoMapper;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.IStudentAiCourseRecordInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 记录分配课程Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class StudentAiCourseRecordInfoServiceImpl implements IStudentAiCourseRecordInfoService, BaseService {

    private final StudentAiCourseRecordInfoMapper baseMapper;

    private final ICourseService courseService;

    /**
     * 查询记录分配课程
     */
    @Override
    public StudentAiCourseRecordInfoVo queryById(Long studentAiCourseRecordInfoId){
        return baseMapper.selectVoById(studentAiCourseRecordInfoId);
    }

    /**
     * 查询记录分配课程列表
     */
    @Override
    public TableDataInfo<StudentAiCourseRecordInfoVo> queryPageList(StudentAiCourseRecordInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentAiCourseRecordInfo> lqw = buildQueryWrapper(bo);
        Page<StudentAiCourseRecordInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询记录分配课程列表
     */
    @Override
    public List<StudentAiCourseRecordInfoVo> queryList(StudentAiCourseRecordInfoBo bo) {
        LambdaQueryWrapper<StudentAiCourseRecordInfo> lqw = buildQueryWrapper(bo);
        List<StudentAiCourseRecordInfoVo> studentAiCourseRecordInfoVoList =  baseMapper.selectVoList(lqw);
        if(Boolean.TRUE.equals(bo.getWithCourseInfo())){
            putCourseInfo(studentAiCourseRecordInfoVoList);
        }
        return studentAiCourseRecordInfoVoList;
    }


    public void putCourseInfo(List<StudentAiCourseRecordInfoVo> recordList) {
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        List<Long> courseIdList = recordList.stream().map(StudentAiCourseRecordInfoVo::getCourseId).distinct().collect(Collectors.toList());
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            return;
        }
        Map<Long, CourseVo> courseMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, vo -> vo));
        recordList.forEach(record -> {
            CourseVo courseVo = courseMap.get(record.getCourseId());
            record.setCourse(courseVo);
        });
    }

    private LambdaQueryWrapper<StudentAiCourseRecordInfo> buildQueryWrapper(StudentAiCourseRecordInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentAiCourseRecordInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentAiCourseRecordId() != null, StudentAiCourseRecordInfo::getStudentAiCourseRecordId, bo.getStudentAiCourseRecordId());
        lqw.eq(bo.getCourseId() != null, StudentAiCourseRecordInfo::getCourseId, bo.getCourseId());
        return lqw;
    }

    /**
     * 新增记录分配课程
     */
    @Override
    public Boolean insertByBo(StudentAiCourseRecordInfoBo bo) {
        StudentAiCourseRecordInfo add = MapstructUtils.convert(bo, StudentAiCourseRecordInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentAiCourseRecordInfoId(add.getStudentAiCourseRecordInfoId());
        }
        return flag;
    }

    /**
     * 修改记录分配课程
     */
    @Override
    public Boolean updateByBo(StudentAiCourseRecordInfoBo bo) {
        StudentAiCourseRecordInfo update = MapstructUtils.convert(bo, StudentAiCourseRecordInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentAiCourseRecordInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除记录分配课程
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
