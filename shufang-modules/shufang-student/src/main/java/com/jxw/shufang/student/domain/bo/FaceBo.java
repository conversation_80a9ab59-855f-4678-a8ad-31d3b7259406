package com.jxw.shufang.student.domain.bo;

import lombok.Data;


/**
 * 人脸识别考勤机 https://yun.fuyingkeji.com/
 *
 */
@Data
public class FaceBo {
    private String healthResult;
    /**
     * 如：1001，每一个id唯一，
     * 由设备生成
     */
    private String msgType;
    /**
     * IdCard ---对应云平台身份证
     */
    private String idCard;
    private String passTime;
    private String offLineRecordNum;
    private String snapType;
    /**
     * 人员编号
     */
    private String idNumber;
    /**
     * 0：比对成功
     * 1：比对失败
     * 2：不在通行时间内
     * 3：不在有效期内
     * 4：未授权
     */
    private String compareResult;
    private String normalNumber;
    private String temperature;
    private String company;
    private String inOutMode;
    private String sex;
    private String msgID;
    private String healthType;
    /**
     * 设备编号
     */
    private String deviceNo;
    private String faceMaskResult;
    private String priority;
    private String version;
    /**
     * 云服务平台不下发手机号
     */
    private String phone;
    private String token;
    private String comparePoint;
    private String name;
    private String retransmissionFlag;
    private String temperatureResult;
    private String validDateStart;
    private String validDateEnd;
}
