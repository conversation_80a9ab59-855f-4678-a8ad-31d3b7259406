package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.student.domain.StudentExpire;
import com.jxw.shufang.student.domain.bo.StudentExpireBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudentExpireVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;
import com.jxw.shufang.student.mapper.StudentExpireMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）Service业务层处理
 *
 *
 * @date 2024-03-08
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentExpireServiceImpl implements IStudentExpireService, BaseService {

    private final StudentExpireMapper baseMapper;
    private final IStudentService studentService;
    private final IStudyRecordService studyRecordService;
    private final IStudyPlanningService studyPlanningService;
    private final IStudentConsultantRecordService studentConsultantRecordService;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteOrderService remoteOrderService;


    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）
     */
    @Override
    public StudentExpireVo queryById(Long studentExpireId) {
        return baseMapper.selectVoById(studentExpireId);
    }

    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    @Override
    public TableDataInfo<StudentExpireVo> queryPageList(StudentExpireBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<StudentExpire> lqw = buildQueryWrapper(bo);
        Page<StudentExpireVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentOrderInfo())) {
            putStudentOrderInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithNotFinishPlanCount())){
            putNotFinishPlanCount(result.getRecords());
        }
        putExpireInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void putStudentOrderInfo(List<StudentExpireVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIds = records.stream().map(StudentExpireVo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
        remoteOrderBo.setStudentIdList(studentIds);
        List<RemoteOrderVo> studentOrderList = remoteOrderService.selectStudentLastOrder(remoteOrderBo);
        if (CollUtil.isEmpty(studentOrderList)) {
            return;
        }
        Map<Long, RemoteOrderVo> map = StreamUtils.toMap(studentOrderList, RemoteOrderVo::getStudentId, Function.identity());
        for (StudentExpireVo record : records) {
            RemoteOrderVo remoteOrderVo = map.get(record.getStudentId());
            if (remoteOrderVo == null) {
                continue;
            }
            List<RemoteOrderProductInfoVo> productInfoList = remoteOrderVo.getOrderProductInfoList();
            if (CollUtil.isNotEmpty(productInfoList)) {
                String collect = productInfoList.stream().map(RemoteOrderProductInfoVo::getProductName).filter(Objects::nonNull).collect(Collectors.joining(","));
                record.setLastOrderProductNames(collect);
            }
        }
    }

    private void putBranchInfo(List<StudentExpireVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentList = records.stream().map(StudentExpireVo::getStudent).filter(Objects::nonNull).filter(studentVo -> studentVo.getBranchId() != null).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentList)) {
            return;
        }
        List<Long> branchIds = studentList.stream().map(StudentVo::getBranchId).collect(Collectors.toList());
        branchIds.remove(null);
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(branchIds);
        List<RemoteBranchVo> branchList = remoteBranchService.selectBranchList(remoteBranchBo);
        Map<Long, RemoteBranchVo> branchMap = branchList.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, Function.identity()));
        studentList.forEach(studentVo -> {
            RemoteBranchVo remoteBranchVo = branchMap.get(studentVo.getBranchId());
            studentVo.setBranch(remoteBranchVo);
        });
    }


    private void putExpireInfo(List<StudentExpireVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        //如果在studentExpire表里，那么他一定是student表的expireTime也超过了的，我们直接拿student表的expireTime去计算

        Date now = new Date();
        records.forEach(record -> {
            StudentVo student = record.getStudent();
            if (student == null || student.getExpireTime() == null) {
                return;
            }
            //计算出过期了几天
            long between = DateUtil.between(student.getExpireTime(), now, DateUnit.DAY, true);
            record.setExpireDays(between);
            record.setExpireTime(student.getExpireTime());
        });

    }


    private void putNotFinishPlanCount(List<StudentExpireVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIds = records.stream().map(StudentExpireVo::getStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        //查询已经存在的planidList
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudentIdList(studentIds);
        List<Long> studyPlanningRecordIdList = studyRecordService.queryStudyPlanningRecordIdList(studyRecordBo);

        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setNotInStudyPlanningRecordIdList(studyPlanningRecordIdList);
        studyPlanningBo.setStudentIdList(studentIds);
        List<StudyPlanningVo> studyPlanningVos = studyPlanningService.queryPlanAndRecordList(studyPlanningBo);
        if (CollUtil.isEmpty(studyPlanningVos)) {
            return;
        }
        Map<Long, List<StudyPlanningVo>> map = studyPlanningVos.stream().collect(Collectors.groupingBy(StudyPlanningVo::getStudentId));
        for (StudentExpireVo record : records) {
            Long studentId = record.getStudentId();
            List<StudyPlanningVo> studyPlanningVoList = map.get(studentId);
            if (CollUtil.isEmpty(studyPlanningVoList)) {
                continue;
            }
            long count = studyPlanningVoList.stream().map(StudyPlanningVo::getStudyPlanningDate).distinct().count();
            record.setNotFinishPlanCount(count);
        }
    }


    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    @Override
    public List<StudentExpireVo> queryList(StudentExpireBo bo) {
        LambdaQueryWrapper<StudentExpire> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentExpire> buildLambdaQueryWrapper(StudentExpireBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentExpire> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentExpire::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsShow()), StudentExpire::getIsShow, bo.getIsShow());
        return lqw;
    }

    private QueryWrapper<StudentExpire> buildQueryWrapper(StudentExpireBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<StudentExpire> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsShow()), "t.is_show", bo.getIsShow());
        lqw.like(StringUtils.isNotBlank(bo.getStudentName()), "s.student_name", bo.getStudentName());
        lqw.eq(bo.getBranchId() != null, "s.branch_id", bo.getBranchId());
        if (StringUtils.isNotBlank(bo.getIsShow())) {
            lqw.eq("t.is_show", bo.getIsShow());
        } else {
            lqw.eq("t.is_show", "0");
        }
        return lqw;
    }


    /**
     * 新增会员过期（用于 会员过期列 的展示和数据操作）
     */
    @Override
    public Boolean insertByBo(StudentExpireBo bo) {
        StudentExpire add = MapstructUtils.convert(bo, StudentExpire.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentExpireId(add.getStudentExpireId());
        }
        return flag;
    }

    @Override
    public Boolean insertBatchByBo(List<StudentExpireBo> boList) {
        List<StudentExpire> convert = MapstructUtils.convert(boList, StudentExpire.class);
        return baseMapper.insertBatch(convert);
    }

    /**
     * 修改会员过期（用于 会员过期列 的展示和数据操作）
     */
    @Override
    public Boolean updateByBo(StudentExpireBo bo) {
        StudentExpire update = MapstructUtils.convert(bo, StudentExpire.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentExpire entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员过期（用于 会员过期列 的展示和数据操作）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        List<StudentExpire> studentExpires = new ArrayList<>();
        for (Long id : ids) {
            StudentExpire studentExpire = new StudentExpire();
            studentExpire.setStudentExpireId(id);
            studentExpire.setIsShow("1");
            studentExpires.add(studentExpire);
        }
        return baseMapper.updateBatchById(studentExpires);
    }

    @Override
    @CacheEvict(value = "studentExpire", allEntries = true)
    public Boolean removeStudentExpireRecord(Long studentId) {
        LambdaQueryWrapper<StudentExpire> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudentExpire::getStudentId, studentId);
        return baseMapper.delete(lqw) > 0;
    }


    @Cacheable(value = "studentExpire", key = "#studentExpireId", condition = "#studentExpireId != null")
    @Override
    public StudentExpire queryStudentExpireById(Long studentExpireId) {
        return baseMapper.selectById(studentExpireId);
    }


    @CacheEvict(value = "studentExpire", allEntries = true)
    public void cleanCache() {
        log.info("===========studentExpireService cleanCache===========");
    }

    @Override
    public List<Long> filterExpireStudentId(List<Long> studentIdList) {
        if (CollUtil.isEmpty(studentIdList)) {
            return List.of();
        }
        List<StudentExpire> studentExpireList = baseMapper.selectList(Wrappers.<StudentExpire>lambdaQuery()
            .in(StudentExpire::getStudentId, studentIdList)
            .select(StudentExpire::getStudentId));
        if (CollUtil.isEmpty(studentExpireList)) {
            return List.of();
        }
        return studentExpireList.stream().map(StudentExpire::getStudentId).collect(Collectors.toList());

    }

    @Override
    public void init() {
        IStudentExpireService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentExpireService init===========");
        LambdaQueryWrapper<StudentExpire> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentExpire::getStudentExpireId);
        List<StudentExpire> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========studentExpireService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentExpireById(item.getStudentExpireId());
        });
        log.info("===========studentExpireService init end===========");
    }


    private void handleQueryParam(StudentExpireBo record) {
        if (record.getStudentId()!=null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null ) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }

        }
    }


}
