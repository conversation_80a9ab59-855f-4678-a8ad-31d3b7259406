package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.ApplyCorrectionRecord;

import java.util.Date;
import java.util.List;

/**
 * 申请批改记录业务对象 apply_correction_record
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ApplyCorrectionRecord.class, reverseConvertGenerate = false)
public class ApplyCorrectionRecordBo extends BaseEntity {

    /**
     * 申请批改记录id
     */
    @NotNull(message = "申请批改记录id不能为空", groups = { EditGroup.class })
    private Long applyCorrectionRecordId;

    /**
     * 会员id
     */
    //@NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 学习规划记录id
     */
    @NotNull(message = "学习规划记录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 申请批改类型（1练习  2测试）
     */
    @NotBlank(message = "申请批改类型（1练习  2测试）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyType;

    /**
     * 申请结果（1允许 2拒绝 0待审核）
     */
    //@NotBlank(message = "申请结果（1允许 2拒绝）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String applyResult;

    /**
     * 是否允许自主批改（1允许自主批改 2不允许自主批改）
     */
    private String allowOwnType;

    private List<Long> studentIdList;

    /**
     * 携带是否允许自主批改信息
     */
    private Boolean withAllowOwnCorrection;

    /**
     * 携带学生系统用户信息
     */
    private Boolean withStudentUserInfo;

    /**
     * 携带会员顾问用户信息
     */
    private Boolean withConsultantInfo;

    /**
     * 携带测试或练习的批改状态
     */
    private Boolean withTPState;


    private String nameWithPhone;

    /**
     * 时间段 如 2024-01-01 03:00:00 至 2024-01-02 03:00:00
     */
    private String timeLimit;

    /**
     * 最新一次重新提交时间
     */
    private Date applyTime;

}
