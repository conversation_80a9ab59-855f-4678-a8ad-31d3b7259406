package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PrintRecordBo;
import com.jxw.shufang.student.domain.vo.PrintRecordInfoVo;
import com.jxw.shufang.student.domain.vo.PrintRecordVo;
import com.jxw.shufang.student.service.IPrintRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 打印记录
 * 前端访问路由地址为:/student/printRecord
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/printRecord")
public class PrintRecordController extends BaseController {

    private final IPrintRecordService printRecordService;

    /**
     * 查询打印记录列表
     */
    @SaCheckPermission("student:printRecord:list")
    @GetMapping("/list")
    public TableDataInfo<PrintRecordVo> list(PrintRecordBo bo, PageQuery pageQuery) {
        if (LoginHelper.isBranchUser()){
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return printRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出打印记录列表
     */
    @SaCheckPermission("student:printRecord:export")
    @Log(title = "打印记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PrintRecordBo bo, HttpServletResponse response) {
        List<PrintRecordVo> list = printRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "打印记录", PrintRecordVo.class, response);
    }

//    /**
//     * 获取打印记录详细信息
//     *
//     * @param printRecordId 主键
//     */
//    @SaCheckPermission("student:printRecord:query")
//    @GetMapping("/{printRecordId}")
//    public R<PrintRecordVo> getInfo(@NotNull(message = "主键不能为空")
//                                     @PathVariable Long printRecordId) {
//        return R.ok(printRecordService.queryById(printRecordId));
//    }

    /**
     * 获取打印记录详细信息
     *
     * @param printRecordId 主键
     */
    @SaCheckPermission("student:printRecord:query")
    @GetMapping("/queryInfo/{printRecordId}")
    public R<List<PrintRecordInfoVo>> queryInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long printRecordId) {
        return R.ok(printRecordService.queryInfo(printRecordId));
    }

    /**
     * 新增打印记录
     */
    @SaCheckPermission("student:printRecord:add")
    @Log(title = "打印记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrintRecordBo bo) {
        return toAjax(printRecordService.insertByBo(bo));
    }

    /**
     * 修改打印记录
     */
    @SaCheckPermission("student:printRecord:edit")
    @Log(title = "打印记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PrintRecordBo bo) {
        return toAjax(printRecordService.updateByBo(bo));
    }

    /**
     * 删除打印记录
     *
     * @param printRecordIds 主键串
     */
    @SaCheckPermission("student:printRecord:remove")
    @Log(title = "打印记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{printRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] printRecordIds) {
        return toAjax(printRecordService.deleteWithValidByIds(List.of(printRecordIds), true));
    }
}
