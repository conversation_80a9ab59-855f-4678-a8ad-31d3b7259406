package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.bo.NewStudyPlanningRecordBO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.INewStudyPlanningRecordService;
import com.jxw.shufang.system.api.RemoteConfigService;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.student.service.IStudyPlanningRecordService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 学习规划记录
 * 前端访问路由地址为:/student/management/studyPlanningRecord
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studyPlanningRecord")
public class StudyPlanningRecordController extends BaseController {

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IStudentService studentService;
    @DubboReference
    private RemoteConfigService remoteConfigService;

    private final INewStudyPlanningRecordService newStudyPlanningRecordService;

    /**
     * 查询门店数据 （学习及测验和练习 测验批改率 学习超过视频时长50%未完成测验 有X位学员学习计划不足）
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/getBranchData")
    public R<BranchDataVo> getBranchData(StudyPlanningRecordBo bo) {
        BranchDataVo branchDataVo = new BranchDataVo();
        branchDataVo.setStudyNum(0);
        branchDataVo.setTestCorrectProportion("0%");
        branchDataVo.setNotFinishTestNum(0);
        branchDataVo.setNotScheduleNum(0);
        branchDataVo.setNotScheduleInfo("");

        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);

        bo.setWithTestCorrectionRecord(true);
        bo.setWithStudyRecord(true);

        TableDataInfo<StudyPlanningRecordVo> tableDataInfo = studyPlanningRecordService.queryStudyPlanningRecordPage(bo, pageQuery);
        if (null != tableDataInfo && null != tableDataInfo.getRows() && tableDataInfo.getRows().size() > 0) {
            List<StudyPlanningRecordVo> voList = tableDataInfo.getRows();

            HashSet<Long> studyNumSet = new HashSet<>();
            voList.parallelStream().forEach(vo -> {
                studyNumSet.add(vo.getStudentId());
            });
            branchDataVo.setStudyNum(studyNumSet.size());//学习及测验和练习

            AtomicReference<Integer> testCorrectionRecordNum = new AtomicReference<>(0);
            voList.parallelStream().forEach(vo -> {
                CorrectionRecordVo testCorrectionRecord = vo.getTestCorrectionRecord();
                if (null != testCorrectionRecord) {
                    testCorrectionRecordNum.getAndSet(testCorrectionRecordNum.get() + 1);
                }
            });
            branchDataVo.setTestCorrectProportion(new BigDecimal(testCorrectionRecordNum.get())
                .divide(new BigDecimal(voList.size()), 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)).toString() + "%");//测验批改率

            AtomicReference<Integer> notFinishTestNum = new AtomicReference<>(0);
            voList.parallelStream().forEach(vo -> {
                StudyRecordVo studyRecordVo = vo.getStudyRecord();
                RemoteVideoVo remoteVideoVo = vo.getVideo();
                if (null != studyRecordVo
                    && null != remoteVideoVo
                    && null != studyRecordVo.getStudyVideoTotalDuration()
                    && studyRecordVo.getStudyVideoTotalDuration() > 0
                    && null != remoteVideoVo.getDuration()
                    && remoteVideoVo.getDuration() > 0) {
                    if (new BigDecimal(studyRecordVo.getStudyVideoTotalDuration())
                        .divide(new BigDecimal(remoteVideoVo.getDuration()), 2, RoundingMode.DOWN)
                        .compareTo(new BigDecimal(0.5)) >= 0) {
                        CorrectionRecordVo testCorrectionRecord = vo.getTestCorrectionRecord();
                        if (null == testCorrectionRecord) {
                            notFinishTestNum.getAndSet(notFinishTestNum.get() + 1);
                        }
                    }
                }
            });
            branchDataVo.setNotFinishTestNum(notFinishTestNum.get());//学习超过视频时长50未完成测验
        }

        //剩余规划天数≦3天的未过期用户, 含体验会员
        List<StudentVo> studentVoList = studentService.queryList(new StudentBo());
        if (null != studentVoList && studentVoList.size() > 0) {

            StringBuilder notScheduleInfo = new StringBuilder("");
            AtomicReference<Integer> count = new AtomicReference<>(0);

            LocalDate today = LocalDate.now();
            studentVoList.parallelStream().forEach(vo -> {
                if (null != vo.getRemainingDay() && vo.getRemainingDay() <= 3 && vo.getRemainingDay() > 0) {
                    Boolean flag = false;
                    for (int i = 0; i < vo.getRemainingDay(); i++) {
                        LocalDate nextDay = today.plus(i, ChronoUnit.DAYS);
                        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                        studyPlanningRecordBo.setStudentId(vo.getStudentId());
                        studyPlanningRecordBo.setStudyPlanningDate(DateUtils.toDate(nextDay));
                        if (studyPlanningRecordService.queryList(studyPlanningRecordBo).size() > 0) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        count.getAndSet(count.get() + 1);
                        notScheduleInfo.append(vo.getNameWithPhone() + ",");
                    }
                }
            });

            branchDataVo.setNotScheduleNum(count.get());//有X位学员学习计划不足

            branchDataVo.setNotScheduleInfo(notScheduleInfo.toString());//有X位学员学习计划不足 文字 暂时只展示名字
        }

        return R.ok(branchDataVo);
    }


    /**
     * 查询学习规划记录分页列表
     */
    /**
     * todo 测试，即将废弃
     * @param bo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudyPlanningRecordVo> list(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return studyPlanningRecordService.queryStudyPlanningRecordPage(bo, pageQuery);
    }

    /**
     * 查询学习规划记录分页列表-新
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/listV2")
    public TableDataInfo<MergeStudyPlanningRecordVO> listV2(NewStudyPlanningRecordBO bo, PageQuery pageQuery) {
        if(null == bo.getStudyPlanningDate()){
            throw new IllegalArgumentException("学习规划日期不可为空");
        }
        return newStudyPlanningRecordService.queryMergeStudyPlanningRecordPage(bo, pageQuery);
    }

    /**
     * 查询学习规划记录分页列表 group by student
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/listV3")
    public TableDataInfo<StudyPlanningRecordDashboardVo> listV3(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return studyPlanningRecordService.queryStudyPlanningRecordPageV3(bo, pageQuery);
    }

    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/studyPlanningRecordList")
    public R<List<StudyPlanningRecordVo>> studyPlanningRecordList(StudyPlanningRecordBo bo) {
        return R.ok(studyPlanningRecordService.queryStudyPlanningRecordList(bo));
    }

    /**
     * 导出学习规划记录列表
     */
    @SaCheckPermission("student:studyPlanningRecord:export")
    @Log(title = "学习规划记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyPlanningRecordBo bo, HttpServletResponse response) {

        List<StudyPlanningRecordVo> list = studyPlanningRecordService.queryStudyPlanningRecordList(bo);
        List<StudyPlanningRecordExcelVo> voList = List.of();
        if (CollUtil.isNotEmpty(list)) {
            voList = list.stream().map(entity -> {
                StudyPlanningRecordExcelVo vo = new StudyPlanningRecordExcelVo();
                if (!ObjectUtils.isEmpty(entity.getStudyPlanningDate())) {
                    // 定义日期格式
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-M-d");
                    // 格式化日期
                    String formattedDate = dateFormat.format(entity.getStudyPlanningDate());
                    vo.setStudyDate(formattedDate);
                }

                RemoteStaffVo staff = entity.getStaff();
                if (!ObjectUtils.isEmpty(staff)) {
                    RemoteUserVo user = staff.getUser();
                    if (!ObjectUtils.isEmpty(user)) {
                        vo.setStaff(user.getNickName());
                    }
                }
                CourseVo course = entity.getCourse();
                if (!ObjectUtils.isEmpty(course)) {
                    String courseName = course.getCourseName();
                    CourseVo topmostCourse = course.getTopmostCourse();
                    if (ObjectUtils.isEmpty(topmostCourse)) {
                        vo.setStudyContent(courseName);
                    } else {
                        String topmostCourseCourseName = topmostCourse.getCourseName();
                        String separator = remoteConfigService.selectCourseDetailSeparator();
                        vo.setCourseDetail(topmostCourse.getCourseDetail());
                        vo.setStudyContent(courseName + separator + topmostCourseCourseName);
                    }
                }

                Date studyStartTime = entity.getStudyStartTime();
                Date studyEndTime = entity.getStudyEndTime();
                if (!ObjectUtils.isEmpty(studyStartTime) && !ObjectUtils.isEmpty(studyEndTime)) {
                    // 定义日期格式
                    SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");
                    // 格式化日期
                    String startTime = timeFormat.format(studyStartTime);
                    String endTime = timeFormat.format(studyEndTime);
                    // 拼接成所需的格式
                    String result = startTime + " - " + endTime;
                    vo.setStudyTime(result);
                }

                return vo;
            }).collect(Collectors.toList());
        }
        ExcelUtil.exportExcel(voList, "学习规划记录", StudyPlanningRecordExcelVo.class, response);
    }

    /**
     * 获取学习规划记录详细信息
     *
     * @param studyPlanningRecordId 主键
     */
    @SaCheckPermission("student:studyPlanningRecord:query")
    @GetMapping("/{studyPlanningRecordId}")
    public R<StudyPlanningRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                            @PathVariable Long studyPlanningRecordId) {
        return R.ok(studyPlanningRecordService.queryById(studyPlanningRecordId));
    }

    /**
     * 新增学习规划记录
     */
    @SaCheckPermission("student:studyPlanningRecord:add")
    @Log(title = "学习规划记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyPlanningRecordBo bo) {
        return toAjax(studyPlanningRecordService.insertByBo(bo));
    }

    /**
     * 修改学习规划记录
     */
    @SaCheckPermission("student:studyPlanningRecord:edit")
    @Log(title = "学习规划记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyPlanningRecordBo bo) {
        return toAjax(studyPlanningRecordService.updateByBo(bo));
    }

    /**
     * 删除学习规划记录
     *
     * @param studyPlanningRecordIds 主键串
     */
    @SaCheckPermission("student:studyPlanningRecord:remove")
    @Log(title = "学习规划记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyPlanningRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyPlanningRecordIds) {
        return toAjax(studyPlanningRecordService.deleteWithValidByIds(List.of(studyPlanningRecordIds), true));
    }

    /**
     * 查询学习规划记录列表
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/queryStudyPlanningRecordList")
    public R<List<StudyPlanningRecordVo>> queryStudyPlanningRecordList(StudyPlanningRecordBo bo) {
        return R.ok(studyPlanningRecordService.queryStudyPlanningRecordList(bo));
    }

    /**
     * 查询指定会员学习统计数据
     */
    @SaCheckPermission("student:studyPlanningRecord:query")
    @GetMapping("/queryStudyRecordStatistics")
    public R<StudyRecordStatisticsVo> queryStudyRecordStatistics(StudyPlanningRecordBo bo) {
        if (bo.getStudentId() == null) {
            return R.fail("会员id不能为空");
        }
        List<StudyRecordStatisticsVo> list = studyPlanningRecordService.queryStudyRecordStatisticsList(bo);
        return R.ok(CollUtil.getFirst(list));
    }


}
