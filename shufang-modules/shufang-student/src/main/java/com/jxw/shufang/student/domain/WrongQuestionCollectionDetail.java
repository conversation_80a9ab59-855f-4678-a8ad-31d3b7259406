package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 错题合集详情对象 wrong_question_collection_detail
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wrong_question_collection_detail")
public class WrongQuestionCollectionDetail extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题合集详情id
     */
    @TableId(value = "wrong_question_collection_detail_id")
    private Long wrongQuestionCollectionDetailId;

    /**
     * 错题合集id
     */
    private Long wrongQuestionCollectionId;

    /**
     * 问题id
     */
    private Long questionId;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    private String answerResult;

    private Long questionPid;

    private Integer sort;

}
