package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.WrongQuestionRecordNew;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WrongQuestionRecordNew.class, reverseConvertGenerate = false)
public class WrongQuestionRecordNewBo extends BaseEntity {
    /**
     * 错题id
     */
    private Long wrongQuestionRecordNewId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = {AddGroup.class})
    private Long studentId;

    /**
     * 题目ID
     */
    @NotNull(message = "题目id不能为空", groups = {AddGroup.class})
    private Long questionId;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 用户答案状态（对应字典值，如全错 半错）
     */
    private String userAnswerStatus;

    /**
     * 来源类型(对应字典值)
     */
    private String sourceType;

    /**
     * 来源ID（学测练为courseId,AI评测为paperId）
     */
    private String sourceId;

    private List<Long> questionIds;

    @NotNull(message = "题目id不能为空", groups = {AddGroup.class})
    private String reviseScreenshots;
}
