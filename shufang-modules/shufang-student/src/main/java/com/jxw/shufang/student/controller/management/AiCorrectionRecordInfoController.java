package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordInfoVo;
import com.jxw.shufang.student.service.IAiCorrectionRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ai批改记录详情
 * 前端访问路由地址为:/student/aiCorrectionRecordInfo
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiCorrectionRecordInfo")
public class AiCorrectionRecordInfoController extends BaseController {

    private final IAiCorrectionRecordInfoService aiCorrectionRecordInfoService;

    /**
     * 查询ai批改记录详情列表
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:list")
    @GetMapping("/list")
    public TableDataInfo<AiCorrectionRecordInfoVo> list(AiCorrectionRecordInfoBo bo, PageQuery pageQuery) {
        return aiCorrectionRecordInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai批改记录详情列表
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:export")
    @Log(title = "ai批改记录详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiCorrectionRecordInfoBo bo, HttpServletResponse response) {
        List<AiCorrectionRecordInfoVo> list = aiCorrectionRecordInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai批改记录详情", AiCorrectionRecordInfoVo.class, response);
    }

    /**
     * 获取ai批改记录详情详细信息
     *
     * @param aiCorrectionRecordInfoId 主键
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:query")
    @GetMapping("/{aiCorrectionRecordInfoId}")
    public R<AiCorrectionRecordInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiCorrectionRecordInfoId) {
        return R.ok(aiCorrectionRecordInfoService.queryById(aiCorrectionRecordInfoId));
    }

    /**
     * 新增ai批改记录详情
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:add")
    @Log(title = "ai批改记录详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiCorrectionRecordInfoBo bo) {
        return toAjax(aiCorrectionRecordInfoService.insertByBo(bo));
    }

    /**
     * 修改ai批改记录详情
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:edit")
    @Log(title = "ai批改记录详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiCorrectionRecordInfoBo bo) {
        return toAjax(aiCorrectionRecordInfoService.updateByBo(bo));
    }

    /**
     * 删除ai批改记录详情
     *
     * @param aiCorrectionRecordInfoIds 主键串
     */
    @SaCheckPermission("student:aiCorrectionRecordInfo:remove")
    @Log(title = "ai批改记录详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiCorrectionRecordInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiCorrectionRecordInfoIds) {
        return toAjax(aiCorrectionRecordInfoService.deleteWithValidByIds(List.of(aiCorrectionRecordInfoIds), true));
    }
}
