package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习规划模板详情对象 study_planning_template_info
 *
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_planning_template_info")
public class StudyPlanningTemplateInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划模板详情id
     */
    @TableId(value = "study_planning_template_info_id")
    private Long studyPlanningTemplateInfoId;

    /**
     * 学习规划模板id
     */
    private Long studyPlanningTemplateId;

    /**
     * 课程id（章节）
     */
    private Long courseId;

    /**
     * 课程名称(最顶层的课程id)
     */
    private Long topmostCourseId;

    /**
     * 学习开始时间
     */
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    private Date studyEndTime;


}
