package com.jxw.shufang.student.consumer;

import com.alibaba.fastjson.JSONObject;
import com.jxw.shufang.common.core.constant.MqGroupConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityBo;
import com.jxw.shufang.student.service.AttendanceDailyActivityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = MqTopicConstant.ATTENDANCE_DAILY_ACTIVITY_TOPIC,
    consumerGroup = MqGroupConstant.ATTENDANCE_DAILY_ACTIVITY_GROUP,
    messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.CONCURRENTLY)
public class AttendanceDailyActivityConsumer implements RocketMQListener<String> {

    private final AttendanceDailyActivityService attendanceDailyActivityService;

    @Override
    public void onMessage(String msg) {
        log.info("接收到会员每日打卡登录消息{}", msg);

        List<AttendanceDailyActivityBo> attendanceDailyActivityBoList = JSONObject.parseArray(msg, AttendanceDailyActivityBo.class);

        try {
            attendanceDailyActivityService.saveBatchAttendanceDailyActivity(attendanceDailyActivityBoList);
        } catch (Exception e) {
            log.error("会员每日打卡登录消息处理失败{}", msg, e);
        }
    }
}
