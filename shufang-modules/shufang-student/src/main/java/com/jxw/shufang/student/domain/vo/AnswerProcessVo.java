package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 数据统计-答题情况 answerProcess
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
public class AnswerProcessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 答题情况列表
     */
    private List<AnswerEntity> answerEntities;

    /**
     * 数据统计-答题情况-实体 AnswerEntity
     *
     * <AUTHOR>
     * @date 2024-05-13
     */
    @Data
    public static class AnswerEntity {

        /**
         * 科目，对应字典
         */
        private String affiliationSubject;

        /**
         * 进度
         */
        private Double process;
    }
}
