package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudyPlanningRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 学习规划记录视图对象 study_planning_record
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanningRecord.class)
public class StudyPlanningRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划记录id
     */
    @ExcelProperty(value = "学习规划记录id")
    private Long studyPlanningRecordId;

    /**
     * 学习规划id
     */
    @ExcelProperty(value = "学习规划id")
    private Long studyPlanningId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 学习开始时间
     */
    @ExcelProperty(value = "学习开始时间")
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    @ExcelProperty(value = "学习结束时间")
    private Date studyEndTime;

    /**
     * 学习时长
     */
    @ExcelProperty(value = "学习时长")
    private Long studyDuration;

    /**
     * 学习状态（0未开始 1进行中 2已完成）
     */
    @ExcelProperty(value = "学习状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未开始,1=进行中,2=已完成")
    private String studyStatus;

    /**
     * 学习记录状态（0正常 1覆盖 2删除 ）
     */
    @ExcelProperty(value = "学习记录状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=覆盖,2=删除")
    private String studyRecordStatus;

    /**
     * 学习规划下载状态（0未下载 1已下载）
     */
    private Integer downloadStatus;

    /**
     * 学习规划日期（非数据库字段）
     */
    private Date studyPlanningDate;

    /**
     * 课程信息
     */
    private CourseVo course;


    /**
     * 学习规划主体
     */
    private StudyPlanningVo studyPlanning;

    /**
     * 学习记录
     */
    private StudyRecordVo studyRecord;

    /**
     * 会员顾问
     */
    private RemoteStaffVo staff;

    /**
     * 考勤时间
     */
    private Date attendanceTime;

    /**
     * 机位
     */
    private RemoteBranchMachineSeatVo branchMachineSeat;


    private RemoteVideoVo video;


    /**
     * 反馈记录
     */
    private FeedbackRecordVo feedbackRecord;

    /**
     * 练习批改记录
     */
    private CorrectionRecordVo practiceCorrectionRecord;

    /**
     * 测试批改记录
     */
    private CorrectionRecordVo testCorrectionRecord;
    private CorrectionRecordVo previewCorrectionRecord;
    private CorrectionRecordVo speakCorrectionRecord;

    /**
     * 最后一次学习视频记录
     */
    private StudyVideoRecordVo lastStudyVideoRecord;

    /**
     * 会员信息
     */
    private StudentVo student;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 来店考勤记录
     */
    private AttendanceLogStudentEzkecoVo inStoreAttendanceLog;


    /**
     * 离店考勤记录
     */
    private AttendanceLogStudentEzkecoVo outStoreAttendanceLog;

    /**
     * 最近学习时间
     */
    private Date latestTime;

    /**
     * 课程路径
     */
    private List<Tree<Long>> courseTreePath;


    /**
     * 该课程是否重复
     */
    private Boolean repeatStatus;
    /**
     * 上次练习批改记录
     */
    private CorrectionRecordVo lastPracticeCorrectionRecord;

    /**
     * 上次测试批改记录
     */
    private CorrectionRecordVo lastTestCorrectionRecord;

}
