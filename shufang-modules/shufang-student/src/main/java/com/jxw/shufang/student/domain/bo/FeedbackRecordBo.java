package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.FeedbackRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 反馈记录业务对象 feedback_record
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FeedbackRecord.class, reverseConvertGenerate = false)
public class FeedbackRecordBo extends BaseEntity {

    /**
     * 反馈记录id
     */
    @NotNull(message = "反馈记录id不能为空", groups = { EditGroup.class })
    private Long feedbackRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 反馈内容
     */
    @NotBlank(message = "反馈内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackContent;

    /**
     * 反馈日期范围-开始
     */
    @NotNull(message = "反馈日期范围-开始不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackStartDate;

    /**
     * 反馈日期范围-结束
     */
    @NotNull(message = "反馈日期范围-结束不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackEndDate;


    /**
     * 反馈图片（oss_id，多个，逗号隔开）
     */
//    @NotBlank(message = "反馈图片（oss_id，多个，逗号隔开）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackImgs;

    /**
     * 表现分数（满分为5分，用星进行展示）
     */
    @NotNull(message = "表现分数（满分为5分，用星进行展示）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long showScore;

    /**
     * 发布状态 1暂存 2已发布
     */
    @NotBlank(message = "发布状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackStatus;

    /**
     * 反馈提交状态 1.未反馈 2.已反馈 3.超时反馈 4.超时未反馈
     */
    private Integer feedbackSubmitStatus;

    /**
     * 1已通知 2未通知(默认) 3通知但失败
     */
    private String parentNotificationStatus;

    /**
     * 最近的一次通知时间
     */
    private Date notificationTime;

    /**
     * 最近一次通知的openid
     */
    private String notificationOpenid;


    /**
     * 开始时间的一个限制
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackStartDateLimit;

    /**
     * 结束时间的一个限制
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackEndDateLimit;

    private Date feedbackTime;

    private Date feedbackRecordCreateTimeStart;

    private Date feedbackRecordCreateTimeEnd;

    private List<Long> studentIdList;


    private Boolean withConsultantInfo;

    private Boolean withCreateStaffInfo;

    /**
     * 会员姓名+电话尾数
     */
    private String nameWithPhone;

    /**
     * 会员账号
     */
    private String studentAccount;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    private Long createBy;

    private Boolean nonSelectFeedbackContentField;

    /**
     * 需要隐藏的学习记录id列表
     */
    private List<Long> unShowStudyRecordIdList;
}
