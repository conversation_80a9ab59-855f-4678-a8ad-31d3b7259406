package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AiPracticeRecord;
import com.jxw.shufang.student.domain.bo.AiPracticeRecordBo;
import com.jxw.shufang.student.domain.vo.AiPracticeRecordVo;
import com.jxw.shufang.student.mapper.AiPracticeRecordMapper;
import com.jxw.shufang.student.service.IAiPracticeRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Ai练习记录Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class AiPracticeRecordServiceImpl implements IAiPracticeRecordService, BaseService {

    private final AiPracticeRecordMapper baseMapper;

    /**
     * 查询Ai练习记录
     */
    @Override
    public AiPracticeRecordVo queryById(Long aiPracticeRecordId){
        return baseMapper.selectVoById(aiPracticeRecordId);
    }

    /**
     * 查询Ai练习记录列表
     */
    @Override
    public TableDataInfo<AiPracticeRecordVo> queryPageList(AiPracticeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiPracticeRecord> lqw = buildQueryWrapper(bo);
        Page<AiPracticeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询Ai练习记录列表
     */
    @Override
    public List<AiPracticeRecordVo> queryList(AiPracticeRecordBo bo) {
        LambdaQueryWrapper<AiPracticeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiPracticeRecord> buildQueryWrapper(AiPracticeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiPracticeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AiPracticeRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, AiPracticeRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getQuestionId() != null, AiPracticeRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getCourseResourceId() != null, AiPracticeRecord::getCourseResourceId, bo.getCourseResourceId());
        lqw.eq(bo.getResourceContent() != null, AiPracticeRecord::getResourceContent, bo.getResourceContent());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), AiPracticeRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), AiPracticeRecord::getAnswerResult, bo.getAnswerResult());
        lqw.eq(bo.getAnswerImg() != null, AiPracticeRecord::getAnswerImg, bo.getAnswerImg());
        return lqw;
    }

    /**
     * 新增Ai练习记录
     */
    @Override
    public Boolean insertByBo(AiPracticeRecordBo bo) {
        AiPracticeRecord add = MapstructUtils.convert(bo, AiPracticeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiPracticeRecordId(add.getAiPracticeRecordId());
        }
        return flag;
    }

    /**
     * 修改Ai练习记录
     */
    @Override
    public Boolean updateByBo(AiPracticeRecordBo bo) {
        AiPracticeRecord update = MapstructUtils.convert(bo, AiPracticeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiPracticeRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除Ai练习记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<AiPracticeRecordBo> convert) {
        List<AiPracticeRecord> list = MapstructUtils.convert(convert, AiPracticeRecord.class);
        return baseMapper.insertBatch(list);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
