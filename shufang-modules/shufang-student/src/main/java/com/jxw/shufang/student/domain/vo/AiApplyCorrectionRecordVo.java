package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.AiApplyCorrectionRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * ai申请批改记录视图对象 ai_apply_correction_record
 *
 *
 * @date 2024-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiApplyCorrectionRecord.class)
public class AiApplyCorrectionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ai申请批改记录id
     */
    @ExcelProperty(value = "ai申请批改记录id")
    private Long aiApplyCorrectionRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 课程ID
     */
    @ExcelProperty(value = "课程ID")
    private Long courseId;

    /**
     * 申请批改类型（1练习  2测试）
     */
    private String applyType;

    /**
     * 申请结果（1允许 2拒绝 0待审核）
     */
    @ExcelProperty(value = "申请结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=允许,2=拒绝,0=待审核")
    private String applyResult;

    /**
     * 是否允许自主批改（1允许自主批改 2不允许自主批改）
     */
    private String allowOwnType;

    /**
     * 练习状态（1未批改 2批改中 3已批改）
     */
    private String practiceState;

    /**
     * 测验状态（1未批改 2批改中 3已批改）
     */
    private String testState;

    private AllowOwnCorrectionVo allowOwnCorrection;

    private StudentVo student;

    private CourseVo course;

    private Date createTime;

    private Date updateTime;

    private Long createBy;

    private Long updateBy;

    private Long createDept;
    /**
     * 最新一次重新提交时间
     */
    private Date applyTime;

}
