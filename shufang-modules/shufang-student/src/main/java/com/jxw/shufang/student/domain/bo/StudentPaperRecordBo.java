package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentPaperRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用户答题试卷记录业务对象 student_paper_record
 *
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentPaperRecord.class, reverseConvertGenerate = false)
public class StudentPaperRecordBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long studentPaperRecordId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long studentId;

    /**
     * 学生姓名
     */
    @NotBlank(message = "学生姓名不能为空")
    private String studentName;

    /**
     * 年级id
     */
    @NotNull(message = "年级id不能为空")
    private Integer gradeId;

    /**
     * 试卷id
     */
    @NotNull(message = "试卷id不能为空")
    private Integer testPaperId;

    /**
     * 试卷名称
     */
    @NotBlank(message = "试卷名称不能为空")
    private String testPaperName;

    /**
     * 试卷类型
     */
    @NotNull(message = "试卷类型不能为空")
    private Integer testPaperType;

    @NotNull(message = "测试范围不能为空")
    private String paperTypeName;
    /**
     * 试卷科目id
     */
    @NotNull(message = "试卷科目id不能为空")
    private Integer subjectId;

    /**
     * 正确率
     */
    private Integer score;


    /**
     * 学习记录状态（0未完成 1已完成）
     */
    @NotNull(message = "学习记录状态不能为空")
    private Integer recordStatus;

    /**
     * pdf报告url
     */
    private String reportUrl;

    /**
     * （来源） XWSF=学王书房
     */
    @NotBlank(message = "（来源） XWSF=学王书房不能为空")
    private String source;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTime;

    /**
     * 查询时使用
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTimeStart;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishTimeEnd;


}
