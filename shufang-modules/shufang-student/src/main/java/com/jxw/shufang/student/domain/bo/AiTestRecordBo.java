package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AiTestRecord;

/**
 * ai测验记录业务对象 ai_test_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiTestRecord.class, reverseConvertGenerate = false)
public class AiTestRecordBo extends BaseEntity {

    /**
     * ai测验记录id
     */
    @NotNull(message = "ai测验记录id不能为空", groups = { EditGroup.class })
    private Long aiTestRecordId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 课程资源id
     */
    @NotNull(message = "课程资源id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseResourceId;

    /**
     * 资源内容（oss_id）
     */
    @NotNull(message = "资源内容（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long resourceContent;

    /**
     * 题目序号
     */
    @NotBlank(message = "题目序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对 全错 半错）
     */
    @NotBlank(message = "作答结果（对应字典值，如全对 全错 半错）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String answerResult;

    /**
     * 作答图片（oss_id）
     */
    @NotNull(message = "作答图片（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long answerImg;


}
