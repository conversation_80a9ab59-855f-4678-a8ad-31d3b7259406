package com.jxw.shufang.student.service.studyduration.aistudy;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import com.jxw.shufang.student.domain.dto.BatchQueryQuestionRecordDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateQuestionProcessDTO;
import com.jxw.shufang.student.domain.dto.StudySlicesProcessingContextDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.IAiStudyRecordService;
import com.jxw.shufang.student.service.studyduration.AbstractQuestionDurationTime;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/7 23:27
 * @Version 1
 * @Description AI伴学【练习】【测试】 时长统计
 */
@Service
public class AiQuestionVideoDurationTimeServiceImpl extends AbstractQuestionDurationTime<QuestionVideoRecordBo> {
    @Resource
    private IAiStudyRecordService apiStudyRecordService;

    @Override
    public List<QuestionVideoRecordBo> filterData(List<QuestionVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return records;
    }

    @Override
    public StudySlicesProcessingContextDTO contextData(List<QuestionVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<Long> studentIds = records.stream().map(QuestionVideoRecordBo::getStudentId).toList();
        List<Long> courseIds = records.stream().map(QuestionVideoRecordBo::getCourseId).filter(Objects::nonNull).toList();
        List<Long> videoIds = records.stream().map(QuestionVideoRecordBo::getVideoId).filter(Objects::nonNull).collect(Collectors.toList());
        StudySlicesProcessingContextDTO contextData = new StudySlicesProcessingContextDTO();
        contextData.setExistQuestionVideoRecordMap(this.getCourseQuestionVideoRecordMap(records, moduleAndGroupEnum));
        contextData.setExistAiStudyRecordMap(this.batchQuerySameStudentAiRecord(studentIds, courseIds));
        contextData.setStudentMap(super.studentMap(studentIds));
        contextData.setCourseDurationMap(super.courseDurationMap(videoIds));
        contextData.setStudyModuleTypeEnum(moduleAndGroupEnum);

        this.checkRepeatRecord(records, contextData);

        return contextData;
    }

    private void checkRepeatRecord(List<QuestionVideoRecordBo> records, StudySlicesProcessingContextDTO contextData) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        QuestionVideoRecordBo videoRecordBo = records.get(0);

        String studyVideoSlices = videoRecordBo.getStudyVideoSlices();
        List<QuestionVideoRecord> existQuestionVideoRecords = contextData.getExistQuestionVideoRecordMap().get(videoRecordBo.getStudentId());
        if (CollectionUtils.isEmpty(existQuestionVideoRecords)) {
            return;
        }
        List<QuestionVideoRecord> repeatSlicesList = existQuestionVideoRecords.stream()
            .filter(studyVideoRecord -> VideoSlicesUtils.ignoreRepeatSlice(studyVideoRecord.getStudyVideoSlices(), studyVideoSlices))
            .toList();
        if (CollectionUtil.isNotEmpty(repeatSlicesList)) {
            contextData.setIgnoreStudyVideoRecord(true);
        }
    }

    @Override
    public SaveOrUpdateQuestionProcessDTO buildQuestionProcessDTO(QuestionVideoRecordBo recordBo) {
        SaveOrUpdateQuestionProcessDTO processDTO = new SaveOrUpdateQuestionProcessDTO();
        processDTO.setModuleGroup(recordBo.getStudyModuleType().getGroupEnum().getGroupCode());
        processDTO.setStudentId(recordBo.getStudentId());
        processDTO.setCourseId(recordBo.getCourseId());
        processDTO.setVideoId(recordBo.getVideoId());
        processDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        processDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        processDTO.setCommitTime(recordBo.getCommitTime());
        processDTO.setQuestionId(recordBo.getQuestionId());
        processDTO.setQuestionType(recordBo.getQuestionType());
        return processDTO;
    }

    public Map<Long, AiStudyRecord> batchQuerySameStudentAiRecord(List<Long> studentIds, List<Long> courseIds) {
        Long studentId = studentIds.get(0);
        Long courseId = courseIds.get(0);
        AiStudyRecord aiStudyRecord = apiStudyRecordService.selectStudyAitestRecordByStudentCourse(studentId, courseId);
        if (aiStudyRecord == null) {
            return new HashMap<>();
        }
        HashMap<Long, AiStudyRecord> resultMap = new HashMap<>();
        resultMap.put(courseId, aiStudyRecord);
        return resultMap;
    }

    private Map<Long, List<QuestionVideoRecord>> getCourseQuestionVideoRecordMap(List<QuestionVideoRecordBo> records,
                                                                                 StudyModuleAndGroupEnum studyModuleAndGroupEnum) {
        List<BatchQueryQuestionRecordDTO> batchQueryQuestionRecordList = records.stream()
            .map(questionVideoRecordBo -> convertToBatchQueryQuestionRecordDTO(studyModuleAndGroupEnum, questionVideoRecordBo))
            .toList();
        return super.studentQuestionVideoRecordMap(batchQueryQuestionRecordList);
    }

    private static BatchQueryQuestionRecordDTO convertToBatchQueryQuestionRecordDTO(StudyModuleAndGroupEnum studyModuleAndGroupEnum,
                                                                                    QuestionVideoRecordBo questionVideoRecordBo) {
        BatchQueryQuestionRecordDTO batchQueryQuestionRecordDTO = new BatchQueryQuestionRecordDTO();
        batchQueryQuestionRecordDTO.setStudentId(questionVideoRecordBo.getStudentId());
        batchQueryQuestionRecordDTO.setCourseId(questionVideoRecordBo.getCourseId());
        batchQueryQuestionRecordDTO.setQuestionType(questionVideoRecordBo.getQuestionType());
        batchQueryQuestionRecordDTO.setQuestionId(questionVideoRecordBo.getQuestionId());
        batchQueryQuestionRecordDTO.setModuleType(studyModuleAndGroupEnum.getGroupEnum().getGroupCode());
        batchQueryQuestionRecordDTO.setVideoId(questionVideoRecordBo.getVideoId());
        return batchQueryQuestionRecordDTO;
    }

    @Override
    public SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(QuestionVideoRecordBo recordBo) {
        SaveOrUpdateStudyRecordDTO studyRecordDTO = new SaveOrUpdateStudyRecordDTO();
        studyRecordDTO.setCourseId(recordBo.getCourseId());
        studyRecordDTO.setStudentId(recordBo.getStudentId());
        studyRecordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        studyRecordDTO.setCommitTime(recordBo.getCommitTime());
        studyRecordDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        studyRecordDTO.setVideoId(recordBo.getVideoId());
        return studyRecordDTO;
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return List.of(StudyModelGroupEnum.AI_STUDY);
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return List.of(StudyModuleTypeEnum.PRACTICE, StudyModuleTypeEnum.TEST);
    }
}
