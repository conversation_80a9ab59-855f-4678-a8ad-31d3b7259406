package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo;

import java.util.Collection;
import java.util.List;

/**
 * 允许自主批改Service接口
 *
 *
 * @date 2024-05-07
 */
public interface IAllowOwnCorrectionService {

    /**
     * 查询允许自主批改
     */
    AllowOwnCorrectionVo queryById(Long allowOwnCorrectionId);

    /**
     * 查询允许自主批改列表
     */
    TableDataInfo<AllowOwnCorrectionVo> queryPageList(AllowOwnCorrectionBo bo, PageQuery pageQuery);

    /**
     * 查询允许自主批改列表
     */
    List<AllowOwnCorrectionVo> queryList(AllowOwnCorrectionBo bo);

    /**
     * 新增允许自主批改
     */
    boolean insertByBo(AllowOwnCorrectionBo bo);

    /**
     * 修改允许自主批改
     */
    Boolean updateByBo(AllowOwnCorrectionBo bo);

    /**
     * 校验并批量删除允许自主批改信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 按学生id查询
     *
     * @param studentId 学生id
     * @param type      应用类型，1学习规划  2ai学习
     * @param allowType      允许类型，1练习  2测试
     *
     * @date 2024/05/08 03:43:17
     */
    AllowOwnCorrectionVo queryByStudentId(Long studentId, String type,String allowType);

    /**
     * 修改学生是否允许自主批改(存在就删除，不存在就新增)
     * @param studentId
     * @return
     */
    boolean changeAllowOwnCorrection(Long studentId,String type,String allowType);


    boolean updateApplyCorrectionRecord(String allowType, Long studentId);

    boolean updateAiApplyCorrectionRecord(String allowType, Long studentId);

    /**
     * 计数
     *
     * @param bo bo
     *
     * @date 2024/06/06 10:34:16
     */
    Long count(AllowOwnCorrectionBo bo);
}
