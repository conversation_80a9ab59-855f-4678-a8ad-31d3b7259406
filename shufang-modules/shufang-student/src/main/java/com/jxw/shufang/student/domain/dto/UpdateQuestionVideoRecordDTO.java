package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/2 14:56
 * @Version 1
 * @Description
 */
@Data
public class UpdateQuestionVideoRecordDTO {
    /**
     * 学习规划记录Id
     */
    private Long studyPlanningRecordId;
    /**
     * 题目视频记录集合
     */
    private List<QuestionVideoRecordBo> questionVideoRecordList;
    /**
     * 会员信息
     */
    private Student student;

    public static UpdateQuestionVideoRecordDTO of(Long studyPlanningRecordId,
                                           List<QuestionVideoRecordBo> questionVideoR<PERSON>ordList,
                                           Student student) {
        UpdateQuestionVideoRecordDTO updateQuestionVideoRecordDTO = new UpdateQuestionVideoRecordDTO();
        updateQuestionVideoRecordDTO.setStudyPlanningRecordId(studyPlanningRecordId);
        updateQuestionVideoRecordDTO.setQuestionVideoRecordList(questionVideoRecordList);
        updateQuestionVideoRecordDTO.setStudent(student);
        return updateQuestionVideoRecordDTO;
    }
}
