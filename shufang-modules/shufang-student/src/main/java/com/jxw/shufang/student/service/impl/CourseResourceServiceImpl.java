package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.BeanUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceV2Vo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.student.domain.CourseResource;
import com.jxw.shufang.student.domain.bo.CourseResourceBo;
import com.jxw.shufang.student.domain.bo.CourseResourceKnowledgeBo;
import com.jxw.shufang.student.domain.bo.CourseResourceV2Bo;
import com.jxw.shufang.student.domain.dto.CourseWithTopCourseDTO;
import com.jxw.shufang.student.domain.dto.DownloadMergeResourceDTO;
import com.jxw.shufang.student.domain.vo.CourseResourceV2Vo;
import com.jxw.shufang.student.domain.vo.CourseResourceVo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.mapper.CourseResourceMapper;
import com.jxw.shufang.student.service.ICourseResourceService;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.INewStudyPlanningRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程资源（绑定到课程的资源）Service业务层处理
 *
 * @date 2024-05-13
 */
@RequiredArgsConstructor
@Service
public class CourseResourceServiceImpl implements ICourseResourceService, BaseService {

    private final CourseResourceMapper baseMapper;
    private final ICourseService courseService;
    @DubboReference
    private RemoteExtResourceService resourceService;

    @Resource
    private INewStudyPlanningRecordService studyPlanningRecordService;

    /**
     * 查询课程资源（绑定到课程的资源）
     */
    @Override
    public CourseResourceVo queryById(Long courseResourceId) {
        return baseMapper.selectVoById(courseResourceId);
    }

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    @Override
    public TableDataInfo<CourseResourceVo> queryPageList(CourseResourceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CourseResource> lqw = buildQueryWrapper(bo);
        Page<CourseResourceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    @Override
    public List<CourseResourceVo> queryList(CourseResourceBo bo) {
        LambdaQueryWrapper<CourseResource> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CourseResource> buildQueryWrapper(CourseResourceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CourseResource> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCourseId() != null, CourseResource::getCourseId, bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseResourceType()), CourseResource::getCourseResourceType, bo.getCourseResourceType());
        lqw.eq(StringUtils.isNotBlank(bo.getResourceSource()), CourseResource::getResourceSource, bo.getResourceSource());
        lqw.eq(StringUtils.isNotBlank(bo.getExternalInformationId()), CourseResource::getExternalInformationId, bo.getExternalInformationId());
        lqw.eq(bo.getInformationId() != null, CourseResource::getInformationId, bo.getInformationId());
        lqw.eq(StringUtils.isNotBlank(bo.getResourceContent()), CourseResource::getResourceContent, bo.getResourceContent());
        lqw.eq(bo.getResourceOssId() != null, CourseResource::getResourceOssId, bo.getResourceOssId());
        lqw.like(StringUtils.isNotBlank(bo.getResourceName()), CourseResource::getResourceName, bo.getResourceName());
        lqw.eq(bo.getResourceSize() != null, CourseResource::getResourceSize, bo.getResourceSize());
        lqw.eq(bo.getResourceDuration() != null, CourseResource::getResourceDuration, bo.getResourceDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), CourseResource::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionInfo()), CourseResource::getQuestionInfo, bo.getQuestionInfo());
        return lqw;
    }

    /**
     * 新增课程资源（绑定到课程的资源）
     */
    @Override
    public Boolean insertByBo(CourseResourceBo bo) {
        CourseResource add = MapstructUtils.convert(bo, CourseResource.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCourseResourceId(add.getCourseResourceId());
        }
        return flag;
    }

    /**
     * 修改课程资源（绑定到课程的资源）
     */
    @Override
    public Boolean updateByBo(CourseResourceBo bo) {
        CourseResource update = MapstructUtils.convert(bo, CourseResource.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CourseResource entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除课程资源（绑定到课程的资源）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public CourseResourceV2Vo getInfo(CourseResourceV2Bo bo) {
        Long courseId = bo.getCourseId();
        CourseVo courseVo = courseService.queryById(courseId, Boolean.FALSE);
        if (ObjectUtils.isEmpty(courseVo)) {
            return null;
        }
        courseService.putTopmostCourseInfo(List.of(courseVo), Boolean.TRUE);
        CourseVo topmostCourse = courseVo.getTopmostCourse();
        if (!ObjectUtils.isEmpty(topmostCourse)) {
            courseService.putCourseDetail(List.of(topmostCourse), Boolean.FALSE);
        }
        Long knowledgeId = courseVo.getKnowledgeId();
        if (ObjectUtils.isEmpty(knowledgeId)) {
            return null;
        }
        CourseResourceV2Vo courseResourceV2Vo = new CourseResourceV2Vo();
        courseResourceV2Vo.setCourse(courseVo);
        //获取课程资源
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setTypeList(List.of(KnowledgeResourceType.HANDOUT.getType(),
            KnowledgeResourceType.TEST.getType(),
            KnowledgeResourceType.TEST_ANALYSIS.getType(),
            KnowledgeResourceType.PRACTICE.getType(),
            KnowledgeResourceType.PRACTICE_ANALYSIS.getType()));


        courseResourceV2Vo.setKnowledgeResourceList(afterSet(resourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo)));
        return courseResourceV2Vo;

    }

    private List<RemoteGroupResourceV2Vo> afterSet(List<RemoteGroupResourceVo> knowledgeResourceList) {
        if (CollectionUtils.isEmpty(knowledgeResourceList)){
            return List.of();
        }
        return knowledgeResourceList.stream().map(v -> {
            RemoteGroupResourceV2Vo remoteGroupResourceV2Vo = new RemoteGroupResourceV2Vo();
            if(!ObjectUtils.isEmpty(v.getKnowledgeResource())) {
                BeanUtils.copyProperties(v.getKnowledgeResource(), remoteGroupResourceV2Vo);
            }
            remoteGroupResourceV2Vo.setResourceType(v.getType());
            remoteGroupResourceV2Vo.setKnowledgeId(v.getKnowledgeId());
            return remoteGroupResourceV2Vo;
        }).sorted(Comparator.comparing(v -> v.getResourceType().getSortNum())).collect(Collectors.toList());
    }

    @Override
    public String preview(CourseResourceKnowledgeBo bo) {
        RemoteGroupResourceVo resource = resourceService.getKnowledgeResourceById(bo.getKnowledgeId(), bo.getResourceType().getType());
        if (ObjectUtils.isEmpty(resource)){
            return null;
        }
        RemoteKnowledgeResourceVo knowledgeResource = resource.getKnowledgeResource();
        if (ObjectUtils.isEmpty(knowledgeResource)){
            return null;
        }
        return knowledgeResource.getUrl();
    }

    @Override
    public List<String> multiGetUrls(List<CourseResourceKnowledgeBo> knowledgeList) {
        if (CollectionUtils.isEmpty(knowledgeList)){
            return List.of();
        }

        CourseResourceKnowledgeBo courseResourceKnowledgeBo = knowledgeList.get(0);
        Long knowledgeId = courseResourceKnowledgeBo.getKnowledgeId();
        //获取课程资源
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setTypeList(knowledgeList.stream().map(CourseResourceKnowledgeBo::getResourceType).map(KnowledgeResourceType::getType).toList());
        List<RemoteGroupResourceVo> knowledgeResourceList = resourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);

        if (CollectionUtils.isEmpty(knowledgeResourceList)){
            return List.of();
        }
        return knowledgeResourceList
            .stream()
            .map(RemoteGroupResourceVo::getKnowledgeResource)
            .filter(v -> !ObjectUtils.isEmpty(v))
            .map(RemoteKnowledgeResourceVo::getUrl)
            .toList();

    }

    @Override
    public List<RemoteGroupResourceV2Vo> multiGetUrlsVo(List<CourseResourceKnowledgeBo> knowledgeList) {
        if (CollectionUtils.isEmpty(knowledgeList)){
            return List.of();
        }

        CourseResourceKnowledgeBo courseResourceKnowledgeBo = knowledgeList.get(0);
        Long knowledgeId = courseResourceKnowledgeBo.getKnowledgeId();
        //获取课程资源
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setTypeList(knowledgeList.stream().map(CourseResourceKnowledgeBo::getResourceType).map(KnowledgeResourceType::getType).toList());
        List<RemoteGroupResourceVo> knowledgeResourceList = resourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);

        if (CollectionUtils.isEmpty(knowledgeResourceList)){
            return List.of();
        }
        return knowledgeResourceList
            .stream()
            .map(v->{
                RemoteGroupResourceV2Vo remoteGroupResourceV2Vo = new RemoteGroupResourceV2Vo();
                if(!ObjectUtils.isEmpty(v.getKnowledgeResource())) {
                    BeanUtils.copyProperties(v.getKnowledgeResource(), remoteGroupResourceV2Vo);
                }
                remoteGroupResourceV2Vo.setResourceType(v.getType());
                remoteGroupResourceV2Vo.setKnowledgeId(v.getKnowledgeId());
                return remoteGroupResourceV2Vo;
            })
            .filter(v -> StringUtils.isNotEmpty(v.getUrl()))
            .toList();
    }

    @Override
    public Map<Long, CourseWithTopCourseDTO> getMergeCourseResource(DownloadMergeResourceDTO resourceDTO) {
        // 根据课程找到知识点ID
        List<Long> courseIds = resourceDTO.getCourseIds();
        Set<Long> courseIdset = new HashSet<>(courseIds);
        Map<Long, CourseWithTopCourseDTO> courseMap = studyPlanningRecordService.getCourseMap(courseIdset);
        if (CollectionUtils.isEmpty(courseMap)) {
            throw new ServiceException("无效的课程ID");
        }
        return courseMap;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
