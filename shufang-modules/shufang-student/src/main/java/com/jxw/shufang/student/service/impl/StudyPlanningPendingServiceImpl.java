package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudyFeedbackReport;
import com.jxw.shufang.student.domain.StudyPlanningFeedbackPendingRelation;
import com.jxw.shufang.student.domain.StudyPlanningPending;
import com.jxw.shufang.student.domain.StudyPlanningPendingRelation;
import com.jxw.shufang.student.domain.bo.StudyPlanningPendingBo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo;
import com.jxw.shufang.student.mapper.StudyFeedbackReportMapper;
import com.jxw.shufang.student.mapper.StudyPlanningFeedbackPendingRelationMapper;
import com.jxw.shufang.student.mapper.StudyPlanningPendingMapper;
import com.jxw.shufang.student.mapper.StudyPlanningPendingRelationMapper;
import com.jxw.shufang.student.service.IStudentMembershipService;
import com.jxw.shufang.student.service.IStudyPlanningPendingService;
import com.jxw.shufang.common.core.enums.QuarterlyEnum;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 需学习规划学生Service业务层处理
 *
 * @date 2024-06-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StudyPlanningPendingServiceImpl implements IStudyPlanningPendingService {

    private final StudyPlanningPendingMapper baseMapper;
    private final StudyPlanningPendingRelationMapper relationMapper;
    private final StudyPlanningFeedbackPendingRelationMapper feedbackReportPendingRelationMapper;
    private final StudyFeedbackReportMapper studyFeedbackReportMapper;
    private final IStudentMembershipService studentMembershipService;

    /**
     * 查询需学习规划学生
     */
    @Override
    public StudyPlanningPendingVo queryById(Long id) {
        StudyPlanningPendingVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            setTimeoutStatus(vo);
        }
        return vo;
    }

    /**
     * 查询需学习规划学生列表
     */
    @Override
    public TableDataInfo<StudyPlanningPendingVo> queryPageList(StudyPlanningPendingBo bo, PageQuery pageQuery) {
        QueryWrapper<StudyPlanningPending> qw = buildJoinQueryWrapper(bo);

        Page<StudyPlanningPendingVo> result;

        // 查询数据
        result = baseMapper.selectVoPageWithJoin(pageQuery.build(), qw);

        // 设置超时状态
        if (result != null && result.getRecords() != null) {
            result.getRecords().forEach(this::setTimeoutStatus);
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询需学习规划学生列表
     */
    @Override
    public List<StudyPlanningPendingVo> queryList(StudyPlanningPendingBo bo) {
        QueryWrapper<StudyPlanningPending> qw = buildJoinQueryWrapper(bo);

        List<StudyPlanningPendingVo> result;
        // 查询数据
        result = baseMapper.selectVoListWithJoin(qw);

        // 设置超时状态
        if (result != null) {
            result.forEach(this::setTimeoutStatus);
        }

        return result;
    }

    private LambdaQueryWrapper<StudyPlanningPending> buildQueryWrapper(StudyPlanningPendingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyPlanningPending> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudyPlanningPending::getStudentId, bo.getStudentId());
        lqw.eq(bo.getModeType() != null, StudyPlanningPending::getModeType, bo.getModeType());
        lqw.eq(bo.getFeedbackStatus() != null, StudyPlanningPending::getFeedbackStatus, bo.getFeedbackStatus());
        lqw.eq(bo.getPlanningStatus() != null, StudyPlanningPending::getPlanningStatus, bo.getPlanningStatus());
        lqw.ge(bo.getPlanStartDate() != null, StudyPlanningPending::getPlanStartDate, bo.getPlanStartDate());
        lqw.le(bo.getPlanEndDate() != null, StudyPlanningPending::getPlanEndDate, bo.getPlanEndDate());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIds()), StudyPlanningPending::getStudentId, bo.getStudentIds());
        lqw.in(CollUtil.isNotEmpty(bo.getFeedbackStatusList()), StudyPlanningPending::getFeedbackStatus, bo.getFeedbackStatusList());
        lqw.in(CollUtil.isNotEmpty(bo.getPlanningStatusList()), StudyPlanningPending::getPlanningStatus, bo.getPlanningStatusList());
        lqw.orderByDesc(StudyPlanningPending::getCreateTime);
        return lqw;
    }

    /**
     * 构建连表查询的QueryWrapper
     */
    private QueryWrapper<StudyPlanningPending> buildJoinQueryWrapper(StudyPlanningPendingBo bo) {
        QueryWrapper<StudyPlanningPending> qw = new QueryWrapper<>();

        // 基础条件
        qw.eq(bo.getStudentId() != null, "spp.student_id", bo.getStudentId());
        qw.eq(bo.getBranchId() != null, "s.branch_id", bo.getBranchId());
        qw.eq(bo.getConsultantId() != null, "scr.student_consultant_id", bo.getConsultantId());
        qw.eq(bo.getModeType() != null, "spp.mode_type", bo.getModeType());
        qw.eq(bo.getFeedbackStatus() != null, "spp.feedback_status", bo.getFeedbackStatus());
        qw.eq(bo.getPlanningStatus() != null, "spp.planning_status", bo.getPlanningStatus());
        qw.ge(bo.getPlanStartDate() != null, "spp.plan_start_date", bo.getPlanStartDate());
        qw.le(bo.getPlanEndDate() != null, "spp.plan_end_date", bo.getPlanEndDate());
        qw.in(CollUtil.isNotEmpty(bo.getStudentIds()), "spp.student_id", bo.getStudentIds());
        qw.in(CollUtil.isNotEmpty(bo.getConsultantIds()), "scr.student_consultant_id", bo.getConsultantIds());
        qw.in(CollUtil.isNotEmpty(bo.getFeedbackStatusList()), "spp.feedback_status", bo.getFeedbackStatusList());
        qw.in(CollUtil.isNotEmpty(bo.getPlanningStatusList()), "spp.planning_status", bo.getPlanningStatusList());

        // 分支权限控制
        addBranchPermissionConditions(qw, bo);

        // 处理组合状态查询
        if (CollUtil.isNotEmpty(bo.getCombinedPlanningStatus())) {
            addCombinedPlanningStatusConditions(qw, bo.getCombinedPlanningStatus());
        }
        if (CollUtil.isNotEmpty(bo.getCombinedFeedbackStatus())) {
            addCombinedFeedbackStatusConditions(qw, bo.getCombinedFeedbackStatus());
        }

        // 连表查询条件
        qw.like(StrUtil.isNotBlank(bo.getStudentName()), "s.student_name", bo.getStudentName());
        qw.like(StrUtil.isNotBlank(bo.getConsultantName()), "su.nick_name", bo.getConsultantName());
        qw.eq(bo.getPlanningStatus()!= null, "spp.planning_status", bo.getPlanningStatus());
        qw.eq(bo.getFeedbackStatus()!= null, "spp.feedback_status", bo.getFeedbackStatus());

        qw.orderByDesc("spp.plan_start_date").orderByDesc("spp.id");
        return qw;
    }

    /**
     * 添加分支权限控制条件
     * 根据当前用户的分支权限控制数据访问范围
     */
    private void addBranchPermissionConditions(QueryWrapper<StudyPlanningPending> qw, StudyPlanningPendingBo bo) {
        // 如果查询条件中已经指定了分支ID，则不需要额外的权限控制
        if (bo.getBranchId() != null) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        try {
            // 获取当前用户的分支权限
            List<Long> branchIdList = LoginHelper.getBranchIdList();
            Long branchId = LoginHelper.getBranchId();

            if (CollUtil.isNotEmpty(branchIdList)) {
                // 用户有多个分支权限，使用分支ID列表过滤
                qw.in("s.branch_id", branchIdList);
                log.debug("应用分支权限控制 - 分支ID列表: {}", branchIdList);
            } else if (branchId != null) {
                // 用户只有单个分支权限，使用单个分支ID过滤
                qw.eq("s.branch_id", branchId);
                log.debug("应用分支权限控制 - 单个分支ID: {}", branchId);
            } else {
                // 用户没有分支权限，返回空结果
                qw.eq("s.branch_id", -1L);
                log.debug("用户没有分支权限，限制查询结果为空");
            }
        } catch (Exception e) {
            // 如果获取用户权限信息失败，为安全起见，限制查询结果为空
            log.warn("获取用户分支权限信息失败，限制查询结果为空", e);
            qw.eq("s.branch_id", -1L);
        }
    }

    /**
     * 新增需学习规划学生
     */
    @Override
    public Boolean insertByBo(StudyPlanningPendingBo bo) {
        StudyPlanningPending add = MapstructUtils.convert(bo, StudyPlanningPending.class);
        validEntityBeforeSave(add);

        // 设置默认值
        if (add.getFeedbackStatus() == null) {
            add.setFeedbackStatus(0); // 默认待反馈
        }
        if (add.getPlanningStatus() == null) {
            add.setPlanningStatus(0); // 默认待规划
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改需学习规划学生
     */
    @Override
    public Boolean updateByBo(StudyPlanningPendingBo bo) {
        StudyPlanningPending update = MapstructUtils.convert(bo, StudyPlanningPending.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyPlanningPending entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除需学习规划学生
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }

        // 删除关联记录
        relationMapper.delete(
            Wrappers.lambdaQuery(StudyPlanningPendingRelation.class)
                .in(StudyPlanningPendingRelation::getPendingId, ids)
        );

        return baseMapper.deleteBatchIds(ids) > 0;
    }


    /**
     * 判断当前是否是春秋模式
     * 使用QuarterlyEnum来确保与Quarter接口的数据保持一致
     */
    private boolean isSpringAutumnMode() {
        return QuarterlyEnum.isCurrentSpringAutumnMode(LocalDate.now());
    }

    /**
     * 获取春秋模式的时间范围
     * 春秋模式按周规划，返回指定日期所在周的周六到周日
     */
    private Pair<Date, Date> getSpringAutumnDateRange(LocalDate baseDate) {
        LocalDate saturday = baseDate.with(DayOfWeek.SATURDAY);
        LocalDate sunday = baseDate.with(DayOfWeek.SUNDAY);
        Date startDate = Date.from(saturday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(sunday.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        return Pair.of(startDate, endDate);
    }

    /**
     * 获取寒暑模式的时间范围
     * 寒暑模式按日规划，只返回指定日期下周一到下周五中实际在寒暑假范围内的日期
     */
    private Pair<Date, Date> getSummerWinterDateRange(LocalDate baseDate) {
        LocalDate nextMonday = baseDate.with(DayOfWeek.MONDAY).plusWeeks(1);
        LocalDate nextFriday = nextMonday.with(DayOfWeek.FRIDAY);

        // 找到这一周内第一个和最后一个在寒暑假范围内的日期
        LocalDate firstSummerWinterDate = null;
        LocalDate lastSummerWinterDate = null;

        LocalDate currentDate = nextMonday;
        while (!currentDate.isAfter(nextFriday)) {
            if (QuarterlyEnum.isCurrentSummerWinterMode(currentDate)) {
                if (firstSummerWinterDate == null) {
                    firstSummerWinterDate = currentDate;
                }
                lastSummerWinterDate = currentDate;
            }
            currentDate = currentDate.plusDays(1);
        }

        // 如果没有找到寒暑假日期，说明不需要生成寒暑模式数据
        if (firstSummerWinterDate == null || lastSummerWinterDate == null) {
            return null; // 返回null表示不需要生成数据
        }

        Date startDate = Date.from(firstSummerWinterDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(lastSummerWinterDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
        return Pair.of(startDate, endDate);
    }

    /**
     * 根据当前日期获取对应的学期时间范围
     * 使用QuarterlyEnum来确保与Quarter接口的数据保持一致
     */
    private Pair<Date, Date> getCurrentSemesterDateRange() {
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        QuarterlyEnum currentQuarter = QuarterlyEnum.getCurrentQuarterEnum(now);

        if (currentQuarter == null) {
            // 如果无法确定季度，按月份判断（降级处理）
            int month = now.getMonthValue();
            if (month >= 3 && month <= 6) {
                // 春季：3月1日-6月30日
                return Pair.of(
                    Date.from(LocalDate.of(year, 3, 1).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(LocalDate.of(year, 6, 30).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
                );
            } else if (month >= 7 && month <= 8) {
                // 暑假：7月1日-8月31日
                return Pair.of(
                    Date.from(LocalDate.of(year, 7, 1).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(LocalDate.of(year, 8, 31).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
                );
            } else if (month >= 9 && month <= 12) {
                // 秋季：9月1日-12月31日
                return Pair.of(
                    Date.from(LocalDate.of(year, 9, 1).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(LocalDate.of(year, 12, 31).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
                );
            } else {
                // 寒假：1月1日-2月28日（考虑闰年）
                int lastDayOfFebruary = LocalDate.of(year, 2, 1).lengthOfMonth();
                return Pair.of(
                    Date.from(LocalDate.of(year, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant()),
                    Date.from(LocalDate.of(year, 2, lastDayOfFebruary).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
                );
            }
        }

        // 使用QuarterlyEnum的定义来计算时间范围
        LocalDate startDate = LocalDate.of(year, currentQuarter.getStartMonth(), 1);
        LocalDate endDate = LocalDate.of(year, currentQuarter.getEndMonth(), 1).withDayOfMonth(
            LocalDate.of(year, currentQuarter.getEndMonth(), 1).lengthOfMonth()
        );

        return Pair.of(
            Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant()),
            Date.from(endDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant())
        );
    }

    /**
     * 生成春秋模式待规划数据
     */
    private List<StudyPlanningPending> generateSpringAutumnPendingData(List<StudentVo> students, int modeType, Date startDate, Date endDate) {
        List<StudyPlanningPending> batchInsertList = new ArrayList<>();
        Set<String> uniqueKeys = new HashSet<>();

        for (StudentVo student : students) {
            String key = student.getStudentId() + "_" + modeType + "_" + startDate.getTime();
            if (!uniqueKeys.contains(key)) {
                batchInsertList.add(buildPendingRecord(student, modeType, startDate, endDate));
                uniqueKeys.add(key);
            }
        }
        return batchInsertList;
    }

    private List<StudyPlanningPending> generateSummerWinterPendingData(List<StudentVo> students, int modeType, Date startDate, Date endDate) {
        List<StudyPlanningPending> batchInsertList = new ArrayList<>();
        Set<String> uniqueKeys = new HashSet<>();

        for (StudentVo student : students) {
            LocalDate currentDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

            while (!currentDate.isAfter(endLocalDate)) {
                Date dayStart = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
                Date dayEnd = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());


                String key = student.getStudentId() + "_" + modeType + "_" + dayStart.getTime();
                if (!uniqueKeys.contains(key)) {
                    batchInsertList.add(buildPendingRecord(student, modeType, dayStart, dayEnd));
                    uniqueKeys.add(key);
                }
                currentDate = currentDate.plusDays(1);
            }
        }
        return batchInsertList;
    }

    @Override
    public Boolean generatePendingStudents() {
        return generatePendingStudents((String) null);
    }

    @Override
    public Boolean generatePendingStudents(String dateParam) {
        LocalDate targetDate = parseDateParam(dateParam);
        return generatePendingStudentsByDate(targetDate);
    }

    /**
     * 解析日期参数
     */
    private LocalDate parseDateParam(String dateParam) {
        if (dateParam == null || dateParam.trim().isEmpty()) {
            LocalDate now = LocalDate.now();
            log.info("日期参数为空，使用当前日期：{}", now);
            return now;
        }

        try {
            LocalDate targetDate = LocalDate.parse(dateParam.trim());
            log.info("使用指定日期：{}", targetDate);
            return targetDate;
        } catch (Exception e) {
            log.error("日期参数解析失败：{}，使用当前日期", dateParam, e);
            return LocalDate.now();
        }
    }

    /**
     * 根据指定日期生成待规划学生记录的核心逻辑
     */
    private Boolean generatePendingStudentsByDate(LocalDate targetDate) {
        try {
            //获取指定日期是否是春秋模式
            boolean isSpringAutumnMode = QuarterlyEnum.isCurrentSpringAutumnMode(targetDate);
            //获取指定日期下周是否有寒暑模式的天数
            boolean isSummerWinterMode = shouldGenerateSummerWinterMode(targetDate);

            if (isSpringAutumnMode) {
                generatePendingStudents(true, targetDate);
            }

            if (isSummerWinterMode) {
                generatePendingStudents(false, targetDate);
            }

            return true;
        } catch (Exception e) {
            log.error("生成待规划学生记录总体流程失败，目标日期：{}", targetDate, e);
            // 重新抛出异常，让xxl-job能够感知到失败并触发重试
            throw new RuntimeException("生成待规划学生记录总体流程失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否应该生成寒暑模式的数据
     * 检查下周一到下周五这一周内有几天在寒暑假范围内，有就生成
     */
    private boolean shouldGenerateSummerWinterMode(LocalDate baseDate) {
        // 获取下周一到下周五
        LocalDate nextMonday = baseDate.with(DayOfWeek.MONDAY).plusWeeks(1);
        LocalDate nextFriday = nextMonday.with(DayOfWeek.FRIDAY);

        // 检查这一周内每一天是否在寒暑模式期间
        LocalDate currentDate = nextMonday;
        while (!currentDate.isAfter(nextFriday)) {
            if (QuarterlyEnum.isCurrentSummerWinterMode(currentDate)) {
                return true; // 只要有一天在寒暑模式期间就生成数据
            }
            currentDate = currentDate.plusDays(1);
        }

        return false;
    }

    private void generatePendingStudents(boolean isSpringAutumnMode, LocalDate baseDate) {
        try {
            int modeType = isSpringAutumnMode ? 1 : 2;

            // 获取时间范围
            Pair<Date, Date> dateRange = isSpringAutumnMode ?
                getSpringAutumnDateRange(baseDate) : getSummerWinterDateRange(baseDate);

            // 如果寒暑模式返回null，说明不需要生成数据
            if (dateRange == null) {
                log.info("寒暑模式下周一到下周五没有寒暑假日期，不需要生成数据，基准日期：{}", baseDate);
                return;
            }

            Date startDate = dateRange.getKey();
            Date endDate = dateRange.getValue();
            List<StudentVo> enrolledStudents = studentMembershipService.getEnrolledStudentsInPeriod(startDate, endDate);

            // 查询在籍会员

            if (CollUtil.isEmpty(enrolledStudents)) {
                log.info("当前时间段内没有在籍会员，模式类型：{}，开始日期：{}，结束日期：{}，基准日期：{}",
                    modeType, DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), baseDate);
                return;
            }

            // 生成对应的模式数据
            List<StudyPlanningPending> batchInsertList = isSpringAutumnMode ?
                generateSpringAutumnPendingData(enrolledStudents, modeType, startDate, endDate) :
                generateSummerWinterPendingData(enrolledStudents, modeType, startDate, endDate);
        //插入之前先检查在数据库是否存在
            if (CollUtil.isNotEmpty(batchInsertList)) {
                List<StudyPlanningPending> existList = baseMapper.selectList(
                    Wrappers.lambdaQuery(StudyPlanningPending.class)
                        .in(StudyPlanningPending::getStudentId, batchInsertList.stream().map(StudyPlanningPending::getStudentId).distinct().toList())
                        .eq(StudyPlanningPending::getModeType, modeType)
                        //结束时间要比StartDate小
                        .le(StudyPlanningPending::getPlanStartDate, endDate)
                        //开始时间要比EndDate大
                        .ge(StudyPlanningPending::getPlanEndDate, startDate));
                if (CollUtil.isNotEmpty(existList)) {
                    //移除已经存在的数据
                    for (StudyPlanningPending exist : existList) {
                        batchInsertList.removeIf(
                            p -> p.getStudentId().equals(exist.getStudentId()));
                    }
                }
            }
            // 批量插入
            if (CollUtil.isNotEmpty(batchInsertList)) {
                baseMapper.insertBatch(batchInsertList);
            }

            log.info("生成待规划学生记录完成，模式类型：{}，开始日期：{}，结束日期：{}，生成记录数：{}，基准日期：{}",
                modeType, DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), batchInsertList.size(), baseDate);

        } catch (Exception e) {
            log.error("生成待规划学生记录失败，基准日期：{}", baseDate, e);
            // 重新抛出异常，让上层调用者（如xxl-job）能够感知到失败
            throw new RuntimeException("生成待规划学生记录失败: " + e.getMessage(), e);
        }
    }


    /**
     * 计算应规划时间
     * 春秋模式：这周五23:59:59
     * 寒暑模式：上周六23:59:59
     */
    private Date calculateExpectedPlanningTime(int modeType, Date startDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDateTime expectedDateTime;

        if (modeType == 1) { // 春秋模式
            // 找到这周的周四
            LocalDate thisFriday = startLocalDate.with(DayOfWeek.THURSDAY);
            expectedDateTime = thisFriday.atTime(23, 59, 59);
        } else { // 寒暑模式
            // 找到上周的周六
            LocalDate lastSaturday = startLocalDate.with(DayOfWeek.SATURDAY).minusWeeks(1);
            expectedDateTime = lastSaturday.atTime(23, 59, 59);
        }

        return Date.from(expectedDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算应反馈时间
     * 春秋模式：这周五23:59:59
     * 寒暑模式：上周六23:59:59
     */
    private Date calculateExpectedFeedbackTime(int modeType, Date startDate) {
        // 反馈时间与规划时间相同
        return calculateExpectedPlanningTime(modeType, startDate);
    }

    /**
     * 构建待规划记录对象
     */
    private StudyPlanningPending buildPendingRecord(StudentVo student, int modeType, Date startDate, Date endDate) {
        StudyPlanningPending pending = new StudyPlanningPending();
        pending.setStudentId(student.getStudentId());
        pending.setModeType(modeType);
        pending.setPlanStartDate(startDate);
        pending.setPlanEndDate(endDate);
        pending.setFeedbackStatus(0); // 待反馈
        pending.setPlanningStatus(0); // 待规划

        // 设置应规划时间和应反馈时间
        Date expectedPlanningTime = calculateExpectedPlanningTime(modeType, startDate);
        Date expectedFeedbackTime = calculateExpectedFeedbackTime(modeType, startDate);
        pending.setExpectedPlanningTime(expectedPlanningTime);
        pending.setExpectedFeedbackTime(expectedFeedbackTime);

        return pending;
    }

    /**
     * 标记学生为已规划
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean markAsPlanned(Long studentId, List<Long> planningRecordIds) {
        // 更新规划状态
        LambdaQueryWrapper<StudyPlanningPending> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(StudyPlanningPending::getStudentId, studentId);
        updateWrapper.eq(StudyPlanningPending::getPlanningStatus, 0); // 待规划状态

        StudyPlanningPending update = new StudyPlanningPending();
        update.setPlanningStatus(1); // 已规划
        update.setActualPlanningTime(new Date()); // 设置实际规划时间

        boolean updateResult = baseMapper.update(update, updateWrapper) > 0;

        if (updateResult && CollUtil.isNotEmpty(planningRecordIds)) {
            // 查询待规划记录
            StudyPlanningPending pending = baseMapper.selectOne(
                Wrappers.lambdaQuery(StudyPlanningPending.class)
                    .eq(StudyPlanningPending::getStudentId, studentId)
                    .eq(StudyPlanningPending::getPlanningStatus, 1)
                    .orderByDesc(StudyPlanningPending::getCreateTime)
                    .last("LIMIT 1")
            );

            if (pending != null) {
                // 保存关联记录
                for (Long planningRecordId : planningRecordIds) {
                    StudyPlanningPendingRelation relation = new StudyPlanningPendingRelation();
                    relation.setPendingId(pending.getId());
                    relation.setPlanningId(planningRecordId);
                    relationMapper.insert(relation);
                }
            }
        }

        return updateResult;
    }

    /**
     * 根据待处理记录ID标记为已反馈
     *
     * <p>根据具体的学习规划待处理记录ID来更新反馈状态，
     * 只有在当前状态为待反馈(0)时才会更新为已反馈(1)，
     * 如果已经反馈过了则不会重复更新。</p>
     *
     * @param pendingId 学习规划待处理记录ID
     */
    @Override
    public void markAsFeedbackByPendingId(Long pendingId) {
        if (pendingId == null) {
            log.warn("学习规划待处理记录ID不能为空");
            return;
        }

        // 先查询当前记录状态
        StudyPlanningPending current = baseMapper.selectById(pendingId);
        if (current == null) {
            log.warn("未找到学习规划待处理记录, pendingId: {}", pendingId);
            return;
        }

        // 如果已经是已反馈状态，直接返回成功
        if (current.getFeedbackStatus() != null && current.getFeedbackStatus() == 1) {
            log.info("学习规划待处理记录已经是已反馈状态，无需重复更新, pendingId: {}, studentId: {}",
                pendingId, current.getStudentId());
            return;
        }

        // 只更新待反馈状态的记录
        LambdaQueryWrapper<StudyPlanningPending> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(StudyPlanningPending::getId, pendingId);
        updateWrapper.eq(StudyPlanningPending::getFeedbackStatus, 0); // 只更新待反馈状态的记录

        StudyPlanningPending update = new StudyPlanningPending();
        update.setFeedbackStatus(1); // 已反馈
        update.setActualFeedbackTime(new Date()); // 设置实际反馈时间

        baseMapper.update(update, updateWrapper);
    }

    /**
     * 根据待处理记录ID标记为已规划
     *
     * <p>根据具体的学习规划待处理记录ID来更新规划状态，
     * 只有在当前状态为待规划(0)时才会更新为已规划(1)，
     * 如果已经规划过了则不会重复更新。</p>
     *
     * @param pendingId 学习规划待处理记录ID
     * @param planningRecordIds 学习规划记录ID列表，用于建立关联关系
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markAsPlannedByPendingId(Long pendingId, List<Long> planningRecordIds) {
        if (pendingId == null) {
            log.warn("学习规划待处理记录ID不能为空");
            return;
        }

        // 先查询当前记录状态
        StudyPlanningPending current = baseMapper.selectById(pendingId);
        if (current == null) {
            log.warn("未找到学习规划待处理记录, pendingId: {}", pendingId);
            return;
        }

        // 如果已经是已规划状态，仍需要处理关联关系
        if (current.getPlanningStatus() != null && current.getPlanningStatus() == 1) {
            log.info("学习规划待处理记录已经是已规划状态，检查并新增不存在的关联关系, pendingId: {}, studentId: {}",
                pendingId, current.getStudentId());

            // 处理关联关系：新增不存在的学习规划关联记录
            if (CollUtil.isNotEmpty(planningRecordIds)) {
                addMissingPlanningRelations(pendingId, planningRecordIds);
            }
            return;
        }

        // 只更新待规划状态的记录
        LambdaQueryWrapper<StudyPlanningPending> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.eq(StudyPlanningPending::getId, pendingId);
        updateWrapper.eq(StudyPlanningPending::getPlanningStatus, 0); // 只更新待规划状态的记录

        StudyPlanningPending update = new StudyPlanningPending();
        update.setPlanningStatus(1); // 已规划
        update.setActualPlanningTime(new Date()); // 设置实际规划时间

        boolean updateResult = baseMapper.update(update, updateWrapper) > 0;

        // 如果更新成功且有学习规划记录ID，则保存关联记录
        if (updateResult && CollUtil.isNotEmpty(planningRecordIds)) {
            // 保存关联记录
            for (Long planningRecordId : planningRecordIds) {
                StudyPlanningPendingRelation relation = new StudyPlanningPendingRelation();
                relation.setPendingId(pendingId);
                relation.setPlanningId(planningRecordId);
                relationMapper.insert(relation);
            }
            log.info("成功保存学习规划关联记录, pendingId: {}, 关联记录数: {}", pendingId, planningRecordIds.size());
        }
    }

    /**
     * 根据StudyPlanningPendingId返回最新一条关联的学习规划反馈记录
     */
    @Override
    public StudyFeedbackReportVo getLatestFeedbackReportByPendingId(Long pendingId) {
        if (pendingId == null) {
            return null;
        }

        // 查询关联的反馈报告ID列表
        List<StudyPlanningFeedbackPendingRelation> relations = feedbackReportPendingRelationMapper.selectList(
            Wrappers.lambdaQuery(StudyPlanningFeedbackPendingRelation.class)
                .eq(StudyPlanningFeedbackPendingRelation::getPendingId, pendingId)
                .orderByDesc(StudyPlanningFeedbackPendingRelation::getCreateTime)
        );

        if (CollUtil.isEmpty(relations)) {
            return null;
        }

        // 获取所有关联的报告ID
        List<Long> reportIds = relations.stream()
            .map(StudyPlanningFeedbackPendingRelation::getReportId)
            .collect(Collectors.toList());

        // 查询最新的反馈报告
        StudyFeedbackReportVo latestReport = studyFeedbackReportMapper.selectVoOne(
            Wrappers.<StudyFeedbackReport>lambdaQuery()
                .in(StudyFeedbackReport::getId, reportIds)
                .orderByDesc(StudyFeedbackReport::getCreateTime)
                .last("LIMIT 1")
        );

        return latestReport;
    }

    /**
     * 添加学习规划组合状态查询条件（多选）
     * 组合状态定义：
     * 0-待规划且未超时, 1-待规划且超时, 2-已规划且未超时, 3-已规划且超时
     * 超时状态通过实际规划时间和应规划时间判断
     */
    private void addCombinedPlanningStatusConditions(QueryWrapper<StudyPlanningPending> qw, List<Integer> combinedStatusList) {
        if (CollUtil.isEmpty(combinedStatusList)) {
            return;
        }

        // 使用OR条件连接多个状态
        qw.and(wrapper -> {
            for (int i = 0; i < combinedStatusList.size(); i++) {
                Integer combinedStatus = combinedStatusList.get(i);
                if (i > 0) {
                    wrapper.or();
                }
                wrapper.and(subWrapper -> addSingleCombinedPlanningStatusCondition(subWrapper, combinedStatus));
            }
        });
    }

    /**
     * 添加学习规划组合状态查询条件（单个状态）
     * 组合状态定义：
     * 0-待规划且未超时, 1-待规划且超时, 2-已规划且未超时, 3-已规划且超时
     * 超时状态通过实际规划时间和应规划时间判断
     */
    private void addSingleCombinedPlanningStatusCondition(QueryWrapper<StudyPlanningPending> qw, Integer combinedStatus) {
        Date now = new Date();
        switch (combinedStatus) {
            case 0: // 待规划且未超时
                qw.eq("spp.planning_status", 0);
                qw.isNull("spp.actual_planning_time"); // 实际规划时间为空
                qw.and(wrapper -> wrapper
                    .isNull("spp.expected_planning_time")
                    .or()
                    .ge("spp.expected_planning_time", now) // 应规划时间大于等于现在时间（未超时）
                );
                break;
            case 1: // 待规划且超时
                qw.eq("spp.planning_status", 0);
                qw.isNull("spp.actual_planning_time"); // 实际规划时间为空
                qw.isNotNull("spp.expected_planning_time");
                qw.lt("spp.expected_planning_time", now); // 现在时间大于应规划时间
                break;
            case 2: // 已规划且未超时
                qw.eq("spp.planning_status", 1);
                qw.and(wrapper -> wrapper
                    .isNull("spp.expected_planning_time")
                    .or()
                    .isNull("spp.actual_planning_time")
                    .or()
                    .apply("spp.actual_planning_time <= spp.expected_planning_time")
                );
                break;
            case 3: // 已规划且超时
                qw.eq("spp.planning_status", 1);
                qw.isNotNull("spp.expected_planning_time");
                qw.isNotNull("spp.actual_planning_time");
                qw.apply("spp.actual_planning_time > spp.expected_planning_time");
                break;
            default:
                // 无效状态，不添加条件
                break;
        }
    }

    /**
     * 添加学习规划反馈组合状态查询条件（多选）
     * 组合状态定义：
     * 0-待反馈且未超时, 1-待反馈且超时, 2-已反馈且未超时, 3-已反馈且超时
     * 超时状态通过实际反馈时间和应反馈时间判断
     */
    private void addCombinedFeedbackStatusConditions(QueryWrapper<StudyPlanningPending> qw, List<Integer> combinedStatusList) {
        if (CollUtil.isEmpty(combinedStatusList)) {
            return;
        }

        // 使用OR条件连接多个状态
        qw.and(wrapper -> {
            for (int i = 0; i < combinedStatusList.size(); i++) {
                Integer combinedStatus = combinedStatusList.get(i);
                if (i > 0) {
                    wrapper.or();
                }
                wrapper.and(subWrapper -> addSingleCombinedFeedbackStatusCondition(subWrapper, combinedStatus));
            }
        });
    }

    /**
     * 添加学习规划反馈组合状态查询条件（单个状态）
     * 组合状态定义：
     * 0-待反馈且未超时, 1-待反馈且超时, 2-已反馈且未超时, 3-已反馈且超时
     * 超时状态通过实际反馈时间和应反馈时间判断
     */
    private void addSingleCombinedFeedbackStatusCondition(QueryWrapper<StudyPlanningPending> qw, Integer combinedStatus) {
        Date now = new Date();
        switch (combinedStatus) {
            case 0: // 待反馈且未超时
                qw.eq("spp.feedback_status", 0);
                qw.isNull("spp.actual_feedback_time"); // 实际反馈时间为空
                qw.and(wrapper -> wrapper
                    .isNull("spp.expected_feedback_time")
                    .or()
                    .ge("spp.expected_feedback_time", now) // 应反馈时间大于等于现在时间（未超时）
                );
                break;
            case 1: // 待反馈且超时
                qw.eq("spp.feedback_status", 0);
                qw.isNull("spp.actual_feedback_time"); // 实际反馈时间为空
                qw.isNotNull("spp.expected_feedback_time");
                qw.lt("spp.expected_feedback_time", now); // 现在时间大于应反馈时间
                break;
            case 2: // 已反馈且未超时
                qw.eq("spp.feedback_status", 1);
                qw.and(wrapper -> wrapper
                    .isNull("spp.expected_feedback_time")
                    .or()
                    .isNull("spp.actual_feedback_time")
                    .or()
                    .apply("spp.actual_feedback_time <= spp.expected_feedback_time")
                );
                break;
            case 3: // 已反馈且超时
                qw.eq("spp.feedback_status", 1);
                qw.isNotNull("spp.expected_feedback_time");
                qw.isNotNull("spp.actual_feedback_time");
                qw.apply("spp.actual_feedback_time > spp.expected_feedback_time");
                break;
            default:
                // 无效状态，不添加条件
                break;
        }
    }

    /**
     * 设置超时状态
     * 参考 ServiceStatisticsServiceImpl 的超时判断逻辑
     */
    private void setTimeoutStatus(StudyPlanningPendingVo vo) {
        if (vo == null) {
            return;
        }

        Date now = new Date();

        // 设置学习规划超时状态
        vo.setOvertimePlanning(calculatePlanningTimeoutStatus(vo, now));

        // 设置学习规划反馈超时状态
        vo.setOvertimeFeedback(calculateFeedbackTimeoutStatus(vo, now));

        // 设置学习规划状态文字描述
        vo.setPlanningStatusText(getPlanningStatusText(vo));

        // 设置学习规划反馈状态文字描述
        vo.setFeedbackStatusText(getFeedbackStatusText(vo));
    }

    /**
     * 计算学习规划超时状态
     * 参考 ServiceStatisticsServiceImpl.isOvertimePlanning 和 isOvertimeUnplanned 方法
     */
    private Integer calculatePlanningTimeoutStatus(StudyPlanningPendingVo vo, Date now) {
        if (vo.getPlanningStatus() == null) {
            return 0;
        }

        if (vo.getPlanningStatus() == 1) {
            // 已规划状态：判断实际规划时间是否超过应规划时间
            return isOvertimePlanning(vo) ? 1 : 0;
        } else {
            // 待规划状态：判断当前时间是否超过应规划时间
            return isOvertimeUnplanned(vo, now) ? 1 : 0;
        }
    }

    /**
     * 计算学习规划反馈超时状态
     * 参考 ServiceStatisticsServiceImpl.isOvertimeActualFeedback 和 isOvertimeUnfeedback 方法
     */
    private Integer calculateFeedbackTimeoutStatus(StudyPlanningPendingVo vo, Date now) {
        if (vo.getFeedbackStatus() == null) {
            return 0;
        }

        if (vo.getFeedbackStatus() == 1) {
            // 已反馈状态：判断实际反馈时间是否超过应反馈时间
            return isOvertimeActualFeedback(vo) ? 1 : 0;
        } else {
            // 待反馈状态：判断当前时间是否超过应反馈时间
            return isOvertimeUnfeedback(vo, now) ? 1 : 0;
        }
    }

    /**
     * 判断是否超时规划
     * 已规划但实际规划时间超过应规划时间
     */
    private boolean isOvertimePlanning(StudyPlanningPendingVo vo) {
        return vo.getExpectedPlanningTime() != null &&
               vo.getActualPlanningTime() != null &&
               vo.getActualPlanningTime().after(vo.getExpectedPlanningTime());
    }

    /**
     * 判断是否超时未规划
     * 待规划状态且已超过应规划时间
     */
    private boolean isOvertimeUnplanned(StudyPlanningPendingVo vo, Date now) {
        return vo.getPlanningStatus() != null &&
               vo.getPlanningStatus() == 0 &&
               vo.getExpectedPlanningTime() != null &&
               now.after(vo.getExpectedPlanningTime());
    }

    /**
     * 判断是否超时反馈
     * 已反馈但实际反馈时间超过应反馈时间
     */
    private boolean isOvertimeActualFeedback(StudyPlanningPendingVo vo) {
        return vo.getExpectedFeedbackTime() != null &&
               vo.getActualFeedbackTime() != null &&
               vo.getActualFeedbackTime().after(vo.getExpectedFeedbackTime());
    }

    /**
     * 判断是否超时未反馈
     * 待反馈状态且已超过应反馈时间
     */
    private boolean isOvertimeUnfeedback(StudyPlanningPendingVo vo, Date now) {
        return vo.getFeedbackStatus() != null &&
               vo.getFeedbackStatus() == 0 &&
               vo.getExpectedFeedbackTime() != null &&
               now.after(vo.getExpectedFeedbackTime());
    }

    /**
     * 根据学生ID和反馈日期范围查找匹配的pending记录ID列表
     *
     * <p>查询条件：学生ID匹配，且pending的计划日期精确匹配反馈报告的周期（只比较年月日）</p>
     * <p>精确日期匹配：DATE(pending.planStartDate) = DATE(periodStart) AND DATE(pending.planEndDate) = DATE(periodEnd)</p>
     * <p>规划状态：包含待规划(0)和已规划(1)状态的数据</p>
     *
     * @param studentId 学生ID
     * @param periodStart 反馈周期开始日期
     * @param periodEnd 反馈周期结束日期
     * @return 匹配的pending记录ID列表
     */
    @Override
    public List<Long> findPendingIdsByStudentAndPeriod(Long studentId, Date periodStart, Date periodEnd) {
        if (studentId == null || periodStart == null || periodEnd == null) {
            return new ArrayList<>();
        }

        // 构建查询条件：学生ID匹配，且pending的计划日期精确匹配反馈报告的周期
        LambdaQueryWrapper<StudyPlanningPending> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudyPlanningPending::getStudentId, studentId);

        // 精确日期匹配（只比较年月日，忽略时分秒）：
        // DATE(pending.plan_start_date) = DATE(periodStart) AND DATE(pending.plan_end_date) = DATE(periodEnd)
        lqw.apply("DATE(plan_start_date) = DATE({0})", periodStart);
        lqw.apply("DATE(plan_end_date) = DATE({0})", periodEnd);

        // 包含待规划(0)和已规划(1)状态的数据
        lqw.in(StudyPlanningPending::getPlanningStatus, 1);

        // 按创建时间倒序排列
        lqw.orderByDesc(StudyPlanningPending::getCreateTime);

        List<StudyPlanningPending> pendingList = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(pendingList)) {
            return new ArrayList<>();
        }
        return pendingList.stream()
            .map(StudyPlanningPending::getId)
            .collect(Collectors.toList());
    }

    /**
     * 根据学生ID和学习规划日期范围查找匹配的pending记录ID列表（用于学习规划）
     *
     * <p>查询条件：学生ID匹配，且学习规划的日期范围与pending的计划日期范围有交集</p>
     * <p>范围匹配：学习规划日期范围[planningStart, planningEnd]与pending的[planStartDate, planEndDate]有交集</p>
     * <p>交集条件：planningStart <= planEndDate AND planningEnd >= planStartDate</p>
     *
     * @param studentId 学生ID
     * @param planningStart 学习规划开始日期
     * @param planningEnd 学习规划结束日期
     * @return 匹配的pending记录ID列表
     */
    @Override
    public List<Long> findPendingIdsByStudentAndPlanningPeriod(Long studentId, Date planningStart, Date planningEnd) {
        if (studentId == null || planningStart == null || planningEnd == null) {
            return new ArrayList<>();
        }

        // 构建查询条件：学生ID匹配，且学习规划的日期范围与pending的计划日期范围有交集
        LambdaQueryWrapper<StudyPlanningPending> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudyPlanningPending::getStudentId, studentId);

        // 范围匹配（只比较年月日，忽略时分秒）：
        // 学习规划日期范围[planningStart, planningEnd]与pending的[planStartDate, planEndDate]有交集
        // 交集条件：planningStart <= planEndDate AND planningEnd >= planStartDate
        lqw.apply("DATE({0}) <= DATE(plan_end_date)", planningStart);
        lqw.apply("DATE({0}) >= DATE(plan_start_date)", planningEnd);

        // 按创建时间倒序排列
        lqw.orderByDesc(StudyPlanningPending::getCreateTime);

        List<StudyPlanningPending> pendingList = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(pendingList)) {
            return new ArrayList<>();
        }
        return pendingList.stream()
            .map(StudyPlanningPending::getId)
            .collect(Collectors.toList());
    }

    /**
     * 批量查询多个学生在指定日期的pending记录
     *
     * <p>查询条件：学生ID在列表中，且学习规划的日期与pending的计划日期范围有交集</p>
     * <p>范围匹配：学习规划日期[planningDate]与pending的[planStartDate, planEndDate]有交集</p>
     * <p>交集条件：planningDate <= planEndDate AND planningDate >= planStartDate</p>
     *
     * @param studentIds 学生ID列表
     * @param planningDate 学习规划日期
     * @return 学生ID到pending记录ID列表的映射
     */
    @Override
    public Map<Long, List<Long>> findPendingIdsByStudentsAndDate(List<Long> studentIds, Date planningDate) {
        if (CollUtil.isEmpty(studentIds) || planningDate == null) {
            return new HashMap<>();
        }

        // 构建查询条件：学生ID在列表中，且学习规划的日期与pending的计划日期范围有交集
        LambdaQueryWrapper<StudyPlanningPending> lqw = Wrappers.lambdaQuery();
        lqw.in(StudyPlanningPending::getStudentId, studentIds);

        // 范围匹配（只比较年月日，忽略时分秒）：
        // 学习规划日期[planningDate]与pending的[planStartDate, planEndDate]有交集
        // 交集条件：planningDate <= planEndDate AND planningDate >= planStartDate
        lqw.apply("DATE({0}) <= DATE(plan_end_date)", planningDate);
        lqw.apply("DATE({0}) >= DATE(plan_start_date)", planningDate);

        // 按创建时间倒序排列
        lqw.orderByDesc(StudyPlanningPending::getCreateTime);

        List<StudyPlanningPending> pendingList = baseMapper.selectList(lqw);

        if (CollUtil.isEmpty(pendingList)) {
            return new HashMap<>();
        }

        // 按学生ID分组，返回学生ID到pending记录ID列表的映射
        return pendingList.stream()
            .collect(Collectors.groupingBy(
                StudyPlanningPending::getStudentId,
                Collectors.mapping(StudyPlanningPending::getId, Collectors.toList())
            ));
    }

    /**
     * 新增不存在的学习规划关联关系
     *
     * @param pendingId 待处理记录ID
     * @param planningRecordIds 学习规划记录ID列表
     */
    private void addMissingPlanningRelations(Long pendingId, List<Long> planningRecordIds) {
        try {
            // 查询已存在的关联关系
            LambdaQueryWrapper<StudyPlanningPendingRelation> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StudyPlanningPendingRelation::getPendingId, pendingId);
            List<StudyPlanningPendingRelation> existingRelations = relationMapper.selectList(queryWrapper);

            // 获取已存在的学习规划记录ID集合
            Set<Long> existingPlanningIds = existingRelations.stream()
                .map(StudyPlanningPendingRelation::getPlanningId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 找出需要新增的关联关系
            List<Long> missingPlanningIds = planningRecordIds.stream()
                .filter(planningId -> !existingPlanningIds.contains(planningId))
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(missingPlanningIds)) {
                // 批量新增缺失的关联关系
                List<StudyPlanningPendingRelation> newRelations = new ArrayList<>();
                for (Long planningRecordId : missingPlanningIds) {
                    StudyPlanningPendingRelation relation = new StudyPlanningPendingRelation();
                    relation.setPendingId(pendingId);
                    relation.setPlanningId(planningRecordId);
                    newRelations.add(relation);
                }

                // 批量插入新的关联关系
                for (StudyPlanningPendingRelation relation : newRelations) {
                    relationMapper.insert(relation);
                }

                log.info("成功新增缺失的学习规划关联记录, pendingId: {}, 新增关联数: {}, 已存在关联数: {}",
                    pendingId, missingPlanningIds.size(), existingPlanningIds.size());
            } else {
                log.debug("所有学习规划关联关系已存在，无需新增, pendingId: {}, 关联数: {}",
                    pendingId, existingPlanningIds.size());
            }

        } catch (Exception e) {
            log.error("新增学习规划关联关系失败, pendingId: {}, planningRecordIds: {}",
                pendingId, planningRecordIds, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取学习规划状态文字描述
     */
    private String getPlanningStatusText(StudyPlanningPendingVo vo) {
        if (vo.getPlanningStatus() == null) {
            return "未规划";
        }

        if (vo.getPlanningStatus() == 0) {
            // 待规划状态
            if (vo.getOvertimePlanning() != null && vo.getOvertimePlanning() == 1) {
                return "超时未规划";
            } else {
                return "未规划";
            }
        } else if (vo.getPlanningStatus() == 1) {
            // 已规划状态
            if (vo.getOvertimePlanning() != null && vo.getOvertimePlanning() == 1) {
                return "超时规划";
            } else {
                return "已规划";
            }
        }

        return "未规划";
    }

    /**
     * 获取学习规划反馈状态文字描述
     */
    private String getFeedbackStatusText(StudyPlanningPendingVo vo) {
        if (vo.getFeedbackStatus() == null) {
            return "未反馈";
        }

        if (vo.getFeedbackStatus() == 0) {
            // 待反馈状态
            if (vo.getOvertimeFeedback() != null && vo.getOvertimeFeedback() == 1) {
                return "超时未反馈";
            } else {
                return "未反馈";
            }
        } else if (vo.getFeedbackStatus() == 1) {
            // 已反馈状态
            if (vo.getOvertimeFeedback() != null && vo.getOvertimeFeedback() == 1) {
                return "超时反馈";
            } else {
                return "已反馈";
            }
        }

        return "未反馈";
    }

}
