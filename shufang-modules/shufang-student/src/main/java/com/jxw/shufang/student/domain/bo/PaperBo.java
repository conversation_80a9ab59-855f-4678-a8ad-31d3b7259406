package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Paper;

import java.util.List;

/**
 * 试卷业务对象 paper
 *
 *
 * @date 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Paper.class, reverseConvertGenerate = false)
public class PaperBo extends BaseEntity {

    /**
     * 试卷Id
     */
    @NotNull(message = "试卷Id不能为空", groups = { EditGroup.class })
    private Long paperId;

    /**
     * 学段，对应字典 paper_stage
     */
    @NotBlank(message = "学段不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperStage;

    /**
     * 分类，对应字典 paper_type
     */
    @NotBlank(message = "分类不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperType;

    /**
     * 年份
     */
    @NotBlank(message = "年份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperGrade;

    /**
     * 归属学科(科目)（对应字典值） paper_affiliation_subject
     */
    @NotBlank(message = "归属学科(科目)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperAffiliationSubject;

    /**
     * 地区，省市县之间用空格分割
     */
    @NotBlank(message = "地区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paperRegion;

    /**
     * 试卷标题
     */
    @NotBlank(message = "试卷标题不能为空", groups = { EditGroup.class })
    private String paperTitle;

    /**
     * 原卷ossId
     */
    @NotNull(message = "原卷ossId不能为空", groups = {  EditGroup.class })
    private Long original;

    /**
     * 解析ossId
     */
    @NotNull(message = "解析ossId不能为空", groups = {  EditGroup.class })
    private Long analysis;

    /**
     * 原卷带解析ossId
     */
    //@NotNull(message = "原卷带解析ossId不能为空", groups = {  EditGroup.class })
    private Long originalWithAnalysis;


    /**
     * 删除标志 0正常 2删除
     */
    private String delFlag;


    private List<PaperDocBo> paperDocList;

    /**
     * 试卷来源 1管理端 2店铺端
     */
    private String paperSource;

    /**
     * 原卷缩略图ossId
     */
    private Long originalThumbnail;

    /**
     * 门店Id
     */
    private Long branchId;

    /**
     * 是否收藏 true 已收藏 false 未收藏
     */
    private Boolean isCollect;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 试卷id列表
     */
    private List<Long> paperIdList;

    /**
     * 是否包含管理端试卷
     */
    private Boolean containManagement;

    private List<Long> branchIdList;

    private Boolean withBranchInfo;

}
