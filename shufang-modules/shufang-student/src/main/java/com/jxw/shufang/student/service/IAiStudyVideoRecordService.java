package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;
import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyVideoRecordVo;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * ai学习视频记录（视频观看记录）Service接口
 *
 * @date 2024-05-21
 */
public interface IAiStudyVideoRecordService {

    /**
     * 查询ai学习视频记录（视频观看记录）
     */
    AiStudyVideoRecordVo queryById(Long aiStudyVideoRecordId);

    /**
     * 查询ai学习视频记录（视频观看记录）列表
     */
    TableDataInfo<AiStudyVideoRecordVo> queryPageList(AiStudyVideoRecordBo bo, PageQuery pageQuery);

    /**
     * 查询ai学习视频记录（视频观看记录）列表
     */
    List<AiStudyVideoRecordVo> queryList(AiStudyVideoRecordBo bo);

    /**
     * 新增ai学习视频记录（视频观看记录）
     */
    Boolean insertByBo(AiStudyVideoRecordBo bo);

    /**
     * 修改ai学习视频记录（视频观看记录）
     */
    Boolean updateByBo(AiStudyVideoRecordBo bo);

    /**
     * 校验并批量删除ai学习视频记录（视频观看记录）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean saveBatch(List<AiStudyVideoRecord> insertList);

    Boolean updateBatchById(List<AiStudyVideoRecord> updateList);

    AiStudyVideoRecordVo queryOne(AiStudyVideoRecordBo aiStudyVideoRecordBo);

    AiStudyVideoRecordVo queryLastDayRecord(Long studentId, Long courseId);

    List<AiStudyVideoRecord> batchQueryByStudentIdAndVideoId(List<Long> studentIds, StudyModuleTypeEnum moduleTypeEnum, List<Long> videoIds);

    List<AiStudyVideoRecord> batchQueryByStudentIdAndCouredId(List<Long> studentIds, List<Long> courseIds, StudyModuleTypeEnum moduleTypeEnum);

    void batchUpdate(List<AiStudyVideoRecord> videoProcessorUpdates);
}
