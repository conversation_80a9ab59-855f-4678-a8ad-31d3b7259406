package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanningTemplate;

import java.util.List;

/**
 * 学习规划模板业务对象 study_planning_template
 *
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanningTemplate.class, reverseConvertGenerate = false)
public class StudyPlanningTemplateBo extends BaseEntity {

    /**
     * 学习规划模板id
     */
    @NotNull(message = "学习规划模板id不能为空", groups = { EditGroup.class })
    private Long studyPlanningTemplateId;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateName;

    /**
     * 模板描述
     */
    @NotBlank(message = "模板描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateInfo;

    /**
     * 模板说明
     */
    @NotBlank(message = "模板说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateDesc;

    /**
     * 课程学段（与课程共用一个字典）
     */
    @NotBlank(message = "课程学段（与课程共用一个字典）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String courseStage;

    /**
     * 课程学年（与课程共用一个字典），这里允许存在多个，使用逗号分隔
     */
    @NotBlank(message = "课程学年（与课程共用一个字典），这里允许存在多个，使用逗号分隔不能为空", groups = { AddGroup.class, EditGroup.class })
    private String courseGrades;

    /**
     * 模版上下架状态，0：下架，1：上架
     */
    @NotBlank(message = "模版上下架状态，0：下架，1：上架不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateStatus;

    /**
     * 模板来源，对应枚举类SourceEnum
     */
    //@NotBlank(message = "模板来源，对应枚举类SourceEnum不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceType;

    /**
     * 来源为门店的时候，这里关联门店id
     */
    //@NotNull(message = "来源为门店的时候，这里关联门店id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    @Valid
    private List<StudyPlanningTemplateInfoBo> studyPlanningTemplateInfoBoList;


    private Boolean withBranchInfo;


}
