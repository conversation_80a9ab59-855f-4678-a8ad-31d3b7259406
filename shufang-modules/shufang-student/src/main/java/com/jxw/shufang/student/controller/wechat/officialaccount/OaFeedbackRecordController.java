package com.jxw.shufang.student.controller.wechat.officialaccount;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.FeedbackRecordVo;
import com.jxw.shufang.student.service.IFeedbackRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 反馈记录
 * 前端访问路由地址为:/student/officialAccount/feedbackRecord
 *
 *
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/officialAccount/feedbackRecord")
public class OaFeedbackRecordController extends BaseController {

    private final IFeedbackRecordService feedbackRecordService;



    /**
     * 获取反馈记录详细信息
     *
     * @param feedbackRecordId 主键
     */
    @GetMapping("/queryById")
    public R<FeedbackRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     Long feedbackRecordId) {
        return R.ok(feedbackRecordService.queryById(feedbackRecordId, true,true));
    }


}
