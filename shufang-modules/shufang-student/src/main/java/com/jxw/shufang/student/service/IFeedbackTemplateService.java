package com.jxw.shufang.student.service;

import jakarta.validation.constraints.NotNull;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.FeedbackTemplate;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateSourceVo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 反馈模板Service接口
 *
 *
 * @date 2024-03-18
 */
public interface IFeedbackTemplateService {

    /**
     * 查询反馈模板
     */
    FeedbackTemplateVo queryById(Long feedbackTemplateId);

    /**
     * 查询反馈模板列表
     */
    TableDataInfo<FeedbackTemplateVo> queryPageList(FeedbackTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询反馈模板列表
     */
    List<FeedbackTemplateVo> queryList(FeedbackTemplateBo bo);

    /**
     * 新增反馈模板
     */
    Boolean insertByBo(FeedbackTemplateBo bo);

    /**
     * 修改反馈模板
     */
    Boolean updateByBo(FeedbackTemplateBo bo);

    /**
     * 校验并批量删除反馈模板信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    FeedbackTemplate queryFeedbackTemplateById(Long feedbackTemplateId);

    List<FeedbackTemplateSourceVo> sourceOptions();

    void cleanCache();

    Boolean stepTemplateUseCount(@NotNull(message = "主键不能为空") Long feedbackTemplateId);
}
