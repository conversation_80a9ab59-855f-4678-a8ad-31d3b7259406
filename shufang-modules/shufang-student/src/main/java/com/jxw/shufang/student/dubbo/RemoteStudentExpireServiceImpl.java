package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.student.api.RemoteStudentExpireService;
import com.jxw.shufang.student.service.IStudentExpireService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentExpireServiceImpl implements RemoteStudentExpireService {

    private final IStudentExpireService studentExpireService;

    @Override
    public Boolean removeStudentExpireRecord(Long studentId) {
        return studentExpireService.removeStudentExpireRecord(studentId);
    }

    @Override
    public List<Long> filterExpireStudentId(List<Long> studentIdList) {
        return studentExpireService.filterExpireStudentId(studentIdList);
    }

}
