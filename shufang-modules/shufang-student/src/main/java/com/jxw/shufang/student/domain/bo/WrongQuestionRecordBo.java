package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.WrongQuestionRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 错题记录业务对象 wrong_question_record
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WrongQuestionRecord.class, reverseConvertGenerate = false)
public class WrongQuestionRecordBo extends BaseEntity {

    /**
     * 错题id
     */
    @NotNull(message = "错题id不能为空", groups = { EditGroup.class })
    private Long wrongQuestionRecordId;

    private List<Long> wrongQuestionRecordIdList;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 学习规划记录ID
     */
    @NotNull(message = "学习规划记录ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 题目序号
     */
    @NotBlank(message = "题目序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    @NotBlank(message = "作答结果（对应字典值，如全错 半错）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String answerResult;

    /**
     * 来源类型（1测试 2练习）
     */
    private String sourceType;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程专题，对应字典course_special_topic（只有最顶层的课程才会存）
     */
    private String specialTopic;

    /**
     * 学科（对应字典值 course_affiliation_subject）（只有最顶层的课程才会存）
     */
    private String affiliationSubject;

    /**
     * 记录创建时间范围起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String recordCreateTimeStart;

    /**
     * 记录创建时间范围结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String recordCreateTimeEnd;

    private String studyPlanningRecordStatus;

    /**
     * 订正状态 0-否 1-是
     */
    private Integer reviseStatus;

    /**
     * 生成上周错题合集
     */
    private Integer lastWeek;

    /**
     * 开始时间
     */
    private LocalDateTime start;

    /**
     * 结束时间
     */
    private LocalDateTime end;

    /**
     * 错题合集id
     */
    private Long wrongQuestionCollectionId;

    private List<Long> questionIds;

}
