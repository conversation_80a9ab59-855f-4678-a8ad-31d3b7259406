package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import com.jxw.shufang.student.domain.vo.StudyTimeRankVo;
import com.jxw.shufang.student.mapper.StudyRecordMapper;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudentDataStatisticsService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.student.service.IStudyRecordService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学习记录Service业务层处理
 * @date 2024-05-06
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class StudyRecordServiceImpl implements IStudyRecordService, BaseService {

    private final StudyRecordMapper baseMapper;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentService studentService;

    private final IStudentDataStatisticsService studentDataStatisticsService;

    /**
     * 查询学习记录
     */
    @Override
    public StudyRecordVo queryById(Long studyRecordId){
        return baseMapper.selectVoById(studyRecordId);
    }

    @Override
    public StudyRecordVo queryByStudyPlanningRecordId(Long studyPlanningRecordId) {
        LambdaQueryWrapper<StudyRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StudyRecord::getStudyPlanningRecordId, studyPlanningRecordId);
        queryWrapper.last("limit 1");
        return baseMapper.selectVoOne(queryWrapper);
    }

    /**
     * 查询学习记录列表
     */
    @Override
    public TableDataInfo<StudyRecordVo> queryPageList(StudyRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        LambdaQueryWrapper<StudyRecord> lqw = buildQueryWrapper(bo);
        Page<StudyRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询学习记录列表
     */
    @Override
    public List<StudyRecordVo> queryList(StudyRecordBo bo) {
        handleQueryParam(bo);

        LambdaQueryWrapper<StudyRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }
    /**
     * 根据条件查询出来学习规划记录idList
     */
    @Override
    public List<Long> queryStudyPlanningRecordIdList(StudyRecordBo bo) {
        LambdaQueryWrapper<StudyRecord> lqw = buildQueryWrapper(bo);
        lqw.select(StudyRecord::getStudyPlanningRecordId);
        lqw.isNotNull(StudyRecord::getStudyPlanningRecordId);
        List<StudyRecordVo> studyRecordVos = baseMapper.selectVoList(lqw);
        if (null != studyRecordVos && !studyRecordVos.isEmpty()) {
            return studyRecordVos.stream().map(StudyRecordVo::getStudyPlanningRecordId).distinct().toList();
        }
        return List.of();
    }

    private LambdaQueryWrapper<StudyRecord> buildQueryWrapper(StudyRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudyRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, StudyRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudyVideoTotalDuration() != null, StudyRecord::getStudyVideoTotalDuration, bo.getStudyVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getPracticeState()), StudyRecord::getPracticeState, bo.getPracticeState());
        lqw.eq(bo.getPracticeRightNum() != null, StudyRecord::getPracticeRightNum, bo.getPracticeRightNum());
        lqw.eq(bo.getPracticeWrongNum() != null, StudyRecord::getPracticeWrongNum, bo.getPracticeWrongNum());
        lqw.eq(bo.getPracticeUnansweredNum() != null, StudyRecord::getPracticeUnansweredNum, bo.getPracticeUnansweredNum());
        lqw.eq(bo.getPracticeVideoTotalDuration() != null, StudyRecord::getPracticeVideoTotalDuration, bo.getPracticeVideoTotalDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getTestState()), StudyRecord::getTestState, bo.getTestState());
        lqw.eq(bo.getTestRightNum() != null, StudyRecord::getTestRightNum, bo.getTestRightNum());
        lqw.eq(bo.getTestWrongNum() != null, StudyRecord::getTestWrongNum, bo.getTestWrongNum());
        lqw.eq(bo.getTestUnansweredNum() != null, StudyRecord::getTestUnansweredNum, bo.getTestUnansweredNum());
        lqw.eq(bo.getTestVideoTotalDuration() != null, StudyRecord::getTestVideoTotalDuration, bo.getTestVideoTotalDuration());
        lqw.eq(bo.getStudyPlanningRecordId() != null,StudyRecord::getStudyPlanningRecordId,bo.getStudyPlanningRecordId());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()),StudyRecord::getStudentId,bo.getStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), StudyRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordIdList());
        lqw.gt(bo.getGtStudyVideoTotalDuration() != null, StudyRecord::getStudyVideoTotalDuration, bo.getGtStudyVideoTotalDuration());
        return lqw;
    }

    /**
     * 新增学习记录
     */
    @Override
    public Boolean insertByBo(StudyRecordBo bo) {
        StudyRecord add = MapstructUtils.convert(bo, StudyRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudyRecordId(add.getStudyRecordId());
        }
        return flag;
    }

    /**
     * 修改学习记录
     */
    @Override
    public Boolean updateByBo(StudyRecordBo bo) {
        StudyRecord update = MapstructUtils.convert(bo, StudyRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学习记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public StudyRecordVo queryOnce(StudyRecordBo studyRecordBo) {
        LambdaQueryWrapper<StudyRecord> lqw = buildQueryWrapper(studyRecordBo);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public String queryCorrectStatus(int queryType, Long studyPlanningRecordId,Long studentId) {
        LambdaQueryWrapper<StudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudyRecord::getStudyPlanningRecordId, studyPlanningRecordId);
        lqw.eq(StudyRecord::getStudentId, studentId);
        StudyRecordVo studyRecordVo = baseMapper.selectVoOne(lqw);
        if (studyRecordVo == null) {
            return null;
        }
        if (queryType == 1) {
            return studyRecordVo.getPracticeState();
        } else {
            return studyRecordVo.getTestState();
        }

    }

    @Override
    public Integer queryMyRank(StudyRecordBo bo, Long studentId) {
        StudyTimeRankVo studyTimeRankVo = studentDataStatisticsService.getStudyTimeRankList("W",1,new PageQuery());
        return studyTimeRankVo.getMyRankNo();
    }

    @Override
    public Long countStudentStudyTime(Long studentId, Date startDate, Date endDate) {
        return baseMapper.countStudentStudyTime(studentId,startDate,endDate);
    }

    @Override
    public void updateBatchById(List<StudyRecord> updated) {

        baseMapper.updateBatchById(updated);
    }

    @Override
    public List<StudyRecordVo> batchQueryListByPlanningIds(List<Long> studyPlanningRecordIds) {
        LambdaQueryWrapper<StudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(studyPlanningRecordIds), StudyRecord::getStudyPlanningRecordId, studyPlanningRecordIds);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Map<Long, StudyRecord> batchQueryMapByPlanningIds(List<Long> planningIds) {
        LambdaQueryWrapper<StudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(planningIds), StudyRecord::getStudyPlanningRecordId, planningIds);
        List<StudyRecord> studyRecordVos = baseMapper.selectList(lqw);
        return studyRecordVos.stream()
            .collect(Collectors.toMap(StudyRecord::getStudyPlanningRecordId, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public void batchUpdate(List<StudyRecord> updates) {
        if (CollUtil.isEmpty(updates)) {
            return;
        }
        updates.forEach(baseMapper::updateById);
    }

    @Override
    public void batchInsert(List<StudyRecord> inserts) {
        if (CollUtil.isEmpty(inserts)) {
            return;
        }
        inserts.forEach(baseMapper::insert);
    }

    @Override
    public List<StudyRecord> batchQuerySameStudentAiRecord(List<Long> studentIds, List<Long> courseIds) {
        if (CollUtil.isEmpty(studentIds) || CollUtil.isEmpty(courseIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StudyRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(StudyRecord::getStudentId, studentIds);
        lqw.in(StudyRecord::getCourseId, courseIds);
        lqw.isNull(StudyRecord::getStudyPlanningRecordId);
        return baseMapper.selectList(lqw);
    }


    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void handleQueryParam(StudyRecordBo record) {
        if (record.getStudentId() != null || CollUtil.isNotEmpty(record.getStudentIdList())) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else if (null != LoginHelper.getBranchId()) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        }

    }
}
