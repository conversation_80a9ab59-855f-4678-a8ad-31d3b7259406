package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 学习规划反馈报告关联学习记录对象 study_planning_feedback_pending_relation
 *
 * @date 2024-06-14
 */
@Data
@TableName("study_planning_feedback_pending_relation")
public class StudyPlanningFeedbackPendingRelation extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 反馈报告ID
     */
    private Long reportId;

    /**
     * 需学习规划记录ID
     */
    private Long pendingId;

}
