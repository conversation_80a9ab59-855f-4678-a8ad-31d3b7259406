package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGradeVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordBo;
import com.jxw.shufang.student.domain.vo.StudentPaperRecordVo;
import com.jxw.shufang.student.service.IStudentPaperRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户答题试卷记录
 * 前端访问路由地址为:/system/paperRecord
 *
 * <AUTHOR>
 * @date 2024-08-12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/paperRecord")
public class StudentPaperRecordController extends BaseController {

    private final IStudentPaperRecordService studentPaperRecordService;


    @DubboReference
    private RemoteCdsCommonService cdsCommonService;

    @GetMapping("/listSubjects")
    public R<List<RemoteSubjectVo>> listSubjects(@RequestParam(required = false) List<Integer> ids) {
        return R.ok(cdsCommonService.listSubjects(ids));
    }

    @GetMapping("/getGradeList")
    public R<List<RemoteGradeVo>> getGradeList(@RequestParam(required = false) List<Integer> ids) {
        return R.ok(cdsCommonService.getNewGradeList(ids));
    }


    /**
     * 查询用户答题试卷记录列表
     */
    @SaCheckPermission("student:paperRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudentPaperRecordVo> list(StudentPaperRecordBo bo, PageQuery pageQuery) {
        if (!ObjectUtils.isEmpty(bo)) {
            //默认展示已完成的
            if (bo.getRecordStatus() == null)
                bo.setRecordStatus(1);
        }
        return studentPaperRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户答题试卷记录列表
     */
    @SaCheckPermission("student:paperRecord:export")
    @Log(title = "用户答题试卷记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentPaperRecordBo bo, HttpServletResponse response) {
        List<StudentPaperRecordVo> list = studentPaperRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户答题试卷记录", StudentPaperRecordVo.class, response);
    }

    /**
     * 获取用户答题试卷记录详细信息
     *
     * @param studentPaperRecordId 主键
     */
    @SaCheckPermission("student:paperRecord:query")
    @GetMapping("/{studentPaperRecordId}")
    public R<StudentPaperRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long studentPaperRecordId) {
        return R.ok(studentPaperRecordService.queryById(studentPaperRecordId));
    }


    @SaCheckPermission("student:paperRecord:view")
    @GetMapping("/url/{studentPaperRecordId}")
    public R<String> queryByReportIdUrl(@NotNull(message = "主键不能为空")
                                        @PathVariable String studentPaperRecordId) {
        return R.ok("操作成功", studentPaperRecordService.queryByReportIdUrl(studentPaperRecordId));
    }

    /**
     * 修改用户答题试卷记录
     */
    @SaCheckPermission("student:paperRecord:edit")
    @Log(title = "用户答题试卷记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
//    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentPaperRecordBo bo) {
        return toAjax(studentPaperRecordService.updateByBo(bo));
    }

    /**
     * 删除用户答题试卷记录
     *
     * @param studentPaperRecordIds 主键串
     */
    @SaCheckPermission("student:paperRecord:remove")
    @Log(title = "用户答题试卷记录", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{studentPaperRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable String[] studentPaperRecordIds) {
        return toAjax(studentPaperRecordService.deleteWithValidByIds(List.of(studentPaperRecordIds), true));
    }
}
