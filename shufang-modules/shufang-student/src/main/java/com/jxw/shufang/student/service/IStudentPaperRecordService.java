package com.jxw.shufang.student.service;


import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentPaperRecordBo;
import com.jxw.shufang.student.domain.dto.AiPaperOssUrlDTO;
import com.jxw.shufang.student.domain.dto.AiRecordOssUrlDTO;
import com.jxw.shufang.student.domain.vo.StudentPaperRecordQuestionsVo;
import com.jxw.shufang.student.domain.vo.StudentPaperRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 用户答题试卷记录Service接口
 *
 */
public interface IStudentPaperRecordService {

    /**
     * 查询用户答题试卷记录
     */
    StudentPaperRecordVo queryById(Long studentPaperRecordId);

    String queryByReportIdUrl(String studentPaperRecordId);

    /**
     * 查询用户答题试卷记录列表
     */
    TableDataInfo<StudentPaperRecordVo> queryPageList(StudentPaperRecordBo bo, PageQuery pageQuery);

    /**
     * 查询用户答题试卷记录列表
     */
    List<StudentPaperRecordVo> queryList(StudentPaperRecordBo bo);

    /**
     * 新增用户答题试卷记录
     */
    StudentPaperRecordVo insertByBo(StudentPaperRecordBo bo);

    /**
     * 修改用户答题试卷记录
     */
    Boolean updateByBo(StudentPaperRecordBo bo);

    Boolean uploadFile(Long recordId, String url);

    /**
     * 校验并批量删除用户答题试卷记录信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    Boolean checkSignIsNotExist(Long userId);

    int finish(Long studentPaperRecordId);

    void checkSign(Long userId);

    int finishAndCalculate(Long studentPaperRecordId);

    StudentPaperRecordQuestionsVo getQuestionsAndKnowledge(Long studentPaperRecordId);

    List<AiRecordOssUrlDTO> getStudentRecordInfo(List<String> studentPaperRecordIds);

    List<StudentPaperRecordVo> getStudentRecordsByIds(List<String> studentPaperRecordIds);

    /**
     * 获取AI评测的试卷
     * @param studentPaperRecordIds
     * @return
     */
    List<AiPaperOssUrlDTO> getAiPaperInfo(List<String> studentPaperRecordIds);
}
