package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.lang.tree.Tree;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

// 科目观看范围

@Data
public class SubjectViewingRangeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    //课程树结构
    private List<Tree<Long>> courseTree;

    //授权的课程IDs
    private String studentAiCourseIds;


    //授权的课程记录 变更明细List
    private List<ChangeRecordInfoVo> changeList;

}
