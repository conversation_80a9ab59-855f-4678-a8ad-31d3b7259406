package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudyFeedbackReport;
import com.jxw.shufang.student.domain.StudyPlanningFeedbackPendingRelation;
import com.jxw.shufang.student.domain.bo.StudyFeedbackReportBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.mapper.StudyFeedbackReportMapper;
import com.jxw.shufang.student.mapper.StudyPlanningFeedbackPendingRelationMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习反馈报告Service业务层处理
 *
 * @date 2024-06-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StudyFeedbackReportServiceImpl implements IStudyFeedbackReportService {

    private final StudyFeedbackReportMapper baseMapper;
    private final StudyPlanningFeedbackPendingRelationMapper feedbackReportPendingRelationMapper;
    private final IStudyPlanningRecordService studyPlanningRecordService;
    private final IStudyPlanningPendingService studyPlanningPendingService;
    private final RemoteExtResourceService remoteExtResourceService;
    private final IStudentService studentService;
    private final IStudentConsultantRecordService studentConsultantRecordService;

    @DubboReference
    private RemoteWxService remoteWxService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    /**
     * 查询学习反馈报告
     */
    @Override
    public StudyFeedbackReportVo queryById(Long id) {
        StudyFeedbackReportVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            // 查询关联的学习规划记录
            List<StudyPlanningFeedbackPendingRelation> reportRecords = feedbackReportPendingRelationMapper.selectList(
                Wrappers.lambdaQuery(StudyPlanningFeedbackPendingRelation.class)
                    .eq(StudyPlanningFeedbackPendingRelation::getReportId, id)
            );
            if (CollUtil.isNotEmpty(reportRecords)) {
                List<Long> pendingIds = reportRecords.stream()
                    .map(StudyPlanningFeedbackPendingRelation::getPendingId)
                    .collect(Collectors.toList());
                // 这里可以调用学习规划记录服务获取详细信息
                // List<StudyPlanningRecordVo> planningRecords = studyPlanningRecordService.queryByIds(pendingIds);
                // vo.setPlanningRecords(planningRecords);
            }
        }
        return vo;
    }

    @Override
    public StudyFeedbackReportVo queryH5ById(Long id) {
        if (id == null) {
            return null;
        }

        // 1. 查询反馈报告基本信息
        StudyFeedbackReportVo reportVo = baseMapper.selectVoById(id);
        if (reportVo == null) {
            return null;
        }

        // 2. 直接通过反馈报告的日期范围和学生ID查询学习规划记录
        if (reportVo.getPeriodStart() != null && reportVo.getPeriodEnd() != null && reportVo.getStudentId() != null) {
            // 构建查询条件
            StudyPlanningRecordBo queryBo = new StudyPlanningRecordBo();
            queryBo.setStudentId(reportVo.getStudentId());
            queryBo.setStudyPlanningDateStart(DateUtil.formatDate(reportVo.getPeriodStart()));
            queryBo.setStudyPlanningDateEnd(DateUtil.formatDate(reportVo.getPeriodEnd()));
            queryBo.setWithCourseDetail(true); // 需要课程详情信息

            // 3. 查询学习规划记录详细信息
            List<StudyPlanningRecordVo> planningRecords = studyPlanningRecordService.queryStudyPlanningRecordList(queryBo);

            if (CollUtil.isNotEmpty(planningRecords)) {
                // 4. 按照学习开始时间排序
                planningRecords.sort((a, b) -> {
                    // 首先按照studyplan主体时间（学习规划日期）正序排序
                    Date aStudyPlanDate = a.getStudyPlanningDate();
                    Date bStudyPlanDate = b.getStudyPlanningDate();

                    if (aStudyPlanDate == null && bStudyPlanDate == null) {
                        // 如果两个学习规划日期都为空，则按studyplanrecord的时间排序
                        return compareStudyStartTime(a, b);
                    }
                    if (aStudyPlanDate == null) {
                        return 1; // 空值排在后面
                    }
                    if (bStudyPlanDate == null) {
                        return -1; // 空值排在后面
                    }

                    int dateComparison = aStudyPlanDate.compareTo(bStudyPlanDate);
                    if (dateComparison != 0) {
                        return dateComparison; // 学习规划日期不同，直接返回比较结果
                    }

                    // 学习规划日期相同，则按studyplanrecord的时间（学习开始时间）正序排序
                    return compareStudyStartTime(a, b);
                });

                // 5. 设置学习计划信息
                reportVo.setPlanningRecords(planningRecords);

                // 6. 提取学习资料信息（讲义）
                List<RemoteKnowledgeResourceVo> studyMaterials = getStudyMaterials(planningRecords);

                // 转换为URL列表用于前端展示
                List<String> materialUrls = studyMaterials.stream()
                    .map(RemoteKnowledgeResourceVo::getUrl)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

                // 设置完整的资料信息
                reportVo.setStudyMaterials(studyMaterials);
                // 设置URL列表用于前端预览链接
                reportVo.setMaterialUrls(materialUrls);
            }
        }

        // 7. 填充学生姓名和顾问姓名到最外层
        populateStudentAndConsultantNames(reportVo);

        return reportVo;
    }

    /**
     * 查询学习反馈报告列表
     */
    @Override
    public TableDataInfo<StudyFeedbackReportVo> queryPageList(StudyFeedbackReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudyFeedbackReport> lqw = buildQueryWrapper(bo);
        Page<StudyFeedbackReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询学习反馈报告列表
     */
    @Override
    public List<StudyFeedbackReportVo> queryList(StudyFeedbackReportBo bo) {
        LambdaQueryWrapper<StudyFeedbackReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudyFeedbackReport> buildQueryWrapper(StudyFeedbackReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyFeedbackReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudyFeedbackReport::getStudentId, bo.getStudentId());
        lqw.eq(bo.getConsultantId() != null, StudyFeedbackReport::getConsultantId, bo.getConsultantId());
        lqw.eq(bo.getPeriodStart() != null, StudyFeedbackReport::getPeriodStart, bo.getPeriodStart());
        lqw.eq(bo.getPeriodEnd() != null, StudyFeedbackReport::getPeriodEnd, bo.getPeriodEnd());
        lqw.eq(bo.getStatus() != null, StudyFeedbackReport::getStatus, bo.getStatus());
        lqw.eq(bo.getFeedbackStatus() != null, StudyFeedbackReport::getFeedbackStatus, bo.getFeedbackStatus());
        lqw.orderByDesc(StudyFeedbackReport::getCreateTime);
        return lqw;
    }

    /**
     * 新增学习反馈报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(StudyFeedbackReportBo bo) {
        StudyFeedbackReport add = MapstructUtils.convert(bo, StudyFeedbackReport.class);
        validEntityBeforeSave(add);

        // 设置默认值
        if (add.getStatus() == null) {
            add.setStatus(2); // 默认发布
        }
        if (add.getFeedbackStatus() == null) {
            add.setFeedbackStatus(0); // 默认待反馈
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());

            // 根据学生ID和反馈日期查找对应的pending记录并保存关联关系
            if (add.getStudentId() != null && add.getPeriodStart() != null && add.getPeriodEnd() != null) {
                List<Long> pendingIds = findPendingIdsByStudentAndPeriod(add.getStudentId(), add.getPeriodStart(), add.getPeriodEnd());
                if (CollUtil.isNotEmpty(pendingIds)) {
                    saveFeedbackReportRecords(add.getId(), pendingIds);
                }
            }
        }
        return flag;
    }

    /**
     * 修改学习反馈报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(StudyFeedbackReportBo bo) {
        StudyFeedbackReport update = MapstructUtils.convert(bo, StudyFeedbackReport.class);
        validEntityBeforeSave(update);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 根据学生ID和反馈日期查找对应的pending记录ID列表
     *
     * @param studentId 学生ID
     * @param periodStart 反馈周期开始日期
     * @param periodEnd 反馈周期结束日期
     * @return pending记录ID列表
     */
    private List<Long> findPendingIdsByStudentAndPeriod(Long studentId, Date periodStart, Date periodEnd) {
        // 直接调用StudyPlanningPendingService的方法，在数据库层面完成查询和过滤
        return studyPlanningPendingService.findPendingIdsByStudentAndPeriod(studentId, periodStart, periodEnd);
    }

    /**
     * 保存反馈报告关联记录
     */
    private void saveFeedbackReportRecords(Long reportId, List<Long> pendingIds) {
        List<StudyPlanningFeedbackPendingRelation> records = pendingIds.stream()
            .map(pendingId -> {
                StudyPlanningFeedbackPendingRelation record = new StudyPlanningFeedbackPendingRelation();
                record.setReportId(reportId);
                record.setPendingId(pendingId);
                return record;
            })
            .collect(Collectors.toList());
        feedbackReportPendingRelationMapper.insertBatch(records);

    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyFeedbackReport entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学习反馈报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }

        // 删除关联记录
        feedbackReportPendingRelationMapper.delete(
            Wrappers.lambdaQuery(StudyPlanningFeedbackPendingRelation.class)
                .in(StudyPlanningFeedbackPendingRelation::getReportId, ids)
        );

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据学生ID和时间范围查询报告
     */
    @Override
    public StudyFeedbackReportVo queryByStudentAndPeriod(Long studentId, String periodStart, String periodEnd) {
        LambdaQueryWrapper<StudyFeedbackReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudyFeedbackReport::getStudentId, studentId);
        lqw.eq(StringUtils.isNotBlank(periodStart), StudyFeedbackReport::getPeriodStart, periodStart);
        lqw.eq(StringUtils.isNotBlank(periodEnd), StudyFeedbackReport::getPeriodEnd, periodEnd);
        lqw.orderByDesc(StudyFeedbackReport::getCreateTime);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 更新反馈状态
     *
     * <p>当学生或家长确认查看学习反馈报告后，更新反馈状态为已反馈。
     * 此方法会同时更新反馈报告本身的状态以及关联的学习规划待处理记录状态。</p>
     *
     * <p><strong>业务逻辑：</strong></p>
     * <ul>
     *   <li>1. 更新学习反馈报告的反馈状态</li>
     *   <li>2. 查找通过 study_planning_feedback_pending_relation 关联的学习规划待处理记录</li>
     *   <li>3. 更新关联记录的反馈状态为已反馈，并设置实际反馈时间</li>
     *   <li>4. 确保学习规划待处理列表中的反馈状态能够正确显示</li>
     * </ul>
     *
     * @param id             学习反馈报告ID
     * @param feedbackStatus 反馈状态：0-待反馈，1-已反馈
     * @return 更新是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFeedbackStatus(Long id, Integer feedbackStatus) {
        try {
            // 1. 更新反馈报告状态
            StudyFeedbackReport report = new StudyFeedbackReport();
            report.setId(id);
            report.setFeedbackStatus(feedbackStatus);
            boolean updateResult = baseMapper.updateById(report) > 0;

            if (!updateResult) {
                log.error("更新学习反馈报告状态失败, reportId: {}, feedbackStatus: {}", id, feedbackStatus);
                return false;
            }

            // 2. 查询关联的学习规划待处理记录ID
            List<StudyPlanningFeedbackPendingRelation> relations = feedbackReportPendingRelationMapper.selectList(
                Wrappers.lambdaQuery(StudyPlanningFeedbackPendingRelation.class)
                    .eq(StudyPlanningFeedbackPendingRelation::getReportId, id)
            );

            // 3. 如果反馈状态为已反馈(1)，则更新关联的学习规划待处理记录
            if (feedbackStatus == 1 && CollUtil.isNotEmpty(relations)) {
                log.info("开始更新关联的学习规划待处理记录状态, reportId: {}, 关联记录数: {}", id, relations.size());

                for (StudyPlanningFeedbackPendingRelation relation : relations) {
                    try {
                        // 直接根据pendingId标记为已反馈，如果已经反馈过了则不会重复更新
                       studyPlanningPendingService.markAsFeedbackByPendingId(relation.getPendingId());
                    } catch (Exception e) {
                        log.error("处理学习规划待处理记录失败, pendingId: {}, reportId: {}", relation.getPendingId(), id, e);
                        // 继续处理其他记录，不中断整个流程
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("更新反馈状态失败, reportId: {}, feedbackStatus: {}", id, feedbackStatus, e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 获取学习资料（讲义）
     */
    private List<RemoteKnowledgeResourceVo> getStudyMaterials(List<StudyPlanningRecordVo> planningRecords) {
        if (CollectionUtils.isEmpty(planningRecords)) {
            return new ArrayList<>();
        }

        // 提取knowledgeIdList
        List<Long> knowledgeIdList = planningRecords.stream()
            .map(StudyPlanningRecordVo::getCourse)
            .filter(v -> !ObjectUtils.isEmpty(v))
            .map(CourseVo::getKnowledgeId)
            .filter(v -> !ObjectUtils.isEmpty(v))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(knowledgeIdList)) {
            return new ArrayList<>();
        }

        // 构建查询条件，获取讲义资料
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setTypeList(List.of(KnowledgeResourceType.HANDOUT.getType()));
        remoteKnowledgeResourceBo.setKnowledgeIdList(knowledgeIdList);

        // 获取课程的讲义文件
        return getRemoteKnowledgeResourceVosList(remoteKnowledgeResourceBo);
    }

    /**
     * 获取远程知识资源列表
     */
    private List<RemoteKnowledgeResourceVo> getRemoteKnowledgeResourceVosList(RemoteKnowledgeResourceBo remoteKnowledgeResourceBo) {
        try {
            List<RemoteGroupResourceVo> knowledgeResourceList = remoteExtResourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);
            if (CollectionUtils.isEmpty(knowledgeResourceList)) {
                return new ArrayList<>();
            }
            return knowledgeResourceList.stream()
                .map(RemoteGroupResourceVo::getKnowledgeResource)
                .filter(v -> !ObjectUtils.isEmpty(v))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取学习资料失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 填充学生姓名和顾问姓名到最外层
     */
    private void populateStudentAndConsultantNames(StudyFeedbackReportVo reportVo) {
        if (reportVo == null) {
            return;
        }

        // 填充学生姓名
        if (reportVo.getStudentId() != null) {
            try {
                Map<Long, String> studentNameMap = studentService.queryStudentName(List.of(reportVo.getStudentId()));
                if (studentNameMap != null && !studentNameMap.isEmpty()) {
                    reportVo.setStudentName(studentNameMap.get(reportVo.getStudentId()));
                }
            } catch (Exception e) {
                log.error("获取学生姓名失败, studentId: {}", reportVo.getStudentId(), e);
            }
        }

        // 填充顾问姓名 - 通过Student获取对应的会员顾问
        if (reportVo.getStudentId() != null) {
            try {
                // 通过学生ID获取对应的会员顾问ID
                Map<Long, Long> studentConsultantMap = studentConsultantRecordService.getStudentConsultantIdMap(List.of(reportVo.getStudentId()));
                Long consultantId = studentConsultantMap.get(reportVo.getStudentId());

                if (consultantId != null) {
                    RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
                    remoteStaffBo.setBranchStaffId(consultantId);
                    remoteStaffBo.setWithSysUserInfo(true); // 需要用户信息来获取姓名
                    List<RemoteStaffVo> staffList = remoteStaffService.queryStaffList(remoteStaffBo);

                    if (CollUtil.isNotEmpty(staffList)) {
                        RemoteStaffVo staff = staffList.get(0);
                        if (staff.getUser() != null && StringUtils.isNotBlank(staff.getUser().getNickName())) {
                            reportVo.setConsultantName(staff.getUser().getNickName());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("获取顾问姓名失败, studentId: {}", reportVo.getStudentId(), e);
            }
        }
    }

    /**
     * 获取学习规划H5地址
     */
    @Override
    public String getStudyPlanningUrl(Long studyPlanningId) {
        return remoteWxService.getStudyPlanningUrl(studyPlanningId);
    }

    /**
     * 比较学习开始时间的辅助方法
     */
    private int compareStudyStartTime(StudyPlanningRecordVo a, StudyPlanningRecordVo b) {
        Date aStudyStartTime = a.getStudyStartTime();
        Date bStudyStartTime = b.getStudyStartTime();

        if (aStudyStartTime == null && bStudyStartTime == null) {
            return 0;
        }
        if (aStudyStartTime == null) {
            return 1; // 空值排在后面
        }
        if (bStudyStartTime == null) {
            return -1; // 空值排在后面
        }
        return aStudyStartTime.compareTo(bStudyStartTime);
    }

}
