package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.NewStudyPlanningRecordBO;
import com.jxw.shufang.student.domain.dto.CourseWithTopCourseDTO;
import com.jxw.shufang.student.domain.dto.StudyPlanningRecordInfoVO;
import com.jxw.shufang.student.domain.vo.MergeStudyPlanningRecordVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/2/15 10:49
 * @Version 1
 * @Description
 */
public interface INewStudyPlanningRecordService {

    /**
     * 查询合并会员的学习规划记录
     * @param bo 请求参数
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<MergeStudyPlanningRecordVO> queryMergeStudyPlanningRecordPage(NewStudyPlanningRecordBO bo, PageQuery pageQuery);

    Map<Long, CourseWithTopCourseDTO> getCourseMap(Set<Long> courseIdSet);

    /**
     * 设置学习规划记录中课程完成状态
     * 通用方法，直接操作记录列表
     *
     * @param recordInfoList 学习规划记录信息列表
     */
    void setCourseCompleteStatus(List<StudyPlanningRecordInfoVO> recordInfoList);
}
