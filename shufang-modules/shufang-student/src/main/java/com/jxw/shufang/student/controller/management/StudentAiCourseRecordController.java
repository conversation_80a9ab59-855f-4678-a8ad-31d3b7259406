package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordBo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordVo;
import com.jxw.shufang.student.service.IStudentAiCourseRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
 * 前端访问路由地址为:/student/management/studentAiCourseRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studentAiCourseRecord")
public class StudentAiCourseRecordController extends BaseController {

    private final IStudentAiCourseRecordService studentAiCourseRecordService;

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:studentAiCourseRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudentAiCourseRecordVo> list(StudentAiCourseRecordBo bo, PageQuery pageQuery) {
        return studentAiCourseRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:studentAiCourseRecord:export")
    @Log(title = "会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentAiCourseRecordBo bo, HttpServletResponse response) {
        List<StudentAiCourseRecordVo> list = studentAiCourseRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）", StudentAiCourseRecordVo.class, response);
    }

    /**
     * 获取会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）详细信息
     *
     * @param studentAiCourseRecordId 主键
     */
    @SaCheckPermission("student:studentAiCourseRecord:query")
    @GetMapping("/{studentAiCourseRecordId}")
    public R<StudentAiCourseRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentAiCourseRecordId) {
        return R.ok(studentAiCourseRecordService.queryById(studentAiCourseRecordId));
    }

    /**
     * 新增会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:studentAiCourseRecord:add")
    @Log(title = "会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentAiCourseRecordBo bo) {
        return toAjax(studentAiCourseRecordService.insertByBo(bo));
    }

    /**
     * 修改会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:studentAiCourseRecord:edit")
    @Log(title = "会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentAiCourseRecordBo bo) {
        return toAjax(studentAiCourseRecordService.updateByBo(bo));
    }

    /**
     * 删除会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     *
     * @param studentAiCourseRecordIds 主键串
     */
    @SaCheckPermission("student:studentAiCourseRecord:remove")
    @Log(title = "会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentAiCourseRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentAiCourseRecordIds) {
        return toAjax(studentAiCourseRecordService.deleteWithValidByIds(List.of(studentAiCourseRecordIds), true));
    }
}
