package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

//门店数据 （学习及测验和练习 测验批改率 学习超过视频时长50%未完成测验 有X位学员学习计划不足）

@Data
public class BranchDataVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    //学习及测验和练习
    private Integer studyNum;

    //测验批改率
    private String testCorrectProportion;

    //学习超过视频时长50未完成测验
    private Integer notFinishTestNum;

    //有X位学员学习计划不足
    private Integer notScheduleNum;

    //有X位学员学习计划不足 详情
    private String notScheduleInfo;

}
