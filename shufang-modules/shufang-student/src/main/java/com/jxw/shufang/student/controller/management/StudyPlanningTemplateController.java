package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo;
import com.jxw.shufang.student.service.IStudyPlanningTemplateInfoService;
import com.jxw.shufang.student.service.IStudyPlanningTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习规划模板
 * 前端访问路由地址为:/student/management/studyPlanningTemplate
 *
 *
 * @date 2024-06-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studyPlanningTemplate")
public class StudyPlanningTemplateController extends BaseController {

    private final IStudyPlanningTemplateService studyPlanningTemplateService;

    private final IStudyPlanningTemplateInfoService studyPlanningTemplateInfoService;

    /**
     * 查询学习规划模板列表
     */
    @SaCheckPermission("student:studyPlanningTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<StudyPlanningTemplateVo> list(StudyPlanningTemplateBo bo, PageQuery pageQuery) {
        return studyPlanningTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出学习规划模板列表
     */
    @SaCheckPermission("student:studyPlanningTemplate:export")
    @Log(title = "学习规划模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyPlanningTemplateBo bo, HttpServletResponse response) {
        List<StudyPlanningTemplateVo> list = studyPlanningTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习规划模板", StudyPlanningTemplateVo.class, response);
    }

    /**
     * 获取学习规划模板详细信息
     *
     * @param studyPlanningTemplateId 主键
     */
    @SaCheckPermission("student:studyPlanningTemplate:query")
    @GetMapping("/{studyPlanningTemplateId}")
    public R<StudyPlanningTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studyPlanningTemplateId,Boolean  withTemplateInfo) {
        return R.ok( studyPlanningTemplateService.queryById(studyPlanningTemplateId, withTemplateInfo));
    }

    /**
     * 新增学习规划模板
     */
    @SaCheckPermission("student:studyPlanningTemplate:add")
    @Log(title = "学习规划模板", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyPlanningTemplateBo bo) {
        return toAjax(studyPlanningTemplateService.insertByBo(bo));
    }

    /**
     * 修改学习规划模板
     */
    @SaCheckPermission("student:studyPlanningTemplate:edit")
    @Log(title = "学习规划模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyPlanningTemplateBo bo) {
        return toAjax(studyPlanningTemplateService.updateByBo(bo));
    }

    /**
     * 修改上下架状态
     */
    @SaCheckPermission("student:studyPlanningTemplate:edit")
    @Log(title = "学习规划模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/changeStatus")
    public R<Void> changeStatus( @RequestBody StudyPlanningTemplateBo bo) {
        return toAjax(studyPlanningTemplateService.changeStatus(bo));
    }

    /**
     * 删除学习规划模板
     *
     * @param studyPlanningTemplateIds 主键串
     */
    @SaCheckPermission("student:studyPlanningTemplate:remove")
    @Log(title = "学习规划模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyPlanningTemplateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyPlanningTemplateIds) {
        return toAjax(studyPlanningTemplateService.deleteWithValidByIds(List.of(studyPlanningTemplateIds), true));
    }
}
