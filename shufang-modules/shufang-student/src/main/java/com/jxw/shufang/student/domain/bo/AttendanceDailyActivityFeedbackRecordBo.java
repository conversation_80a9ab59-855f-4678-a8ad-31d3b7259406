package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = AttendanceDailyActivityFeedbackRecord.class)
public class AttendanceDailyActivityFeedbackRecordBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long attendanceDailyActivityFeedbackRecordId;

    /**
     * 每日会员打卡登录详情表id
     */
    private Long attendanceDailyActivityId;

    /**
     * 反馈状态 1未反馈 2已反馈 3超时反馈 4超时未反馈
     */
    private Integer feedbackStatus;

    /**
     * 发布状态（1：暂存；2：未发布 3：已发布）
     */
    private Integer publishStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Long feedbackRecordId;

}
