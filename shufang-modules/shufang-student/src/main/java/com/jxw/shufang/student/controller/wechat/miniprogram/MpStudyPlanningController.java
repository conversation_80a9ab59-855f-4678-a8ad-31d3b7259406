package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudyPlanningService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学习规划---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/studyPlanning
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/studyPlanning")
public class MpStudyPlanningController extends BaseController {

    private final IStudyPlanningService studyPlanningService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    /**
     * 查询学习规划对应的知识点的题目列表
     *
     * @param studyPlanningRecordId 研究计划记录id
     * @param resourceType           测试 TEST    练习 PRACTICE
     * @param withAnalysis          带分析
     * @param withAnswer            有答案
     *
     * @date 2024/05/09 04:03:47
     */
    @GetMapping("/getKnowledgeQuestionList")
    public R<List<RemoteQuestionVo>> getKnowledgeQuestionList(Long studyPlanningRecordId,
                                                              Long courseId,
                                                              @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType,
                                                              Boolean withAnalysis,
                                                              Boolean withAnswer) {
        if(null == studyPlanningRecordId && null == courseId){
            return R.fail("参数有误");
        }


        return R.ok(studyPlanningService.getKnowledgeQuestionList(studyPlanningRecordId,courseId,resourceType, withAnalysis, withAnswer));
    }


    /**
     * 查询学习规划对应的课程外部资源（讲义，练习，测试，练习带解析，测试带解析）
     *
     * @param studyPlanningRecordId 学习规划记录Id
     * @param resourceType          测试 TEST    练习 PRACTICE 练习带解析 PRACTICE_ANALYSIS 测试带解析 TEST_ANALYSIS
     *
     * @date 2024/05/09 03:20:51
     */
    @GetMapping("/getExtResource")
    public R<RemoteKnowledgeResourceVo> getExtResource(@NotNull(message = "学习规划记录Id不能为空") Long studyPlanningRecordId, @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType) {
        return R.ok(studyPlanningService.getExtResource(studyPlanningRecordId, resourceType));
    }

    /**
     * 获取某一天的学习计划和记录列表
     *
     *
     * @date 2024/06/07 02:42:27
     */
    @GetMapping("/queryStudyPlanPage")
    public TableDataInfo<StudyPlanningVo> queryStudyPlanPage(StudyPlanningBo studyPlanningBo, PageQuery pageQuery) {
        if (!LoginHelper.isBranchStaff()){
            throw new ServiceException("非门店员工，无权访问此接口");
        }
        return studyPlanningService.queryStudyPlanPage(studyPlanningBo,pageQuery);
    }



}
