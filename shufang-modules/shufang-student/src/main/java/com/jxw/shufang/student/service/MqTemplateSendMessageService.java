package com.jxw.shufang.student.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/4/22 17:29
 * @Version 1
 * @Description
 */
@Service
@Slf4j
public class MqTemplateSendMessageService {

    @Resource
    private RocketMQTemplate rocketMQTemplate;


    /**
     * 发送异步消息
     * @param topicAndTagFlag
     * @param data
     */
    public void sendAsyncMq(String topicAndTagFlag, Object data) {
        rocketMQTemplate.asyncSend(topicAndTagFlag, data, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送成功:{}", topicAndTagFlag);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("发送失败:{},异常原因:{}", topicAndTagFlag,throwable.getMessage());
            }
        });
    }
}
