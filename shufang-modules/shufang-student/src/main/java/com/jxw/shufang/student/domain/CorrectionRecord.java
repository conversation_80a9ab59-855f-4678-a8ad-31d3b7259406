package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 批改记录对象 correction_record
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("correction_record")
public class CorrectionRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批改记录id
     */
    @TableId(value = "correction_record_id")
    private Long correctionRecordId;

    /**
     * 学习规划记录ID
     */
    private Long studyPlanningRecordId;

    /**
     * 会员Id
     */
    private Long studentId;

    /**
     * 批改类型（1测试 2练习）
     */
    private String correctionType;

    /**
     * 批改人类型（1顾问 2会员）
     */
    private String correctionPersonType;

    /**
     * 批改截图（oss_id，多个，逗号隔开）
     */
    private String correctionScreenshots;


}
