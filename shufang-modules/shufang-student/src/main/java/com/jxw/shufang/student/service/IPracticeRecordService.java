package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.PracticeRecordBo;
import com.jxw.shufang.student.domain.vo.PracticeRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 练习记录Service接口
 *
 *
 * @date 2024-05-09
 */
public interface IPracticeRecordService {

    /**
     * 查询练习记录
     */
    PracticeRecordVo queryById(Long practiceRecordId);

    /**
     * 查询练习记录列表
     */
    TableDataInfo<PracticeRecordVo> queryPageList(PracticeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询练习记录列表
     */
    List<PracticeRecordVo> queryList(PracticeRecordBo bo);

    /**
     * 新增练习记录
     */
    Boolean insertByBo(PracticeRecordBo bo);

    /**
     * 修改练习记录
     */
    Boolean updateByBo(PracticeRecordBo bo);

    /**
     * 校验并批量删除练习记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<PracticeRecordBo> convert);
}
