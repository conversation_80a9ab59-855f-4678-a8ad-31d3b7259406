package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 允许自主批改对象 allow_own_correction
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("allow_own_correction")
public class AllowOwnCorrection extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 允许自主批改id
     */
    @TableId(value = "allow_own_correction_id")
    private Long allowOwnCorrectionId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 应用类型，1学习规划  2ai学习
     */
    private String  type;

    /**
     * 允许类型，1练习  2测试
     */
    private String allowType;
}
