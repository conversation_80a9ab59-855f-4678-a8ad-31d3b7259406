package com.jxw.shufang.student.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.RemoteBranchControlStudentService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.RemoteIntegralService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.branch.api.domain.vo.RemotePayMerchantConfigVO;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.crm.api.RemoteCrmStudentService;
import com.jxw.shufang.extresource.api.RemoteSmsService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.RemoteStudentMembershipService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.api.RemoteStudentConsultantRecordService;
import com.jxw.shufang.student.config.StudentIntroduceConfig;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.dto.StudentMerchantConfigDTO;
import com.jxw.shufang.student.domain.dto.StudentRemainingTimeDTO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.StudentMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.RemoteDictService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.api.domain.vo.RemoteDictDataVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 会员Service业务层处理
 *
 * @date 2024-02-27
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
@Slf4j
public class StudentServiceImpl implements IStudentService, BaseService {

    private final StudentMapper baseMapper;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentInfoService studentInfoService;

    private final IStudentSignService studentSignService;

    private final IStudyVideoRecordService studyVideoRecordService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IStudentAiCourseRecordInfoService studentAiCourseRecordInfoService;

    private final ICourseService courseService;

    private final IStudentParentRecordService studentParentRecordService;

    private final IStudentIntroduceRecordService studentIntroduceRecordService;

    private final IStudentPreferentialRecordService studentPreferentialRecordService;

    private final StudentIntroduceConfig studentIntroduceConfig;

    private final IProductService productService;

    @DubboReference
    private RemoteDictService remoteDictService;

    @DubboReference
    private RemoteFileService remoteFileService;

    @DubboReference
    private RemoteSmsService remoteSmsService;

    @DubboReference
    private RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteIntegralService remoteIntegralService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteDeptService remoteDeptService;


    @DubboReference
    private RemoteBranchControlStudentService remoteBranchControlStudentService;

    @DubboReference
    private RemoteCrmStudentService remoteCrmStudentService;

    @DubboReference
    private RemoteStudentMembershipService remoteStudentMembershipService;

    /**
     * 查询会员
     */
    @Override
    public StudentVo queryById(StudentBo bo) {
        StudentVo studentVo = baseMapper.selectStudentById(bo.getStudentId());
        List<StudentVo> singletonList = Collections.singletonList(studentVo);
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putSysUserInfo(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSignInfo())) {
            putStudentSignInfo(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentLastPayOrderProductInfo())) {
            putStudentLastPayOrderProductInfo(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentLastAiCourseRecordInfoSummary())) {
            putStudentLastAiCourseRecordInfoSummary(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentIntegralCount())) {
            putStudentIntegralCount(singletonList);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentIntroduce())) {
            putStudentIntroduceInfo(singletonList);
        }
        putRemainingHour(singletonList);
        //查询冻结优惠金额
        studentVo.setFrozenPreferentialAmount(studentPreferentialRecordService.getFrozenAmount(studentVo.getStudentId()));
        if (null != studentVo.getPreferentialAmount() && null != studentVo.getFrozenPreferentialAmount()) {
            studentVo.setPreferentialAmount(studentVo.getPreferentialAmount().subtract(studentVo.getFrozenPreferentialAmount()));
        }
        return studentVo;
    }

    private void putStudentIntroduceInfo(List<StudentVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentIds =
            list.stream().map(StudentVo::getStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        Map<Long, StudentIntroduceRecord> studentIntroduceRecordMap =
            studentIntroduceRecordService.getIntroduceMapByStudentIdList(studentIds);
        Map<Long, StudentVo> idToStudentVoMap = new HashMap<>();
        list.forEach(studentVo -> {
            StudentIntroduceRecord studentIntroduceRecord = studentIntroduceRecordMap.get(studentVo.getStudentId());
            if (studentIntroduceRecord != null) {
                StudentVo introducer = idToStudentVoMap.computeIfAbsent(studentIntroduceRecord.getIntroduceStudentId(),
                    info -> baseMapper.selectVoById(studentIntroduceRecord.getIntroduceStudentId()));
                StudentVo introducerVo = new StudentVo();
                introducerVo.setStudentId(introducer.getStudentId());
                introducerVo.setStudentName(introducer.getStudentName());
                studentVo.setIntroduceStudent(introducerVo);
            }
        });
    }

    private void putStudentIntegralCount(List<StudentVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentIds =
            list.stream().map(StudentVo::getStudentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        Map<Long, BigDecimal> integralByStudentIdList = remoteIntegralService.getIntegralByStudentIdList(studentIds);
        list.forEach(studentVo -> {
            BigDecimal integralCount = integralByStudentIdList.get(studentVo.getStudentId());
            if (integralCount != null) {
                studentVo.setTotalIntegral(integralCount);
            }
        });
    }

    /**
     * 查询会员列表
     */
    @Override
    public TableDataInfo<StudentVo> queryPageList(StudentBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);

        QueryWrapper<Student> lqw = buildQueryWrapper(bo);
        Page<StudentVo> result = baseMapper.selectStudentPage(pageQuery.build(), lqw, bo);
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putSysUserInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSignInfo())) {
            putStudentSignInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentLastPayOrderProductInfo())) {
            putStudentLastPayOrderProductInfo(result.getRecords());
        }
        putRemainingHour(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查看剩余期限
     *
     * @param records 记录
     *
     * @date 2024/03/18 01:28:45
     */
    private void putRemainingHour(List<StudentVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIds = records.stream().map(StudentVo::getStudentId).toList();
        List<RemoteStudentMembershipCardVo> membershipCardVos = remoteStudentMembershipService.queryStudentMembershipCard(studentIds);
        if (CollUtil.isEmpty(membershipCardVos)){
            return;
        }

        this.setRemainDate(records, membershipCardVos);
        this.setEarliestMemberCardStartDate(records, membershipCardVos);
    }

    /**
     * 设置最早的会员卡开始时间
     * @param records
     * @param membershipCardVos
     */
    private void setEarliestMemberCardStartDate(List<StudentVo> records, List<RemoteStudentMembershipCardVo> membershipCardVos) {
        if (CollUtil.isEmpty(membershipCardVos)){
            return;
        }
        Map<Long, RemoteStudentMembershipCardVo> membershipCardVoMap = membershipCardVos.stream()
            .collect(Collectors.toMap(RemoteStudentMembershipCardVo::getStudentId,
                Function.identity(),
                BinaryOperator.minBy(Comparator.comparing(RemoteStudentMembershipCardVo::getProductBeginDate))));

        // 获取会员卡最早开始时间
        records.stream()
            .filter(studentVo -> membershipCardVoMap.containsKey(studentVo.getStudentId()))
            .forEach(studentVo -> {
                RemoteStudentMembershipCardVo membershipCardVo = membershipCardVoMap.get(studentVo.getStudentId());
                studentVo.setEarliestMemberCardStartDate(membershipCardVo.getProductBeginDate());
            });
    }

    private void setRemainDate(List<StudentVo> records, List<RemoteStudentMembershipCardVo> membershipCardVos) {
        Map<Long, StudentRemainingTimeDTO> maxRemainingTimeDTOs =  this.getStudentMaxExpireInfoDTO(membershipCardVos);
        records.forEach(studentVo -> {
            StudentRemainingTimeDTO remainingTimeDTO = maxRemainingTimeDTOs.get(studentVo.getStudentId());
            if (null == remainingTimeDTO) {
                processNoExistVipRecord(studentVo);
            } else {
                studentVo.setRemainingHour(remainingTimeDTO.getRemainingHours());
                studentVo.setRemainingDay(remainingTimeDTO.getRemainingDays());
            }
        });
    }

    private static void processNoExistVipRecord(StudentVo studentVo) {
        if (studentVo.getExpireTime() != null) {
            if (new Date().getTime() > studentVo.getExpireTime().getTime()) {
                studentVo.setRemainingHour(0L);
                studentVo.setRemainingDay(0L);
                return;
            }
            long betweenHour = DateUtil.between(studentVo.getExpireTime(), new Date(), DateUnit.HOUR);
            studentVo.setRemainingHour(betweenHour);
            long betweenDay = DateUtil.between(studentVo.getExpireTime(), new Date(), DateUnit.DAY);
            studentVo.setRemainingDay(betweenDay);
        }else {
            studentVo.setRemainingHour(0L);
            studentVo.setRemainingDay(0L);
        }
    }

    /**
     * 获取学生最大到期时间
     * @param membershipCardVos
     * @return
     */
    private Map<Long, StudentRemainingTimeDTO> getStudentMaxExpireInfoDTO(List<RemoteStudentMembershipCardVo> membershipCardVos) {
        if (CollectionUtil.isEmpty(membershipCardVos)) {
            return Collections.emptyMap();
        }
        Map<Long, StudentRemainingTimeDTO> resultMap = new HashMap<>();

        for (RemoteStudentMembershipCardVo card : membershipCardVos) {
            if (card.getProductBeginDate() == null || card.getProductEndDate() == null) continue;
            // 计算剩余天数和剩余时长
            StudentRemainingTimeDTO current = StudentRemainingTimeDTO.calculate(
                card.getStudentId(),
                card.getProductBeginDate(),
                card.getProductEndDate(),
                LocalDateTime.now());
            // 相同学生的合并，取最大天数的一个
            resultMap.merge(
                card.getStudentId(),
                current,
                (oldVal, newVal) -> newVal.getRemainingDays() > oldVal.getRemainingDays() ? newVal : oldVal
            );
        }
        return resultMap;
    }

    /**
     * 查询会员列表
     */
    @Override
    public List<StudentVo> queryList(StudentBo bo) {
        return queryList(bo, false);
    }

    /**
     * 查询会员列表
     */
    @Override
    public List<StudentVo> queryList(StudentBo bo, Boolean ignoreDataScope) {
        if (!Boolean.TRUE.equals(ignoreDataScope)) {
            handleQueryParam(bo);
        }

        LambdaQueryWrapper<Student> lqw = buildLambdaQueryWrapper(bo);
        List<StudentVo> studentVos = baseMapper.selectStudentList(lqw);
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(studentVos);
        }
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(studentVos);
        }
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putSysUserInfo(studentVos);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentSignInfo())) {
            putStudentSignInfo(studentVos);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentLastPayOrderProductInfo())) {
            putStudentLastPayOrderProductInfo(studentVos);
        }
        if (!Boolean.TRUE.equals(bo.getExcludeRemainHourInfo())) {
            putRemainingHour(studentVos);
        }
        return studentVos;
    }

    //会员顾问记录详情
    private void putConsultantInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentConsultantRecordId = studentVos.stream().map(StudentVo::getStudentConsultantRecordId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantRecordId)) {
            return;
        }
        List<StudentConsultantRecordVo> studentConsultantRecordVos = studentConsultantRecordService.queryByIdList(studentConsultantRecordId);
        if (CollUtil.isEmpty(studentConsultantRecordVos)) {
            return;
        }
        List<Long> studentConsultantIdList = studentConsultantRecordVos.stream().map(StudentConsultantRecordVo::getStudentConsultantId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(studentConsultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        studentVos.forEach(studentVo -> {
            StudentConsultantRecordVo studentConsultantRecordVo = studentConsultantRecordVos.stream().filter(vo -> vo.getStudentConsultantRecordId().equals(studentVo.getStudentConsultantRecordId())).findFirst().orElse(null);
            if (studentConsultantRecordVo != null) {
                RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(studentConsultantRecordVo.getStudentConsultantId());
                studentVo.setConsultant(remoteStaffVo);
            }
        });

    }

    //店铺信息
    public void putBranchInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> branchIds = studentVos.stream().map(StudentVo::getBranchId).collect(Collectors.toList());
        branchIds.remove(null);
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(branchIds);
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo, true);
        Map<Long, RemoteBranchVo> remoteBranchVoMap = remoteBranchVos.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteBranchVo remoteBranchVo = remoteBranchVoMap.get(studentVo.getBranchId());
            studentVo.setBranch(remoteBranchVo);
        });

    }

    public void putSysUserInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        remoteUserBo.setNeedUserFaceImg(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    /**
     * 放置用户头像
     *
     * @param records 记录
     *
     * @date 2024/03/07 03:50:31
     */
    private void putUserAvatar(List<StudentVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<RemoteUserVo> list = records.stream().map(StudentVo::getSysUser).toList();
        String ossIds = list.stream().map(RemoteUserVo::getAvatar).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.isBlank(ossIds)) {
            return;
        }
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIds(ossIds);
        if (CollUtil.isEmpty(remoteFiles)) {
            return;
        }
        Map<Long, RemoteFile> remoteFileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, Function.identity()));
        list.forEach(item -> {
            RemoteFile remoteFile = remoteFileMap.get(item.getAvatar());
            if (remoteFile != null) {
                item.setAvatarUrl(remoteFile.getUrl());
            }
        });
    }


    public void putStudentInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentIds = studentVos.stream().map(StudentVo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        StudentInfoBo studentInfoBo = new StudentInfoBo();
        studentInfoBo.setStudentIdList(studentIds);
        List<StudentInfoVo> studentInfoVos = studentInfoService.queryList(studentInfoBo);
        if (CollUtil.isEmpty(studentInfoVos)) {
            return;
        }
        Map<Long, StudentInfoVo> studentInfoVoMap = studentInfoVos.stream().collect(Collectors.toMap(StudentInfoVo::getStudentId, vo -> vo));
        studentVos.forEach(studentVo -> {
            StudentInfoVo studentInfoVo = studentInfoVoMap.get(studentVo.getStudentId());
            studentVo.setStudentInfo(studentInfoVo);
        });
    }

    public void putStudentSignInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentIds = studentVos.stream().map(StudentVo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        StudentSignBo studentSignBo = new StudentSignBo();
        studentSignBo.setStudentIds(studentIds);
        List<StudentSignVo> studentSignVos = studentSignService.queryList(studentSignBo);
        Map<Long, List<StudentSignVo>> studentSignVoMap = studentSignVos.stream().collect(Collectors.groupingBy(StudentSignVo::getStudentId));
        studentVos.forEach(studentVo -> {
            List<StudentSignVo> signVos = studentSignVoMap.get(studentVo.getStudentId());
            studentVo.setStudentSignList(signVos);
        });
    }

    /**
     * 往实体类里面放置会员上次付款订单产品信息
     *
     * @param studentVos 会员vos
     *
     * @date 2024/03/14 07:34:21
     */
    public void putStudentLastPayOrderProductInfo(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentIds = studentVos.stream().map(StudentVo::getStudentId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
        remoteOrderBo.setStudentIdList(studentIds);
        remoteOrderBo.setOrderStatusList(List.of(OrderStatusEnum.PAYED.getCode(), OrderStatusEnum.PENDING_PAY.getCode()));
        List<RemoteOrderVo> remoteOrderVos = remoteOrderService.selectOrderListAndInfo(remoteOrderBo,true);
        Map<Long, List<RemoteOrderVo>> remoteOrderVoMap = remoteOrderVos.stream().collect(Collectors.groupingBy(RemoteOrderVo::getStudentId));
        studentVos.forEach(studentVo -> {
            List<RemoteOrderVo> orderVos = remoteOrderVoMap.get(studentVo.getStudentId());
            if (CollUtil.isEmpty(orderVos)) {
                return;
            }
            //所有的产品
            List<RemoteOrderProductInfoVo> list = new ArrayList<>();
            orderVos.stream().map(RemoteOrderVo::getOrderProductInfoList).forEach(list::addAll);
            list.remove(null);

            if (CollUtil.isNotEmpty(list)) {
                String productNames = list.stream().map(RemoteOrderProductInfoVo::getProductName).collect(Collectors.joining(","));
                studentVo.setStudentHasProductNames(productNames);
                studentVo.setStudentProductList(list);
            }
        });
    }

    /**
     * 往实体类里面放置会员AI课程分配汇总 如 初中2科 | 高中1科
     *
     * @param studentVos 会员vos
     *
     * @date 2024/03/14 07:34:21
     */
    public void putStudentLastAiCourseRecordInfoSummary(List<StudentVo> studentVos) {
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }

        Map<Long, RemoteBranchAuthTypeVo> map = new HashMap<>();

        studentVos.forEach(studentVo -> {
            if (studentVo.getExpireTime()==null) {
                studentVo.setAiCourseRecordInfoSummary("无有效会员卡");
                return;
            }
            if (studentVo.getExpireTime().getTime()<System.currentTimeMillis()) {
                studentVo.setAiCourseRecordInfoSummary("会员已过期");
                return;
            }
            if (null == studentVo.getBranchId()) {
                if (null != studentVo.getStudentId()) {
                    StudentBo studentBo = new StudentBo();
                    studentBo.setStudentId(studentVo.getStudentId());
                    studentVo.setBranchId(this.queryById(studentBo).getBranchId());
                } else {
                    return;
                }
            }

            //学生所在门店全部AI课程
            Long branchId = studentVo.getBranchId();
            if (null == branchId) {
                return;
            }
            RemoteBranchAuthTypeVo remoteBranchAuthTypeVo = map.get(branchId);
            if (null == remoteBranchAuthTypeVo) {
                remoteBranchAuthTypeVo = remoteBranchAuthTypeService.getAuthTypeByBranchId(branchId);
                if (null != remoteBranchAuthTypeVo) {
                    map.put(branchId, remoteBranchAuthTypeVo);
                }
            }
            if (null == remoteBranchAuthTypeVo) {
                return;
            }
            String authTypeCourseIds = remoteBranchAuthTypeVo.getCourseIds();
            if (StringUtils.isBlank(authTypeCourseIds)) {
                return;
            }
            List<Long> allCourseIds = Arrays.stream(authTypeCourseIds.split(",")).toList().stream().filter(StringUtils::isNotBlank).map(Long::parseLong).collect(Collectors.toList());

            List<Long> courseIds = null;

            if (null != studentVo.getStudentAiCourseRecordId()) {
                StudentAiCourseRecordInfoBo studentAiCourseRecordInfoBo = new StudentAiCourseRecordInfoBo();
                studentAiCourseRecordInfoBo.setStudentAiCourseRecordId(studentVo.getStudentAiCourseRecordId());
                List<StudentAiCourseRecordInfoVo> infoList = studentAiCourseRecordInfoService.queryList(studentAiCourseRecordInfoBo);
                courseIds = infoList.stream().map(StudentAiCourseRecordInfoVo::getCourseId).collect(Collectors.toList());
            }
            // 如果courseIds是空 表示这个会员还没有分配过课程，那么获取这个会员所在门店上级代理商分配的课程信息
            if (CollUtil.isEmpty(courseIds)) {
                RemoteDeptVo preDeptByDeptVo = remoteDeptService.getPreDeptByDeptId(studentVo.getCreateDept());
                // 根据这个deptId查询到关联的模板ID
                RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
                remoteStudentProductTemplateAuthBo.setDeptId(preDeptByDeptVo.getDeptId());//代理商ID
                remoteStudentProductTemplateAuthBo.setTemplateType(1);//课程模板查询
                courseIds = remoteOrderService.queryAuthCourseIds(remoteStudentProductTemplateAuthBo);
            }
            List<CourseVo> courseVoList = null;
            if (ObjectUtil.isEmpty(courseIds)) {
                //没有设置，则查看全部AI课程
                CourseBo courseBo = new CourseBo();
                courseBo.setCourseIdList(allCourseIds);
                courseVoList = courseService.queryList(courseBo);
            } else {
                CourseBo courseBo = new CourseBo();
                courseBo.setCourseIdList(courseIds);
                courseVoList = courseService.queryList(courseBo);
            }
            if (ObjectUtil.isEmpty(courseVoList)) {
                return;
            }

            //分组并且汇总数据 如 小学为p 初中为m 高中为h
            Map<String, List<CourseVo>> groupByGradeMap = courseVoList.stream().collect(Collectors.groupingBy(courseVo -> StringUtils.isNotBlank(courseVo.getStage()) ? courseVo.getStage() : "gradeIsNull"));

            groupByGradeMap.remove("gradeIsNull");

            StringBuffer stringBuffer = new StringBuffer("");

            //字典查询对应的值
            List<RemoteDictDataVo> remoteDictDataVoList = remoteDictService.selectDictDataByType("course_stage");

            Map<String, String> dictDataMap = new Hashtable<>();
            remoteDictDataVoList.parallelStream().forEach(vo -> {
                dictDataMap.put(vo.getDictValue(), vo.getDictLabel());
            });

            AtomicInteger i = new AtomicInteger();
            groupByGradeMap.forEach((key, value) -> {
                stringBuffer.append(dictDataMap.get(key) + value.size() + "科" + (i.get() == groupByGradeMap.size() - 1 ? "" : " | "));
                i.getAndIncrement();
            });

            studentVo.setAiCourseRecordInfoSummary(stringBuffer.toString());

        });
    }

    private LambdaQueryWrapper<Student> buildLambdaQueryWrapper(StudentBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Student> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, Student::getBranchId, bo.getBranchId());
        lqw.eq(bo.getStudentAccountType() != null, Student::getStudentAccountType, bo.getStudentAccountType());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentAccount()), Student::getStudentAccount, bo.getStudentAccount());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentAccounts()), Student::getStudentAccount, bo.getStudentAccounts());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentPassword()), Student::getStudentPassword, bo.getStudentPassword());
        lqw.like(StringUtils.isNotBlank(bo.getStudentName()), Student::getStudentName, bo.getStudentName());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentSex()), Student::getStudentSex, bo.getStudentSex());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentGrade()), Student::getStudentGrade, bo.getStudentGrade());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentSource()), Student::getStudentSource, bo.getStudentSource());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentParentPhone()), Student::getStudentParentPhone, bo.getStudentParentPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentBackupPhone()), Student::getStudentBackupPhone, bo.getStudentBackupPhone());
        lqw.eq(bo.getStudentConsultantRecordId() != null, Student::getStudentConsultantRecordId, bo.getStudentConsultantRecordId());
        lqw.eq(bo.getStudentParentRecordId() != null, Student::getStudentParentRecordId, bo.getStudentParentRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentRemark()), Student::getStudentRemark, bo.getStudentRemark());
        lqw.eq(bo.getLastLoginTime() != null, Student::getLastLoginTime, bo.getLastLoginTime());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(student_name,'(',right(student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.lt(bo.getLessThanExpireTime() != null, Student::getExpireTime, bo.getLessThanExpireTime());
        lqw.apply(bo.getExpireDate() != null, "DATE(expire_time) = DATE({0}) ", bo.getExpireDate());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIds()), Student::getStudentId, bo.getStudentIds());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), Student::getBranchId, bo.getBranchIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getUserIdList()), Student::getCreateBy, bo.getUserIdList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudentIds()), Student::getStudentId, bo.getNotInStudentIds());
        return lqw;
    }

    private QueryWrapper<Student> buildQueryWrapper(StudentBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<Student> lqw = Wrappers.query();
        lqw.eq(bo.getBranchId() != null, "t.branch_id", bo.getBranchId());
        lqw.eq(bo.getStudentAccountType() != null, "t.student_account_type", bo.getStudentAccountType());
        lqw.like(StringUtils.isNotBlank(bo.getStudentAccount()), "t.student_account", bo.getStudentAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentAccountAcrossBranch()), "t.student_account",
            bo.getStudentAccountAcrossBranch());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentPassword()), "t.student_password", bo.getStudentPassword());
        lqw.like(StringUtils.isNotBlank(bo.getStudentName()), "t.student_name", bo.getStudentName());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentSex()), "t.student_sex", bo.getStudentSex());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentGrade()), "t.student_grade", bo.getStudentGrade());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentSource()), "t.student_source", bo.getStudentSource());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentParentPhone()), "t.student_parent_phone", bo.getStudentParentPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentBackupPhone()), "t.student_backup_phone", bo.getStudentBackupPhone());
        lqw.eq(bo.getStudentConsultantRecordId() != null, "t.student_consultant_record_id", bo.getStudentConsultantRecordId());
        lqw.eq(bo.getStudentParentRecordId() != null, "t.student_parent_record_id", bo.getStudentParentRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentRemark()), "t.student_remark", bo.getStudentRemark());
        lqw.eq(bo.getLastLoginTime() != null, "t.last_login_time", bo.getLastLoginTime());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(t.student_name,'(',right(t.student_account,4),')') like concat('%',{0},'%')", bo.getNameWithPhone());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIds()), "t.student_id", bo.getStudentIds());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudentIds()), "t.student_id", bo.getNotInStudentIds());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), "t.branch_id", bo.getBranchIdList());
        lqw.in(bo.getPurchasedCardFlag() != null, "t.purchased_card_flag", bo.getPurchasedCardFlag());

        //查顾问
        if (StringUtils.isNotBlank(bo.getConsultantName()) || bo.getConsultantId() != null) {
            RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
            remoteStaffBo.setName(bo.getConsultantName());
            remoteStaffBo.setBranchStaffId(bo.getConsultantId());
            List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
            if (CollUtil.isNotEmpty(remoteStaffVos)) {
                List<Long> consultantIds = remoteStaffVos.stream().map(RemoteStaffVo::getBranchStaffId).collect(Collectors.toList());
                Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(consultantIds);
                if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
                    lqw.in("t.student_id", staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList());
                } else {
                    lqw.in("t.student_id", -1);
                }
            } else {
                lqw.in("t.student_id", -1);
            }
        }

        //查会员状态 TODO 后期改成缓存，过滤会员，只要状态正常的
        if (StringUtils.isNotBlank(bo.getStudentStatus())) {
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setStatus(bo.getStudentStatus());
            //if (StringUtils.isBlank(bo.getStudentStatus())) {
            //    remoteUserBo.setStatus("0");
            //}
            List<Long> userIds = remoteUserService.queryUserIds(remoteUserBo, true);
            if (CollUtil.isEmpty(userIds)) {
                userIds = Collections.singletonList(-1L);
            }
            lqw.in("t.create_by", userIds);
        }


        //查会员买的产品
        if (bo.getProductId() != null) {
            RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
            remoteOrderBo.setOrderStatus(OrderStatusEnum.PAYED.getCode());
            remoteOrderBo.setProductId(bo.getProductId());
            List<RemoteOrderVo> remoteOrderVos = remoteOrderService.selectStudentLastOrder(remoteOrderBo);
            List<Long> studentIds = remoteOrderVos.stream().map(RemoteOrderVo::getStudentId).collect(Collectors.toList());
            if (CollUtil.isEmpty(studentIds)) {
                studentIds = Collections.singletonList(-1L);
            } else {
                lqw.in("t.student_id", studentIds);
            }
        }

        //绑定了家长的
        if (Boolean.TRUE.equals(bo.getWhetherBindParents())) {
            lqw.isNotNull("t.student_parent_record_id");
        } else if (Boolean.FALSE.equals(bo.getWhetherBindParents())) {
            lqw.isNull("t.student_parent_record_id");
        }

        lqw.apply(bo.getLessThanExpireDays() != null, "t.expire_time < DATE_ADD(NOW(),INTERVAL {0} DAY)", bo.getLessThanExpireDays());
        lqw.apply(bo.getLessThanExpireHours() != null, "t.expire_time < DATE_ADD(NOW(),INTERVAL {0} HOUR)", bo.getLessThanExpireHours());
        lqw.apply(bo.getExpireDate() != null, "DATE(t.expire_time ) = DATE({0})", bo.getExpireDate());
        if(null != bo.getExistPendingPayOrder()){
            if(Boolean.TRUE.equals(bo.getExistPendingPayOrder())){
                lqw.eq("o_operate.has_supplement_order",1);
            }else {
                lqw.eq("o_operate.has_supplement_order",0);
            }
        }
        return lqw;
    }

    /**
     * 新增会员
     *
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long insertByBo(StudentBo bo) throws ServiceException {
        //默认密码手机号码后6位
        if (StringUtils.isBlank(bo.getStudentPassword())) {
            bo.setStudentPassword(generatePassword(bo.getStudentAccount()));
        }else {
            bo.setStudentPassword(BCrypt.hashpw(bo.getStudentPassword()));
        }
        boolean isIntroduce = null != bo.getIntroduceStudent();

        //查询门店对应的deptId
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchId(bo.getBranchId());
        Long branchDeptId;
        RemoteBranchVo remoteBranchVo = null;
        if (isIntroduce) {
            remoteBranchVo = remoteBranchService.selectBranchById(bo.getBranchId());
            branchDeptId = remoteBranchVo.getCreateDept();
        } else {
            branchDeptId = remoteBranchService.selectDeptIdByBranchId(bo.getBranchId());
        }
        if (branchDeptId == null) {
            throw new ServiceException("门店不存在");
        }
        Long userId = getUserId(bo, branchDeptId);

        Student studentOne = baseMapper.selectOne(Wrappers.<Student>lambdaQuery().eq(Student::getCreateBy, userId));
        if (ObjectUtil.isNotEmpty(studentOne)) {
            throw new ServiceException("该手机号已注册");
        }


        Integer studentAccountType = bo.getStudentAccountType();
        if (Objects.nonNull(studentAccountType) && studentAccountType == 1) {
            StudentBo checkExist = new StudentBo();
            checkExist.setBranchId(bo.getBranchId());
            checkExist.setStudentAccountType(1);
            LambdaQueryWrapper<Student> studentLambdaQueryWrapper = buildLambdaQueryWrapper(checkExist);
            List<Student> students = baseMapper.selectList(studentLambdaQueryWrapper);
            if (CollUtil.isNotEmpty(students)) {
                throw new ServiceException("门店已经拥有演示账号");
            }
        }

        //新建student
        Student student = MapstructUtils.convert(bo, Student.class);
        student.setCreateBy(userId);
        student.setCreateDept(branchDeptId);
        validEntityBeforeSave(student);
        boolean flag = baseMapper.insert(student) > 0;
        if (!flag || student.getStudentId() == null) {
            throw new ServiceException("会员新增失败");
        }

        //新建会员信息
        StudentInfoBo studentInfo = bo.getStudentInfo();
        if (studentInfo == null) {
            studentInfo = new StudentInfoBo();
        }
        studentInfo.setStudentId(student.getStudentId());
        studentInfo.setCreateBy(userId);
        studentInfo.setCreateDept(branchDeptId);
        Boolean b = studentInfoService.insertByBo(studentInfo);
        if (!b) {
            throw new ServiceException("会员信息新增失败");
        }

        //新建顾问绑定记录
        Long consultantId = bo.getConsultantId();
        StudentConsultantRecordBo studentConsultantRecordBo = new StudentConsultantRecordBo();
        studentConsultantRecordBo.setStudentId(student.getStudentId());
        studentConsultantRecordBo.setStudentConsultantId(consultantId);
        studentConsultantRecordBo.setCreateBy(userId);
        studentConsultantRecordBo.setCreateDept(branchDeptId);
        Boolean c = studentConsultantRecordService.insertByBo(studentConsultantRecordBo);
        if (!c) {
            throw new ServiceException("顾问绑定记录新增失败");
        }

        //处理会员标签
        List<StudentSignBo> studentSignList = bo.getStudentSignList();
        if (CollUtil.isNotEmpty(studentSignList)) {
            studentSignList.forEach(item -> {
                item.setStudentId(student.getStudentId());
                item.setCreateBy(userId);
                item.setCreateDept(branchDeptId);
            });
            Boolean d = studentSignService.insertBatchByBo(studentSignList);
            if (!d) {
                throw new ServiceException("会员标签新增失败");
            }
        }
        if (isIntroduce) {
            StudentVo introducerInfo = baseMapper.selectStudentById(bo.getIntroduceStudent().getStudentId());
            if (null == introducerInfo) {
                throw new ServiceException("介绍人信息不存在");
            }
            // 介绍人不为空的时候认为当前渠道为转介绍
            StudentIntroduceRecordBo studentIntroduceRecordBo = new StudentIntroduceRecordBo(student, introducerInfo, remoteBranchVo);
            if (!studentIntroduceRecordService.insertByBo(studentIntroduceRecordBo)) {
                throw new ServiceException("转介绍记录信息保存失败");
            }
        }

        //处理顾问记录
        Student updateStudentBean = new Student();
        updateStudentBean.setStudentId(student.getStudentId());
        updateStudentBean.setStudentConsultantRecordId(studentConsultantRecordBo.getStudentConsultantRecordId());
        boolean e = baseMapper.updateById(updateStudentBean) > 0;
        if (!e) {
            throw new ServiceException("顾问记录更新失败");
        }
        return student.getStudentId();
    }

    private @NotNull Long getUserId(StudentBo bo, Long branchDeptId) {
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserName(bo.getStudentAccount());
        List<Long> longs = remoteUserService.queryUserIds(remoteUserBo, true);
        //如果存在则直接返回,不存在则新建
        if (CollUtil.isNotEmpty(longs)) {
            return longs.get(0);
        }
        //添加student对应的sys_user
        remoteUserBo.setNickName(bo.getStudentName());
        remoteUserBo.setDeptId(branchDeptId);
        remoteUserBo.setPassword(bo.getStudentPassword());
        remoteUserBo.setSex(bo.getStudentSex());
        remoteUserBo.setUserType(UserType.APP_STU_USER.getUserType());
        Long userId = remoteUserService.insertUser(remoteUserBo);
        if (userId == null) {
            throw new ServiceException("会员新增失败");
        }
        return userId;
    }

    /**
     * 修改会员
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = "student", key = "#bo.studentId",condition = "#bo.studentId != null")
    public void updateByBo(StudentBo bo) {
        //校验提到前面
        StudentInfoBo studentInfo = bo.getStudentInfo();
        if (studentInfo == null || studentInfo.getStudentInfoId() == null) {
            throw new ServiceException("会员信息不存在");
        }

        //查询门店对应的deptId
        RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchById(bo.getBranchId());
        if (remoteBranchVo == null) {
            throw new ServiceException("门店不存在");
        }
        Long branchDeptId = remoteBranchVo.getCreateDept();
        //获取student对应的sys_user
        Student student = baseMapper.selectById(bo.getStudentId());
        if (student == null || student.getCreateBy() == null) {
            throw new ServiceException("会员不存在");
        }
        Long sysUserId = student.getCreateBy();

        // 修改student对应的sys_use
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(sysUserId);
        // remoteUserBo.setUserName(bo.getStudentAccount()); 用户名不给修改
        remoteUserBo.setNickName(bo.getStudentName());
        remoteUserBo.setDeptId(branchDeptId);
        // remoteUserBo.setPassword(bo.getStudentPassword()); 仅允许使用重置密码功能重置密码
        remoteUserBo.setSex(bo.getStudentSex());
        remoteUserService.updateUserInfo(remoteUserBo);

        // 修改student
        Student studentUpdateBean = MapstructUtils.convert(bo, Student.class);
        studentUpdateBean.setStudentAccount(null);// 用户名不给修改
        studentUpdateBean.setStudentPassword(null);// 仅允许使用重置密码功能重置密码
        studentUpdateBean.setCreateDept(branchDeptId);

        // 新建会员信息
        studentInfo.setCreateDept(branchDeptId);
        Boolean b = studentInfoService.updateByBo(studentInfo);
        if (!b) {
            throw new ServiceException("会员信息修改失败");
        }

        Long consultantId = bo.getConsultantId();
        // 查询顾问绑定记录
        Long studentConsultantRecordId = student.getStudentConsultantRecordId();
        StudentConsultantRecordVo studentConsultantRecordVo =
            studentConsultantRecordService.queryById(studentConsultantRecordId);
        // 判断顾问是否变更
        if (!Objects.equals(consultantId, studentConsultantRecordVo.getStudentConsultantId())) {
            // 新建顾问绑定记录
            StudentConsultantRecordBo studentConsultantRecordBo = new StudentConsultantRecordBo();
            studentConsultantRecordBo.setStudentId(student.getStudentId());
            studentConsultantRecordBo.setStudentConsultantId(consultantId);
            studentConsultantRecordBo.setCreateBy(sysUserId);
            studentConsultantRecordBo.setCreateDept(branchDeptId);
            Boolean c = studentConsultantRecordService.insertByBo(studentConsultantRecordBo);
            if (!c) {
                throw new ServiceException("顾问绑定记录新增失败");
            }
            // 处理顾问记录
            studentUpdateBean.setStudentConsultantRecordId(studentConsultantRecordBo.getStudentConsultantRecordId());
        }

        // 更新转介绍信息
        if (null != bo.getUpdateStudentIntroduceRecordType()) {
            if (!updateStudentIntroduceRecord(student, bo.getIntroduceStudent(), remoteBranchVo,
                bo.getUpdateStudentIntroduceRecordType())) {
                throw new ServiceException("更新转介绍信息失败");
            }
        }

        // 处理会员更新
        boolean flag = baseMapper.updateById(studentUpdateBean) > 0;
        if (!flag) {
            throw new ServiceException("会员修改失败");
        }
    }

    /**
     * 更新转介绍信息
     *
     * @param student
     * @param introduceStudent
     * @param remoteBranchVo
     * @param updateStudentIntroduceRecordType 调整类型 1-更换介绍人 2-调整会员渠道由转介绍调整为非转介绍 3-调整会员渠道由非转介绍调整为转介绍
     * @return
     */
    private boolean updateStudentIntroduceRecord(Student student, StudentBo introduceStudent,
        RemoteBranchVo remoteBranchVo, Integer updateStudentIntroduceRecordType) {
        if (updateStudentIntroduceRecordType == null) {
            return true;
        }
        RemoteOrderVo officialOrder = remoteOrderService.getRemoteOrder(student.getStudentId(),
            studentIntroduceConfig.getExperienceStudentTypeId());
        if (null != officialOrder) {
            throw new ServiceException("当前会员已购入正式会员卡，不能修改转介绍信息");
        }
        StudentIntroduceRecordVo introduceRecordVo =
            studentIntroduceRecordService.getByStudentId(student.getStudentId());
        if (1 == updateStudentIntroduceRecordType || 2 == updateStudentIntroduceRecordType) {
            // 更换介绍人，更新转介绍记录

            if (null == introduceRecordVo) {
                log.error("数据异常，无法查询到转介绍记录，新会员id：{}", student.getStudentId());
                throw new ServiceException("修改会员转介绍信息异常");
            }
        }
        if (1 == updateStudentIntroduceRecordType || 3 == updateStudentIntroduceRecordType) {

            if (null == introduceStudent) {
                throw new ServiceException("请选择新的转介绍人信息");
            }
            StudentVo introducerInfo = getInfo(introduceStudent.getStudentId());
            if (introducerInfo == null) {
                log.error("数据异常，无法查询到新转介绍人信息，介绍人id：{}，新会员id：{}", introduceStudent.getStudentId(),
                    student.getStudentId());
                throw new ServiceException("修改会员转介绍信息异常");
            }
            if (1 == updateStudentIntroduceRecordType) {
                StudentIntroduceRecordBo updateBo = new StudentIntroduceRecordBo();
                updateBo.setIntroduceRecordId(introduceRecordVo.getIntroduceRecordId());
                updateBo.setIntroduceStudentId(introduceStudent.getStudentId());
                updateBo.setIntroduceStudentName(introducerInfo.getStudentName());
                if (!studentIntroduceRecordService.updateByBo(updateBo)) {
                    log.error("修改转介绍记录失败，修改信息：{}", JSONObject.toJSONString(updateBo));
                    throw new ServiceException("修改会员转介绍信息异常");
                }
            } else {
                StudentIntroduceRecordBo studentIntroduceRecordBo =
                    new StudentIntroduceRecordBo(student, introducerInfo, remoteBranchVo);
                if (!studentIntroduceRecordService.insertByBo(studentIntroduceRecordBo)) {
                    log.error("修改会员信息时新增转介绍记录失败，info：{}", JSONObject.toJSONString(studentIntroduceRecordBo));
                    throw new ServiceException("修改会员转介绍信息异常");
                }
            }
        } else {
            // 调整转介绍为非转介绍 需要删除转介绍记录
            if (!studentIntroduceRecordService.deleteById(introduceRecordVo.getIntroduceRecordId())) {
                log.error("删除转介绍记录信息失败，删除记录id：{}", introduceRecordVo.getIntroduceRecordId());
                throw new ServiceException("修改会员转介绍信息异常");
            }
        }
        return true;
    }

    /**
     * 修改会员 StudentConsultantRecord
     */
    @Override
    public void updateStudentConsultantRecordIdByBo(StudentBo bo) {
        //获取student
        Student studentUpdateBean = baseMapper.selectById(bo.getStudentId());
        studentUpdateBean.setStudentConsultantRecordId(bo.getStudentConsultantRecordId());
        studentUpdateBean.setBranchId(bo.getBranchId());
        //处理会员更新
        boolean flag = baseMapper.updateById(studentUpdateBean) > 0;
        if (!flag) {
            throw new ServiceException("会员修改失败");
        }
    }

    /**
     * 修改会员 StudentAiCourseRecordId
     */
    @Override
    public void updateStudentAiCourseRecordIdByBo(StudentBo bo) {
        //获取student
        Student studentUpdateBean = baseMapper.selectById(bo.getStudentId());
        studentUpdateBean.setStudentAiCourseRecordId(bo.getStudentAiCourseRecordId());
        //处理会员更新
        boolean flag = baseMapper.updateById(studentUpdateBean) > 0;
        if (!flag) {
            throw new ServiceException("会员修改失败");
        }
    }


    /**
     * 修改会员 StudentParentRecord
     */
    @Override
    public void updateStudentParentRecordIdByBo(StudentBo bo) {
        //获取student
        Student studentUpdateBean = baseMapper.selectById(bo.getStudentId());
        studentUpdateBean.setStudentParentRecordId(bo.getStudentParentRecordId());
        //处理会员更新
        boolean flag = baseMapper.updateById(studentUpdateBean) > 0;
        if (!flag) {
            throw new ServiceException("会员修改失败");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Student entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<StudentVo> queryOptionList(StudentBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(bo.getStudentAccountAcrossBranch())) {
            handleQueryParam(bo);
        }
        QueryWrapper<Student> lqw = buildQueryWrapper(bo);
        Page<StudentVo> result = baseMapper.queryOptionList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<Long> queryStudentIdList(StudentBo studentBo) {
        QueryWrapper<Student> lqw = buildQueryWrapper(studentBo);
        return baseMapper.queryStudentIdList(lqw);
    }

    @Override
    public void updateStudentExpireTime(Long studentId, Date maxExpireTime) {
        baseMapper.update(null, Wrappers.lambdaUpdate(Student.class)
            .set(Student::getExpireTime, maxExpireTime)
            .eq(Student::getStudentId, studentId)
        );
    }

    @Override
    public void changeStatus(Long studentId, String studentStatus) {
        if (!UserConstants.USER_NORMAL.equals(studentStatus) && !UserConstants.USER_DISABLE.equals(studentStatus)) {
            throw new ServiceException("状态不合法");
        }
        Student student = baseMapper.selectById(studentId);
        if (student == null) {
            throw new ServiceException("会员不存在");
        }
        Long sysUserId = student.getCreateBy();
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(sysUserId);
        remoteUserBo.setStatus(studentStatus);
        remoteUserService.updateUserInfo(remoteUserBo);

    }

    @Override
    public void forceOffline(Long studentId) {
        Student student = baseMapper.selectById(studentId);
        if (student == null) {
            throw new ServiceException("会员不存在");
        }
        Long sysUserId = student.getCreateBy();
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(sysUserId);
        RemoteUserVo userInfo = remoteUserService.getSimpleUserInfoByUserId(sysUserId);
        String userType = userInfo.getUserType();
        String loginId = userType + ":" + sysUserId;
        StpUtil.kickout(loginId);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void restPwd(StudentBo bo) {
        String studentPassword = bo.getStudentPassword();
        bo.setStudentPassword(null);
        LambdaQueryWrapper<Student> studentQueryWrapper = buildLambdaQueryWrapper(bo);
        List<StudentVo> studentVos = baseMapper.selectStudentList(studentQueryWrapper);
        if (CollUtil.isEmpty(studentVos)) {
            throw new ServiceException("会员不存在");
        }
        if (StringUtils.isNotBlank(studentPassword)) {
            studentPassword = BCrypt.hashpw(studentPassword);
        }

        List<RemoteUserBo> userUpdateList = new ArrayList<>();
        List<Student> studentUpdateList = new ArrayList<>();

        for (StudentVo studentVo : studentVos) {
            Long studentId = studentVo.getStudentId();
            Student student = new Student();
            student.setStudentId(studentId);
            student.setStudentPassword(StringUtils.isNotBlank(studentPassword) ? studentPassword : generatePassword(studentVo.getStudentAccount()));
            studentUpdateList.add(student);


            Long createBy = studentVo.getCreateBy();
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setUserId(createBy);
            remoteUserBo.setPassword(StringUtils.isNotBlank(studentPassword) ? studentPassword : generatePassword(studentVo.getStudentAccount()));
            userUpdateList.add(remoteUserBo);
        }

        boolean flag = baseMapper.updateBatchById(studentUpdateList);

        if (!flag) {
            throw new ServiceException("会员密码修改失败");
        }

        remoteUserService.batchUpdateUserInfo(userUpdateList);
    }

    private String generatePassword(String phone) {
        return BCrypt.hashpw(phone.substring(phone.length() - 6));
    }



    @Override
    public Student queryStudentById(Long studentId) {
        return baseMapper.selectById(studentId);
    }

    @CacheEvict(value = "student", allEntries = true)
    public void cleanCache() {
        log.info("===========studentService cleanCache===========");
    }

    @Override
    public Long queryStudentIdByUserId(Long userId) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Student::getStudentId).eq(Student::getCreateBy, userId);
        Student student = baseMapper.selectOne(wrapper);
        if (student == null) {
            return null;
        }
        return student.getStudentId();
    }

    @Override
    public StudentVo getInfo(Long studentId) {
        StudentVo studentVo = baseMapper.selectVoById(studentId);
        if (studentVo == null) {
            throw new ServiceException("会员不存在");
        }
        studentVo.setStudentPassword(null);
        putSysUserInfo(Collections.singletonList(studentVo));
        putUserAvatar(Collections.singletonList(studentVo));
        putStudentInfo(Collections.singletonList(studentVo));
        putStudentIntegralCount(Collections.singletonList(studentVo));
        return studentVo;
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void changePwd(Long studentId, String oldPwd, String newPwd, Boolean checkOldPwd) {
        checkPassword(newPwd);
        StudentVo studentVo = baseMapper.selectVoById(studentId);
        if (studentVo == null) {
            throw new ServiceException("会员不存在");
        }
        RemoteUserVo simpleUserInfoByUserId = remoteUserService.getSimpleUserInfoByUserId(studentVo.getCreateBy());
        if (ObjectUtils.isEmpty(simpleUserInfoByUserId)){
            throw new ServiceException("会员不存在");
        }
        if (checkOldPwd) {
            String studentPassword = simpleUserInfoByUserId.getPassword();
            if (!BCrypt.checkpw(oldPwd, studentPassword)) {
                throw new ServiceException("原密码错误");
            }
        }
        Long createBy = studentVo.getCreateBy();
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(createBy);
        remoteUserBo.setPassword(BCrypt.hashpw(newPwd));
        remoteUserService.updateUserInfo(remoteUserBo);


        Student student = new Student();
        student.setStudentId(studentId);
        student.setStudentPassword(BCrypt.hashpw(newPwd));
        boolean b = baseMapper.updateById(student) > 0;
        if (!b) {
            throw new ServiceException("会员密码修改失败");
        }
    }


    public void checkPassword(String password) {
        //校验密码是否满足以下条件：
        //1. 至少包含5个字符；
        //2. 同时包含字母（大小写均可）和数字。

        // 1. 检查密码长度是否至少为5位
        if (password.length() < 5) {
            throw new ServiceException("密码长度至少为5位");
        }
        // 2. 检查密码是否同时包含字母和数字
        // 使用正则表达式简化判断
        String pattern = "^(?=.*[A-Za-z])(?=.*\\d).+$";
        if (!Pattern.matches(pattern, password)) {
            throw new ServiceException("密码必须同时包含字母和数字");
        }
    }

    @Override
    public StudentVo queryByStudentPhone(String phone, Boolean withSysUserInfo) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getStudentAccount, phone);
        StudentVo student = baseMapper.selectVoOne(wrapper);
        if (student == null) {
            return null;
        }
        student.setStudentPassword(null);
        if (Boolean.TRUE.equals(withSysUserInfo)) {
            putSysUserInfo(Collections.singletonList(student));
        }

        return student;
    }

    @Override
    public StudentVo queryStudentByUserId(Long userId) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getCreateBy, userId);
        StudentVo student = baseMapper.selectVoOne(wrapper);
        return student;
    }

    @Override
    public StudentVo queryStudentByPhone(String phone) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getStudentAccount, phone);
        StudentVo student = baseMapper.selectVoOne(wrapper);
        return student;
    }

    @Override
    public List<Long> getStudentIdListByBranchId(Long branchId) {
        List<Long> studentIdList = new ArrayList<>();
        StudentBo bo = new StudentBo();
        bo.setBranchId(branchId);
        List<StudentVo> studentVoList = this.queryList(bo);
        if (null != studentVoList && !studentVoList.isEmpty()) {
            studentIdList = studentVoList.stream().map(StudentVo::getStudentId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return studentIdList;
    }

    @Override
    public List<Long> getStudentIdListByBranchIdList(List<Long> branchIdList) {
        List<Long> studentIdList = new ArrayList<>();
        StudentBo bo = new StudentBo();
        bo.setBranchIdList(branchIdList);
        bo.setExcludeRemainHourInfo(true);
        List<StudentVo> studentVoList = this.queryList(bo);
        if (null != studentVoList && !studentVoList.isEmpty()) {
            studentIdList = studentVoList.stream().map(StudentVo::getStudentId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return studentIdList;
    }

    @Override
    public List<StudentVo> getStudentListByStaffId(Long branchStaffId, String nameWithPhone) {
        List<Long> staffIds = new ArrayList<>();
        staffIds.add(branchStaffId);
        //获取员工负责的会员idMap
        Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIds);

        List<StudentVo> studentVoList = new ArrayList<>();

        if (null != staffResponsibleStudentIdMap) {
            List<Long> studentIds = staffResponsibleStudentIdMap.get(branchStaffId);
            if (null != studentIds && studentIds.size() > 0) {
                StudentBo bo = new StudentBo();
                bo.setNameWithPhone(nameWithPhone);
                bo.setStudentIds(studentIds);
                studentVoList = this.queryList(bo);
            }
        }

        return studentVoList;
    }

    @Override
    public Map<Long, Long> queryUserIdMapByStudentIdList(List<Long> studentIdList) {
        LambdaQueryWrapper<Student> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(Student::getStudentId, studentIdList);
        queryWrapper.select(Student::getCreateBy, Student::getStudentId);
        List<Student> studentList = baseMapper.selectList(queryWrapper);
        Map<Long, Long> userIdMap = new HashMap<>();
        for (Student student : studentList) {
            userIdMap.put(student.getStudentId(), student.getCreateBy());
        }
        return userIdMap;
    }

    @Override
    public StuAccumulatedStudyData getStuAccumulatedStudyData(Long studentId) {
        StuAccumulatedStudyData stuAccumulatedStudyData = new StuAccumulatedStudyData();
        stuAccumulatedStudyData.setAccumulateStudyTime(0L);
        stuAccumulatedStudyData.setAccumulateStudyChapterCount(0L);
        stuAccumulatedStudyData.setAccumulateStudyDays(0L);
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudentId(studentId);
        studyPlanningRecordBo.setStudyRecordHas(true);
        studyPlanningRecordBo.setWithStudyRecord(true);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordVos)) {
            return stuAccumulatedStudyData;
        }
        //累计学习时长
        Long totalStudyTime = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getStudyRecord).filter(Objects::nonNull).map(StudyRecordVo::getStudyVideoTotalDuration).filter(Objects::nonNull).reduce(Long::sum).orElse(0L);
        //累计学习课程节数（TODO 这里两个相同课程的算一节还是两节呢？这里先按照算两节来做了）
        Long totalStudyChapterCount = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getCourseId).filter(Objects::nonNull).count();
        //累计学习天数
        Long totalStudyDays = studyVideoRecordService.queryStudyDaysByStudentId(studentId);
        stuAccumulatedStudyData.setAccumulateStudyTime(totalStudyTime);
        stuAccumulatedStudyData.setAccumulateStudyChapterCount(totalStudyChapterCount);
        stuAccumulatedStudyData.setAccumulateStudyDays(totalStudyDays);
        return stuAccumulatedStudyData;

    }

    @Override
    public List<StudentVo> queryByStuPhoneOrParentPhone(String phone) {
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Student::getStudentAccount, phone);
        wrapper.or();
        wrapper.eq(Student::getStudentParentPhone, phone);
        wrapper.or();
        wrapper.eq(Student::getStudentBackupPhone, phone);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public boolean updateBatchByBo(List<StudentBo> updateStuList) {
        if (CollUtil.isEmpty(updateStuList)) {
            return false;
        }
        List<Student> convert = MapstructUtils.convert(updateStuList, Student.class);
        return baseMapper.updateBatchById(convert);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindParent(Long studentId) {
        studentParentRecordService.unbindParent(studentId);

        return baseMapper.update(null,
            Wrappers.<Student>lambdaUpdate()
                .set(Student::getStudentParentRecordId, null)
                .in(Student::getStudentId, studentId)
        ) > 0;
    }

    @Override
    public Map<Long, String> queryStudentName(List<Long> studentIdList) {
        if (CollectionUtils.isEmpty(studentIdList)){
            return Map.of();
        }

        List<Map<String, Object>> resultList = baseMapper.queryStudentNameMap(studentIdList);

        // 将 List<Map<Long, String>> 转换为 Map<Long, String>
        return resultList.stream()
            .collect(Collectors.toMap(
                map -> (Long)map.get("student_id"),
                map -> (String) map.get("student_name")
            ));
    }

    @Override
    public RemoteBranchVo getBranch(Long studentId) {
        StudentVo studentVo = baseMapper.selectVoById(studentId);
        if (ObjectUtils.isEmpty(studentVo)) {
            return null;
        }
        return remoteBranchService.selectBranchById(studentVo.getBranchId());
    }

    @Override
    public List<Student> batchQueryStudentById(List<Long> studentIds) {
         if(CollectionUtil.isEmpty(studentIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(studentIds);
    }

    @Override
    @CacheEvict(cacheNames = "student", key = "#bo.studentId",condition = "#bo.studentId != null")
    public boolean updatePreferentialAmountByBo(StudentBo bo, BigDecimal productPreferentialAmount) {
        if (null == bo.getStudentId()) {
            return false;
        }
        LambdaUpdateWrapper<Student> studentLambdaUpdateWrapper = Wrappers.lambdaUpdate(Student.class);
        studentLambdaUpdateWrapper.eq(Student::getStudentId, bo.getStudentId())
            .eq(Student::getPreferentialAmountVersion, bo.getPreferentialAmountVersion())
            .set(Student::getPreferentialAmount,null != bo.getPreferentialAmount()  ? bo.getPreferentialAmount().add(productPreferentialAmount) : productPreferentialAmount )
            .set(Student::getPreferentialAmountVersion,(bo.getPreferentialAmountVersion()+1));
        return baseMapper.update(studentLambdaUpdateWrapper) > 0;
    }

    @Override
    public StudentVo getPreferentialAmount(StudentBo studentBo) {
        StudentVo studentVo = baseMapper.selectStudentById(studentBo.getStudentId());
        if (null == studentVo) {
            throw new ServiceException("查询优惠额度异常");
        }
        BigDecimal availableAmount = null != studentVo.getPreferentialAmount() ? studentVo.getPreferentialAmount() : BigDecimal.ZERO;
        availableAmount = availableAmount.subtract(studentPreferentialRecordService.getFrozenAmount(studentVo.getStudentId()));
        StudentVo returnVo = new StudentVo();
        returnVo.setStudentId(studentVo.getStudentId());
        returnVo.setPreferentialAmount(availableAmount.compareTo(BigDecimal.ZERO) > 0 ? availableAmount : BigDecimal.ZERO);
        return returnVo;
    }

    @Override
    public Map<Long, Student> batchQueryMapStudents(List<Long> studentIds) {
        List<Student> students = this.batchQueryStudentById(studentIds);
        return students.stream()
            .collect(Collectors.toMap(Student::getStudentId, Function.identity(), (v1, v2) -> v2));
    }



    @Override
    public TableDataInfo<StudentVo> queryControlledStudentList(Long branchFeatureTemplateId, StudentBo bo, PageQuery pageQuery) {
        if ( branchFeatureTemplateId == null) {
            throw new ServiceException("请选择模板");
        }
        //根据模板ID查询对应的学生ID
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            throw new ServiceException("请选择对应的门店进行操作");
        }
        List<Long> studentIds = remoteBranchControlStudentService.selectStudentListByBranchIdAndTemplateId(branchId, branchFeatureTemplateId);
        bo.setNotInStudentIds(studentIds);
        return this.queryPageList(bo, pageQuery);
    }

    /**
     * 根据会员ID查询会员所有门店的上一级代理商ID
     *
     * @param studentId
     */
    @Override
    public Long queryPreDeptIdByStudentId(Long studentId) {
        if (ObjectUtil.isNull(studentId)) return null;
        //查询学生基本信息
        Student student = baseMapper.selectById(studentId);
        if (ObjectUtil.isNull(student)) return null;
        //获取学生对应的门店-代理商ID
        Long createDept = student.getCreateDept();
        //根据门店代理商ID 查询上一级代理商ID
        RemoteDeptVo remoteDeptVo = remoteDeptService.getPreDeptByDeptId(createDept);
        return remoteDeptVo.getDeptId();
    }

    @Override
    public String getCommonPayAppId(Long studentId) {
        StudentVo info = baseMapper.selectVoById(studentId);
        if (info == null) {
            return null;
        }
        return remoteBranchService.getCommonPayAppId(info.getBranchId());
    }

    @Override
    public StudentMerchantConfigDTO getStudentMerchantConfig(Long studentId) {
        StudentVo studentVo = baseMapper.selectVoById(studentId);
        if (studentVo == null) {
            return null;
        }

        RemotePayMerchantConfigVO commonPayConfig = remoteBranchService.getCommonPayConfig(studentVo.getBranchId());
        if(null == commonPayConfig){
            return null;
        }
        StudentMerchantConfigDTO studentMerchantConfigDTO = new StudentMerchantConfigDTO();
        studentMerchantConfigDTO.setMerchantName(commonPayConfig.getMerchantName());
        studentMerchantConfigDTO.setAppId(commonPayConfig.getAppId());
        studentMerchantConfigDTO.setPayType(commonPayConfig.getPayType());
        studentMerchantConfigDTO.setPayCode(commonPayConfig.getPayCode());
        studentMerchantConfigDTO.setWayCode(commonPayConfig.getWayCode());
        studentMerchantConfigDTO.setConfigParamJson(commonPayConfig.getConfigParamJson());
        return studentMerchantConfigDTO;
    }

    @Override
    @CacheEvict(cacheNames = "student", key = "#studentId", condition = "#studentId != null")
    public void updateBuyCardFlag(Long studentId, Integer updateFlag) {
        if (baseMapper.update(Wrappers.lambdaUpdate(Student.class).eq(Student::getStudentId, studentId)
            .set(Student::getPurchasedCardFlag, updateFlag)) <= 0) {
            log.error("更新购卡标识失败，会员id：{}", studentId);
            throw new ServiceException("更新会员信息异常");
        }
    }

    @Override
    public Boolean updateKuaidingPrivilege(UpdateKuaidingPrivilegeBo bo) {
        if (CollUtil.isEmpty(bo.getStudentAccounts())) {
            throw new ServiceException("学生账号不为空");
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentAccounts(bo.getStudentAccounts());
        List<StudentVo> studentVos =
            baseMapper.selectVoList(buildLambdaQueryWrapper(studentBo).select(Student::getStudentId));
        Set<Long> collect = studentVos.stream().map(StudentVo::getStudentId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)) {
            throw new ServiceException("账号数据异常");
        }
        return studentInfoService.updateKuaidingPrivilege(collect, bo.getHasKuaidingPrivilege(),
            bo.getKuaidingPrivilegeExpireTime());
    }

    @Override
    public void init() {
        IStudentService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentService init===========");
        LambdaQueryWrapper<Student> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Student::getStudentId);
        List<Student> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========studentService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentById(item.getStudentId());
        });
        log.info("===========studentService init end===========");
    }


    private void handleQueryParam(StudentBo record) {
        //对于已经进行筛选出来的不进行判断
        if (record.getStudentId()!=null) {
            return;
        }
        if(null != record.getStudentIds() && !record.getStudentIds().isEmpty()){
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isBranchStaff()) {
            if (null != LoginHelper.getBranchId()) {
                record.setBranchId(LoginHelper.getBranchId());
            } else {
                record.setBranchId(-1L);
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
                record.setBranchIdList(CollUtil.isEmpty(record.getBranchIdList()) ? LoginHelper.getBranchIdList() : new ArrayList<>(CollUtil.intersectionDistinct(record.getBranchIdList(), LoginHelper.getBranchIdList())));
            }
            if (null != LoginHelper.getBranchId()) {
                record.setBranchId(LoginHelper.getBranchId());
            }
        }

        // 存在未缴齐的订单
        Boolean existPendingPayOrder = record.getExistPendingPayOrder();
        if(null != existPendingPayOrder){
            if(existPendingPayOrder){
                record.setOrderOperateStatusList(OrderStatusEnum.getExistPendingOrderStatus());
            }else {
                record.setOrderOperateStatusList(OrderStatusEnum.noExistPendingOrderStatus());
            }
        }
    }
}
