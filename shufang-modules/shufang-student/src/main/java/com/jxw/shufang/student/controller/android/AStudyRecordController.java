package com.jxw.shufang.student.controller.android;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.IStudyRecordService;
import com.jxw.shufang.student.service.MqTemplateSendMessageService;
import com.jxw.shufang.student.service.RecordVideoProcessService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 学习记录--平板端
 * 前端访问路由地址为:/student/android/studyRecord
 *
 *
 * @date 2024-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/studyRecord")
@Slf4j
public class AStudyRecordController extends BaseController {

    private final IStudyRecordService studyRecordService;

    /**
     * mybatis-plus的雪花id生成器
     */
    private final IdentifierGenerator identifierGenerator;

    private final MqTemplateSendMessageService sendMessageService;

    private final RecordVideoProcessService recordVideoProcessService;

    /**
     * 查询练习批改状态
     */
    @GetMapping("/queryPracticeCorrectStatus")
    public R<String> queryCorrectStatus(Long studyPlanningRecordId) {
        return R.ok(studyRecordService.queryCorrectStatus(1, studyPlanningRecordId, LoginHelper.getStudentId()));
    }

    /**
     * 查询测试批改状态
     */
    @GetMapping("/queryTestCorrectStatus")
    public R<String> queryTestCorrectStatus(Long studyPlanningRecordId) {
        return R.ok(studyRecordService.queryCorrectStatus(2, studyPlanningRecordId, LoginHelper.getStudentId()));
    }

    /**
     * 记录video学习进度
     *
     * @param studyPlanningRecordId 学习规划记录id
     * @param courseId              课程id
     * @param videoId               视频id
     * @param spliceItem            视频进度点
     */
    @PostMapping("/recordVideoProgress")
    public R<String> recordVideoProgress(@NotNull(message = "studyPlanningRecordId不能为空") Long studyPlanningRecordId,
                                         @NotNull(message = "courseId不能为空") Long courseId,
                                         @NotNull(message = "videoId不能为空") Long videoId,
                                          Double multiple,
                                         @NotBlank(message = "spliceItem不能为空") String spliceItem
    ) {
        log.info("记录video学习进度:{},courseId:{},videoId:{},spliceItem:{}", studyPlanningRecordId, courseId, videoId, spliceItem);
        if (multiple == null) {
            multiple = 1.0;
        }
        StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
        studyVideoRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        studyVideoRecordBo.setCourseId(courseId);
        studyVideoRecordBo.setVideoId(videoId);
        studyVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(spliceItem, multiple));
        studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        studyVideoRecordBo.setCommitTime(new Date());
        studyVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_LEARN);
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,studyVideoRecordBo);
        return R.ok();
    }

    /**
     * 记录测试题目视频学习进度
     *
     * @param studyPlanningRecordId 学习规划记录id
     * @param courseId              课程id
     * @param videoId               视频id
     * @param questionId            题目id
     * @param spliceItem            视频进度点
     */
    @PostMapping("/recordQuestionVideoProgress")
    public R<String> recordQuestionVideoProgress(@NotNull(message = "studyPlanningRecordId不能为空") Long studyPlanningRecordId,
                                                 @NotNull(message = "courseId不能为空") Long courseId,
                                                 Long videoId,
                                                 Double multiple,
                                                 Long questionId,
                                                 String spliceItem,
                                                 Long stayPageTime
    ) {
        log.info("记录测试题目视频学习进度:{},courseId:{},videoId:{},questionId:{},spliceItem:{}", studyPlanningRecordId, courseId, videoId, questionId, spliceItem);
        if (multiple == null) {
            multiple = 1.0;
        }
        QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
        questionVideoRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        questionVideoRecordBo.setCourseId(courseId);
        questionVideoRecordBo.setVideoId(videoId);
        questionVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(spliceItem, multiple));
        questionVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        questionVideoRecordBo.setCommitTime(new Date());
        questionVideoRecordBo.setQuestionId(questionId);
        questionVideoRecordBo.setQuestionType("1");
        questionVideoRecordBo.setStudyVideoDuration(stayPageTime);
        questionVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_TEST);
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,questionVideoRecordBo);
        return R.ok();
    }

    /**
     * 记录练习题目视频学习进度
     *
     * @param studyPlanningRecordId 学习规划记录id
     * @param courseId              课程id
     * @param videoId               视频id
     * @param questionId            题目id
     * @param multiple              倍数
     * @param spliceItem            视频进度点
     */
    @PostMapping("/recordPracticeVideoProgress")
    public R<String> recordPracticeVideoProgress(@NotNull(message = "studyPlanningRecordId不能为空") Long studyPlanningRecordId,
                                                 @NotNull(message = "courseId不能为空") Long courseId,
                                                Long videoId,
                                                Long questionId,
                                                Double multiple,
                                                String spliceItem,
                                                Long stayPageTime
    ) {
        log.info("记录练习题目视频学习进度:{},courseId:{},videoId:{},questionId:{},spliceItem:{}", studyPlanningRecordId, courseId, videoId, questionId, spliceItem);
        if (multiple == null) {
            multiple = 1.0;
        }
        QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
        questionVideoRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        questionVideoRecordBo.setCourseId(courseId);
        questionVideoRecordBo.setVideoId(videoId);
        questionVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(spliceItem, multiple));
        questionVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        questionVideoRecordBo.setCommitTime(new Date());
        questionVideoRecordBo.setQuestionId(questionId);
        questionVideoRecordBo.setQuestionType("2");
        questionVideoRecordBo.setStudyVideoDuration(stayPageTime);
        questionVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_PRACTICE);
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,questionVideoRecordBo);
        return R.ok();
    }

    /**
     * 记录【预习】视频学习进度
     */
    @PostMapping("/recordPreviewProgress")
    public R<String> recordPreviewProgress(@RequestBody @Validated PreviewStudyDurationTimeBO previewStudyDurationTimeBO) {
        log.info("记录【预习】视频学习进度:{}", JSONUtil.toJsonStr(previewStudyDurationTimeBO));
        StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
        studyVideoRecordBo.setStudyPlanningRecordId(previewStudyDurationTimeBO.getStudyPlanningRecordId());
        studyVideoRecordBo.setCourseId(previewStudyDurationTimeBO.getCourseId());
        studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        studyVideoRecordBo.setCommitTime(new Date());
        studyVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_PREVIEW);
        studyVideoRecordBo.setVideoId(previewStudyDurationTimeBO.getVideoId());
        studyVideoRecordBo.setStudyVideoDuration(previewStudyDurationTimeBO.getStayPageTime());
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,studyVideoRecordBo);
        return R.ok();
    }


    /**
     * 记录【自讲】视频学习进度
     */
    @PostMapping("/recordSelfSpeechProgress")
    public R<String> recordSelfSpeechProgress(@RequestBody @Validated SelfSpeechStudyDurationTimeBO speechStudyDurationTimeBO) {
        StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
        studyVideoRecordBo.setStudyPlanningRecordId(speechStudyDurationTimeBO.getStudyPlanningRecordId());
        studyVideoRecordBo.setCourseId(speechStudyDurationTimeBO.getCourseId());
        studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        studyVideoRecordBo.setCommitTime(new Date());
        studyVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_SELF_SPEECH);
        studyVideoRecordBo.setVideoId(speechStudyDurationTimeBO.getSelfSpeechVideoId());
        studyVideoRecordBo.setStudyVideoDuration(speechStudyDurationTimeBO.getSelfSpeechTime());
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,studyVideoRecordBo);
        return R.ok();
    }

    /**
     * 批量记录学习进度
     * @param batchPlanReportProcessBO
     * @return
     */
    @PostMapping("/batchRecordVideProgress")
    public R<String> batchRecordVideProgress(@RequestBody @Validated BatchPlanReportProcessBO batchPlanReportProcessBO) {
        recordVideoProcessService.batchRecordPlanVideoProgress(batchPlanReportProcessBO, LoginHelper.getStudentId());
        return R.ok();
    }
}
