package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 试卷对象 paper
 *
 *
 * @date 2024-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("paper")
public class Paper extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 试卷Id
     */
    @TableId(value = "paper_id")
    private Long paperId;

    /**
     * 学段，对应字典 paper_stage
     */
    private String paperStage;

    /**
     * 分类，对应字典 paper_type
     */
    private String paperType;

    /**
     * 年份
     */
    private String paperGrade;

    /**
     * 归属学科(科目)（对应字典值） paper_affiliation_subject
     */
    private String paperAffiliationSubject;

    /**
     * 地区，省市县之间用空格分割
     */
    private String paperRegion;

    /**
     * 试卷标题
     */
    private String paperTitle;

    /**
     * 原卷ossId
     */
    private Long original;

    /**
     * 原卷缩略图ossId
     */
    private Long originalThumbnail;

    /**
     * 解析ossId
     */
    private Long analysis;

    /**
     * 原卷带解析ossId
     */
    private Long originalWithAnalysis;

    /**
     * 试卷来源 1管理端 2店铺端
     */
    private String paperSource;

    /**
     * 门店Id
     */
    private Long branchId;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
