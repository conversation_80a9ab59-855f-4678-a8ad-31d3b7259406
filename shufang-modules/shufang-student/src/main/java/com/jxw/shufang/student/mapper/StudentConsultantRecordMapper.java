package com.jxw.shufang.student.mapper;

import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudentConsultantRecord;
import com.jxw.shufang.student.domain.dto.StudentWithConsultantDTO;
import com.jxw.shufang.student.domain.vo.StudentConsultantRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）Mapper接口
 *
 *
 * @date 2024-02-29
 */
public interface StudentConsultantRecordMapper extends BaseMapperPlus<StudentConsultantRecord, StudentConsultantRecordVo> {

    List<StudentConsultantRecordVo> selectNewResponsibleRecord(List<Long> staffIdList,List<Long> studentIdList);

    List<StudentWithConsultantDTO> queryConsultantRecordListByStudentId(@Param("studentIds") List<Long> studentIds);
}
