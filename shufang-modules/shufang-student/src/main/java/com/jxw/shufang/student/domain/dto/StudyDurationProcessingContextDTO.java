package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/3 15:15
 * @Version 1
 * @Description 学习时长处理上下文
 */
@Data
public class StudyDurationProcessingContextDTO {
    /**
     * 存在【练】【测】视频记录
     */
    private Map<Long, StudyVideoRecord> existVideoRecordMap;
    /**
     * 存在【学】记录
     */
    private Map<Long, StudyRecord> existStudyRecordMap;
    private Map<Long, Student> studentMap;
    private Map<Long, Long> courseDurationMap;
    private StudyModuleAndGroupEnum moduleAndGroupEnum;

    /**
     * 是否忽略学习视频记录
     */
    private Boolean ignoreStudyVideoRecord;

    public Boolean ignoreRecord() {
        return null != ignoreStudyVideoRecord && ignoreStudyVideoRecord;
    }
}
