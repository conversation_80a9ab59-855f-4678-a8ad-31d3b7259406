package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.mqtt.MqttTemplate;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.MessageBo;
import com.jxw.shufang.student.domain.vo.MessageVo;
import com.jxw.shufang.student.service.IMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 消息
 * 前端访问路由地址为:/student/management/message
 *
 *
 * @date 2024-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/message")
public class MessageController extends BaseController {

    private final IMessageService messageService;
    private final MqttTemplate mqttTemplate;

    /**
     * 查询消息列表
     */
    @SaCheckPermission("student:message:list")
    @GetMapping("/list")
    public TableDataInfo<MessageVo> list(MessageBo bo, PageQuery pageQuery) {
        if (pageQuery.getSearchCount()==null) {
            pageQuery.setSearchCount(false);
        }
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            //如果按照默认的雪花算法，新数据永远比旧数据的ID大
            pageQuery.setOrderByColumn("t.message_id");
            pageQuery.setIsAsc("desc");
        }
        TableDataInfo<MessageVo> messageVoTableDataInfo = messageService.queryPageList(bo, pageQuery);
        List<MessageVo> rows = messageVoTableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是管理端的消息，也就是员工（门店管理员）接受的，我们只需要把会员的的消息标记为已读即可
            List<Long> messageIdList = rows.stream().map(MessageVo::getMessageId).toList();
            messageService.readMessages(messageIdList, UserConstants.MESSAGE_SENDER_TYPE_STUDENT);
        }
        return messageVoTableDataInfo;
    }

    /**
     * 查询新消息列表
     */
    @SaCheckPermission("student:message:list")
    @GetMapping("/newMessageList")
    public R<List<MessageVo>> newMessageList(MessageBo bo) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        pageQuery.setOrderByColumn("t.message_id");
        pageQuery.setIsAsc("desc");
        pageQuery.setSearchCount(Boolean.FALSE);
        TableDataInfo<MessageVo> messageVoTableDataInfo = messageService.queryPageList(bo, pageQuery);
        List<MessageVo> rows = messageVoTableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是管理端的消息，也就是员工（门店管理员）接受的，我们只需要把会员的的消息标记为已读即可
            List<Long> messageIdList = rows.stream().map(MessageVo::getMessageId).toList();
            messageService.readMessages(messageIdList, UserConstants.MESSAGE_SENDER_TYPE_STUDENT);
        }
        return R.ok(rows);
    }

    /**
     * 被过滤的消息列表查询
     */
    @SaCheckPermission("student:message:list")
    @GetMapping("/filterList")
    public TableDataInfo<MessageVo> filterList(MessageBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("t.message_id");
            pageQuery.setIsAsc("desc");
        }
        bo.setSendStatus(UserConstants.MESSAGE_SEND_STATUS_FILTER);
        return messageService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出消息列表
     */
    @SaCheckPermission("student:message:export")
    @Log(title = "消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MessageBo bo, HttpServletResponse response) {
        List<MessageVo> list = messageService.queryList(bo);
        ExcelUtil.exportExcel(list, "消息", MessageVo.class, response);
    }

    /**
     * 获取消息详细信息
     *
     * @param messageId 主键
     */
    @SaCheckPermission("student:message:query")
    @GetMapping("/{messageId}")
    public R<MessageVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long messageId) {
        return R.ok(messageService.queryById(messageId));
    }

    /**
     * 新增消息
     */
    @SaCheckPermission("student:message:add")
    @Log(title = "消息", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000, message = "请勿发送消息过快")
    @PostMapping()
    public R<MessageVo> add(@Validated(AddGroup.class) @RequestBody MessageBo bo) {
        if (!LoginHelper.isBranchUser()) {
            return R.fail("非门店用户，无权操作");
        }
        if (bo.getMessageStudentId() == null) {
            return R.fail("会员ID不能为空");
        }
        if (UserConstants.MESSAGE_TYPE_TEXT.equals(bo.getContentType()) && StringUtils.isBlank(bo.getMessageConcat())) {
            return R.fail("文本消息内容不能为空");
        }
        if (UserConstants.MESSAGE_TYPE_IMAGE.equals(bo.getContentType()) && bo.getMessageResources() == null) {
            return R.fail("图片消息资源不能为空");
        }
        bo.setSendUserType(UserConstants.MESSAGE_SENDER_TYPE_STAFF);
        bo.setMessageStaffId(LoginHelper.getUserId());
        String s = messageService.insertByBo(bo);
        if (StringUtils.isNotBlank(s)) {
            return R.fail(s);
        }

        // mqtt通知安卓端有新留言
        CompletableFuture.runAsync(()-> mqttTemplate.publish(bo.getContentType()));

        return R.ok(messageService.queryById(bo.getMessageId()));
    }

    /**
     * 修改消息
     */
    @SaCheckPermission("student:message:edit")
    @Log(title = "消息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MessageBo bo) {
        return toAjax(messageService.updateByBo(bo));
    }

    /**
     * 删除消息
     *
     * @param messageIds 主键串
     */
    @SaCheckPermission("student:message:remove")
    @Log(title = "消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] messageIds) {
        return toAjax(messageService.deleteWithValidByIds(List.of(messageIds), true));
    }


    /**
     * 获取留言列表
     */
    @GetMapping("/stuMessageInfoPage")
    @SaCheckPermission("student:message:list")
    public TableDataInfo<MessageVo> stuMessageInfoPage(MessageBo bo, PageQuery pageQuery) {
        return messageService.stuMessageInfoPage(bo, pageQuery);
    }

    @GetMapping("/mqtt/test")
    public R<Boolean> testMqttSend(@RequestParam String message) {
        return mqttTemplate.publish(message) ? R.ok(true) : R.ok(false);
    }

}
