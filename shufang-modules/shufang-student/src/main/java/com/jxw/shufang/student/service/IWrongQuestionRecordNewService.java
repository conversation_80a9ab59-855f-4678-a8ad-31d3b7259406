package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.WrongQuestionRecordNewBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordNewVo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IWrongQuestionRecordNewService {

    TableDataInfo<WrongQuestionRecordNewVo> pageWrongQuestionRecord(WrongQuestionRecordNewBo wrongQuestionRecordNewBo, PageQuery pageQuery);

    Boolean insertBatchByBo(List<WrongQuestionRecordNewBo> collect);

    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<WrongQuestionRecordNewVo> queryList(WrongQuestionRecordNewBo bo);

    Boolean batchUpdate(List<WrongQuestionRecordNewVo> voList);

    Boolean revise(WrongQuestionRecordNewBo bo);
}
