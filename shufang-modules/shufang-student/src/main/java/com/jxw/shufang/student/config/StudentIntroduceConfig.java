package com.jxw.shufang.student.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "biz.student.introduce")
public class StudentIntroduceConfig {
    private String freezeTimeUnit = "DAYS";
    /**
     * 体验卡id 不是体验卡的认为是正式卡
     */
    private Long experienceStudentTypeId = 1839502186175639554L;
}
