package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import com.jxw.shufang.student.service.IStudyRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习记录
 * 前端访问路由地址为:/student/studyRecord
 *
 *
 * @date 2024-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studyRecord")
public class StudyRecordController extends BaseController {

    private final IStudyRecordService studyRecordService;

    /**
     * 查询学习记录列表
     */
    @SaCheckPermission("student:studyRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudyRecordVo> list(StudyRecordBo bo, PageQuery pageQuery) {
        return studyRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出学习记录列表
     */
    @SaCheckPermission("student:studyRecord:export")
    @Log(title = "学习记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyRecordBo bo, HttpServletResponse response) {
        List<StudyRecordVo> list = studyRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习记录", StudyRecordVo.class, response);
    }

    /**
     * 获取学习记录详细信息
     *
     * @param studyRecordId 主键
     */
    @SaCheckPermission("student:studyRecord:query")
    @GetMapping("/{studyRecordId}")
    public R<StudyRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studyRecordId) {
        return R.ok(studyRecordService.queryById(studyRecordId));
    }

    /**
     * 新增学习记录
     */
    @SaCheckPermission("student:studyRecord:add")
    @Log(title = "学习记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyRecordBo bo) {
        return toAjax(studyRecordService.insertByBo(bo));
    }

    /**
     * 修改学习记录
     */
    @SaCheckPermission("student:studyRecord:edit")
    @Log(title = "学习记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyRecordBo bo) {
        return toAjax(studyRecordService.updateByBo(bo));
    }

    /**
     * 删除学习记录
     *
     * @param studyRecordIds 主键串
     */
    @SaCheckPermission("student:studyRecord:remove")
    @Log(title = "学习记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyRecordIds) {
        return toAjax(studyRecordService.deleteWithValidByIds(List.of(studyRecordIds), true));
    }
}
