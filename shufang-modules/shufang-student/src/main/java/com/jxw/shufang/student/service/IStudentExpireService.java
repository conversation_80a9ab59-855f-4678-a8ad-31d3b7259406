package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentExpire;
import com.jxw.shufang.student.domain.bo.StudentExpireBo;
import com.jxw.shufang.student.domain.vo.StudentExpireVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）Service接口
 *
 *
 * @date 2024-03-08
 */
public interface IStudentExpireService {

    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）
     */
    StudentExpireVo queryById(Long studentExpireId);

    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    TableDataInfo<StudentExpireVo> queryPageList(StudentExpireBo bo, PageQuery pageQuery);

    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    List<StudentExpireVo> queryList(StudentExpireBo bo);

    /**
     * 新增会员过期（用于 会员过期列 的展示和数据操作）
     */
    Boolean insertByBo(StudentExpireBo bo);

    /**
     * 批量新增会员过期（用于 会员过期列 的展示和数据操作）
     */
    Boolean insertBatchByBo(List<StudentExpireBo> boList);

    /**
     * 修改会员过期（用于 会员过期列 的展示和数据操作）
     */
    Boolean updateByBo(StudentExpireBo bo);

    /**
     * 校验并批量删除会员过期（用于 会员过期列 的展示和数据操作）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除会员过期记录
     */
    Boolean removeStudentExpireRecord(Long studentId);


    StudentExpire queryStudentExpireById(Long studentExpireId);

    void cleanCache();

    /**
     * 通过会员Id过滤掉过期的会员ID，返回没有过期的会员ID列表
     * @param studentIdList 会员ID列表
     */
    List<Long> filterExpireStudentId(List<Long> studentIdList);
}
