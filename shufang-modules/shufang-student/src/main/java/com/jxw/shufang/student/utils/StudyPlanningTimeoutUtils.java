package com.jxw.shufang.student.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 学习规划超时计算工具类
 *
 * <AUTHOR>
 * @date 2024-06-15
 */
@Slf4j
public class StudyPlanningTimeoutUtils {

    /**
     * 春秋模式类型
     */
    public static final int SPRING_AUTUMN_MODE = 1;

    /**
     * 寒暑模式类型
     */
    public static final int WINTER_SUMMER_MODE = 2;

    /**
     * 计算超时时间
     *
     * @param modeType 模式类型：1-春秋模式，2-寒暑模式
     * @param baseTime 基准时间（通常是关联记录的创建时间）
     * @return 超时时间
     */
    public static Date calculateTimeoutTime(Integer modeType, Date baseTime) {
        if (baseTime == null) {
            return null;
        }

        LocalDateTime baseDateTime = baseTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDate baseDate = baseDateTime.toLocalDate();
        LocalDateTime timeoutDateTime;

        if (SPRING_AUTUMN_MODE == modeType) {
            // 春秋模式：这周五00:00:00算超时
            timeoutDateTime = calculateSpringAutumnTimeout(baseDate);
        } else if (WINTER_SUMMER_MODE == modeType) {
            // 寒暑模式：上周六00:00:00算超时
            timeoutDateTime = calculateWinterSummerTimeout(baseDate);
        } else {
            log.warn("未知的模式类型: {}, 使用默认超时计算", modeType);
            // 默认使用春秋模式
            timeoutDateTime = calculateSpringAutumnTimeout(baseDate);
        }

        return Date.from(timeoutDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算春秋模式的超时时间
     * 规则：这周五00:00:00算超时
     *
     * @param baseDate 基准日期
     * @return 超时时间
     */
    private static LocalDateTime calculateSpringAutumnTimeout(LocalDate baseDate) {
        // 找到这周的周五
        LocalDate thisFriday = baseDate.with(DayOfWeek.FRIDAY);
        
        // 如果基准日期已经是周五之后，则取下周五
        if (baseDate.isAfter(thisFriday)) {
            thisFriday = thisFriday.plusWeeks(1);
        }
        
        return thisFriday.atTime(LocalTime.MIDNIGHT);
    }

    /**
     * 计算寒暑模式的超时时间
     * 规则：上周六00:00:00算超时
     *
     * @param baseDate 基准日期
     * @return 超时时间
     */
    private static LocalDateTime calculateWinterSummerTimeout(LocalDate baseDate) {
        // 找到上周的周六
        LocalDate lastSaturday = baseDate.with(DayOfWeek.SATURDAY).minusWeeks(1);
        
        return lastSaturday.atTime(LocalTime.MIDNIGHT);
    }

    /**
     * 判断是否超时
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @param currentTime 当前时间
     * @return 是否超时
     */
    public static boolean isTimeout(Integer modeType, Date relationCreateTime, Date currentTime) {
        if (relationCreateTime == null || currentTime == null) {
            return false;
        }

        Date timeoutTime = calculateTimeoutTime(modeType, relationCreateTime);
        if (timeoutTime == null) {
            return false;
        }

        return currentTime.after(timeoutTime);
    }

    /**
     * 判断是否超时（使用当前时间）
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @return 是否超时
     */
    public static boolean isTimeout(Integer modeType, Date relationCreateTime) {
        return isTimeout(modeType, relationCreateTime, new Date());
    }

    /**
     * 获取超时状态描述
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @return 超时状态描述
     */
    public static String getTimeoutStatusDescription(Integer modeType, Date relationCreateTime) {
        if (relationCreateTime == null) {
            return "无关联记录";
        }

        boolean timeout = isTimeout(modeType, relationCreateTime);
        Date timeoutTime = calculateTimeoutTime(modeType, relationCreateTime);
        
        String modeDesc = SPRING_AUTUMN_MODE == modeType ? "春秋模式" : "寒暑模式";
        String timeoutDesc = timeout ? "已超时" : "未超时";
        
        return String.format("%s - %s (超时时间: %s)", modeDesc, timeoutDesc, 
                timeoutTime != null ? timeoutTime.toString() : "未知");
    }

    /**
     * 计算距离超时的剩余时间（毫秒）
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @return 剩余时间（毫秒），负数表示已超时
     */
    public static long getTimeoutRemainingMillis(Integer modeType, Date relationCreateTime) {
        if (relationCreateTime == null) {
            return 0;
        }

        Date timeoutTime = calculateTimeoutTime(modeType, relationCreateTime);
        if (timeoutTime == null) {
            return 0;
        }

        return timeoutTime.getTime() - System.currentTimeMillis();
    }

    /**
     * 格式化剩余时间
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @return 格式化的剩余时间字符串
     */
    public static String formatRemainingTime(Integer modeType, Date relationCreateTime) {
        long remainingMillis = getTimeoutRemainingMillis(modeType, relationCreateTime);

        if (remainingMillis <= 0) {
            return "已超时";
        }

        long days = remainingMillis / (24 * 60 * 60 * 1000);
        long hours = (remainingMillis % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (remainingMillis % (60 * 60 * 1000)) / (60 * 1000);

        if (days > 0) {
            return String.format("%d天%d小时", days, hours);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }

    /**
     * 判断反馈是否超时
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @param feedbackStatus 反馈状态：0-待反馈，1-已反馈
     * @return 是否超时反馈：0-否，1-是
     */
    public static Integer isOvertimeFeedback(Integer modeType, Date relationCreateTime, Integer feedbackStatus) {
        // 如果已经反馈，则不算超时
        if (feedbackStatus != null && feedbackStatus == 1) {
            return 0;
        }

        // 如果没有关联记录，不算超时
        if (relationCreateTime == null) {
            return 0;
        }

        // 判断是否超时
        boolean timeout = isTimeout(modeType, relationCreateTime);
        return timeout ? 1 : 0;
    }

    /**
     * 判断规划是否超时
     *
     * @param modeType 模式类型
     * @param relationCreateTime 关联记录创建时间
     * @param planningStatus 规划状态：0-待规划，1-已规划
     * @return 是否超时规划：0-否，1-是
     */
    public static Integer isOvertimePlanning(Integer modeType, Date relationCreateTime, Integer planningStatus) {
        // 如果已经规划，则不算超时
        if (planningStatus != null && planningStatus == 1) {
            return 0;
        }

        // 如果没有关联记录，不算超时
        if (relationCreateTime == null) {
            return 0;
        }

        // 判断是否超时
        boolean timeout = isTimeout(modeType, relationCreateTime);
        return timeout ? 1 : 0;
    }
}
