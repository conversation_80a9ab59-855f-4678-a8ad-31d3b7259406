package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentAnswerRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentAnswerRecord.class, reverseConvertGenerate = false)
public class StudentAnswerRecordBo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    private Long studentAnswerRecordId;

    /**
     * 用户id
     */
    private Long studentId;

    /**
     * 试卷ID
     */
    @NotNull(message = "试卷ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long testPaperId;


    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long questionId;

    private Long studentPaperRecordId;
    /**
     * 来源
     */
    @NotNull(message = "来源不能为空", groups = {AddGroup.class, EditGroup.class})
    private String source;
    /**
     * 0：正确
     * 1：错误
     */
    private Integer isMistake;

    private Integer stayTime;

    private String answer;


    private Date startTime;

    private Date finishTime;


}
