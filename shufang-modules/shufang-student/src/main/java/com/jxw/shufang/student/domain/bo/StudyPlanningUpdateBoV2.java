package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanning;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
public class StudyPlanningUpdateBoV2 extends BaseEntity {

    /**
     * 学习规划id
     */
    @NotNull(message = "学习规划记录ID不能为空", groups = { EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 学习开始时间
     */
    @NotNull(message = "学习开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyStartTime;

    /**
     * 学习开始时间
     */
    @NotNull(message = "学习计划时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDate;

    /**
     * 学习结束时间
     */
    @NotNull(message = "学习结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyEndTime;


}
