package com.jxw.shufang.student.domain.bo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/8 16:11
 * @Version 1
 * @Description
 */
@Data
public class BatchPlanReportProcessBO {
    @NotEmpty(message = "学习记录不能为空")
    @Size(max = 20, message = "记录数量不能超过20条")
    private List<RecordProcess> records;

    @Data
    @Valid
    public static class RecordProcess {
        @NotBlank(message = "学习类型不能为空")
        private String studyType;
        @NotNull(message = "学习记录不能为空")
        private Long studyPlanningRecordId;
        @NotNull(message = "课程id不能为空")
        private Long courseId;
        @NotNull(message = "视频id不能为空")
        private Long videoId;
        private Long questionId;
        @NotNull(message = "倍数不能为空")
        private Double multiple;
        @NotNull(message = "视频分片数据不能为空")
        private String spliceItem;
    }
}
