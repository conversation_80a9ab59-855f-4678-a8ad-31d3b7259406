package com.jxw.shufang.student.controller.wechat.officialaccount;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.student.domain.vo.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.CacheConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteSmsService;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.service.IStudentParentRecordService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.student.service.IStudyPlanningService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 会员绑定家长记录--公众号端
 * 前端访问路由地址为:/student/officialAccount/parentRecord
 *
 * @date 2024-03-11
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/officialAccount/parentRecord")
public class OaStudentParentRecordController extends BaseController {

    private final IStudentParentRecordService studentParentRecordService;

    private final IStudentService studentService;

    @DubboReference
    private RemoteSmsService remoteSmsService;

    private final IStudyPlanningService studyPlanningService;


    /**
     * 发送验证码接口
     * 这里就不加限流注解了，因为短信验证码发送频率（代码实现了）有限制，所以不用担心频繁发送验证码
     *
     * @param phone 手机号
     */
    @PostMapping("/sendSmsCode")
    @Log(title = "公众号端-绑定学生-发送验证码", businessType = BusinessType.INSERT)
    public R<Void> sendCode(@NotBlank(message = "手机号不能为空") String phone) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setSearchCount(false);
        TableDataInfo<StudyPlanningVo> studyPlanningVoTableDataInfo = studyPlanningService.queryStudyPlanPage(new StudyPlanningBo(), pageQuery);
        //TODO 校验是一个家长
        List<StudentVo> studentVoList = DataPermissionHelper.ignore(() -> studentService.queryByStuPhoneOrParentPhone(phone));
        if (CollUtil.isEmpty(studentVoList)) {
            return R.fail("此手机号码未检索到会员或家长信息");
        }
        remoteSmsService.sendSmsCaptcha(phone);
        return R.ok();
    }

    /**
     * 绑定
     *
     * @param phone 手机号
     * @param code  验证码
     */
    @PostMapping("/bind")
    @RepeatSubmit()
    @Log(title = "公众号端-绑定学生-绑定", businessType = BusinessType.INSERT)
    public R<Void> bind(@NotBlank(message = "手机号不能为空") String phone,
                        @NotBlank(message = "验证码不能为空") String code,
                        @NotBlank(message = "keyId不能为空") String keyId) {

        String openId = LoginHelper.getWechatOpenId();

        if (null == openId) {
            openId = RedisUtils.getCacheObject(CacheConstants.WX_OPEN_ID + keyId);
        }
        log.info("keyId:" + keyId + " openId:" + openId);

        remoteSmsService.checkSmsCaptcha(phone, code);
        List<StudentVo> studentVoList = DataPermissionHelper.ignore(() -> studentService.queryByStuPhoneOrParentPhone(phone));
        if (CollUtil.isEmpty(studentVoList)) {
            return R.fail("此手机号码未检索到会员或家长信息");
        }

        StudentParentRecordBo parentInfo = new StudentParentRecordBo();
        parentInfo.setParentWechatOpenId(openId);
        studentParentRecordService.bind(studentVoList, parentInfo);
        return R.ok();
    }


    /**
     * 查询绑定的会员列表
     */
    @GetMapping("/queryBindList")
    public R<List<StudentParentRecordVo>> queryBindList(StudentParentRecordBo studentParentRecordBo) {
        String openId = "-1";
        if (StringUtils.isNotBlank(studentParentRecordBo.getKeyId())) {
            openId = RedisUtils.getCacheObject(CacheConstants.WX_OPEN_ID + studentParentRecordBo.getKeyId());
        }
        studentParentRecordBo.setParentWechatOpenId(openId);
        List<StudentParentRecordVo> studentParentRecordVoList = studentParentRecordService.queryBindList(studentParentRecordBo);
        return R.ok(studentParentRecordVoList);

    }

    /**
     * 查询打卡记录
     */
    @GetMapping("/queryAttendanceRecordList")
    public R<List<StudyPlanningRecordVo>> queryAttendanceRecordList(StudentParentRecordBo studentParentRecordBo) {
        String openId = "-1";
        if (StringUtils.isNotBlank(studentParentRecordBo.getKeyId())) {
            openId = RedisUtils.getCacheObject(CacheConstants.WX_OPEN_ID + studentParentRecordBo.getKeyId());
        }
        studentParentRecordBo.setParentWechatOpenId(openId);
        return R.ok(studentParentRecordService.queryAttendanceRecordList(studentParentRecordBo));
    }

    /**
     * 解绑
     */
    @PostMapping("/unbindParent")
    @RepeatSubmit()
    @Log(title = "公众号端-绑定学生-解绑", businessType = BusinessType.DELETE)
    public R<Void> unbindParent(@NotNull(message = "会员id不能为空") Long studentId) {
        return toAjax(DataPermissionHelper.ignore(() -> studentService.unbindParent(studentId)));
    }

    /**
     * 公众号查询打卡记录
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/attendanceLog")
    public TableDataInfo<AttendanceLogStudentEzkecoVo> pageAttendanceRecord(StudentParentRecordBo bo, PageQuery pageQuery) {
        String openId = "-1";
        if (StringUtils.isNotBlank(bo.getKeyId())) {
            openId = RedisUtils.getCacheObject(CacheConstants.WX_OPEN_ID + bo.getKeyId());
        }
        bo.setParentWechatOpenId(openId);
        bo.setWithStudentSysUserInfo(Boolean.TRUE);
        return studentParentRecordService.pageAttendanceRecord(bo, pageQuery);
    }
}
