package com.jxw.shufang.student.controller.android;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentAnswerRecordBo;
import com.jxw.shufang.student.service.IStudentAnswerRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户提交答案
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paper/answer")
public class AStudentAnswerRecordController extends BaseController {

    private final IStudentAnswerRecordService studentAnswerRecordService;


    @PostMapping("/submit")
    @Log(title = "用户提交答案", businessType = BusinessType.INSERT)
    public R<Boolean> submit(@Validated(AddGroup.class) StudentAnswerRecordBo bo) {
        return R.ok(studentAnswerRecordService.insertByBo(bo));
    }


    @PostMapping("/submit/batch")
    @Log(title = "用户提交答案", businessType = BusinessType.INSERT)
    public R<Boolean> submitList(@Validated(AddGroup.class) StudentAnswerRecordBo bo) {
        return R.ok(studentAnswerRecordService.insertByBo(bo));
    }

}
