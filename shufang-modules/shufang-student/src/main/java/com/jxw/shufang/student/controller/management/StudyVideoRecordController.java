package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordStatisticVo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;
import com.jxw.shufang.student.service.IStudyVideoRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习视频记录（视频观看记录）
 * 前端访问路由地址为:/student/videoRecord
 *
 *
 * @date 2024-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/videoRecord")
public class StudyVideoRecordController extends BaseController {

    private final IStudyVideoRecordService studyVideoRecordService;

    /**
     * 查询学习视频记录（视频观看记录）列表
     */
    @SaCheckPermission("student:videoRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudyVideoRecordVo> list(StudyVideoRecordBo bo, PageQuery pageQuery) {
        return studyVideoRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询学习视频播放统计数据
     * @param studentId 会员ID
     */
    @SaCheckPermission("student:videoRecord:list")
    @GetMapping("/videoRecordStatistic")
    public R<StudyVideoRecordStatisticVo> videoRecordStatistic(@NotNull(message = "会员ID不能为空") Long studentId) {
        return R.ok(studyVideoRecordService.videoRecordStatistic(studentId));
    }

    /**
     * 导出学习视频记录（视频观看记录）列表
     */
    @SaCheckPermission("student:videoRecord:export")
    @Log(title = "学习视频记录（视频观看记录）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyVideoRecordBo bo, HttpServletResponse response) {
        List<StudyVideoRecordVo> list = studyVideoRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "学习视频记录（视频观看记录）", StudyVideoRecordVo.class, response);
    }

    /**
     * 获取学习视频记录（视频观看记录）详细信息
     *
     * @param studyVideoRecordId 主键
     */
    @SaCheckPermission("student:videoRecord:query")
    @GetMapping("/{studyVideoRecordId}")
    public R<StudyVideoRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studyVideoRecordId) {
        return R.ok(studyVideoRecordService.queryById(studyVideoRecordId));
    }

    /**
     * 新增学习视频记录（视频观看记录）
     */
    @SaCheckPermission("student:videoRecord:add")
    @Log(title = "学习视频记录（视频观看记录）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyVideoRecordBo bo) {
        return toAjax(studyVideoRecordService.insertByBo(bo));
    }

    /**
     * 修改学习视频记录（视频观看记录）
     */
    @SaCheckPermission("student:videoRecord:edit")
    @Log(title = "学习视频记录（视频观看记录）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyVideoRecordBo bo) {
        return toAjax(studyVideoRecordService.updateByBo(bo));
    }

    /**
     * 删除学习视频记录（视频观看记录）
     *
     * @param studyVideoRecordIds 主键串
     */
    @SaCheckPermission("student:videoRecord:remove")
    @Log(title = "学习视频记录（视频观看记录）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studyVideoRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studyVideoRecordIds) {
        return toAjax(studyVideoRecordService.deleteWithValidByIds(List.of(studyVideoRecordIds), true));
    }
}
