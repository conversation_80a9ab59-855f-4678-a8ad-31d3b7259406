package com.jxw.shufang.student.controller.android;


import cn.hutool.json.JSONUtil;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.student.domain.bo.AiReportVideoDurationTimeBO;
import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.MqTemplateSendMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/1 20:36
 * @Version 1
 * @Description ai评测相关
 */
@RestController
@RequestMapping("/android/aiText")
@RequiredArgsConstructor
@Slf4j
public class AiTestRecordTimeController {
    private final MqTemplateSendMessageService sendMessageService;
    /**
     * 记录AI错题观看时长记录
     */
    @PostMapping("/videoProgress")
    public R<String> analysisProgress(@RequestBody @Validated AiReportVideoDurationTimeBO aiTestProgressBO) {
        log.info("记录AI错题观看时长记录:{}", JSONUtil.toJsonStr(aiTestProgressBO));
        AiStudyVideoRecordBo studyVideoRecordBo = new AiStudyVideoRecordBo();
        studyVideoRecordBo.setVideoId(aiTestProgressBO.getVideoId());
        studyVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(aiTestProgressBO.getSpliceItem(), 1.0d));
        studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        studyVideoRecordBo.setTestPaperId(aiTestProgressBO.getTestPaperId());
        studyVideoRecordBo.setCommitTime(new Date());
        studyVideoRecordBo.setModuleAndGroupEnum(StudyModuleAndGroupEnum.AI_ERROR_VIDEO);
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.AI_STUDY_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag, studyVideoRecordBo);
        return R.ok();
    }
}
