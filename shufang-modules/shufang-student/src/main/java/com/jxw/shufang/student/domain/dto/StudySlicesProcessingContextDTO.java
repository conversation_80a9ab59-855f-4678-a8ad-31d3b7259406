package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/7 16:50
 * @Version 1
 * @Description 学习时长分片进度上下文
 */
@Data
public class StudySlicesProcessingContextDTO {
    /**
     * 存在学习视频记录
     */
    private Map<Long, List<QuestionVideoRecord>> existQuestionVideoRecordMap;
    /**
     * 存在AI学习记录
     */
    private Map<Long, AiStudyRecord> existAiStudyRecordMap;
    /**
     * 存在学习记录
     */
    private Map<Long, StudyRecord> existStudyRecordMap;
    private Map<Long, Student> studentMap;
    /**
     * 课程时长
     */
    private Map<Long, Long> courseDurationMap;
    private StudyModuleAndGroupEnum studyModuleTypeEnum;

    /**
     * 是否忽略学习视频记录
     */
    private Boolean ignoreStudyVideoRecord;

    public Boolean ignoreRecord() {
        return null != ignoreStudyVideoRecord && ignoreStudyVideoRecord;
    }
}
