package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 错题合集对象 wrong_question_collection
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wrong_question_collection")
public class WrongQuestionCollection extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题合集id
     */
    @TableId(value = "wrong_question_collection_id")
    private Long wrongQuestionCollectionId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 错题合集状态 0-未订正 1-已订正
     */
    private Integer collectionStatus;

    /**
     * 错题合集类型 1-打印 2-上周错题汇总
     */
    private Integer collectionType;

    /**
     * 订正截图（oss_id，多个，逗号隔开）
     */
    private String reviseScreenshots;

    /**
     * 错题评语
     */
    private String remark;

}
