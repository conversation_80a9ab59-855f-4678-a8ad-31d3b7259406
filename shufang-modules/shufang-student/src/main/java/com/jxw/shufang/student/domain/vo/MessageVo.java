package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.Message;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 消息视图对象 message
 *
 *
 * @date 2024-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Message.class)
public class MessageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息id
     */
    @ExcelProperty(value = "消息id")
    private Long messageId;

    /**
     * 消息发送者类型 1 员工（或门店管理员） 2会员
     */
    @ExcelProperty(value = "消息发送者类型 1 员工 2会员 ")
    private String sendUserType;

    /**
     * 消息类型 1文本 2图片
     */
    @ExcelProperty(value = "消息类型 1文本 2图片")
    private String contentType;

    /**
     * 消息内容,发送类型为文本时，这里有值
     */
    @ExcelProperty(value = "消息内容,发送类型为文本时，这里有值")
    private String messageConcat;

    /**
     * 员工或者门店管理员的userid
     */
    private Long messageStaffId;

    /**
     * 会员id
     */
    private Long messageStudentId;
    /**
     * 发送的为图片资源时，里面放ossId
     */
    @ExcelProperty(value = "发送的为图片资源时，里面放ossId")
    private Long messageResources;

    /**
     * 接受者读取消息状态（1已读 2未读）
     */
    @ExcelProperty(value = "接受者读取消息状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=已读,2=未读")
    private String readStatus;

    /**
     * 发送状态（1发送成功 2被过滤）
     */
    @ExcelProperty(value = "发送状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=发送成功,2=被过滤")
    private String sendStatus;

    /**
     * 敏感词id（被过滤才有）
     */
    @ExcelProperty(value = "敏感词id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "被=过滤才有")
    private Long sensitiveId;

    /**
     * 会话数
     */
    private Integer chatCount;

    /**
     * 最后一条消息内容
     */
    private String lastMessageConcat;

    /**
     * 最后一条消息时间
     */
    private Date lastCreateTime;

    /**
     * 是否有未读消息
     */
    private Boolean hasUnread;

    private Date createTime;


    private StudentVo student;

    private RemoteStaffVo consultant;

    /**
     * 里面放的并非staff，因为门店管理员不是员工，但是又可以发信息，所以这里放sysUser的信息更合适
     */
    private RemoteUserVo messageStaffInfo;

    /**
     * 未读消息数
     */
    private Long unreadCount;


}
