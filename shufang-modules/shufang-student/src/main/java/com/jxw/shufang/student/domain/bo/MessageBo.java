package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Message;

import java.util.List;

/**
 * 消息业务对象 message
 *
 *
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Message.class, reverseConvertGenerate = false)
public class MessageBo extends BaseEntity {

    /**
     * 消息id
     */
    @NotNull(message = "消息id不能为空", groups = { EditGroup.class })
    private Long messageId;

    /**
     * 消息发送者类型 1 员工 2会员
     */
    //@NotBlank(message = "消息发送者类型 1 员工 2会员 不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sendUserType;

    /**
     * 消息类型 1文本 2图片
     */
    @NotBlank(message = "消息类型 1文本 2图片不能为空", groups = { AddGroup.class, EditGroup.class })
    private String contentType;

    /**
     * 消息内容,发送类型为文本时，这里有值
     */
    //@NotBlank(message = "消息内容,发送类型为文本时，这里有值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String messageConcat;

    /**
     * 员工或者门店管理员的userid
     */
    private Long messageStaffId;

    /**
     * 会员id
     */
    private Long messageStudentId;

    /**
     * 发送的为图片资源时，里面放ossId
     */
    //@NotNull(message = "发送的为图片资源时，里面放ossId不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long messageResources;

    /**
     * 接受者读取消息状态（1已读 2未读）
     */
    //@NotBlank(message = "接受者读取消息状态（1已读 2未读）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String readStatus;

    /**
     * 发送状态（1发送成功 2被过滤）
     */
    //@NotBlank(message = "发送状态（1发送成功 2被过滤）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sendStatus;


    /**
     * 敏感词id（被过滤才有）
     */
    //@NotNull(message = "敏感词id（被过滤才有）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sensitiveId;


    private Boolean withStudentInfo;

    private Boolean withStudentSysUserInfo;

    private Boolean withMessageStaffInfo;

    private Boolean withConsultantInfo;


    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 会员顾问
     */
    private Long consultantId;



    private List<String> sendStatusList;

    /**
     * 小于message_id ,主要用于下拉式分页
     */
    private Long ltMessageId;

    /**
     * 大于message_id
     */
    private Long gtMessageId;


    private List<Long> messageStudentIdList;


}
