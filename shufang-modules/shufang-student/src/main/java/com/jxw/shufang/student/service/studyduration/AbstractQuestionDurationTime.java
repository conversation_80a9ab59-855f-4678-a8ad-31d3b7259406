package com.jxw.shufang.student.service.studyduration;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.student.domain.*;
import com.jxw.shufang.student.domain.dto.*;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.IAiStudyRecordService;
import com.jxw.shufang.student.service.IQuestionVideoRecordService;
import com.jxw.shufang.student.service.IStudyRecordService;
import com.jxw.shufang.student.service.studyduration.processor.AiStudyTimeProcessor;
import com.jxw.shufang.student.service.studyduration.processor.QuestionVideoTimeProcessor;
import com.jxw.shufang.student.service.studyduration.processor.StudyRecordTimeProcessor;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/7 20:49
 * @Version 1
 * @Description
 */
@Service
public abstract class AbstractQuestionDurationTime<T> implements ModuleGroupProvider<T> {
    @Resource
    private IStudyRecordService studyRecordService;
    @Resource
    private IQuestionVideoRecordService questionVideoRecordService;
    @Resource
    private StudyRecordTimeContentService studyRecordTimeContentService;

    @Resource
    private IAiStudyRecordService aiStudyRecordService;

    public abstract StudySlicesProcessingContextDTO contextData(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum);

    public abstract SaveOrUpdateQuestionProcessDTO buildQuestionProcessDTO(T recordBo);

    public abstract SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(T recordBo);

    @GlobalTransactional(rollbackFor = Exception.class)
    public void process(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<T> filterData = this.filterData(records, moduleAndGroupEnum);
        if (CollectionUtil.isEmpty(filterData)) {
            return;
        }

        // 设置上下文数据
        StudySlicesProcessingContextDTO contextData = this.contextData(filterData, moduleAndGroupEnum);
        if (contextData.ignoreRecord()) {
            return;
        }

        StudyModelGroupEnum groupEnum = moduleAndGroupEnum.getGroupEnum();

        if (groupEnum == StudyModelGroupEnum.AI_STUDY) {
            processAiStudy(filterData, contextData);
        } else if (groupEnum == StudyModelGroupEnum.STUDY_PLANNING) {
            processStudyPlan(filterData, contextData);
        } else {

        }

    }

    private void processStudyPlan(List<T> records, StudySlicesProcessingContextDTO contextData) {
        QuestionVideoTimeProcessor questionVideoTimeProcessor = new QuestionVideoTimeProcessor(contextData);
        StudyRecordTimeProcessor studyProcessor = new StudyRecordTimeProcessor(this.getStudyRecordProcessingContextData(contextData));
        records.forEach(recordBo -> {
            questionVideoTimeProcessor.processByCourseId(this.buildQuestionProcessDTO(recordBo));
            studyProcessor.studyProcessByQuestionRecord(this.buildStudyRecordProcessDTO(recordBo));
        });
        this.batchSaveOrUpdateStudyPlan(questionVideoTimeProcessor, studyProcessor);
    }

    private void processAiStudy(List<T> records, StudySlicesProcessingContextDTO contextData) {
        QuestionVideoTimeProcessor questionVideoTimeProcessor = new QuestionVideoTimeProcessor(contextData);
        AiStudyTimeProcessor aiStudyTimeProcessor = new AiStudyTimeProcessor(this.getAiStudyContext(contextData));

        records.forEach(recordBo -> {
            questionVideoTimeProcessor.processByCourseId(this.buildQuestionProcessDTO(recordBo));

            SaveOrUpdateStudyRecordDTO saveOrUpdateStudyRecordDTO = this.buildStudyRecordProcessDTO(recordBo);

            SaveOrUpdateAiStudyRecordDTO aiStudyRecordDTO = new SaveOrUpdateAiStudyRecordDTO();
            aiStudyRecordDTO.setCourseId(saveOrUpdateStudyRecordDTO.getCourseId());
            aiStudyRecordDTO.setStudentId(saveOrUpdateStudyRecordDTO.getStudentId());
            aiStudyRecordDTO.setVideoId(saveOrUpdateStudyRecordDTO.getVideoId());
            aiStudyRecordDTO.setStudyVideoDuration(saveOrUpdateStudyRecordDTO.getStudyVideoDuration());
            aiStudyRecordDTO.setCommitTime(saveOrUpdateStudyRecordDTO.getCommitTime());
            aiStudyRecordDTO.setStudyVideoSlices(saveOrUpdateStudyRecordDTO.getStudyVideoSlices());
            aiStudyTimeProcessor.process(aiStudyRecordDTO);
        });
        this.batchSaveOrUpdateAiStudy(questionVideoTimeProcessor, aiStudyTimeProcessor);
    }

    private void batchSaveOrUpdateAiStudy(QuestionVideoTimeProcessor questionVideoTimeProcessor, AiStudyTimeProcessor aiStudyTimeProcessor) {
        List<QuestionVideoRecord> videoProcessorInserts = questionVideoTimeProcessor.getInserts();
        List<QuestionVideoRecord> videoProcessorUpdates = questionVideoTimeProcessor.getUpdates();
        List<AiStudyRecord> studyProcessorInserts = aiStudyTimeProcessor.getInserts();
        List<AiStudyRecord> studyProcessorUpdates = aiStudyTimeProcessor.getUpdates();
        if (CollectionUtil.isNotEmpty(videoProcessorUpdates)) {
            questionVideoRecordService.updateBatchById(videoProcessorUpdates);
        }
        if (CollectionUtil.isNotEmpty(videoProcessorInserts)) {
            questionVideoRecordService.saveBatch(videoProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorInserts)) {
            aiStudyRecordService.batchInsert(studyProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorUpdates)) {
            aiStudyRecordService.batchUpdate(studyProcessorUpdates);
        }
    }

    private AiStudyDurationProcessingContextDTO getAiStudyContext(StudySlicesProcessingContextDTO slicesProcessingContextDTO) {
        AiStudyDurationProcessingContextDTO aiStudyDurationProcessingContextDTO = new AiStudyDurationProcessingContextDTO();
        aiStudyDurationProcessingContextDTO.setExistAiStudyRecord(slicesProcessingContextDTO.getExistAiStudyRecordMap().values().stream().toList());
        aiStudyDurationProcessingContextDTO.setStudentMap(slicesProcessingContextDTO.getStudentMap());
        aiStudyDurationProcessingContextDTO.setStudyModuleTypeEnum(slicesProcessingContextDTO.getStudyModuleTypeEnum());
        aiStudyDurationProcessingContextDTO.setCourseDurationMap(slicesProcessingContextDTO.getCourseDurationMap());
        return aiStudyDurationProcessingContextDTO;
    }

    private void batchSaveOrUpdateStudyPlan(QuestionVideoTimeProcessor questionVideoTimeProcessor, StudyRecordTimeProcessor studyProcessor) {
        List<QuestionVideoRecord> questionInsertRecords = questionVideoTimeProcessor.getInserts();
        List<QuestionVideoRecord> questionUpdateRecords = questionVideoTimeProcessor.getUpdates();
        List<StudyRecord> studyInsertRecords = studyProcessor.getInserts();
        List<StudyRecord> studyUpdateRecords = studyProcessor.getUpdates();
        if (CollectionUtil.isNotEmpty(questionInsertRecords)) {
            questionVideoRecordService.saveBatch(questionInsertRecords);
        }
        if (CollectionUtil.isNotEmpty(questionUpdateRecords)) {
            questionVideoRecordService.updateBatchById(questionUpdateRecords);
        }
        if (CollectionUtil.isNotEmpty(studyInsertRecords)) {
            studyRecordService.batchInsert(studyInsertRecords);
        }
        if (CollectionUtil.isNotEmpty(studyUpdateRecords)) {
            studyRecordService.updateBatchById(studyUpdateRecords);
        }
    }

    private StudyDurationProcessingContextDTO getStudyRecordProcessingContextData(StudySlicesProcessingContextDTO studyRecordContextData) {
        StudyDurationProcessingContextDTO processingContext = new StudyDurationProcessingContextDTO();
        processingContext.setExistStudyRecordMap(studyRecordContextData.getExistStudyRecordMap());
        processingContext.setStudentMap(studyRecordContextData.getStudentMap());
        processingContext.setCourseDurationMap(studyRecordContextData.getCourseDurationMap());
        processingContext.setModuleAndGroupEnum(studyRecordContextData.getStudyModuleTypeEnum());
        return processingContext;
    }

    public Map<Long, Student> studentMap(List<Long> studentIds) {
        return studyRecordTimeContentService.studentMap(studentIds);
    }

    public Map<Long, StudyRecord> studyRecordMap(List<Long> studyPlanningRecordIds) {
        return studyRecordTimeContentService.studyRecordMap(studyPlanningRecordIds);
    }

    public Map<Long, List<QuestionVideoRecord>> studentQuestionVideoRecordMap(List<BatchQueryQuestionRecordDTO> recordList) {
        return studyRecordTimeContentService.studentQuestionVideoRecordMap(recordList);
    }

    public Map<Long, Long> courseDurationMap(List<Long> videoIds) {
        return studyRecordTimeContentService.courseDurationMap(videoIds);
    }

}
