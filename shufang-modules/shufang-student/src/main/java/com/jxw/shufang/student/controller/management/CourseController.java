package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.core.ExcelResult;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.domain.vo.CourseImportVo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.SpecialTopicCourseTreeVo;
import com.jxw.shufang.student.domain.vo.SpecialTopicCourseV2Vo;
import com.jxw.shufang.student.enums.CourseProcessEnum;
import com.jxw.shufang.student.listener.CourseImportNewListener;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.system.api.RemoteAttributeService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程（课程包含章节）
 * 前端访问路由地址为:/course/course
 *
 * @date 2024-03-30
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/course")
public class CourseController extends BaseController {

    private final ICourseService courseService;

    @DubboReference
    private RemoteAttributeService remoteAttributeService;

    /**
     * 查询课程（课程包含章节）列表
     */
    @SaCheckPermission("course:course:list")
    @GetMapping("/list")
    public TableDataInfo<CourseVo> list(CourseBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
            pageQuery.setIsAsc("desc");
        }
        bo.setCourseType(UserConstants.TOP_COURSE_TYPE);
        return courseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出课程（课程包含章节）列表
     */
    @SaCheckPermission("course:course:export")
    @Log(title = "课程管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CourseBo bo, HttpServletResponse response) {

        courseService.export(bo, "课程（课程包含章节）", response);
        //ExcelUtil.exportExcel(list, "课程（课程包含章节）", CourseVo.class, response);
    }

    /**
     * 获取课程（课程包含章节）详细信息
     *
     * @param courseId 主键
     */
    @SaCheckPermission("course:course:query")
    @GetMapping("/{courseId}")
    public R<CourseVo> getInfo(@NotNull(message = "主键不能为空")
                               @PathVariable Long courseId) {
        return R.ok(courseService.queryById(courseId, true));
    }

    /**
     * 新增课程（课程包含章节）
     */
    @SaCheckPermission("course:course:add")
    @Log(title = "课程管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CourseBo bo) {
        return toAjax(courseService.insertByBo(bo));
    }


    @SaCheckPermission("course:catalog:add")
    @Log(title = "课程管理添加章节", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/catalog")
    public R<Boolean> addCatalog(@Validated(AddGroup.class) @RequestBody CourseBo bo) {
        return R.ok(courseService.addCatalog(bo));
    }

    @SaCheckPermission("course:catalog:edit")
    @Log(title = "课程管理修改章节", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/catalog")
    public R<Boolean> editCatalog(@Validated(EditGroup.class) @RequestBody CourseBo bo) {
        return R.ok(courseService.editCatalog(bo));
    }

    @SaCheckPermission("course:catalog:config")
    @Log(title = "课程管理关联课次", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/associate")
    public R<Boolean> associateCourse(@RequestBody CourseBo bo) {
        return R.ok(courseService.associateCourse(bo));
    }

    @SaCheckPermission("course:catalog:batch")
    @Log(title = "课程管理批量关联课次", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/associate/batch")
    public R<Boolean> batchAssociateCourse(@Validated @RequestBody AssociateCourseBo bo) {
        log.info("课程管理批量添加课次{}", bo);
        return R.ok(courseService.batchAssociateCourse(bo));
    }

    @SaCheckPermission("course:catalog:move")
    @Log(title = "课程管理移动课程", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/move")
    public R<Boolean> moveCourse(@Validated @RequestBody MoveCourseBo bo) {
        log.info("课程管理移动课次{}", bo);
        return R.ok(courseService.moveCourse(bo));
    }

    /**
     * 同步课程生成课次
     */
    @SaCheckPermission("course:course:add")
    @Log(title = "课程管理", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @PostMapping("/sync")
    public R<Void> sync(@RequestBody @Validated CourseSyncBo syncBo) {
        return toAjax(courseService.sync(syncBo));
    }

    /**
     * 新增课程（课程包含章节）
     */
    @SaCheckPermission("course:course:add")
    @RepeatSubmit()
    @PostMapping("/changeSort")
    public R<Void> changeSort(@RequestBody CourseBo bo) {
        return toAjax(courseService.changeSort(bo));
    }

    /**
     * 修改课程（课程包含章节）
     */
    @SaCheckPermission("course:course:edit")
    @Log(title = "课程管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CourseBo bo) {
        courseService.updateByBo(bo);
        return R.ok();
    }

    /**
     * 删除课程（课程包含章节）
     *
     * @param courseId 主键
     */
    @SaCheckPermission("course:course:remove")
    @Log(title = "课程管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseId}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long courseId) {
        return toAjax(courseService.deleteCourse(courseId));
    }

    @SaCheckPermission("course:catalog:clear")
    @Log(title = "课程管理清除知识点", businessType = BusinessType.DELETE)
    @GetMapping("/clear")
    public R<Boolean> batchClearCourse(@RequestParam List<Long> courseIds) {
        return R.ok(courseService.batchRemoveCourse(courseIds, CourseProcessEnum.CLEAR.getCode()));
    }

    @SaCheckPermission("course:catalog:remove")
    @Log(title = "课程管理删除章节", businessType = BusinessType.DELETE)
    @GetMapping("/remove")
    public R<Boolean> batchRemoveCourse(@RequestParam List<Long> courseIds) {
        return R.ok(courseService.batchRemoveCourse(courseIds, CourseProcessEnum.REMOVE.getCode()));
    }

    /**
     * 导入数据
     *
     * @param file 导入文件
     */
    @Log(title = "课程管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("course:course:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<CourseImportVo> result = ExcelUtil.importExcel(file.getInputStream(), CourseImportVo.class, new CourseImportNewListener(remoteAttributeService));
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {

        List<ExcelUtil.ExcelExportHeader> excelExportHeaders = new ArrayList<>();
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("学段", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("学科", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("课程专题", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("课程来源", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("季度类型", "", false));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("教材版本", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("年级", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("排序", "", false));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("课程名称", "", true));
        excelExportHeaders.add(new ExcelUtil.ExcelExportHeader("一级名称", "", true));

        List<String> matterList = new ArrayList<>();
        matterList.add("填写注意事项：");
        matterList.add("1.红色表头为必填项");
        matterList.add("2.年级到课程名称之间的表头，为属性表头");
        matterList.add("3.如果层级不够，可以在知识点ID表头前继续添加层级表头");
        ExcelUtil.exportDynamicTemplate(excelExportHeaders, matterList, "课程导入模板", response);
    }

    /**
     * 查询课程树结构列表
     */
    @SaCheckPermission("course:course:list")
    @GetMapping("/tree")
    public R<List<Tree<Long>>> treeQuery(CourseBo bo) {
        return R.ok(courseService.treeQuery(bo));
    }

    @SaCheckPermission("course:course:list")
    @GetMapping("/tree/v2")
    public R<List<Tree<Long>>> queryTreeV2(Long courseId) {
        return R.ok(courseService.queryTreeV2(courseId));
    }

    /**
     * 查询课程树结构列表,课程专题为主体
     */
    @SaCheckPermission("course:course:list")
    @GetMapping("/treeBySpecialTopic")
    public R<List<SpecialTopicCourseTreeVo>> treeBySpecialTopic(CourseBo bo) {
        return R.ok(courseService.treeBySpecialTopic(bo));
    }


    /**
     * 查询课程树结构列表,课程专题为主体
     *
     * @param bo             课程查询条件
     * @param queryTopicType 0 课程类型  1 寒暑假类型
     * @return
     */
    @SaCheckPermission("course:course:list")
    @GetMapping("/findSpecialTopicV2")
    public R<List<SpecialTopicCourseV2Vo>> findSpecialTopicV2(CourseBo bo, Integer queryTopicType) {
        if (ObjectUtils.isEmpty(queryTopicType)) {
            queryTopicType = 0;
        }
        return R.ok(courseService.findSpecialTopicV2(bo, queryTopicType));
    }

    @SaCheckPermission("course:course:list")
    @GetMapping("/queryList")
    public R<List<CourseVo>> queryList(CourseBo bo) {
        //只允许查询拥有父级ID的课程
        if (ObjectUtils.isEmpty(bo.getCourseParentId())) {
            return R.ok(List.of());
        }
        return R.ok(courseService.listCourseSelection(bo));
    }

    @SaCheckPermission("course:course:list")
    @GetMapping("/tree/batch")
    public R<List<Tree<Long>>> batchQueryTree(@RequestParam List<Long> courseIds) {
        return R.ok(courseService.batchQueryTree(courseIds));
    }

    /**
     * 查询课程树结构列表,课程学段为主体
     */
    @SaCheckPermission("course:course:list")
    @GetMapping("/treeByStage")
    public R<List<Tree<Long>>> treeByStage(CourseBo bo) {
        //不要孩子节点
        bo.setNeedChild(false);
        return R.ok(courseService.treeByStage(bo));
    }

    @SaCheckPermission("course:course:grade:synchronize")
    @GetMapping("/synchronize/courseGrade")
    public R<Void> synchronizedCourseGrade() {
        courseService.synchronizedData();
        return R.ok();
    }

    /**
     * 根据学段返回年级接口
     */
    @GetMapping("/treeByStageAndCourseTypeWithGrades")
    public R<List<GradeTreeVo>> treeByStageAndCourseTypeWithGrades(@RequestParam("stage") String stage, @RequestParam("courseType") Integer courseType) {
        List<GradeTreeVo> gradeList = courseService.buildCourseTreeByStageAndType(stage, courseType);
        return R.ok(gradeList);
    }

    @PostMapping("/search/save")
    public R<Boolean> saveCourseSearch(@Validated @RequestBody CourseSearchBo searchBo) {
        return R.ok(courseService.saveCourseSearch(searchBo.getSearchContent(), searchBo.getStudentId()));
    }

    @GetMapping("/search")
    public R<CourseSearchHistoryVo> getCourseSearch(@RequestParam Long studentId) {
        return R.ok(courseService.getCourseSearch(studentId));
    }

}
