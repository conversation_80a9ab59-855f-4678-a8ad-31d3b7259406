package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.ProductBo;
import com.jxw.shufang.student.domain.vo.ProductVo;
import com.jxw.shufang.student.service.IProductService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品（会员卡）
 * 前端访问路由地址为:/student/product
 *
 *
 * @date 2024-02-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/product")
public class ProductController extends BaseController {

    private final IProductService productService;

    /**
     * 查询产品（会员卡）列表
     */
    @SaCheckPermission("student:product:list")
    @GetMapping("/list")
    public TableDataInfo<ProductVo> list(ProductBo bo, PageQuery pageQuery) {
        return productService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品（会员卡）列表
     */
    @SaCheckPermission("student:product:export")
    @Log(title = "产品（会员卡）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductBo bo, HttpServletResponse response) {
        List<ProductVo> list = productService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品（会员卡）", ProductVo.class, response);
    }

    /**
     * 获取产品（会员卡）详细信息
     *
     * @param productId 主键
     */
    @SaCheckPermission("student:product:query")
    @GetMapping("/{productId}")
    public R<ProductVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long productId) {
        return R.ok(productService.queryById(productId));
    }

    /**
     * 新增产品（会员卡）
     */
    @SaCheckPermission("student:product:add")
    @Log(title = "产品（会员卡）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductBo bo) {
        return toAjax(productService.insertByBo(bo));
    }

    /**
     * 修改产品（会员卡）
     */
    @SaCheckPermission("student:product:edit")
    @Log(title = "产品（会员卡）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductBo bo) {
        return toAjax(productService.updateByBo(bo));
    }

    /**
     * 上下架产品（会员卡）
     */
    @SaCheckPermission("student:product:edit")
    @Log(title = "产品（会员卡）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody ProductBo bo) {
        return toAjax(productService.changeProductStatus(bo.getProductId(), bo.getProductStatus()));
    }

    /**
     * 删除产品（会员卡）
     *
     * @param productIds 主键串
     */
    @SaCheckPermission("student:product:remove")
    @Log(title = "产品（会员卡）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] productIds) {
        return toAjax(productService.deleteWithValidByIds(List.of(productIds), true));
    }

    /**
     * 查询选项列表
     *
     * @param bo bo
     */
    @SaCheckPermission("student:product:optionList")
    @GetMapping("/option")
    public R<List<ProductVo>> optionList(ProductBo bo) {
        return R.ok(productService.queryOptionList(bo));
    }

}
