package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AttendanceLogStaffEzkeco;
import com.jxw.shufang.student.domain.bo.AttendanceLogStaffEzkecoBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStaffEzkecoVo;
import com.jxw.shufang.student.mapper.AttendanceLogStaffEzkecoMapper;
import com.jxw.shufang.student.service.IAttendanceLogStaffEzkecoService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ezkeco员工考勤记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RequiredArgsConstructor
@Service
public class AttendanceLogStaffEzkecoServiceImpl implements IAttendanceLogStaffEzkecoService, BaseService {

    private final AttendanceLogStaffEzkecoMapper baseMapper;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询ezkeco员工考勤记录
     */
    @Override
    public AttendanceLogStaffEzkecoVo queryById(Long attendanceLogStaffEzkecoId){
        return baseMapper.selectVoById(attendanceLogStaffEzkecoId);
    }

    /**
     * 查询ezkeco员工考勤记录列表
     */
    @Override
    public TableDataInfo<AttendanceLogStaffEzkecoVo> queryPageList(AttendanceLogStaffEzkecoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AttendanceLogStaffEzkeco> lqw = buildLambdaQueryWrapper(bo);
        Page<AttendanceLogStaffEzkecoVo> result = baseMapper.queryPageList(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithStaffInfo())){
            putStaffInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询ezkeco员工考勤记录列表
     */
    @Override
    public List<AttendanceLogStaffEzkecoVo> queryList(AttendanceLogStaffEzkecoBo bo) {
        LambdaQueryWrapper<AttendanceLogStaffEzkeco> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private void putStaffInfo(List<AttendanceLogStaffEzkecoVo> records) {
        if (CollUtil.isEmpty(records)){
            return;
        }
        List<Long> staffUserIdList = records.stream().map(AttendanceLogStaffEzkecoVo::getUserId).collect(Collectors.toList());
        if (CollUtil.isEmpty(staffUserIdList)){
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setUserIdList(staffUserIdList);
        remoteStaffBo.setWithSysUserInfo(true);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)){
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getCreateBy, Function.identity()));
        records.forEach(e -> {
            if (e.getUserId() != null){
                e.setStaff(remoteStaffVoMap.get(e.getUserId()));
            }
        });



    }


    private LambdaQueryWrapper<AttendanceLogStaffEzkeco> buildLambdaQueryWrapper(AttendanceLogStaffEzkecoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AttendanceLogStaffEzkeco> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getVerify()), AttendanceLogStaffEzkeco::getVerify, bo.getVerify());
        lqw.eq(bo.getChecktime() != null, AttendanceLogStaffEzkeco::getChecktime, bo.getChecktime());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), AttendanceLogStaffEzkeco::getSn, bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getAlias()), AttendanceLogStaffEzkeco::getAlias, bo.getAlias());
        lqw.eq(StringUtils.isNotBlank(bo.getPin()), AttendanceLogStaffEzkeco::getPin, bo.getPin());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), AttendanceLogStaffEzkeco::getState, bo.getState());
        lqw.eq(bo.getAttendanceUserId() != null, AttendanceLogStaffEzkeco::getAttendanceUserId, bo.getAttendanceUserId());
        lqw.eq(bo.getUserId() != null, AttendanceLogStaffEzkeco::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), AttendanceLogStaffEzkeco::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotograph()), AttendanceLogStaffEzkeco::getPhotograph, bo.getPhotograph());
        if (StringUtils.isNotBlank(bo.getRangeStartDate()) && StringUtils.isNotBlank(bo.getRangeEndDate())){
            if (bo.getRangeStartDate().contains(":")){
                lqw.between(AttendanceLogStaffEzkeco::getChecktime, DateUtils.parseDate(bo.getRangeStartDate()), DateUtils.parseDate(bo.getRangeEndDate()));
            }else {
                lqw.between(AttendanceLogStaffEzkeco::getChecktime, DateUtils.parseDate(bo.getRangeStartDate() + " 00:00:00"), DateUtils.parseDate(bo.getRangeEndDate() + " 23:59:59"));
            }
        }

        if (StringUtils.isNotBlank(bo.getBranchStaffName())) {
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setUserName(bo.getBranchStaffName());
            List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
            if (CollUtil.isEmpty(remoteUserVos)){
                lqw.eq(AttendanceLogStaffEzkeco::getUserId,-1L);
            }else {
                List<Long> userIds = remoteUserVos.stream().map(RemoteUserVo::getUserId).collect(Collectors.toList());
                lqw.in(AttendanceLogStaffEzkeco::getUserId,userIds);
            }
        }
        return lqw;
    }



    private QueryWrapper<AttendanceLogStaffEzkeco> buildQueryWrapper(AttendanceLogStaffEzkecoBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<AttendanceLogStaffEzkeco> lqw = Wrappers.query();
        lqw.eq(StringUtils.isNotBlank(bo.getVerify()), "t.verify", bo.getVerify());
        lqw.eq(bo.getChecktime() != null, "t.checktime", bo.getChecktime());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), "t.sn", bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getAlias()), "t.alias", bo.getAlias());
        lqw.eq(StringUtils.isNotBlank(bo.getPin()), "t.pin", bo.getPin());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), "t.state", bo.getState());
        lqw.eq(bo.getAttendanceUserId() != null, "t.attendance_user_id", bo.getAttendanceUserId());
        lqw.eq(bo.getUserId() != null, "t.user_id", bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), "t.nick_name", bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotograph()), "t.photograph", bo.getPhotograph());
        if (StringUtils.isNotBlank(bo.getRangeStartDate()) && StringUtils.isNotBlank(bo.getRangeEndDate())){
            if (bo.getRangeStartDate().contains(":")){
                lqw.between("t.checktime", DateUtils.parseDate(bo.getRangeStartDate()), DateUtils.parseDate(bo.getRangeEndDate()));
            }else {
                lqw.between("t.checktime", DateUtils.parseDate(bo.getRangeStartDate() + " 00:00:00"), DateUtils.parseDate(bo.getRangeEndDate() + " 23:59:59"));
            }
        }
        return lqw;
    }


    /**
     * 新增ezkeco员工考勤记录
     */
    @Override
    public Boolean insertByBo(AttendanceLogStaffEzkecoBo bo) {
        AttendanceLogStaffEzkeco add = MapstructUtils.convert(bo, AttendanceLogStaffEzkeco.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAttendanceLogStaffEzkecoId(add.getAttendanceLogStaffEzkecoId());
        }
        return flag;
    }

    /**
     * 修改ezkeco员工考勤记录
     */
    @Override
    public Boolean updateByBo(AttendanceLogStaffEzkecoBo bo) {
        AttendanceLogStaffEzkeco update = MapstructUtils.convert(bo, AttendanceLogStaffEzkeco.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AttendanceLogStaffEzkeco entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ezkeco员工考勤记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量插入
     *
     * @param list
     */
    @Override
    public void insertBatch(Collection<AttendanceLogStaffEzkecoBo> list) {
        List<AttendanceLogStaffEzkeco> staffEzkecoList = MapstructUtils.convert(ListUtil.toList(list), AttendanceLogStaffEzkeco.class);
        baseMapper.insertBatch(staffEzkecoList);
    }

    @Override
    public Boolean exist(AttendanceLogStaffEzkecoBo attendanceLogStaffEzkecoBo) {
        LambdaQueryWrapper<AttendanceLogStaffEzkeco> queryWrapper = buildLambdaQueryWrapper(attendanceLogStaffEzkecoBo);
        return baseMapper.exists(queryWrapper);
    }




    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
