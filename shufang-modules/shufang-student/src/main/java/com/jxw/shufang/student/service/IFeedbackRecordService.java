package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.FeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.FeedbackRecordVo;
import com.jxw.shufang.student.domain.vo.StuDailyShareStatisticVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;

/**
 * 反馈记录Service接口
 * @date 2024-05-24
 */
public interface IFeedbackRecordService {

    /**
     * 查询反馈记录
     */
    FeedbackRecordVo queryById(Long feedbackRecordId,Boolean withStudentInfo,Boolean withImgUrl);

    StuDailyShareStatisticVo queryStuDailyShareStatistic(FeedbackRecordVo feedbackRecordVo);

    StuDailyShareStatisticVo getResult(List<StudyPlanningRecordVo> thisTimePlanningList);

    List<StudyPlanningRecordVo> getThisTimeStudyPlanningVos(FeedbackRecordVo feedbackRecordVo);

    /**
     * 查询反馈记录列表
     */
    TableDataInfo<FeedbackRecordVo> queryPageList(FeedbackRecordBo bo, PageQuery pageQuery);

    /**
     * 查询反馈记录列表
     */
    List<FeedbackRecordVo> queryList(FeedbackRecordBo bo);

    /**
     * 新增反馈记录
     */
    Boolean insertByBo(FeedbackRecordBo bo);

    /**
     * 修改反馈记录
     */
    Boolean updateByBo(FeedbackRecordBo bo);

    //发送家长通知
    void notifyParent(Long feedbackRecordId);

    /**
     * 校验并批量删除反馈记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void read(Long feedbackRecordId);

    /**
     * 获取feedbackRecordId上一次的反馈记录
     * @param feedbackRecordId 反馈记录id
     * @param studentId  学生id
     * @return
     */
    FeedbackRecordVo queryLatestFeedbackRecord(Long feedbackRecordId, Long studentId);

    /**
     * 修改反馈状态
     * @param feedbackRecordId
     */
    void changeStatus(Long feedbackRecordId);
}
