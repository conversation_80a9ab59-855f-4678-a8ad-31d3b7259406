package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.system.api.RemoteDeptConfigService;
import com.jxw.shufang.system.api.domain.vo.RemoteSysDeptConfigVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/deptConfig")
public class AStudentDeptConfigController {

    @DubboReference
    private final RemoteDeptConfigService remoteDeptConfigService;

    /**
     * 获取部门配置
     */
    @GetMapping("/get")
    public R<RemoteSysDeptConfigVo> selectDeptById(@RequestParam  Long deptId) {
        RemoteSysDeptConfigVo remoteSysDeptConfigVo = remoteDeptConfigService.selectDeptById(deptId);
        return R.ok(remoteSysDeptConfigVo);
    }
}
