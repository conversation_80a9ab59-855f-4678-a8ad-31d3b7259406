package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.dto.CourseWithTopCourseDTO;
import com.jxw.shufang.student.enums.CourseLabelDictEnum;
import com.jxw.shufang.student.service.ICourseDetailService;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.system.api.RemoteAttributeService;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.api.enums.AttrGroupTypeEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/17 11:04
 * @Version 1
 * @Description
 */
@Service
public class CourseDetailServiceImpl implements ICourseDetailService, BaseService {
    @Resource
    private ICourseService courseService;
    @Resource
    private DictService dictService;
    @DubboReference
    private RemoteAttributeService remoteAttributeService;

    @Override
    public List<CourseWithTopCourseDTO> queryCourseWithTopInfo(Set<Long> courseIdList) {
        List<Course> courseList = courseService.getCoursesByCourseId(new ArrayList<>(courseIdList));
        if (CollectionUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }

        List<String> existTopCourseIds = courseList.stream()
            .filter(Objects::nonNull)
            .map(CourseDetailServiceImpl::getCourseTopIds)
            .toList();
        List<Course> topCourses = this.getTopCourse(existTopCourseIds);
        if (CollectionUtil.isEmpty(topCourses)) {
            return null;
        }
        Map<Long, Course> topCoursesMap = topCourses.stream()
            .collect(Collectors.toMap(Course::getCourseId, Function.identity(), (v1, v2) -> v2));

        // 属性关系
        List<Long> topCourseIds = new ArrayList<>(topCoursesMap.keySet());
        List<CourseAttributeDetailDTO> attributeDetails = remoteAttributeService.batchQueryAttributeByType(topCourseIds, AttrGroupTypeEnum.COURSE.getType());
        Map<Long, List<CourseAttributeDetailDTO>> courseAttributeTypeIdMap = attributeDetails.stream()
            .collect(Collectors.groupingBy(CourseAttributeDetailDTO::getTypeId));

        // build
        return courseList.stream()
            .map(course -> this.buildCourseWithTopCourseDTO(topCoursesMap, courseAttributeTypeIdMap, course))
            .toList();
    }

    /**
     * 将课程的顶级课程ID取出
     * @param course
     * @return
     */
    private static String getCourseTopIds(Course course) {
        if (StringUtils.isNotBlank(course.getAncestors())) {
            String[] splitCourseId = course.getAncestors().split(StringUtils.SEPARATOR);
            if (splitCourseId.length > 1) {
                return splitCourseId[1];
            }
        }
        return course.getCourseParentId().toString();
    }

    private CourseWithTopCourseDTO buildCourseWithTopCourseDTO(Map<Long, Course> topCourseMap,
                                                               Map<Long, List<CourseAttributeDetailDTO>> courseAttributeTypeIdMap,
                                                               Course course) {
        CourseWithTopCourseDTO courseWithTopCourseDTO = new CourseWithTopCourseDTO();
        courseWithTopCourseDTO.setCourseId(course.getCourseId());
        courseWithTopCourseDTO.setCourseName(course.getCourseName());
        courseWithTopCourseDTO.setKnowledgeId(course.getKnowledgeId());

        Long topCourseId = this.getTopCourseId(course);
        Course topCourse = topCourseMap.get(topCourseId);
        if (Objects.isNull(topCourse)) {
            return courseWithTopCourseDTO;
        }

        String courseStage = getDictLabel(topCourse.getStage(), CourseLabelDictEnum.COURSE_STAGE);
        String courseGrade = getDictLabel(topCourse.getGrade(), CourseLabelDictEnum.COURSE_GRADE);
        String courseAffiliationSubject = getDictLabel(topCourse.getAffiliationSubject(), CourseLabelDictEnum.COURSE_AFFILIATION_SUBJECT);
        String courseSpecialTopic = getDictLabel(topCourse.getSpecialTopic(), CourseLabelDictEnum.COURSE_SPECIAL_TOPIC);
        String courseQuarterType = getDictLabel(topCourse.getQuarterType(), CourseLabelDictEnum.COURSE_QUARTER_TYPE);
        List<CourseAttributeDetailDTO> detailDTO = courseAttributeTypeIdMap.getOrDefault(topCourse.getCourseId(), Collections.emptyList());

        courseWithTopCourseDTO.setTopCourseId(topCourseId);
        courseWithTopCourseDTO.setTopCourseName(topCourse.getCourseName());
        courseWithTopCourseDTO.setCourseStage(courseStage);
        courseWithTopCourseDTO.setCourseGrade(courseGrade);
        courseWithTopCourseDTO.setSubject(courseAffiliationSubject);
        courseWithTopCourseDTO.setCourseSpecialTopic(courseSpecialTopic);
        courseWithTopCourseDTO.setCourseQuarterType(courseQuarterType);
        courseWithTopCourseDTO.setValue(detailDTO.stream().map(CourseAttributeDetailDTO::getValue).collect(Collectors.toList()));
        return courseWithTopCourseDTO;
    }

    private Long getTopCourseId(Course course) {
        Long topCourseId = null;
        if (null == course.getAncestors()) {
            topCourseId = course.getCourseParentId();
        } else {
            String[] splitCourseId = course.getAncestors().split(StringUtils.SEPARATOR);
            if (splitCourseId.length > 1) {
                topCourseId = Long.parseLong(splitCourseId[1]);
            } else {
                topCourseId = Long.parseLong(course.getAncestors());
            }
        }
        return topCourseId;
    }

    private String getDictLabel(String code, CourseLabelDictEnum dictEnum) {
        if (StringUtils.isNotBlank(code)) {
            return dictService.getDictLabel(dictEnum.getCode(), code);
        }
        return "";
    }

    /**
     * 获取顶层课程信息
     *
     * @param courseList
     * @return
     */
    private List<Course> getTopCourse(List<String> courseList) {
        if (CollectionUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }
        List<Long> existTopCourseIds = courseList.stream().map(Long::parseLong).toList();
        return courseService.getCoursesByCourseId(existTopCourseIds);
    }
}
