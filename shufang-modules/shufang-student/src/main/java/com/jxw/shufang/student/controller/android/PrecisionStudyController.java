package com.jxw.shufang.student.controller.android;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxw.shufang.common.core.domain.R;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/dify/precisionStudy")
@RequiredArgsConstructor
@Slf4j
public class PrecisionStudyController {

    @Value("${dify.aiPrecisionStudyQuestion:app-6lJF2gYo3uyw1AEk4x2rgUEk}")
    private String aiPrecisionStudyQuestion;

    @Value("${dify.url:http://jxwdify.xuexizhiwang.com/v1/workflows/run}")
    private String url;
    private static final OkHttpClient client = new OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS) // 设置连接超时时间
        .writeTimeout(60, TimeUnit.SECONDS)   // 设置写入超时时间
        .readTimeout(60, TimeUnit.SECONDS)    // 设置读取超时时间
        .build();

    @PostMapping("/question")
    public R<WorkflowRunResult> chatMessages(@org.springframework.web.bind.annotation.RequestBody LearningReport.Inputs inputs) {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        LearningReport learningReport = new LearningReport();
        learningReport.setResponse_mode("blocking");
        learningReport.setUser("同学");
        learningReport.setInputs(inputs);

        ObjectMapper objectMapper = new ObjectMapper();
        Request authorization;
        try {
            authorization = new Request
                .Builder()
                .addHeader("Authorization", "Bearer " + aiPrecisionStudyQuestion)
                .url(url)
                .post(RequestBody.create(objectMapper.writeValueAsString(learningReport), JSON))
                .build();
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        try (Response execute = client.newCall(authorization).execute()) {
            ResponseBody body = execute.body();
            if (ObjectUtils.isEmpty(body)) {
                return R.fail();
            }
            // 现在你可以使用result对象了
            WorkflowRunResult result = objectMapper.readValue(body.bytes(), WorkflowRunResult.class);
            return R.ok(result);
        } catch (Exception e) {
            log.error("ai服务调用错误", e);
            return R.fail(e.getMessage());
        }
    }

    @Data
    public static class LearningReport {
        public Inputs inputs;
        public String response_mode;
        public String user;

        // 嵌套类用于表示inputs对象
        @Data
        public static class Inputs {
            public String subject;
            public String question_sum;
            public String question_correct;
            public String question_error;
            public String accuracy;
            public String unmastered_knowledge;
            public String reinforced_knowledge;
        }

    }

    @Data
    public static class WorkflowRunResult {

        @JsonProperty("task_id")
        private String taskId;

        @JsonProperty("workflow_run_id")
        private String workflowRunId;

        @JsonProperty("data")
        private WorkflowRunData data;

        // 省略getter和setter方法
        @Data
        public static class WorkflowRunData {

            @JsonProperty("id")
            private String id;

            @JsonProperty("workflow_id")
            private String workflowId;

            @JsonProperty("status")
            private String status;

            @JsonProperty("outputs")
            private WorkflowOutputs outputs;

            @JsonProperty("error")
            private Object error; // 可以根据实际需要更改为更具体的类型

            @JsonProperty("elapsed_time")
            private double elapsedTime;

            @JsonProperty("total_tokens")
            private int totalTokens;

            @JsonProperty("total_steps")
            private int totalSteps;

            @JsonProperty("created_at")
            private long createdAt; // 假设时间戳是long类型

            @JsonProperty("finished_at")
            private long finishedAt; // 假设时间戳是long类型

            @Data
            public static class WorkflowOutputs {

                @JsonProperty("text")
                private String text;

            }

        }

    }
}

