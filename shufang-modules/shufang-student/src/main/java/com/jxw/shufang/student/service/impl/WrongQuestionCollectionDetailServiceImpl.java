package com.jxw.shufang.student.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.WrongQuestionCollectionDetail;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionDetailBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionDetailVo;
import com.jxw.shufang.student.mapper.WrongQuestionCollectionDetailMapper;
import com.jxw.shufang.student.service.IWrongQuestionCollectionDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 错题合集详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionCollectionDetailServiceImpl implements IWrongQuestionCollectionDetailService, BaseService {

    private final WrongQuestionCollectionDetailMapper baseMapper;

    /**
     * 查询错题合集详情
     */
    @Override
    public WrongQuestionCollectionDetailVo queryById(Long wrongQuestionCollectionDetailId) {
        return baseMapper.selectVoById(wrongQuestionCollectionDetailId);
    }

    /**
     * 查询错题合集详情列表
     */
    @Override
    public TableDataInfo<WrongQuestionCollectionDetailVo> queryPageList(WrongQuestionCollectionDetailBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WrongQuestionCollectionDetail> lqw = buildQueryWrapper(bo);
        Page<WrongQuestionCollectionDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询错题合集详情列表
     */
    @Override
    public List<WrongQuestionCollectionDetailVo> queryList(WrongQuestionCollectionDetailBo bo) {
        LambdaQueryWrapper<WrongQuestionCollectionDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WrongQuestionCollectionDetail> buildQueryWrapper(WrongQuestionCollectionDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WrongQuestionCollectionDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getWrongQuestionCollectionId() != null, WrongQuestionCollectionDetail::getWrongQuestionCollectionId, bo.getWrongQuestionCollectionId());
        lqw.eq(bo.getQuestionId() != null, WrongQuestionCollectionDetail::getQuestionId, bo.getQuestionId());
        lqw.like(StringUtils.isNotBlank(bo.getQuestionNo()), WrongQuestionCollectionDetail::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), WrongQuestionCollectionDetail::getAnswerResult, bo.getAnswerResult());
        lqw.in(CollectionUtils.isNotEmpty(bo.getCollectionList()), WrongQuestionCollectionDetail::getWrongQuestionCollectionId, bo.getCollectionList());
        return lqw;
    }

    /**
     * 新增错题合集详情
     */
    @Override
    public Boolean saveCollectionDetail(WrongQuestionCollectionDetailBo bo) {
        WrongQuestionCollectionDetail add = BeanUtil.toBean(bo, WrongQuestionCollectionDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWrongQuestionCollectionDetailId(add.getWrongQuestionCollectionDetailId());
        }
        return flag;
    }

    @Transactional
    @Override
    public Boolean batchSaveCollectionDetail(List<WrongQuestionCollectionDetail> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return false;
        }
        return baseMapper.insertBatch(detailList) > 0;
    }

    /**
     * 修改错题合集详情
     */
    @Override
    public Boolean updateByBo(WrongQuestionCollectionDetailBo bo) {
        WrongQuestionCollectionDetail update = BeanUtil.toBean(bo, WrongQuestionCollectionDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean batchUpdate(List<WrongQuestionCollectionDetail> detailList) {
        return baseMapper.updateBatchById(detailList);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WrongQuestionCollectionDetail entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除错题合集详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据错题合集ID查询详情列表
     */
    @Override
    public List<WrongQuestionCollectionDetailVo> queryByCollectionId(Long wrongQuestionCollectionId) {
        LambdaQueryWrapper<WrongQuestionCollectionDetail> lqw = Wrappers.lambdaQuery();
        lqw.eq(WrongQuestionCollectionDetail::getWrongQuestionCollectionId, wrongQuestionCollectionId);
        lqw.orderByAsc(WrongQuestionCollectionDetail::getSort);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 批量新增错题合集详情
     */
    @Override
    public Boolean insertBatchByBo(List<WrongQuestionCollectionDetailBo> boList) {
        List<WrongQuestionCollectionDetail> addList = MapstructUtils.convert(boList, WrongQuestionCollectionDetail.class);
        return baseMapper.insertBatch(addList) > 0;
    }

    /**
     * 根据错题合集ID删除详情
     */
    @Override
    public Boolean deleteByCollectionId(Long wrongQuestionCollectionId) {
        return baseMapper.deleteByCollectionId(wrongQuestionCollectionId) > 0;
    }

    /**
     * 根据题目ID查询相关的错题合集详情
     */
    @Override
    public List<WrongQuestionCollectionDetailVo> queryByQuestionId(Long questionId) {
        return baseMapper.selectByQuestionId(questionId);
    }

    /**
     * 统计错题合集详情数量
     */
    @Override
    public int countByCollectionId(Long wrongQuestionCollectionId) {
        return baseMapper.countByCollectionId(wrongQuestionCollectionId);
    }

    /**
     * 统计指定作答结果的详情数量
     */
    @Override
    public int countByCollectionIdAndAnswerResult(Long wrongQuestionCollectionId, String answerResult) {
        return baseMapper.countByCollectionIdAndAnswerResult(wrongQuestionCollectionId, answerResult);
    }

    /**
     * 查询错题合集详情统计信息
     */
    @Override
    public List<java.util.Map<String, Object>> getStatisticsByCollectionId(Long wrongQuestionCollectionId) {
        return baseMapper.selectStatisticsByCollectionId(wrongQuestionCollectionId);
    }

}
