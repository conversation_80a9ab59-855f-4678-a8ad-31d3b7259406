package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AiApplyCorrectionRecord;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;

/**
 * ai申请批改记录Mapper接口
 *
 *
 * @date 2024-05-23
 */
public interface AiApplyCorrectionRecordMapper extends BaseMapperPlus<AiApplyCorrectionRecord, AiApplyCorrectionRecordVo> {

    Page<AiApplyCorrectionRecordVo> selectRecordVoPage(@Param("page") Page<AiApplyCorrectionRecord> build,@Param(Constants.WRAPPER) QueryWrapper<AiApplyCorrectionRecord> lqw);
}
