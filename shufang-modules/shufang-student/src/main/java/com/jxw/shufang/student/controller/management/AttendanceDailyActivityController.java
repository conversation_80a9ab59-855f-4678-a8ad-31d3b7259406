package com.jxw.shufang.student.controller.management;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityBo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;
import com.jxw.shufang.student.service.AttendanceDailyActivityService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员每日打卡登录情况
 *
 * <AUTHOR>
 * @since 2025-06-04 11:57:25
 */
@Validated
@RestController
@RequestMapping("/management/attendance-daily-activity")
@RequiredArgsConstructor
public class AttendanceDailyActivityController {

    private final AttendanceDailyActivityService attendanceDailyActivityService;

    /**
     * 保存会员每日打卡登录情况
     */
    @PostMapping("/save")
    public R<Void> saveAttendanceDailyActivity(@Validated(AddGroup.class) @RequestBody AttendanceDailyActivityBo bo) {
        attendanceDailyActivityService.saveAttendanceDailyActivity(bo);
        return R.ok();
    }

    /**
     * 简单批量保存会员每日打卡登录情况
     */
    @PostMapping("/saveBatchAttendanceDailyActivity")
    public R<Void> saveBatchAttendanceDailyActivity(@Validated(AddGroup.class) @RequestBody List<AttendanceDailyActivityBo> boList) {
        attendanceDailyActivityService.saveBatchAttendanceDailyActivity(boList);
        return R.ok();
    }


    /**
     * 查询出待反馈的学生列表
     */
    @GetMapping("/queryNotFeedbackStudentList")
    public R<TableDataInfo<AttendanceDailyActivityVo>> queryNotFeedbackStudentList(AttendanceDailyActivityBo bo, PageQuery pageQuery) {
        TableDataInfo<AttendanceDailyActivityVo> page = attendanceDailyActivityService.queryNotFeedbackStudentList(bo, pageQuery);
        return R.ok(page);
    }

    /**
     * 导出待反馈会员列表
     */
    @PostMapping("/export")
    public void exportNotFeedbackStudentList(AttendanceDailyActivityBo bo, HttpServletResponse response) {
        TableDataInfo<AttendanceDailyActivityVo> page = attendanceDailyActivityService.queryNotFeedbackStudentList(bo, new PageQuery());
        List<AttendanceDailyActivityVo> list = page.getRows();
        list.forEach(vo -> {
            if (vo.getConsultantInfo() != null && vo.getConsultantInfo().getUser() != null) {
                vo.setConsultantName(vo.getConsultantInfo().getUser().getNickName());
            }
        });
        ExcelUtil.exportExcel(list, "待反馈会员", AttendanceDailyActivityVo.class, response);
    }

}
