package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.common.core.constant.FeedbackStatusConstant;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.FeedbackRecord;
import com.jxw.shufang.student.domain.FeedbackRecordExclusion;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.FeedbackRecordExclusionMapper;
import com.jxw.shufang.student.mapper.FeedbackRecordMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 反馈记录Service业务层处理
 *
 * @date 2024-05-24
 */
@RequiredArgsConstructor
@Service
public class FeedbackRecordServiceImpl implements IFeedbackRecordService, BaseService {

    private final FeedbackRecordMapper baseMapper;

    private final FeedbackRecordExclusionMapper feedbackRecordExclusionMapper;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentService studentService;

    private final IStudentParentRecordService studentParentRecordService;

    private final IStudyPlanningRecordService iStudyPlanningRecordService;

    private final AttendanceDailyActivityFeedbackRecordService attendanceDailyActivityFeedbackRecordService;

    private final AttendanceDailyActivityService attendanceDailyActivityService;

    @DubboReference
    private RemoteFileService ossService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteWxService remoteWxService;

    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 查询反馈记录
     */
    @Override
    public FeedbackRecordVo queryById(Long feedbackRecordId, Boolean withStudentInfo, Boolean withImgUrl) {
        FeedbackRecordVo feedbackRecordVo = baseMapper.selectVoById(feedbackRecordId);
        if (feedbackRecordVo == null) {
            return null;
        }

        if (Boolean.TRUE.equals(withStudentInfo)) {
            putStudentInfo(List.of(feedbackRecordVo));
        }
        if (Boolean.TRUE.equals(withImgUrl)) {
            putImgUrl(List.of(feedbackRecordVo));
        }

        // 设置当前反馈记录对应的考勤登录记录信息
        putAttendanceDailyActivityVo(feedbackRecordVo);

        afterSetFeedbackUnShowStudyRecordId(feedbackRecordVo);

        return feedbackRecordVo;
    }

    @Override
    public StuDailyShareStatisticVo queryStuDailyShareStatistic(FeedbackRecordVo feedbackRecordVo) {
        if  (!ObjectUtils.isEmpty(feedbackRecordVo)) {
            Date feedbackStartDate = feedbackRecordVo.getFeedbackStartDate();
            Date feedbackEndDate = feedbackRecordVo.getFeedbackEndDate();

            StudyPlanningRecordBo studyPlanningBo = new StudyPlanningRecordBo();
            studyPlanningBo.setStudentId(feedbackRecordVo.getStudentId());
            studyPlanningBo.setDateYearMonthLimitStart(DateUtils.dateTime(feedbackStartDate));
            studyPlanningBo.setDateYearMonthLimitEnd(DateUtils.dateTime(feedbackEndDate));
            studyPlanningBo.setWithStudyRecord(Boolean.TRUE);
            studyPlanningBo.setNotInStudyPlanningRecordIdList(feedbackRecordVo.getUnShowStudyRecordIdList());

            List<StudyPlanningRecordVo> thisTimeStudyPlanningVos = getThisTimeStudyPlanningWithBo(studyPlanningBo);
           return this.getResult(thisTimeStudyPlanningVos);
        }
        return null;
    }

    @Override
    public StuDailyShareStatisticVo getResult(List<StudyPlanningRecordVo> thisTimePlanningList) {
        StuDailyShareStatisticVo stuDailyShareStatisticVo = new StuDailyShareStatisticVo();
        long doStudyTime = 0L;
        long doPracticeTime = 0L;
        long doTestTime = 0L;
        long doTestAccuracy = 0L;

        List<Double> doTestAccuracyList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(thisTimePlanningList)){
            return stuDailyShareStatisticVo;
        }
        for (StudyPlanningRecordVo studyPlanningRecordVo : thisTimePlanningList) {
            StudyRecordVo studyRecord = studyPlanningRecordVo.getStudyRecord();
            if(ObjectUtils.isEmpty(studyRecord)){
                continue;
            }
            Long studyVideoTotalDuration = studyRecord.getStudyVideoTotalDuration();

            if (!ObjectUtils.isEmpty(studyVideoTotalDuration)&&studyVideoTotalDuration>60){
                doStudyTime++;
            }
            if (StringUtils.isNotBlank(studyRecord.getTestState())) {
                if (UserConstants.CORRECTION_STATUS_DONE.equals(studyRecord.getTestState())) {
                    doTestTime++;
                    double totalNum = Double.valueOf(studyRecord.getTestRightNum()) + studyRecord.getTestWrongNum();
                    Double accuracy = Double.valueOf(studyRecord.getTestRightNum()) / totalNum;
                    doTestAccuracyList.add(accuracy);
                }
            }
            if (StringUtils.isNotBlank(studyRecord.getPracticeState())) {
                if (UserConstants.CORRECTION_STATUS_DONE.equals(studyRecord.getPracticeState())) {
                    doPracticeTime++;
                }
            }
        }
        if (!CollectionUtils.isEmpty(doTestAccuracyList)){

            // 计算总和
            double sum = 0.0;
            for (Double value : doTestAccuracyList) {
                sum += value;
            }

            // 计算平均值
            double average = sum / doTestAccuracyList.size();

            // 将平均值转换为百分比
            double averagePercentage = average * 100;

            // 四舍五入到小数点后两位
            doTestAccuracy = Math.round(averagePercentage);


        }

        stuDailyShareStatisticVo.setDoStudyTime(doStudyTime);
        stuDailyShareStatisticVo.setDoPracticeTime(doPracticeTime);
        stuDailyShareStatisticVo.setDoTestTime(doTestTime);
        stuDailyShareStatisticVo.setDoTestAccuracy(doTestAccuracy);
        return stuDailyShareStatisticVo;
    }

    private List<StudyPlanningRecordVo> getThisTimeStudyPlanningWithBo(StudyPlanningRecordBo studyPlanningBo) {
        List<StudyPlanningRecordVo> studyPlanningList = DataPermissionHelper.ignore(() -> iStudyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningBo));
        if (CollectionUtils.isEmpty(studyPlanningList)) {
            return Collections.emptyList();
        }

        return studyPlanningList.stream().sorted(Comparator.comparing(StudyPlanningRecordVo::getStudyPlanningDate).thenComparing(StudyPlanningRecordVo::getStudyStartTime).reversed()).toList();
    }

    @Override
    public List<StudyPlanningRecordVo> getThisTimeStudyPlanningVos(FeedbackRecordVo feedbackRecordVo) {
        Date feedbackStartDate = feedbackRecordVo.getFeedbackStartDate();
        Date feedbackEndDate = feedbackRecordVo.getFeedbackEndDate();

        StudyPlanningRecordBo studyPlanningBo = new StudyPlanningRecordBo();
        studyPlanningBo.setStudentId(feedbackRecordVo.getStudentId());
        studyPlanningBo.setDateYearMonthLimitStart(DateUtils.dateTime(feedbackStartDate));
        studyPlanningBo.setDateYearMonthLimitEnd(DateUtils.dateTime(feedbackEndDate));
        studyPlanningBo.setWithCourseDetail(Boolean.TRUE);
        studyPlanningBo.setWithPracticeCorrectionRecord(Boolean.TRUE);
        studyPlanningBo.setWithTestCorrectionRecord(Boolean.TRUE);
        studyPlanningBo.setWithStudyRecord(Boolean.TRUE);
        studyPlanningBo.setNotInStudyPlanningRecordIdList(feedbackRecordVo.getUnShowStudyRecordIdList());
        List<StudyPlanningRecordVo> studyPlanningList = DataPermissionHelper.ignore(() -> iStudyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningBo));
        if (CollectionUtils.isEmpty(studyPlanningList)) {
            return Collections.emptyList();
        }
        for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningList) {
            setScreenshotsUrl(studyPlanningRecordVo.getTestCorrectionRecord());
            setScreenshotsUrl(studyPlanningRecordVo.getPracticeCorrectionRecord());
        }
        return studyPlanningList.stream().sorted(Comparator.comparing(StudyPlanningRecordVo::getStudyPlanningDate).thenComparing(StudyPlanningRecordVo::getStudyStartTime).reversed()).toList();
    }




    private void setScreenshotsUrl(CorrectionRecordVo recordVo) {
        if (ObjectUtils.isEmpty(recordVo)) {
            return;
        }
        String correctionScreenshots = recordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)) {
            String urls = ossService.selectUrlByIds(correctionScreenshots);
            if (StringUtils.isNotBlank(urls)) {
                recordVo.setCorrectionScreenshotsUrl(List.of(urls.split(",")));
            }
        }
    }

    private void afterSetFeedbackUnShowStudyRecordId(FeedbackRecordVo feedbackRecordVo) {
        if (ObjectUtils.isEmpty(feedbackRecordVo) || ObjectUtils.isEmpty(feedbackRecordVo.getFeedbackRecordId())) {
            return;
        }

        List<FeedbackRecordExclusion> feedbackRecordExclusions =
            feedbackRecordExclusionMapper.selectList(Wrappers.<FeedbackRecordExclusion>lambdaQuery()
                .eq(FeedbackRecordExclusion::getFeedbackRecordId, feedbackRecordVo.getFeedbackRecordId()));
        if (CollUtil.isEmpty(feedbackRecordExclusions)) {
            feedbackRecordVo.setUnShowStudyRecordIdList(new ArrayList<>());
        } else {
            feedbackRecordVo.setUnShowStudyRecordIdList(feedbackRecordExclusions
                .stream()
                .map(FeedbackRecordExclusion::getStudyPlanningRecordId)
                .toList());
        }
    }

    private void putImgUrl(List<FeedbackRecordVo> feedbackRecordVo) {
        if (CollUtil.isEmpty(feedbackRecordVo)) {
            return;
        }
        List<Long> ossIdList = new ArrayList<>();
        for (FeedbackRecordVo recordVo : feedbackRecordVo) {
            String feedbackImgs = recordVo.getFeedbackImgs();
            if (StringUtils.isNotBlank(feedbackImgs)) {
                String[] imgs = feedbackImgs.split(",");
                ossIdList.addAll(Arrays.stream(imgs).map(Long::parseLong).toList());
            }
        }
        if (CollUtil.isEmpty(ossIdList)) {
            return;
        }

        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIdList(ossIdList);
        if (CollUtil.isEmpty(remoteFiles)) {
            return;
        }

        Map<Long, String> ossUrlMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, RemoteFile::getUrl));

        for (FeedbackRecordVo recordVo : feedbackRecordVo) {
            String feedbackImgs = recordVo.getFeedbackImgs();
            if (StringUtils.isNotBlank(feedbackImgs)) {
                String[] imgs = feedbackImgs.split(",");
                List<String> imgUrlList = Arrays.stream(imgs).map(ossId -> ossUrlMap.get(Long.parseLong(ossId))).toList();
                recordVo.setFeedbackImgUrlList(imgUrlList);
            }
        }

    }

    /**
     * 查询反馈记录列表
     */
    @Override
    public TableDataInfo<FeedbackRecordVo> queryPageList(FeedbackRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<FeedbackRecord> lqw = buildQueryWrapper(bo);
        Page<FeedbackRecordVo> result = baseMapper.selectRecordPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithCreateStaffInfo())) {
            putCreateStaffInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }


    @Override
    public void read(Long feedbackRecordId) {
        FeedbackRecord check = baseMapper.selectById(feedbackRecordId);
        if (ObjectUtils.isEmpty(check)) {
            return;
        }
        if (UserConstants.FEEDBACK_PARENT_READ_STATUS_YES.equals(check.getParentReadStatus())) {
            return;
        }
        FeedbackRecord feedbackRecord = new FeedbackRecord();
        feedbackRecord.setFeedbackRecordId(feedbackRecordId);
        feedbackRecord.setParentReadStatus(UserConstants.FEEDBACK_PARENT_READ_STATUS_YES);
        baseMapper.updateById(feedbackRecord);
    }


    @Override
    public FeedbackRecordVo queryLatestFeedbackRecord(Long feedbackRecordId, Long studentId) {
        FeedbackRecordVo feedbackRecordVo = baseMapper.queryLatestFeedbackRecord(feedbackRecordId, studentId);
        afterSetFeedbackUnShowStudyRecordId(feedbackRecordVo);
        return feedbackRecordVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Long feedbackRecordId) {
        // 查询出这条记录的反馈日期
        FeedbackRecord feedbackRecord = baseMapper.selectById(feedbackRecordId);
        if (ObjectUtils.isEmpty(feedbackRecord)) {
            throw new ServiceException("未查询到反馈记录");
        }


        // 已经反馈过了，不再重复计算反馈状态 历史数据不做处理
        if (feedbackRecord.getFeedbackSubmitStatus() == null || FeedbackStatusConstant.HAS_FEEDBACK.equals(feedbackRecord.getFeedbackSubmitStatus()) || FeedbackStatusConstant.OVERTIME_FEEDBACK.equals(feedbackRecord.getFeedbackSubmitStatus())) {
            return;
        }

        Integer feedbackSubmitStatus;
        if (!feedbackRecord.getFeedbackStartDate().equals(feedbackRecord.getFeedbackEndDate())) {
            // 多日反馈
            feedbackSubmitStatus = FeedbackStatusConstant.HAS_FEEDBACK;
        } else {
            // 单日反馈
            feedbackSubmitStatus = attendanceDailyActivityFeedbackRecordService.calculateFeedbackStatus(feedbackRecord.getFeedbackStartDate(),true);
            AttendanceDailyActivityFeedbackRecordVo attendanceDailyActivityFeedbackRecordVo = attendanceDailyActivityFeedbackRecordService.queryByFeedbackRecordId(feedbackRecordId);
            // 反馈记录id不存在那么就不需要更新会员每日学习反馈记录 的反馈状态
            if (attendanceDailyActivityFeedbackRecordVo != null) {
                AttendanceDailyActivityFeedbackRecordBo attendanceDailyActivityFeedbackRecordBo = new AttendanceDailyActivityFeedbackRecordBo();
                attendanceDailyActivityFeedbackRecordBo.setAttendanceDailyActivityId(attendanceDailyActivityFeedbackRecordVo.getAttendanceDailyActivityId());
                attendanceDailyActivityFeedbackRecordBo.setFeedbackStatus(feedbackSubmitStatus);
                attendanceDailyActivityFeedbackRecordService.updateByAttendanceDailyActivityFeedbackRecord(attendanceDailyActivityFeedbackRecordBo);
            }
        }
        // 设置反馈状态
        feedbackRecord.setFeedbackSubmitStatus(feedbackSubmitStatus);
        baseMapper.updateById(feedbackRecord);
    }

    /**
     * 查询反馈记录列表
     */
    @Override
    public List<FeedbackRecordVo> queryList(FeedbackRecordBo bo) {
        handleQueryParam(bo);
        QueryWrapper<FeedbackRecord> lqw = buildQueryWrapper(bo);
        List<FeedbackRecordVo> result = baseMapper.selectRecordList(lqw);
        if (Boolean.TRUE.equals(bo.getWithCreateStaffInfo())) {
            putCreateStaffInfo(result);
        }
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result);
        }
        return result;
    }


    private QueryWrapper<FeedbackRecord> buildQueryWrapper(FeedbackRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<FeedbackRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackContent()), "t.feedback_content", bo.getFeedbackContent());
        lqw.ge(bo.getFeedbackStartDate() != null, "t.feedback_start_date", bo.getFeedbackStartDate() != null ? DateUtils.dateTime(bo.getFeedbackStartDate()) : null);
        lqw.le(bo.getFeedbackEndDate() != null, "t.feedback_end_date", bo.getFeedbackEndDate() != null ? DateUtils.dateTime(bo.getFeedbackEndDate()) : null);
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackImgs()), "t.feedback_imgs", bo.getFeedbackImgs());
        lqw.eq(bo.getShowScore() != null, "t.show_score", bo.getShowScore());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.ge(bo.getFeedbackStartDateLimit() != null, "t.feedback_start_date", bo.getFeedbackStartDateLimit() != null ? DateUtils.dateTime(bo.getFeedbackStartDateLimit()) : null);
        lqw.le(bo.getFeedbackEndDateLimit() != null, "t.feedback_end_date", bo.getFeedbackEndDateLimit() != null ? DateUtils.dateTime(bo.getFeedbackEndDateLimit()) : null);
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like concat('%',{0},'%')", bo.getNameWithPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentAccount()), "s.student_account", bo.getStudentAccount());
        lqw.eq(bo.getCreateBy() != null, "t.create_by", bo.getCreateBy());
        // 增加反馈状态的查询
        lqw.eq(bo.getFeedbackSubmitStatus() != null, "t.feedback_submit_status", bo.getFeedbackSubmitStatus());

        lqw.eq(StringUtils.isNotBlank(bo.getParentNotificationStatus()), "t.parent_notification_status", bo.getParentNotificationStatus());
        lqw.ge(bo.getNotificationTime() != null, "t.notification_time", bo.getNotificationTime());
        lqw.eq(StringUtils.isNotBlank(bo.getNotificationOpenid()), "t.notification_openid", bo.getNotificationOpenid());


        if (bo.getFeedbackRecordCreateTimeEnd() != null && bo.getFeedbackRecordCreateTimeStart() != null) {
            lqw.between("t.create_time", bo.getFeedbackRecordCreateTimeStart(), bo.getFeedbackRecordCreateTimeEnd());
        }
        //查顾问
        if (bo.getConsultantId() != null) {
            RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
            remoteStaffBo.setBranchStaffId(bo.getConsultantId());
            List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
            if (CollUtil.isNotEmpty(remoteStaffVos)) {
                List<Long> consultantIds = remoteStaffVos.stream().map(RemoteStaffVo::getBranchStaffId).collect(Collectors.toList());
                Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(consultantIds);
                if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
                    lqw.in("t.student_id", staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList());
                } else {
                    lqw.in("t.student_id", -1);
                }
            } else {
                lqw.in("t.student_id", -1);
            }
        }
        lqw.orderByDesc("t.create_time");
        return lqw;
    }

    private void putConsultantInfo(List<FeedbackRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(FeedbackRecordVo::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(list);
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return;
        }
        List<Long> consultantIdList = studentConsultantIdMap.values().stream().distinct().toList();
        if (CollUtil.isEmpty(consultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(consultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        records.forEach(item -> {
            item.setConsultantInfo(remoteStaffVoMap.get(studentConsultantIdMap.get(item.getStudentId())));
        });
    }


    private void putCreateStaffInfo(List<FeedbackRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(FeedbackRecordVo::getCreateBy).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }

        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setUserIdList(list);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getCreateBy, vo -> vo));
        records.forEach(item -> {
            item.setCreateStaffInfo(remoteStaffVoMap.get(item.getCreateBy()));
        });
    }


    public void putStudentInfo(List<FeedbackRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(FeedbackRecordVo::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(list);
        studentBo.setWithSysUserInfo(Boolean.TRUE);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, vo -> vo));
        records.forEach(item -> {
            item.setStudent(studentVoMap.get(item.getStudentId()));
        });
    }

    public void putAttendanceDailyActivityVo(FeedbackRecordVo feedbackRecordVo) {
        Long feedbackRecordId = feedbackRecordVo.getFeedbackRecordId();
        // 设置对应的AttendanceDailyActivityFeedbackRecordVo信息
        AttendanceDailyActivityFeedbackRecordVo attendanceDailyActivityFeedbackRecordVo = attendanceDailyActivityFeedbackRecordService.queryByFeedbackRecordId(feedbackRecordId);
        feedbackRecordVo.setAttendanceDailyActivityFeedbackRecordVo(attendanceDailyActivityFeedbackRecordVo);
    }


    /**
     * 新增反馈记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FeedbackRecordBo bo) {
        if (LoginHelper.getBranchStaffId() == null) {
            throw new ServiceException("非员工不能新增反馈记录");
        }
        bo.setFeedbackSubmitStatus(FeedbackStatusConstant.NOT_FEEDBACK);
        FeedbackRecord add = MapstructUtils.convert(bo, FeedbackRecord.class);

        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFeedbackRecordId(add.getFeedbackRecordId());
        }

        if (ObjectUtils.isEmpty(add.getFeedbackRecordId())){
            throw new ServiceException("新增反馈记录失败");
        }

        List<Long> unShowStudyRecordIdList = bo.getUnShowStudyRecordIdList();
        Long feedbackRecordId = add.getFeedbackRecordId();
        afterInsertUnShowStudyRecordList(unShowStudyRecordIdList, feedbackRecordId);

        if (UserConstants.FEEDBACK_STATUS_PUBLISHED.equals(add.getFeedbackStatus())) {
            SpringUtils.getAopProxy(this).notifyParent(add.getFeedbackRecordId());
        }

        AttendanceDailyActivityVo attendanceDailyActivityVo = attendanceDailyActivityService.queryAttendanceDailyActivityByStudentIdAndDate(bo.getStudentId(), bo.getFeedbackStartDate());
        if (attendanceDailyActivityVo == null) {
            // 说明本次新增的反馈日期，这个学生这个日期本来不需要反馈，直接返回
            return flag;
        }
        handleFeedbackStatus(bo, attendanceDailyActivityVo);

        return flag;
    }

    private void afterInsertUnShowStudyRecordList(List<Long> unShowStudyRecordIdList, Long feedbackRecordId) {
        if (CollUtil.isNotEmpty(unShowStudyRecordIdList)) {
            List<FeedbackRecordExclusion> list = unShowStudyRecordIdList.stream().map(studyRecordId -> {
                FeedbackRecordExclusion feedbackRecordExclusion = new FeedbackRecordExclusion();
                feedbackRecordExclusion.setFeedbackRecordId(feedbackRecordId);
                feedbackRecordExclusion.setStudyPlanningRecordId(studyRecordId);
                return feedbackRecordExclusion;
            }).toList();
            feedbackRecordExclusionMapper.delete(
                new LambdaQueryWrapper<FeedbackRecordExclusion>().eq(FeedbackRecordExclusion::getFeedbackRecordId,
                    feedbackRecordId));
            feedbackRecordExclusionMapper.insertBatch(list);

        }
    }

    /**
     * 修改反馈记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FeedbackRecordBo bo) {
        // 查询修改后的日期是否有会员每日打卡登录情况，有则修改会员每日打卡登录情况
        AttendanceDailyActivityVo attendanceDailyActivityVo = attendanceDailyActivityService.queryAttendanceDailyActivityByStudentIdAndDate(bo.getStudentId(), bo.getFeedbackStartDate());
        handleFeedbackStatus(bo, attendanceDailyActivityVo);

        FeedbackRecord update = MapstructUtils.convert(bo, FeedbackRecord.class);
        validEntityBeforeSave(update);
        boolean b = baseMapper.updateById(update) > 0;
        afterInsertUnShowStudyRecordList(bo.getUnShowStudyRecordIdList(), update.getFeedbackRecordId());

        if (UserConstants.FEEDBACK_STATUS_PUBLISHED.equals(update.getFeedbackStatus())) {
            SpringUtils.getAopProxy(this).notifyParent(update.getFeedbackRecordId());
        }
        return b;
    }


    private void handleFeedbackStatus(FeedbackRecordBo bo, AttendanceDailyActivityVo attendanceDailyActivityVo) {
        if (!bo.getFeedbackStartDate().equals(bo.getFeedbackEndDate())) {
            return;
        }
        // 更新反馈状态
        if (attendanceDailyActivityVo != null) {
            // 说明已经反馈过  查询这天的反馈记录是否已经分享链接
            Long attendanceDailyActivityId = attendanceDailyActivityVo.getAttendanceDailyActivityId();
            AttendanceDailyActivityFeedbackRecordVo attendanceDailyActivityFeedbackRecordVo = attendanceDailyActivityFeedbackRecordService.queryByAttendanceDailyActivityId(attendanceDailyActivityId);
            Integer feedbackStatus = attendanceDailyActivityFeedbackRecordVo.getFeedbackStatus();


            AttendanceDailyActivityFeedbackRecordBo attendanceDailyActivityFeedbackRecordBo = new AttendanceDailyActivityFeedbackRecordBo();
            attendanceDailyActivityFeedbackRecordBo.setAttendanceDailyActivityId(attendanceDailyActivityId);

            // 已反馈过或者超时反馈的，那么后续修改的和新增的都使用这个状态
            if (FeedbackStatusConstant.HAS_FEEDBACK.equals(feedbackStatus)
                || FeedbackStatusConstant.OVERTIME_FEEDBACK.equals(feedbackStatus)) {
                bo.setFeedbackSubmitStatus(feedbackStatus);

                // 根据最新的反馈记录 修改 对应的会员每日打卡登录情况表中的 发布状态
                attendanceDailyActivityFeedbackRecordBo.setPublishStatus(Integer.valueOf(bo.getFeedbackStatus()));
            } else {
                // 因为新增反馈记录的时候，发布状态可能会发生变化，所以这里重新设置
                attendanceDailyActivityFeedbackRecordBo.setPublishStatus(Integer.valueOf(bo.getFeedbackStatus()));
                // 能查询到记录存在，但是反馈记录没有分享过.（每次都保存未分享之前最新的反馈记录ID）
                attendanceDailyActivityFeedbackRecordBo.setFeedbackRecordId(bo.getFeedbackRecordId());
            }
            attendanceDailyActivityFeedbackRecordService.updateByAttendanceDailyActivityFeedbackRecord(attendanceDailyActivityFeedbackRecordBo);
        }
    }

    //发送家长通知
    @Override
    public void notifyParent(Long feedbackRecordId) {
        Date now = new Date();
        FeedbackRecordVo feedbackRecord = baseMapper.selectVoById(feedbackRecordId);
        putCreateStaffInfo(List.of(feedbackRecord));
        RemoteStaffVo createStaffInfo = feedbackRecord.getCreateStaffInfo();
        if (createStaffInfo == null || createStaffInfo.getUser() == null) {
            throw new ServiceException("创建员工信息不存在");
        }
        Long studentId = feedbackRecord.getStudentId();
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(studentId);
        StudentVo studentVo = studentService.queryById(studentBo);
        if (studentVo == null) {
            throw new ServiceException("会员不存在");
        }
        String parentWechatOpenId = "";
        StudentParentRecordVo studentParentRecordVo = getStudentParentRecordVo(studentVo);
        if (!ObjectUtils.isEmpty(studentParentRecordVo)) {
         parentWechatOpenId=studentParentRecordVo.getParentWechatOpenId();
        }

        String msgTemplate = "点评时间: {}\n点评老师: {}\n学员姓名: {}\n点评内容: {}......";
        String str = StrUtil.format(msgTemplate, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, feedbackRecord.getCreateTime()),
            createStaffInfo.getUser().getNickName(),
            studentVo.getStudentName(),
            StrUtil.sub(feedbackRecord.getFeedbackContent(), 0, 4));

        Boolean b = remoteWxService.sendFeedbackMessage(parentWechatOpenId, feedbackRecordId, "新的学习反馈通知", str, "#999999");
        if (!b) {
            throw new ServiceException("发送微信通知失败");
        }
        //更新为已通知
        FeedbackRecord updateBean = new FeedbackRecord();
        updateBean.setFeedbackRecordId(feedbackRecordId);
        updateBean.setParentNotificationStatus(UserConstants.FEEDBACK_PARENT_NOTIFICATION_STATUS_YES);
        updateBean.setNotificationTime(now);
        updateBean.setNotificationOpenid(parentWechatOpenId);
        baseMapper.updateById(updateBean);
    }

    private StudentParentRecordVo getStudentParentRecordVo(StudentVo studentVo) {
        Long studentParentRecordId = studentVo.getStudentParentRecordId();
        // 没有绑定家长，报错，业务理论上来说不会出现没有绑定而进行绑定的情况
        //2025.2.24没有绑定家长也可以发反馈
        if (studentParentRecordId == null) {
          return null;
        }
        //查会员绑定记录
        StudentParentRecordVo studentParentRecordVo = studentParentRecordService.queryById(studentParentRecordId);
        if (studentParentRecordVo == null || studentParentRecordVo.getParentWechatOpenId() == null) {
            return null;
        }
        return studentParentRecordVo;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FeedbackRecord entity) {
        //做一些数据校验,如唯一约束
        Assert.notNull(entity, "参数缺失");

    }

    /**
     * 批量删除反馈记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }


    private void handleQueryParam(FeedbackRecordBo record) {
        if (record.getStudentId() != null) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else if (null != LoginHelper.getBranchId()) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else {
            record.setStudentIdList(List.of(-1L));
        }

    }
}
