package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 学习计划冲突vo
 *
 *
 * @date 2024/05/31 03:19:46
 */
@Data
public class StudyPlanningConflictVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 冲突的日期
     */
    private String conflictDate;

    /**
     * 线下courseID
     */
    private Long offlineCourseId;

    /**
     * 线下course信息
     */
    private CourseVo offlineCourse;

    /**
     * 线下冲突的studyPlanningRecordId(不一定有，但是如果是拖动以前已经保存的学习规划详情，那么就有)
     */
    private  Long offlineStudyPlanningRecordId;

    /**
     * 线上courseID
     */
    private Long onlineCourseId;

    /**
     * 线上course信息
     */
    private CourseVo onlineCourse;

    /**
     * 线上冲突的studyPlanningRecordId
     */
    private Long onlineStudyPlanningRecordId;

    /**
     * 线下开始学习时间
     */
    private String offlineStartTime;

    /**
     * 线下结束学习时间
     */
    private String offlineEndTime;

    /**
     * 线上开始学习时间
     */
    private String onlineStartTime;

    /**
     * 线上结束学习时间
     */
    private String onlineEndTime;

    /**
     * 会员ID
     */
    private Long studentId;

    /**
     * 会员信息
     */
    private StudentVo student;

}
