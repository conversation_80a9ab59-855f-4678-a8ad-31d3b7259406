package com.jxw.shufang.student.service;

import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.dto.StudentMerchantConfigDTO;
import com.jxw.shufang.student.domain.bo.UpdateKuaidingPrivilegeBo;
import com.jxw.shufang.student.domain.vo.StuAccumulatedStudyData;
import com.jxw.shufang.student.domain.vo.StudentVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员Service接口
 *
 *
 * @date 2024-02-27
 */
public interface IStudentService {

    /**
     * 查询会员
     */
    StudentVo queryById(StudentBo studentBo);

    /**
     * 查询会员列表
     */
    TableDataInfo<StudentVo> queryPageList(StudentBo bo, PageQuery pageQuery);

    /**
     * 查询会员列表
     */
    List<StudentVo> queryList(StudentBo bo);

    /**
     * 查询会员列表
     */
    List<StudentVo> queryList(StudentBo bo, Boolean ignoreDataScope);

    /**
     * 新增会员
     *
     * @return
     */
    Long insertByBo(StudentBo bo) throws ServiceException;

    /**
     * 修改会员
     */
    void updateByBo(StudentBo bo);

    /**
     * 修改会员StudentConsultantRecord
     */
    void updateStudentConsultantRecordIdByBo(StudentBo bo);

    /**
     * 修改会员studentAiCourseRecordId
     */
    void updateStudentAiCourseRecordIdByBo(StudentBo bo);

    /**
     * 修改会员StudentConsultantRecord
     */
    void updateStudentParentRecordIdByBo(StudentBo bo);

    /**
     * 校验并批量删除会员信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 选项列表,简化了返回的数据，加快返回，支持分页查询
     *
     * @param bo bo
     *
     * @date 2024/02/29 04:40:36
     */
    TableDataInfo<StudentVo> queryOptionList(StudentBo bo, PageQuery pageQuery);

    /**
     * 查询会员id列表
     *
     * @param studentBo 会员bo
     *
     * @date 2024/03/04 03:05:09
     */
    List<Long> queryStudentIdList(StudentBo studentBo);


    /**
     * 更新会员过期时间
     *
     * @param studentId     会员id
     * @param maxExpireTime 最长过期时间
     * @date 2024/03/14 01:59:55
     */
    void updateStudentExpireTime(Long studentId, Date maxExpireTime);

    /**
     * 更改状态
     *
     * @param studentId     会员id
     * @param studentStatus 会员状态
     *
     * @date 2024/03/15 06:01:45
     */
    void changeStatus(Long studentId, String studentStatus);

    /**
     * 强制脱机
     *
     * @param studentId 会员id
     *
     * @date 2024/03/16 02:59:09
     */
    void forceOffline(Long studentId);


    /**
     * 更改pwd
     *
     * @param bo bo
     *
     * @date 2024/03/16 11:56:04
     */
    void restPwd(StudentBo bo);

    Student queryStudentById(Long studentId);

    void cleanCache();

    Long queryStudentIdByUserId(Long userId);

    /**
     * 获取信息
     *
     * @param studentId 会员id
     *
     * @date 2024/04/18 08:24:51
     */
    StudentVo getInfo(Long studentId);

    /**
     * 更改pwd
     *
     * @param oldPwd      旧pwd
     * @param newPwd      新pwd
     * @param checkOldPwd 是否校验旧pwd
     *
     * @date 2024/04/19 01:04:28
     */
    void changePwd(Long studentId, String oldPwd, String newPwd, Boolean checkOldPwd);

    void checkPassword(String password);

    /**
     * 查询会员信息通过会员用户名（手机格式）
     *
     * @param phone           电话
     * @param withSysUserInfo 带有系统用户信息
     *
     * @date 2024/04/23 10:52:57
     */
    StudentVo queryByStudentPhone(String phone, Boolean withSysUserInfo);

    /**
     * 按用户id查询学生
     *
     * @param userId 用户id
     *
     * @date 2024/04/24 03:29:28
     */
    StudentVo queryStudentByUserId(Long userId);

    /**
     * 通过电话询问学生
     *
     * @param phone 电话
     *
     * @date 2024/05/06 12:59:02
     */
    StudentVo queryStudentByPhone(String phone);

    /**
     * 查询门店下所有会员
     *
     * @param branchId 门店id
     *
     * @date 2024/05/06 12:59:02
     */
    List<Long> getStudentIdListByBranchId(Long branchId);


    /**
     * 查询门店下所有会员
     *
     * @param branchIdList 门店id列表
     *
     * @date 2024/05/06 12:59:02
     */
    List<Long> getStudentIdListByBranchIdList(List<Long> branchIdList);

    /**
     * 查询会员顾问下所有会员
     *
     * @param branchStaffId 会员顾问id
     *
     * @date 2024/05/06 12:59:02
     */
    List<StudentVo> getStudentListByStaffId(Long branchStaffId, String nameWithPhone);

    /**
     * 查询用户idMap 学生id -> 用户id
     *
     * @param studentIdList
     * @return
     */
    Map<Long, Long> queryUserIdMapByStudentIdList(List<Long> studentIdList);

    /**
     * 查询学生的累计学习数据
     *
     * @param studentId
     * @return
     *
     * @date 2024/06/12 0:58:22
     */
    StuAccumulatedStudyData getStuAccumulatedStudyData(Long studentId);

    List<StudentVo> queryByStuPhoneOrParentPhone(String phone);

    boolean updateBatchByBo(List<StudentBo> updateStuList);

    boolean unbindParent(Long studentId);

    Map<Long, String> queryStudentName(List<Long> studentIdList);

    RemoteBranchVo getBranch(Long studentId);

    /**
     * 根据会员ID批量查询会员
     * @param studentIds
     * @return
     */
    List<Student> batchQueryStudentById(List<Long> studentIds);


    /**
     * 更新会员的优惠额度
     *
     * @param bo 会员信息
     * @param productPreferentialAmount 变动的金额
     * @return
     */
    boolean updatePreferentialAmountByBo(StudentBo bo, BigDecimal productPreferentialAmount);

    /**
     * 获取会员的优惠额度
     *
     * @param studentBo
     * @return
     */
    StudentVo getPreferentialAmount(StudentBo studentBo);

    Map<Long, Student> batchQueryMapStudents(List<Long> studentIds);


    TableDataInfo<StudentVo> queryControlledStudentList(Long branchFeatureTemplateId, StudentBo bo, PageQuery pageQuery);

    /**
     * 获取学生对应门店的支付配置
     *
     * @param studentId
     * @return
     */
    String getCommonPayAppId(Long studentId);

    StudentMerchantConfigDTO getStudentMerchantConfig(Long studentId);

    /**
     * 根据会员ID查询会员所有门店的上一级代理商ID
     */
    Long queryPreDeptIdByStudentId(Long studentId);

    /**
     * 更新会员购卡标识
     *
     * @param studentId
     * @param updateFlag
     */
    void updateBuyCardFlag(Long studentId, Integer updateFlag);

    /**
     * 更新会员快叮岛权益
     *
     * @param bo
     * @return
     */
    Boolean updateKuaidingPrivilege(UpdateKuaidingPrivilegeBo bo);
}
