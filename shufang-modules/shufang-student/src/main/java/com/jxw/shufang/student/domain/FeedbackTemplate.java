package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 反馈模板对象 feedback_template
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback_template")
public class FeedbackTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈模板id
     */
    @TableId(value = "feedback_template_id")
    private Long feedbackTemplateId;

    /**
     * 反馈维度（对应字典值）
     */
    private String feedbackType;

    /**
     * 反馈内容
     */
    private String feedbackTemplateContent;

    /**
     * 模板来源（对应字典值）
     */
    private String feedbackTemplateSource;

    /**
     * 模板使用次数
     */
    private Integer templateUseCount;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
