package com.jxw.shufang.student.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AttendanceLogStudentEzkeco;
import com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo;
import com.jxw.shufang.student.domain.vo.StudentAttendanceCountVo;

/**
 * ezkeco学员考勤记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface AttendanceLogStudentEzkecoMapper extends BaseMapperPlus<AttendanceLogStudentEzkeco, AttendanceLogStudentEzkecoVo> {


    Page<AttendanceLogStudentEzkecoVo> queryPageList(@Param("page") Page<AttendanceLogStudentEzkeco> build,@Param(Constants.WRAPPER) QueryWrapper<AttendanceLogStudentEzkeco> lqw);

    List<String> getAllAttendanceDate(Long studentId);

    /**
     * 查询学生在指定时间段内的打卡天数统计
     *
     * @param studentIds 学生ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 学生ID -> 打卡天数
     */
    List<StudentAttendanceCountVo> countCheckDaysByStudentIds(@Param("studentIds") List<Long> studentIds,
        @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
