package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanningRecord;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 学习规划记录业务对象 study_planning_record
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanningRecord.class, reverseConvertGenerate = false)
public class StudyPlanningRecordUpdateBo extends BaseEntity {

    /**
     * 学习规划记录id
     */
    @NotNull(message = "学习规划记录id不能为空", groups = { EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 学习规划id
     */
    @NotNull(message = "学习规划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningId;


    /**
     * 学习开始时间
     */
    @NotNull(message = "学习开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    @NotNull(message = "学习结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyEndTime;


}
