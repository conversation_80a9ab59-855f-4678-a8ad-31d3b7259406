package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.CorrectionStatusEnum;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.TreeBuildUtils;
import com.jxw.shufang.common.excel.convert.ExcelBigNumberConvert;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.student.config.StudentGradeConfig;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.CourseGrade;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.dto.CourseTreeNodeDTO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.enums.CourseProcessEnum;
import com.jxw.shufang.student.mapper.CourseMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteAttributeService;
import com.jxw.shufang.system.api.RemoteConfigService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.RemoteDictService;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;
import com.jxw.shufang.system.api.domain.vo.*;
import com.jxw.shufang.system.api.enums.AttrGroupTypeEnum;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 课程（课程包含章节）Service业务层处理
 *
 * @date 2024-03-30
 */
//懒加载，允许循环依赖
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class CourseServiceImpl implements ICourseService, BaseService {

    private static final Logger log = LoggerFactory.getLogger(CourseServiceImpl.class);

    private final CourseMapper baseMapper;

    //字典服务
    private final DictService dictService;

    @DubboReference
    private RemoteDictService remoteDictService;

    @DubboReference
    private RemoteAttributeService remoteAttributeService;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    @DubboReference
    private RemoteExtVideoService remoteVideoService;

    @DubboReference
    private RemoteExtResourceService remoteResourceService;

    private final IStudyRecordService studyRecordService;

    private final IAiStudyRecordService aiStudyRecordService;

    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;

    private final IStudentService studentService;

    private final IStudentAiCourseRecordInfoService studentAiCourseRecordInfoService;

    @DubboReference
    private RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;


    private final IAiStudyVideoRecordService aiStudyVideoRecordService;

    private final ICourseGradeService courseGradeService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteExtResourceService remoteExtResourceService;

    @Autowired
    private StudentGradeConfig studentGradeConfig;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询课程（课程包含章节）
     */
    @Override
    public CourseVo queryById(Long courseId, Boolean withAttr) {

        CourseVo courseVo = baseMapper.selectVoById(courseId);
        if (courseVo == null) {
            return null;
        }
        if (withAttr) {
            putAttr(List.of(courseVo));
            putCourseDetail(List.of(courseVo), false);
        }

        // 获取可适用课程
        List<CourseGradeVo> courseGradeVos = courseGradeService.listByCourseId(courseId);
        if (!CollectionUtils.isEmpty(courseGradeVos)) {
            courseVo
                .setApplicableGrade(courseGradeVos.stream().map(CourseGradeVo::getGrade).collect(Collectors.toList()));
        }
        return courseVo;
    }

    /**
     * 查询课程（课程包含章节）
     */
    @Override
    public CourseVo queryById(Long courseId) {
        return queryById(courseId, false);
    }

    /**
     * 查询课程（课程包含章节）列表
     */
    @Override
    public TableDataInfo<CourseVo> queryPageList(CourseBo bo, PageQuery pageQuery) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        LambdaQueryWrapper<Course> lqw = buildLambdaQueryWrapper(bo);
        Page<CourseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (bo.getWithAttr() != null && bo.getWithAttr()) {
            putAttr(result.getRecords());
            putCourseDetail(result.getRecords(), false);
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询课程（课程包含章节）列表
     */
    @Override
    public List<CourseVo> queryList(CourseBo bo) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        LambdaQueryWrapper<Course> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Course> buildLambdaQueryWrapper(CourseBo bo) {
        LambdaQueryWrapper<Course> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCourseId() != null, Course::getCourseId, bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getAncestors()), Course::getAncestors, bo.getAncestors());
        lqw.eq(StringUtils.isNotBlank(bo.getStage()), Course::getStage, bo.getStage());
        lqw.eq(StringUtils.isNotBlank(bo.getGrade()), Course::getGrade, bo.getGrade());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecialTopic()), Course::getSpecialTopic, bo.getSpecialTopic());
        lqw.eq(StringUtils.isNotBlank(bo.getAffiliationSubject()), Course::getAffiliationSubject, bo.getAffiliationSubject());
        lqw.like(StringUtils.isNotBlank(bo.getCourseName()), Course::getCourseName, bo.getCourseName());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseIntroduction()), Course::getCourseIntroduction, bo.getCourseIntroduction());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseNo()), Course::getCourseNo, bo.getCourseNo());
        lqw.eq(bo.getCourseThumbnail() != null, Course::getCourseThumbnail, bo.getCourseThumbnail());
        lqw.eq(bo.getCourseParentId() != null, Course::getCourseParentId, bo.getCourseParentId());
        lqw.eq(bo.getCourseType() != null, Course::getCourseType, bo.getCourseType());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseSource()), Course::getCourseSource, bo.getCourseSource());
        lqw.eq(bo.getCreateDept() != null, Course::getCreateDept, bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, Course::getCreateBy, bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, Course::getCreateTime, bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, Course::getUpdateBy, bo.getUpdateBy());
        lqw.eq(bo.getUpdateTime() != null, Course::getUpdateTime, bo.getUpdateTime());
        lqw.eq(bo.getQuarterType() != null, Course::getQuarterType, bo.getQuarterType());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), Course::getCourseId, bo.getCourseIdList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotIncourseIdList()), Course::getCourseId, bo.getNotIncourseIdList());

        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), Course::getDelFlag, bo.getDelFlag());
        } else {
            lqw.eq(Course::getDelFlag, UserConstants.DEL_FLAG_NO);
        }

        Set<Long> courseIds = new HashSet<>();
        if (StringUtils.isNotBlank(bo.getAttrSearchJson())) {
            List<Long> courseIdList = remoteAttributeService.search(bo.getAttrSearchJson(), AttrGroupTypeEnum.COURSE.getType());
            if (CollUtil.isNotEmpty(courseIdList)) {
                courseIds.addAll(courseIdList);
            } else {
                setNotExistCourseId(courseIds);
            }
        }

        // 根据课次名称或者知识点id查询
        if (StringUtils.isNotBlank(bo.getCatalogName())) {
            List<CourseVo> courseVos = baseMapper.selectVoList(Wrappers.<Course>lambdaQuery()
                .eq(Course::getDelFlag, 0)
                .ne(Course::getCourseType, 1)
                .like(Course::getCourseName, bo.getCatalogName())
            );
            if (CollUtil.isNotEmpty(courseVos)) {
                List<Long> list = courseVos.stream().map(CourseVo::getTopCourseId).toList();
                if (CollUtil.isNotEmpty(courseIds)) {
                    courseIds.retainAll(list);
                } else {
                    courseIds.addAll(list);
                }
                if (CollUtil.isEmpty(courseIds)) {
                    setNotExistCourseId(courseIds);
                }
            } else {
                setNotExistCourseId(courseIds);
            }
        } else if (Objects.nonNull(bo.getKnowledgeId())) {
            List<CourseVo> courseVos = baseMapper.selectVoList(Wrappers.<Course>lambdaQuery()
                .eq(Course::getDelFlag, 0)
                .eq(Course::getKnowledgeId, bo.getKnowledgeId())
            );
            if (CollUtil.isNotEmpty(courseVos)) {
                List<Long> list = courseVos.stream().map(CourseVo::getTopCourseId).toList();
                if (CollUtil.isNotEmpty(courseIds)) {
                    courseIds.retainAll(list);
                } else {
                    courseIds.addAll(list);
                }
                if (CollUtil.isEmpty(courseIds)) {
                    setNotExistCourseId(courseIds);
                }
            } else {
                setNotExistCourseId(courseIds);
            }
        }

        if (CollUtil.isNotEmpty(courseIds)) {
            lqw.in(Course::getCourseId, courseIds);
        }

        if (Boolean.TRUE.equals(bo.getOrderBy())) {
            lqw.orderByAsc(Course::getSort);
        }
        return lqw;
    }

    private void setNotExistCourseId(Set<Long> courseIds) {
        courseIds.clear();
        courseIds.add(-1L);
    }


    private QueryWrapper<Course> buildQueryWrapper(CourseBo bo, boolean considerApplicableGrade) {
        QueryWrapper<Course> lqw = Wrappers.query();
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getStage()), "t.stage", bo.getStage());
        if (considerApplicableGrade) {
            lqw.eq(StringUtils.isNotBlank(bo.getGrade()), "cg.grade", bo.getGrade());
        } else {
            lqw.eq(StringUtils.isNotBlank(bo.getGrade()), "t.grade", bo.getGrade());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getAncestors()), "t.ancestors", bo.getAncestors());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecialTopic()), "t.special_topic", bo.getSpecialTopic());
        lqw.eq(StringUtils.isNotBlank(bo.getAffiliationSubject()), "t.affiliation_subject", bo.getAffiliationSubject());
        lqw.like(StringUtils.isNotBlank(bo.getCourseName()), "t.course_name", bo.getCourseName());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseIntroduction()), "t.course_introduction", bo.getCourseIntroduction());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseNo()), "t.course_no", bo.getCourseNo());
        lqw.eq(bo.getCourseThumbnail() != null, "t.course_thumbnail", bo.getCourseThumbnail());
        lqw.eq(bo.getCourseParentId() != null, "t.course_parent_id", bo.getCourseParentId());
        lqw.eq(bo.getCourseType() != null, "t.course_type", bo.getCourseType());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseSource()), "t.course_source", bo.getCourseSource());
        lqw.eq(bo.getCreateDept() != null, "t.create_dept", bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, "t.create_by", bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, "t.create_time", bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, "t.update_by", bo.getUpdateBy());
        lqw.eq(bo.getUpdateTime() != null, "t.update_time", bo.getUpdateTime());
        lqw.eq(bo.getQuarterType() != null, "t.quarter_type", bo.getQuarterType());
        lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), "t.del_flag", bo.getDelFlag());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), "t.course_id", bo.getCourseIdList());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotIncourseIdList()), "t.course_id", bo.getNotIncourseIdList());

        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), "t.del_flag", bo.getDelFlag());
        } else {
            lqw.eq("t.del_flag", UserConstants.DEL_FLAG_NO);
        }
        if (StringUtils.isNotBlank(bo.getAttrSearchJson())) {
            List<Long> courseIdList = remoteAttributeService.search(bo.getAttrSearchJson(), AttrGroupTypeEnum.COURSE.getType());
            if (CollUtil.isNotEmpty(courseIdList)) {
                lqw.in("t.course_id", courseIdList);
            } else {
                lqw.eq(false, "t.course_id", -1L);
            }
        }
        if (Boolean.TRUE.equals(bo.getOrderBy())) {
            lqw.orderByAsc("t.sort");
        }
        return lqw;
    }

    /**
     * 新增课程（课程包含章节）
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(CourseBo bo) {
        if (bo.getCourseParentId() == null || bo.getCourseParentId() == 0) {
            bo.setCourseParentId(0L);
            bo.setCourseType(1);
            bo.setAncestors("0");
            if (bo.getKnowledgeId() != null) {
                throw new ServiceException("最顶级课程不允许设置知识点");
            }
        } else {
            //添加章节
            CourseVo courseVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class).eq(Course::getCourseId, bo.getCourseParentId()));
            if (courseVo == null) {
                throw new ServiceException("父节点不存在");
            }
            bo.setCourseType(courseVo.getCourseType() + 1);
            bo.setAncestors(courseVo.getAncestors() + "," + courseVo.getCourseId());
            if (courseVo.getKnowledgeId() != null) {
                //判断上级课程是否有知识点
                throw new ServiceException("上级章节已设置知识点ID，不能再添加子节点，请先删除上级章节的知识点ID");
            }
        }


        Course add = MapstructUtils.convert(bo, Course.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCourseId(add.getCourseId());
            changeSort(bo);
        }

        List<CourseGrade> courseGradeList = getCourseGradeList(add, bo.getApplicableGrade());
        if (CollectionUtil.isNotEmpty(courseGradeList) && !courseGradeService.saveBatch(courseGradeList)) {
            throw new ServiceException("保存课程信息失败");
        }

        //属性处理
        if (bo.getAttrAddJson() != null) {
            String trim = bo.getAttrAddJson().replace("{", "").replace("}", "").replace("[", "").replace("]", "").trim();
            if (StringUtils.isNotBlank(trim)) {
                String attrAddJson = bo.getAttrAddJson();
                List<RemoteAttributeRelationBo> attrList = null;
                try {
                    attrList = JSONUtil.toList(attrAddJson, RemoteAttributeRelationBo.class);
                } catch (Exception e) {
                    log.error("属性反序列化异常", e);
                    throw new ServiceException("属性反序列化异常");
                }
                if (attrList != null) {
                    attrList = attrList.stream().filter(e -> StringUtils.isNotBlank(e.getValue())).collect(Collectors.toList());
                }
                if (!CollUtil.isEmpty(attrList)) {
                    Optional<RemoteAttributeRelationBo> any = attrList.stream().filter(e -> Objects.isNull(e.getAttributeId())).findAny();
                    if (any.isPresent()) {
                        throw new ServiceException("属性id不能为空");
                    }
                    for (RemoteAttributeRelationBo remoteAttributeRelationBo : attrList) {
                        remoteAttributeRelationBo.setTypeId(add.getCourseId());
                        remoteAttributeRelationBo.setType(AttrGroupTypeEnum.COURSE.getType());
                    }
                    boolean b = remoteAttributeService.insertRelationBatch(attrList);
                    if (!b) {
                        throw new ServiceException("批量新增属性关系失败");
                    }
                }
            }
        }
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean addCatalog(CourseBo bo) {
        //添加章节
        CourseVo courseVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, bo.getCourseParentId()));
        checkCourseParent(courseVo);

        // 修改已存在节点的顺序
        Integer sort = sortCourse(bo.getCourseParentId(), bo.getSort(), 1);
        bo.setSort(sort);

        bo.setCourseType(courseVo.getCourseType() + 1);
        bo.setAncestors(courseVo.getAncestors() + "," + courseVo.getCourseId());
        Course course = MapstructUtils.convert(bo, Course.class);
        if (Objects.isNull(course)) {
            return false;
        }

        // 设置顶级课程id
        String[] split = course.getAncestors().split(",");
        if (split.length > 1) {
            course.setTopCourseId(Long.valueOf(split[1]));
        }
        return baseMapper.insert(course) > 0;
    }

    /**
     * @param courseParentId 插入节点id
     * @param sort           插入节点的位置，可为空
     * @param step           需要插入节点的数量
     * @return
     */
    private Integer sortCourse(Long courseParentId, Integer sort, Integer step) {
        if (Objects.isNull(sort)) {
            Course maxSortCourse = findMaxSortCourse(courseParentId);
            sort = maxSortCourse == null ? 1 : maxSortCourse.getSort() + 1;
        }

        List<Course> sortCourseList = findSortCourse(courseParentId, sort)
            .stream().peek(i -> i.setSort(i.getSort() + step)).toList();
        if (CollectionUtil.isNotEmpty(sortCourseList)) {
            baseMapper.updateBatchById(sortCourseList);
        }

        return sort;
    }

    @Override
    public Boolean editCatalog(CourseBo bo) {
        CourseVo courseVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, bo.getCourseId()));
        if (Objects.isNull(courseVo)) {
            throw new ServiceException("节点不存在");
        }
        courseVo.setCourseName(bo.getCourseName());
        Course course = MapstructUtils.convert(courseVo, Course.class);
        return baseMapper.updateById(course) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean associateCourse(CourseBo bo) {
        if (Objects.isNull(bo.getKnowledgeId())) {
            throw new ServiceException("关联外部课程知识点不能为空");
        }
        if (Objects.nonNull(bo.getCourseId())) {
            Course course = baseMapper.selectOne(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getCourseId, bo.getCourseId()));
            if (Objects.isNull(course)) {
                throw new ServiceException("节点不存在");
            }
            Long count = baseMapper.selectCount(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getCourseParentId, bo.getCourseId()));
            if (count > 0) {
                throw new ServiceException("节点存在子级，无法配置课次");
            }
            course.setKnowledgeId(bo.getKnowledgeId());
            return baseMapper.updateById(course) > 0;
        } else if (Objects.nonNull(bo.getCourseParentId()) && Objects.nonNull(bo.getCourseName())) {
            return addCatalog(bo);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchAssociateCourse(AssociateCourseBo bo) {
        CourseVo courseVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, bo.getCourseParentId()));
        checkCourseParent(courseVo);

        //章节层级判断，当前支持最大4层
        Integer currentLevel = courseVo.getAncestors().split(",").length - 1;
        for (CourseTreeNodeDTO node : bo.getTreeNodes()) {
            if (org.apache.commons.lang3.ObjectUtils.anyNull(node.getId(), node.getName(), node.getLeaf())) {
                throw new ServiceException("节点参数异常");
            }
            if ((currentLevel + calculateMaxLevel(node)) > 4) {
                throw new ServiceException("章节支持最大4层结构");
            }
        }

        //排序设置
        Integer sort = sortCourse(bo.getCourseParentId(), bo.getSort(), bo.getTreeNodes().size());

        //插入节点
        for (CourseTreeNodeDTO node : bo.getTreeNodes()) {
            Course course = MapstructUtils.convert(courseVo, Course.class);
            if (Objects.nonNull(course)) {
                insertCourseNode(course, sort++, node);
            }
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean moveCourse(MoveCourseBo bo) {
        CourseVo courseVo = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, bo.getCourseParentId()));
        if (Objects.isNull(courseVo)) {
            throw new ServiceException("父节点不存在");
        }

        // 校验移动后章节层级
        Integer currentLevel = courseVo.getAncestors().split(",").length - 1;
        for (CourseTreeNodeDTO node : bo.getTreeNodes()) {
            if ((currentLevel + calculateMaxLevel(node)) > 4) {
                throw new ServiceException("章节支持最大4层结构");
            }
        }

        //校验移动节点与原节点是否一致
        Map<Long, CourseVo> childrenMap = baseMapper.findCourseAncestors(courseVo.getCourseId())
            .stream().collect(Collectors.toMap(CourseVo::getCourseId, Function.identity()));
        if (childrenMap.isEmpty()) {
            throw new ServiceException("没有可移动的节点");
        }
        Set<Long> nodeIdSet = new HashSet<>();
        extractTreeNodeId(bo.getTreeNodes(), nodeIdSet);
        if (childrenMap.size() != nodeIdSet.size()) {
            throw new ServiceException("移动节点与存在节点不一致");
        }
        for (Long id : childrenMap.keySet()) {
            if (!nodeIdSet.contains(id)) {
                throw new ServiceException("移动节点与存在节点不一致");
            }
        }

        //移动节点
        moveTreeNode(courseVo, bo.getTreeNodes(), childrenMap);
        baseMapper.updateBatchById(childrenMap.values()
            .stream().map(i -> MapstructUtils.convert(i, Course.class)).toList());

        return true;
    }

    private void moveTreeNode(CourseVo parent, List<CourseTreeNodeDTO> treeNodes, Map<Long, CourseVo> courseVoMap) {
        int sort = 1;
        String ancestors = parent.getAncestors();
        for (CourseTreeNodeDTO node : treeNodes) {
            CourseVo vo = courseVoMap.get(node.getId());
            vo.setAncestors(ancestors + "," + parent.getCourseId());
            vo.setCourseParentId(parent.getCourseId());
            vo.setCourseType(parent.getCourseType() + 1);
            vo.setSort(sort++);
            if (CollUtil.isNotEmpty(node.getChildren())) {
                if (node.getLeaf() == 1) {
                    throw new ServiceException("该层级已配置知识点，不允许移动到该层级下面");
                }
                moveTreeNode(vo, node.getChildren(), courseVoMap);
            }
        }
    }

    private void extractTreeNodeId(List<CourseTreeNodeDTO> nodes, Set<Long> idSet) {
        for (CourseTreeNodeDTO node : nodes) {
            if (org.apache.commons.lang3.ObjectUtils.anyNull(node.getId(), node.getName(), node.getLeaf())) {
                throw new ServiceException("节点参数异常");
            }
            idSet.add(node.getId());
            if (CollectionUtil.isNotEmpty(node.getChildren())) {
                extractTreeNodeId(node.getChildren(), idSet);
            }
        }
    }

    private void insertCourseNode(Course parent, Integer sort, CourseTreeNodeDTO node) {
        Course course = new Course();
        course.setCourseParentId(parent.getCourseId());
        course.setAncestors(parent.getAncestors() + "," + parent.getCourseId());
        course.setCourseType(parent.getCourseType() + 1);
        course.setCourseName(node.getName());
        course.setSort(sort);
        if (node.getLeaf() == 1) {
            course.setKnowledgeId(node.getId());
        }
        // 设置顶级课程id
        String[] split = course.getAncestors().split(",");
        if (split.length > 1) {
            course.setTopCourseId(Long.valueOf(split[1]));
        }
        baseMapper.insert(course);

        if (CollUtil.isNotEmpty(node.getChildren())) {
            int subSort = 1;
            for (CourseTreeNodeDTO child : node.getChildren()) {
                insertCourseNode(course, subSort++, child);
            }
        }
    }

    private Integer calculateMaxLevel(CourseTreeNodeDTO node) {
        if (Objects.isNull(node)) {
            return 0;
        }
        if (node.getLeaf() == 1 || CollUtil.isEmpty(node.getChildren())) {
            return 1;
        }
        int maxLevel = 0;
        for (CourseTreeNodeDTO child : node.getChildren()) {
            // 取子节点最大的层级数
            maxLevel = Math.max(maxLevel, calculateMaxLevel(child));
        }
        return maxLevel + 1;
    }

    private void checkCourseParent(CourseVo courseVo) {
        if (Objects.isNull(courseVo)) {
            throw new ServiceException("父节点不存在");
        }
        if (Objects.nonNull(courseVo.getKnowledgeId())) {
            //判断上级课程是否有知识点
            throw new ServiceException("上级章节已设置知识点ID，不能再添加子节点，请先清除上级章节的知识点ID");
        }
        if (courseVo.getAncestors().split(",").length > 4) {
            //判断是否超过4层
            throw new ServiceException("章节支持最大4层结构");
        }
    }

    private List<Course> findSortCourse(Long courseParentId, Integer sort) {
        return baseMapper.selectList(Wrappers.<Course>lambdaQuery()
            .eq(Course::getCourseParentId, courseParentId)
            .ge(Course::getSort, sort)
            .eq(Course::getDelFlag, UserConstants.DEL_FLAG_NO));
    }

    private Course findMaxSortCourse(Long courseParentId) {
        return baseMapper.selectOne(Wrappers.<Course>lambdaQuery()
            .eq(Course::getCourseParentId, courseParentId)
            .eq(Course::getDelFlag, UserConstants.DEL_FLAG_NO)
            .orderByDesc(Course::getSort)
            .last("limit 1"));
    }

    /**
     * 生成课程年级记录
     *
     * @param course
     * @param applicableGrade
     * @return
     */
    private List<CourseGrade> getCourseGradeList(Course course, List<String> applicableGrade) {
        List<CourseGrade> courseGrades = new ArrayList<>();
        Set<String> gradeSet = new HashSet<>();
        if (StringUtils.isNotEmpty(course.getGrade())) {
            courseGrades.add(new CourseGrade(course.getCourseId(), course.getGrade()));
            gradeSet.add(course.getGrade());
        }
        if (CollectionUtil.isNotEmpty(applicableGrade)) {
            applicableGrade.forEach(grade -> {
                if (!gradeSet.contains(grade) && StringUtils.isNotBlank(grade)) {
                    gradeSet.add(grade);
                    courseGrades.add(new CourseGrade(course.getCourseId(), grade));
                }
            });
        }
        return courseGrades;
    }

    /**
     * 对课程排序
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean changeSort(CourseBo bo) {
        Long courseId = bo.getCourseId();
        Long courseParentId = bo.getCourseParentId();
        if (ObjectUtils.isEmpty(courseId) || ObjectUtils.isEmpty(courseParentId)) {
            return Boolean.FALSE;
        }
        Integer insertType = bo.getInsertSortType();
        Integer insertSortPosition = bo.getInsertSortPosition();
        Integer insertSortNum = bo.getInsertSortNum();
        if (ObjectUtils.isEmpty(insertType) || ObjectUtils.isEmpty(insertSortPosition)) {
            return Boolean.FALSE;

        }
        //只有课程排序

        CourseBo query = new CourseBo();
        query.setCourseParentId(courseParentId);
        query.setNotIncourseIdList(Collections.singletonList(courseId));
        query.setDelFlag(UserConstants.DEL_FLAG_NO);
        query.setOrderBy(true);
        List<CourseVo> courseVos = queryList(query);
        if (CollUtil.isEmpty(courseVos)) {
            return Boolean.FALSE;
        }

        if (insertType == 0) {
            insertSortNum = 1;
        } else if (insertType == 1) {
            insertSortNum = courseVos.size();
        } else {
            insertSortNum = checkInsertSortNum(insertSortNum, courseVos);
        }

        if (insertSortPosition == 0) {
            insertSortNum = insertSortNum - 1;
        }
        CourseVo courseVo = new CourseVo();
        courseVo.setCourseId(courseId);
        courseVos.add(insertSortNum, courseVo);
        int tempSort = 0;
        for (CourseVo vo : courseVos) {
            vo.setSort(tempSort);
            tempSort++;
        }
        List<Course> list = courseVos.stream().map(v -> {
            Course course = new Course();
            course.setCourseId(v.getCourseId());
            course.setSort(v.getSort());
            return course;
        }).toList();
//        List<Course> courses = list.subList(insertSortNum, list.size());
        //目前全量修改
        return baseMapper.updateBatchById(list);
    }

    private static Integer checkInsertSortNum(Integer insertSortNum, List<CourseVo> courseVos) {
        if (ObjectUtils.isEmpty(insertSortNum)) {
            return courseVos.size();
        }
        if (insertSortNum < 0) {
            return 0;
        }
        if (insertSortNum > courseVos.size()) {
            return courseVos.size();
        }
        return insertSortNum;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void insertBatchByBoList(List<CourseBo> boList) {
        List<Course> add = MapstructUtils.convert(boList, Course.class);
        for (Course course : add) {
            validEntityBeforeSave(course);
        }
        boolean b = baseMapper.insertBatch(add);
        if (b) {
            for (int i = 0; i < boList.size(); i++) {
                boList.get(i).setCourseId(add.get(i).getCourseId());
            }
        } else {
            throw new ServiceException("批量新增失败");
        }
        List<RemoteAttributeRelationBo> remoteAttributeRelationBos = new ArrayList<>();
        for (CourseBo courseBo : boList) {
            List<RemoteAttributeRelationBo> list = courseBo.getAttributeRelationList();
            if (list != null && !list.isEmpty()) {
                remoteAttributeRelationBos.addAll(list);
            }
        }
        if (!remoteAttributeRelationBos.isEmpty()) {
            boolean b1 = remoteAttributeService.insertRelationBatch(remoteAttributeRelationBos);
            if (!b1) {
                throw new ServiceException("批量新增属性关系失败");
            }
        }

    }

    /**
     * 修改课程（课程包含章节）
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void updateByBo(CourseBo bo) {
        Course update = MapstructUtils.convert(bo, Course.class);
        validEntityBeforeSave(update);
        Course course = baseMapper.selectById(bo.getCourseId());
        Course parentCourse = baseMapper.selectById(course.getCourseParentId());
        if (bo.getKnowledgeId() != null) {
            //判断上级课程是否有知识点
            if (!Objects.equals(course.getCourseParentId(), 0L)) {
                if (parentCourse != null && parentCourse.getKnowledgeId() != null) {
                    throw new ServiceException("上级课程已设置知识点ID，不能再设置，请先删除上级课程的知识点ID");
                }
            }
            //判断下级章节列表是否有知识点
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseId(bo.getCourseId());
            CourseChildInfoVo childInfo = getChildInfo(courseBo);
            if (childInfo != null && StringUtils.isNotBlank(childInfo.getHasKnowledgeIdCourseIds())) {
                throw new ServiceException("下级章节列表已设置知识点ID，不能再设置，请先删除下级章节列表的知识点ID");
            }
        }
        if (parentCourse != null) {
            course.setCourseType(parentCourse.getCourseType() + 1);
        }
        boolean b = baseMapper.updateById(update) > 0;
        if (!b) {
            throw new ServiceException("修改课程失败");
        }

        List<CourseGrade> courseGradeList = getCourseGradeList(update, bo.getApplicableGrade());
        if (!courseGradeService.updateCourseGrade(course, courseGradeList)) {
            throw new ServiceException("修改课程失败");
        }

        if (course.getKnowledgeId() != null && Boolean.TRUE.equals(bo.getKnowledgeIdSetNull())) {
            //删除课程知识点
            boolean b1 = baseMapper.update(null,
                Wrappers.<Course>lambdaUpdate()
                    .set(Course::getKnowledgeId, null)
                    .in(Course::getCourseId, bo.getCourseId())
            ) > 0;
            if (!b1) {
                throw new ServiceException("删除课程知识点失败");
            }

        }

        if (StringUtils.isNotBlank(bo.getAttrAddJson())) {
            String trim = bo.getAttrAddJson().replace("{", "").replace("}", "").replace("[", "").replace("]", "").trim();
            if (StringUtils.isNotBlank(trim)) {
                remoteAttributeService.updateTypeRelation(AttrGroupTypeEnum.COURSE.getType(), update.getCourseId(), bo.getAttrAddJson());
            }
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Course entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除课程（课程包含章节）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public CourseVo getOnceByBo(CourseBo courseBo) {
        if ("all".equals(courseBo.getGrade())) {
            courseBo.setGrade(null);
        }
        return baseMapper.selectVoOne(buildLambdaQueryWrapper(courseBo));
    }

    @Override
    public List<Tree<Long>> treeQuery(CourseBo bo) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        //先查最顶级课程
        bo.setCourseType(UserConstants.TOP_COURSE_TYPE);

        //如果是门店，查该门店下全部
        if (null != LoginHelper.getBranchId() || CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<RemoteBranchAuthTypeVo> remoteBranchAuthTypeVos = new ArrayList<>();
            if (null != LoginHelper.getBranchId()) {
                RemoteBranchAuthTypeVo authTypeByBranchId = remoteBranchAuthTypeService.getAuthTypeByBranchId(LoginHelper.getBranchId());
                if (authTypeByBranchId != null) {
                    remoteBranchAuthTypeVos.add(authTypeByBranchId);
                }
            } else {
                List<RemoteBranchAuthTypeVo> authTypeListByBranchIdList = remoteBranchAuthTypeService.getAuthTypeListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isNotEmpty(authTypeListByBranchIdList)) {
                    remoteBranchAuthTypeVos.addAll(authTypeListByBranchIdList);
                }
            }
            if (CollUtil.isNotEmpty(remoteBranchAuthTypeVos)) {
                List<Long> allCourseIdList = remoteBranchAuthTypeVos.stream()
                    .map(RemoteBranchAuthTypeVo::getCourseIds)
                    .filter(StringUtils::isNotBlank)
                    .flatMap(s -> Arrays.stream(s.split(",")))
                    .map(Long::parseLong)
                    .distinct()
                    .collect(Collectors.toList());

                bo.setCourseIdList(allCourseIdList);
            }

        }
        bo.setOrderBy(Boolean.TRUE);
        LambdaQueryWrapper<Course> lqw = buildLambdaQueryWrapper(bo);
        List<CourseVo> courseVos = baseMapper.selectVoList(lqw);
        if (bo.getWithAttr() != null && bo.getWithAttr()) {
            putAttr(courseVos);
            putCourseDetail(courseVos, false);
        }
        if (CollUtil.isEmpty(courseVos)) {
            return List.of();
        }
        List<Long> courseIdList = courseVos.stream().map(CourseVo::getCourseId).collect(Collectors.toList());

        //拿到最顶级的以后再去查所有下级的
        if (bo.getNeedChild()) {
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseIdList(courseIdList);
            List<CourseChildInfoVo> chindInfoList = getChildInfoList(courseBo);
            if (CollUtil.isNotEmpty(chindInfoList)) {
                //将所有子节点完整信息查出来
                List<Long> childIdList = chindInfoList.stream().filter(vo -> StringUtils.isNotBlank(vo.getChildIds())).map(vo -> vo.getChildIds().split(",")).flatMap(strings -> CollUtil.newArrayList(strings).stream()).map(Long::parseLong).collect(Collectors.toList());
                if (CollUtil.isEmpty(childIdList)) {
                    return buildTree(courseVos);
                }
                LambdaQueryWrapper<Course> query = Wrappers.lambdaQuery();
                query.in(Course::getCourseId, childIdList);
                query.eq(Course::getDelFlag, UserConstants.DEL_FLAG_NO);
                query.orderByAsc(Course::getSort);
                List<CourseVo> childList = baseMapper.selectVoList(query);
                //子节点放到跟父节点一个list里面
                courseVos.addAll(childList);
            }
        }
        return buildTree(courseVos);
    }

    @Override
    public List<Tree<Long>> queryTreeV2(Long courseId) {

        CourseBo courseBo = new CourseBo();
        //先查最顶级课程
        courseBo.setCourseType(UserConstants.TOP_COURSE_TYPE);
        courseBo.setOrderBy(Boolean.TRUE);
        courseBo.setCourseId(courseId);
        LambdaQueryWrapper<Course> lqw = buildLambdaQueryWrapper(courseBo);
        List<CourseVo> courseVos = baseMapper.selectVoList(lqw);

        if (CollUtil.isEmpty(courseVos)) {
            return new ArrayList<>();
        }

        List<CourseVo> children = baseMapper.findCourseAncestors(courseId);
        if (CollUtil.isNotEmpty(children)) {
            courseVos.addAll(children);
        }
        return buildTree(courseVos);
    }

    @Override
    public List<CourseChildInfoVo> getChildInfoList(CourseBo courseBo) {
        if ("all".equals(courseBo.getGrade())) {
            courseBo.setGrade(null);
        }
        courseBo.setOrderBy(Boolean.FALSE);
        QueryWrapper<Course> courseQueryWrapper = buildQueryWrapper(courseBo, false);

        //首先获取父级课程
        List<Course> courses = baseMapper.getQueryList(courseQueryWrapper);
        if (CollUtil.isEmpty(courses)) {
            return Lists.newArrayList();
        }

        LambdaQueryWrapper<Course> lqw = Wrappers.lambdaQuery();
        lqw.in(Course::getTopCourseId, courses.stream().map(Course::getCourseId).toList());
        lqw.eq(Course::getDelFlag, "0");
        List<Course> childrenList = baseMapper.selectList(lqw);
        Map<Long, List<Course>> childrenMap = getMapCourse(childrenList);
        return courses.stream().map(entity -> {
            CourseChildInfoVo courseChildInfoVo = new CourseChildInfoVo();
            courseChildInfoVo.setCourseId(entity.getCourseId());
            courseChildInfoVo.setCourseParentId(entity.getCourseParentId());
            List<Course> orDefault = childrenMap.getOrDefault(entity.getCourseId(), List.of());
            if (CollUtil.isNotEmpty(orDefault)) {
                String childIds = orDefault
                    .stream()
                    .map(Course::getCourseId)
                    .map(this::getStringValue)
                    .collect(Collectors.joining(","));
                courseChildInfoVo.setChildIds(childIds);
                String KnowledgeIdCourseIds = orDefault
                    .stream()
                    .filter(v -> v.getKnowledgeId() != null)
                    .map(Course::getCourseId)
                    .map(this::getStringValue)
                    .collect(Collectors.joining(","));
                courseChildInfoVo.setHasKnowledgeIdCourseIds(KnowledgeIdCourseIds);
            }
            return courseChildInfoVo;
        }).toList();
    }

    private String getStringValue(Long aLong) {
        if (ObjectUtils.isEmpty(aLong)) {
            return "";
        }
        return String.valueOf(aLong);
    }

    private Map<Long, List<Course>> getMapCourse(List<Course> childrenList) {
        if (CollUtil.isEmpty(childrenList)) {
            return Maps.newHashMap();
        }
        return childrenList.stream().collect(Collectors.groupingBy(Course::getTopCourseId));
    }

    @Override
    public CourseChildInfoVo getChildInfo(CourseBo courseBo) {
        if ("all".equals(courseBo.getGrade())) {
            courseBo.setGrade(null);
        }
        List<CourseChildInfoVo> childInfoList = getChildInfoList(courseBo);
        if (CollUtil.isEmpty(childInfoList)) {
            return null;
        }
        return childInfoList.get(0);
    }

    @Override
    public List<Tree<Long>> buildTree(List<CourseVo> courseVoList) {
        if (CollUtil.isEmpty(courseVoList)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.build(courseVoList, (courseVo, node) ->
                node.setId(courseVo.getCourseId())
                    .setParentId(courseVo.getCourseParentId())
                    .setName(courseVo.getCourseName())
                    .setWeight(courseVo.getCourseType())
                    .putExtra("self", courseVo)
            , "courseParentId");
    }

    @Override
    public List<Tree<Long>> buildTreeV2(List<CourseVo> courseVoList, Boolean withDetail) {
        if (CollectionUtils.isEmpty(courseVoList)) {
            return new ArrayList<>();
        }
        return TreeBuildUtils.build(courseVoList, (courseVo, node) -> {
            node.setId(courseVo.getCourseId())
                .setParentId(courseVo.getCourseParentId())
                .setName(courseVo.getCourseName())
                .setWeight(courseVo.getCourseType());
            if (Boolean.TRUE.equals(withDetail)) {
                node.putExtra("self", courseVo);
            }
        }, "courseParentId");
    }

    @Override
    public void putTopmostCourseInfo(List<CourseVo> courseVoList, Boolean withAttr) {
        putTopmostCourseInfo(courseVoList, withAttr, false);
    }

    @Override
    public Map<Long, List<Tree<Long>>> queryCourseTreePath(List<CourseVo> courseVoList) {
        Map<Long, List<Tree<Long>>> treeMap = new HashMap<>();
        if (CollUtil.isEmpty(courseVoList)) {
            return treeMap;
        }

        //通过祖先节点拿到最顶层，已知0为所有的最顶级，第二个就是祖先节点的第一个
        Set<Long> ancestorSet = new HashSet<>();
        Map<Long, List<CourseVo>> courseMap = new HashMap<>();
        for (CourseVo courseVo : courseVoList) {
            String ancestors = courseVo.getAncestors();
            if (StringUtils.isNotBlank(ancestors)) {
                String[] split = ancestors.split(",");
                //保持非0的祖先节点
                Arrays.stream(split).forEach(i -> {
                    if (!"0".equals(i)) {
                        ancestorSet.add(Long.parseLong(i));
                    }
                });
                // 获取顶级课程ID（如果存在）
                Long topCourseId = split.length > 1 ? Long.parseLong(split[1]) : courseVo.getCourseId();
                // 获取或创建对应的课程列表
                List<CourseVo> courseVos = courseMap.computeIfAbsent(topCourseId, k -> new ArrayList<>());
                // 将当前课程添加到列表中
                courseVos.add(courseVo);
            }
        }
        Set<Long> topCourseIdSet = courseMap.keySet();
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(new ArrayList<>(topCourseIdSet));
        List<CourseVo> courseVos = queryList(courseBo);

        if (CollUtil.isEmpty(courseVos)) {
            return treeMap;
        }

        //排除顶级课程节点，只保留章节
        final Map<Long, CourseVo> courseCatalogMap;
        ancestorSet.removeAll(topCourseIdSet);
        if (!ancestorSet.isEmpty()) {
            courseBo.setCourseIdList(new ArrayList<>(ancestorSet));
            courseCatalogMap = queryList(courseBo).stream().collect(
                Collectors.toMap(CourseVo::getCourseId, Function.identity())
            );
        } else {
            courseCatalogMap = new HashMap<>();
        }

        for (CourseVo courseVo : courseVos) {
            Long courseId = courseVo.getCourseId();
            List<CourseVo> voList = courseMap.get(courseId);
            if (CollUtil.isNotEmpty(voList)) {
                voList.forEach(item ->
                    treeMap.put(item.getCourseId(), buildCourseTreePath(item, courseVo, courseCatalogMap)));
            }
        }

        return treeMap;
    }

    @Override
    public void putTopmostCourseInfo(List<CourseVo> courseVoList, Boolean withAttr, Boolean needGradeName) {
        if (CollUtil.isEmpty(courseVoList)) {
            return;
        }
        //通过祖先节点拿到最顶层，已知0为所有的最顶级，第二个就是祖先节点的第一个
        Map<Long, List<CourseVo>> map = new HashMap<>();
        for (CourseVo courseVo : courseVoList) {
            String ancestors = courseVo.getAncestors();
            if (StringUtils.isNotBlank(ancestors)) {
                String[] split = ancestors.split(",");
                if (split.length > 1) {
                    Long topmostCourseId = Long.parseLong(split[1]);
                    List<CourseVo> courseVos = map.getOrDefault(topmostCourseId, new ArrayList<>());
                    courseVos.add(courseVo);
                    map.put(topmostCourseId, courseVos);
                } else {
                    //自己就是最顶级
                    List<CourseVo> courseVos = map.getOrDefault(courseVo.getCourseId(), new ArrayList<>());
                    courseVos.add(courseVo);
                    map.put(courseVo.getCourseId(), courseVos);
                }
            }
        }
        Set<Long> topmostCourseIdList = map.keySet();
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(new ArrayList<>(topmostCourseIdList));
        List<CourseVo> courseVos = queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            return;
        }
        if (Boolean.TRUE.equals(withAttr)) {
            putAttr(courseVos);
        }
        if (Boolean.TRUE.equals(needGradeName)) {
            putGradeName(courseVos);
        }

        for (CourseVo courseVo : courseVos) {
            Long courseId = courseVo.getCourseId();
            List<CourseVo> voList = map.get(courseId);
            if (CollUtil.isNotEmpty(voList)) {
                voList.forEach(item -> item.setTopmostCourse(courseVo));
            }
        }
    }

    private List<Tree<Long>> buildCourseTreePath(CourseVo item, CourseVo topCourse, Map<Long, CourseVo> courseCatalogMap) {
        List<Tree<Long>> treeList = new ArrayList<>();
        String ancestors = item.getAncestors();
        if (StringUtils.isBlank(ancestors)) {
            return treeList;
        }
        List<CourseVo> list = new ArrayList<>();
        list.add(topCourse);
        Arrays.stream(ancestors.split(",")).forEach(i -> {
                CourseVo courseVo = courseCatalogMap.get(Long.parseLong(i));
                if (Objects.nonNull(courseVo)) {
                    list.add(courseVo);
                }
            }
        );
        list.add(item);
        return buildTreeV2(list, Boolean.FALSE);
    }

    @Override
    public void putCourseWithAttrInfo(List<CourseVo> courseVos) {
        putAttr(courseVos);
    }

    @Override
    public RemoteKnowledgeResourceVo getCourseResourceByCourseId(Long courseId, KnowledgeResourceType type) {
        Course course = baseMapper.selectById(courseId);
        if (course == null || course.getKnowledgeId() == null) {
            return null;
        }
        //获取课程资源
        RemoteGroupResourceVo knowledgeResource = remoteResourceService.getKnowledgeResourceById(course.getKnowledgeId(), type.getType());

        return knowledgeResource == null ? null : knowledgeResource.getKnowledgeResource();
    }


    @Override
    public List<RemoteQuestionVo> getKnowledgeQuestionList(Long courseId, KnowledgeResourceType resourceType, Boolean withAnalysis, Boolean withAnswer, Boolean filterInvalidQuestion) {
        if (!resourceType.getIsQuestionType()) {
            throw new ServiceException("不支持" + resourceType.getDescription());
        }

        CourseVo courseVo = queryById(courseId, Boolean.FALSE);
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //获取知识点
        Long knowledgeId = courseVo.getKnowledgeId();
        if (knowledgeId == null) {
            return null;
        }
        RemoteQuestionBo remoteQuestionBo = new RemoteQuestionBo();
        remoteQuestionBo.setKnowledgeId(knowledgeId);
        remoteQuestionBo.setType(resourceType.getType());
        List<RemoteQuestionVo> knowledgeQuestionList = remoteQuestionService.getKnowledgeQuestionList(remoteQuestionBo);
        if (CollUtil.isEmpty(knowledgeQuestionList)) {
            return List.of();
        }
        putQuestionNo(knowledgeQuestionList);
        knowledgeQuestionList = filterInvalidQuestion(knowledgeQuestionList);
        hideInfo(knowledgeQuestionList, withAnalysis, withAnswer);
        return knowledgeQuestionList;
    }

    /**
     * 筛选无效问题，过滤掉children不为空和没有答案的题目，并展开问题树
     *
     * @param knowledgeQuestionList 知识问题列表
     * @date 2024/05/15 02:19:48
     */
    private List<RemoteQuestionVo> filterInvalidQuestion(List<RemoteQuestionVo> knowledgeQuestionList) {
        if (CollUtil.isEmpty(knowledgeQuestionList)) {
            return List.of();
        }
        List<RemoteQuestionVo> flattened = new ArrayList<>();
        flattenQuestionsRecursive(knowledgeQuestionList, flattened);
        //过滤掉children不为空和没有答案的题目
        return flattened.stream().filter(vo -> CollUtil.isEmpty(vo.getChildren()) && vo.getAnswer() != null).collect(Collectors.toList());

    }

    /**
     * 展开问题树
     *
     * @param questions 问题
     * @param flattened 压扁
     * @date 2024/05/15 02:19:57
     */
    private static void flattenQuestionsRecursive(List<RemoteQuestionVo> questions, List<RemoteQuestionVo> flattened) {
        for (RemoteQuestionVo question : questions) {
            flattened.add(question); // 添加当前节点到扁平列表中
            List<RemoteQuestionVo> children = question.getChildren();
            if (children != null && !children.isEmpty()) {
                // 递归处理子节点
                flattenQuestionsRecursive(children, flattened);
            }
        }
    }

    @Override
    public CourseVo getAiChapterResource(Long courseId, Long studentId) {
        CourseVo courseVo = queryById(courseId, Boolean.FALSE);
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //获取章节的顶级课程信息
        putTopmostCourseInfo(List.of(courseVo), true);
        if (courseVo.getTopmostCourse() != null) {
            putCourseDetail(List.of(courseVo.getTopmostCourse()), false);
        }
        //获取知识点
        Long knowledgeId = courseVo.getKnowledgeId();
        if (knowledgeId == null) {
            return courseVo;
        }

        //获取课程资源
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setTypeList(List.of(KnowledgeResourceType.HANDOUT.getType(), KnowledgeResourceType.TEST.getType(), KnowledgeResourceType.PRACTICE.getType()));
        List<RemoteGroupResourceVo> knowledgeResourceList = remoteResourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);

        if (CollUtil.isNotEmpty(knowledgeResourceList)) {
            for (RemoteGroupResourceVo remoteGroupResourceVo : knowledgeResourceList) {
                KnowledgeResourceType type = remoteGroupResourceVo.getType();
                switch (type) {
                    case HANDOUT:
                        courseVo.setHandout(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    case TEST:
                        courseVo.setTest(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    case PRACTICE:
                        courseVo.setPractice(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    default:
                        log.error("未知的资源类型");
                        break;
                }
            }
        }
        putResourceCompletionStatus(courseVo, LoginHelper.getStudentId(), courseId);


        return courseVo;
    }

    private List<Course> getSpecialTopicCourseList(CourseBo courseBo) {
        //获取所有顶级课程
        courseBo.setCourseType(UserConstants.TOP_COURSE_TYPE);
        //查询是否有分配AI课程，没有则查该门店下全部
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(LoginHelper.getStudentId());
        StudentVo studentVo = studentService.queryById(studentBo);
        if (null != studentVo.getStudentAiCourseRecordId()) {
            StudentAiCourseRecordInfoBo studentAiCourseRecordInfoBo = new StudentAiCourseRecordInfoBo();
            studentAiCourseRecordInfoBo.setStudentAiCourseRecordId(studentVo.getStudentAiCourseRecordId());
            List<StudentAiCourseRecordInfoVo> infoList = studentAiCourseRecordInfoService.queryList(studentAiCourseRecordInfoBo);
            List<Long> courseIdList = infoList.stream().map(StudentAiCourseRecordInfoVo::getCourseId).collect(Collectors.toList());

            courseBo.setCourseIdList(courseIdList);
        } else {
                RemoteDeptVo preDeptByDeptVo = remoteDeptService.getPreDeptByDeptId(studentVo.getCreateDept());
                // 根据这个deptId查询到关联的模板ID
                RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
                remoteStudentProductTemplateAuthBo.setDeptId(preDeptByDeptVo.getDeptId());//代理商ID
                remoteStudentProductTemplateAuthBo.setTemplateType(1);//课程模板查询
                courseBo.setCourseIdList(remoteOrderService.queryAuthCourseIds(remoteStudentProductTemplateAuthBo));
        }
        LambdaQueryWrapper<Course> courseLambdaQueryWrapper = buildLambdaQueryWrapper(courseBo);
        return baseMapper.selectList(courseLambdaQueryWrapper);
    }

    @Override
    public List<SpecialTopicCategoryVo> getSpecialTopicCategory(CourseBo courseBo, Long studentId, Long branchId) {
        if ("all".equals(courseBo.getGrade())) {
            courseBo.setGrade(null);
        }
        //获取专题分类
        List<RemoteDictDataVo> courseSpecialTopic = remoteDictService.selectDictDataByType("course_special_topic");
        if (CollUtil.isEmpty(courseSpecialTopic)) {
            return List.of();
        }

        List<SpecialTopicCategoryVo> resList = new ArrayList<>();
        for (RemoteDictDataVo remoteDictDataVo : courseSpecialTopic) {
            SpecialTopicCategoryVo specialTopicCategoryVo = new SpecialTopicCategoryVo();
            specialTopicCategoryVo.setSpecialTopic(remoteDictDataVo.getDictValue());
            resList.add(specialTopicCategoryVo);
        }

        List<Course> courseList = getSpecialTopicCourseList(courseBo);
        if (CollUtil.isEmpty(courseList)) {
            return resList;
        }

        //过滤掉没有专题的课程
        courseList = courseList.stream().filter(course -> StringUtils.isNotBlank(course.getSpecialTopic())).collect(Collectors.toList());
        if (CollUtil.isEmpty(courseList)) {
            return resList;
        }

        //获取所有有知识点的子章节id
        List<Long> courseIdList = courseList.stream().map(Course::getCourseId).collect(Collectors.toList());
        List<CourseVo> courseChildren = baseMapper.findCourseChildren(courseIdList).stream().filter(
            i -> Objects.nonNull(i.getKnowledgeId())).toList();
        if (CollUtil.isEmpty(courseChildren)) {
            return resList;
        }
        Map<Long, CourseVo> courseChildrenMap = courseChildren.stream()
            .collect(Collectors.toMap(CourseVo::getCourseId, Function.identity()));
        if (courseChildrenMap.isEmpty()) {
            return resList;
        }
        Map<Long, List<CourseVo>> topCourseVoMap = courseChildren.stream()
            .collect(Collectors.groupingBy(CourseVo::getTopCourseId));

        AiStudyVideoRecordBo aiStudyVideoRecordBo = new AiStudyVideoRecordBo();
        aiStudyVideoRecordBo.setStudentId(studentId);
        aiStudyVideoRecordBo.setCourseIdList(new ArrayList<>(courseChildrenMap.keySet()));
        Map<Long, List<AiStudyVideoRecordVo>> aiStudyVideoRecordMap = aiStudyVideoRecordService.queryList(aiStudyVideoRecordBo)
            .stream().filter(v->!ObjectUtils.isEmpty(v.getCourseId()))
            .collect(Collectors.groupingBy(AiStudyVideoRecordVo::getCourseId));

        //按照专题分类课程
        Map<String, List<Course>> groupedBySpecialTopic = StreamUtils.groupByKey(courseList, Course::getSpecialTopic);

        for (SpecialTopicCategoryVo specialTopicCategoryVo : resList) {
            String specialTopic = specialTopicCategoryVo.getSpecialTopic();
            List<Course> courseListBySpecialTopic = groupedBySpecialTopic.get(specialTopic);
            List<SpecialTopicCategoryItemVo> specialTopicCategoryItemList = new ArrayList<>();
            if (CollUtil.isNotEmpty(courseListBySpecialTopic)) {
                for (Course course : courseListBySpecialTopic) {
                    SpecialTopicCategoryItemVo specialTopicCategoryItemVo = new SpecialTopicCategoryItemVo();
                    specialTopicCategoryItemVo.setCourseName(course.getCourseName());
                    List<CourseVo> children = topCourseVoMap.get(course.getCourseId());
                    if (CollUtil.isEmpty(children)) {
                        specialTopicCategoryItemVo.setChapterCount(0L);
                        continue;
                    }
                    //课程小于等于0的不展示
                    specialTopicCategoryItemVo.setChapterCount((long) children.size());

                    AtomicReference<Long> chapterCompletedCount = new AtomicReference<>(0L);
                    children.forEach(item -> {
                       List<AiStudyVideoRecordVo> aiStudyVideoRecordVo = aiStudyVideoRecordMap.get(item.getCourseId());
                       if (CollUtil.isEmpty(aiStudyVideoRecordVo)) {
                           return;
                       }
                        for (AiStudyVideoRecordVo studyVideoRecordVo : aiStudyVideoRecordVo) {
                            Long studyVideoDuration = studyVideoRecordVo.getStudyVideoDuration();
                            if (Objects.nonNull(studyVideoDuration)
                                && studyVideoDuration >= studyVideoRecordVo.getDuration()) {
                                chapterCompletedCount.getAndSet(chapterCompletedCount.get() + 1);
                                return;
                            }
                        }
                    });
                    specialTopicCategoryItemVo.setChapterCompletedCount(chapterCompletedCount.get());
                    specialTopicCategoryItemVo.setCourseId(course.getCourseId());
                    specialTopicCategoryItemList.add(specialTopicCategoryItemVo);
                }
            }
            specialTopicCategoryVo.setSpecialTopicCategoryItemList(specialTopicCategoryItemList);
        }
        return resList.stream().filter(v -> CollUtil.isNotEmpty(v.getSpecialTopicCategoryItemList())).toList();
    }

    private List<CourseVo> sortChildren(Long parentCourseId, List<CourseVo> childrenList) {
        CourseVo parentCourse = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, parentCourseId));
        if (Objects.isNull(parentCourse)) {
            return childrenList;
        }
        List<CourseVo> sortList = new ArrayList<>();
        childrenList.add(0, parentCourse);
        List<Tree<Long>> trees = buildTree(childrenList);
        recurseSortChildren(sortList, trees.get(0).getChildren());
        return sortList;
    }

    private void recurseSortChildren(List<CourseVo> childrenList, List<Tree<Long>> trees) {
        for (Tree<Long> tree : trees) {
            Object self = tree.get("self");
            try {
                if (Objects.isNull(self)) {
                    continue;
                }
                CourseVo vo = JSON.parseObject(JSONObject.toJSONString(self), CourseVo.class);
                childrenList.add(vo);
                if (CollUtil.isNotEmpty(tree.getChildren())) {
                    recurseSortChildren(childrenList, tree.getChildren());
                }
            } catch (Exception e) {
                log.error("节点解析异常{}", tree, e);
            }
        }
    }

    @Override
    public List<CourseVo> getAiChapterAndLearnInfo(Long courseId, Long studentId) {
        //查询子章节信息
        List<CourseVo> childrenList = baseMapper.findCourseAncestors(courseId);
        if (CollUtil.isEmpty(childrenList)) {
            return Collections.emptyList();
        }
        childrenList = sortChildren(courseId, childrenList);

        //查询有知识点的子章节
        List<CourseVo> knowledgeCourseList = childrenList.stream().filter(i -> Objects.nonNull(i.getKnowledgeId())).toList();
        if (CollUtil.isEmpty(knowledgeCourseList)) {
            return Collections.emptyList();
        }

        //查ai学习记录
        AiStudyRecordBo aiStudyRecordBo = new AiStudyRecordBo();
        aiStudyRecordBo.setStudentId(studentId);
        aiStudyRecordBo.setCourseIdList(knowledgeCourseList.stream().map(CourseVo::getCourseId).collect(Collectors.toList()));
        List<AiStudyRecordVo> aiStudyRecordVos = aiStudyRecordService.queryList(aiStudyRecordBo);
        //按照课程分组转为map
        Map<Long, AiStudyRecordVo> aiStudyRecordMap = StreamUtils.toMap(aiStudyRecordVos, AiStudyRecordVo::getCourseId, Function.identity());


        //获取所有章节的知识点id关联的视频
        List<Long> knowledgeIdList = knowledgeCourseList.stream().map(CourseVo::getKnowledgeId).collect(Collectors.toList());

        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        //按照知识点id分组
        Map<Long, RemoteKnowledgeVideoVo> remoteKnowledgeVideoVoMap = StreamUtils.toMap(knowledgeVideoList, RemoteKnowledgeVideoVo::getKnowledgeId, Function.identity());

        //组装数据
        for (CourseVo chapter : knowledgeCourseList) {
            Long knowledgeId = chapter.getKnowledgeId();
            Long chapterCourseId = chapter.getCourseId();

            RemoteKnowledgeVideoVo remoteKnowledgeVideoVo = remoteKnowledgeVideoVoMap.get(knowledgeId);
            chapter.setRemoteKnowledgeVideoVo(remoteKnowledgeVideoVo);

            AiStudyRecordVo aiStudyRecordVo = aiStudyRecordMap.get(chapterCourseId);
            chapter.setAiStudyRecord(aiStudyRecordVo);

            if (aiStudyRecordVo != null) {
                chapter.setTestStatus(UserConstants.CORRECTION_STATUS_DONE.equals(aiStudyRecordVo.getTestState()));
                chapter.setPracticeStatus(UserConstants.CORRECTION_STATUS_DONE.equals(aiStudyRecordVo.getPracticeState()));
            } else {
                chapter.setTestStatus(false);
                chapter.setPracticeStatus(false);
            }
        }
        return knowledgeCourseList;
    }

    @Override
    public List<Tree<Long>> getAiCatalogAndLearnInfo(Long courseId, Long studentId) {
        CourseVo parentCourse = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
            .eq(Course::getCourseId, courseId));
        if (Objects.isNull(parentCourse)) {
            return Collections.emptyList();
        }
        //查询子章节信息
        List<CourseVo> childrenList = baseMapper.findCourseAncestors(courseId);
        if (CollUtil.isEmpty(childrenList)) {
            return Collections.emptyList();
        }

        childrenList.add(0, parentCourse);
        List<Tree<Long>> trees = buildTree(childrenList);
        List<CourseVo> leafCourseVos = childrenList.stream().filter(i -> Objects.nonNull(i.getKnowledgeId())).toList();
        if (CollUtil.isEmpty(leafCourseVos)) {
            return trees;
        }
        List<Long> leafIds = leafCourseVos.stream().map(CourseVo::getCourseId).toList();

        AiStudyRecordBo aiStudyRecordBo = new AiStudyRecordBo();
        aiStudyRecordBo.setStudentId(studentId);
        aiStudyRecordBo.setCourseIdList(leafIds);
        List<AiStudyRecordVo> aiStudyRecordVos = aiStudyRecordService.queryList(aiStudyRecordBo);
        //按照课程分组转为map
        Map<Long, AiStudyRecordVo> aiStudyRecordMap = StreamUtils.toMap(aiStudyRecordVos, AiStudyRecordVo::getCourseId, Function.identity());

        //获取所有章节的知识点id关联的视频
        List<Long> knowledgeIdList = leafCourseVos.stream().map(CourseVo::getKnowledgeId).collect(Collectors.toList());

        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        //按照知识点id分组
        Map<Long, RemoteKnowledgeVideoVo> remoteKnowledgeVideoVoMap = StreamUtils.toMap(knowledgeVideoList, RemoteKnowledgeVideoVo::getKnowledgeId, Function.identity());

        //组装数据
        for (CourseVo courseVo : leafCourseVos) {
            Long knowledgeId = courseVo.getKnowledgeId();
            Long chapterCourseId = courseVo.getCourseId();

            RemoteKnowledgeVideoVo remoteKnowledgeVideoVo = remoteKnowledgeVideoVoMap.get(knowledgeId);
            courseVo.setRemoteKnowledgeVideoVo(remoteKnowledgeVideoVo);

            AiStudyRecordVo aiStudyRecordVo = aiStudyRecordMap.get(chapterCourseId);
            courseVo.setAiStudyRecord(aiStudyRecordVo);

            courseVo.setCatalogCount(1L);
            courseVo.setCatalogCompletedCount(putCatalogCompletedCount(remoteKnowledgeVideoVo, aiStudyRecordVo));

            if (aiStudyRecordVo != null) {
                courseVo.setTestStatus(UserConstants.CORRECTION_STATUS_DONE.equals(aiStudyRecordVo.getTestState()));
                courseVo.setPracticeStatus(UserConstants.CORRECTION_STATUS_DONE.equals(aiStudyRecordVo.getPracticeState()));
            } else {
                courseVo.setTestStatus(false);
                courseVo.setPracticeStatus(false);
            }
        }

        for (Tree<Long> tree : trees) {
            calculateCatalogCount(tree);
        }
        return trees;
    }

    private void calculateCatalogCount(Tree<Long> node) {
        if (Objects.isNull(node) || CollUtil.isEmpty(node.getChildren())) {
            return;
        }
        try {
            long catalogCount = 0;
            long catalogCompletedCount = 0;
            for (Tree<Long> child : node.getChildren()) {
                calculateCatalogCount(child);
                Object self = child.get("self");
                CourseVo sub = JSON.parseObject(JSONObject.toJSONString(self), CourseVo.class);
                catalogCount += Optional.ofNullable(sub).map(CourseVo::getCatalogCount).orElse(0L);
                catalogCompletedCount += Optional.ofNullable(sub).map(CourseVo::getCatalogCompletedCount).orElse(0L);
            }
            Object self = node.get("self");
            CourseVo courseVo = JSON.parseObject(JSONObject.toJSONString(self), CourseVo.class);
            courseVo.setCatalogCount(catalogCount);
            courseVo.setCatalogCompletedCount(catalogCompletedCount);
            node.putExtra("self", courseVo);
        } catch (Exception e) {
            log.error("课次完成情况统计节点异常{}", node, e);
        }
    }


    private long putCatalogCompletedCount(RemoteKnowledgeVideoVo videoVo, AiStudyRecordVo recordVo) {
        Long recordDuration = Optional.ofNullable(recordVo).map(AiStudyRecordVo::getStudyVideoTotalDuration).orElse(0L);
        Long videoDuration = Optional.ofNullable(videoVo).map(RemoteKnowledgeVideoVo::getVideoTotalDuration).orElse(0L);
        if (videoDuration == 0) {
            return 0;
        }
        return recordDuration >= videoDuration ? 1 : 0;
    }

    private void putResourceCompletionStatus(CourseVo courseVo, Long studentId, Long courseId) {
        if (courseVo == null) {
            return;
        }

        //获取视频
        RemoteKnowledgeVideoVo knowledgeVideo = remoteExtVideoService.getKnowledgeVideoListById(courseVo.getKnowledgeId());
        courseVo.setRemoteKnowledgeVideoVo(knowledgeVideo);

        //获取学习记录
        //查总视频记录

        //查询学习记录数据
        AiStudyRecordBo aiStudyRecordBo = new AiStudyRecordBo();
        aiStudyRecordBo.setCourseId(courseId);
        aiStudyRecordBo.setStudentId(studentId);
        AiStudyRecordVo aiStudyRecordVo = aiStudyRecordService.queryOnce(aiStudyRecordBo);
        aiStudyRecordVo = aiStudyRecordVo == null ? new AiStudyRecordVo() : aiStudyRecordVo;
        RemoteKnowledgeResourceVo handout = courseVo.getHandout();
        RemoteKnowledgeResourceVo test = courseVo.getTest();
        RemoteKnowledgeResourceVo practice = courseVo.getPractice();


        if (aiStudyRecordVo != null && knowledgeVideo != null && CollUtil.isNotEmpty(knowledgeVideo.getVideoList())) {
            knowledgeVideo.getVideoList().get(0).setTotalStudyDuration(aiStudyRecordVo.getStudyVideoTotalDuration());
            //视频记录是按照天来记的，所以只需要查最后一天的即可
            AiStudyVideoRecordVo aiStudyVideoRecordVo = aiStudyVideoRecordService.queryLastDayRecord(studentId, courseId);
            if (aiStudyVideoRecordVo != null && StringUtils.isNotBlank(aiStudyVideoRecordVo.getStudyVideoSlices())) {
                String[] split = aiStudyVideoRecordVo.getStudyVideoSlices().split(",");
                knowledgeVideo.getVideoList().get(0).setLastDayStudyVideoSlices(split[split.length - 1]);
            }
        }


        if (handout != null) {
            //nothing
        }
        if (test != null) {
            String testState = aiStudyRecordVo.getTestState();
            test.setCompletionStatus(CorrectionStatusEnum.CORRECTED.getCode().equals(testState));
        }
        if (practice != null) {
            String practiceState = aiStudyRecordVo.getPracticeState();
            practice.setCompletionStatus(CorrectionStatusEnum.CORRECTED.getCode().equals(practiceState));
        }
        courseVo.setAiStudyRecord(aiStudyRecordVo);
    }

    private void hideInfo(List<RemoteQuestionVo> knowledgeQuestionList, Boolean withAnalysis, Boolean withAnswer) {
        if (CollUtil.isEmpty(knowledgeQuestionList)) {
            return;
        }
        for (RemoteQuestionVo remoteQuestionVo : knowledgeQuestionList) {
            if (Boolean.TRUE.equals(withAnalysis)) {
                remoteQuestionVo.setAnalysis(null);
            }
            if (Boolean.TRUE.equals(withAnswer)) {
                remoteQuestionVo.setAnswer(null);
            }
        }
    }

    private void putQuestionNo(List<RemoteQuestionVo> remoteQuestionVos) {
        putQuestionNo(remoteQuestionVos, null);
    }

    private void putQuestionNo(List<RemoteQuestionVo> remoteQuestionVos, String parentQuestionNo) {
        if (CollUtil.isEmpty(remoteQuestionVos)) {
            return;
        }
        //按顺序添加序号
        for (int i = 0; i < remoteQuestionVos.size(); i++) {
            String questionNo = "";
            if (StringUtils.isNotBlank(parentQuestionNo)) {
                questionNo = parentQuestionNo + "." + (i + 1);
            } else {
                questionNo = i + 1 + "";
            }
            RemoteQuestionVo remoteQuestionVo = remoteQuestionVos.get(i);
            remoteQuestionVo.setQuestionNo(questionNo);
            if (CollUtil.isNotEmpty(remoteQuestionVo.getChildren())) {
                putQuestionNo(remoteQuestionVo.getChildren(), questionNo);
            }

        }
    }


    /**
     * 放置属性
     *
     * @param records 记录
     * @date 2024/04/09 05:12:07
     */
    public void putAttr(List<CourseVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> courseIdList = records.stream().map(CourseVo::getCourseId).collect(Collectors.toList());
        List<RemoteAttrRelationResultVo> attrWithValueByGroupType = remoteAttributeService.getAttrWithValueByGroupType(courseIdList, AttrGroupTypeEnum.COURSE.getType(), -1L, true);
        if (CollUtil.isEmpty(attrWithValueByGroupType)) {
            return;
        }
        Map<Long, RemoteAttrRelationResultVo> map = StreamUtils.toMap(attrWithValueByGroupType, RemoteAttrRelationResultVo::getTypeId, Function.identity());
        for (CourseVo record : records) {
            record.setAttrRelationResult(map.get(record.getCourseId()));
        }
    }


    /**
     * 放置年级名称
     *
     * @param records
     */
    private void putGradeName(List<CourseVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Map<String, String> courseGradeMap = dictService.getAllDictByDictType("course_grade");
        records.forEach(course -> course.setGradeName(courseGradeMap.getOrDefault(course.getGrade(), null)));
    }

    /**
     * 输入课程详细信息，规则：这里假设分隔符为 |    那么就是  学科|学段|年级|动态属性
     *
     * @param records 记录
     * @date 2024/04/09 05:12:03
     */
    @Override
    public void putCourseDetail(List<CourseVo> records, Boolean lastConcatCourseName) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        String separator = remoteConfigService.selectCourseDetailSeparator();

        for (CourseVo course : records) {
            List<String> infoList = new ArrayList<>();

            if (StringUtils.isNotBlank(course.getStage())) {
                String courseStage = dictService.getDictLabel("course_stage", course.getStage());
                infoList.add(courseStage);
                course.setStageName(courseStage);
            }
            if (StringUtils.isNotBlank(course.getGrade())) {
                String courseGrade = dictService.getDictLabel("course_grade", course.getGrade());
                infoList.add(courseGrade);
                course.setGradeName(courseGrade);
            }
            if (StringUtils.isNotBlank(course.getAffiliationSubject())) {
                String courseAffiliationSubject = dictService.getDictLabel("course_affiliation_subject", course.getAffiliationSubject());
                infoList.add(courseAffiliationSubject);
                course.setAffiliationSubjectName(courseAffiliationSubject);
            }
            if (StringUtils.isNotBlank(course.getSpecialTopic())) {
                infoList.add(dictService.getDictLabel("course_special_topic", course.getSpecialTopic()));
            }
            if (StringUtils.isNotBlank(course.getQuarterType())) {
                infoList.add(dictService.getDictLabel("course_quarter_type", course.getQuarterType()));
            }

            RemoteAttrRelationResultVo attrRelationResult = course.getAttrRelationResult();
            if (attrRelationResult != null && CollUtil.isNotEmpty(attrRelationResult.getRelationList())) {
                List<RemoteAttributeVo> attributeList = attrRelationResult.getAttributeList();
                List<RemoteAttributeRelationVo> relationList = attrRelationResult.getRelationList();
                //relationList按照 attributeList中的sort排序
                relationList.sort((o1, o2) -> {
                    RemoteAttributeVo attributeVo1 = attributeList.stream().filter(vo -> vo.getAttributeId().equals(o1.getAttributeId())).findFirst().orElse(null);
                    RemoteAttributeVo attributeVo2 = attributeList.stream().filter(vo -> vo.getAttributeId().equals(o2.getAttributeId())).findFirst().orElse(null);
                    return ObjUtil.defaultIfNull(attributeVo1.getAttributeSort(), 0) - ObjUtil.defaultIfNull(attributeVo2.getAttributeSort(), 0);

                });
                infoList.addAll(relationList.stream().map(RemoteAttributeRelationVo::getValue).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
            if (lastConcatCourseName) {
                infoList.add(course.getCourseName());
            }
            String join = infoList.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(separator));
            course.setCourseDetail(join);
        }
    }

    @Override
    public List<SpecialTopicCourseV2Vo> findSpecialTopicV2(CourseBo bo, Integer queryTopicType) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        String dictType = "course_special_topic";
        if (1 == queryTopicType) {
            dictType = "course_quarter_type";
        }

        //获取专题分类
        List<RemoteDictDataVo> courseSpecialTopic = remoteDictService.selectDictDataByType(dictType);
        if (CollUtil.isEmpty(courseSpecialTopic)) {
            return List.of();
        }

        List<SpecialTopicCourseV2Vo> resList = new ArrayList<>();
        for (RemoteDictDataVo remoteDictDataVo : courseSpecialTopic) {
            SpecialTopicCourseV2Vo specialTopicCategoryVo = new SpecialTopicCourseV2Vo();
            specialTopicCategoryVo.setSpecialTopic(remoteDictDataVo.getDictValue());
            specialTopicCategoryVo.setSpecialType(remoteDictDataVo.getDictType());
            specialTopicCategoryVo.setSpecialTopicName(remoteDictDataVo.getDictLabel());
            resList.add(specialTopicCategoryVo);
        }

        //获取所有顶级课程
        bo.setCourseType(UserConstants.TOP_COURSE_TYPE);
        List<Course> courseList = getCourseList(bo);
        if (CollUtil.isEmpty(courseList)) {
            return getList(resList);
        }

        courseList = courseList.stream().filter(course -> {
            if (queryTopicType == 1) {
                return StringUtils.isNotBlank(course.getQuarterType());
            } else if (queryTopicType == 0) {
                return StringUtils.isNotBlank(course.getSpecialTopic());
            }
            //默认取课程类型
            return StringUtils.isNotBlank(course.getSpecialTopic());
        }).collect(Collectors.toList());

        if (CollUtil.isEmpty(courseList)) {
            return getList(resList);
        }
        Map<String, List<Course>> groupedBySpecialTopic;
        //按照专题分类课程
        if (queryTopicType == 0) {
            groupedBySpecialTopic = StreamUtils.groupByKey(courseList, Course::getSpecialTopic);
        } else if (queryTopicType == 1) {
            groupedBySpecialTopic = StreamUtils.groupByKey(courseList, Course::getQuarterType);
        } else {
            groupedBySpecialTopic = StreamUtils.groupByKey(courseList, Course::getSpecialTopic);
        }
        for (SpecialTopicCourseV2Vo specialTopicCategoryVo : resList) {
            String specialTopic = specialTopicCategoryVo.getSpecialTopic();
            List<Course> courseListBySpecialTopic = groupedBySpecialTopic.get(specialTopic);
            List<CourseVo> specialTopicCategoryItemList = new ArrayList<>();
            if (CollUtil.isNotEmpty(courseListBySpecialTopic)) {
                for (Course course : courseListBySpecialTopic) {
                    CourseVo convert = MapstructUtils.convert(course, CourseVo.class);
                    specialTopicCategoryItemList.add(convert);
                }
            }
            specialTopicCategoryVo.setCourseList(specialTopicCategoryItemList);
        }
        return getList(resList);
    }

    private List<Course> getCourseList(CourseBo bo) {
        if (null == bo.getGrade()) {
            LambdaQueryWrapper<Course> courseLambdaQueryWrapper = buildLambdaQueryWrapper(bo);
            courseLambdaQueryWrapper.select(Course::getCourseId, Course::getCourseName, Course::getSpecialTopic,
                Course::getQuarterType);
            return baseMapper.selectList(courseLambdaQueryWrapper);
        } else {
            QueryWrapper<Course> wrapper = buildQueryWrapper(bo, true);
            return baseMapper.listGradeCourse(wrapper);
        }
    }

    private static @NotNull List<SpecialTopicCourseV2Vo> getList(List<SpecialTopicCourseV2Vo> resList) {
        if (CollUtil.isEmpty(resList)) {
            return List.of();
        }
        return resList.stream().filter(v -> CollUtil.isNotEmpty(v.getCourseList())).toList();
    }

    @Override
    public List<SpecialTopicCourseTreeVo> treeBySpecialTopic(CourseBo bo) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        List<Tree<Long>> trees = treeQuery(bo);
        if (CollUtil.isEmpty(trees)) {
            return List.of();
        }
        Map<String, List<Tree<Long>>> map = new HashMap<>();
        for (Tree<Long> tree : trees) {
            Object self = tree.get("self");
            if (self == null) {
                continue;
            }
            CourseVo course = (CourseVo) self;
            String specialTopic = course.getSpecialTopic();
            if (StringUtils.isBlank(specialTopic)) {
                continue;
            }
            if (map.containsKey(specialTopic)) {
                map.get(specialTopic).add(tree);
            } else {
                List<Tree<Long>> treeList = new ArrayList<>();
                treeList.add(tree);
                map.put(specialTopic, treeList);
            }
        }
        List<SpecialTopicCourseTreeVo> resList = new ArrayList<>();
        for (Map.Entry<String, List<Tree<Long>>> entry : map.entrySet()) {
            SpecialTopicCourseTreeVo specialTopicCourseTreeVo = new SpecialTopicCourseTreeVo();
            String key = entry.getKey();
            List<Tree<Long>> value = entry.getValue();
            specialTopicCourseTreeVo.setSpecialTopic(key);
            specialTopicCourseTreeVo.setCourseTreeList(value);
            resList.add(specialTopicCourseTreeVo);
        }
        return resList;
    }


    @Override
    public List<Tree<Long>> treeByStage(CourseBo bo) {
        if ("all".equals(bo.getGrade())) {
            bo.setGrade(null);
        }
        List<Tree<Long>> trees = treeQuery(bo);
        if (CollUtil.isEmpty(trees)) {
            return List.of();
        }
        Map<String, List<Tree<Long>>> map = new HashMap<>();
        for (Tree<Long> tree : trees) {
            Object self = tree.get("self");
            if (self == null) {
                continue;
            }
            CourseVo course = (CourseVo) self;
            String stage = course.getStage();
            if (StringUtils.isBlank(stage)) {
                continue;
            }
            if (map.containsKey(stage)) {
                map.get(stage).add(tree);
            } else {
                List<Tree<Long>> treeList = new ArrayList<>();
                treeList.add(tree);
                map.put(stage, treeList);
            }
        }

        //字典查询对应的值
        List<RemoteDictDataVo> remoteDictDataVoList = remoteDictService.selectDictDataByType("course_stage");

        Map<String, String> dictDataMap = new Hashtable<>();
        remoteDictDataVoList.parallelStream().forEach(vo -> {
            dictDataMap.put(vo.getDictValue(), vo.getDictLabel());
        });


        List<Tree<Long>> resList = new ArrayList<>();
        for (Map.Entry<String, List<Tree<Long>>> entry : map.entrySet()) {
            Tree<Long> tree = new Tree<>();
            tree.setName(dictDataMap.get(entry.getKey()));
            tree.setParentId(-1L);
            tree.setChildren(entry.getValue());
            resList.add(tree);
        }
        return resList;
    }

    @Override
    public boolean deleteCourse(Long courseId) {
        //找出子节点
        CourseBo bo = new CourseBo();
        bo.setCourseId(courseId);
        CourseChildInfoVo childInfo = getChildInfo(bo);

        List<Long> delList = new ArrayList<>();
        delList.add(courseId);

        //删除子节点
        if (childInfo != null && StringUtils.isNotBlank(childInfo.getChildIds())) {
            List<Long> childIdList = Arrays.stream(childInfo.getChildIds().split(",")).map(Long::parseLong).toList();
            if (CollUtil.isNotEmpty(childIdList)) {
                for (Long childId : childIdList) {
                    delList.add(childId);
                }
            }
        }
        return baseMapper.deleteBatchIds(delList) > 0;
    }

    @Override
    public boolean batchRemoveCourse(List<Long> courseIds, Integer type) {
        if (CollUtil.isEmpty(courseIds)) {
            return false;
        }
        List<Course> courseList = baseMapper.selectList(Wrappers.lambdaQuery(Course.class)
            .in(Course::getCourseId, courseIds));
        if (CollectionUtil.isEmpty(courseList)) {
            return false;
        }
        if (CourseProcessEnum.CLEAR.getCode().equals(type)) {
            return baseMapper.batchClearCourse(courseIds) > 0;
        }
        courseList.stream().filter(i -> Objects.nonNull(i.getKnowledgeId()))
            .findFirst().ifPresent(e -> {
                throw new ServiceException("节点已配置知识点，请先清除知识点");
            });
        if (CourseProcessEnum.REMOVE.getCode().equals(type)) {
            courseList.forEach(c -> c.setDelFlag(UserConstants.DEL_FLAG_YES));
            return baseMapper.deleteBatchIds(courseIds) > 0;
        }
        return false;
    }

    /**
     * 获取当前树的最大深度
     *
     * @param treeNode
     * @param level
     * @return
     */
    private long getMaxLevel(Tree<Long> treeNode, long level) {
        List<Tree<Long>> childrenList = treeNode.getChildren();
        if (childrenList != null && !childrenList.isEmpty()) {
            long l = 0;
            for (Tree<Long> item : childrenList) {
                l = Math.max(l, getMaxLevel(item, level + 1));
            }
            return l;
        }
        return level;
    }

    @Override
    public void export(CourseBo bo, String sheetName, HttpServletResponse response) {
        bo.setWithAttr(Boolean.TRUE);
        List<Tree<Long>> trees = treeQuery(bo);
        trees = CollUtil.isEmpty(trees) ? List.of() : trees;
        long index = 0;
        for (Tree<Long> tree : trees) {
            index = Math.max(getMaxLevel(tree, 0), index);
        }
        ServletOutputStream outputStream = null;
        try {
            ExcelUtil.resetResponse("课程（课程包含章节）", response);
            outputStream = response.getOutputStream();
        } catch (Exception e) {
            throw new ServiceException("导出失败");
        }

        //动态表头
        List<List<String>> header = new ArrayList<>();
        header.add(List.of("课程名称"));
        header.add(List.of("课程详情"));
        header.add(List.of("课程简介"));
        for (long i = 0; i < index; i++) {
            header.add(List.of(Convert.numberToChinese((i + 1), false) + "级名称"));
        }
        header.add(List.of("知识点ID"));

        int headerSize = header.size();

        //构建数据集
        List<List<String>> data = new ArrayList<>();
        for (Tree<Long> tree : trees) {
            Object o = tree.get("self");
            Object self = tree.get("self");
            if (self == null) {
                continue;
            }
            CourseVo course = (CourseVo) self;
            List<String> row = new ArrayList<>();
            row.add(course.getCourseName());
            row.add(course.getCourseDetail());
            row.add(course.getCourseIntroduction());
            data.add(row);

            int baseStartIndex = 3;
            recursiveProcessing(tree, (item) -> {
                Object selfCourse = item.get("self");
                if (selfCourse == null) {
                    return;
                }
                CourseVo courseVo = (CourseVo) selfCourse;
                //判断自己是第几级
                String ancestors = courseVo.getAncestors();
                String[] split = ancestors.split(",");
                int courseType = split.length - 1;
                List<String> list = new ArrayList<>();
                for (int i = 0; i < baseStartIndex + courseType; i++) {
                    if (i == baseStartIndex + courseType - 1) {
                        list.add(courseVo.getCourseName());
                    }
                    list.add("");
                }
                if (headerSize - 1 == list.size()) {
                    list.add(Convert.toStr(courseVo.getKnowledgeId()));
                }
                data.add(list);
            });
        }

        EasyExcel.write(outputStream)
            .autoCloseStream(false)
            .registerConverter(new ExcelBigNumberConvert())
            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
            // 这里放入动态头
            .head(header)
            .sheet(sheetName)
            // 当然这里数据也可以用 List<List<String>> 去传入
            .doWrite(data);
    }

    @Override
    public List<CourseVo> listCourseVos(List<Long> courseIds) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<Course>().in(Course::getCourseId, courseIds));
    }

    @Override
    public boolean sync(CourseSyncBo syncBo) {
        List<RemoteExtCourseCatalogVo> courseCatalogTree = remoteResourceService.getCourseCatalogTree(syncBo.getCourseId(), syncBo.getParentId());
        List<RemoteExtCourseCatalogVo> remoteExtCourseCatalogVos = extractLeafNodes(courseCatalogTree);

        if (CollUtil.isEmpty(remoteExtCourseCatalogVos)) {
            return true;
        }
        Long resourceId = syncBo.getResourceId();
        CourseVo courseParentVo = queryById(resourceId);
        if (ObjectUtils.isEmpty(courseParentVo)) {
            return true;
        }

        List<CourseBo> list = remoteExtCourseCatalogVos.stream().map(v -> {
            CourseBo bo = new CourseBo();
            if (v.getLeaf() != null && v.getLeaf() != 0) {
                bo.setKnowledgeId(v.getId());
            }
            bo.setCourseName(v.getCatalogName());
            bo.setCourseParentId(courseParentVo.getCourseId());
            bo.setCourseType(courseParentVo.getCourseType() + 1);
            bo.setAncestors(courseParentVo.getAncestors() + "," + courseParentVo.getCourseId());
            return bo;
        }).toList();

        int tempSort = getCourseMaxSort(courseParentVo) + 1;
        for (CourseBo bo : list) {
            bo.setSort(tempSort);
            tempSort++;
        }
        insertBatchByBoList(list);
        return true;
    }

    @Override
    public List<Course> getCoursesByCourseId(List<Long> courseIdList) {
        LambdaQueryWrapper<Course> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Course::getCourseId, courseIdList);
        lambdaQueryWrapper.eq(Course::getDelFlag, 0);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public void synchronizedData() {
        Page<Course> coursePage = new Page<>(1, 2000);
        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class).isNotNull(Course::getGrade)
            .select(Course::getCourseId, Course::getGrade);
        baseMapper.selectPage(coursePage, wrapper);
        while (CollectionUtil.isNotEmpty(coursePage.getRecords())) {
            List<Course> courses = coursePage.getRecords();
            List<CourseGrade> existCourseGradeList = courseGradeService
                .listByCourseIds(courses.stream().map(Course::getCourseId).collect(Collectors.toList()));
            Map<Long, List<CourseGrade>> courseGradeMap =
                existCourseGradeList.stream().collect(Collectors.groupingBy(CourseGrade::getCourseId));
            List<CourseGrade> courseGrades = courses.stream().filter(course -> {
                List<CourseGrade> courseExistGrade = courseGradeMap.get(course.getCourseId());
                return CollectionUtil.isEmpty(courseExistGrade) || courseExistGrade.stream()
                    .noneMatch(courseGrade -> course.getGrade().equals(courseGrade.getGrade()));
            }).map(course -> new CourseGrade(course.getCourseId(), course.getGrade())).collect(Collectors.toList());
            if (courseGradeService.saveBatch(courseGrades)) {
                coursePage = new Page<>(coursePage.getCurrent() + 1, 2000);
                baseMapper.selectPage(coursePage, wrapper);
            } else {
                throw new ServiceException("同步数据失败");
            }
        }
    }

    @Override
    public List<CourseVo> listCourseSelection(CourseBo bo) {
        List<CourseVo> courseVos = baseMapper.findCourseAncestors(bo.getCourseParentId())
            .stream().filter(i -> Objects.nonNull(i.getKnowledgeId())).toList();
        checkKnowledgeComplete(courseVos);
        return courseVos;
    }

    private void checkKnowledgeComplete(List<CourseVo> courseVos) {
        List<Long> knowledgeIdList = courseVos.stream().map(CourseVo::getKnowledgeId).collect(Collectors.toList());
        RemoteKnowledgeCompleteVo knowledgeComplete = remoteExtResourceService.getKnowledgeComplete(knowledgeIdList);
        if (null != knowledgeComplete) {
            Map<Long, Boolean> knowledgeCompleteMap = knowledgeComplete.getKnowledgeCompleteMap();
            if (!CollectionUtils.isEmpty(knowledgeCompleteMap)) {
                courseVos.forEach(courseVo -> {
                    if (null != courseVo.getKnowledgeId() && knowledgeCompleteMap.get(courseVo.getKnowledgeId()) != null
                        && !knowledgeCompleteMap.get(courseVo.getKnowledgeId())) {
                        courseVo.setKnowledgeId(null);
                    }
                });
            }
        }
    }

    @Override
    public List<Tree<Long>> batchQueryTree(List<Long> courseIds) {
        List<Tree<Long>> treeList = new ArrayList<>();
        for (Long courseId : courseIds) {
            CourseVo parentCourse = baseMapper.selectVoOne(Wrappers.lambdaQuery(Course.class)
                .eq(Course::getCourseId, courseId));
            if (Objects.isNull(parentCourse)) {
                continue;
            }

            List<CourseVo> courseVoList = Lists.newArrayList(parentCourse);
            courseVoList.addAll(baseMapper.findCourseAncestors(courseId));

            courseVoList = processCatalogWithoutKnowledge(courseVoList);
            if (CollectionUtil.isNotEmpty(courseVoList)) {
                checkKnowledgeComplete(courseVoList);
                List<Tree<Long>> trees = buildTree(courseVoList);
                List<Tree<Long>> children = trees.get(0).getChildren();
                if (CollUtil.isNotEmpty(children)) {
                    treeList.addAll(children);
                }
            }
        }
        return treeList;
    }

    private List<CourseVo> processCatalogWithoutKnowledge(List<CourseVo> courseVoList) {
        if (CollUtil.isNotEmpty(courseVoList)) {
            List<Tree<Long>> trees = buildTree(courseVoList);
            List<Long> filterIds = recurseCatalogWithoutKnowledge(trees, null);
            return courseVoList.stream().filter(i -> !filterIds.contains(i.getCourseId())).toList();
        }
        return courseVoList;
    }

    private List<Long> recurseCatalogWithoutKnowledge(List<Tree<Long>> trees, List<Long> list) {
        list = Optional.ofNullable(list).orElseGet(ArrayList::new);
        for (Tree<Long> tree : trees) {
            try {
                CourseVo courseVo = JSONUtil.toBean(JSONUtil.parseObj(tree.get("self")), CourseVo.class);
                if (CollUtil.isEmpty(tree.getChildren())) {
                    if (Objects.isNull(courseVo.getKnowledgeId())) {
                        list.add(courseVo.getCourseId());
                    }
                } else {
                    recurseCatalogWithoutKnowledge(tree.getChildren(), list);
                }
            } catch (Exception e) {
                log.error("课程树节点异常{}", tree);
            }
        }
        return list;
    }
    @Override
    public List<CourseVo> getCourseInfoWithParentNameByCourseId(List<Long> courseIds) {

        return baseMapper.getCourseInfoWithParentNameByCourseId(courseIds);
    }

    @Override
    public List<GradeTreeVo> buildCourseTreeByStageAndType(String stage, Integer courseType) {
        // 根据学段Id和课程类型查询出所有的年级列表
        Map<String, String> courseStageMap = dictService.getAllDictByDictType("course_stage");
        log.info("学段字典信息{}", courseStageMap);
        String stageLabel = courseStageMap.get(stage);

        List<String> gradeList = switch (stageLabel) {
            case "小学" -> studentGradeConfig.getXiaoxue();
            case "初中" -> studentGradeConfig.getJuniorHigh();
            case "高中" -> studentGradeConfig.getSeniorHigh();
            default -> throw new ServiceException("学段参数错误");
        };
        String dictType;
        if (courseType == 1) {
            dictType = "course_quarter_type";
        }else {
            dictType = "course_special_topic";
        }
        List<RemoteDictDataVo> disctDataList = remoteDictService.selectDictDataByType(dictType);
        List<CourseVo> courseVos = baseMapper.queryGradeCourse(Wrappers.query(Course.class)
                .eq("del_flag", 0)
            .eq("course_type", 1)
            // 查询寒暑春秋
            .in(courseType == 1, "quarter_type", disctDataList.stream().map(RemoteDictDataVo::getDictValue).toList())
            // 查询AI伴学
            .in(courseType == 2, "special_topic", disctDataList.stream().map(RemoteDictDataVo::getDictValue).toList())
            .in(CollUtil.isNotEmpty(gradeList), "cg.grade", gradeList));

        // 课程是空，不做处理
        if (CollUtil.isEmpty(courseVos)) {
            return Collections.emptyList();
        }

        // 过滤授权课程列表
        List<Long> branchAuthCourseIdList = getBranchAuthCourse();
        if (CollUtil.isNotEmpty(branchAuthCourseIdList)) {
            // 从授权课程中获取目标课程
            courseVos = courseVos.stream().filter(
                courseVo -> branchAuthCourseIdList.contains(courseVo.getCourseId())).toList();
        }

        if (CollUtil.isEmpty(courseVos)) {
            return Collections.emptyList();
        }

        return treeByStageAndCourseTypeWithGrades(courseType, courseVos, 1);
    }

    private List<Long> getBranchAuthCourse() {
        //如果是门店，查该门店下全部
        log.info("当前登录用户【{}】的机构id【{}】", LoginHelper.getLoginUser().getUserId(), LoginHelper.getDeptId());
        if (null != LoginHelper.getBranchId() || CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            Long deptId = LoginHelper.getDeptId();
            boolean b = remoteDeptService.deptIsShop(deptId);
            if (Boolean.TRUE.equals(b)) {
                RemoteDeptVo preDeptByDeptId = remoteDeptService.getPreDeptByDeptId(LoginHelper.getSelectDeptId());
                log.info("当前登录用户【{}】的上级机构信息【{}】", LoginHelper.getLoginUser().getUserId(), preDeptByDeptId);
                if (ObjectUtils.isEmpty(preDeptByDeptId)) {
                    return Collections.emptyList();
                }
                deptId = preDeptByDeptId.getDeptId();
            }
            // 根据这个deptId查询到关联的模板ID
            RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
            remoteStudentProductTemplateAuthBo.setDeptId(deptId);//代理商ID
            remoteStudentProductTemplateAuthBo.setTemplateType(1);//课程模板查询
            remoteStudentProductTemplateAuthBo.setShowAuth(true);
            List<Long> remoteBranchAuthTypeVos = remoteOrderService.queryAuthCourseIds(remoteStudentProductTemplateAuthBo);

            if (CollUtil.isNotEmpty(remoteBranchAuthTypeVos)) {
                return remoteBranchAuthTypeVos.stream()
                    .distinct()
                    .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    /**
     * 1. 加入一个年级入参
     * 2. 默认是all年级、学段【】
     * 3. 传了学段，按学段的年级来
     * 4. 不传，就是所有的年级
     * 5. 传了年级，就按单个年查询
     * 6， 不传就是查询所有的年级 all
     *
     * @return
     */
    @Override
    public List<GradeTreeVo> treeByStageAndCourseTypeWithGrades(Integer courseType, List<CourseVo> courseVos, int defaultLayer) {

        //  查询字典表中的年级、学科、课程专题、课程季度类型
        List<RemoteDictDataVo> quarterDictDataVoList = remoteDictService.selectDictDataByType("course_quarter_type");
        List<RemoteDictDataVo> courseGradeDictDataVoList = remoteDictService.selectDictDataByType("course_grade");
        List<RemoteDictDataVo> affiliationSubjectDictDataVoList = remoteDictService.selectDictDataByType("course_affiliation_subject");
        List<RemoteDictDataVo> specialTopicDictDataVoList = remoteDictService.selectDictDataByType("course_special_topic");

        // 学段
        Map<String, RemoteDictDataVo> affiliationSubjectValueMap = affiliationSubjectDictDataVoList.stream().collect(Collectors.toMap(RemoteDictDataVo::getDictValue, Function.identity()));

        // 查询出所有课程ID对应的属性关系
        List<Long> courseIds = courseVos.stream().map(CourseVo::getCourseId).collect(Collectors.toList());

        // Map属性名称-属性Id 例：类型-类型的属性Id
        Map<String, Long> remoteAttributeMap = this.loadAllAttributes("course");
        // 只找出需要构建树结构的属性Id集合
        List<String> attributeNameList = studentGradeConfig.getAttributeNameList();
        List<Long> attributeIds = remoteAttributeMap.entrySet().stream()
            .filter(entry -> attributeNameList.contains(entry.getKey()))
            .map(Map.Entry::getValue).collect(Collectors.toList());
        List<CourseAttributeDetailDTO> courseAttributeDetailDTOList = remoteAttributeService.batchQueryAttributeByIdAndType(courseIds, attributeIds, "course");

        // 先构建学科树结构
        Map<String, List<CourseVo>> gradeCourseMap = courseVos.stream().collect(Collectors.groupingBy(CourseVo::getAffiliationSubject));
        List<GradeTreeVo> gradeTreeVoList = new ArrayList<>();
        for (Map.Entry<String, List<CourseVo>> entry : gradeCourseMap.entrySet()) {
            GradeTreeVo gradeTreeVo = new GradeTreeVo();
            gradeTreeVo.setDictName(affiliationSubjectValueMap.get(entry.getKey()).getDictLabel());
            gradeTreeVo.setDictType("学科");
            gradeTreeVo.setDictValue(entry.getKey());
            gradeTreeVo.setDictSort(affiliationSubjectValueMap.get(entry.getKey()).getDictSort());
            // 构建年级树结构
            List<GradeTreeVo> treeVoList = buildGradeTree(entry.getValue(), courseGradeDictDataVoList, remoteAttributeMap,
                specialTopicDictDataVoList, quarterDictDataVoList, courseType, courseAttributeDetailDTOList, defaultLayer);

            gradeTreeVo.setChildrenList(treeVoList);

            gradeTreeVoList.add(gradeTreeVo);
        }
        log.info("【根据学段Id和课程类型查询出所有的年级列表】返回结果：{}", gradeTreeVoList);

        return gradeTreeVoList.stream().sorted(Comparator.comparing(GradeTreeVo::getDictSort)).toList();
    }


    /**
     * 构建学科树结构
     * 1. 找出不同学科下面的课程集合列表（跟属性树一样）
     *
     * @param courseVoList                     不同年级下面的课程学习列表
     * @param affiliationSubjectDictDataVoList 学科字典信息集合
     * @param remoteAttributeMap               属性字典集合
     * @param specialTopicDictDataVoList       专题字典信息集合
     * @param quarterDictDataVoList            季度字典信息集合
     * @param courseType                       课程类型（1：寒暑春秋，2：AI伴学）
     * @param courseAttributeDetailDTOList     属性关系集合
     */
    private List<GradeTreeVo> buildGradeTree(List<CourseVo> courseVoList, List<RemoteDictDataVo> affiliationSubjectDictDataVoList,
                                             Map<String, Long> remoteAttributeMap, List<RemoteDictDataVo> specialTopicDictDataVoList,
                                             List<RemoteDictDataVo> quarterDictDataVoList, Integer courseType,
                                             List<CourseAttributeDetailDTO> courseAttributeDetailDTOList, int defaultLayer) {
        Map<String, List<CourseVo>> subjectCourseMap = courseVoList.stream().collect(Collectors.groupingBy(CourseVo::getGrade));
        Map<String, RemoteDictDataVo> affiliationSubjectDictDataVoMap = affiliationSubjectDictDataVoList.stream().collect(Collectors.toMap(RemoteDictDataVo::getDictValue, Function.identity()));
        return subjectCourseMap.entrySet().stream().map(entry -> {
            GradeTreeVo node = new GradeTreeVo();
            node.setDictType("年级");
            node.setDictName(affiliationSubjectDictDataVoMap.get(entry.getKey()).getDictLabel());
            node.setDictSort(affiliationSubjectDictDataVoMap.get(entry.getKey()).getDictSort());
            node.setDictValue(entry.getKey());
            // 构建年级树结构
            if (defaultLayer == 1) {
                node.setChildrenList(buildAttributeTree(entry.getValue(), remoteAttributeMap, studentGradeConfig.getAttributeNameList(), specialTopicDictDataVoList, quarterDictDataVoList, courseType, courseAttributeDetailDTOList));
            } else {
                node.setChildrenList(buildAttributeTree(entry.getValue(), remoteAttributeMap, Collections.emptyList(), specialTopicDictDataVoList, quarterDictDataVoList, courseType, courseAttributeDetailDTOList));
            }
            return node;
        }).sorted(Comparator.comparing(GradeTreeVo::getDictSort)).collect(Collectors.toList());
    }


    /**
     * 构建属性树结构
     *
     * @param courses                      课程信息列表
     * @param attributeMap                 属性名称映射集
     * @param attributeNameList            属性名称列表
     * @param specialTopicDictDataVoList   专题字典信息集合
     * @param quarterDictDataVoList        季度字典信息集合
     * @param courseType                   课程类型（1：寒暑春秋，2：AI伴学）
     * @param courseAttributeDetailDTOList 属性关系集合
     */
    private List<GradeTreeVo> buildAttributeTree(List<CourseVo> courses,
                                                 Map<String, Long> attributeMap,
                                                 List<String> attributeNameList, List<RemoteDictDataVo> specialTopicDictDataVoList,
                                                 List<RemoteDictDataVo> quarterDictDataVoList, Integer courseType, List<CourseAttributeDetailDTO> courseAttributeDetailDTOList) {
        return buildTreeRecursively(courses, attributeMap, attributeNameList, 0, specialTopicDictDataVoList, quarterDictDataVoList, courseType, courseAttributeDetailDTOList);
    }


    /**
     * 构建属性树结构
     *
     * @param currentLevelCourseList       当前层级的课程列表
     * @param attributeMap                 属性名称映射集
     * @param attributeNameList            属性名称列表
     * @param currentDepth                 当前层级的深度
     * @param specialTopicDictDataVoList   专题字典信息集合
     * @param quarterDictDataVoList        季度字典信息集合
     * @param courseType                   课程类型（1：寒暑春秋，2：AI伴学）
     * @param courseAttributeDetailDTOList 属性关系集合
     */
    private List<GradeTreeVo> buildTreeRecursively(List<CourseVo> currentLevelCourseList,
                                                   Map<String, Long> attributeMap,
                                                   List<String> attributeNameList,
                                                   int currentDepth, List<RemoteDictDataVo> specialTopicDictDataVoList,
                                                   List<RemoteDictDataVo> quarterDictDataVoList, Integer courseType,
                                                   List<CourseAttributeDetailDTO> courseAttributeDetailDTOList) {
        // 终止条件：处理完所有层级属性
        if (currentDepth >= attributeNameList.size()) {
            // 层级构建完成之后，开始构建 专题课程体系（需要考虑的是，这个层级要分课程类型 1.寒暑春秋[course_quarter_type]；2.AI伴学[course_special_topic]）
            return buildTopicOrQuarterTree(currentLevelCourseList, specialTopicDictDataVoList, quarterDictDataVoList, courseType);
        }
        // 获取当前层级的属性ID
        String currentAttributeName = attributeNameList.get(currentDepth);
        Long attributeId = Optional.ofNullable(attributeMap.get(currentAttributeName)).orElseThrow(() -> new ServiceException("未找到属性: " + currentAttributeName));

        // 找出属性下面所有的课程ID
        Map<String, Set<Long>> groupedCourses = groupCoursesByAttribute(currentLevelCourseList, attributeId, courseAttributeDetailDTOList);

        // 如果groupedCourses 是空，表示这个设置类型、教材版本、难度等属性
        AtomicInteger index = new AtomicInteger();
        List<GradeTreeVo> childrenList = new ArrayList<>();
        groupedCourses.entrySet().forEach(entry -> {
            GradeTreeVo node = new GradeTreeVo();
            node.setDictType(attributeNameList.get(currentDepth));
            node.setDictName(entry.getKey());
            // 做唯一标识
            node.setDictValue(String.valueOf(index.getAndIncrement()));
            // 筛选当前节点包含的课程
            List<CourseVo> childCourses = filterCourses(currentLevelCourseList, entry.getValue());
            // 递归构建子节点
            node.setChildrenList(buildTreeRecursively(childCourses, attributeMap, attributeNameList, currentDepth + 1, specialTopicDictDataVoList, quarterDictDataVoList, courseType, courseAttributeDetailDTOList));
            if ("通用".equals(node.getDictName())) {
                childrenList.add(0, node);
            } else {
                childrenList.add(node);
            }
        });
        return childrenList;
    }

    /**
     * 构建到课程类型树之后，子节点需要存储对应类型的课程信息
     *
     * @param courseVoList
     * @param specialTopicDictDataVoList
     * @param quarterDictDataVoList
     * @param courseType
     * @return
     */
    private List<GradeTreeVo> buildTopicOrQuarterTree(List<CourseVo> courseVoList, List<RemoteDictDataVo> specialTopicDictDataVoList,
                                                      List<RemoteDictDataVo> quarterDictDataVoList, Integer courseType) {
        Map<String, List<CourseVo>> typeMap;
        Map<String, RemoteDictDataVo> specialTopicValueMap = specialTopicDictDataVoList.stream().collect(Collectors.toMap(RemoteDictDataVo::getDictValue, Function.identity()));
        Map<String, RemoteDictDataVo> quarterValueMap = quarterDictDataVoList.stream().collect(Collectors.toMap(RemoteDictDataVo::getDictValue, Function.identity()));

        // 1 表示寒暑春秋
        if (courseType == 1) {
            typeMap = courseVoList.stream().collect(Collectors.groupingBy(CourseVo::getQuarterType));
        } else {
            // 表示专题课程
            typeMap = courseVoList.stream().collect(Collectors.groupingBy(CourseVo::getSpecialTopic));
        }
        return typeMap.entrySet().stream().map(entry -> {
            RemoteDictDataVo quarterDictDataVo = quarterValueMap.get(entry.getKey());
            RemoteDictDataVo specialTopicDictDataVo = specialTopicValueMap.get(entry.getKey());
            GradeTreeVo node = new GradeTreeVo();
            node.setDictValue(entry.getKey());
            node.setDictType("专题");
            node.setDictSort(courseType == 1 ? quarterDictDataVo.getDictSort() : specialTopicDictDataVo.getDictSort());
            node.setDictName(courseType == 1 ? quarterDictDataVo.getDictLabel() : specialTopicDictDataVo.getDictLabel());

            // 递归构建子节点
            node.setChildrenList(convertToGradeTreeVoList(entry.getValue()));
            return node;
        }).sorted(Comparator.comparing(GradeTreeVo::getDictSort)).collect(Collectors.toList());
    }

    /**
     * 将课程列表转换成树结构的课程列表数据
     *
     * @param courseVoList 课程列表
     */
    private List<GradeTreeVo> convertToGradeTreeVoList(List<CourseVo> courseVoList) {
        return courseVoList.stream().map(courseVo -> {
            GradeTreeVo gradeTreeVo = new GradeTreeVo();
            gradeTreeVo.setCourseId(courseVo.getCourseId());
            gradeTreeVo.setDictName(courseVo.getCourseName());
            gradeTreeVo.setDictType("course");
            return gradeTreeVo;
        }).collect(Collectors.toList());
    }


    /**
     * 找出属性下面所有的课程ID
     *
     * @param courseVoList 课程集合
     * @param attributeId  属性ID【教材版本,类型,难度】
     * @return Map<属性值, 课程ID集合>
     */
    private Map<String, Set<Long>> groupCoursesByAttribute(List<CourseVo> courseVoList, Long attributeId, List<CourseAttributeDetailDTO> courseAttributeDetailDTOList) {
        if (courseVoList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Long> courseIds = courseVoList.stream().map(CourseVo::getCourseId).toList();
        Map<String, Set<Long>> groupedMap = courseAttributeDetailDTOList.stream()
            .filter(dto -> courseIds.contains(dto.getTypeId()) && dto.getAttributeId().equals(attributeId))
            .collect(Collectors.groupingBy(
                CourseAttributeDetailDTO::getValue,
                Collectors.mapping(CourseAttributeDetailDTO::getTypeId, Collectors.toSet())
            ));
        Set<Long> groupedCourseIds = groupedMap.values().stream()
            .flatMap(Set::stream)
            .collect(Collectors.toSet());

        // 找出没有设置属性的课程ID
        Set<Long> ungroupedCourseIds = courseIds.stream()
            .filter(id -> !groupedCourseIds.contains(id))
            .collect(Collectors.toSet());

        // 给这些设置新的分组 通用
        if (!ungroupedCourseIds.isEmpty()) {
            Set<Long> allAttributeIds = groupedMap.get("通用");
            if (CollUtil.isNotEmpty(allAttributeIds)) {
                allAttributeIds.addAll(ungroupedCourseIds);
            }else {
                groupedMap.put("通用", ungroupedCourseIds);
            }
        }
        return groupedMap;
    }

    /**
     * 课程筛选方法
     *
     * @param source    课程信息列表
     * @param targetIds 需要筛选出来的课程Id集合
     * @return 筛选后的课程信息列表
     */
    private List<CourseVo> filterCourses(List<CourseVo> source, Set<Long> targetIds) {
        return source.stream()
            .filter(c -> targetIds.contains(c.getCourseId()))
            .collect(Collectors.toList());
    }

    /**
     * 加载所有属性
     */
    @Override
    public Map<String, Long> loadAllAttributes(String attributeType) {
        List<RemoteAttributeVo> remoteAttributeVoList = remoteAttributeService.getAttributeByGroupType(attributeType, -1L, true);
        return remoteAttributeVoList.stream().collect(Collectors.toMap(RemoteAttributeVo::getAttributeName, RemoteAttributeVo::getAttributeId));
    }

    @Override
    public List<GradeTreeVo> getTopicCategoryV2(String grade, int defaultLayer) {
        CourseBo courseBo = new CourseBo();
        courseBo.setGrade(grade);
        List<Course> courseList = getSpecialTopicCourseList(courseBo);
        if (CollUtil.isEmpty(courseList)) {
            return Collections.emptyList();
        }
        List<CourseVo> courseVoList = MapstructUtils.convert(courseList, CourseVo.class);
        if (CollUtil.isEmpty(courseVoList)) {
            return Collections.emptyList();
        }
        return treeByStageAndCourseTypeWithGrades(2, courseVoList, defaultLayer);
    }

    @Override
    public boolean saveCourseSearch(String searchContent, Long studentId) {
        try {
            CourseSearchHistoryVo courseSearchHistoryVo = buildCourseSearchHistory(studentId);
            courseSearchHistoryVo.setSearchContent(searchContent);

            Long loginUserId = LoginHelper.getLoginUser().getUserId();
            String key = "course_search_" + loginUserId + "_" + studentId;
            // 保存到Redis，有效期为一周（7天）
            RedisUtils.setCacheObject(key, courseSearchHistoryVo, Duration.ofDays(7));
            return true;
        } catch (Exception e) {
            log.error("保存课程搜索历史失败{}", studentId, e);
            return false;
        }
    }

    @Override
    public CourseSearchHistoryVo getCourseSearch(Long studentId) {
        try {
            // 获取登录用户id
            Long loginUserId = LoginHelper.getLoginUser().getUserId();
            // 构建Redis key
            String key = "course_search_" + loginUserId + "_" + studentId;
            // 从Redis获取课程搜索历史
            Object cache = RedisUtils.getCacheObject(key);
            if (Objects.nonNull(cache)) {
                return (CourseSearchHistoryVo) cache;
            }
            return buildCourseSearchHistory(studentId);
        } catch (Exception e) {
            log.error("获取课程搜索历史失败{}", studentId, e);
            return null;
        }
    }

    private CourseSearchHistoryVo buildCourseSearchHistory(Long studentId) {
        CourseSearchHistoryVo courseSearchHistoryVo = new CourseSearchHistoryVo();
        StudentVo student = studentService.getInfo(studentId);
        if (Objects.isNull(student)) {
            return courseSearchHistoryVo;
        }
        courseSearchHistoryVo.setGrade(student.getStudentGrade());

        Map<String, String> courseGradeMap = dictService.getAllDictByDictType("course_grade");
        String gradeCode = null;
        for (Map.Entry<String, String> entry : courseGradeMap.entrySet()) {
            if (entry.getValue().equals(courseSearchHistoryVo.getGrade())) {
                gradeCode = entry.getKey();
                break;
            }
        }
        if (StringUtils.isBlank(gradeCode)) {
            return courseSearchHistoryVo;
        }

        String stage = null;
        Map<String, String> originalCourseStageMap = dictService.getAllDictByDictType("course_stage");
        // 翻转map的key和value，使得可以通过标签获取字典值
        Map<String, String> courseStageMap = originalCourseStageMap.entrySet().stream()
            .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

        if (studentGradeConfig.getXiaoxue().contains(gradeCode)) {
            stage = courseStageMap.get("小学");
        } else if (studentGradeConfig.getJuniorHigh().contains(gradeCode)) {
            stage = courseStageMap.get("初中");
        } else if (studentGradeConfig.getSeniorHigh().contains(gradeCode)) {
            stage = courseStageMap.get("高中");
        }
        courseSearchHistoryVo.setStage(stage);

        return courseSearchHistoryVo;
    }

    /**
     * 根据课程ID集合，查询这些课程对应的学段名称
     *
     * @param courseIds
     * @return Map<Long, String> key-课程ID value-学段名称
     */
    @Override
    public Map<Long, String> getStageListByCourseIds(List<Long> courseIds) {
        log.info("【根据课程ID集合查询对应的学段名称】请求参数：{}", courseIds);
        //判断课程ID集合是否为空
        if (ObjectUtil.isEmpty(courseIds)) return MapUtil.newHashMap();

        //根据课程ID 查询课程信息
        List<CourseVo> courseVos = baseMapper.selectVoList(Wrappers.lambdaQuery(Course.class)
            .in(Course::getCourseId, courseIds));
        log.info("【根据课程ID集合查询对应的学段名称】课程信息列表：{}", courseVos);
        if (ObjectUtil.isEmpty(courseVos)) return MapUtil.newHashMap();

        //查询学段枚举列表
        List<RemoteDictDataVo> remoteDictDataVoList = remoteDictService.selectDictDataByType("course_stage");
        log.info("【根据课程ID集合查询对应的学段名称】学段数据字典列表：{}", remoteDictDataVoList);
        if (ObjectUtil.isEmpty(remoteDictDataVoList)) return MapUtil.newHashMap();

        //将学段枚举列表转换为Map
        //key-学段标识（比如：h） value-学段名称（比如：高中）
        Map<String, String> courseStageMaps = remoteDictDataVoList.stream()
            .collect(Collectors.groupingBy(RemoteDictDataVo::getDictValue, Collectors.collectingAndThen(
                Collectors.mapping(RemoteDictDataVo::getDictLabel, Collectors.toList()),
                list -> list.isEmpty() ? null : list.get(0)
            )));

        //给课程设置对应的学段名称
        Map<Long, String> maps = courseVos.stream().map(courseVo -> {
            courseVo.setStageName(courseStageMaps.get(courseVo.getStage()));
            return courseVo;
        }).collect(Collectors.toMap(CourseVo::getCourseId, CourseVo::getStageName));
        return maps;
    }

    private int getCourseMaxSort(CourseVo courseVo) {
        Long courseId = courseVo.getCourseId();
        Integer l = this.baseMapper.selectCourseMaxSort(courseId);
        if (l == null) {
            return 0;
        }
        return l;
    }

    public static List<RemoteExtCourseCatalogVo> extractLeafNodes(List<RemoteExtCourseCatalogVo> catalogList) {
        List<RemoteExtCourseCatalogVo> leafNodes = new ArrayList<>();
        for (RemoteExtCourseCatalogVo catalog : catalogList) {
            if (catalog.getChildren() == null || catalog.getChildren().isEmpty()) {
                leafNodes.add(catalog);
            } else {
                leafNodes.addAll(extractLeafNodes(catalog.getChildren()));
            }
        }
        return leafNodes;
    }

    private void recursiveProcessing(Tree<Long> tree, Consumer<Tree<Long>> consumer) {
        List<Tree<Long>> childrenList = tree.getChildren();
        if (childrenList != null && !childrenList.isEmpty()) {
            for (Tree<Long> item : childrenList) {
                consumer.accept(item);
                recursiveProcessing(item, consumer);
            }
        }
    }

    @Override
    public void init() {
    }
}
