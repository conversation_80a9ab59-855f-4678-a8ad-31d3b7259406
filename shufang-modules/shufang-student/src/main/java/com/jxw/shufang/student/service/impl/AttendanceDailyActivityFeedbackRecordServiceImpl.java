package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxw.shufang.common.core.constant.FeedbackStatusConstant;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityFeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityFeedbackRecordVo;
import com.jxw.shufang.student.mapper.AttendanceDailyActivityFeedbackRecordMapper;
import com.jxw.shufang.student.service.AttendanceDailyActivityFeedbackRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 会员每日学习反馈记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 03:26:09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AttendanceDailyActivityFeedbackRecordServiceImpl extends ServiceImpl<AttendanceDailyActivityFeedbackRecordMapper, AttendanceDailyActivityFeedbackRecord> implements AttendanceDailyActivityFeedbackRecordService {


    @Override
    public Integer calculateFeedbackStatus(Date feedbackDate, Boolean isFlag) {
        log.info("反馈日期：{}", feedbackDate);
        if (feedbackDate == null) {
            return null;
        }

        // 根据feedbackDate设置最晚反馈时间
        Calendar cal = Calendar.getInstance();
        cal.setTime(feedbackDate);
        cal.add(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        Date feedbackDeadline = cal.getTime();

        // 获取当前时间
        Date now = new Date();
        if (isFlag) {
            // 判断是否超时反馈
            return now.before(feedbackDeadline) ? FeedbackStatusConstant.HAS_FEEDBACK : FeedbackStatusConstant.OVERTIME_FEEDBACK;
        } else {
            // 判断是否超时未反馈
            return now.before(feedbackDeadline) ? FeedbackStatusConstant.NOT_FEEDBACK : FeedbackStatusConstant.OVERTIME_NOT_FEEDBACK;
        }
    }

    @Override
    public void saveAttendanceDailyActivityFeedbackRecord(AttendanceDailyActivityFeedbackRecordBo bo) {
        AttendanceDailyActivityFeedbackRecord convert = MapstructUtils.convert(bo, AttendanceDailyActivityFeedbackRecord.class);
        this.save(convert);
    }

    @Override
    public void updateByAttendanceDailyActivityFeedbackRecord(AttendanceDailyActivityFeedbackRecordBo bo) {
        this.update(new LambdaUpdateWrapper<AttendanceDailyActivityFeedbackRecord>()
                .eq(bo.getAttendanceDailyActivityId() != null, AttendanceDailyActivityFeedbackRecord::getAttendanceDailyActivityId, bo.getAttendanceDailyActivityId())
                .set(bo.getFeedbackRecordId() != null, AttendanceDailyActivityFeedbackRecord::getFeedbackRecordId, bo.getFeedbackRecordId())
                .set(bo.getFeedbackStatus() != null, AttendanceDailyActivityFeedbackRecord::getFeedbackStatus, bo.getFeedbackStatus())
                .set(bo.getPublishStatus() != null, AttendanceDailyActivityFeedbackRecord::getPublishStatus, bo.getPublishStatus())
        );
    }

    @Override
    public AttendanceDailyActivityFeedbackRecordVo queryByAttendanceDailyActivityId(Long attendanceDailyActivityId) {
        LambdaQueryWrapper<AttendanceDailyActivityFeedbackRecord> eq = new LambdaQueryWrapper<AttendanceDailyActivityFeedbackRecord>()
            .eq(AttendanceDailyActivityFeedbackRecord::getAttendanceDailyActivityId, attendanceDailyActivityId);
        return baseMapper.selectVoOne(eq);
    }

    @Override
    public AttendanceDailyActivityFeedbackRecordVo queryByFeedbackRecordId(Long feedbackRecordId) {
        LambdaQueryWrapper<AttendanceDailyActivityFeedbackRecord> eq = new LambdaQueryWrapper<AttendanceDailyActivityFeedbackRecord>()
            .eq(AttendanceDailyActivityFeedbackRecord::getFeedbackRecordId, feedbackRecordId);
        return baseMapper.selectVoOne(eq);
    }

    @Override
    public void saveBatchAttendanceDailyActivityFeedbackRecord(List<AttendanceDailyActivityFeedbackRecordBo> feedbackList) {
        List<AttendanceDailyActivityFeedbackRecord> convertList = MapstructUtils.convert(feedbackList, AttendanceDailyActivityFeedbackRecord.class);
        baseMapper.insertBatch(convertList);
    }

}
