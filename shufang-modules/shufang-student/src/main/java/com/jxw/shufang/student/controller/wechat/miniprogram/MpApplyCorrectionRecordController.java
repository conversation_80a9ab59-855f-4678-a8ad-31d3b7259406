package com.jxw.shufang.student.controller.wechat.miniprogram;

import cn.hutool.core.util.ObjectUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.ApplyCorrectionRecordVo;
import com.jxw.shufang.student.service.IAiApplyCorrectionRecordService;
import com.jxw.shufang.student.service.IApplyCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 申请批改记录---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/applyCorrectionRecord
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/applyCorrectionRecord")
public class MpApplyCorrectionRecordController extends BaseController {

    private final IApplyCorrectionRecordService applyCorrectionRecordService;

    private final IAiApplyCorrectionRecordService aiApplyCorrectionRecordService;

    /**
     * 查询申请批改记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<ApplyCorrectionRecordVo> list(ApplyCorrectionRecordBo bo, PageQuery pageQuery) {
        if (!LoginHelper.isBranchStaff()) {
            throw new ServiceException("无权访问");
        }
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }

        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");

        return applyCorrectionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 修改申请批改状态
     * @param applyCorrectionRecordId 申请批改记录id
     * @param applyResult 申请结果（1允许 2拒绝 0待审核）
     */
    @RepeatSubmit
    @PutMapping("/updateStatus")
    @Log(title = "修改申请批改状态--小程序端", businessType = BusinessType.UPDATE)
    public R<Void> updateStatus(@NotNull(message = "申请批改记录id不能为空")Long applyCorrectionRecordId,
                                @NotBlank(message = "申请结果不能为空")String applyResult) {
        if (!LoginHelper.isBranchStaff()) {
             return R.fail("无权访问");
        }
        if (!applyResult.equals("1") && !applyResult.equals("2")) {
            return R.fail("申请结果只能为同意或拒绝");
        }
        return toAjax(applyCorrectionRecordService.updateApplyResult(applyCorrectionRecordId, applyResult));
    }

    /**
     * 查询申请批改待审核的数量（规划（测验练习）+ai学习（测验练习））
     */
    @GetMapping("/countWaitAudit")
    public R<Long> countWaitAudit() {
        if (!LoginHelper.isBranchStaff()) {
            return R.fail("无权访问");
        }
        ApplyCorrectionRecordBo bo = new ApplyCorrectionRecordBo();
        bo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        //默认显示今日03:00至次日03:00提交的批改申请
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plus(1, ChronoUnit.DAYS);
        bo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        Long count = applyCorrectionRecordService.count(bo);

        AiApplyCorrectionRecordBo aiApplyCorrectionRecordBo = new AiApplyCorrectionRecordBo();
        aiApplyCorrectionRecordBo.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        aiApplyCorrectionRecordBo.setTimeLimit(today+" 03:00:00 至 "+tomorrow+" 03:00:00");
        Long aiCount = aiApplyCorrectionRecordService.count(aiApplyCorrectionRecordBo);
        return R.ok(ObjectUtil.defaultIfNull(aiCount, 0L)+ObjectUtil.defaultIfNull(count, 0L));
    }
}
