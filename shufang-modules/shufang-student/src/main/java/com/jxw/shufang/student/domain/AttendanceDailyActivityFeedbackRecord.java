package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员每日学习反馈记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 03:26:09
 */
@Data
@Accessors(chain = true)
@TableName("attendance_daily_activity_feedback_record")
public class AttendanceDailyActivityFeedbackRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_daily_activity_feedback_record_id")
    private Long attendanceDailyActivityFeedbackRecordId;

    /**
     * 每日会员打卡登录详情表id
     */
    @TableField("attendance_daily_activity_id")
    private Long attendanceDailyActivityId;

    /**
     * 反馈状态
     */
    @TableField("feedback_status")
    private Integer feedbackStatus;

    /**
     * 发布状态（1：暂存；2：未发布）
     */
    @TableField("publish_status")
    private Integer publishStatus;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 反馈记录表id
     */
    @TableField("feedback_record_id")
    private Long feedbackRecordId;

}
