package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 最近课程分享
 */
@Data
public class StuShareLatestCourseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    private Long courseId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 学科名称
     */
    private String subjectName;
    /**
     * 课次名称
     */
    private String className;
    /**
     * 学习日期
     */
    private Date studyPlanningDate;
    /**
     * 学习开始时间
     */
    private Date studyStartTime;


    /**
     * 学习结束时间
     */
    private Date studyEndTime;

}
