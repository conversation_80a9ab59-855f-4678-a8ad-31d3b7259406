package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.StuCourseResourceCollect;

import java.io.Serial;
import java.io.Serializable;


/**
 * 会员对课程资源的收藏视图对象 stu_course_resource_collect
 *
 *
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StuCourseResourceCollect.class)
public class StuCourseResourceCollectVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 资源收藏id
     */
    @ExcelProperty(value = "资源收藏id")
    private Long resourceCollectId;

    /**
     * 课程资源ID
     */
    @ExcelProperty(value = "课程资源ID")
    private Long courseResourceId;

    /**
     * 会员ID
     */
    @ExcelProperty(value = "会员ID")
    private Long studentId;


}
