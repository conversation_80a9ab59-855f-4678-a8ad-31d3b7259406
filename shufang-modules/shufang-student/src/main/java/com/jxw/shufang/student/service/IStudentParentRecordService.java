package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentParentRecord;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo;
import com.jxw.shufang.student.domain.vo.StudentParentRecordVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）Service接口
 *
 *
 * @date 2024-03-11
 */
public interface IStudentParentRecordService {

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    StudentParentRecordVo queryById(Long studentParentRecordId);

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    TableDataInfo<StudentParentRecordVo> queryPageList(StudentParentRecordBo bo, PageQuery pageQuery);

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    List<StudentParentRecordVo> queryList(StudentParentRecordBo bo);

    /**
     * 新增会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean insertByBo(StudentParentRecordBo bo);

    /**
     * 修改会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean updateByBo(StudentParentRecordBo bo);

    /**
     * 校验并批量删除会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    StudentParentRecord queryStudentParentRecordById(Long studentParentRecordId);

    List<StudentParentRecord> queryStudentParentRecordByIdList(List<Long> studentParentRecordIdList);


    void cleanCache();

    String getBoundQrCode(Long studentId);

    String getFeedbackUrl(Long feedbackId);

    void bind(List<StudentVo> studentVoList, StudentParentRecordBo parentInfo);

    boolean unbindParent(Long studentId);

    List<StudentParentRecordVo> queryBindList(StudentParentRecordBo studentParentRecordBo);

    List<StudyPlanningRecordVo> queryAttendanceRecordList(StudentParentRecordBo studentParentRecordBo);

    TableDataInfo<AttendanceLogStudentEzkecoVo> pageAttendanceRecord(StudentParentRecordBo bo, PageQuery pageQuery);
}
