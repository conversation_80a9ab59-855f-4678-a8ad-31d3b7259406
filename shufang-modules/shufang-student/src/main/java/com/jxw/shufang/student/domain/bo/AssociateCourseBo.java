package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.dto.CourseTreeNodeDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AssociateCourseBo extends BaseEntity {

    @NotNull(message = "父节点不能为空")
    private Long courseParentId;

    private Integer sort;

    @NotEmpty(message = "课程节点不能为空")
    @Valid
    private List<CourseTreeNodeDTO> treeNodes;

}
