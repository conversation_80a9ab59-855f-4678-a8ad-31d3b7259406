package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.AttendanceLogStaffEzkeco;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * ezkeco员工考勤记录视图对象 attendance_log_staff_ezkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AttendanceLogStaffEzkeco.class)
public class AttendanceLogStaffEzkecoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long attendanceLogStaffEzkecoId;

    /**
     * 流水号
     */
    private Long logId;

    /**
     * 验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)
     */
    @ExcelProperty(value = "验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)")
    private String verify;

    /**
     * 打卡时间
     */
    @ExcelProperty(value = "打卡时间")
    private Date checktime;

    /**
     * 设备序列号
     */
    @ExcelProperty(value = "设备序列号")
    private String sn;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String alias;

    /**
     * 人员编号
     */
    @ExcelProperty(value = "人员编号")
    private String pin;

    /**
     * 考勤状态说明
     */
    @ExcelProperty(value = "考勤状态说明")
    private String state;

    /**
     * 考勤关联用户_id
     */
    @ExcelProperty(value = "考勤关联用户_id")
    private Long attendanceUserId;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名")
    private String nickName;

    /**
     * 照片地址
     */
    @ExcelProperty(value = "照片地址")
    private String photograph;

    /**
     * 备注说明
     */
    @ExcelProperty(value = "备注说明")
    private String remark;

    private RemoteStaffVo staff;

}
