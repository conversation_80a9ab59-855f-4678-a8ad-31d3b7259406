package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteExtPaperVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGradeVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.domain.StudentPaperRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 用户答题试卷记录视图对象 student_paper_record
 *
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
public class StudentEtxPaperSyncVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private List<RemoteExtPaperVo> syncList;
    private List<RemoteExtPaperVo> interimList;
    private List<RemoteExtPaperVo> finalList;


}
