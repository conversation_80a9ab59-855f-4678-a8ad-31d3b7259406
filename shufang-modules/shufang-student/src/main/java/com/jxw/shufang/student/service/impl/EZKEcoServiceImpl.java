package com.jxw.shufang.student.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.jxw.shufang.student.domain.bo.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.student.domain.AttendanceUser;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.AttendanceUserVo;
import com.jxw.shufang.student.domain.vo.EZKEcoBaseResponse;
import com.jxw.shufang.student.domain.vo.EZKEcoEmployeeVo;
import com.jxw.shufang.student.domain.vo.EzkEcoTransactionVo;
import com.jxw.shufang.student.enums.AttendanceVerificationMethod;
import com.jxw.shufang.student.service.IAttendanceLogStaffEzkecoService;
import com.jxw.shufang.student.service.IAttendanceLogStudentEzkecoService;
import com.jxw.shufang.student.service.IAttendanceUserService;
import com.jxw.shufang.student.service.IEZKEcoService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

import static com.jxw.shufang.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 *  对接考勤机 Service业务层处理
 * @date 2024-05-18
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class EZKEcoServiceImpl implements IEZKEcoService, BaseService {

    private final DictService dictService;

    private final IAttendanceUserService attendanceUserService;

    private final IAttendanceLogStaffEzkecoService attendanceLogStaffEzkecoService;

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;

    @DubboReference
    private final RemoteFileService remoteFileService;
    @DubboReference
    private final RemoteUserService remoteUserService;

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    /**
     * 人员对接 - 获取人员信息
     *
     * @param config 数据字典 ezkeco考勤机配置信息 ezkeco_ip_key
     * @param ezkEcoEmployeeBo 参数
     * @return EZKEcoEmployeeVo
     */
    @Override
    public EZKEcoBaseResponse<EZKEcoEmployeeVo> getEmployee(String config, EZKEcoEmployeeBo ezkEcoEmployeeBo) {
        String url = "{}/api/v2/employee/get/?key={}";
        // 数据字典中，用:::分割键值对
        String[] split = config.split(":::");
        String formatURL = StringUtils.format(url, split[0], split[1]);
        // 请求接口
        String res = HttpUtil.post(formatURL, JSONUtil.toJsonStr(ezkEcoEmployeeBo));
        return JSONUtil.toBean(res, new TypeReference<EZKEcoBaseResponse<EZKEcoEmployeeVo>>(){}, false);
    }

    /**
     * 记录对接 - 获取考勤记录
     * 单次只能获取一台设备的考勤记录
     *
     * @param config 数据字典 ezkeco考勤机配置信息 ezkeco_ip_key
     * @param ezkEcoTransactionRequestBo 参数
     * @return EZKEcoTransactionVo
     */
    @Override
    public EZKEcoBaseResponse<EzkEcoTransactionVo> getTransaction(String config, EzkEcoTransactionRequestBo ezkEcoTransactionRequestBo) {
        String url = "{}/api/v2/transaction/get/?key={}";
        // 数据字典中，用:::分割键值对
        String[] split = config.split(":::");
        String formatURL = StringUtils.format(url, split[0], split[1]);
        // 请求接口
        String res = HttpUtil.post(formatURL, JSONUtil.toJsonStr(ezkEcoTransactionRequestBo));
        EZKEcoBaseResponse<EzkEcoTransactionVo> response = JSONUtil.toBean(res, new TypeReference<EZKEcoBaseResponse<EzkEcoTransactionVo>>() {
        }, false);

        // verify别名转换
        ListUtil.toList(response.getData().getItems()).forEach(item -> {
            String description = AttendanceVerificationMethod.getDescriptionByCode(Integer.parseInt(item.getVerify()));
            item.setVerify(description);
        });
        return response;
    }

    /**
     * 记录对接 - 手动同步考勤机信息
     * @param ezkEcoTransactionRequestBo  手动同步参数
     * @return EZKEcoBaseResponse<ManualSyncResVo>
     */
    @Override
    public Integer manualSyncAllAttendanceLog(EzkEcoTransactionRequestBo ezkEcoTransactionRequestBo) {
        // 定义考勤机和系统的关联的字段，后期只需要改这个就行
        final Function<EzkEcoTransactionVo, String> getRelaFieldFunc = EzkEcoTransactionVo::getPin;
        // 用来存放考勤机的同步条数
        AtomicInteger count = new AtomicInteger(0);
        // 设置 当前时间 作为结束时间
        final Date nowDate = DateUtils.getNowDate();
        // 兜底设置
        if (null == ezkEcoTransactionRequestBo.getStarttime()) {
            // 计算出来的开始时间 兜底默认当前时间向前推一天
            ezkEcoTransactionRequestBo.setStarttime(
                DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, DateUtils.addDays(nowDate, -1))
            );
        }
        if (null == ezkEcoTransactionRequestBo.getEndtime()) {
            ezkEcoTransactionRequestBo.setEndtime(DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, nowDate));
        }
        if (null == ezkEcoTransactionRequestBo.getNumber()) {
            ezkEcoTransactionRequestBo.setNumber(2000);
        }
        // 获取数据字典的考勤机配置信息
        Map<String, String> ezkecoIpKey = dictService.getAllDictByDictType("ezkeco_ip_key");
        // 取出所有的考勤机配置
        Set<String> ipKeys = ezkecoIpKey.keySet();
        // 循环遍历考勤机
        ipKeys.forEach(config -> {
            // 查询最新的时间
            AttendanceUserBo bo = new AttendanceUserBo();
            // 指定考勤机IPKey配置
            bo.setIp(config);
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageSize(2);
            pageQuery.setOrderByColumn("update_time");
            pageQuery.setIsAsc("desc");
            AttendanceUser attendanceUser = CollectionUtil.get(
                attendanceUserService.queryDataPageList(bo, pageQuery).getRows(), 0);
            // 如果查到数据则取数据库里的最新时间最为查询条件的开始时间
            if (attendanceUser != null) {
                ezkEcoTransactionRequestBo.setStarttime(
                    DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, attendanceUser.getUpdateTime())
                );
            }
            // 获取这个IP + Key下的所有设备的考勤记录
            List<EzkEcoTransactionVo> transactionVoList = this.getTransaction(config, new EzkEcoTransactionRequestBo()).getDataItems();
            // 用于接口返回的同步数
            count.addAndGet(transactionVoList.size());
            // 分区处理数据
            List<List<EzkEcoTransactionVo>> partitionLog = ListUtil.partition(transactionVoList, 100);
            partitionLog.forEach(ecoTransactionVos -> {
                // 目前先认为这个pin是关联系统的字段。暂时认为它存的是手机号。从考勤机接口中拿到关联字段列表
                List<String> relaFieldList = StreamUtils.toList(ecoTransactionVos, getRelaFieldFunc);
                // 远程通过用户名称（一般都是手机号）
                RemoteUserBo userBo = new RemoteUserBo();
                userBo.setUserNames(relaFieldList);
                Map<String, RemoteUserVo> remoteUserVoMap = StreamUtils.toMap(
                    remoteUserService.queryUserList(userBo, true), RemoteUserVo::getUserName, remoteUserVo -> remoteUserVo);
                // 用户id集合
                List<Long> userIdList = StreamUtils.toList(remoteUserVoMap.values(), RemoteUserVo::getUserId);
                // 构建查询条件
                AttendanceUserBo queryAttendanceUserBo = new AttendanceUserBo();
                queryAttendanceUserBo.setUserIds(userIdList);
                // userId、attendanceUserId键值对查询
                Map<Long, Long> attendanceUserIdMap = StreamUtils.toMap(
                    attendanceUserService.queryList(queryAttendanceUserBo), AttendanceUserVo::getUserId, AttendanceUserVo::getAttendanceUserId);
                // 员工考勤记录
                List<AttendanceLogStaffEzkecoBo> staffEzkecoList = new CopyOnWriteArrayList<>();
                // 学员考勤记录
                List<AttendanceLogStudentEzkecoBo> studentEzkecoList = new CopyOnWriteArrayList<>();
                // 上传考勤图片
                Map<String, String> photographMap = MapUtil.newConcurrentHashMap(
                    StreamUtils.toMap(ecoTransactionVos, getRelaFieldFunc, EzkEcoTransactionVo::getPhotograph));
                // 存放在上传考勤图片的结果
                Map<String, String> newPhotographMap = MapUtil.newConcurrentHashMap();
                // 处理考勤图片
                photographMap.forEach((pin, photograph) -> {
                    // 解析图片
                    byte[] photoBytes = Base64.decode(photograph);
                    // 上传图片到OSS服务
                    RemoteFile uploadedResult = remoteFileService.upload(UUID.fastUUID().toString(true) + ".jpg"
                        , UUID.fastUUID().toString(true) + ".jpg"
                        , "application/octet-stream"
                        , photoBytes);
                    newPhotographMap.put(pin, uploadedResult.getUrl());
                });
                // 遍历接口返回的考勤列表
                ecoTransactionVos.forEach(transactionVo -> {
                    // 关联系统的重要字段
                    RemoteUserVo userVo = remoteUserVoMap.get(getRelaFieldFunc.apply(transactionVo));
                    // 关联字段
                    String relaField = getRelaFieldFunc.apply(transactionVo);
                    // 用户类型
                    switch (UserType.getUserType(userVo.getUserType())) {
                        // 员工
                        case SYS_USER:
                            AttendanceLogStaffEzkecoBo attendanceLogStaffEzkeco = new AttendanceLogStaffEzkecoBo();
                            attendanceLogStaffEzkeco.setLogId(transactionVo.getId());
                            attendanceLogStaffEzkeco.setVerify(transactionVo.getVerify());
                            attendanceLogStaffEzkeco.setChecktime(DateUtils.parseDate(transactionVo.getChecktime()));
                            attendanceLogStaffEzkeco.setSn(transactionVo.getSn());
                            attendanceLogStaffEzkeco.setAlias(transactionVo.getAlias());
                            attendanceLogStaffEzkeco.setPin(relaField);
                            attendanceLogStaffEzkeco.setState(transactionVo.getState());
                            attendanceLogStaffEzkeco.setAttendanceUserId(attendanceUserIdMap.get(userVo.getUserId()));
                            attendanceLogStaffEzkeco.setCreateTime(nowDate);
                            attendanceLogStaffEzkeco.setUpdateTime(nowDate);
                            attendanceLogStaffEzkeco.setUserId(userVo.getUserId());
                            attendanceLogStaffEzkeco.setNickName(userVo.getNickName());
                            attendanceLogStaffEzkeco.setPhotograph(newPhotographMap.get(relaField));

                            staffEzkecoList.add(attendanceLogStaffEzkeco);
                            break;
                        // 学生、学员
                        case APP_STU_USER:
                            AttendanceLogStudentEzkecoBo attendanceLogStudentEzkeco = new AttendanceLogStudentEzkecoBo();
                            attendanceLogStudentEzkeco.setLogId(transactionVo.getId());
                            attendanceLogStudentEzkeco.setVerify(transactionVo.getVerify());
                            attendanceLogStudentEzkeco.setChecktime(DateUtils.parseDate(transactionVo.getChecktime()));
                            attendanceLogStudentEzkeco.setSn(transactionVo.getSn());
                            attendanceLogStudentEzkeco.setAlias(transactionVo.getAlias());
                            attendanceLogStudentEzkeco.setPin(relaField);
                            attendanceLogStudentEzkeco.setState(transactionVo.getState());
                            attendanceLogStudentEzkeco.setAttendanceUserId(attendanceUserIdMap.get(userVo.getUserId()));
                            attendanceLogStudentEzkeco.setUserId(userVo.getUserId());
                            attendanceLogStudentEzkeco.setNickName(userVo.getNickName());
                            attendanceLogStudentEzkeco.setPhotograph(newPhotographMap.get(relaField));
                            attendanceLogStudentEzkeco.setCreateTime(nowDate);
                            attendanceLogStudentEzkeco.setUpdateTime(nowDate);
                            attendanceLogStudentEzkeco.setBindParents("N");
                            attendanceLogStudentEzkeco.setNotifyParents("N");

                            studentEzkecoList.add(attendanceLogStudentEzkeco);
                            break;
                    }
                });

                // 批量插入数据
                attendanceLogStudentEzkecoService.insertBatch(studentEzkecoList);
                attendanceLogStaffEzkecoService.insertBatch(staffEzkecoList);
            });
        });

        return count.intValue();
    }
}

