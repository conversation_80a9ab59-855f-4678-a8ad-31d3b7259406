package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.StudentAiCourseRecord;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）视图对象 student_ai_course_record
 *
 *
 * @date 2024-05-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentAiCourseRecord.class)
public class StudentAiCourseRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员AI课程分配记录表id
     */
    @ExcelProperty(value = "会员AI课程分配记录表id")
    private Long studentAiCourseRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    private RemoteUserVo sysUser;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

}
