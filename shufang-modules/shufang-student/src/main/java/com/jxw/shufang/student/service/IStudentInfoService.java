package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentInfo;
import com.jxw.shufang.student.domain.bo.StudentInfoBo;
import com.jxw.shufang.student.domain.vo.StudentInfoVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）Service接口
 *
 *
 * @date 2024-03-11
 */
public interface IStudentInfoService {

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    StudentInfoVo queryById(Long studentInfoId);

    /**
     * 查询会员信息
     */
    StudentInfoVo queryByStudentId(Long studentId);

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    TableDataInfo<StudentInfoVo> queryPageList(StudentInfoBo bo, PageQuery pageQuery);

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    List<StudentInfoVo> queryList(StudentInfoBo bo);

    /**
     * 新增会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    Boolean insertByBo(StudentInfoBo bo);



    /**
     * 修改会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    Boolean updateByBo(StudentInfoBo bo);

    /**
     * 校验并批量删除会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    StudentInfo queryStudentInfoById(Long studentInfoId);

    void cleanCache();

    /**
     * 更新快叮岛权益数据
     *
     * @param studentIdList
     * @param hasKuaidingPrivilege
     * @param kuaidingPrivilegeExpireTime
     * @return
     */
    boolean updateKuaidingPrivilege(Collection<Long> studentIdList, Boolean hasKuaidingPrivilege,
        Date kuaidingPrivilegeExpireTime);
}
