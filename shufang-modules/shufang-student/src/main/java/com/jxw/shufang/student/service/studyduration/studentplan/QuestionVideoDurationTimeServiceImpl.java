package com.jxw.shufang.student.service.studyduration.studentplan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import com.jxw.shufang.student.domain.dto.BatchQueryQuestionRecordDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateQuestionProcessDTO;
import com.jxw.shufang.student.domain.dto.StudySlicesProcessingContextDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.studyduration.AbstractQuestionDurationTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/7 16:40
 * @Version 1
 * @Description
 */
@Service
@Slf4j
public class QuestionVideoDurationTimeServiceImpl extends AbstractQuestionDurationTime<QuestionVideoRecordBo> {
    @Override
    public List<QuestionVideoRecordBo> filterData(List<QuestionVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return filterInvalidMessage(records);
    }

    @Override
    public StudySlicesProcessingContextDTO contextData(List<QuestionVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<Long> studentIds = records.stream().map(QuestionVideoRecordBo::getStudentId).toList();
        List<Long> videoIds = records.stream().map(QuestionVideoRecordBo::getVideoId).filter(Objects::nonNull).toList();
        List<Long> studyPlanningRecordIds = records.stream().map(QuestionVideoRecordBo::getStudyPlanningRecordId).toList();

        StudySlicesProcessingContextDTO contextData = new StudySlicesProcessingContextDTO();
        contextData.setExistQuestionVideoRecordMap(this.getStudenQuestionVideoRecordMap(records));
        contextData.setStudentMap(super.studentMap(studentIds));
        contextData.setExistStudyRecordMap(super.studyRecordMap(studyPlanningRecordIds));
        contextData.setCourseDurationMap(super.courseDurationMap(videoIds));
        contextData.setStudyModuleTypeEnum(moduleAndGroupEnum);

        this.checkRepeatRecord(records, contextData);
        return contextData;
    }

    private void checkRepeatRecord(List<QuestionVideoRecordBo> records, StudySlicesProcessingContextDTO contextData) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        QuestionVideoRecordBo videoRecordBo = records.get(0);

        String studyVideoSlices = videoRecordBo.getStudyVideoSlices();
        List<QuestionVideoRecord> existQuestionVideoRecords = contextData.getExistQuestionVideoRecordMap().get(videoRecordBo.getStudentId());
        if (CollectionUtils.isEmpty(existQuestionVideoRecords)) {
            return;
        }
        List<QuestionVideoRecord> repeatSlicesList = existQuestionVideoRecords.stream()
            .filter(studyVideoRecord -> VideoSlicesUtils.ignoreRepeatSlice(studyVideoRecord.getStudyVideoSlices(), studyVideoSlices))
            .toList();
        if (CollectionUtil.isNotEmpty(repeatSlicesList)) {
            contextData.setIgnoreStudyVideoRecord(true);
        }
    }

    @Override
    public SaveOrUpdateQuestionProcessDTO buildQuestionProcessDTO(QuestionVideoRecordBo recordBo) {
        SaveOrUpdateQuestionProcessDTO processDTO = new SaveOrUpdateQuestionProcessDTO();
        processDTO.setModuleGroup(recordBo.getStudyModuleType().getGroupEnum().getGroupCode());
        processDTO.setStudentId(recordBo.getStudentId());
        processDTO.setCourseId(recordBo.getCourseId());
        processDTO.setVideoId(recordBo.getVideoId());
        processDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        processDTO.setCommitTime(recordBo.getCommitTime());
        processDTO.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        processDTO.setQuestionId(recordBo.getQuestionId());
        processDTO.setQuestionType(recordBo.getQuestionType());
        processDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        return processDTO;
    }

    @Override
    public SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(QuestionVideoRecordBo recordBo) {
        SaveOrUpdateStudyRecordDTO studyRecordDTO = new SaveOrUpdateStudyRecordDTO();
        studyRecordDTO.setCourseId(recordBo.getCourseId());
        studyRecordDTO.setStudentId(recordBo.getStudentId());
        studyRecordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        studyRecordDTO.setCommitTime(recordBo.getCommitTime());
        studyRecordDTO.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        studyRecordDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        studyRecordDTO.setVideoId(recordBo.getVideoId());
        return studyRecordDTO;
    }

    private Map<Long, List<QuestionVideoRecord>> getStudenQuestionVideoRecordMap(List<QuestionVideoRecordBo> records) {
        List<BatchQueryQuestionRecordDTO> batchQueryQuestionRecordList = records.stream()
            .map(QuestionVideoDurationTimeServiceImpl::convertToBatchQueryQuestionRecordDTO)
            .toList();
        return super.studentQuestionVideoRecordMap(batchQueryQuestionRecordList);
    }

    private static BatchQueryQuestionRecordDTO convertToBatchQueryQuestionRecordDTO(QuestionVideoRecordBo questionVideoRecordBo) {
        BatchQueryQuestionRecordDTO batchQueryQuestionRecordDTO = new BatchQueryQuestionRecordDTO();
        batchQueryQuestionRecordDTO.setStudentId(questionVideoRecordBo.getStudentId());
        batchQueryQuestionRecordDTO.setQuestionType(questionVideoRecordBo.getQuestionType());
        batchQueryQuestionRecordDTO.setQuestionId(questionVideoRecordBo.getQuestionId());
        batchQueryQuestionRecordDTO.setModuleType(questionVideoRecordBo.getStudyModuleType().getGroupEnum().getGroupCode());
        batchQueryQuestionRecordDTO.setStudyPlanningRecordId(questionVideoRecordBo.getStudyPlanningRecordId());
        batchQueryQuestionRecordDTO.setCourseId(questionVideoRecordBo.getCourseId());
        batchQueryQuestionRecordDTO.setVideoId(questionVideoRecordBo.getVideoId());
        return batchQueryQuestionRecordDTO;
    }

    private List<QuestionVideoRecordBo> filterInvalidMessage(List<QuestionVideoRecordBo> questionVideoRecords) {
        return questionVideoRecords.stream().filter(Objects::nonNull)
            .filter(f -> null != f.getStudyPlanningRecordId())
            .filter(f -> null != f.getCourseId())
            .filter(f -> null != f.getVideoId())
            .filter(f -> null != f.getQuestionId())
            .filter(f -> StrUtil.isNotEmpty(f.getQuestionType()))
            .filter(f -> StringUtils.equalsAny(f.getQuestionType(), "1", "2"))
            .collect(Collectors.toList());
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return List.of(StudyModelGroupEnum.STUDY_PLANNING, StudyModelGroupEnum.AI_STUDY);
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return List.of(StudyModuleTypeEnum.PRACTICE, StudyModuleTypeEnum.TEST);
    }
}
