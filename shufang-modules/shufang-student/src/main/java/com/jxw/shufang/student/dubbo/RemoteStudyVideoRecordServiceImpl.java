package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudyVideoRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyVideoRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyVideoRecordVo;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;
import com.jxw.shufang.student.service.IStudyVideoRecordService;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudyVideoRecordServiceImpl implements RemoteStudyVideoRecordService {

    private final IStudyVideoRecordService studyVideoRecordService;

    @Override
    public List<RemoteStudyVideoRecordVo> queryList(RemoteStudyVideoRecordBo remoteStudyVideoRecordBo, boolean ignoreDataPermission) {
        StudyVideoRecordBo convert = MapstructUtils.convert(remoteStudyVideoRecordBo, StudyVideoRecordBo.class);
        List<StudyVideoRecordVo> list = null;
        if (ignoreDataPermission) {
            list = DataPermissionHelper.ignore(() -> studyVideoRecordService.queryList(convert));
        } else {
            list = studyVideoRecordService.queryList(convert);
        }

        return MapstructUtils.convert(list, RemoteStudyVideoRecordVo.class);
    }

    @Override
    public Long count(RemoteStudyVideoRecordBo remoteStudyVideoRecordBo, boolean ignoreDataPermission) {
        StudyVideoRecordBo convert = MapstructUtils.convert(remoteStudyVideoRecordBo, StudyVideoRecordBo.class);
        Long count = null;
        if (ignoreDataPermission) {
            count = DataPermissionHelper.ignore(() -> studyVideoRecordService.accumulateStudyVideo(convert));
        } else {
            count = studyVideoRecordService.accumulateStudyVideo(convert);
        }

        return count;
    }
}
