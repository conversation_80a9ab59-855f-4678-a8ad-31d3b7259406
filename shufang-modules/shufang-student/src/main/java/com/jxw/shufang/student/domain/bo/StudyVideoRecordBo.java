package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 学习视频记录（视频观看记录）业务对象 study_video_record
 *
 *
 * @date 2024-05-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyVideoRecord.class, reverseConvertGenerate = false)
public class StudyVideoRecordBo extends BaseEntity {

    /**
     * 学习视频记录id
     */
    @NotNull(message = "学习视频记录id不能为空", groups = { EditGroup.class })
    private Long studyVideoRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 学习规划记录Id
     */
    @NotNull(message = "学习规划记录Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 对应外部资源的视频Id
     */
    @NotNull(message = "对应外部资源的视频Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long videoId;

    /**
     * 章节ID
     */
    @NotNull(message = "章节ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 播放时长（单位秒 按日累加）
     */
    @NotNull(message = "播放时长（单位秒 按日累加）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyVideoDuration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    @NotBlank(message = "分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyVideoSlices;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date videoRecordCreateDateStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date videoRecordCreateDateEnd;


    /**
     * 创建时间(日期)列表
     */
    private List<String> createDateList;

    /**
     * 记录提交时间，与数据库无关
     */
    private Date commitTime;

    private List<Long> studentIdList;

    /**
     * 非需要展示情况下，是否需要展示分片值字段
     */
    Boolean nonSelectStudyVideoSlicesField;


    /**
     * 大于等于studyVideoDuration
     */
    private Long geStudyVideoDuration;

    private List<Long> studyPlanningRecordIdList;

    /**
     * 学习记录状态（0正常 1覆盖 2删除 ）
     */
    private String studyRecordStatus;

    /**
     * 学习模块类型
     * {@link StudyModuleAndGroupEnum}
     */
    private StudyModuleAndGroupEnum studyModuleType;

    /**
     * 评测试卷ID
     */
    private Long testPaperId;
}
