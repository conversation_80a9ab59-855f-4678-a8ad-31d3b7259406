package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudyPlanning;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 学习规划视图对象 study_planning
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanning.class)
public class StudyPlanningVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划id
     */
    @ExcelProperty(value = "学习规划id")
    private Long studyPlanningId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 学习规划日期
     */
    @ExcelProperty(value = "学习规划日期")
    private Date studyPlanningDate;

    /**
     * 学习规划保存状态（1暂存 2完成）
     */
    @ExcelProperty(value = "学习规划保存状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=暂存,2=完成")
    private String studyPlanningStatus;


    private List<StudyPlanningRecordVo> studyPlanningRecordList;

    private String week;

    /**
     * 会员信息
     */
    private StudentVo student;

    /**
     * 反馈记录
     */
    private List<FeedbackRecordVo> feedbackRecordList;

    /**
     * 会员总数
     */
    private Long studentCount;

    /**
     * 学习记录总数
     */
    private Long recordCount;

    //响应应带上系统时间戳
    public long getTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 练习批改记录
     */
    private CorrectionRecordVo practiceCorrectionRecord;

    /**
     * 测试批改记录
     */
    private CorrectionRecordVo testCorrectionRecord;
    private CorrectionRecordVo previewCorrectionRecord;
    private CorrectionRecordVo speakCorrectionRecord;

}
