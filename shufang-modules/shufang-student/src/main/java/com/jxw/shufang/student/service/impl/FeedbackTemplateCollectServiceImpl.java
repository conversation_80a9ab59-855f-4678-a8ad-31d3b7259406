package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.FeedbackTemplateCollect;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateCollectBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateCollectVo;
import com.jxw.shufang.student.mapper.FeedbackTemplateCollectMapper;
import com.jxw.shufang.student.service.IFeedbackTemplateCollectService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 反馈模板收藏Service业务层处理
 *
 *
 * @date 2024-05-24
 */
@RequiredArgsConstructor
@Service
public class FeedbackTemplateCollectServiceImpl implements IFeedbackTemplateCollectService, BaseService {

    private final FeedbackTemplateCollectMapper baseMapper;

    /**
     * 查询反馈模板收藏
     */
    @Override
    public FeedbackTemplateCollectVo queryById(Long feedbackTemplateCollectId){
        return baseMapper.selectVoById(feedbackTemplateCollectId);
    }

    /**
     * 查询反馈模板收藏列表
     */
    @Override
    public TableDataInfo<FeedbackTemplateCollectVo> queryPageList(FeedbackTemplateCollectBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FeedbackTemplateCollect> lqw = buildQueryWrapper(bo);
        Page<FeedbackTemplateCollectVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询反馈模板收藏列表
     */
    @Override
    public List<FeedbackTemplateCollectVo> queryList(FeedbackTemplateCollectBo bo) {
        LambdaQueryWrapper<FeedbackTemplateCollect> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FeedbackTemplateCollect> buildQueryWrapper(FeedbackTemplateCollectBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FeedbackTemplateCollect> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getFeedbackTemplateId() != null, FeedbackTemplateCollect::getFeedbackTemplateId, bo.getFeedbackTemplateId());
        return lqw;
    }

    /**
     * 新增反馈模板收藏
     */
    @Override
    public Boolean insertByBo(FeedbackTemplateCollectBo bo) {
        FeedbackTemplateCollect add = MapstructUtils.convert(bo, FeedbackTemplateCollect.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFeedbackTemplateCollectId(add.getFeedbackTemplateCollectId());
        }
        return flag;
    }

    /**
     * 修改反馈模板收藏
     */
    @Override
    public Boolean updateByBo(FeedbackTemplateCollectBo bo) {
        FeedbackTemplateCollect update = MapstructUtils.convert(bo, FeedbackTemplateCollect.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FeedbackTemplateCollect entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除反馈模板收藏
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Long> queryTemplateIdListByCreateUserId(Long userId) {
        LambdaQueryWrapper<FeedbackTemplateCollect> select = Wrappers.lambdaQuery(FeedbackTemplateCollect.class)
            .eq(FeedbackTemplateCollect::getCreateBy, userId)
            .select(FeedbackTemplateCollect::getFeedbackTemplateId);
        return baseMapper.selectObjs(select, x -> {
            return Convert.toLong(x);
        });
    }

    @Override
    public Boolean collect(Long feedbackTemplateId, Long userId) {
        //如果存在，则为取消收藏
        LambdaQueryWrapper<FeedbackTemplateCollect> select = Wrappers.lambdaQuery(FeedbackTemplateCollect.class)
            .eq(FeedbackTemplateCollect::getFeedbackTemplateId, feedbackTemplateId)
            .eq(FeedbackTemplateCollect::getCreateBy, userId);
        List<FeedbackTemplateCollect> feedbackTemplateCollects = baseMapper.selectList(select);
        if(CollUtil.isNotEmpty(feedbackTemplateCollects)){
            return deleteWithValidByIds(feedbackTemplateCollects.stream().map(FeedbackTemplateCollect::getFeedbackTemplateCollectId).toList(), true);
        }
        FeedbackTemplateCollectBo bo = new FeedbackTemplateCollectBo();
        bo.setFeedbackTemplateId(feedbackTemplateId);
        bo.setCreateBy(userId);
        return insertByBo(bo);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
