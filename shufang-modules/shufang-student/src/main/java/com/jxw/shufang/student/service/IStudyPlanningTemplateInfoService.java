package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateInfoBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 学习规划模板详情Service接口
 *
 *
 * @date 2024-06-14
 */
public interface IStudyPlanningTemplateInfoService {

    /**
     * 查询学习规划模板详情
     */
    StudyPlanningTemplateInfoVo queryById(Long studyPlanningTemplateInfoId);

    /**
     * 查询学习规划模板详情列表
     */
    TableDataInfo<StudyPlanningTemplateInfoVo> queryPageList(StudyPlanningTemplateInfoBo bo, PageQuery pageQuery);

    /**
     * 查询学习规划模板详情列表
     */
    List<StudyPlanningTemplateInfoVo> queryList(StudyPlanningTemplateInfoBo bo);

    /**
     * 新增学习规划模板详情
     */
    Boolean insertByBo(StudyPlanningTemplateInfoBo bo);

    /**
     * 修改学习规划模板详情
     */
    Boolean updateByBo(StudyPlanningTemplateInfoBo bo);

    /**
     * 校验并批量删除学习规划模板详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    boolean insertBatchByBo(List<StudyPlanningTemplateInfoBo> studyPlanningTemplateInfoBoList);

    boolean deleteByStudyPlanningTemplateId(Long studyPlanningTemplateId);

}
