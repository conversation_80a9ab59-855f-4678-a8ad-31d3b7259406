package com.jxw.shufang.student.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考勤记录查询参数实体类 EZKEcoTransactionBo
 *
 * <AUTHOR>
 * @date 2024-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EZKEcoTransactionBo {

    /**
     * 调用接口凭证
     */
    private String key;

    /**
     * 开始时间
     * <p>开始时间结束时间跨度不得超过一个月</p>
     * <p>格式：%Y-%m-%d %H:%M:%S</p>
     * <p>示例：2018-04-25 00:00:01</p>
     */
    private String starttime;

    /**
     * 结束时间
     * <p>格式：%Y-%m-%d %H:%M:%S</p>
     * <p>示例：2018-04-25 23:00:01</p>
     */
    private String endtime;

    /**
     * 人员编号（可选）
     */
    private String pin;

    /**
     * 设备序列号（可选）
     */
    private String sn;

    /**
     * 记录流水号（可选）
     *
     * <p>建议同步考勤记录到第三方数据库时使用id</p>
     * <p>记录流水号自动递增</p>
     * <p>存在id或number时，开始时间和结束时间可为非必选参数</p>
     *
     */
    private Long id;

    /**
     * 每次请求获取的记录数（可选）
     * <p>
     * 建议控制在每次2000条以内
     * </p>
     */
    private Integer number;

    /**
     * 是否传考勤照片（可选）
     * <p>
     * 1 表示为是
     * 0 表示为否
     * </p>
     */
    private Integer uploadPic;

    /**
     * 是否传体温与是否佩戴口罩（可选）
     * <p>
     * 1 表示为是
     * 0 表示为否
     * </p>
     */
    private Integer getTemperature;
}
