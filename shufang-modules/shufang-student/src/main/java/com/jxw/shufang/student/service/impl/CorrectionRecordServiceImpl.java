package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.AnswerResultTypeEnum;
import com.jxw.shufang.common.core.enums.CorrectionStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.student.domain.CorrectionRecord;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.CorrectionRecordMapper;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.jxw.shufang.common.core.constant.UserConstants.*;

/**
 * 批改记录Service业务层处理
 *
 *
 * @date 2024-05-09
 */
@RequiredArgsConstructor
@Service
public class CorrectionRecordServiceImpl implements ICorrectionRecordService, BaseService {

    private final CorrectionRecordMapper baseMapper;

    private final ICorrectionRecordInfoService correctionRecordInfoService;

    private final ITestRecordService testRecordService;

    private final IPracticeRecordService practiceRecordService;

    private final IWrongQuestionRecordService wrongQuestionRecordService;

    private final IStudyRecordService studyRecordService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IAiCorrectionRecordService aiCorrectionRecordService;

    private final IAiStudyRecordService aiStudyRecordService;

    @DubboReference(mock = "true")
    private RemoteFileService ossService;


    /**
     * 查询批改记录
     */
    @Override
    public CorrectionRecordVo queryById(Long correctionRecordId) {
        return baseMapper.selectVoById(correctionRecordId);
    }

    /**
     * 查询批改记录列表
     */
    @Override
    public TableDataInfo<CorrectionRecordVo> queryPageList(CorrectionRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        Page<CorrectionRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询批改记录列表
     */
    @Override
    public List<CorrectionRecordVo> queryList(CorrectionRecordBo bo) {
        LambdaQueryWrapper<CorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CorrectionRecord> buildLambdaQueryWrapper(CorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CorrectionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudyPlanningRecordId() != null, CorrectionRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionType()), CorrectionRecord::getCorrectionType, bo.getCorrectionType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionPersonType()), CorrectionRecord::getCorrectionPersonType, bo.getCorrectionPersonType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionScreenshots()), CorrectionRecord::getCorrectionScreenshots, bo.getCorrectionScreenshots());
        lqw.between(bo.getCorrectionRecordCreateTimeStart() != null && bo.getCorrectionRecordCreateTimeEnd() != null, CorrectionRecord::getCreateTime, bo.getCorrectionRecordCreateTimeStart(), bo.getCorrectionRecordCreateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), CorrectionRecord::getStudentId, bo.getStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getCorrectionTypeList()), CorrectionRecord::getCorrectionType, bo.getCorrectionTypeList());
        return lqw;
    }

    private QueryWrapper<CorrectionRecord> buildQueryWrapper(CorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<CorrectionRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudyPlanningRecordId() != null, "t.study_planning_record_id", bo.getStudyPlanningRecordId());
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), "t.study_planning_record_id", bo.getStudyPlanningRecordIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionType()), "t.correction_type", bo.getCorrectionType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionPersonType()), "t.correction_person_type", bo.getCorrectionPersonType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionScreenshots()), "t.correction_screenshots", bo.getCorrectionScreenshots());
        lqw.between(bo.getCorrectionRecordCreateTimeStart() != null && bo.getCorrectionRecordCreateTimeEnd() != null, "t.create_time", bo.getCorrectionRecordCreateTimeStart(), bo.getCorrectionRecordCreateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getCorrectionTypeList()), "t.correction_type", bo.getCorrectionTypeList());
        return lqw;
    }

    /**
     * 新增批改记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal insertByBo(CorrectionRecordBo bo) {
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(bo.getStudyPlanningRecordId());
        if (studyPlanningRecordVo == null) {
            throw new ServiceException("未找到学习规划记录");
        }
        if (bo.getStudentId() == null) {
            bo.setStudentId(studyPlanningRecordVo.getStudentId());
        }
        //查下有没有已存在的，不允许重复提交
        LambdaQueryWrapper<CorrectionRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq( CorrectionRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        wrapper.eq(CorrectionRecord::getStudentId, bo.getStudentId());
        wrapper.eq(CorrectionRecord::getCorrectionType, bo.getCorrectionType());
        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("请勿重复提交");
        }
        List<CorrectionRecordInfoBo> correctionRecordInfoBoList = bo.getCorrectionRecordInfoBoList();
        if (CollUtil.isEmpty(correctionRecordInfoBoList)) {
            throw new ServiceException("批改记录题目结果不能为空");
        }
        if (bo.getStudentId() == null) {
            throw new ServiceException("会员ID不能为空");
        }

        if (!CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType()) && !CORRECTION_TYPE_TEST.equals(bo.getCorrectionType())) {
            throw new ServiceException("批改类型错误");
        }

        CorrectionRecord add = MapstructUtils.convert(bo, CorrectionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("新增批改记录失败");
        }
        bo.setCorrectionRecordId(add.getCorrectionRecordId());

        for (CorrectionRecordInfoBo correctionRecordInfoBo : correctionRecordInfoBoList) {
            correctionRecordInfoBo.setCorrectionRecordId(bo.getCorrectionRecordId());
        }

        Boolean b = correctionRecordInfoService.insertBatchByBo(correctionRecordInfoBoList);
        if (!b) {
            throw new ServiceException("新增批改记录详情失败");
        }

        //同步新增练习或者考试记录
        if (CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType())) {
            List<PracticeRecordBo> convert = MapstructUtils.convert(correctionRecordInfoBoList, PracticeRecordBo.class);
            convert.forEach(item -> {
                item.setStudyPlanningRecordId(bo.getStudyPlanningRecordId());
                item.setStudentId(bo.getStudentId());

            });
            Boolean b2 = practiceRecordService.insertBatchByBo(convert);
            if (!b2) {
                throw new ServiceException("新增练习记录失败");
            }
        } else {
            List<TestRecordBo> convert = MapstructUtils.convert(correctionRecordInfoBoList, TestRecordBo.class);
            convert.forEach(item -> {
                item.setStudyPlanningRecordId(bo.getStudyPlanningRecordId());
                item.setStudentId(bo.getStudentId());
            });
            Boolean b2 = testRecordService.insertBatchByBo(convert);
            if (!b2) {
                throw new ServiceException("新增测试记录失败");
            }
        }
        //错题记录增加
        //过滤出错的题目
        List<CorrectionRecordInfoBo> wrongQuestionList = correctionRecordInfoBoList.stream().filter(item -> AnswerResultTypeEnum.ALL_WRONG.getType().equals(item.getAnswerResult()) || AnswerResultTypeEnum.HALF_WRONG.getType().equals(item.getAnswerResult())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(wrongQuestionList)) {
            List<WrongQuestionRecordBo> wrongList = MapstructUtils.convert(wrongQuestionList, WrongQuestionRecordBo.class);
            wrongList.forEach(item -> {
                item.setCourseId(studyPlanningRecordVo.getCourseId());
                item.setStudentId(studyPlanningRecordVo.getStudentId());
                item.setStudyPlanningRecordId(studyPlanningRecordVo.getStudyPlanningRecordId());
                item.setSourceType(bo.getCorrectionType());
            });
            Boolean b3 = wrongQuestionRecordService.insertBatchByBo(wrongList);
            if (!b3) {
                throw new ServiceException("新增错题记录失败");
            }
        }

        //过滤出答题正确的
        List<CorrectionRecordInfoBo> rightQuestionList = correctionRecordInfoBoList.stream().filter(item -> AnswerResultTypeEnum.ALL_CORRECT.getType().equals(item.getAnswerResult())).collect(Collectors.toList());

        //查询学习记录，并更新做题相关的数据
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordId(bo.getStudyPlanningRecordId());
        studyRecordBo.setStudentId(bo.getStudentId());
        StudyRecordVo studyRecordVo = studyRecordService.queryOnce(studyRecordBo);
        boolean exist = studyRecordVo != null;

        StudyRecordBo updateBean = new StudyRecordBo();
        if (CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType())) {
            updateBean.setPracticeState(CorrectionStatusEnum.CORRECTED.getCode());
            updateBean.setPracticeRightNum((long) rightQuestionList.size());
            updateBean.setPracticeWrongNum((long) wrongQuestionList.size());
            updateBean.setPracticeUnansweredNum((long) (correctionRecordInfoBoList.size() - rightQuestionList.size() - wrongQuestionList.size()));
        } else if (CORRECTION_TYPE_TEST.equals(bo.getCorrectionType())) {
            updateBean.setTestState(CorrectionStatusEnum.CORRECTED.getCode());
            updateBean.setTestRightNum((long) rightQuestionList.size());
            updateBean.setTestWrongNum((long) wrongQuestionList.size());
            updateBean.setTestUnansweredNum((long) (correctionRecordInfoBoList.size() - rightQuestionList.size() - wrongQuestionList.size()));
        }
        if (exist) {
            updateBean.setStudyRecordId(studyRecordVo.getStudyRecordId());
            Boolean b1 = studyRecordService.updateByBo(updateBean);
            if (!b1) {
                throw new ServiceException("更新学习记录失败");
            }
        } else {
            updateBean.setStudentId(bo.getStudentId());
            updateBean.setCourseId(studyPlanningRecordVo.getCourseId());
            updateBean.setStudyPlanningRecordId(bo.getStudyPlanningRecordId());
            Boolean b1 = studyRecordService.insertByBo(updateBean);
            if (!b1) {
                throw new ServiceException("新增学习记录失败");
            }
        }
        return new BigDecimal(rightQuestionList.size()).divide(new BigDecimal(correctionRecordInfoBoList.size()), 4, RoundingMode.CEILING);
    }

    /**
     * 修改批改记录
     */
    @Override
    public Boolean updateByBo(CorrectionRecordBo bo) {
        CorrectionRecord update = MapstructUtils.convert(bo, CorrectionRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CorrectionRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除批改记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        //先找出来
        List<CorrectionRecord> list = baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("未找到要删除的记录");
        }
        //获取学习规划ID
        List<Long> studyPlanningRecordIdList = list.stream().map(CorrectionRecord::getStudyPlanningRecordId).toList();
        //删除学习记录
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);

        Map<Long, List<CorrectionRecord>> studyPlanningRecordIdMap = list.stream().collect(Collectors.groupingBy(CorrectionRecord::getStudyPlanningRecordId));


        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isNotEmpty(studyRecordVos)){
            List<StudyRecordVo> updateList = Lists.newArrayList();
            for (StudyRecordVo studyRecordVo : studyRecordVos) {
                StudyRecordVo temp = new StudyRecordVo();
                temp.setStudyRecordId(studyRecordVo.getStudyRecordId());

                Long studyPlanningRecordId = studyRecordVo.getStudyPlanningRecordId();
                if (Objects.isNull(studyPlanningRecordId)){
                    continue;
                }
                List<CorrectionRecord> correctionRecords = studyPlanningRecordIdMap.get(studyPlanningRecordId);
                if (CollUtil.isEmpty(correctionRecords)){
                    continue;
                }
                correctionRecords.forEach(item -> {
                    if (UserConstants.CORRECTION_TYPE_TEST.equals(item.getCorrectionType())) {
                        temp.cleanTestState();
                    } else if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(item.getCorrectionType())){
                        temp.cleanPracticeState();
                    }
                });
                updateList.add(temp);
            }

            List<StudyRecord> updated = updateList.stream().map(item -> MapstructUtils.convert(item, StudyRecord.class))
                .filter(Objects::nonNull)
                .toList();
            studyRecordService.updateBatchById(updated);
        }
        //获取批改记录详情ID
        List<Long> correctionRecordIdList = list.stream().map(CorrectionRecord::getCorrectionRecordId).toList();
        CorrectionRecordInfoBo correctionRecordInfoBo = new CorrectionRecordInfoBo();
        correctionRecordInfoBo.setCorrectionRecordIdList(correctionRecordIdList);

        List<CorrectionRecordInfoVo> correctionRecordInfoVos = correctionRecordInfoService.queryList(correctionRecordInfoBo);
        if (CollUtil.isNotEmpty(correctionRecordInfoVos)) {
            correctionRecordInfoService.deleteWithValidByIds(correctionRecordInfoVos
                .stream().map(CorrectionRecordInfoVo::getCorrectionRecordInfoId).toList(), false);
        }

        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public CorrectionRecordVo getOnce(CorrectionRecordBo bo) {
        LambdaQueryWrapper<CorrectionRecord> lambdaQuery = buildLambdaQueryWrapper(bo);
        lambdaQuery.orderBy(true, false, CorrectionRecord::getCorrectionRecordId);
        lambdaQuery.last("LIMIT 1");
        return baseMapper.selectVoOne(lambdaQuery);
    }

    @Override
    public CorrectionQuestionRecordVo queryQuestionRecord(Long studyPlanningRecordId, String correctionType) {
        CorrectionRecordBo correctionRecordBo = new CorrectionRecordBo();
        correctionRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        correctionRecordBo.setCorrectionType(correctionType);
        CorrectionRecordVo correctionRecordVo = getOnce(correctionRecordBo);
        if (correctionRecordVo == null) {
            throw new ServiceException("未找到批改记录");
        }
        CorrectionQuestionRecordVo result = new CorrectionQuestionRecordVo();

        //批改记录详情
        CorrectionRecordInfoBo correctionRecordInfoBo = new CorrectionRecordInfoBo();
        correctionRecordInfoBo.setCorrectionRecordId(correctionRecordVo.getCorrectionRecordId());
        List<CorrectionRecordInfoVo> correctionRecordInfoVoList = correctionRecordInfoService.queryList(correctionRecordInfoBo);
        if (CollUtil.isEmpty(correctionRecordInfoVoList)) {
            throw new ServiceException("未找到批改记录详情");
        }
        //按照题目序号排序，从小到大
        correctionRecordInfoVoList.sort(Comparator.comparing(CorrectionRecordInfoVo::takeQuestionNo));
        //计算正确率
        long count = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.ALL_CORRECT.getType().equals(item.getAnswerResult())).count();

        // 计算正确数量
        // 计算错误数量
        long wrongCount = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.ALL_WRONG.getType().equals(item.getAnswerResult())).count();
        // 计算半对错数量
        long rightWrongCount = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.HALF_WRONG.getType().equals(item.getAnswerResult())).count();

        //获取拍照截图里对应的url列表
        String correctionScreenshots = correctionRecordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)) {
            String urls = ossService.selectUrlByIds(correctionScreenshots);
            if (StringUtils.isNotBlank(urls)) {
                result.setCorrectionScreenshotUrlList(List.of(urls.split(",")));
            }
        }
        result.setRightCount(count);
        result.setWrongCount(wrongCount);
        result.setRightWrongCount(rightWrongCount);
        result.setAccuracy(new BigDecimal(count).divide(new BigDecimal(correctionRecordInfoVoList.size()), 4, RoundingMode.CEILING));
        result.setCorrectionRecordInfoVoList(correctionRecordInfoVoList);
        return result;
    }

    @Override
    public List<CorrectionRecordVo> queryRecordAndRightWrongInfo(CorrectionRecordBo correctionRecordBo) {
        QueryWrapper<CorrectionRecord> correctionRecordQueryWrapper = buildQueryWrapper(correctionRecordBo);
        return baseMapper.queryRecordAndRightWrongInfo(correctionRecordQueryWrapper);
    }

    @Override
    public Long count(CorrectionRecordBo convert) {
        LambdaQueryWrapper<CorrectionRecord> queryWrapper = buildLambdaQueryWrapper(convert);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Boolean submitWithoutCorrection(CorrectionRecordBo bo) {

            StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(bo.getStudyPlanningRecordId());
            if (studyPlanningRecordVo == null) {
                throw new ServiceException("未找到学习规划记录");
            }
            if (bo.getStudentId() == null) {
                bo.setStudentId(studyPlanningRecordVo.getStudentId());
            }
            //查下有没有已存在的，不允许重复提交
            LambdaQueryWrapper<CorrectionRecord> wrapper = Wrappers.lambdaQuery();
            wrapper.eq( CorrectionRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
            wrapper.eq(CorrectionRecord::getStudentId, bo.getStudentId());
            wrapper.eq(CorrectionRecord::getCorrectionType, bo.getCorrectionType());
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("请勿重复提交");
            }

            if (bo.getStudentId() == null) {
                throw new ServiceException("会员ID不能为空");
            }

            if (!UserConstants.CORRECTION_TYPE_PREVIEW.equals(bo.getCorrectionType()) && !UserConstants.CORRECTION_TYPE_SPEAK.equals(bo.getCorrectionType())) {
                throw new ServiceException("批改类型错误");
            }

            CorrectionRecord add = MapstructUtils.convert(bo, CorrectionRecord.class);
            validEntityBeforeSave(add);
            boolean flag = baseMapper.insert(add) > 0;
            if (!flag) {
                throw new ServiceException("新增批改记录失败");
            }
            bo.setCorrectionRecordId(add.getCorrectionRecordId());
        return true;
    }

    @Override
    public CorrectionRecordVo queryRecord(Long studyPlanningRecordId, String correctionType) {
        CorrectionRecordBo correctionRecordBo = new CorrectionRecordBo();
        correctionRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        correctionRecordBo.setCorrectionType(correctionType);
        CorrectionRecordVo correctionRecordVo = getOnce(correctionRecordBo);
        if (correctionRecordVo == null) {
           return null;
        }

        //获取拍照截图里对应的url列表
        String correctionScreenshots = correctionRecordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)) {
            String urls;
            if (CORRECTION_TYPE_SPEAK.equals(correctionType)){
                //如果是视频 则返回1天
                urls = ossService.selectUrlByIds(correctionScreenshots,86400);
            }else {
                urls = ossService.selectUrlByIds(correctionScreenshots);
            }
            if (StringUtils.isNotBlank(urls)) {
                correctionRecordVo.setCorrectionScreenshotsUrl(List.of(urls.split(",")));
            }
        }
        return correctionRecordVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetStudentAnswerRecord(List<Long> correctionRecordIds, Integer type) {
        // 如果type是1 ，删除学习规划的批改记录
        if (type == 1) {
            this.deleteWithValidByIds(correctionRecordIds, false);
        }else {
            aiCorrectionRecordService.deleteWithValidByIds(correctionRecordIds,false);
        }

        return true;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
