package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.student.domain.AttendanceLogStaffEzkeco;
import com.jxw.shufang.student.domain.vo.AttendanceLogStaffEzkecoVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * ezkeco员工考勤记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface AttendanceLogStaffEzkecoMapper extends BaseMapperPlus<AttendanceLogStaffEzkeco, AttendanceLogStaffEzkecoVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    Page<AttendanceLogStaffEzkecoVo> queryPageList(@Param("page") Page<AttendanceLogStaffEzkeco> build,@Param(Constants.WRAPPER) LambdaQueryWrapper<AttendanceLogStaffEzkeco> lqw);
}
