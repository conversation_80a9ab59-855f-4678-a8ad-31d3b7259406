package com.jxw.shufang.student.service.studyduration;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.*;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.IStudyRecordService;
import com.jxw.shufang.student.service.IStudyVideoRecordService;
import com.jxw.shufang.student.service.studyduration.processor.StudyRecordTimeProcessor;
import com.jxw.shufang.student.service.studyduration.processor.StudyVideoRecordTimeProcessor;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/8 1:10
 * @Version 1
 * @Description
 */
@Service
public abstract class AbstractStudyRecordTime<T> implements ModuleGroupProvider<T> {
    /**
     * 获取上下文数据
     * @param records
     * @param moduleAndGroupEnum
     * @return
     */
    public abstract StudyDurationProcessingContextDTO contextData(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum);

    /**
     * 获取视频记录处理DTO
     * @param recordBo
     * @return
     */
    public abstract SaveOrUpdateStudyVideoRecordDTO buildStudyVideoRecordProcessDTO(T recordBo);

    /**
     * 获取学习记录处理DTO
     * @param recordBo
     * @return
     */
    public abstract SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(T recordBo);

    @Resource
    private IStudyVideoRecordService studyVideoRecordService;
    @Resource
    private IStudyRecordService studyRecordService;
    @Resource
    private StudyRecordTimeContentService studyRecordTimeContentService;

    @GlobalTransactional(rollbackFor = Exception.class)
    public void process(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<T> filterData = this.filterData(records, moduleAndGroupEnum);
        if (CollectionUtil.isEmpty(filterData)){
            return;
        }

        StudyDurationProcessingContextDTO contextData = this.contextData(filterData, moduleAndGroupEnum);
        if(contextData.ignoreRecord()){
            return;
        }

        StudyVideoRecordTimeProcessor videoProcessor = new StudyVideoRecordTimeProcessor(contextData);
        StudyRecordTimeProcessor studyProcessor = new StudyRecordTimeProcessor(contextData);

        videoProcessor.process(this.buildStudyVideoRecordProcessDTO(filterData.get(0)));
        studyProcessor.studyProcess(this.buildStudyRecordProcessDTO(filterData.get(0)));

        this.batchSaveOrUpdate(videoProcessor, studyProcessor);
    }

    private void batchSaveOrUpdate(StudyVideoRecordTimeProcessor videoProcessor, StudyRecordTimeProcessor studyProcessor) {
        List<StudyVideoRecord> videoProcessorUpdates = videoProcessor.getUpdates();
        List<StudyVideoRecord> videoProcessorInserts = videoProcessor.getInserts();
        List<StudyRecord> studyProcessorInserts = studyProcessor.getInserts();
        List<StudyRecord> studyProcessorUpdates = studyProcessor.getUpdates();
        if (CollectionUtil.isNotEmpty(videoProcessorUpdates)) {
            studyVideoRecordService.batchUpdate(videoProcessorUpdates);
        }
        if (CollectionUtil.isNotEmpty(videoProcessorInserts)) {
            studyVideoRecordService.batchInsert(videoProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorInserts)) {
            studyRecordService.batchInsert(studyProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorUpdates)) {
            studyRecordService.batchUpdate(studyProcessorUpdates);
        }
    }

    public Map<Long, Student> studentMap(List<Long> studentIds) {
        return studyRecordTimeContentService.studentMap(studentIds);
    }

    public Map<Long, Long> courseDurationMap(List<Long> videoIds) {
        return studyRecordTimeContentService.courseDurationMap(videoIds);
    }

    public Map<Long, StudyVideoRecord> batchQueryVideoRecords(List<StudyVideoRecordBo> recordBos) {
        List<BatchQueryVideoRecordDTO> batchQueryVideoRecords = recordBos.stream()
            .map(m -> BatchQueryVideoRecordDTO.of(m.getStudentId(), m.getStudyPlanningRecordId(), m.getStudyModuleType())
            ).toList();
        List<StudyVideoRecord> studyVideoRecords = studyVideoRecordService.batchQueryRecord(batchQueryVideoRecords);
        return studyVideoRecords.stream()
            .collect(Collectors.toMap(StudyVideoRecord::getStudyPlanningRecordId, Function.identity(), (v1, v2) -> v2));
    }
}
