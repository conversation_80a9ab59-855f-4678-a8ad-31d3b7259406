package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiPracticeRecordBo;
import com.jxw.shufang.student.domain.vo.AiPracticeRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * Ai练习记录Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiPracticeRecordService {

    /**
     * 查询Ai练习记录
     */
    AiPracticeRecordVo queryById(Long aiPracticeRecordId);

    /**
     * 查询Ai练习记录列表
     */
    TableDataInfo<AiPracticeRecordVo> queryPageList(AiPracticeRecordBo bo, PageQuery pageQuery);

    /**
     * 查询Ai练习记录列表
     */
    List<AiPracticeRecordVo> queryList(AiPracticeRecordBo bo);

    /**
     * 新增Ai练习记录
     */
    Boolean insertByBo(AiPracticeRecordBo bo);

    /**
     * 修改Ai练习记录
     */
    Boolean updateByBo(AiPracticeRecordBo bo);

    /**
     * 校验并批量删除Ai练习记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<AiPracticeRecordBo> convert);
}
