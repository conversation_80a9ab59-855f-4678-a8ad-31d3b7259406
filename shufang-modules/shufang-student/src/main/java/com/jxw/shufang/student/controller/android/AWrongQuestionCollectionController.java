package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.domain.bo.WrongQuestionReviseBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionInfoVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.student.service.IWrongQuestionCollectionService;
import com.jxw.shufang.student.service.IWrongQuestionRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/wrongQuestionCollection")
public class AWrongQuestionCollectionController extends BaseController {

    private final IWrongQuestionCollectionService wrongQuestionCollectionService;
    private final IWrongQuestionRecordService wrongQuestionRecordService;

    @GetMapping("/page")
    public TableDataInfo<WrongQuestionCollectionVo> pageWrongQuestionCollection(WrongQuestionCollectionBo bo,
                                                                                PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("仅允许会员可以访问");
        }
        pageQuery.setSearchCount(false);
        bo.setStudentId(LoginHelper.getStudentId());
        return wrongQuestionCollectionService.queryWrongQuestionCollection(bo, pageQuery);
    }

    @GetMapping("/{wrongQuestionCollectionId}")
    public R<WrongQuestionCollectionInfoVo> queryQuestion(@PathVariable Long wrongQuestionCollectionId) {
        return R.ok(wrongQuestionCollectionService.queryCollectionInfo(wrongQuestionCollectionId));
    }


    @RepeatSubmit
    @PostMapping("/revise")
    public R<Boolean> wrongQuestionRevise(@Validated(AddGroup.class) @RequestBody WrongQuestionReviseBo bo) {
        return R.ok(wrongQuestionCollectionService.wrongQuestionRevise(bo));
    }

}
