package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.student.domain.CourseResource;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 课程资源（绑定到课程的资源）视图对象 course_resource
 *
 *
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CourseResource.class)
public class CourseResourceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程资源id
     */
    @ExcelProperty(value = "课程资源id")
    private Long courseResourceId;

    /**
     * 课程id
     */
    @ExcelProperty(value = "课程id")
    private Long courseId;

    /**
     * 资源种类（1学习视频 2课程讲义 3会员练习 4练习解答视频 5会员测验 6测验解答视频 7试卷 8试卷解析 9试卷+解析）
     */
    @ExcelProperty(value = "资源种类", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=学习视频,2=课程讲义,3=会员练习,4=练习解答视频,5=会员测验,6=测验解答视频,7=试卷,8=试卷解析,9=试卷+解析")
    private String courseResourceType;

    /**
     * 资源来源（1公共资源 2本地资源）
     */
    @ExcelProperty(value = "资源来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=公共资源,2=本地资源")
    private String resourceSource;

    /**
     * 外部资料id（公共资源才有此项，可以为表加id与外部资源进行对应）
     */
    @ExcelProperty(value = "外部资料id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "公=共资源才有此项，可以为表加id与外部资源进行对应")
    private String externalInformationId;

    /**
     * 资料id（本地资源才有此项）
     */
    @ExcelProperty(value = "资料id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "本=地资源才有此项")
    private Long informationId;

    /**
     * 资源内容（资源地址）
     */
    @ExcelProperty(value = "资源内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "资=源地址")
    private String resourceContent;

    /**
     * 资源内容（oss_id）
     */
    @ExcelProperty(value = "资源内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id")
    private Long resourceOssId;

    /**
     * 资源名称（可用于重命名，包含文件后缀）
     */
    @ExcelProperty(value = "资源名称", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=用于重命名，包含文件后缀")
    private String resourceName;

    /**
     * 资源总大小（单位KB）
     */
    @ExcelProperty(value = "资源总大小", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位KB")
    private Long resourceSize;

    /**
     * 资源总时长（单位秒 学习视频才有此项）
     */
    @ExcelProperty(value = "资源总时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位秒,学=习视频才有此项")
    private Long resourceDuration;

    /**
     * 题目序号（题目解答视频才有此项）
     */
    @ExcelProperty(value = "题目序号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "题=目解答视频才有此项")
    private String questionNo;

    /**
     * 题目详情（对应试卷中有哪些题型，数量多少）
     */
    @ExcelProperty(value = "题目详情", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应试卷中有哪些题型，数量多少")
    private String questionInfo;

    /**
     * 创建时间(上传时间)
     */
    private Date createTime;

    /**
     * 试卷名称（试卷+会员测验才有此项）
     */
    private String paperName;

    /**
     * 是否收藏
     */
    private Boolean isCollect;

    /**
     * 关联课程
     */
    private CourseVo course;

    /**
     * 测试或者练习的原卷
     */
    private RemoteGroupResourceVo originalPaper;

    /**
     * 测试或者练习的带解析的卷子
     */
    private RemoteGroupResourceVo parsedPaper;

}
