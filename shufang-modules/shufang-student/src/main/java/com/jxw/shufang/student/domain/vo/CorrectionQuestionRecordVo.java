package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.student.domain.ApplyCorrectionRecord;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ApplyCorrectionRecord.class)
public class CorrectionQuestionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 做题拍照URL列表
     */
    private List<String> correctionScreenshotUrlList;
    /**
     * 正确率
     */
    private BigDecimal accuracy;

    /**
     * 题目批改列表
     */
    private List<CorrectionRecordInfoVo> correctionRecordInfoVoList;


    /**
     * 正确数量
     */
    private Long rightCount;

    /**
     * 错误数量（包含未做题目）(不包含半对错)
     */
    private Long wrongCount;

    /**
     * 半对错
     */
    private Long rightWrongCount;
}
