package com.jxw.shufang.student.service;

import com.jxw.shufang.student.domain.bo.EZKEcoEmployeeBo;
import com.jxw.shufang.student.domain.bo.EzkEcoTransactionRequestBo;
import com.jxw.shufang.student.domain.vo.EZKEcoBaseResponse;
import com.jxw.shufang.student.domain.vo.EZKEcoEmployeeVo;
import com.jxw.shufang.student.domain.vo.EzkEcoTransactionVo;

/**
 * 对接考勤机Service接口
 * @date 2024-05-18
 */
public interface IEZKEcoService {

    /**
     * 人员对接 - 获取人员信息
     *
     * @param config 数据字典 ezkeco考勤机配置信息 ezkeco_ip_key
     * @param ezkEcoEmployeeBo 参数
     * @return EZKEcoBaseResponse<EZKEcoEmployeeVo>
     */
    EZKEcoBaseResponse<EZKEcoEmployeeVo> getEmployee(String config, EZKEcoEmployeeBo ezkEcoEmployeeBo);

    /**
     * 记录对接 - 获取考勤记录
     *
     * @param config 数据字典 ezkeco考勤机配置信息 ezkeco_ip_key
     * @param ezkEcoTransactionRequestBo 参数
     * @return EZKEcoBaseResponse<EZKEcoTransactionVo>
     */
    EZKEcoBaseResponse<EzkEcoTransactionVo> getTransaction(String config, EzkEcoTransactionRequestBo ezkEcoTransactionRequestBo);

    /**
     * 记录对接 - 手动同步考勤机信息
     * @param ezkEcoTransactionRequestBo  手动同步参数
     * @return EZKEcoBaseResponse<ManualSyncResVo>
     */
    Integer manualSyncAllAttendanceLog(EzkEcoTransactionRequestBo ezkEcoTransactionRequestBo);
}
