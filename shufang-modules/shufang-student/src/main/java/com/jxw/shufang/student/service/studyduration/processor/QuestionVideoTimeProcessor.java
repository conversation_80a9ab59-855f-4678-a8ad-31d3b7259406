package com.jxw.shufang.student.service.studyduration.processor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateQuestionProcessDTO;
import com.jxw.shufang.student.domain.dto.StudySlicesProcessingContextDTO;

import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @Date 2025/4/7 16:55
 * @Version 1
 * @Description
 */
public class QuestionVideoTimeProcessor {
    private List<QuestionVideoRecord> updates = new ArrayList<>();
    private List<QuestionVideoRecord> inserts = new ArrayList<>();
    private StudySlicesProcessingContextDTO context;

    public QuestionVideoTimeProcessor(StudySlicesProcessingContextDTO context) {
        this.context = context;
    }


    public List<QuestionVideoRecord> getUpdates() {
        return updates;
    }

    public List<QuestionVideoRecord> getInserts() {
        return inserts;
    }

    public void processByCourseId(SaveOrUpdateQuestionProcessDTO processDTO) {
        QuestionVideoRecord existing = this.checkExistQuestionRecord(processDTO);
        Student student = context.getStudentMap().get(processDTO.getStudentId());
        if (existing != null) {
            Optional.ofNullable(this.buildUpdateRecord(existing, processDTO, student)).ifPresent(updates::add);
        } else {
            Optional.of(this.buildInsertRecord(processDTO, student)).ifPresent(inserts::add);
        }
    }

    private QuestionVideoRecord checkExistQuestionRecord(SaveOrUpdateQuestionProcessDTO processDTO) {
        List<QuestionVideoRecord> existQuestionVideoRecordMap = context.getExistQuestionVideoRecordMap().get(processDTO.getStudentId());
        if (CollectionUtil.isEmpty(existQuestionVideoRecordMap)) {
            return null;
        }
        return existQuestionVideoRecordMap
            .stream()
            .filter(Objects::nonNull)
            .findFirst().orElse(null);
    }


    private QuestionVideoRecord buildInsertRecord(SaveOrUpdateQuestionProcessDTO processDTO, Student student) {
        Long sliceSeconds = this.getSliceSeconds(processDTO.getStudyVideoDuration(), processDTO.getStudyVideoSlices());
        Map<Long, Long> courseDurationMap = context.getCourseDurationMap();
        Long courseDuration = Optional.ofNullable(courseDurationMap.get(processDTO.getVideoId())).orElse(0L);
        QuestionVideoRecord questionVideoRecord = new QuestionVideoRecord();
        questionVideoRecord.setStudentId(processDTO.getStudentId());
        questionVideoRecord.setStudyPlanningRecordId(processDTO.getStudyPlanningRecordId());
        questionVideoRecord.setVideoId(processDTO.getVideoId());
        questionVideoRecord.setCourseId(processDTO.getCourseId());
        questionVideoRecord.setStudyVideoSlices(VideoSlicesUtils.delExcessCommas(processDTO.getStudyVideoSlices()));
        questionVideoRecord.setQuestionId(processDTO.getQuestionId());
        long remainDuration = courseDuration - sliceSeconds;
        boolean overCourseMaxTime = sliceSeconds > courseDuration;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;
        questionVideoRecord.setStudyVideoDuration(useCourseDuration ? courseDuration : sliceSeconds);
        questionVideoRecord.setCreateBy(student.getCreateBy());
        questionVideoRecord.setCreateDept(student.getCreateDept());
        questionVideoRecord.setCreateTime(processDTO.getCommitTime());
        questionVideoRecord.setUpdateBy(student.getCreateBy());
        questionVideoRecord.setUpdateTime(processDTO.getCommitTime());
        questionVideoRecord.setQuestionType(processDTO.getQuestionType());
        questionVideoRecord.setModuleGroup(processDTO.getModuleGroup());
        return questionVideoRecord;
    }

    private QuestionVideoRecord buildUpdateRecord(QuestionVideoRecord existing, SaveOrUpdateQuestionProcessDTO processDTO, Student student) {
        Map<Long, Long> courseDurationMap = context.getCourseDurationMap();
        Long courseDuration = Optional.ofNullable(courseDurationMap.get(processDTO.getVideoId())).orElse(0L);

        String oldSlices = existing.getStudyVideoSlices();
        Long oldDuration = existing.getStudyVideoDuration();
        String newStudyVideoSlices = processDTO.getStudyVideoSlices();
        Long newStudyVideoDuration = processDTO.getStudyVideoDuration();

        String mergedSlices = VideoSlicesUtils.mergeVideoSlices(oldSlices, newStudyVideoSlices);
        Long maxDuration = this.getMaxDuration(existing, newStudyVideoDuration, mergedSlices);

        boolean updateRecordFlag = maxDuration > oldDuration;
        if (!updateRecordFlag) {
            return null;
        }

        QuestionVideoRecord updateBean = new QuestionVideoRecord();
        updateBean.setQuestionVideoRecordId(existing.getQuestionVideoRecordId());
        long remainDuration = courseDuration - maxDuration;
        boolean overCourseMaxTime = maxDuration > courseDuration;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;
        updateBean.setStudyVideoDuration(useCourseDuration ? courseDuration : maxDuration);
        updateBean.setStudyVideoSlices(mergedSlices);
        updateBean.setUpdateBy(student.getCreateBy());
        updateBean.setUpdateTime(processDTO.getCommitTime());
        return updateBean;
    }

    private Long getMaxDuration(QuestionVideoRecord existing, Long studyVideoDuration, String mergedSlices) {
        Long maxDuration = 0L;
        if (StrUtil.isEmpty(mergedSlices)) {
            maxDuration = Optional.ofNullable(studyVideoDuration).orElse(0L) + existing.getStudyVideoDuration();
        } else {
            maxDuration = this.getSliceSecondsBySlice(mergedSlices);
        }
        return maxDuration;
    }

    private Long getSliceSeconds(Long studyVideoDuration, String videoSlice) {
        if (null != studyVideoDuration && studyVideoDuration > 0) {
            return studyVideoDuration;
        }
        return getSliceSecondsBySlice(videoSlice);
    }

    private Long getSliceSecondsBySlice(String videoSlice) {
        return VideoSlicesUtils.calVideoSliceSeconds(videoSlice);
    }
}
