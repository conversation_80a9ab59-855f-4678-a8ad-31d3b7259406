package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.IAllowOwnCorrectionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 允许自主批改--平板端
 * 前端访问路由地址为:/student/android/allowOwnCorrection
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/allowOwnCorrection")
public class AAllowOwnCorrectionController extends BaseController {

    private final IAllowOwnCorrectionService allowOwnCorrectionService;


}
