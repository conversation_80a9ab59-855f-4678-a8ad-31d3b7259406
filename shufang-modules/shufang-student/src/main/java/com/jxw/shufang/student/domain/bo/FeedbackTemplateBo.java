package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.FeedbackTemplate;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateSourceVo;

/**
 * 反馈模板业务对象 feedback_template
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FeedbackTemplate.class, reverseConvertGenerate = false)
public class FeedbackTemplateBo extends BaseEntity {

    /**
     * 反馈模板id
     */
    @NotNull(message = "反馈模板id不能为空", groups = { EditGroup.class })
    private Long feedbackTemplateId;

    /**
     * 反馈维度（对应字典值）
     */
    @NotBlank(message = "反馈维度（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackType;

    /**
     * 反馈内容
     */
    @NotBlank(message = "反馈内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackTemplateContent;

    /**
     * 模板来源（对应字典值）
     */
    //@NotBlank(message = "模板来源（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String feedbackTemplateSource;



    /**
     * 模板使用次数
     */
    private Integer templateUseCount;

    /**
     * 是否携带部门信息
     */
    private Boolean withDeptInfo;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /*
    * 是否收藏
     */
    private Boolean isCollect;

    private FeedbackTemplateSourceVo FeedbackTemplateSourceVo;

}
