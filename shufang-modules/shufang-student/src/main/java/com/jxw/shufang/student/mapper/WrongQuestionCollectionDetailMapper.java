package com.jxw.shufang.student.mapper;

import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.WrongQuestionCollectionDetail;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionDetailBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 错题合集详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface WrongQuestionCollectionDetailMapper extends BaseMapperPlus<WrongQuestionCollectionDetail, WrongQuestionCollectionDetailVo> {

    /**
     * 查询错题合集详情列表
     */
    List<WrongQuestionCollectionDetailVo> selectWrongQuestionCollectionDetailList(WrongQuestionCollectionDetailBo bo);

    /**
     * 根据错题合集ID查询详情
     */
    List<WrongQuestionCollectionDetailVo> selectByCollectionId(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId);

    /**
     * 根据错题合集ID和题目ID查询详情
     */
    WrongQuestionCollectionDetailVo selectByCollectionIdAndQuestionId(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId, @Param("questionId") Long questionId);

    /**
     * 统计错题合集详情数量
     */
    int countByCollectionId(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId);

    /**
     * 统计指定作答结果的详情数量
     */
    int countByCollectionIdAndAnswerResult(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId, @Param("answerResult") String answerResult);

    /**
     * 批量查询错题合集详情
     */
    List<WrongQuestionCollectionDetailVo> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 根据错题合集ID删除详情
     */
    int deleteByCollectionId(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId);

    /**
     * 批量插入错题合集详情
     */
    int insertBatch(@Param("list") List<WrongQuestionCollectionDetail> list);

    /**
     * 根据题目ID查询相关的错题合集详情
     */
    List<WrongQuestionCollectionDetailVo> selectByQuestionId(@Param("questionId") Long questionId);

    /**
     * 查询错题合集详情统计信息
     */
    List<Map<String, Object>> selectStatisticsByCollectionId(@Param("wrongQuestionCollectionId") Long wrongQuestionCollectionId);

}
