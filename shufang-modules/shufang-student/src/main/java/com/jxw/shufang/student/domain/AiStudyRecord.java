package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习记录对象 study_record
 * @date 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_study_record")
public class AiStudyRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习记录id
     */
    @TableId(value = "ai_study_record_id")
    private Long aiStudyRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 测评试卷ID
     */
    private Long testPaperId;

    /**
     * 学习视频时长（单位为秒 此为多个视频记录总计）
     */
    private Long studyVideoTotalDuration;

    /**
     * 练习状态（1未批改 2批改中 3已批改）
     */
    private String practiceState;

    /**
     * 练习-正确题目数量
     */
    private Long practiceRightNum;

    /**
     * 练习-错误题目数量
     */
    private Long practiceWrongNum;

    /**
     * 练习-未答题目数量
     */
    private Long practiceUnansweredNum;

    /**
     * 练习视频时长（单位为秒 此为多个视频总计）
     */
    private Long practiceVideoTotalDuration;

    /**
     * 测验状态（1未批改 2批改中 3已批改）
     */
    private String testState;

    /**
     * 测验-正确题目数量
     */
    private Long testRightNum;

    /**
     * 测验-错误题目数量
     */
    private Long testWrongNum;

    /**
     * 测验-未答题目数量
     */
    private Long testUnansweredNum;

    /**
     * 测验视频时长（单位为秒 此为多个视频总计）
     */
    private Long testVideoTotalDuration;

    /**
     * 预习浏览总时长
     */
    private Long previewTotalDuration;

    /**
     * 自讲视频浏览总时长
     */
    private Long selfSpeechTotalDuration;

    /**
     * AI评测讲解浏览总时长
     */
    private Long aiExplainTotalDuration;

    /**
     * AI评测浏览总时长
     */
    private Long aiTestTotalDuration;

    /**
     * 创建部门(和会员id对应的部门一致)
     */
    private Long createDept;

    /**
     * 创建人(和会员id对应的创建人一致)
     */
    private Long createBy;

    /**
     * 创建时间（开始学习时间）
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
