package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordV2Vo;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.WrongQuestionRecord;
import com.jxw.shufang.student.domain.vo.WrongQuestionGroupVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionRecordVo;

import java.util.List;

/**
 * 错题记录Mapper接口
 *
 *
 * @date 2024-05-09
 */
public interface WrongQuestionRecordMapper extends BaseMapperPlus<WrongQuestionRecord, WrongQuestionRecordVo> {

    Page<WrongQuestionGroupVo> listGroupByDate(@Param("studentId") Long studentId,@Param("courseIdList") List<Long> courseIdList,@Param("page") Page<WrongQuestionGroupVo> page);

    Page<WrongQuestionRecordVo> selectWrongQuestionRecordPage(@Param("page") Page<WrongQuestionGroupVo> page,@Param(Constants.WRAPPER) QueryWrapper<WrongQuestionRecord> lqw);

    Page<WrongQuestionRecordV2Vo> selectWrongQuestionRecordPageV2(@Param("page") Page<WrongQuestionGroupVo> page, @Param(Constants.WRAPPER) QueryWrapper<WrongQuestionRecord> lqw);
    Page<WrongQuestionRecordV2Vo> selectRecordPageGroupByStudent(@Param("page") Page<WrongQuestionGroupVo> page, @Param(Constants.WRAPPER) QueryWrapper<WrongQuestionRecord> lqw);



    Integer wrongCount(Long questionId, Long studentId);

    Page<WrongQuestionGroupVo> listGroupByDateV2(String recordCreateTimeStart, String recordCreateTimeEnd, Long studentId,
                                                 @Param("courseIdList")  List<Long> courseIdList,@Param("page") Page<Object> build);

}
