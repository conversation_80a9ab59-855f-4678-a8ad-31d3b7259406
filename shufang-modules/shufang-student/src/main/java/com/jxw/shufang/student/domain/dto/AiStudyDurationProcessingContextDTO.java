package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/4/7 10:50
 * @Version 1
 * @Description
 */
@Data
public class AiStudyDurationProcessingContextDTO {
    private List<AiStudyVideoRecord> existAiVideoRecord;
    private List<AiStudyRecord> existAiStudyRecord;
    private Map<Long, Student> studentMap;
    private StudyModuleAndGroupEnum studyModuleTypeEnum;
    private Map<Long, Long> courseDurationMap;
}
