package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentPreferentialRecordBo;
import com.jxw.shufang.student.domain.vo.StudentPreferentialRecordVo;
import com.jxw.shufang.student.service.IStudentPreferentialRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @author: cyj
 * @date: 2025/3/12
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/student/preferential")
public class StudentPreferentialController {

    private final IStudentPreferentialRecordService studentPreferentialRecordService;

    /**
     * 查询会员转介绍列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("student:student:preferential")
    @GetMapping("/listRecord")
    public TableDataInfo<StudentPreferentialRecordVo.PreferentialRecordVo> listRecord(StudentPreferentialRecordBo bo, PageQuery pageQuery) {
        return studentPreferentialRecordService.listRecord(bo, pageQuery);
    }

    /**
     * 会员转赠
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("student:student:preferential")
    @PostMapping("/transfer")
    public R<Void> transfer(@Validated @RequestBody StudentPreferentialRecordBo bo) {
        return studentPreferentialRecordService.transfer(bo) ? R.ok() : R.fail("会员转赠失败") ;
    }

}
