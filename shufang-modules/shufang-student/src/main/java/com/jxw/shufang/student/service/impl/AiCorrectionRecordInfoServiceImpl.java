package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AiCorrectionRecordInfo;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordInfoVo;
import com.jxw.shufang.student.mapper.AiCorrectionRecordInfoMapper;
import com.jxw.shufang.student.service.IAiCorrectionRecordInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * ai批改记录详情Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class AiCorrectionRecordInfoServiceImpl implements IAiCorrectionRecordInfoService, BaseService {

    private final AiCorrectionRecordInfoMapper baseMapper;

    /**
     * 查询ai批改记录详情
     */
    @Override
    public AiCorrectionRecordInfoVo queryById(Long aiCorrectionRecordInfoId){
        return baseMapper.selectVoById(aiCorrectionRecordInfoId);
    }

    /**
     * 查询ai批改记录详情列表
     */
    @Override
    public TableDataInfo<AiCorrectionRecordInfoVo> queryPageList(AiCorrectionRecordInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiCorrectionRecordInfo> lqw = buildQueryWrapper(bo);
        Page<AiCorrectionRecordInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai批改记录详情列表
     */
    @Override
    public List<AiCorrectionRecordInfoVo> queryList(AiCorrectionRecordInfoBo bo) {
        LambdaQueryWrapper<AiCorrectionRecordInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiCorrectionRecordInfo> buildQueryWrapper(AiCorrectionRecordInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiCorrectionRecordInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, AiCorrectionRecordInfo::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getAiCorrectionRecordId() != null, AiCorrectionRecordInfo::getAiCorrectionRecordId, bo.getAiCorrectionRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), AiCorrectionRecordInfo::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), AiCorrectionRecordInfo::getAnswerResult, bo.getAnswerResult());
        lqw.in(CollUtil.isNotEmpty(bo.getAiCorrectionRecordIds()), AiCorrectionRecordInfo::getAiCorrectionRecordId, bo.getAiCorrectionRecordIds());
        return lqw;
    }

    /**
     * 新增ai批改记录详情
     */
    @Override
    public Boolean insertByBo(AiCorrectionRecordInfoBo bo) {
        AiCorrectionRecordInfo add = MapstructUtils.convert(bo, AiCorrectionRecordInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiCorrectionRecordInfoId(add.getAiCorrectionRecordInfoId());
        }
        return flag;
    }

    /**
     * 修改ai批改记录详情
     */
    @Override
    public Boolean updateByBo(AiCorrectionRecordInfoBo bo) {
        AiCorrectionRecordInfo update = MapstructUtils.convert(bo, AiCorrectionRecordInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiCorrectionRecordInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ai批改记录详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<AiCorrectionRecordInfoBo> correctionRecordInfoBoList) {
        List<AiCorrectionRecordInfo> correctionRecordInfoList = MapstructUtils.convert(correctionRecordInfoBoList, AiCorrectionRecordInfo.class);
        return baseMapper.insertBatch(correctionRecordInfoList);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
