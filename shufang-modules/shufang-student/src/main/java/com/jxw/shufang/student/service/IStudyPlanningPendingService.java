package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyPlanningPendingBo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 需学习规划学生Service接口
 *
 * @date 2024-06-14
 */
public interface IStudyPlanningPendingService {

    /**
     * 查询需学习规划学生
     */
    StudyPlanningPendingVo queryById(Long id);

    /**
     * 查询需学习规划学生列表
     */
    TableDataInfo<StudyPlanningPendingVo> queryPageList(StudyPlanningPendingBo bo, PageQuery pageQuery);

    /**
     * 查询需学习规划学生列表
     */
    List<StudyPlanningPendingVo> queryList(StudyPlanningPendingBo bo);

    /**
     * 新增需学习规划学生
     */
    Boolean insertByBo(StudyPlanningPendingBo bo);

    /**
     * 修改需学习规划学生
     */
    Boolean updateByBo(StudyPlanningPendingBo bo);

    /**
     * 校验并批量删除需学习规划学生信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 生成待规划学生记录
     */
    Boolean generatePendingStudents();

    /**
     * 根据指定日期生成待规划学生记录
     *
     * @param dateParam 日期参数，格式：yyyy-MM-dd，如果为空则使用当前日期
     */
    Boolean generatePendingStudents(String dateParam);

    /**
     * 标记学生为已规划
     */
    Boolean markAsPlanned(Long studentId, List<Long> planningRecordIds);


    /**
     * 根据待处理记录ID标记为已反馈
     *
     * <p>根据具体的学习规划待处理记录ID来更新反馈状态，
     * 只有在当前状态为待反馈(0)时才会更新为已反馈(1)，
     * 如果已经反馈过了则不会重复更新。</p>
     *
     * @param pendingId 学习规划待处理记录ID
     */
    void markAsFeedbackByPendingId(Long pendingId);

    /**
     * 根据待处理记录ID标记为已规划
     *
     * <p>根据具体的学习规划待处理记录ID来更新规划状态，
     * 只有在当前状态为待规划(0)时才会更新为已规划(1)，
     * 如果已经规划过了则不会重复更新。</p>
     *
     * @param pendingId 学习规划待处理记录ID
     * @param planningRecordIds 学习规划记录ID列表，用于建立关联关系
     */
    void markAsPlannedByPendingId(Long pendingId, List<Long> planningRecordIds);

    /**
     * 根据StudyPlanningPendingId返回最新一条关联的学习规划反馈记录
     */
    StudyFeedbackReportVo getLatestFeedbackReportByPendingId(Long pendingId);

    /**
     * 根据学生ID和反馈日期范围查找匹配的pending记录ID列表
     *
     * <p>查询条件：学生ID匹配，且pending的计划日期精确匹配反馈报告的周期（只比较年月日）</p>
     * <p>精确日期匹配：DATE(pending.planStartDate) = DATE(periodStart) AND DATE(pending.planEndDate) = DATE(periodEnd)</p>
     *
     * @param studentId 学生ID
     * @param periodStart 反馈周期开始日期
     * @param periodEnd 反馈周期结束日期
     * @return 匹配的pending记录ID列表
     */
    List<Long> findPendingIdsByStudentAndPeriod(Long studentId, Date periodStart, Date periodEnd);

    /**
     * 根据学生ID和学习规划日期范围查找匹配的pending记录ID列表（用于学习规划）
     *
     * <p>查询条件：学生ID匹配，且学习规划的日期范围与pending的计划日期范围有交集</p>
     * <p>范围匹配：学习规划日期范围[planningStart, planningEnd]与pending的[planStartDate, planEndDate]有交集</p>
     * <p>交集条件：planningStart <= planEndDate AND planningEnd >= planStartDate</p>
     *
     * @param studentId 学生ID
     * @param planningStart 学习规划开始日期
     * @param planningEnd 学习规划结束日期
     * @return 匹配的pending记录ID列表
     */
    List<Long> findPendingIdsByStudentAndPlanningPeriod(Long studentId, Date planningStart, Date planningEnd);

    /**
     * 批量查询多个学生在指定日期的pending记录
     *
     * <p>查询条件：学生ID在列表中，且学习规划的日期与pending的计划日期范围有交集</p>
     * <p>范围匹配：学习规划日期[planningDate]与pending的[planStartDate, planEndDate]有交集</p>
     * <p>交集条件：planningDate <= planEndDate AND planningDate >= planStartDate</p>
     *
     * @param studentIds 学生ID列表
     * @param planningDate 学习规划日期
     * @return 学生ID到pending记录ID列表的映射
     */
    Map<Long, List<Long>> findPendingIdsByStudentsAndDate(List<Long> studentIds, Date planningDate);

}
