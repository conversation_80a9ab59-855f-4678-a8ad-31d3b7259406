package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteVideoLabelService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionDetailVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoLabelVo;
import com.jxw.shufang.student.domain.vo.QuestionCollectVo;
import com.jxw.shufang.student.service.IQuestionCollectService;
import com.jxw.shufang.student.service.IVideoService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class VideoServiceImpl implements IVideoService, BaseService {
    @DubboReference
    private RemoteVideoLabelService remoteVideoLabelService;

    private final IQuestionCollectService questionCollectService;

    @Override
    public List<RemoteVideoLabelVo> listVideoLabels(Long videoId, Boolean needKnowledgeNote) {
        List<RemoteVideoLabelVo> remoteVideoLabelVos = remoteVideoLabelService.listVideoLabels(videoId, needKnowledgeNote);
        if (CollUtil.isEmpty(remoteVideoLabelVos)) {
            return List.of();
        }
        List<Long> allQuestionIds = new ArrayList<>();
        for (RemoteVideoLabelVo remoteVideoLabelVo : remoteVideoLabelVos) {
            List<Long> questionIds = remoteVideoLabelVo.getLabelQuestions().stream().map(RemoteQuestionDetailVo::getId).toList();
            allQuestionIds.addAll(questionIds);
        }
        Map<Long, QuestionCollectVo> collectQuestionMap = Map.of();
        if (CollUtil.isNotEmpty(allQuestionIds)) {
            List<QuestionCollectVo> questionCollectVos = questionCollectService.listQuestionCollectsByQuestionIds(allQuestionIds);
            collectQuestionMap = questionCollectVos.stream().collect(Collectors.toMap(QuestionCollectVo::getQuestionId, Function.identity()));

        }
        for (RemoteVideoLabelVo remoteVideoLabelVo : remoteVideoLabelVos) {
            List<RemoteQuestionDetailVo> labelQuestions = remoteVideoLabelVo.getLabelQuestions();
            if (CollUtil.isNotEmpty(labelQuestions)) {
                for (RemoteQuestionDetailVo labelQuestion : labelQuestions) {
                    QuestionCollectVo questionCollectVo = collectQuestionMap.get(labelQuestion.getId());
                    if (questionCollectVo != null) {
                        labelQuestion.setIsCollection(1);
                    } else {
                        labelQuestion.setIsCollection(0);
                    }
                }
            }

        }
        return remoteVideoLabelVos;
    }
}
