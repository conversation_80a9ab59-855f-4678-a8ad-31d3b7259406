package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/14 17:48
 * @Version 1
 * @Description 学习规划记录业务对象-新
 */
@Data
public class NewStudyPlanningRecordBO {
    /**
     * 学习规划日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date studyPlanningDate;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    /**
     * 会员姓名
     */
    private String studentName;
}
