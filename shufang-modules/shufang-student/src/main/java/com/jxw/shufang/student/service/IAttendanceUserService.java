package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.AttendanceUser;
import com.jxw.shufang.student.domain.bo.AttendanceUserBo;
import com.jxw.shufang.student.domain.vo.AttendanceUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 考勤关联用户Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IAttendanceUserService {

    /**
     * 查询考勤关联用户
     */
    AttendanceUserVo queryById(Long attendanceUserId);

    /**
     * 查询考勤关联用户列表
     */
    TableDataInfo<AttendanceUserVo> queryPageList(AttendanceUserBo bo, PageQuery pageQuery);

    /**
     * 查询考勤关联用户列表
     */
    TableDataInfo<AttendanceUser> queryDataPageList(AttendanceUserBo bo, PageQuery pageQuery);

    /**
     * 查询考勤关联用户列表
     */
    List<AttendanceUserVo> queryList(AttendanceUserBo bo);

    /**
     * 新增考勤关联用户
     */
    Boolean insertByBo(AttendanceUserBo bo);

    /**
     * 修改考勤关联用户
     */
    Boolean updateByBo(AttendanceUserBo bo);

    /**
     * 校验并批量删除考勤关联用户信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 同步EzkEco考勤机的关联信息
     */
    Integer syncEzkEcoAttendanceUser();
}
