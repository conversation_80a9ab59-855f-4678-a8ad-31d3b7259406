package com.jxw.shufang.student.enums;

/**
 * <AUTHOR>
 * @Date 2025/4/7 10:22
 * @Version 1
 * @Description 模块和分组枚举
 */
public enum StudyModuleAndGroupEnum {
    // 学习规划
    STUDY_PLAN_LEARN(StudyModelGroupEnum.STUDY_PLANNING, StudyModuleTypeEnum.STUDY, "学习规划-学"),
    STUDY_PLAN_PRACTICE(StudyModelGroupEnum.STUDY_PLANNING, StudyModuleTypeEnum.PRACTICE, "学习规划-练"),
    STUDY_PLAN_TEST(StudyModelGroupEnum.STUDY_PLANNING, StudyModuleTypeEnum.TEST, "学习规划-测"),
    STUDY_PLAN_PREVIEW(StudyModelGroupEnum.STUDY_PLANNING, StudyModuleTypeEnum.PREVIEW, "学习规划-预"),
    STUDY_PLAN_SELF_SPEECH(StudyModelGroupEnum.STUDY_PLANNING, StudyModuleTypeEnum.SELF_SPEECH, "学习规划-自讲"),

    // AI伴学
    AI_STUDY_LEARN(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.STUDY, "AI伴学-学"),
    AI_STUDY_PRACTICE(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.PRACTICE, "AI伴学-练"),
    AI_STUDY_TEST(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.TEST, "AI伴学-测"),
    AI_STUDY_PREVIEW(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.PREVIEW, "AI伴学-预"),
    AI_STUDY_SELF_SPEECH(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.SELF_SPEECH, "AI伴学-自讲"),

    // AI评测
    AI_TEST(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.AI_TEST, "AI评测时长"),
    AI_ERROR_VIDEO(StudyModelGroupEnum.AI_STUDY, StudyModuleTypeEnum.AI_TEST_ERROR_VIDEO, "AI评测-观看错误视频时长");

    private StudyModelGroupEnum groupEnum;
    private StudyModuleTypeEnum moduleEnum;
    private String name;

    StudyModuleAndGroupEnum(StudyModelGroupEnum groupEnum, StudyModuleTypeEnum moduleEnum, String name) {
        this.groupEnum = groupEnum;
        this.moduleEnum = moduleEnum;
        this.name = name;
    }

    public StudyModelGroupEnum getGroupEnum() {
        return groupEnum;
    }

    public void setGroupEnum(StudyModelGroupEnum groupEnum) {
        this.groupEnum = groupEnum;
    }

    public StudyModuleTypeEnum getModuleEnum() {
        return moduleEnum;
    }

    public void setModuleEnum(StudyModuleTypeEnum moduleEnum) {
        this.moduleEnum = moduleEnum;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
