package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.PreferentialModifyTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudentPreferentialRecord;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentPreferentialRecordBo;
import com.jxw.shufang.student.domain.vo.StudentPreferentialRecordVo;
import com.jxw.shufang.student.enums.PreferentialInOrOutEnum;
import com.jxw.shufang.student.mapper.StudentPreferentialRecordMapper;
import com.jxw.shufang.student.service.IStudentPreferentialRecordService;
import com.jxw.shufang.student.service.IStudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StudentPreferentialRecordServiceImpl implements IStudentPreferentialRecordService, BaseService {

    private final StudentPreferentialRecordMapper baseMapper;

    private final IStudentService studentService;

    @Override
    public boolean saveRecordByBo(StudentPreferentialRecordBo saveRecordBo) {
        return baseMapper.insert(MapstructUtils.convert(saveRecordBo, StudentPreferentialRecord.class)) > 0;
    }

    @Override
    public BigDecimal getFrozenAmount(Long studentId) {
        StudentPreferentialRecordBo bo = new StudentPreferentialRecordBo();
        bo.setOwnerStudentId(studentId);
        bo.setChangeType(PreferentialInOrOutEnum.IN.getType());
        LambdaQueryWrapper<StudentPreferentialRecord> queryWrapper = buildWrapper(bo);
        queryWrapper.gt(StudentPreferentialRecord::getUnFrozenTime, new Date());
        List<StudentPreferentialRecordVo> frozenRecord = baseMapper.selectVoList(queryWrapper);
        return CollectionUtils.isEmpty(frozenRecord) ?
            BigDecimal.ZERO : frozenRecord.stream().map(StudentPreferentialRecordVo::getChangePreferentialAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public StudentPreferentialRecordVo getFrozenRecord(StudentPreferentialRecordBo bo) {
        bo.setChangeType(PreferentialInOrOutEnum.IN.getType());
        LambdaQueryWrapper<StudentPreferentialRecord> wrapper = buildWrapper(bo);
        wrapper.gt(StudentPreferentialRecord::getUnFrozenTime, new Date())
            .orderByDesc(StudentPreferentialRecord::getRecordId).last("limit 1 ");
        return baseMapper.selectVoOne(wrapper);
    }

    @Override
    public Boolean removeFrozenTime(StudentPreferentialRecordBo frozenQueryBo) {
        if (null == frozenQueryBo.getRecordId()) {
            return false;
        }
        LambdaUpdateWrapper<StudentPreferentialRecord> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(StudentPreferentialRecord::getRecordId, frozenQueryBo.getRecordId())
            .set(StudentPreferentialRecord::getUnFrozenTime, null);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    public TableDataInfo<StudentPreferentialRecordVo.PreferentialRecordVo> listRecord(StudentPreferentialRecordBo bo, PageQuery pageQuery) {
        if (bo.getOwnerStudentId() == null) {
            return TableDataInfo.build(new Page<>());
        }
        bo.setNoFrozenRecord(true);
        LambdaQueryWrapper<StudentPreferentialRecord> wrapper = buildWrapper(bo);
        wrapper.orderByDesc(BaseEntity::getCreateTime);
        IPage<StudentPreferentialRecordVo> recordVoIPage = baseMapper.selectVoPage(pageQuery.build(), wrapper);
        List<StudentPreferentialRecordVo.PreferentialRecordVo> voList = new ArrayList<>();
        Map<Long, Student> studentVoMap = new HashMap<>();
        for (StudentPreferentialRecordVo record : recordVoIPage.getRecords()) {
            voList.add(new StudentPreferentialRecordVo.PreferentialRecordVo(
                PreferentialModifyTypeEnum.getByType(record.getGainType()),
                PreferentialInOrOutEnum.getByType(record.getChangeType()),
                record.getChangePreferentialAmount(),
                studentVoMap.computeIfAbsent(record.getOwnerStudentId(), vo -> studentService.queryStudentById(record.getOwnerStudentId())).getStudentName(),
                (null != record.getFromStudentId() && 0 != record.getFromStudentId())
                    ? studentVoMap.computeIfAbsent(record.getFromStudentId(), vo -> studentService.queryStudentById(record.getFromStudentId())).getStudentName() : null,
                record.getCreateTime()
            ));
        }
        return TableDataInfo.build(voList, recordVoIPage.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean transfer(StudentPreferentialRecordBo bo) {
        if (bo.getChangePreferentialAmount().compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        if (bo.getFromStudentId().equals(bo.getOwnerStudentId())) {
            throw new ServiceException("转入转出会员对象一致，转赠失败。");
        }
        //此次转赠优惠额度来源的会员
        Student student = studentService.queryStudentById(bo.getFromStudentId());
        BigDecimal frozenAmount = getFrozenAmount(student.getStudentId());
        if (null == student.getPreferentialAmount()
            || student.getPreferentialAmount().subtract(frozenAmount)
            .subtract(bo.getChangePreferentialAmount()).compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("会员当前的优惠额度小于转赠优惠额度，转赠失败。");
        }
        //优惠额度转移到的会员
        Student toStudent = studentService.queryStudentById(bo.getOwnerStudentId());
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(student.getStudentId());
        studentBo.setPreferentialAmount(student.getPreferentialAmount());
        studentBo.setPreferentialAmountVersion(student.getPreferentialAmountVersion());
        if (!studentService.updatePreferentialAmountByBo(studentBo, bo.getChangePreferentialAmount().negate())) {
            log.error("转赠时会员优惠额度更新源会员失败");
            throw new ServiceException("会员转赠失败");
        }
        studentBo = new StudentBo();
        studentBo.setStudentId(toStudent.getStudentId());
        studentBo.setPreferentialAmount(toStudent.getPreferentialAmount());
        studentBo.setPreferentialAmountVersion(toStudent.getPreferentialAmountVersion());
        if (!studentService.updatePreferentialAmountByBo(studentBo, bo.getChangePreferentialAmount())) {
            log.error("转赠时会员优惠额度更新会员失败");
            throw new ServiceException("会员转赠失败");
        }
        // 新增优惠额度变动记录
        StudentPreferentialRecordBo saveFromRecordBo = new StudentPreferentialRecordBo();
        saveFromRecordBo.setOwnerStudentId(student.getStudentId());
        saveFromRecordBo.setFromStudentId(toStudent.getStudentId());
        saveFromRecordBo.setChangeType(PreferentialInOrOutEnum.OUT.getType());
        saveFromRecordBo.setGainType(PreferentialModifyTypeEnum.STUDENT_TRANSFER.getType());
        saveFromRecordBo.setChangePreferentialAmount(bo.getChangePreferentialAmount());
        if (!saveRecordByBo(saveFromRecordBo)) {
            log.error("转赠时源会员优惠额度记录保存失败");
            throw new ServiceException("会员转赠失败");
        }
        // 新增优惠额度变动记录
        StudentPreferentialRecordBo saveToRecordBo = new StudentPreferentialRecordBo();
        saveToRecordBo.setOwnerStudentId(toStudent.getStudentId());
        saveToRecordBo.setFromStudentId(student.getStudentId());
        saveToRecordBo.setBusinessId(student.getStudentId());
        saveToRecordBo.setChangeType(PreferentialInOrOutEnum.IN.getType());
        saveToRecordBo.setGainType(PreferentialModifyTypeEnum.STUDENT_TRANSFER.getType());
        saveToRecordBo.setChangePreferentialAmount(bo.getChangePreferentialAmount());
        if (!saveRecordByBo(saveToRecordBo)) {
            log.error("转赠时会员优惠额度记录保存失败");
            throw new ServiceException("会员转赠失败");
        }
        return true;
    }

    /**
     * get wrapper
     *
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<StudentPreferentialRecord> buildWrapper(StudentPreferentialRecordBo bo) {
        LambdaQueryWrapper<StudentPreferentialRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(null != bo.getOwnerStudentId(), StudentPreferentialRecord::getOwnerStudentId, bo.getOwnerStudentId()).eq(null != bo.getChangeType(), StudentPreferentialRecord::getChangeType, bo.getChangeType()).eq(null != bo.getBusinessId(), StudentPreferentialRecord::getBusinessId, bo.getBusinessId());
        if (Boolean.TRUE.equals(bo.getNoFrozenRecord())) {
            wrapper.and(queryWrapper -> queryWrapper.isNull(StudentPreferentialRecord::getUnFrozenTime)
                .or().lt(StudentPreferentialRecord::getUnFrozenTime, new Date()));
        }
        return wrapper;
    }
}
