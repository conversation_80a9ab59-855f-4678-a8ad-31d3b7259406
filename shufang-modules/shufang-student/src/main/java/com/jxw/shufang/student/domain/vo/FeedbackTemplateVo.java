package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.FeedbackTemplate;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 反馈模板视图对象 feedback_template
 *
 *
 * @date 2024-03-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackTemplate.class)
public class FeedbackTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈模板id
     */
    @ExcelProperty(value = "反馈模板id")
    private Long feedbackTemplateId;

    /**
     * 反馈维度（对应字典值）
     */
    @ExcelProperty(value = "反馈维度", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String feedbackType;

    /**
     * 反馈内容
     */
    @ExcelProperty(value = "反馈内容")
    private String feedbackTemplateContent;

    /**
     * 模板来源（对应字典值）
     */
    @ExcelProperty(value = "模板来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String feedbackTemplateSource;


    /**
     * 模板使用次数
     */
    @ExcelProperty(value = "模板使用次数")
    private Integer templateUseCount;



    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建dept
     */
    private Long createDept;

    /**
     * 是否收藏
     */
    private Boolean isCollect;


    private RemoteDeptVo createDeptEntity;


}
