package com.jxw.shufang.student.service;

import cn.hutool.core.lang.tree.Tree;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.bo.AssociateCourseBo;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.bo.CourseSyncBo;
import com.jxw.shufang.student.domain.bo.MoveCourseBo;
import com.jxw.shufang.student.domain.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 课程（课程包含章节）Service接口
 *
 * <AUTHOR>
 * @date 2024-03-30
 */
public interface ICourseService {

    /**
     * 查询课程（课程包含章节）
     */
    CourseVo queryById(Long courseId, Boolean withAttr);

    /**
     * 查询课程（课程包含章节）
     */
    CourseVo queryById(Long courseId);

    /**
     * 查询课程（课程包含章节）列表
     */
    TableDataInfo<CourseVo> queryPageList(CourseBo bo, PageQuery pageQuery);

    /**
     * 查询课程（课程包含章节）列表
     */
    List<CourseVo> queryList(CourseBo bo);

    /**
     * 新增课程（课程包含章节）
     */
    Boolean insertByBo(CourseBo bo);

    Boolean addCatalog(CourseBo bo);

    Boolean editCatalog(CourseBo bo);

    Boolean associateCourse(CourseBo bo);

    Boolean batchAssociateCourse(AssociateCourseBo bo);

    Boolean moveCourse(MoveCourseBo bo);

    Boolean changeSort(CourseBo bo);

    /**
     * 批量新增课程（课程包含章节）
     */
    void insertBatchByBoList(List<CourseBo> boList);

    /**
     * 修改课程（课程包含章节）
     */
    void updateByBo(CourseBo bo);

    /**
     * 校验并批量删除课程（课程包含章节）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    CourseVo getOnceByBo(CourseBo courseBo);

    /**
     * 树形查询
     */
    List<Tree<Long>> treeQuery(CourseBo bo);

    List<Tree<Long>> queryTreeV2(Long courseId);


    /**
     * 构建前端所需要树结构
     *
     * @param courseVoList 部门列表
     * @return 树结构列表
     */
    List<Tree<Long>> buildTree(List<CourseVo> courseVoList);

    List<Tree<Long>> buildTreeV2(List<CourseVo> courseVoList, Boolean withDetail);

    /**
     * 放置本身最顶级课程的信息
     *
     * @param courseVoList 课程列表
     * @date 2024/04/29 05:56:03
     */
    void putTopmostCourseInfo(List<CourseVo> courseVoList, Boolean withAttr);

    Map<Long, List<Tree<Long>>> queryCourseTreePath(List<CourseVo> courseVoList);

    void putTopmostCourseInfo(List<CourseVo> courseVoList, Boolean withAttr, Boolean needGradeName);

    void putCourseWithAttrInfo(List<CourseVo> courseVos);


    /**
     * 通过课程id获取课程资源
     *
     * @param courseId 课程id
     * @param type     类型
     * @date 2024/05/08 07:55:19
     */
    RemoteKnowledgeResourceVo getCourseResourceByCourseId(Long courseId, KnowledgeResourceType type);

    /**
     * 获取知识问题列表
     *
     * @param courseId              课程id
     * @param resourceType          资源类型
     * @param withAnalysis          带分析
     * @param withAnswer            有答案
     * @param filterInvalidQuestion 过滤无效问题(无效题目只无需作答的题目，如有题干的题目(有子节点和没有答案的题目)，会过滤题干)，这个为true的同时会把整颗题目叔平铺
     * @date 2024/05/15 01:48:39
     */
    List<RemoteQuestionVo> getKnowledgeQuestionList(Long courseId, KnowledgeResourceType resourceType, Boolean withAnalysis, Boolean withAnswer, Boolean filterInvalidQuestion);

    CourseVo getAiChapterResource(Long courseId, Long studentId);

    /**
     * 获取课程专题分类
     *
     * @param courseBo
     * @return
     */
    List<SpecialTopicCategoryVo> getSpecialTopicCategory(CourseBo courseBo, Long studentId, Long branchId);


    /**
     * 获取Ai课程章节和学习信息
     *
     * @param courseId
     * @param studentId
     * @date 2024/05/22 01:09:16
     */
    List<CourseVo> getAiChapterAndLearnInfo(Long courseId, Long studentId);

    List<Tree<Long>> getAiCatalogAndLearnInfo(Long courseId, Long studentId);


    List<CourseChildInfoVo> getChildInfoList(CourseBo courseBo);

    CourseChildInfoVo getChildInfo(CourseBo courseBo);

    void putCourseDetail(List<CourseVo> records, Boolean lastConcatCourseName);

    /**
     * 获取专题课程树
     *
     * @param bo
     * @return
     */
    List<SpecialTopicCourseTreeVo> treeBySpecialTopic(CourseBo bo);

    List<SpecialTopicCourseV2Vo> findSpecialTopicV2(CourseBo bo, Integer queryTopicType);

    /**
     * 获取学段课程树
     *
     * @param bo
     * @return
     */
    List<Tree<Long>> treeByStage(CourseBo bo);

    /**
     * 删除课程(章节),同时删除课程下的章节
     *
     * @param courseId
     * @return
     */
    boolean deleteCourse(@NotNull(message = "主键不能为空") Long courseId);

    boolean batchRemoveCourse(List<Long> courseIds, Integer type);

    void export(CourseBo bo, String s, HttpServletResponse response);

    List<CourseVo> listCourseVos(List<Long> courseIds);

    boolean sync(CourseSyncBo syncBo);

    List<Course> getCoursesByCourseId(List<Long> courseIdList);

    void synchronizedData();

    /**
     * 根据课程ID集合，查询这些课程对应的学段名称
     */
    Map<Long, String> getStageListByCourseIds(List<Long> courseIds);

    List<CourseVo> listCourseSelection(CourseBo bo);

    List<Tree<Long>> batchQueryTree(List<Long> courseIds);

    /**
     * 获取课程信息，并添加父级课程名称
     *
     * @param courseIds
     * @return
     */
    List<CourseVo> getCourseInfoWithParentNameByCourseId(List<Long> courseIds);

    List<GradeTreeVo> buildCourseTreeByStageAndType(String stage, Integer courseType);

    /**
     * 构建查询课程树形结构
     * @param courseType 课程体系（1：寒暑春秋；2：AI伴学）
     * @param courseVos 需要构建树形结构的课程集合
     */
    List<GradeTreeVo> treeByStageAndCourseTypeWithGrades(Integer courseType, List<CourseVo> courseVos, int defaultLayer);

    Map<String, Long> loadAllAttributes(String attributeType);

    List<GradeTreeVo> getTopicCategoryV2(String grade, int defaultLayer);

    boolean saveCourseSearch(String searchContent, Long studentId);

    CourseSearchHistoryVo getCourseSearch(Long studentId);

}
