package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.student.domain.StudyPlanningFeedbackPendingRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 学习规划反馈报告关联学习记录业务对象 study_planning_feedback_pending_relation
 *
 * @date 2024-06-14
 */
@Data
@AutoMapper(target = StudyPlanningFeedbackPendingRelation.class, reverseConvertGenerate = false)
public class FeedbackReportPendingRelationBo {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 反馈报告ID
     */
    @NotNull(message = "反馈报告ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long reportId;

    /**
     * 需学习规划记录ID
     */
    @NotNull(message = "需学习规划记录ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long pendingId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新人ID
     */
    private Long updaterId;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
