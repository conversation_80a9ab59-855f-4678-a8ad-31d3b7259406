package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 需学习规划学生对象 study_planning_pending
 *
 * @date 2024-06-14
 */
@Data
@TableName("study_planning_pending")
public class StudyPlanningPending implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 模式类型:1-春秋模式,2-寒暑模式
     */
    private Integer modeType;

    /**
     * 计划开始日期
     */
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    private Date planEndDate;

    /**
     * 反馈状态:0-待反馈,1-已反馈
     */
    private Integer feedbackStatus;

    /**
     * 规划状态:0-待规划,1-已规划
     */
    private Integer planningStatus;

    /**
     * 应规划时间
     */
    private Date expectedPlanningTime;

    /**
     * 应反馈时间
     */
    private Date expectedFeedbackTime;

    /**
     * 实际规划时间
     */
    private Date actualPlanningTime;

    /**
     * 实际反馈时间
     */
    private Date actualFeedbackTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
