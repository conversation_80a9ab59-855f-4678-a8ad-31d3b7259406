package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiPracticeRecordBo;
import com.jxw.shufang.student.domain.vo.AiPracticeRecordVo;
import com.jxw.shufang.student.service.IAiPracticeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Ai练习记录
 * 前端访问路由地址为:/student/aiPracticeRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiPracticeRecord")
public class AiPracticeRecordController extends BaseController {

    private final IAiPracticeRecordService aiPracticeRecordService;

    /**
     * 查询Ai练习记录列表
     */
    @SaCheckPermission("student:aiPracticeRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiPracticeRecordVo> list(AiPracticeRecordBo bo, PageQuery pageQuery) {
        return aiPracticeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出Ai练习记录列表
     */
    @SaCheckPermission("student:aiPracticeRecord:export")
    @Log(title = "Ai练习记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiPracticeRecordBo bo, HttpServletResponse response) {
        List<AiPracticeRecordVo> list = aiPracticeRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "Ai练习记录", AiPracticeRecordVo.class, response);
    }

    /**
     * 获取Ai练习记录详细信息
     *
     * @param aiPracticeRecordId 主键
     */
    @SaCheckPermission("student:aiPracticeRecord:query")
    @GetMapping("/{aiPracticeRecordId}")
    public R<AiPracticeRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiPracticeRecordId) {
        return R.ok(aiPracticeRecordService.queryById(aiPracticeRecordId));
    }

    /**
     * 新增Ai练习记录
     */
    @SaCheckPermission("student:aiPracticeRecord:add")
    @Log(title = "Ai练习记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiPracticeRecordBo bo) {
        return toAjax(aiPracticeRecordService.insertByBo(bo));
    }

    /**
     * 修改Ai练习记录
     */
    @SaCheckPermission("student:aiPracticeRecord:edit")
    @Log(title = "Ai练习记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiPracticeRecordBo bo) {
        return toAjax(aiPracticeRecordService.updateByBo(bo));
    }

    /**
     * 删除Ai练习记录
     *
     * @param aiPracticeRecordIds 主键串
     */
    @SaCheckPermission("student:aiPracticeRecord:remove")
    @Log(title = "Ai练习记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiPracticeRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiPracticeRecordIds) {
        return toAjax(aiPracticeRecordService.deleteWithValidByIds(List.of(aiPracticeRecordIds), true));
    }
}
