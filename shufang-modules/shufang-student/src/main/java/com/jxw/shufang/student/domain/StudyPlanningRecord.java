package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习规划记录对象 study_planning_record
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_planning_record")
public class StudyPlanningRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划记录id
     */
    @TableId(value = "study_planning_record_id")
    private Long studyPlanningRecordId;

    /**
     * 学习规划id
     */
    private Long studyPlanningId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 学习开始时间
     */
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    private Date studyEndTime;

    /**
     * 学习时长
     */
    private Long studyDuration;

    /**
     * 学习状态（0未开始 1进行中 2已完成）
     */
    private String studyStatus;

    /**
     * 学习记录状态（0正常 1覆盖 2删除 ）
     */
    private String studyRecordStatus;

    /**
     * 学习规划下载状态（0未下载 1已下载）
     */
    private Integer downloadStatus;

}
