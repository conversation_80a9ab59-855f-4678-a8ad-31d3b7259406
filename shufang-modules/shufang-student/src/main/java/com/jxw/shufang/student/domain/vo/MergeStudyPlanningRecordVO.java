package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.student.domain.dto.*;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/14 10:33
 * @Version 1
 * @Description
 */
@Data
public class MergeStudyPlanningRecordVO {
    private String studentName;
    private Long studentId;
    private List<StudyPlanningRecordInfoVO> records;

    private MergeStudyPlanningRecordVO() {
    }

    /**
     * 将记录按会员id合并为一条记录
     * * @return
     */
    public static List<MergeStudyPlanningRecordVO> mergeByStudent(List<MergeStudyRecordDTO> mergeStudyRecordList) {
        if (CollectionUtil.isEmpty(mergeStudyRecordList)) {
            return Collections.emptyList();
        }

        Map<Long, List<MergeStudyRecordDTO>> studentIdMergeInfoMap = mergeStudyRecordList.stream()
            .filter(Objects::nonNull).collect(Collectors.groupingBy(MergeStudyRecordDTO::getStudentId));

        return studentIdMergeInfoMap.entrySet().stream()
            .map(entry -> convertToMergeStudyRecordVO(entry.getKey(), entry.getValue()))
            .collect(Collectors.toList());
    }

    /**
     * 转为MergeStudyPlanningRecordVO
     *
     * @param studentId            学生ID
     * @param mergeStudyRecordList 当前学生的学习规划记录
     * @return
     */
    private static MergeStudyPlanningRecordVO convertToMergeStudyRecordVO(Long studentId,
                                                                          List<MergeStudyRecordDTO> mergeStudyRecordList) {
        Optional<MergeStudyRecordDTO> firstRecord = mergeStudyRecordList.stream().findFirst();
        String studentName = firstRecord.map(MergeStudyRecordDTO::getStudentName).orElse("");

        List<StudyPlanningRecordInfoVO> studyRecords = mergeStudyRecordList.stream()
            .map(MergeStudyPlanningRecordVO::convertToStudyPlanningRecordInfo)
            .toList();

        MergeStudyPlanningRecordVO mergeResult = new MergeStudyPlanningRecordVO();
        mergeResult.setStudentName(studentName);
        mergeResult.setStudentId(studentId);
        mergeResult.setRecords(studyRecords);
        return mergeResult;
    }

    private static StudyPlanningRecordInfoVO convertToStudyPlanningRecordInfo(MergeStudyRecordDTO recordDTO) {
        StudentWithPlanningRecordDTO planningRecord = recordDTO.getPlanningRecord();
        CourseWithTopCourseDTO course = recordDTO.getCourse();
        StudentWithConsultantDTO consultant = recordDTO.getConsultant();

        StudyPlanningRecordInfoVO recordInfoResult = new StudyPlanningRecordInfoVO();
        recordInfoResult.setStudyPlanningRecordId(planningRecord.getStudyPlanningRecordId());
        recordInfoResult.setStudyPlanningId(planningRecord.getStudyPlanningId());
        recordInfoResult.setStudentId(planningRecord.getStudentId());
        recordInfoResult.setCourseId(planningRecord.getCourseId());
        recordInfoResult.setStudentName(planningRecord.getStudentName());
        recordInfoResult.setStudyPlanningDate(planningRecord.getStudyPlanningDate());
        recordInfoResult.setStudyStartTime(planningRecord.getStudyStartTime());
        recordInfoResult.setStudyEndTime(planningRecord.getStudyEndTime());
        recordInfoResult.setDownloadStatus(planningRecord.getDownloadStatus());
        recordInfoResult.setStaffName(Optional.ofNullable(consultant).map(StudentWithConsultantDTO::getNickName).orElse(null));
        recordInfoResult.setStudyContent(Optional.ofNullable(course).map(CourseWithTopCourseDTO::getStudyContent).orElse(null));
        recordInfoResult.setContentDetail(Optional.ofNullable(course).map(CourseWithTopCourseDTO::getContentDetail).orElse(null));

        // 目前系统无用的值，直接置空
        recordInfoResult.setBranchMachineSeat(null);
        recordInfoResult.setAttendanceTime(null);
        String studentAccount = getLastFourDigits(recordDTO.getStudentAccount());
        recordInfoResult.setNameWithPhone(recordDTO.getStudentName().concat("(").concat(studentAccount).concat(")"));
        return recordInfoResult;
    }

    public static String getLastFourDigits(String numberStr) {
        if(StrUtil.isEmpty(numberStr)){
            return "";
        }
        if (numberStr.length() < 4) {
            return numberStr;
        }
        return numberStr.substring(numberStr.length() - 4);
    }
}
