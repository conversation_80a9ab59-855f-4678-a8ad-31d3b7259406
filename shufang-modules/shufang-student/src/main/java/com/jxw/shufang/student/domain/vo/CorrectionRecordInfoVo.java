package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.CorrectionRecordInfo;

import java.io.Serial;
import java.io.Serializable;


/**
 * 批改记录详情视图对象 correction_record_info
 *
 *
 * @date 2024-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CorrectionRecordInfo.class)
public class CorrectionRecordInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 批改记录详情id
     */
    @ExcelProperty(value = "批改记录详情id")
    private Long correctionRecordInfoId;

    /**
     * 问题ID
     */
    @ExcelProperty(value = "问题ID")
    private Long questionId;

    /**
     * 批改记录id
     */
    @ExcelProperty(value = "批改记录id")
    private Long correctionRecordId;

    /**
     * 题目序号
     */
    @ExcelProperty(value = "题目序号")
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全对、全错、半错）
     */
    @ExcelProperty(value = "作答结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如全对、全错、半错")
    private String answerResult;



    public Double takeQuestionNo(){
        if (null == this.questionNo|| StringUtils.isBlank(this.questionNo)){
            return null;
        }
        //题目形式 1    1.2   1.2.1    1.3.4 之类的
        //将题目序号转换为小数，去除第一个小数点以后的小数点，能达到正确的排序效果
        String[] split = this.questionNo.split("\\.");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            if (i == 0){
                if (split.length==1){
                    sb.append(split[i]);
                }else {
                    sb.append(split[i]).append(".");
                }
            }else {
                sb.append(split[i]);
            }
        }
        return Double.valueOf(sb.toString());

    }
}
