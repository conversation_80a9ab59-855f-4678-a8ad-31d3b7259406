package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiWrongQuestionRecordBo;
import com.jxw.shufang.student.domain.vo.AiWrongQuestionRecordVo;
import com.jxw.shufang.student.service.IAiWrongQuestionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Ai学习错题记录
 * 前端访问路由地址为:/student/aiWrongQuestionRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiWrongQuestionRecord")
public class AiWrongQuestionRecordController extends BaseController {

    private final IAiWrongQuestionRecordService aiWrongQuestionRecordService;

    /**
     * 查询Ai学习错题记录列表
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiWrongQuestionRecordVo> list(AiWrongQuestionRecordBo bo, PageQuery pageQuery) {
        return aiWrongQuestionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出Ai学习错题记录列表
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:export")
    @Log(title = "Ai学习错题记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiWrongQuestionRecordBo bo, HttpServletResponse response) {
        List<AiWrongQuestionRecordVo> list = aiWrongQuestionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "Ai学习错题记录", AiWrongQuestionRecordVo.class, response);
    }

    /**
     * 获取Ai学习错题记录详细信息
     *
     * @param aiWrongQuestionRecordId 主键
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:query")
    @GetMapping("/{aiWrongQuestionRecordId}")
    public R<AiWrongQuestionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiWrongQuestionRecordId) {
        return R.ok(aiWrongQuestionRecordService.queryById(aiWrongQuestionRecordId));
    }

    /**
     * 新增Ai学习错题记录
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:add")
    @Log(title = "Ai学习错题记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiWrongQuestionRecordBo bo) {
        return toAjax(aiWrongQuestionRecordService.insertByBo(bo));
    }

    /**
     * 修改Ai学习错题记录
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:edit")
    @Log(title = "Ai学习错题记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiWrongQuestionRecordBo bo) {
        return toAjax(aiWrongQuestionRecordService.updateByBo(bo));
    }

    /**
     * 删除Ai学习错题记录
     *
     * @param aiWrongQuestionRecordIds 主键串
     */
    @SaCheckPermission("student:aiWrongQuestionRecord:remove")
    @Log(title = "Ai学习错题记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiWrongQuestionRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiWrongQuestionRecordIds) {
        return toAjax(aiWrongQuestionRecordService.deleteWithValidByIds(List.of(aiWrongQuestionRecordIds), true));
    }
}
