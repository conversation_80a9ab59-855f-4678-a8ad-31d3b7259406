package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员对象 student
 *
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_process")
public class StudyProcess extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;



}
