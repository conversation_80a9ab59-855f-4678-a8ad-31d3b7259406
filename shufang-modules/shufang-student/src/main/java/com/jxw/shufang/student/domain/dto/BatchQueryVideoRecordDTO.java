package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/2 17:04
 * @Version 1
 * @Description 批量查询学习记录入参
 */
@Data
public class BatchQueryVideoRecordDTO {
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 学习规划记录id
     */
    private Long studyPlanningRecordId;
    /**
     * 学习模块类型
     */
    private String studyModuleType;

    /***
     * 学习模块组
     */
    private String studyModuleGroup;

    public static BatchQueryVideoRecordDTO of(Long studentId, Long studyPlanningRecordId, StudyModuleAndGroupEnum studyModuleType) {
        BatchQueryVideoRecordDTO batchQueryVideoRecordDTO = new BatchQueryVideoRecordDTO();
        batchQueryVideoRecordDTO.setStudentId(studentId);
        batchQueryVideoRecordDTO.setStudyPlanningRecordId(studyPlanningRecordId);
        batchQueryVideoRecordDTO.setStudyModuleType(studyModuleType.getModuleEnum().getModuleCode());
        batchQueryVideoRecordDTO.setStudyModuleGroup(studyModuleType.getGroupEnum().getGroupCode());
        return batchQueryVideoRecordDTO;
    }
}
