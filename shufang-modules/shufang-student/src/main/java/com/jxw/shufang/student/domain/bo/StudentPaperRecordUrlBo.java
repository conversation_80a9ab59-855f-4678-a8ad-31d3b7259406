package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class StudentPaperRecordUrlBo  {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long studentPaperRecordId;


    @NotNull(message = "pdf报告url不能为空")
    private String reportUrl;


}
