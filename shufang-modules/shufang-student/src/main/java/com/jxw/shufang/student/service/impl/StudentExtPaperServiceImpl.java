package com.jxw.shufang.student.service.impl;


import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.RemotePaperCommonService;
import com.jxw.shufang.extresource.api.domain.bo.RemotePhaseSubjectBo;
import com.jxw.shufang.extresource.api.domain.vo.RemotePhaseSubjectVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.service.IStudentExtPaperService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
@RequiredArgsConstructor
@Service
public class StudentExtPaperServiceImpl implements IStudentExtPaperService {

    @DubboReference
    private RemotePaperCommonService commonService;

    @DubboReference
    private RemoteCdsCommonService remoteCdsCommonService;

    @Override
    public List<RemotePhaseSubjectVo> getSubjectPhase(RemotePhaseSubjectBo bo) {
        List<RemotePhaseSubjectVo> subjectPhase = commonService.getSubjectPhase(bo);
        if (CollectionUtils.isEmpty(subjectPhase)) {
            return List.of();
        }
        List<Integer> collect = subjectPhase.stream().map(RemotePhaseSubjectVo::getSubjectId).distinct().toList();

        Map<Integer, RemoteSubjectVo> subjectVoMap = remoteCdsCommonService.getSubjectVoMap(collect);

        for (RemotePhaseSubjectVo remotePhaseSubjectVo : subjectPhase) {
            if (subjectVoMap.containsKey(remotePhaseSubjectVo.getSubjectId())) {
                RemoteSubjectVo remoteSubjectVo = subjectVoMap.get(remotePhaseSubjectVo.getSubjectId());
                remotePhaseSubjectVo.setName(remoteSubjectVo.getName());
            }
        }
        return subjectPhase;
    }
}
