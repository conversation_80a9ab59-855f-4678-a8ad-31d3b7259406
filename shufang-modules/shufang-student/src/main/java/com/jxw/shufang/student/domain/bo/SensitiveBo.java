package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Sensitive;

/**
 * 敏感词业务对象 sensitive
 *
 *
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Sensitive.class, reverseConvertGenerate = false)
public class SensitiveBo extends BaseEntity {

    /**
     * 敏感词id
     */
    private Long sensitiveId;

    /**
     * 敏感词组名
     */
    @NotBlank(message = "敏感词组名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sensitiveGroupName;

    /**
     * 敏感词内容（多个，换行符隔开）
     */
    @NotBlank(message = "敏感词内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sensitiveContent;


}
