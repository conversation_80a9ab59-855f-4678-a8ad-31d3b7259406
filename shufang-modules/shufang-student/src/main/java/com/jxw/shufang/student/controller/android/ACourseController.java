package com.jxw.shufang.student.controller.android;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.AnswerResultTypeEnum;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.GradeTreeVo;
import com.jxw.shufang.student.domain.vo.SpecialTopicCategoryVo;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.MqTemplateSendMessageService;
import com.jxw.shufang.student.service.RecordVideoProcessService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 课程（课程包含章节）-平板端
 * 前端访问路由地址为:/course/android/course
 *
 *
 * @date 2024-03-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/course")
@Slf4j
public class ACourseController extends BaseController {

    private final ICourseService courseService;

    private final DictService dictService;

    @DubboReference
    private RemoteExtVideoService extVideoService;

    /**
     * mybatis-plus的雪花id生成器
     */
    private final IdentifierGenerator identifierGenerator;

    private final MqTemplateSendMessageService sendMessageService;

    private final RecordVideoProcessService recordVideoProcessService;

    /**
     * 通过章节Id获取获取指定的课程资源(非视频资源)
     *
     * @param courseId 课程章节id
     * @param resourceType     类型
     *
     * @date 2024/05/08 07:49:36
     */
    @GetMapping("/getCourseResourceByCourseId")
    public R<RemoteKnowledgeResourceVo> getCourseResourceByCourseId(@NotNull(message = "课程章节ID不能为空") Long courseId, @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType) {
        return R.ok(courseService.getCourseResourceByCourseId(courseId, resourceType));
    }


    /**
     * 获取答案结果类型
     *
     *
     * @date 2024/05/09 09:24:57
     */
    @GetMapping("/getAnswerResultType")
    public R<List<Map<String, String>>> getAnswerResultType() {
        List<Map<String, String>> res = new ArrayList<>();
        Map<String, String> answerResultType = dictService.getAllDictByDictType("answer_result_type");
        for (AnswerResultTypeEnum value : AnswerResultTypeEnum.values()) {
            Map<String, String> resMap = new HashMap<>();
            String type = value.getType();
            resMap.put("type", type);
            if (answerResultType.containsKey(type)) {
                resMap.put("name", answerResultType.get(type));
            } else {
                resMap.put("name", value.getDefaultName());
            }
            res.add(resMap);
        }
        return R.ok(res);
    }

    /**
     * 获取AI学习课程专题分类
     */
    @GetMapping("/ai/getSpecialTopicCategory")
    public R<List<SpecialTopicCategoryVo>> getTopicCategory(CourseBo courseBo) {
        List<SpecialTopicCategoryVo>  specialTopicCategoryVos = courseService.getSpecialTopicCategory(courseBo,LoginHelper.getStudentId(),LoginHelper.getBranchId());
        return R.ok(specialTopicCategoryVos);
    }

    /**
     *  获取AI学习指定课程下的章节信息以及学习情况
     */
    @GetMapping("/ai/getChapterAndLearnInfo")
    public R<List<CourseVo>> getAiChapterAndLearnInfo(Long courseId) {
        return R.ok(courseService.getAiChapterAndLearnInfo(courseId, LoginHelper.getStudentId()));
    }

    @GetMapping("/ai/getCatalogAndLearnInfo")
    public R<List<Tree<Long>>> getAiCatalogAndLearnInfo(@RequestParam Long courseId) {
        return R.ok(courseService.getAiCatalogAndLearnInfo(courseId, LoginHelper.getStudentId()));
    }




    /**
     * 查询课程对应的知识点的题目列表
     *
     * @param courseId  课程id
     * @param resourceType           测试 TEST    练习 PRACTICE
     * @param withAnalysis          带分析
     * @param withAnswer            有答案
     *
     * @date 2024/05/09 04:03:47
     */
    @GetMapping("/getKnowledgeQuestionList")
    public R<List<RemoteQuestionVo>> getKnowledgeQuestionList(@NotNull(message = "课程id不能为空") Long courseId,
                                                              @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType,
                                                              Boolean withAnalysis,
                                                              Boolean withAnswer) {
        return R.ok(courseService.getKnowledgeQuestionList(courseId,resourceType, withAnalysis, withAnswer,true));
    }


    /**
     * 记录ai课程video学习进度
     * @param courseId 课程id
     * @param videoId 视频id
     * @param spliceItem 视频进度点
     */
    @PostMapping("/ai/recordVideoProgress")
    public R<String> recordVideoProgress(
                                         @NotNull(message = "courseId不能为空") Long courseId,
                                         @NotNull(message = "videoId不能为空") Long videoId,
                                         Double multiple,
                                         @NotBlank(message = "spliceItem不能为空")  String spliceItem
    ) {
        log.info("记录ai课程video学习进度:{},{},{},{}", courseId, videoId, multiple, spliceItem);

        if (multiple == null) {
            multiple = 1.0;
        }
        AiStudyVideoRecordBo aiStudyVideoRecordBo = new AiStudyVideoRecordBo();
        aiStudyVideoRecordBo.setCourseId(courseId);
        aiStudyVideoRecordBo.setVideoId(videoId);
        aiStudyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
        aiStudyVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(spliceItem, multiple));
        aiStudyVideoRecordBo.setCommitTime(new Date());
        aiStudyVideoRecordBo.setModuleAndGroupEnum(StudyModuleAndGroupEnum.AI_STUDY_LEARN);
        String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.AI_STUDY_VIDEO_DURATION_TIME_TAG;
        sendMessageService.sendAsyncMq(topicAndTagFlag,aiStudyVideoRecordBo);
        return R.ok();
    }

    /**
     * 查询ai课程章节资源(包括视频，讲义，练习，测试)
     */
    @GetMapping("/ai/getChapterResource/{courseId}")
    public R<CourseVo> getAiChapterResource(@NotNull(message = "课程id不能为空") @PathVariable Long courseId) {
        return R.ok(courseService.getAiChapterResource(courseId, LoginHelper.getStudentId()));
    }

    /**
     * 记录ai课程【预习】记录
     */
     @PostMapping("/ai/recordPreviewProgress")
     public R<String> recordPreviewProgress(@RequestBody @Validated PreviewStudyDurationTimeBO previewStudyDurationTimeBO){
         log.info("记录ai课程【预习】记录:{}", JSONUtil.toJsonStr(previewStudyDurationTimeBO));
         AiStudyVideoRecordBo studyVideoRecordBo = new AiStudyVideoRecordBo();
         studyVideoRecordBo.setCourseId(previewStudyDurationTimeBO.getCourseId());
         studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
         studyVideoRecordBo.setCommitTime(new Date());
         studyVideoRecordBo.setModuleAndGroupEnum(StudyModuleAndGroupEnum.AI_STUDY_PREVIEW);
         studyVideoRecordBo.setStudyVideoDuration(previewStudyDurationTimeBO.getStayPageTime());
         studyVideoRecordBo.setVideoId(previewStudyDurationTimeBO.getVideoId());
         String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.AI_STUDY_VIDEO_DURATION_TIME_TAG;
         sendMessageService.sendAsyncMq(topicAndTagFlag,studyVideoRecordBo);
         return R.ok();
     }

    /**
     * 记录ai课程【练习】记录
     */
     @PostMapping("/ai/recordPracticeProgress")
     public R<String> recordPracticeProgress(@RequestBody @Validated AiPracticeVideoProgressBO aiPracticeVideoProgressBO){
         log.info("记录ai课程【练习】记录:{}", JSONUtil.toJsonStr(aiPracticeVideoProgressBO));
         QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
         questionVideoRecordBo.setCourseId(aiPracticeVideoProgressBO.getCourseId());
         questionVideoRecordBo.setVideoId(aiPracticeVideoProgressBO.getVideoId());
         questionVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(
             aiPracticeVideoProgressBO.getSpliceItem(), aiPracticeVideoProgressBO.getMultiple())
         );
         questionVideoRecordBo.setStudentId(LoginHelper.getStudentId());
         questionVideoRecordBo.setCommitTime(new Date());
         questionVideoRecordBo.setQuestionId(aiPracticeVideoProgressBO.getQuestionId());
         questionVideoRecordBo.setQuestionType("2");
         questionVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.AI_STUDY_PRACTICE);
         questionVideoRecordBo.setStudyVideoDuration(aiPracticeVideoProgressBO.getStayPageTime());
         String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG;
         sendMessageService.sendAsyncMq(topicAndTagFlag,questionVideoRecordBo);
         return R.ok();
     }

    /**
     * 记录ai课程【测试】记录
     */
     @PostMapping("/ai/recordTestProgress")
     public R<String> recordTestProgress(@RequestBody @Validated AiTestStudyProgressBO aiTestStudyProgressBO){
         log.info("记录ai课程【测试】记录:{}", JSONUtil.toJsonStr(aiTestStudyProgressBO));
         QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
         questionVideoRecordBo.setCourseId(aiTestStudyProgressBO.getCourseId());
         questionVideoRecordBo.setVideoId(aiTestStudyProgressBO.getVideoId());
         questionVideoRecordBo.setStudyVideoSlices(VideoSlicesUtils.videoSliceConcatMultiple(aiTestStudyProgressBO.getSpliceItem(), aiTestStudyProgressBO.getMultiple()));
         questionVideoRecordBo.setStudentId(LoginHelper.getStudentId());
         questionVideoRecordBo.setCommitTime(new Date());
         questionVideoRecordBo.setQuestionId(aiTestStudyProgressBO.getQuestionId());
         questionVideoRecordBo.setQuestionType("1");
         questionVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.AI_STUDY_TEST);
         questionVideoRecordBo.setStudyVideoDuration(aiTestStudyProgressBO.getStayPageTime());
         String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG;
         sendMessageService.sendAsyncMq(topicAndTagFlag,questionVideoRecordBo);
         return R.ok();
     }

    /**
     * 记录ai课程【自讲】记录
     */
     @PostMapping("/ai/recordSelfSpeechProgress")
     public R<String> recordSelfSpeechProgress(@RequestBody @Validated SelfSpeechStudyDurationTimeBO speechStudyDurationTimeBO){
         log.info("记录ai课程【自讲】记录:{}", JSONUtil.toJsonStr(speechStudyDurationTimeBO));
         AiStudyVideoRecordBo studyVideoRecordBo = new AiStudyVideoRecordBo();
         studyVideoRecordBo.setCourseId(speechStudyDurationTimeBO.getCourseId());
         studyVideoRecordBo.setStudentId(LoginHelper.getStudentId());
         studyVideoRecordBo.setCommitTime(new Date());
         studyVideoRecordBo.setModuleAndGroupEnum(StudyModuleAndGroupEnum.AI_STUDY_SELF_SPEECH);
         studyVideoRecordBo.setVideoId(speechStudyDurationTimeBO.getSelfSpeechVideoId());
         studyVideoRecordBo.setStudyVideoDuration(speechStudyDurationTimeBO.getSelfSpeechTime());
         String topicAndTagFlag = MqTopicConstant.SHUFANG_STUDENT_TOPIC + ":" + MqTagConstant.AI_STUDY_VIDEO_DURATION_TIME_TAG;
         sendMessageService.sendAsyncMq(topicAndTagFlag,studyVideoRecordBo);
         return R.ok();
     }

    /**
     * 批量记录ai课程视频学习记录
     * @param aiBatchReportProcessBO
     * @return
     */
    @PostMapping("/ai/bacthRecordVideoProcess")
    public R<String> batchRecordVideoProcess(@RequestBody @Validated BatchAiReportProcessBO aiBatchReportProcessBO) {
        recordVideoProcessService.batchRecordAiVideoProgress(aiBatchReportProcessBO, LoginHelper.getStudentId());
        return R.ok();
    }

    /**
     * ai伴学课程联动接口
     * @param grade
     * @return
     */
    @GetMapping("/ai/getGradeTopicCategory")
    public R<List<GradeTreeVo>> getTopicCategoryV2(@RequestParam("grade") String grade, @RequestParam int defaultLayer) {
        List<GradeTreeVo> gradeList = courseService.getTopicCategoryV2(grade, defaultLayer);
        return R.ok(gradeList);
    }
}
