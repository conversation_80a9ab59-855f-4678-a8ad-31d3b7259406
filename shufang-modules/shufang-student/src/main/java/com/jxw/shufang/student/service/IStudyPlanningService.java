package com.jxw.shufang.student.service;

import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.StudyPlanning;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 学习规划Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IStudyPlanningService {

    /**
     * 查询学习规划
     */
    StudyPlanningVo queryById(Long studyPlanningId);

    /**
     * 查询学习规划列表
     */
    TableDataInfo<StudyPlanningVo> queryPageList(StudyPlanningBo bo, PageQuery pageQuery);

    /**
     * 查询学习规划列表(包含学习记录)
     */
    List<StudyPlanningVo> queryPlanAndRecordList(StudyPlanningBo bo);

    /**
     * 查询学习规划列表
     */
    List<StudyPlanningVo> queryList(StudyPlanningBo bo);

    /**
     * 新增学习规划
     */
    Boolean insertByBo(StudyPlanningBo bo);

    /**
     * 修改学习规划
     */
    Boolean updateByBo(StudyPlanningBo bo);

    /**
     * 校验并批量删除学习规划信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取学习计划学习状态
     *
     * @param bo        bo
     * @param studentId 会员Id
     *
     * @date 2024/04/29 01:31:46
     */
    List<StudyPlanningVo> getStudyPlanStudyStatus(StudyPlanningBo bo, Long studentId);

    /**
     * 获取学习计划对应的章节资源
     *
     * @param studyPlanningRecordId 学习规划记录Id
     * @param studentId             学生id
     *
     * @date 2024/05/06 08:47:59
     */
    CourseVo getChapterResource(Long studyPlanningRecordId, Long studentId);

    RemoteKnowledgeResourceVo getExtResource(Long studyPlanningRecordId, KnowledgeResourceType resourceType);

    RemoteKnowledgeResourceVo getExtResourceWithNoException(Long studyPlanningRecordId, KnowledgeResourceType resourceType);

    /**
     * 获取知识问题列表
     *
     * @param studyPlanningRecordId 学习规划记录Id
     * @param withAnalysis          带分析
     * @param withAnswer            有答案
     *
     * @date 2024/05/09 04:06:12
     */
    List<RemoteQuestionVo> getKnowledgeQuestionList(Long studyPlanningRecordId,Long courseId, KnowledgeResourceType resourceType, Boolean withAnalysis, Boolean withAnswer);

    /**
     * 获取学习计划完成进度（总和统计）
     *
     * @param bo 学习计划Bo
     * @param studentId 会员Id
     * @param queryMyRank 是否查询我的排名
     *
     * @date 2024/05/12 02:52:22
     */
    StudyPlanCompleteVo getPlanComplete(StudyPlanningBo bo, Long studentId, Boolean queryMyRank);

    /**
     * 获取时间区间内学科完成进度
     * @param studyPlanningDateStart 学习规划开始日期
     * @param studyPlanningDateEnd    学习规划结束日期
     * @param studentId 学生Id
     * @return 学科完成进度列表
     */
    List<SubjectCompleteVo> getSubjectCompleteByDateLimit(Date studyPlanningDateStart, Date studyPlanningDateEnd, Long studentId);


    List<StudyPlanningConflictVo> checkStudyPlanningConflict(List<StudyPlanningRecordBo> studyPlanningRecordBoList);

    /**
     * 排课
     *
     * @param list            列表
     * @param arrangementMode 模式 1单人排课 2多人排课
     * @param noRepeatLessons
     * @date 2024/06/02 05:19:33
     */
    void arrangementStudyPlanning(List<ArrangementStudyPlanningBo> list, Integer arrangementMode, Boolean noRepeatLessons);

    /**
     * 插入批次
     *
     * @param addStudyPlanningBoList 添加研究计划bo列表
     *
     * @date 2024/06/02 03:56:12
     */
    boolean insertBatch(List<StudyPlanning> addStudyPlanningBoList);

    /**
     * 批量更新为覆盖状态，并且如果关联的学习规划子记录已经没有正常的了，会删除学习规划,删除对应的座位信息
     *
     * @param studyPlanningRecordIdList 学习规划记录Id列表
     *
     * @date 2024/06/02 04:49:46
     */
    boolean batchUpdateCover(List<Long> studyPlanningRecordIdList);

    /**
     * 查询学习计划分页,能携带各种属性返回，具体看请求参数
     * @param studyPlanningBo
     * @param pageQuery
     * @return
     */
    TableDataInfo<StudyPlanningVo> queryStudyPlanPage(StudyPlanningBo studyPlanningBo, PageQuery pageQuery);

    StudyPlanningVo recodeCountAndStudentCount(StudyPlanningBo bo);

    List<Long> existStudyPlanningStuList(List<Long> studentIdList, String attendanceDate);

    Long countStudentStudyTime(Long studentId, Date startDate, Date endDate);

    List<StudyPlanningCourseRepeatVo> checkStudyPlanningRepeatCourse(StudyCheckCourseRepeatBo repeatBo);

    Boolean updateByBoV2(StudyPlanningUpdateBoV2 bo);

    TableDataInfo<StudyPlanningVo> queryStudyPlanList(StudyPlanningBo studyPlanningBo, PageQuery pageQuery);
}
