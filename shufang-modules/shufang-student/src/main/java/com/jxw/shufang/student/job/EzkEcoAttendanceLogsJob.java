package com.jxw.shufang.student.job;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.student.domain.bo.EzkEcoTransactionRequestBo;
import com.jxw.shufang.student.service.IEZKEcoService;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 * EzkEco考勤机定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EzkEcoAttendanceLogsJob implements BasicProcessor {

    private final IEZKEcoService ezkEcoService;

    @Override
    public ProcessResult process(TaskContext context) {
//        OmsLogger omsLogger = context.getOmsLogger();
//        String jobParams = context.getJobParams();

        // 同步考勤机关联系统关系表数据
        Integer count = ezkEcoService.manualSyncAllAttendanceLog(new EzkEcoTransactionRequestBo());
        if (count == 0) {
            return new ProcessResult(true, "EzkEco同步【考勤记录】任务，处理失败, 未查询到考勤记录。");
        }
        return new ProcessResult(true, "EzkEco同步【考勤记录】任务，处理成功的条数：" + count);
    }
}
