package com.jxw.shufang.student.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/7 19:51
 * @Version 1
 * @Description
 */
@Data
public class PrepareStudySlicesContextDTO {
    private Long studentId;
    private Long courseId;
    private String questionType;
    private Long studyPlanningRecordId;

    public static PrepareStudySlicesContextDTO of(Long studentId, Long courseId,String questionType,Long studyPlanningRecordId) {
        PrepareStudySlicesContextDTO context = new PrepareStudySlicesContextDTO();
        context.setStudentId(studentId);
        context.setCourseId(courseId);
        context.setQuestionType(questionType);
        context.setStudyPlanningRecordId(studyPlanningRecordId);
        return context;
    }
}
