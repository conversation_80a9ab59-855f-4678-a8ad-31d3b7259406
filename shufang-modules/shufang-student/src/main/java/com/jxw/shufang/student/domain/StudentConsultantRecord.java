package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）对象 student_consultant_record
 *
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_consultant_record")
public class StudentConsultantRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员顾问记录id
     */
    @TableId(value = "student_consultant_record_id")
    private Long studentConsultantRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 会员顾问id
     */
    private Long studentConsultantId;

    /**
     * 记录说明
     */
    private String recordRemark;


}
