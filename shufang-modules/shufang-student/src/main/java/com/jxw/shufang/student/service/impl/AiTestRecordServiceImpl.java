package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AiTestRecord;
import com.jxw.shufang.student.domain.bo.AiTestRecordBo;
import com.jxw.shufang.student.domain.vo.AiTestRecordVo;
import com.jxw.shufang.student.mapper.AiTestRecordMapper;
import com.jxw.shufang.student.service.IAiTestRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * ai测验记录Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class AiTestRecordServiceImpl implements IAiTestRecordService, BaseService {

    private final AiTestRecordMapper baseMapper;

    /**
     * 查询ai测验记录
     */
    @Override
    public AiTestRecordVo queryById(Long aiTestRecordId){
        return baseMapper.selectVoById(aiTestRecordId);
    }

    /**
     * 查询ai测验记录列表
     */
    @Override
    public TableDataInfo<AiTestRecordVo> queryPageList(AiTestRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiTestRecord> lqw = buildQueryWrapper(bo);
        Page<AiTestRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai测验记录列表
     */
    @Override
    public List<AiTestRecordVo> queryList(AiTestRecordBo bo) {
        LambdaQueryWrapper<AiTestRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiTestRecord> buildQueryWrapper(AiTestRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiTestRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCourseId() != null, AiTestRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getQuestionId() != null, AiTestRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getStudentId() != null, AiTestRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseResourceId() != null, AiTestRecord::getCourseResourceId, bo.getCourseResourceId());
        lqw.eq(bo.getResourceContent() != null, AiTestRecord::getResourceContent, bo.getResourceContent());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), AiTestRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), AiTestRecord::getAnswerResult, bo.getAnswerResult());
        lqw.eq(bo.getAnswerImg() != null, AiTestRecord::getAnswerImg, bo.getAnswerImg());
        return lqw;
    }

    /**
     * 新增ai测验记录
     */
    @Override
    public Boolean insertByBo(AiTestRecordBo bo) {
        AiTestRecord add = MapstructUtils.convert(bo, AiTestRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiTestRecordId(add.getAiTestRecordId());
        }
        return flag;
    }

    /**
     * 修改ai测验记录
     */
    @Override
    public Boolean updateByBo(AiTestRecordBo bo) {
        AiTestRecord update = MapstructUtils.convert(bo, AiTestRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiTestRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ai测验记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<AiTestRecordBo> convert) {
        List<AiTestRecord> list = MapstructUtils.convert(convert, AiTestRecord.class);
        return baseMapper.insertBatch(list);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
