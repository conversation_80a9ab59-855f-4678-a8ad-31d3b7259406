package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentParentRecord;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import com.jxw.shufang.student.domain.vo.StudentParentRecordVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.mapper.StudentParentRecordMapper;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）Service业务层处理
 *
 * @date 2024-03-11
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
@Slf4j
public class StudentParentRecordServiceImpl implements IStudentParentRecordService, BaseService {

    private final StudentParentRecordMapper baseMapper;

    private final IStudentService studentService;

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteWxService remoteWxService;


    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public StudentParentRecordVo queryById(Long studentParentRecordId) {
        return baseMapper.selectVoById(studentParentRecordId);
    }

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public TableDataInfo<StudentParentRecordVo> queryPageList(StudentParentRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentParentRecord> lqw = buildLambdaQueryWrapper(bo);
        Page<StudentParentRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public List<StudentParentRecordVo> queryList(StudentParentRecordBo bo) {
        LambdaQueryWrapper<StudentParentRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private QueryWrapper<StudentParentRecord> buildQueryWrapper(StudentParentRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<StudentParentRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getParentName()), "t.parent_name", bo.getParentName());
        lqw.like(StringUtils.isNotBlank(bo.getParentWechatNickname()), "t.parent_wechat_nickname", bo.getParentWechatNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatNo()), "t.parent_wechat_no", bo.getParentWechatNo());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatImg()), "t.parent_wechat_img", bo.getParentWechatImg());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatOpenId()), "t.parent_wechat_open_id", bo.getParentWechatOpenId());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatUnionId()), "t.parent_wechat_union_id", bo.getParentWechatUnionId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsFollow()), "t.is_follow", bo.getIsFollow());
        lqw.orderByDesc("t.create_time");
        return lqw;
    }

    private LambdaQueryWrapper<StudentParentRecord> buildLambdaQueryWrapper(StudentParentRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentParentRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentParentRecord::getStudentId, bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getParentName()), StudentParentRecord::getParentName, bo.getParentName());
        lqw.like(StringUtils.isNotBlank(bo.getParentWechatNickname()), StudentParentRecord::getParentWechatNickname, bo.getParentWechatNickname());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatNo()), StudentParentRecord::getParentWechatNo, bo.getParentWechatNo());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatImg()), StudentParentRecord::getParentWechatImg, bo.getParentWechatImg());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatOpenId()), StudentParentRecord::getParentWechatOpenId, bo.getParentWechatOpenId());
        lqw.eq(StringUtils.isNotBlank(bo.getParentWechatUnionId()), StudentParentRecord::getParentWechatUnionId, bo.getParentWechatUnionId());
        lqw.eq(StringUtils.isNotBlank(bo.getIsFollow()), StudentParentRecord::getIsFollow, bo.getIsFollow());
        return lqw;
    }

    /**
     * 新增会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(StudentParentRecordBo bo) {
        StudentParentRecord add = MapstructUtils.convert(bo, StudentParentRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentParentRecordId(add.getStudentParentRecordId());

            if (null != add.getStudentId()) {
                StudentBo studentBo = new StudentBo();
                studentBo.setStudentId(add.getStudentId());
                studentBo.setStudentParentRecordId(add.getStudentParentRecordId());
                studentService.updateStudentParentRecordIdByBo(studentBo);
            }
        }
        return flag;
    }

    /**
     * 修改会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(StudentParentRecordBo bo) {
        StudentParentRecord update = MapstructUtils.convert(bo, StudentParentRecord.class);
        validEntityBeforeSave(update);
        Boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            if (null != update.getStudentId()) {
                StudentBo studentBo = new StudentBo();
                studentBo.setStudentId(update.getStudentId());
                studentBo.setStudentParentRecordId(update.getStudentParentRecordId());
                studentService.updateStudentParentRecordIdByBo(studentBo);
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentParentRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public String getBoundQrCode(Long studentId) {
        return remoteWxService.getBoundQrCode(studentId);
    }

    @Override
    public String getFeedbackUrl(Long feedbackId) {
        return remoteWxService.getFeedbackUrl(feedbackId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bind(List<StudentVo> studentVoList, StudentParentRecordBo parentInfo) {
        List<StudentParentRecord> insertList = new ArrayList<>();

        for (StudentVo studentVo : studentVoList) {
            StudentParentRecord record = new StudentParentRecord();
            if (null != parentInfo.getParentWechatUnionId()) {
                record.setParentWechatOpenId(parentInfo.getParentWechatUnionId());
            } else {
                record.setParentWechatOpenId(parentInfo.getParentWechatOpenId());
            }

            record.setStudentId(studentVo.getStudentId());

            record.setIsFollow(parentInfo.getIsFollow());
            insertList.add(record);
        }
        boolean b = baseMapper.insertBatch(insertList);
        if (!b) {
            throw new ServiceException("插入绑定记录失败");
        }
        Map<Long, StudentParentRecord> identityMap = StreamUtils.toIdentityMap(insertList, StudentParentRecord::getStudentId);

        List<StudentBo> updateStuList = new ArrayList<>();

        studentVoList.forEach(studentVo -> {
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentId(studentVo.getStudentId());
            studentBo.setStudentParentRecordId(identityMap.get(studentVo.getStudentId()).getStudentParentRecordId());
            updateStuList.add(studentBo);
        });
        b = studentService.updateBatchByBo(updateStuList);
        if (!b) {
            throw new ServiceException("更新会员信息失败，绑定失败");
        }
    }

    @Override
    public boolean unbindParent(Long studentId) {
        return baseMapper.update(null,
            Wrappers.<StudentParentRecord>lambdaUpdate()
                .set(StudentParentRecord::getStudentId, null)
                .in(StudentParentRecord::getStudentId, studentId)
        ) > 0;
    }

    @Override
    public List<StudentParentRecordVo> queryBindList(StudentParentRecordBo studentParentRecordBo) {
        QueryWrapper<StudentParentRecord> studentParentRecordQueryWrapper = buildQueryWrapper(studentParentRecordBo);
        List<StudentParentRecordVo> studentParentRecordList = baseMapper.queryBindList(studentParentRecordQueryWrapper);
        if (Boolean.TRUE.equals(studentParentRecordBo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(studentParentRecordList);
        }
        return studentParentRecordList;
    }

    @Override
    public List<StudyPlanningRecordVo> queryAttendanceRecordList(StudentParentRecordBo studentParentRecordBo) {


        List<StudentParentRecordVo> studentParentRecordVos = queryBindList(studentParentRecordBo);
        if (CollUtil.isEmpty(studentParentRecordVos)) {
            return List.of();
        }
        List<Long> studentIdList = studentParentRecordVos.stream().map(StudentParentRecordVo::getStudentId).collect(Collectors.toList());


        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudentIdList(studentIdList);
        // 默认查询当天的考勤记录
        studyPlanningRecordBo.setStudyPlanningDate(new Date());
        studyPlanningRecordBo.setWithAttendanceInfo(Boolean.TRUE);
        studyPlanningRecordBo.setWithStudentInfo(Boolean.TRUE);
        studyPlanningRecordBo.setWithStudentSysUserInfo(Boolean.TRUE);
        return studyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningRecordBo);
    }

    @Override
    public TableDataInfo<AttendanceLogStudentEzkecoVo> pageAttendanceRecord(StudentParentRecordBo bo, PageQuery pageQuery) {
        log.info("公众号家长查询学生打卡记录入参:{}-{}", bo, pageQuery);
        List<StudentParentRecordVo> studentParentRecordVos = queryBindList(bo);
        if (CollUtil.isEmpty(studentParentRecordVos)) {
            return TableDataInfo.build();
        }

        List<Long> studentIdList = studentParentRecordVos.stream().map(StudentParentRecordVo::getStudentId).toList();
        AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo = new AttendanceLogStudentEzkecoBo();
        attendanceLogStudentEzkecoBo.setStudentIdList(studentIdList);
        attendanceLogStudentEzkecoBo.setRangeStartDate(bo.getAttendanceDate());
        attendanceLogStudentEzkecoBo.setRangeEndDate(bo.getAttendanceDate());
        log.info("家长查询学生打卡记录入参:{}-{}", attendanceLogStudentEzkecoBo, pageQuery);
        return attendanceLogStudentEzkecoService.queryPageRecord(attendanceLogStudentEzkecoBo, pageQuery);
    }

    private void putStudentSysUserInfo(List<StudentParentRecordVo> studentParentRecordList) {
        if (CollUtil.isEmpty(studentParentRecordList)) {
            return;
        }
        List<Long> sysUserIdList = studentParentRecordList.stream().map(StudentParentRecordVo::getStudent).filter(Objects::nonNull).map(StudentVo::getCreateBy).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(Boolean.TRUE);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        for (StudentParentRecordVo studentParentRecord : studentParentRecordList) {
            StudentVo student = studentParentRecord.getStudent();
            if (student != null && student.getCreateBy() != null) {
                student.setSysUser(remoteUserVoMap.get(student.getCreateBy()));
            }
        }
    }


    @Cacheable(value = "studentParentRecord", key = "#studentParentRecordId", condition = "#studentParentRecordId != null")
    @Override
    public StudentParentRecord queryStudentParentRecordById(Long studentParentRecordId) {
        return baseMapper.selectById(studentParentRecordId);
    }

    @Override
    public List<StudentParentRecord> queryStudentParentRecordByIdList(List<Long> studentParentRecordIdList) {
        return baseMapper.selectBatchIds(studentParentRecordIdList);
    }

    @CacheEvict(value = "studentParentRecord", allEntries = true)
    public void cleanCache() {
        log.info("===========studentParentRecordService cleanCache===========");
    }

    @Override
    public void init() {
        IStudentParentRecordService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentParentRecordService init===========");
        LambdaQueryWrapper<StudentParentRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentParentRecord::getStudentParentRecordId);
        List<StudentParentRecord> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========studentParentRecordService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentParentRecordById(item.getStudentParentRecordId());
        });
        log.info("===========studentParentRecordService init end===========");
    }


}
