package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 用户答题试卷记录对象 student_paper_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_paper_record")
public class StudentPaperRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "student_paper_record_id", type = IdType.ASSIGN_ID)
    private Long studentPaperRecordId;

    /**
     * 用户ID
     */
    private Long studentId;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 年级id
     */
    private Integer gradeId;

    /**
     * 试卷id
     */
    private Long testPaperId;

    /**
     * 试卷名称
     */
    private String testPaperName;

    /**
     * 试卷类型
     */
    private Integer testPaperType;
    /**
     * 评测范围
     */
    private String paperTypeName;
    /**
     * 试卷科目id
     */
    private Integer subjectId;

    /**
     * 正确率
     */
    private Integer score;

    /**
     * 学习记录状态（0未完成 1已完成）
     */
    private Integer recordStatus;

    /**
     * pdf报告url
     */
    private String reportUrl;

    /**
     * （来源） XWSF=学王书房
     */
    private String source;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date finishTime;


}
