package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.student.domain.PrintRecord;
import com.jxw.shufang.student.domain.bo.PrintRecordBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.PrintRecordMapper;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.IPaperService;
import com.jxw.shufang.student.service.IPrintRecordService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.*;
import com.jxw.shufang.system.api.domain.vo.*;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 打印记录Service业务层处理
 *
 * @date 2024-05-07
 */
@RequiredArgsConstructor
@Service
public class PrintRecordServiceImpl implements IPrintRecordService, BaseService {

    private final PrintRecordMapper baseMapper;

    private final IStudentService studentService;

    private final ICourseService courseService;

    private final IPaperService paperService;

    @DubboReference
    private RemoteExtResourceService remoteExtResourceService;

    @DubboReference
    private RemoteWxService wxService;

    @DubboReference
    private RemoteDictService remoteDictService;

    /**
     * 查询打印记录
     */
    @Override
    public PrintRecordVo queryById(Long printRecordId) {
        return baseMapper.selectVoById(printRecordId);
    }

    /**
     * 查询打印记录列表
     */
    @Override
    public TableDataInfo<PrintRecordVo> queryPageList(PrintRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<PrintRecord> qw = buildQueryWrapper(bo);
        Page<PrintRecordVo> result = baseMapper.selectPrintRecordVoPage(pageQuery.build(), qw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询打印记录列表
     */
    @Override
    public List<PrintRecordVo> queryList(PrintRecordBo bo) {
        LambdaQueryWrapper<PrintRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PrintRecord> buildLambdaQueryWrapper(PrintRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PrintRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPrintType()), PrintRecord::getPrintType, bo.getPrintType());
        lqw.eq(bo.getCourseId() != null, PrintRecord::getCourseId, bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getPrintContent()), PrintRecord::getPrintContent, bo.getPrintContent());
        lqw.eq(bo.getStudentId() != null, PrintRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanRecordId() != null, PrintRecord::getStudyPlanRecordId, bo.getStudyPlanRecordId());
        lqw.eq(bo.getPaperId() != null, PrintRecord::getPaperId, bo.getPaperId());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperType()), PrintRecord::getPaperType, bo.getPaperType());
        lqw.ge(bo.getCreateTimeLessThanMinute() != null, PrintRecord::getCreateTime, DateUtils.addMinutes(DateUtils.getNowDate(), -ObjectUtil.defaultIfNull(bo.getCreateTimeLessThanMinute(), 0)));
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), PrintRecord::getStudentId, bo.getStudentIdList());

        if (bo.getBranchId() != null || CollUtil.isNotEmpty(bo.getStudentIdList())) {
            List<Long> branchIdList = new ArrayList<>();
            if (bo.getBranchId() != null) {
                branchIdList.add(bo.getBranchId());
            }
            if (CollUtil.isNotEmpty(bo.getStudentIdList())) {
                branchIdList.addAll(bo.getBranchIdList());
            }
            List<Long> studentIdListByBranchIdList = studentService.getStudentIdListByBranchIdList(branchIdList);
            if (CollUtil.isNotEmpty(studentIdListByBranchIdList)) {
                lqw.in(PrintRecord::getStudentId, studentIdListByBranchIdList);
            } else {
                lqw.in(PrintRecord::getStudentId, List.of(-1L));
            }
        }
        return lqw;
    }

    private QueryWrapper<PrintRecord> buildQueryWrapper(PrintRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PrintRecord> lqw = Wrappers.query();
        lqw.eq(StringUtils.isNotBlank(bo.getPrintType()), "t.print_type", bo.getPrintType());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(StringUtils.isNotBlank(bo.getPrintContent()), "t.print_content", bo.getPrintContent());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getStudyPlanRecordId() != null, "t.study_plan_record_id", bo.getStudyPlanRecordId());
        lqw.eq(bo.getPaperId() != null, "t.paper_id", bo.getPaperId());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperType()), "t.paper_type", bo.getPaperType());
        lqw.ge(bo.getCreateTimeLessThanMinute() != null, "t.create_time", DateUtils.addMinutes(DateUtils.getNowDate(), -ObjectUtil.defaultIfNull(bo.getCreateTimeLessThanMinute(), 0)));
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        if (bo.getTaskType() != null) {
            /**
             * taskType (最近打印任务 显示最近30分钟内提交的打印任务 1)(历史打印任务 打印任务提交超过30分钟,将自动加入“历史打印任务”;历史打印任务保留至次日18:00 2)
             */
            if (bo.getTaskType() == 1) {
                lqw.ge("t.create_time", DateUtils.addMinutes(DateUtils.getNowDate(), -30));
            } else if (bo.getTaskType() == 2) {
                Date now = new Date();

                //没超过今天的18:30:00
                //if (DateUtils.timeToSeconds(now)<=DateUtils.timeToSeconds("18:30:00")){
                //    Calendar calendar = Calendar.getInstance();
                //    calendar.setTime(now);
                //    calendar.set(Calendar.HOUR_OF_DAY, 18);
                //    calendar.set(Calendar.MINUTE, 30);
                //    calendar.set(Calendar.SECOND, 0);
                //    calendar.add(Calendar.DATE, -1);
                //    lqw.apply("t.create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND t.create_time >=  {0}", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", calendar.getTime()));
                //}else {
                //    //如果超过了，查是今天的
                //    lqw.apply("t.create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND DATE(t.create_time) =  DATE(NOW())");
                //}
                lqw.apply("NOW() BETWEEN DATE_ADD(t.create_time, INTERVAL 30 MINUTE) AND concat(DATE(DATE_ADD(t.create_time, INTERVAL 1 DAY)) , ' 18:00:00')");


                //
            }
        }
        if (bo.getBranchId() != null || CollUtil.isNotEmpty(bo.getStudentIdList())) {
            List<Long> branchIdList = new ArrayList<>();
            if (bo.getBranchId() != null) {
                branchIdList.add(bo.getBranchId());
            }
            if (CollUtil.isNotEmpty(bo.getStudentIdList())) {
                branchIdList.addAll(bo.getBranchIdList());
            }
            List<Long> studentIdListByBranchIdList = studentService.getStudentIdListByBranchIdList(branchIdList);
            if (CollUtil.isNotEmpty(studentIdListByBranchIdList)) {
                lqw.in("t.student_id", studentIdListByBranchIdList);
            } else {
                lqw.in("t.student_id", List.of(-1L));
            }
        }


        return lqw;
    }

    /**
     * 新增打印记录
     */
    @Override
    public Boolean insertByBo(PrintRecordBo bo) {
        PrintRecord add = MapstructUtils.convert(bo, PrintRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPrintRecordId(add.getPrintRecordId());
        }
        return flag;
    }

    /**
     * 修改打印记录
     */
    @Override
    public Boolean updateByBo(PrintRecordBo bo) {
        PrintRecord update = MapstructUtils.convert(bo, PrintRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PrintRecord entity) {
        if (StringUtils.isNotBlank(entity.getPrintContent())) {
            String[] split = entity.getPrintContent().split(",");
            for (String item : split) {
                if (KnowledgeResourceType.getByType(item) == null) {
                    throw new RuntimeException("打印内容不合法");
                }
            }
        }
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除打印记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<PrintRecordInfoVo> queryInfo(Long printRecordId) {
        PrintRecordVo printRecordVo = baseMapper.selectVoById(printRecordId);
        Long studentId = printRecordVo.getStudentId();
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(studentId);
        StudentVo studentVo = studentService.queryById(studentBo);
        List<PrintRecordInfoVo> result = new ArrayList<>();
        if (Objects.equals(printRecordVo.getPrintSource(), 3)) {
            PaperVo paperVo = paperService.queryById(printRecordVo.getPaperId());
            List<RemoteDictDataVo> paperAffiliationSubject = remoteDictService.selectDictDataByType("paper_affiliation_subject");
            String subjectName = paperAffiliationSubject.stream().filter(i -> paperVo.getPaperAffiliationSubject().equalsIgnoreCase(i.getDictValue())).findFirst().orElse(new RemoteDictDataVo()).getDictLabel();
            paperVo.setPaperAffiliationSubjectName(subjectName);
            PrintRecordInfoVo printRecordInfoVo = new PrintRecordInfoVo();
            printRecordInfoVo.setPrintRecordId(printRecordId);
            printRecordInfoVo.setPaperType(printRecordVo.getPaperType());
            printRecordInfoVo.setPaper(paperVo);
            printRecordInfoVo.setPrintSource(3);
            printRecordInfoVo.setStudent(studentVo);
            result.add(printRecordInfoVo);
        } else {
            CourseVo courseVo = courseService.queryById(printRecordVo.getCourseId());
            if (courseVo == null) {
                return List.of();
            }
            courseService.putTopmostCourseInfo(List.of(courseVo), false);
            courseService.putCourseDetail(List.of(courseVo.getTopmostCourse()), false);
            String printContent = printRecordVo.getPrintContent();
            String[] split = printContent.split(",");
            RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
            remoteKnowledgeResourceBo.setTypeList(Arrays.asList(split));
            remoteKnowledgeResourceBo.setKnowledgeId(courseVo.getKnowledgeId());
            List<RemoteGroupResourceVo> knowledgeResourceList = remoteExtResourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);
            if (CollUtil.isEmpty(knowledgeResourceList)) {
                return List.of();
            }
            knowledgeResourceList = knowledgeResourceList.stream().filter(e -> e.getKnowledgeResource() != null).toList();
            if (CollUtil.isEmpty(knowledgeResourceList)) {
                return List.of();
            }
            for (RemoteGroupResourceVo remoteGroupResourceVo : knowledgeResourceList) {
                PrintRecordInfoVo printRecordInfoVo = new PrintRecordInfoVo();
                printRecordInfoVo.setPrintRecordId(printRecordId);
                printRecordInfoVo.setPrintSource(printRecordVo.getPrintSource());
                printRecordInfoVo.setStudent(studentVo);
                printRecordInfoVo.setCourse(courseVo);
                printRecordInfoVo.setResourceType(remoteGroupResourceVo.getType());
                printRecordInfoVo.setResource(remoteGroupResourceVo.getKnowledgeResource());
                if (remoteGroupResourceVo.getType().equals(KnowledgeResourceType.TEST) || remoteGroupResourceVo.getType().equals(KnowledgeResourceType.PRACTICE)) {
                    JSONObject json = new JSONObject();
                    json.set("printSource", printRecordVo.getPrintSource());
                    json.set("studentId", Convert.toStr(studentId));
                    json.set("courseId", Convert.toStr(courseVo.getCourseId()));
                    json.set("resourceType", remoteGroupResourceVo.getType());
                    json.set("studyPlanRecordId", Convert.toStr(printRecordVo.getStudyPlanRecordId()));
                    String jsonStr = json.toJSONString(0);
                    jsonStr = jsonStr.replace("\n", "");
                    QrConfig qrConfig = new QrConfig(300, 300);
                    qrConfig.setMargin(1);
                    String qrCode = QrCodeUtil.generateAsBase64(wxService.generateMiniProgramUrl(jsonStr), qrConfig, ImgUtil.IMAGE_TYPE_PNG);
                    printRecordInfoVo.setQrCodeBase64(qrCode);
                }
                result.add(printRecordInfoVo);
            }
        }
        result.sort(Comparator.comparing(v ->
        {
            if (Objects.isNull(v.getResourceType())) {
                return Integer.MAX_VALUE;
            }
            return v.getResourceType().getSortNum();
        }));
        return result;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
