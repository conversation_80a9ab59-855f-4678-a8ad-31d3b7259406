package com.jxw.shufang.student.mapper;

import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.WrongQuestionCollection;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 错题合集Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface WrongQuestionCollectionMapper extends BaseMapperPlus<WrongQuestionCollection, WrongQuestionCollectionVo> {

    /**
     * 查询错题合集列表
     */
    List<WrongQuestionCollectionVo> selectWrongQuestionCollectionList(WrongQuestionCollectionBo bo);

    /**
     * 根据学生ID查询错题合集
     */
    List<WrongQuestionCollectionVo> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和类型查询错题合集
     */
    List<WrongQuestionCollectionVo> selectByStudentIdAndType(@Param("studentId") Long studentId, @Param("collectionType") Integer collectionType);

    /**
     * 统计学生错题合集数量
     */
    int countByStudentId(@Param("studentId") Long studentId);

    /**
     * 统计学生指定状态的错题合集数量
     */
    int countByStudentIdAndStatus(@Param("studentId") Long studentId, @Param("collectionStatus") Integer collectionStatus);

    /**
     * 批量查询错题合集
     */
    List<WrongQuestionCollectionVo> selectByIds(@Param("ids") List<Long> ids);

}
