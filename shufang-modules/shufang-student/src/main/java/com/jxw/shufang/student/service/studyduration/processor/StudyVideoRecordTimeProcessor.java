package com.jxw.shufang.student.service.studyduration.processor;

import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyVideoRecordDTO;
import com.jxw.shufang.student.domain.dto.StudyDurationProcessingContextDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/4/3 16:11
 * @Version 1
 * @Description 学习视频记录（视频观看记录）保存时长处理
 */
public class StudyVideoRecordTimeProcessor {
    private List<StudyVideoRecord> updates = new ArrayList<>();
    private List<StudyVideoRecord> inserts = new ArrayList<>();
    private StudyDurationProcessingContextDTO context;

    public StudyVideoRecordTimeProcessor(StudyDurationProcessingContextDTO context) {
        this.context = context;
    }

    public Student getStudent(Long studentId) {
        return context.getStudentMap().get(studentId);
    }

    public List<StudyVideoRecord> getUpdates() {
        return updates;
    }

    public List<StudyVideoRecord> getInserts() {
        return inserts;
    }

    /**
     * 处理视频记录保存时长
     *
     * @param recordDTO
     */
    public void process(SaveOrUpdateStudyVideoRecordDTO recordDTO) {
        if (null == recordDTO) {
            return;
        }
        StudyVideoRecord existing = context.getExistVideoRecordMap().get(recordDTO.getStudyPlanningRecordId());
        Student student = this.getStudent(recordDTO.getStudentId());
        if (existing != null) {
            Optional.ofNullable(this.buildUpdateVideoRecord(existing, recordDTO)).ifPresent(updates::add);
        } else {
            Long courseDuration = context.getCourseDurationMap().get(recordDTO.getVideoId());
            Optional.of(this.buildInsertVideoRecord(recordDTO, student, courseDuration)).ifPresent(inserts::add);
        }
    }

    /**
     * 构建视频记录保存对象
     *
     * @param recordDTO
     * @param student
     * @param courseDuration
     * @return
     */
    private StudyVideoRecord buildInsertVideoRecord(SaveOrUpdateStudyVideoRecordDTO recordDTO, Student student, Long courseDuration) {
        StudyVideoRecord insertRecord = new StudyVideoRecord();
        insertRecord.setStudentId(recordDTO.getStudentId());
        insertRecord.setStudyPlanningRecordId(recordDTO.getStudyPlanningRecordId());
        insertRecord.setCourseId(recordDTO.getCourseId());
        insertRecord.setVideoId(recordDTO.getVideoId());
        insertRecord.setCreateBy(student.getCreateBy());
        insertRecord.setCreateDept(student.getCreateDept());
        insertRecord.setCreateTime(recordDTO.getCommitTime());
        insertRecord.setUpdateTime(recordDTO.getCommitTime());
        insertRecord.setUpdateBy(student.getCreateBy());
        insertRecord.setStudyModuleType(recordDTO.getModuleType());
        insertRecord.setStudyVideoSlices(recordDTO.getStudyVideoSlices());
        this.setStudyVideoDurationTime(recordDTO, insertRecord);
        insertRecord.setDuration(courseDuration);
        return insertRecord;
    }

    private void setStudyVideoDurationTime(SaveOrUpdateStudyVideoRecordDTO recordDTO, StudyVideoRecord insertRecord) {
        Long sliceSeconds = this.getSliceSeconds(recordDTO.getStudyVideoDuration(), recordDTO.getStudyVideoSlices());
        Long courseDuration = Optional.ofNullable(context.getCourseDurationMap())
            .map(map -> map.get(recordDTO.getVideoId()))
            .orElse(0L);
        boolean overCourseMaxTime = sliceSeconds >= courseDuration;
        long remainDuration = courseDuration - sliceSeconds;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;
        insertRecord.setStudyVideoDuration(useCourseDuration ? courseDuration : sliceSeconds);
    }

    /**
     * 构建视频记录更新对象
     *
     * @param existRecord
     * @param recordDTO
     * @return
     */
    private StudyVideoRecord buildUpdateVideoRecord(StudyVideoRecord existRecord, SaveOrUpdateStudyVideoRecordDTO recordDTO) {
        Student student = this.getStudent(recordDTO.getStudentId());
        Map<Long, Long> courseDurationMap = context.getCourseDurationMap();

        Long courseDuration = Optional.ofNullable(courseDurationMap.get(recordDTO.getVideoId())).orElse(0L);
        String oldVideoSlices = existRecord.getStudyVideoSlices();
        Long oldVideoDuration = existRecord.getStudyVideoDuration();
        String newStudyVideoSlices = recordDTO.getStudyVideoSlices();
        Long newStudyVideoDuration = recordDTO.getStudyVideoDuration();

        String mergedSlices = VideoSlicesUtils.mergeVideoSlices(oldVideoSlices, newStudyVideoSlices);
        Long maxDuration = this.getMaxDuration(existRecord, newStudyVideoDuration, mergedSlices);

        boolean updateRecordFlag = maxDuration > oldVideoDuration;
        if (!updateRecordFlag) {
            return null;
        }

        StudyVideoRecord updateBean = new StudyVideoRecord();
        updateBean.setStudyVideoRecordId(existRecord.getStudyVideoRecordId());
        updateBean.setStudyVideoSlices(mergedSlices);
        long remainDuration = courseDuration - maxDuration;
        boolean overCourseMaxTime = maxDuration > courseDuration;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;
        updateBean.setStudyVideoDuration(useCourseDuration ? courseDuration : maxDuration);
        updateBean.setUpdateBy(student.getCreateBy());
        updateBean.setUpdateTime(recordDTO.getCommitTime());
        return updateBean;
    }

    /**
     * 计算最大时长
     *
     * @param existing
     * @param studyVideoDuration
     * @param mergedSlices
     * @return
     */
    private Long getMaxDuration(StudyVideoRecord existing, Long studyVideoDuration, String mergedSlices) {
        Long maxDuration = 0L;
        if (StrUtil.isEmpty(mergedSlices)) {
            maxDuration = Optional.ofNullable(studyVideoDuration).orElse(0L) + existing.getStudyVideoDuration();
        } else {
            maxDuration = this.getSliceSecondsBySlice(mergedSlices);
        }
        return maxDuration;
    }

    /**
     * 返回时长。如果入参传递了时长则不使用分片的数据，直接用时长
     *
     * @param studyVideoDuration
     * @param videoSlice
     * @return
     */
    private Long getSliceSeconds(Long studyVideoDuration, String videoSlice) {
        if (null != studyVideoDuration && studyVideoDuration > 0) {
            return studyVideoDuration;
        }
        return getSliceSecondsBySlice(videoSlice);
    }

    /**
     * 根据分片计算时长
     *
     * @param videoSlice
     * @return
     */
    private Long getSliceSecondsBySlice(String videoSlice) {
        return VideoSlicesUtils.calVideoSliceSeconds(videoSlice);
    }
}
