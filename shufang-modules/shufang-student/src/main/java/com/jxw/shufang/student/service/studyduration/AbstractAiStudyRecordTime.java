package com.jxw.shufang.student.service.studyduration;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;
import com.jxw.shufang.student.domain.dto.*;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.IAiStudyRecordService;
import com.jxw.shufang.student.service.IAiStudyVideoRecordService;
import com.jxw.shufang.student.service.studyduration.processor.AiStudyTimeProcessor;
import com.jxw.shufang.student.service.studyduration.processor.AiStudyVideoTimeProcessor;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/8 11:53
 * @Version 1
 * @Description
 */
@Service
public abstract class AbstractAiStudyRecordTime<T> implements ModuleGroupProvider<T> {
    @Resource
    private IAiStudyVideoRecordService aiStudyVideoRecordService;
    @Resource
    private IAiStudyRecordService aiStudyRecordService;

    public abstract SaveOrUpdateAiStudyRecordDTO buildRecordProcessDTO(T recordBo);

    public abstract SaveOrUpdateAiStudyVideoRecordDTO buildVideoRecordProcessDTO(T recordBo);

    public abstract AiStudyDurationProcessingContextDTO contextData(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum);

    @GlobalTransactional(rollbackFor = Exception.class)
    public void process(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<T> filterData = this.filterData(records, moduleAndGroupEnum);

        // 设置上下文数据
        AiStudyDurationProcessingContextDTO contextData = this.contextData(filterData, moduleAndGroupEnum);

        // 数据处理
        AiStudyVideoTimeProcessor videoProcessor = new AiStudyVideoTimeProcessor(contextData);
        AiStudyTimeProcessor studyProcessor = new AiStudyTimeProcessor(contextData);
        records.forEach(recordBo -> {
            videoProcessor.process(this.buildVideoRecordProcessDTO(recordBo));
            studyProcessor.process(this.buildRecordProcessDTO(recordBo));
        });

        this.batchSaveOrUpdate(videoProcessor, studyProcessor);
    }

    private void batchSaveOrUpdate(AiStudyVideoTimeProcessor videoProcessor, AiStudyTimeProcessor studyProcessor) {
        List<AiStudyVideoRecord> videoProcessorInserts = videoProcessor.getInserts();
        List<AiStudyVideoRecord> videoProcessorUpdates = videoProcessor.getUpdates();
        List<AiStudyRecord> studyProcessorInserts = studyProcessor.getInserts();
        List<AiStudyRecord> studyProcessorUpdates = studyProcessor.getUpdates();
        if (CollectionUtil.isNotEmpty(videoProcessorUpdates)) {
            aiStudyVideoRecordService.batchUpdate(videoProcessorUpdates);
        }
        if (CollectionUtil.isNotEmpty(videoProcessorInserts)) {
            aiStudyVideoRecordService.saveBatch(videoProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorInserts)) {
            aiStudyRecordService.batchInsert(studyProcessorInserts);
        }
        if (CollectionUtil.isNotEmpty(studyProcessorUpdates)) {
            aiStudyRecordService.batchUpdate(studyProcessorUpdates);
        }
    }
}
