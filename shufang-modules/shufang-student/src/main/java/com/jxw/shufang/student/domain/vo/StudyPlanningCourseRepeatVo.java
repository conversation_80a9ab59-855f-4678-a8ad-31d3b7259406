package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 学习计划学习课程重复排查vo
 *
 *
 * @date 2024/05/31 03:19:46
 */
@Data
public class StudyPlanningCourseRepeatVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 会员信息
     */
    private Long studentId;

    /**
     *
     */
    private String studentName;

}
