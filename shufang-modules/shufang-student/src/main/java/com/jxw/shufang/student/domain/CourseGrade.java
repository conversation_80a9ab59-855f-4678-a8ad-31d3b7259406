package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: cyj
 * @date: 2025/3/28
 */
@Data
@TableName("course_grade")
@AllArgsConstructor
@NoArgsConstructor
public class CourseGrade implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程年级id
     */
    @TableId
    private Long courseGradeId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 年级
     */
    private String grade;

    public CourseGrade(Long courseId, String grade) {
        this.courseId = courseId;
        this.grade = grade;
    }
}
