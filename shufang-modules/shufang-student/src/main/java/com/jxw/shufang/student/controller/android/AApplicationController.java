package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.ApplicationVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * 应用中心--平板端
 * 前端访问路由地址为:/student/android/application
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/application")
public class AApplicationController extends BaseController {


    /**
     * 查询应用列表
     */
    @GetMapping("/list")
    public R<List<ApplicationVo>> list() {
        ApplicationVo applicationVo = new ApplicationVo();
        applicationVo.setApplicationName("QQ");
        applicationVo.setApplicationThumbnailUrl("https://gkdwyz.oss-cn-beijing.aliyuncs.com/img/39ec09deda3a41d79e03897b0fdf68a0.jpeg");
        applicationVo.setApplicationApkUrl("https://gkdwyz.oss-cn-beijing.aliyuncs.com/img/7_base.apk");
        ArrayList<ApplicationVo> applicationVos = new ArrayList<>();
        applicationVos.add(applicationVo);
        return R.ok(applicationVos);
    }

}
