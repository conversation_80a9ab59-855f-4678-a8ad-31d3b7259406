package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteFeedbackRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteFeedbackRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteFeedbackRecordVo;
import com.jxw.shufang.student.domain.bo.FeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.FeedbackRecordVo;
import com.jxw.shufang.student.service.IFeedbackRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteFeedbackRecordServiceImpl implements RemoteFeedbackRecordService {

    private final IFeedbackRecordService feedbackRecordService;


    @Override
    public List<RemoteFeedbackRecordVo> queryList(RemoteFeedbackRecordBo remoteFeedbackRecordBo, boolean ignoreDataPermission) {
        FeedbackRecordBo convert = MapstructUtils.convert(remoteFeedbackRecordBo, FeedbackRecordBo.class);
        List<FeedbackRecordVo> list = null;
        if (ignoreDataPermission){
            list = DataPermissionHelper.ignore(() -> feedbackRecordService.queryList(convert));
        }else {
            list = feedbackRecordService.queryList(convert);
        }
        return MapstructUtils.convert(list, RemoteFeedbackRecordVo.class);
    }
}
