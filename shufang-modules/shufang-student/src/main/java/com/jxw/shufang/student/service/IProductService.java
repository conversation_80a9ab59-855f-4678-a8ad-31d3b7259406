package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.Product;
import com.jxw.shufang.student.domain.bo.ProductBo;
import com.jxw.shufang.student.domain.vo.ProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品（会员卡）Service接口
 *
 *
 * @date 2024-02-29
 */
public interface IProductService {

    /**
     * 查询产品（会员卡）
     */
    ProductVo queryById(Long productId);

    /**
     * 查询产品（会员卡）列表
     */
    TableDataInfo<ProductVo> queryPageList(ProductBo bo, PageQuery pageQuery);

    /**
     * 查询产品（会员卡）列表
     */
    List<ProductVo> queryList(ProductBo bo);

    /**
     * 新增产品（会员卡）
     */
    Boolean insertByBo(ProductBo bo);

    /**
     * 修改产品（会员卡）
     */
    Boolean updateByBo(ProductBo bo);

    /**
     * 校验并批量删除产品（会员卡）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询选项列表
     *
     * @param bo bo
     *
     * @date 2024/03/04 02:55:51
     */
    List<ProductVo> queryOptionList(ProductBo bo);

    /**
     * 更改产品状态
     *
     * @param productId     产品id
     * @param productStatus 产品状态
     *
     * @date 2024/03/08 03:58:39
     */
    int changeProductStatus(Long productId, String productStatus);

    Product queryProductById(Long productId);

    void cleanCache();

    /**
     * 按产品id查询学生类型id
     *
     * @param productIds 产品ID
     *
     * @date 2024/06/02 08:06:15
     */
    List<Long> queryStudentTypeIdByProductIds(List<Long> productIds);

    ProductVo queryRecentPeriodCard(Long branchId);
}
