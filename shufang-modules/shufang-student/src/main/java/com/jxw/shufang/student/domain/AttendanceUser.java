package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 考勤关联用户对象 attendance_user
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_user")
public class AttendanceUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "attendance_user_id")
    private Long attendanceUserId;

    /**
     * 考勤机域名或IP
     */
    private String ip;

    /**
     * 系统用户id
     */
    private Long userId;


}
