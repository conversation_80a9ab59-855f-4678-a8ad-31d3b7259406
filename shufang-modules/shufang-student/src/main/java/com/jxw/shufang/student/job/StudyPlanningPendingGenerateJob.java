package com.jxw.shufang.student.job;

import com.jxw.shufang.student.service.IStudyPlanningPendingService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 生成待规划学生记录定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StudyPlanningPendingGenerateJob {

    private final IStudyPlanningPendingService studyPlanningPendingService;

    /**
     * 生成待规划学生记录定时任务
     *
     * 任务特点：
     * 1. 具有幂等性，重复执行不会产生重复数据
     * 2. 建议配置重试机制，重试次数：3次，重试间隔：30秒
     * 3. 失败时会抛出异常，触发xxl-job重试机制
     */
    @XxlJob("studyPlanningPendingGenerateJob")
    public void execute() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("=========【生成待规划学生记录】定时任务开始执行，任务参数：{}=========", jobParam);

        try {
            Boolean result;
            // 如果有输入参数，则按输入的参数日期生成数据
            if (jobParam != null && !jobParam.trim().isEmpty()) {
                log.info("使用任务参数日期生成数据：{}", jobParam);
                result = studyPlanningPendingService.generatePendingStudents(jobParam.trim());
            } else {
                log.info("使用当前日期生成数据");
                result = studyPlanningPendingService.generatePendingStudents();
            }

            if (result) {
                log.info("=========【生成待规划学生记录】定时任务执行成功=========");
                XxlJobHelper.handleSuccess("生成待规划学生记录任务执行成功");
            } else {
                log.error("=========【生成待规划学生记录】定时任务执行失败=========");
                XxlJobHelper.handleFail("生成待规划学生记录任务执行失败");
            }
        } catch (Exception e) {
            log.error("【生成待规划学生记录】定时任务执行异常", e);
            // 抛出异常，触发xxl-job重试机制
            XxlJobHelper.handleFail("生成待规划学生记录任务执行异常：" + e.getMessage());
        }
    }
}
