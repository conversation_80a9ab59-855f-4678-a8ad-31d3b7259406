package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class GradeTreeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 当前树节点的类型
     */
    private String dictType;
    /**
     * 当前树节点名称
     */
    private String dictName;
    /**
     * 唯一标识
     */
    private String dictValue;

    /**
     * 当前树节点的排序
     */
    private Integer dictSort;

    private List<GradeTreeVo> childrenList;

    /**
     * 课程ID
     */
    private Long courseId;


}

