package com.jxw.shufang.student.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/30 16:10
 * @Version 1
 * @Description
 */
@Data
public class StudentRemainingTimeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -8187435690203638468L;
    /**
     * 学生ID
     */
    private Long studentId;
    /**
     * 剩余天数
     */
    private Long remainingDays;
    /**
     * 剩余总小时数
     */
    private Long remainingHours;

    public StudentRemainingTimeDTO(Long studentId, Long remainingDays, Long remainingHours) {
        this.studentId = studentId;
        this.remainingDays = remainingDays;
        this.remainingHours = remainingHours;
    }


    public static StudentRemainingTimeDTO calculate(Long studentId, Date startTime, Date endTime, LocalDateTime now) {
        LocalDateTime start = startTime.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();
        LocalDateTime end = endTime.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();

        // 计算剩余时长
        Duration duration;
        if (now.isBefore(start)) {
            duration = Duration.between(start, end);
        } else if (!now.isAfter(end)) {
            duration = Duration.between(now, end);
        } else {
            duration = Duration.ZERO;
        }
        long days = duration.toDays();
        long totalHours = duration.toHours();
        return new StudentRemainingTimeDTO(studentId, days, totalHours);
    }
}
