package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 学习规划模板对象 study_planning_template
 *
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_planning_template")
public class StudyPlanningTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划模板id
     */
    @TableId(value = "study_planning_template_id")
    private Long studyPlanningTemplateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateInfo;

    /**
     * 模板说明
     */
    private String templateDesc;

    /**
     * 课程学段（与课程共用一个字典）
     */
    private String courseStage;

    /**
     * 课程学年（与课程共用一个字典），这里允许存在多个，使用逗号分隔
     */
    private String courseGrades;

    /**
     * 模版上下架状态，0：下架，1：上架
     */
    private String templateStatus;

    /**
     * 模板来源，对应枚举类SourceEnum
     */
    private String sourceType;

    /**
     * 来源为门店的时候，这里关联门店id
     */
    private Long branchId;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
