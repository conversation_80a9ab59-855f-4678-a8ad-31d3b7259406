package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyPlanning;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;

import java.util.List;

/**
 * 学习规划Mapper接口
 * @date 2024-04-23
 */
public interface StudyPlanningMapper extends BaseMapperPlus<StudyPlanning, StudyPlanningVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept"),
        @DataColumn(key = "userName", value = "t.create_by")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    List<StudyPlanningVo> queryPlanAndRecordList(@Param(Constants.WRAPPER) QueryWrapper<StudyPlanning> wrapper);
}
