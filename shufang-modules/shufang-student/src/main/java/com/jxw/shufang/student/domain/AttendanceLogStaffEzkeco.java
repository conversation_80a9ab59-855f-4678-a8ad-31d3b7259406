package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * ezkeco员工考勤记录对象 attendance_log_staff_ezkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attendance_log_staff_ezkeco")
public class AttendanceLogStaffEzkeco extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "attendance_log_staff_ezkeco_id")
    private Long attendanceLogStaffEzkecoId;

    /**
     * 流水号
     */
    private Long logId;

    /**
     * 验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)
     */
    private String verify;

    /**
     * 打卡时间
     */
    private Date checktime;

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 设备名称
     */
    private String alias;

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 考勤状态说明
     */
    private String state;

    /**
     * 考勤关联用户_id
     */
    private Long attendanceUserId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 人员姓名
     */
    private String nickName;

    /**
     * 照片地址
     */
    private String photograph;

    /**
     * 备注说明
     */
    private String remark;


}
