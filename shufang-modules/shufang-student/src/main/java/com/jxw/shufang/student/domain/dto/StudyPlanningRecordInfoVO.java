package com.jxw.shufang.student.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/2/17 19:16
 * @Version 1
 * @Description
 */
@Data
public class StudyPlanningRecordInfoVO {
    /**
     * 学习规划记录id
     */
    private Long studyPlanningRecordId;

    /**
     * 学习规划ID
     */
    private Long studyPlanningId;

    /**
     * 会员id
     */
    private Long studentId;


    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 学习规划日期
     */
    private Date studyPlanningDate;


    /**
     * 开始时间
     */
    private String studyStartTime;

    /**
     * 结束时间
     */
    private String studyEndTime;

    /**
     * 会员顾问名称
     */
    private String staffName;

    /**
     * 到店和登录情况(目前没有设置取值)
     */
    private Date attendanceTime;

    /**
     * 学习内容
     */
    private String studyContent;
    /**
     * 内容详情
     */
    private String contentDetail;

    /**
     * 机位(目前没有设置取值)
     */
    private String branchMachineSeat;

    private String nameWithPhone;

    /**
     * 课程是否完成
     */
    private Boolean studyCompleteStatus;

    /**
     * 是否下载 0未下载 1已下载
     */
    private Integer downloadStatus;

}
