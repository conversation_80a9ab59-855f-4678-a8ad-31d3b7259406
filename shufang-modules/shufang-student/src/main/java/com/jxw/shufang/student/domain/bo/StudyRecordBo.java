package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 学习记录业务对象 study_record
 *
 *
 * @date 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyRecord.class, reverseConvertGenerate = false)
public class StudyRecordBo extends BaseEntity {

    /**
     * 学习记录id
     */
    @NotNull(message = "学习记录id不能为空", groups = { EditGroup.class })
    private Long studyRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 学习规划记录Id
     */
    @NotNull(message = "学习规划记录Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 学习视频时长（单位为秒 此为多个视频总计）
     */
    @NotNull(message = "学习视频时长（单位为秒 此为多个视频总计）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyVideoTotalDuration;

    /**
     * 练习状态（1未批改 2批改中 3已批改）
     */
    @NotBlank(message = "练习状态（1未批改 2批改中 3已批改）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String practiceState;

    /**
     * 练习-正确题目数量
     */
    @NotNull(message = "练习-正确题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long practiceRightNum;

    /**
     * 练习-错误题目数量
     */
    @NotNull(message = "练习-错误题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long practiceWrongNum;

    /**
     * 练习-未答题目数量
     */
    @NotNull(message = "练习-未答题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long practiceUnansweredNum;

    /**
     * 练习视频时长（单位为秒 此为多个视频总计）
     */
    @NotNull(message = "练习视频时长（单位为秒 此为多个视频总计）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long practiceVideoTotalDuration;

    /**
     * 测验状态（1未批改 2批改中 3已批改）
     */
    @NotBlank(message = "测验状态（1未批改 2批改中 3已批改）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String testState;

    /**
     * 测验-正确题目数量
     */
    @NotNull(message = "测验-正确题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long testRightNum;

    /**
     * 测验-错误题目数量
     */
    @NotNull(message = "测验-错误题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long testWrongNum;

    /**
     * 测验-未答题目数量
     */
    @NotNull(message = "测验-未答题目数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long testUnansweredNum;

    /**
     * 测验视频时长（单位为秒 此为多个视频总计）
     */
    @NotNull(message = "测验视频时长（单位为秒 此为多个视频总计）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long testVideoTotalDuration;

    private List<Long> studyPlanningRecordIdList;

    /**
     * 学习时长大于
     */
    private Long gtStudyVideoTotalDuration;

    /**
     * 学习规划开始时间--范围查询,需配合studyPlanningDateEnd使用
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDateStart;

    /**
     * 学习规划结束时间--范围查询,需配合studyPlanningDateStart使用
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDateEnd;

    private List<Long> studentIdList;

}
