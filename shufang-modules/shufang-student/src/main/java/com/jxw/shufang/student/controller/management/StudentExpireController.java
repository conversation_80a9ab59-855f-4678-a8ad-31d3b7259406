package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentExpireBo;
import com.jxw.shufang.student.domain.vo.StudentExpireVo;
import com.jxw.shufang.student.service.IStudentExpireService;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员过期（用于 会员过期列 的展示和数据操作）
 * 前端访问路由地址为:/student/studentExpire
 *
 *
 * @date 2024-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studentExpire")
public class StudentExpireController extends BaseController {

    private final IStudentExpireService studentExpireService;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    /**
     * 查询会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    @SaCheckPermission("student:studentExpire:list")
    @GetMapping("/list")
    public TableDataInfo<StudentExpireVo> list(StudentExpireBo bo, PageQuery pageQuery) {

        return studentExpireService.queryPageList(bo, pageQuery);
    }

    /**
     * 修改过期迁移时限
     */
    @SaCheckPermission("student:studentExpire:editExpireConfig")
    @Log(title = "会员过期->过期迁移时限", businessType = BusinessType.UPDATE)
    @PutMapping("/editExpireConfig")
    public R<Void> editExpireConfig(Integer expireDays) {
        remoteConfigService.editStudentExpireTime(expireDays);
        return R.ok();
    }
    /**
     * 查询过期迁移时限
     */
    @GetMapping("/getExpireConfig")
    public R<Integer> getExpireConfig() {
        Integer i = remoteConfigService.selectStudentExpireTime();
        return R.ok(i);
    }


    /**
     * 导出会员过期（用于 会员过期列 的展示和数据操作）列表
     */
    @SaCheckPermission("student:studentExpire:export")
    @Log(title = "会员过期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentExpireBo bo, HttpServletResponse response) {
        List<StudentExpireVo> list = studentExpireService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员过期（用于 会员过期列 的展示和数据操作）", StudentExpireVo.class, response);
    }

    /**
     * 获取会员过期（用于 会员过期列 的展示和数据操作）详细信息
     *
     * @param studentExpireId 主键
     */
    @SaCheckPermission("student:studentExpire:query")
    @GetMapping("/{studentExpireId}")
    public R<StudentExpireVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentExpireId) {
        return R.ok(studentExpireService.queryById(studentExpireId));
    }

    /**
     * 新增会员过期（用于 会员过期列 的展示和数据操作）
     */
    @SaCheckPermission("student:studentExpire:add")
    @Log(title = "会员过期（用于 会员过期列 的展示和数据操作）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentExpireBo bo) {
        return toAjax(studentExpireService.insertByBo(bo));
    }

    /**
     * 修改会员过期（用于 会员过期列 的展示和数据操作）
     */
    @SaCheckPermission("student:studentExpire:edit")
    @Log(title = "会员过期（用于 会员过期列 的展示和数据操作）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentExpireBo bo) {
        return toAjax(studentExpireService.updateByBo(bo));
    }

    /**
     * 删除会员过期（用于 会员过期列 的展示和数据操作）
     *
     * @param studentExpireIds 主键串
     */
    @SaCheckPermission("student:studentExpire:remove")
    @Log(title = "会员过期（用于 会员过期列 的展示和数据操作）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentExpireIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentExpireIds) {
        return toAjax(studentExpireService.deleteWithValidByIds(List.of(studentExpireIds), true));
    }
}
