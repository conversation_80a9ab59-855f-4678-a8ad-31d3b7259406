package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.CorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;
import com.jxw.shufang.student.service.ICorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 批改记录
 * 前端访问路由地址为:/student/correctionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/correctionRecord")
public class CorrectionRecordController extends BaseController {

    private final ICorrectionRecordService correctionRecordService;

    /**
     * 查询批改记录列表
     */
    @SaCheckPermission("student:correctionRecord:list")
    @GetMapping("/list")
    public TableDataInfo<CorrectionRecordVo> list(CorrectionRecordBo bo, PageQuery pageQuery) {
        return correctionRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出批改记录列表
     */
    @SaCheckPermission("student:correctionRecord:export")
    @Log(title = "批改记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CorrectionRecordBo bo, HttpServletResponse response) {
        List<CorrectionRecordVo> list = correctionRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "批改记录", CorrectionRecordVo.class, response);
    }

    /**
     * 获取批改记录详细信息
     *
     * @param correctionRecordId 主键
     */
    @SaCheckPermission("student:correctionRecord:query")
    @GetMapping("/{correctionRecordId}")
    public R<CorrectionRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long correctionRecordId) {
        return R.ok(correctionRecordService.queryById(correctionRecordId));
    }

    /**
     * 新增批改记录
     */
    @SaCheckPermission("student:correctionRecord:add")
    @Log(title = "批改记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CorrectionRecordBo bo) {
        correctionRecordService.insertByBo(bo);
        return R.ok();
    }

    /**
     * 修改批改记录
     */
    @SaCheckPermission("student:correctionRecord:edit")
    @Log(title = "批改记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CorrectionRecordBo bo) {
        return toAjax(correctionRecordService.updateByBo(bo));
    }

    /**
     * 删除批改记录
     *
     * @param correctionRecordIds 主键串
     */
    @SaCheckPermission("student:correctionRecord:remove")
    @Log(title = "批改记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{correctionRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] correctionRecordIds) {
        return toAjax(correctionRecordService.deleteWithValidByIds(List.of(correctionRecordIds), true));
    }
}
