package com.jxw.shufang.student.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudyRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyRecordVo;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import com.jxw.shufang.student.service.IStudyRecordService;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudyRecordServiceImpl implements RemoteStudyRecordService {

    private final IStudyRecordService studyRecordService;

    @Override
    public List<RemoteStudyRecordVo> queryList(RemoteStudyRecordBo remoteStudyRecordBo, boolean ignoreDataPermission) {
        StudyRecordBo convert = MapstructUtils.convert(remoteStudyRecordBo, StudyRecordBo.class);
        List<StudyRecordVo> list = null;
        if (ignoreDataPermission){
            list = DataPermissionHelper.ignore(() -> studyRecordService.queryList(convert));
        }else {
            list = studyRecordService.queryList(convert);
        }
        return MapstructUtils.convert(list, RemoteStudyRecordVo.class);
    }
}
