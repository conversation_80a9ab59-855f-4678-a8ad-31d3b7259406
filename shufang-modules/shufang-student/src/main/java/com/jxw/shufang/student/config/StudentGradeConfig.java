package com.jxw.shufang.student.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "course-grade")
@Data
public class StudentGradeConfig {

    /**
     * 小学关联的年级
     */
    private List<String> xiaoxue;

    /**
     * 初中关联的年级
     */
    private List<String> juniorHigh;

    /**
     * 高中关联的年级
     */
    private List<String> seniorHigh;

    /**
     * 属性名称集合
     */
    private List<String> attributeNameList;
}
