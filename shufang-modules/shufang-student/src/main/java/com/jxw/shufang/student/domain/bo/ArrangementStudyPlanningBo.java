package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import com.jxw.shufang.common.core.validate.AddGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class ArrangementStudyPlanningBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "会员ID不能为空", groups = { AddGroup.class })
    private Long studentId;

    @NotEmpty(message = "课程安排不能为空", groups = { AddGroup.class })
    private List<StudyPlanningRecordBo> studyPlanningRecordList;

    private List<Long> delList;
}
