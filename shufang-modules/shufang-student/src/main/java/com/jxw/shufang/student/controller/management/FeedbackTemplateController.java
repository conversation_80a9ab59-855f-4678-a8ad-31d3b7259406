package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateSourceVo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateVo;
import com.jxw.shufang.student.service.IFeedbackTemplateService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 反馈模板
 * 前端访问路由地址为:/student/feedbackTemplate
 *
 *
 * @date 2024-03-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/feedbackTemplate")
public class FeedbackTemplateController extends BaseController {

    private final IFeedbackTemplateService feedbackTemplateService;

    /**
     * 查询反馈模板列表
     */
    @SaCheckPermission("student:feedbackTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<FeedbackTemplateVo> list(FeedbackTemplateBo bo, PageQuery pageQuery) {
        bo.setWithDeptInfo(true);
        bo.setDelFlag(UserConstants.DEL_FLAG_NO);
        return feedbackTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出反馈模板列表
     */
    @SaCheckPermission("student:feedbackTemplate:export")
    @Log(title = "反馈模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FeedbackTemplateBo bo, HttpServletResponse response) {
        List<FeedbackTemplateVo> list = feedbackTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "反馈模板", FeedbackTemplateVo.class, response);
    }

    /**
     * 获取反馈模板详细信息
     *
     * @param feedbackTemplateId 主键
     */
    @SaCheckPermission("student:feedbackTemplate:query")
    @GetMapping("/{feedbackTemplateId}")
    public R<FeedbackTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long feedbackTemplateId) {
        return R.ok(feedbackTemplateService.queryById(feedbackTemplateId));
    }

    /**
     * 新增反馈模板
     */
    @SaCheckPermission("student:feedbackTemplate:add")
    @Log(title = "反馈模板", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FeedbackTemplateBo bo) {
        return toAjax(feedbackTemplateService.insertByBo(bo));
    }

    /**
     * 修改反馈模板
     */
    @SaCheckPermission("student:feedbackTemplate:edit")
    @Log(title = "反馈模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FeedbackTemplateBo bo) {
        return toAjax(feedbackTemplateService.updateByBo(bo));
    }

    /**
     * 删除反馈模板
     *
     * @param feedbackTemplateIds 主键串
     */
    @SaCheckPermission("student:feedbackTemplate:remove")
    @Log(title = "反馈模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{feedbackTemplateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] feedbackTemplateIds) {
        return toAjax(feedbackTemplateService.deleteWithValidByIds(List.of(feedbackTemplateIds), true));
    }

    /**
     * 查询反馈模板列表
     */
    @GetMapping("/sourceOptions")
    public R<List<FeedbackTemplateSourceVo>> sourceOptions() {
        return R.ok(feedbackTemplateService.sourceOptions());
    }

    /**
     * 模版使用次数步进1
     */
    @PostMapping("/stepTemplateUseCount")
    public R<Void> stepTemplateUseCount(@NotNull(message = "主键不能为空") Long feedbackTemplateId) {
        return toAjax(feedbackTemplateService.stepTemplateUseCount(feedbackTemplateId));
    }

}
