package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 反馈记录Service业务层处理
 *
 * @date 2024-05-24
 */
@RequiredArgsConstructor
@Service
public class StudyDailyShareServiceImpl implements IStudyDailyShareService, BaseService {

    private final IStudyPlanningService iStudyPlanningService;
    private final IStudentService iStudentService;
    private final IStudyPlanningRecordService iStudyPlanningRecordService;
    private final IFeedbackRecordService feedbackRecordService;
    private final IStudentDataStatisticsService iStudentDataStatisticsService;
    private final IStudentConsultantRecordService studentConsultantRecordService;
    @DubboReference
    private RemoteFileService ossService;
    @DubboReference
    private RemoteStaffService remoteStaffService;
    @DubboReference
    private RemoteBranchService remoteBranchService;
    @DubboReference
    private RemoteExtResourceService remoteExtResourceService;
    @DubboReference
    private RemoteWxService remoteWxService;



    @Override
    public StuDailyShareVo queryByIdForH5(Long feedbackRecordId) {
        FeedbackRecordVo feedbackRecordVo = feedbackRecordService.queryById(feedbackRecordId, false, false);
        if (ObjectUtils.isEmpty(feedbackRecordVo)) {
            return null;
        }
        StuDailyShareVo stuDailyShareVo = new StuDailyShareVo();

        stuDailyShareVo.setFeedbackRecordVo(MapstructUtils.convert(feedbackRecordVo, FeedbackRecordShareVo.class));
        Student student = iStudentService.queryStudentById(feedbackRecordVo.getStudentId());
        if (!ObjectUtils.isEmpty(student)) {
            stuDailyShareVo.setStudentName(student.getStudentName());
            stuDailyShareVo.setStudentCreateTime(student.getCreateTime());
            stuDailyShareVo.setStudentParentRecordId(student.getStudentParentRecordId());
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchById(student.getBranchId());
            if (!ObjectUtils.isEmpty(remoteBranchVo)) {
                stuDailyShareVo.setBranchName(remoteBranchVo.getBranchName());
            }
        }

        //获取学习反馈中这段时间的学习规划
        List<StudyPlanningRecordVo> thisTimePlanningList = feedbackRecordService.getThisTimeStudyPlanningVos(feedbackRecordVo);
        putCourseRepeatStatus(student.getStudentId(), thisTimePlanningList);
        //统计这段时间学习规划的学习状况
        //studyPlanningVos
        //计算该学生历史累计学习时间
        Long totalStudyTime = iStudyPlanningService.countStudentStudyTime(feedbackRecordVo.getStudentId(), null, null);
        //统计该学生在本周累计的学习时长
        Long thisWeekStudyTime = iStudyPlanningService.countStudentStudyTime(feedbackRecordVo.getStudentId(),
            DateUtils.getStartOfThisWeek(),
            DateUtils.getEndOfThisWeek());
        Long thisStudyTime = iStudyPlanningService.countStudentStudyTime(feedbackRecordVo.getStudentId(), feedbackRecordVo.getFeedbackStartDate(), getEndDay(feedbackRecordVo.getFeedbackEndDate()));

        //获取全区域的本周的学习排名
        StudyTimeRankVo weekRank = iStudentDataStatisticsService.getStudyTimeRankList("W", 0,null);
        RemoteStaffVo consultantInfo = getConsultantInfo(feedbackRecordVo.getStudentId());
        //获取最近一次的学习规划
        StudyPlanningRecordVo latestPlanningVo = getLatestStudyPlanningVos(feedbackRecordVo);
        if (!ObjectUtils.isEmpty(latestPlanningVo)) {
            StuShareLatestCourseVo latestCourseVo = getStuShareLatestCourseVo(latestPlanningVo);
            stuDailyShareVo.setLatestCourseVo(latestCourseVo);
        }
        if (!CollectionUtils.isEmpty(thisTimePlanningList)){
            List<Long> knowledgeIdList = thisTimePlanningList.stream()
                .map(StudyPlanningRecordVo::getCourse)
                .filter(v -> !ObjectUtils.isEmpty(v))
                .map(CourseVo::getKnowledgeId)
                .filter(v -> !ObjectUtils.isEmpty(v))
                .toList();
            RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
            remoteKnowledgeResourceBo.setTypeList(List.of(KnowledgeResourceType.PRACTICE.getType()));
            remoteKnowledgeResourceBo.setKnowledgeIdList(knowledgeIdList);
            //获取课程的练习文件
            List<RemoteKnowledgeResourceVo> remoteKnowledgeResourceVoList =getRemoteKnowledgeResourceVosList(remoteKnowledgeResourceBo);
            stuDailyShareVo.setResourceList(remoteKnowledgeResourceVoList);
        }
        stuDailyShareVo.setStaffVo(consultantInfo);
        stuDailyShareVo.setTotalStudyTime(totalStudyTime);
        stuDailyShareVo.setThisWeekStudyTime(thisWeekStudyTime);
        stuDailyShareVo.setThisTimeStudyTime(thisStudyTime);
        if (!ObjectUtils.isEmpty(weekRank)) {
            stuDailyShareVo.setStudyTimeRank(weekRank.getStudyTimeRankEntities());
        } else {
            stuDailyShareVo.setStudyTimeRank(List.of());
        }
        stuDailyShareVo.setThisTimePlanningList(thisTimePlanningList);
        afterSetDoingTrace(stuDailyShareVo,thisTimePlanningList);
        return stuDailyShareVo;
    }

    private List<RemoteKnowledgeResourceVo> getRemoteKnowledgeResourceVosList(RemoteKnowledgeResourceBo remoteKnowledgeResourceBo) {
        List<RemoteGroupResourceVo> knowledgeResourceList = remoteExtResourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);
        if (CollectionUtils.isEmpty(knowledgeResourceList)){
            return List.of();
        }
        return knowledgeResourceList.stream().map(RemoteGroupResourceVo::getKnowledgeResource).filter(v->!ObjectUtils.isEmpty(v)).toList();
    }

    private Date getEndDay(Date feedbackEndDate) {
        if (ObjectUtils.isEmpty(feedbackEndDate)){
            return null;
        }
        LocalDateTime localDateTime = feedbackEndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atTime(LocalTime.MAX);

        // 将LocalDateTime转换回Date对象
      return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private StuShareLatestCourseVo getStuShareLatestCourseVo(StudyPlanningRecordVo latestPlanningVo) {
        if (ObjectUtils.isEmpty(latestPlanningVo)){
            return null;
        }
        CourseVo course = latestPlanningVo.getCourse();
        if (ObjectUtils.isEmpty(course)){
            return null;
        }
        CourseVo topmostCourse = course.getTopmostCourse();
        if (ObjectUtils.isEmpty(topmostCourse)){
            return null;
        }

        StuShareLatestCourseVo stuShareLatestCourseVo = new StuShareLatestCourseVo();
        stuShareLatestCourseVo.setClassName(course.getCourseName());
        stuShareLatestCourseVo.setCourseId(course.getCourseId());
        stuShareLatestCourseVo.setCourseName(topmostCourse.getCourseName());
        stuShareLatestCourseVo.setSubjectName(topmostCourse.getAffiliationSubjectName());

        Date studyPlanningDate = latestPlanningVo.getStudyPlanningDate();
        Date studyStartTime = latestPlanningVo.getStudyStartTime();
        Date studyEndTime = latestPlanningVo.getStudyEndTime();
        stuShareLatestCourseVo.setStudyPlanningDate(studyPlanningDate);
        stuShareLatestCourseVo.setStudyStartTime(studyStartTime);
        stuShareLatestCourseVo.setStudyEndTime(studyEndTime);

        return stuShareLatestCourseVo;

    }



    private void afterSetDoingTrace(StuDailyShareVo stuDailyShareVo, List<StudyPlanningRecordVo> thisTimePlanningList) {
        FeedbackRecordShareVo feedbackRecordVo = getFeedbackRecordVo(stuDailyShareVo);

        StuDailyShareStatisticVo result = feedbackRecordService.getResult(thisTimePlanningList);
        stuDailyShareVo.setDoStudyTime(result.getDoStudyTime());
        stuDailyShareVo.setDoPracticeTime(result.getDoPracticeTime());
        stuDailyShareVo.setDoTestTime(result.getDoTestTime());
        stuDailyShareVo.setDoTestAccuracy(result.getDoTestAccuracy());

        FeedbackRecordVo latestFeedbackRecord = feedbackRecordService.queryLatestFeedbackRecord(feedbackRecordVo.getFeedbackRecordId(), feedbackRecordVo.getStudentId());
        if  (!ObjectUtils.isEmpty(latestFeedbackRecord)) {
            StuDailyShareStatisticVo stuDailyShareStatisticVo = feedbackRecordService.queryStuDailyShareStatistic(latestFeedbackRecord);
            if (!ObjectUtils.isEmpty(stuDailyShareStatisticVo)){
                stuDailyShareVo.setDoLatestTestAccuracy(stuDailyShareStatisticVo.getDoTestAccuracy());
            }
        }
    }

    private static FeedbackRecordShareVo getFeedbackRecordVo(StuDailyShareVo stuDailyShareVo) {
        return stuDailyShareVo.getFeedbackRecordVo();
    }

    private StudyPlanningRecordVo getLatestStudyPlanningVos(FeedbackRecordVo feedbackRecordVo) {
        Date now = new Date();
        StudyPlanningRecordBo studyPlanningBo = new StudyPlanningRecordBo();
        studyPlanningBo.setStudentId(feedbackRecordVo.getStudentId());
        studyPlanningBo.setDateYearMonthLimitStart(DateUtils.dateTime(now));
        studyPlanningBo.setWithCourseDetail(Boolean.TRUE);
        List<StudyPlanningRecordVo> latestPlanningList = DataPermissionHelper.ignore(() -> iStudyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningBo));
        if (CollectionUtils.isEmpty(latestPlanningList)) {
            return null;
        }
        return latestPlanningList.stream()
            .peek(v -> {
                Date studyPlanningDate = v.getStudyPlanningDate();
                Date studyStartTime = v.getStudyStartTime();
                Date date = combineDates(studyPlanningDate, studyStartTime);
                v.setLatestTime(date);
            })
            .filter(v -> !ObjectUtils.isEmpty(v.getLatestTime()))
            .filter(v -> now.before(v.getLatestTime()))
            .min(Comparator.comparing(StudyPlanningRecordVo::getLatestTime)).orElse(null);


    }

    public Date combineDates(Date studyPlanningDate, Date studyStartTime) {
        LocalDate planningDate = studyPlanningDate.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();

        LocalTime startTime = studyStartTime.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalTime();

        LocalDateTime combinedDateTime = LocalDateTime.of(planningDate, startTime);

        return Date.from(combinedDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private RemoteKnowledgeResourceVo getRemoteKnowledgeResourceVos(Long studyPlanningRecordId) {
        if (ObjectUtils.isEmpty(studyPlanningRecordId)) {
            return null;
        }
        return iStudyPlanningService.getExtResourceWithNoException(studyPlanningRecordId, KnowledgeResourceType.PRACTICE);
    }



    private List<StudyPlanningRecordVo> getThisTimeStudyPlanningWithBo(StudyPlanningRecordBo studyPlanningBo) {
        List<StudyPlanningRecordVo> studyPlanningList = DataPermissionHelper.ignore(() -> iStudyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningBo));
        if (CollectionUtils.isEmpty(studyPlanningList)) {
            return Collections.emptyList();
        }

        return studyPlanningList.stream().sorted(Comparator.comparing(StudyPlanningRecordVo::getStudyPlanningDate).thenComparing(StudyPlanningRecordVo::getStudyStartTime).reversed()).toList();
    }



    private void putCourseRepeatStatus(Long studentId, List<StudyPlanningRecordVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> courseIdList = list.stream()
            .map(StudyPlanningRecordVo::getCourseId)
            .filter(courseId -> !ObjectUtils.isEmpty(courseId))
            .distinct()
            .toList();
        Map<Long, List<StudyPlanningRecordVo>> longListMap =
            iStudyPlanningRecordService.checkCourseRepeatWithCorrection(courseIdList, studentId);

        for (StudyPlanningRecordVo studyPlanningRecordVo : list) {
            CourseVo studyRecordVo = studyPlanningRecordVo.getCourse();
            studyPlanningRecordVo.setRepeatStatus(false);
            if (!ObjectUtils.isEmpty(studyRecordVo)) {
                List<StudyPlanningRecordVo> studyList = longListMap.getOrDefault(studyPlanningRecordVo.getCourseId(), List.of());
                if (CollectionUtils.isEmpty(studyList) || studyList.size() == 1) {
                    continue;
                }
                studyList
                    .stream()
                    .min(Comparator.comparing(StudyPlanningRecordVo::getLatestTime))
                    .ifPresent(v-> studyPlanningRecordVo.setRepeatStatus(!studyPlanningRecordVo.getStudyPlanningRecordId().equals(v.getStudyPlanningRecordId())));

                //獲取學過的課程最低的測試、練習分數
                setPractice(studyPlanningRecordVo, studyList);
                setTest(studyPlanningRecordVo, studyList);

            }
        }


    }

    private static void setTest(StudyPlanningRecordVo studyPlanningRecordVo, List<StudyPlanningRecordVo> studyList) {
        StudyPlanningRecordVo testRecordVo =  studyList
            .stream()
            .filter(v-> !Objects.equals(v.getStudyPlanningRecordId(), studyPlanningRecordVo.getStudyPlanningRecordId()))
            .filter(v->Objects.nonNull(v.getTestCorrectionRecord()))
            .min(
            Comparator.comparing(
                recordVo -> recordVo.getTestCorrectionRecord().getRightCount())
        ).orElse(null);
        if (!ObjectUtils.isEmpty(testRecordVo)){
            studyPlanningRecordVo.setLastTestCorrectionRecord(testRecordVo.getTestCorrectionRecord());
        }
    }

    private static void setPractice(StudyPlanningRecordVo studyPlanningRecordVo, List<StudyPlanningRecordVo> studyList) {
        StudyPlanningRecordVo practiceRecordVo = studyList
            .stream()
            .filter(v-> !Objects.equals(v.getStudyPlanningRecordId(), studyPlanningRecordVo.getStudyPlanningRecordId()))
            .filter(v->Objects.nonNull(v.getPracticeCorrectionRecord()))
            .min(Comparator.comparing(recordVo -> recordVo.getPracticeCorrectionRecord().getRightCount())
        ).orElse(null);
        if (!ObjectUtils.isEmpty(practiceRecordVo)){
            studyPlanningRecordVo.setLastPracticeCorrectionRecord(practiceRecordVo.getPracticeCorrectionRecord());
        }
    }


    private void setScreenshotsUrl(CorrectionRecordVo recordVo) {
        if (ObjectUtils.isEmpty(recordVo)) {
            return;
        }
        String correctionScreenshots = recordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)) {
            String urls = ossService.selectUrlByIds(correctionScreenshots);
            if (StringUtils.isNotBlank(urls)) {
                recordVo.setCorrectionScreenshotsUrl(List.of(urls.split(",")));
            }
        }
    }

    private RemoteStaffVo getConsultantInfo(Long studentId) {

        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(List.of(studentId));
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return null;
        }
        List<Long> consultantIdList = studentConsultantIdMap.values().stream().distinct().toList();
        if (CollUtil.isEmpty(consultantIdList)) {
            return null;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(consultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return null;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(studentConsultantIdMap.get(studentId));
        if (ObjectUtils.isEmpty(remoteStaffVo)) {
            return null;
        }
        Long realAvatar = remoteStaffVo.getRealAvatar();
        String realAvatarUrl = getUrl(realAvatar);
        remoteStaffVo.setRealAvatarUrl(realAvatarUrl);

        Long wechatQrCode = remoteStaffVo.getWechatQrCode();
        String wechatQrCodeUrl = getUrl(wechatQrCode);
        remoteStaffVo.setWechatQrCodeUrl(wechatQrCodeUrl);
        return remoteStaffVo;
    }

    private String getUrl(Long url) {
        if (ObjectUtils.isEmpty(url)) {
            return null;
        }
        String realAvatarId = String.valueOf(url);
        return ossService.selectUrlByIds(realAvatarId);
    }

    @Override
    public void read(Long feedbackRecordId) {
        feedbackRecordService.read(feedbackRecordId);
    }

    @Override
    public String getQrCode() {
        return remoteWxService.getQrCode();
    }
}
