package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.lang.tree.Tree;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 课程专题，包含课程树
 * @date 2024-02-29
 */
@Data
public class SpecialTopicCourseTreeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专题(对应字典course_special_topic)
     */
    private String specialTopic;

    /**
     * 课程列表
     */
    private List<Tree<Long>> courseTreeList;
}
