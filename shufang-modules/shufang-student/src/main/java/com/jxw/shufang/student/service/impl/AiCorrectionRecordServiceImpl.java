package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.AnswerResultTypeEnum;
import com.jxw.shufang.common.core.enums.CorrectionStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.student.domain.AiCorrectionRecord;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.AiCorrectionRecordMapper;
import com.jxw.shufang.student.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.jxw.shufang.common.core.constant.UserConstants.CORRECTION_TYPE_SPEAK;

/**
 * ai批改记录Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class AiCorrectionRecordServiceImpl implements IAiCorrectionRecordService, BaseService {

    private final AiCorrectionRecordMapper baseMapper;

    private final ICourseService courseService;

    private final IAiCorrectionRecordInfoService aiCorrectionRecordInfoService;

    private final IAiPracticeRecordService aiPracticeRecordService;

    private final IAiTestRecordService aiTestRecordService;

    private final IAiWrongQuestionRecordService aiWrongQuestionRecordService;

    private final IAiStudyRecordService aiStudyRecordService;

    @DubboReference(mock = "true")
    private RemoteFileService ossService;


    /**
     * 查询ai批改记录
     */
    @Override
    public AiCorrectionRecordVo queryById(Long aiCorrectionRecordId){
        return baseMapper.selectVoById(aiCorrectionRecordId);
    }

    /**
     * 查询ai批改记录列表
     */
    @Override
    public TableDataInfo<AiCorrectionRecordVo> queryPageList(AiCorrectionRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiCorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        Page<AiCorrectionRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai批改记录列表
     */
    @Override
    public List<AiCorrectionRecordVo> queryList(AiCorrectionRecordBo bo) {
        LambdaQueryWrapper<AiCorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiCorrectionRecord> buildLambdaQueryWrapper(AiCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiCorrectionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCourseId() != null, AiCorrectionRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudentId() != null, AiCorrectionRecord::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionType()), AiCorrectionRecord::getCorrectionType, bo.getCorrectionType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionPersonType()), AiCorrectionRecord::getCorrectionPersonType, bo.getCorrectionPersonType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionScreenshots()), AiCorrectionRecord::getCorrectionScreenshots, bo.getCorrectionScreenshots());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), AiCorrectionRecord::getStudentId, bo.getStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getCorrectionTypeList()), AiCorrectionRecord::getCorrectionType, bo.getCorrectionTypeList());
        return lqw;
    }

    private QueryWrapper<AiCorrectionRecord> buildQueryWrapper(AiCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<AiCorrectionRecord> lqw = Wrappers.query();
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), "t.course_id", bo.getCourseIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionType()), "t.correction_type", bo.getCorrectionType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionPersonType()), "t.correction_person_type", bo.getCorrectionPersonType());
        lqw.eq(StringUtils.isNotBlank(bo.getCorrectionScreenshots()), "t.correction_screenshots", bo.getCorrectionScreenshots());
        lqw.between(bo.getAiCorrectionRecordCreateTimeStart() != null && bo.getAiCorrectionRecordCreateTimeEnd() != null, "t.create_time", bo.getAiCorrectionRecordCreateTimeStart(), bo.getAiCorrectionRecordCreateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getCorrectionTypeList()), "t.correction_type", bo.getCorrectionTypeList());

        return lqw;
    }


    /**
     * 新增ai批改记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal insertByBo(AiCorrectionRecordBo bo) {
        CourseVo courseVo = courseService.queryById(bo.getCourseId());
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //查下有没有已经存在的批改记录，存在就提示
        LambdaQueryWrapper<AiCorrectionRecord> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(AiCorrectionRecord::getCourseId, bo.getCourseId());
        wrapper.eq(AiCorrectionRecord::getStudentId, bo.getStudentId());
        wrapper.eq(AiCorrectionRecord::getCorrectionType, bo.getCorrectionType());
        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("请勿重复提交");
        }

        List<AiCorrectionRecordInfoBo> correctionRecordInfoBoList = bo.getAiCorrectionRecordInfoBoList();
        if (CollUtil.isEmpty(correctionRecordInfoBoList)) {
            throw new ServiceException("批改记录题目结果不能为空");
        }
        if (bo.getStudentId() == null) {
            throw new ServiceException("会员ID不能为空");
        }

        if (!UserConstants.CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType())&&!UserConstants.CORRECTION_TYPE_TEST.equals(bo.getCorrectionType())){
            throw new ServiceException("批改类型错误");
        }

        AiCorrectionRecord add = MapstructUtils.convert(bo, AiCorrectionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("新增Ai学习批改记录失败");
        }
        bo.setAiCorrectionRecordId(add.getAiCorrectionRecordId());

        for (AiCorrectionRecordInfoBo aiCorrectionRecordInfoBo : correctionRecordInfoBoList) {
            aiCorrectionRecordInfoBo.setAiCorrectionRecordId(add.getAiCorrectionRecordId());
        }
        Boolean b = aiCorrectionRecordInfoService.insertBatchByBo(correctionRecordInfoBoList);
        if (!b) {
            throw new ServiceException("新增批改记录详情失败");
        }

        //同步新增练习或者考试记录
        if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType())) {
            List<AiPracticeRecordBo> convert = MapstructUtils.convert(correctionRecordInfoBoList, AiPracticeRecordBo.class);
            convert.forEach(item -> {
                item.setCourseId(bo.getCourseId());
                item.setStudentId(bo.getStudentId());
            });
            Boolean b2 = aiPracticeRecordService.insertBatchByBo(convert);
            if (!b2) {
                throw new ServiceException("新增练习记录失败");
            }
        } else {
            List<AiTestRecordBo> convert = MapstructUtils.convert(correctionRecordInfoBoList, AiTestRecordBo.class);
            convert.forEach(item -> {
                item.setCourseId(bo.getCourseId());
                item.setStudentId(bo.getStudentId());
            });
            Boolean b2 = aiTestRecordService.insertBatchByBo(convert);
            if (!b2) {
                throw new ServiceException("新增测试记录失败");
            }
        }

        //错题记录增加
        //过滤出错的题目
        List<AiCorrectionRecordInfoBo> wrongQuestionList = correctionRecordInfoBoList.stream().filter(item -> AnswerResultTypeEnum.ALL_WRONG.getType().equals(item.getAnswerResult()) || AnswerResultTypeEnum.HALF_WRONG.getType().equals(item.getAnswerResult())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(wrongQuestionList)) {
            List<AiWrongQuestionRecordBo> wrongList = MapstructUtils.convert(wrongQuestionList, AiWrongQuestionRecordBo.class);
            wrongList.forEach(item->{
                item.setCourseId(bo.getCourseId());
                item.setStudentId(bo.getStudentId());
                item.setSourceType(bo.getCorrectionType());
            });
            Boolean b3 = aiWrongQuestionRecordService.insertBatchByBo(wrongList);
            if (!b3) {
                throw new ServiceException("新增错题记录失败");
            }
        }

        //过滤出答题正确的
        List<AiCorrectionRecordInfoBo> rightQuestionList = correctionRecordInfoBoList.stream().filter(item -> AnswerResultTypeEnum.ALL_CORRECT.getType().equals(item.getAnswerResult())).collect(Collectors.toList());

        //查询Ai学习记录，并更新做题相关的数据
        AiStudyRecordBo aiStudyRecordBo = new AiStudyRecordBo();
        aiStudyRecordBo.setCourseId(bo.getCourseId());
        aiStudyRecordBo.setStudentId(bo.getStudentId());
        AiStudyRecordVo aiStudyRecordVo = aiStudyRecordService.queryOnce(aiStudyRecordBo);
        boolean exist = aiStudyRecordVo != null;

        AiStudyRecordBo updateBean = new AiStudyRecordBo();
        if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(bo.getCorrectionType())) {
            updateBean.setPracticeState(CorrectionStatusEnum.CORRECTED.getCode());
            updateBean.setPracticeRightNum((long) rightQuestionList.size());
            updateBean.setPracticeWrongNum((long) wrongQuestionList.size());
            updateBean.setPracticeUnansweredNum((long) (correctionRecordInfoBoList.size() - rightQuestionList.size() - wrongQuestionList.size()));
        } else if (UserConstants.CORRECTION_TYPE_TEST.equals(bo.getCorrectionType())) {
            updateBean.setTestState(CorrectionStatusEnum.CORRECTED.getCode());
            updateBean.setTestRightNum((long) rightQuestionList.size());
            updateBean.setTestWrongNum((long) wrongQuestionList.size());
            updateBean.setTestUnansweredNum((long) (correctionRecordInfoBoList.size() - rightQuestionList.size() - wrongQuestionList.size()));
        }
        if (exist){
            updateBean.setAiStudyRecordId(aiStudyRecordVo.getAiStudyRecordId());
            Boolean b1 = aiStudyRecordService.updateByBo(updateBean);
            if (!b1) {
                throw new ServiceException("更新AI学习记录失败");
            }
        }else {
            updateBean.setStudentId(bo.getStudentId());
            updateBean.setCourseId(bo.getCourseId());
            Boolean b1 = aiStudyRecordService.insertByBo(updateBean);
            if (!b1) {
                throw new ServiceException("新增AI学习记录失败");
            }
        }
        return new BigDecimal(rightQuestionList.size()).divide(new BigDecimal(correctionRecordInfoBoList.size()), 4, RoundingMode.CEILING);
    }

    /**
     * 修改ai批改记录
     */
    @Override
    public Boolean updateByBo(AiCorrectionRecordBo bo) {
        AiCorrectionRecord update = MapstructUtils.convert(bo, AiCorrectionRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiCorrectionRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ai批改记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        List<AiCorrectionRecord> list = baseMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("未找到要删除的记录");
        }
        // 根据课程Id和学生ID匹配
        List<Long> courseIdList = list.stream().map(AiCorrectionRecord::getCourseId).collect(Collectors.toList());
        List<Long> studentIdList = list.stream().map(AiCorrectionRecord::getStudentId).collect(Collectors.toList());
        AiStudyRecordBo aiStudyRecordBo = new AiStudyRecordBo();
        aiStudyRecordBo.setCourseIdList(courseIdList);
        aiStudyRecordBo.setStudentIdList(studentIdList);

        List<AiStudyRecordVo> aiStudyRecordVos = aiStudyRecordService.queryList(aiStudyRecordBo);
        if (CollUtil.isNotEmpty(aiStudyRecordVos)){
            List<AiStudyRecordVo> updateList = Lists.newArrayList();
            for (AiStudyRecordVo aiStudyRecordVo : aiStudyRecordVos) {
                AiStudyRecordVo temp = new AiStudyRecordVo();
                temp.setAiStudyRecordId(aiStudyRecordVo.getAiStudyRecordId());

                Long studyPlanningRecordId = aiStudyRecordVo.getCourseId();
                if (Objects.isNull(studyPlanningRecordId)){
                    continue;
                }
                // 清空学习记录的参数
                list.forEach(item -> {
                    if (UserConstants.CORRECTION_TYPE_TEST.equals(item.getCorrectionType())) {
                        temp.cleanTestState();
                    } else if (UserConstants.CORRECTION_TYPE_PRACTICE.equals(item.getCorrectionType())){
                        temp.cleanPracticeState();
                    }
                });
                updateList.add(temp);
            }

            List<AiStudyRecord> updated = updateList.stream().map(item -> MapstructUtils.convert(item, AiStudyRecord.class))
                .filter(Objects::nonNull)
                .toList();
            aiStudyRecordService.updateBatchById(updated);
        }
        //获取批改记录详情ID
        List<Long> aiCorrectionRecordId = list.stream().map(AiCorrectionRecord::getAiCorrectionRecordId).toList();
        AiCorrectionRecordInfoBo correctionRecordInfoBo = new AiCorrectionRecordInfoBo();
        correctionRecordInfoBo.setAiCorrectionRecordIds(aiCorrectionRecordId);

        List<AiCorrectionRecordInfoVo> correctionRecordInfoVos = aiCorrectionRecordInfoService.queryList(correctionRecordInfoBo);
        if (CollUtil.isNotEmpty(correctionRecordInfoVos)) {
            aiCorrectionRecordInfoService.deleteWithValidByIds(correctionRecordInfoVos
                .stream().map(AiCorrectionRecordInfoVo::getAiCorrectionRecordInfoId).toList(), false);
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public AiCorrectionQuestionRecordVo queryQuestionRecord(Long courseId, Long studentId, String correctionType) {
        LambdaQueryWrapper<AiCorrectionRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AiCorrectionRecord::getCourseId, courseId);
        lambdaQuery.eq(AiCorrectionRecord::getStudentId, studentId);
        lambdaQuery.eq(AiCorrectionRecord::getCorrectionType, correctionType);
        AiCorrectionRecordVo aiCorrectionRecordVo = baseMapper.selectVoOne(lambdaQuery);
        if (aiCorrectionRecordVo == null) {
            throw new ServiceException("未找到AI批改记录");
        }
        AiCorrectionQuestionRecordVo aiResult = new AiCorrectionQuestionRecordVo();

        //批改记录详情
        AiCorrectionRecordInfoBo aiCorrectionRecordInfoBo = new AiCorrectionRecordInfoBo();
        aiCorrectionRecordInfoBo.setAiCorrectionRecordId(aiCorrectionRecordVo.getAiCorrectionRecordId());
        List<AiCorrectionRecordInfoVo> correctionRecordInfoVoList = aiCorrectionRecordInfoService.queryList(aiCorrectionRecordInfoBo);
        if (CollUtil.isEmpty(correctionRecordInfoVoList)) {
            throw new ServiceException("未找到AI批改记录详情");
        }
        //按照题目序号排序，从小到大
        correctionRecordInfoVoList.sort(Comparator.comparing(AiCorrectionRecordInfoVo::takeQuestionNo));
        //计算正确率
        long count = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.ALL_CORRECT.getType().equals(item.getAnswerResult())).count();
        aiResult.setRightCount(count);
        // 计算错误数量
        long wrongCount = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.ALL_WRONG.getType().equals(item.getAnswerResult())).count();
        // 计算半对错数量
        long rightWrongCount = correctionRecordInfoVoList.stream().filter(item -> AnswerResultTypeEnum.HALF_WRONG.getType().equals(item.getAnswerResult())).count();

        //获取拍照截图里对应的url列表
        String correctionScreenshots = aiCorrectionRecordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)){
            String urls = ossService.selectUrlByIds(correctionScreenshots);
            if (StringUtils.isNotBlank(urls)){
                aiResult.setCorrectionScreenshotUrlList(List.of(urls.split(",")));
            }
        }
        aiResult.setRightCount(count);
        aiResult.setWrongCount(wrongCount);
        aiResult.setRightWrongCount(rightWrongCount);
        aiResult.setAccuracy(new BigDecimal(count).divide(new BigDecimal(correctionRecordInfoVoList.size()), 4, RoundingMode.CEILING));
        aiResult.setCorrectionRecordInfoVoList(correctionRecordInfoVoList);
        return aiResult;
    }

    @Override
    public List<AiCorrectionRecordVo> queryRecordAndRightWrongInfo(AiCorrectionRecordBo aiCorrectionRecordBo) {
        QueryWrapper<AiCorrectionRecord> correctionRecordQueryWrapper = buildQueryWrapper(aiCorrectionRecordBo);
        return baseMapper.queryRecordAndRightWrongInfo(correctionRecordQueryWrapper);
    }

    @Override
    public Boolean submitWithoutCorrection(AiCorrectionRecordBo bo) {
        CourseVo courseVo = courseService.queryById(bo.getCourseId());
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //查下有没有已经存在的批改记录，存在就提示
        LambdaQueryWrapper<AiCorrectionRecord> wrapper = Wrappers.lambdaQuery();

        wrapper.eq(AiCorrectionRecord::getCourseId, bo.getCourseId());
        wrapper.eq(AiCorrectionRecord::getStudentId, bo.getStudentId());
        wrapper.eq(AiCorrectionRecord::getCorrectionType, bo.getCorrectionType());
        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("请勿重复提交");
        }

        if (bo.getStudentId() == null) {
            throw new ServiceException("会员ID不能为空");
        }

        if (!UserConstants.CORRECTION_TYPE_PREVIEW.equals(bo.getCorrectionType())
            &&!CORRECTION_TYPE_SPEAK.equals(bo.getCorrectionType())){
            throw new ServiceException("批改类型错误");
        }

        AiCorrectionRecord add = MapstructUtils.convert(bo, AiCorrectionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("新增Ai学习批改记录失败");
        }
        return true;
    }

    @Override
    public AiCorrectionRecordVo queryRecord(Long courseId, Long studentId, String correctionType) {
        LambdaQueryWrapper<AiCorrectionRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AiCorrectionRecord::getCourseId, courseId);
        lambdaQuery.eq(AiCorrectionRecord::getStudentId, studentId);
        lambdaQuery.eq(AiCorrectionRecord::getCorrectionType, correctionType);
        AiCorrectionRecordVo aiCorrectionRecordVo = baseMapper.selectVoOne(lambdaQuery);
        if (aiCorrectionRecordVo == null) {
            return null;
        }

        //获取拍照截图里对应的url列表
        String correctionScreenshots = aiCorrectionRecordVo.getCorrectionScreenshots();
        if (StringUtils.isNotBlank(correctionScreenshots)){
            String urls;
            if (CORRECTION_TYPE_SPEAK.equals(correctionType)){
                //如果是视频 则返回1天
                urls = ossService.selectUrlByIds(correctionScreenshots,86400);
            }else {
                urls = ossService.selectUrlByIds(correctionScreenshots);
            }
            if (StringUtils.isNotBlank(urls)){
                aiCorrectionRecordVo.setCorrectionScreenshotsUrl(List.of(urls.split(",")));
            }
        }
        return aiCorrectionRecordVo;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
