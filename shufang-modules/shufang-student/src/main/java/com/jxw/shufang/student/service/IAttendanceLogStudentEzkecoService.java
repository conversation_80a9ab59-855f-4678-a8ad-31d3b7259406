package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStudentEzkecoVo;
import com.jxw.shufang.student.domain.vo.StudentAttendanceStatisticsVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ezkeco学员考勤记录Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IAttendanceLogStudentEzkecoService {

    /**
     * 查询ezkeco学员考勤记录
     */
    AttendanceLogStudentEzkecoVo queryById(Long attendanceLogStudentEzkecoId);

    /**
     * 查询ezkeco学员考勤记录列表
     */
    TableDataInfo<AttendanceLogStudentEzkecoVo> queryPageList(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery);

    TableDataInfo<AttendanceLogStudentEzkecoVo> queryPageRecord(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery);

    /**
     * 查询ezkeco学员考勤记录列表
     */
    List<AttendanceLogStudentEzkecoVo> queryList(AttendanceLogStudentEzkecoBo bo);

    /**
     * 新增ezkeco学员考勤记录
     */
    Boolean insertByBo(AttendanceLogStudentEzkecoBo bo);

    /**
     * 修改ezkeco学员考勤记录
     */
    Boolean updateByBo(AttendanceLogStudentEzkecoBo bo);

    /**
     * 校验并批量删除ezkeco学员考勤记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入
     *
     * @param list
     */
    void insertBatch(Collection<AttendanceLogStudentEzkecoBo> list);

    Boolean exist(AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo);

    StudentAttendanceStatisticsVo statistics(Long studentId);

    List<Long> getExistAttendanceLogStuIdList(List<Long> studentIdList, String attendanceDate);

    /**
     * 获取会员每周
     *
     * @param studentIds
     * @param rangeStartTime
     * @param rangeEndTime
     * @return
     */
    Map<Long, Integer> getStudentAttendanceDayNumsMap(List<Long> studentIds, Date rangeStartTime, Date rangeEndTime);
}
