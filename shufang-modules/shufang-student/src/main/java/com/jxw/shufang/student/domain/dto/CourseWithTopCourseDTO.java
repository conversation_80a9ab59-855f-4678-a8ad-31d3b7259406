package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/15 15:41
 * @Version 1
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CourseWithTopCourseDTO {

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 顶级课程ID
     */
    private Long topCourseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 顶级课程名称
     */
    private String topCourseName;

    /**
     * 学段
     */
    private String courseStage;

    /**
     * 年级
     */
    private String courseGrade;

    /**
     * 归属学科
     */
    private String subject;

    /**
     * 课程专题
     */
    private String courseSpecialTopic;

    /**
     * 季度类型
     */
    private String courseQuarterType;

    /**
     * 属性值
     */
    private List<String> value;

    /**
     * 课程知识点ID
     */
    private Long knowledgeId;


    /**
     * @return 学习内容
     */
    public String getStudyContent() {
        return this.getCourseName().concat(
            Optional.ofNullable(this.getTopCourseName()).orElse(""));
    }

    /**
     * @return 内容详情
     */
    public String getContentDetail() {
        List<String> detail = new ArrayList<>();
        detail.add(this.getCourseStage());
        detail.add(this.getCourseGrade());
        detail.add(this.getSubject());
        detail.add(this.getCourseSpecialTopic());
        detail.add(this.getCourseQuarterType());
        Optional.ofNullable(this.getValue()).ifPresent(detail::addAll);
        return detail.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("|"));
    }
}
