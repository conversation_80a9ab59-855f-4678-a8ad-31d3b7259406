package com.jxw.shufang.student.controller.wechat.miniprogram;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudyPlanningRecordService;
import com.jxw.shufang.student.service.IStudyRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学习规划记录---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/studyPlanningRecord
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/studyPlanningRecord")
public class MpStudyPlanningRecordController extends BaseController {

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IStudyRecordService studyRecordService;

    private final IStudentConsultantRecordService studentConsultantRecordService;


    private void putStaffResponsibleStudentIdList(StudyPlanningRecordBo bo) {
        List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
        bo.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList)?List.of(-1L):staffResponsibleStudentIdList);
    }


    /**
     * 查询学习规划记录课程详情
     */
    @GetMapping("/query/{studyPlanningRecordId}")
    public R<StudyPlanningRecordVo> queryFullInfoById(@PathVariable @NotNull(message = "学习规划记录id不能为空") Long studyPlanningRecordId) {
        return R.ok(studyPlanningRecordService.queryFullInfoById(studyPlanningRecordId));
    }


    /**
     * 查询批改状态
     * @param studyPlanningRecordId 学习规划记录id
     * @param resourceType 批改类型  PRACTICE=练习，TEST=考试
     * @return 批改状态 1=未批改,2=批改中,3=已批改
     */
    @GetMapping("/queryCorrectionStatus")
    public R<Integer> queryCorrectionStatus(@NotNull(message = "studyPlanningRecordId不能为空") Long studyPlanningRecordId,
                                            @NotNull(message = "correctionType不能为空") KnowledgeResourceType resourceType) {
        StudyRecordVo studyRecordVo = studyRecordService.queryByStudyPlanningRecordId(studyPlanningRecordId);
        if (studyRecordVo == null) {
            return R.ok(1);
        }
        if (KnowledgeResourceType.PRACTICE.equals(resourceType)){
            return StringUtils.isNotBlank(studyRecordVo.getPracticeState())? R.ok(Integer.parseInt(studyRecordVo.getPracticeState())) : R.ok(1);
        }
        if (KnowledgeResourceType.TEST.equals(resourceType)){
            return StringUtils.isNotBlank(studyRecordVo.getTestState())? R.ok(Integer.parseInt(studyRecordVo.getTestState())) : R.ok(1);
        }
        return R.fail("未知的批改类型");
    }

    /**
     * 批改记录
     */
    @GetMapping("/pageList")
    public TableDataInfo<StudyPlanningRecordVo> pageList(StudyPlanningRecordBo studyPlanningRecordBo, PageQuery pageQuery) {
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return studyPlanningRecordService.queryStudyPlanningRecordPage(studyPlanningRecordBo, pageQuery);
    }

}
