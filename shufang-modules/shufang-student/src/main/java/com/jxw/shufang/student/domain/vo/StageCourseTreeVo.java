package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.lang.tree.Tree;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 课程学年，包含课程树
 *
 *
 * @date 2024-02-29
 */
@Data
public class StageCourseTreeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学段(对应字典course_stage)
     */
    private String stage;

    /**
     * 课程列表
     */
    private List<Tree<Long>> courseTreeList;
}

