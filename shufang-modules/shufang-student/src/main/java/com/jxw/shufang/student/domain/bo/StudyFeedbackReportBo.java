package com.jxw.shufang.student.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyFeedbackReport;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 学习反馈报告业务对象 study_feedback_report
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyFeedbackReport.class, reverseConvertGenerate = false)
public class StudyFeedbackReportBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 学生ID
     */
    @NotNull(message = "学生ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 顾问ID
     */
    private Long consultantId;

    /**
     * 报告周期开始日期
     */
    @NotNull(message = "报告周期开始日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date periodStart;

    /**
     * 报告周期结束日期
     */
    @NotNull(message = "报告周期结束日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date periodEnd;

    /**
     * 总结内容
     */
    @NotBlank(message = "请输入会员这段时间的表现吧", groups = { AddGroup.class, EditGroup.class })
    private String summary;

    /**
     * 存在问题
     */
    @NotBlank(message = "请输入会员存在的问题", groups = { AddGroup.class, EditGroup.class })
    private String issues;

    /**
     * 需关注重点
     */
    @NotBlank(message = "请输入会员需要关注的问题", groups = { AddGroup.class, EditGroup.class })
    private String focusPoints;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 反馈状态:0-待反馈,1-已反馈
     */
    private Integer feedbackStatus;

    /**
     * 删除标记:0-未删除,1-已删除
     */
    private Integer isDeleted;

    /**
     * 关联的学习规划记录ID列表
     */
    private List<Long> pendingIds;
}
