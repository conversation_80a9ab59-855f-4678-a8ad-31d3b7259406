package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PaperBo;
import com.jxw.shufang.student.domain.bo.PrintRecordBo;
import com.jxw.shufang.student.domain.vo.PaperTypeGroupVo;
import com.jxw.shufang.student.domain.vo.PaperVo;
import com.jxw.shufang.student.service.IPaperCollectionService;
import com.jxw.shufang.student.service.IPaperService;
import com.jxw.shufang.student.service.IPrintRecordService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 试卷--平板端
 * 前端访问路由地址为:/student/android/paper
 *
 *
 * @date 2024-05-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paper")
public class APaperController extends BaseController {

    private final IPaperService paperService;

    private final IPrintRecordService printRecordService;

    private final IPaperCollectionService paperCollectionService;

    private final RocketMQTemplate rocketMQTemplate;


    /**
     * 获取试卷分类及其试卷份数
     * @param paperBo
     */
    @GetMapping("/queryPaperTypeAndCount")
    public R<List<PaperTypeGroupVo>> queryPaperTypeAndCount(PaperBo paperBo) {
        paperBo.setBranchId(LoginHelper.getBranchId());
        paperBo.setContainManagement(Boolean.TRUE) ;
        List<PaperTypeGroupVo> resList = paperService.queryPaperTypeAndCount(paperBo);
        return R.ok(resList);
    }


    /**
     * 获取总试卷份数
     */
    @GetMapping("/queryTotalPaperCount")
    public R<Long> queryTotalPaperCount(PaperBo paperBo) {
        paperBo.setBranchId(LoginHelper.getBranchId());
        paperBo.setContainManagement(Boolean.TRUE) ;
        return R.ok(paperService.queryTotalPaperCount(paperBo));
    }


    /**
     * 获取试卷列表
     *
     * @param paperBo
     * @param pageQuery
     * @return
     */
    @GetMapping("/queryPaperList")
    public TableDataInfo<PaperVo> queryPaperList(PaperBo paperBo, PageQuery pageQuery) {
        pageQuery.setOrderByColumn("create_time");
        pageQuery.setIsAsc("desc");
        paperBo.setBranchId(LoginHelper.getBranchId());
        paperBo.setContainManagement(Boolean.TRUE) ;
        TableDataInfo<PaperVo> paperVoTableDataInfo = paperService.queryPageList(paperBo, pageQuery);
        paperCollectionService.putPaperCollection(paperVoTableDataInfo.getRows(), LoginHelper.getStudentId());
        return paperVoTableDataInfo;
    }


    /**
     * 获取试卷详情
     *
     * @param paperId 试卷id
     */
    @GetMapping("/queryPaperInfo")
    public R<PaperVo> queryPaperInfo(Long paperId) {
        PaperVo paperVo = paperService.queryById(paperId);
        paperCollectionService.putPaperCollection(List.of(paperVo), LoginHelper.getStudentId());
        return R.ok(paperVo);
    }

    /**
     * 打印试卷,1代表原卷，2代表解析卷，3代表原卷带解析卷
     */
    @RepeatSubmit
    @PostMapping("/printPaper")
    public R<Void> printPaper(@NotNull(message = "试卷id不能为空") Long paperId,@NotBlank(message = "打印类型不能为空") String type) {
        Long studentId = LoginHelper.getStudentId();
        PrintRecordBo printRecordBo = new PrintRecordBo();
        printRecordBo.setPaperId(paperId);
        printRecordBo.setPaperType(type);
        printRecordBo.setStudentId(studentId);
        if (UserConstants.PRINT_PAPER_TYPE_ORIGINAL_PARSE.equals(type)){
            printRecordBo.setPrintType("2");
        }else {
            printRecordBo.setPrintType("1");
        }
        Boolean b = printRecordService.insertByBo(printRecordBo);
        if (b) {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(NotifyMessageConstant.CONTENT, NoticeBizTypeEnum.DAYINSQ.getDesc());

            RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder()
                .templateCode(NoticeBizTypeEnum.DAYINSQ.name())
                .bizType(NoticeBizTypeEnum.DAYINSQ.getCode())
                .noticeType(NoticeTypeEnum.INTERNAL.getCode())
                .content("向您申请进行打印申请审核操作")
                .studentId(studentId)
                .paramMap(paramMap)
                .build();
            rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);
            return R.ok();
        }
        return R.fail("打印失败");
    }

}
