package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.domain.bo.WrongQuestionReviseBo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionInfoVo;
import com.jxw.shufang.student.domain.vo.WrongQuestionCollectionVo;

import java.util.Collection;
import java.util.List;

/**
 * 错题合集Service接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IWrongQuestionCollectionService {

    /**
     * 查询错题合集
     */
    WrongQuestionCollectionVo queryById(Long wrongQuestionCollectionId);

    /**
     * 查询错题合集列表
     */
    TableDataInfo<WrongQuestionCollectionVo> queryWrongQuestionCollection(WrongQuestionCollectionBo bo, PageQuery pageQuery);

    /**
     * 查询错题合集列表
     */
    List<WrongQuestionCollectionVo> queryList(WrongQuestionCollectionBo bo);

    /**
     * 新增错题合集
     */
    Boolean addCollection(WrongQuestionCollectionBo bo);

    Boolean wrongQuestionRevise(WrongQuestionReviseBo bo);

    WrongQuestionCollectionInfoVo queryCollectionInfo(Long wrongQuestionCollectionId);

    /**
     * 修改错题合集
     */
    Boolean updateByBo(WrongQuestionCollectionBo bo);

    /**
     * 校验并批量删除错题合集信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据学生ID查询错题合集列表
     */
    List<WrongQuestionCollectionVo> queryByStudentId(Long studentId);

    /**
     * 根据学生ID和类型查询错题合集列表
     */
    List<WrongQuestionCollectionVo> queryByStudentIdAndType(Long studentId, Integer collectionType);

    /**
     * 更新错题合集状态
     */
    Boolean updateCollectionStatus(Long wrongQuestionCollectionId, Integer collectionStatus);

    /**
     * 统计学生错题合集数量
     */
    int countByStudentId(Long studentId);

    /**
     * 统计学生指定状态的错题合集数量
     */
    int countByStudentIdAndStatus(Long studentId, Integer collectionStatus);

}
