package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PrintRecordBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.service.IPrintRecordService;
import com.jxw.shufang.student.service.IStudyPlanningRecordService;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 打印记录--平板端
 * 前端访问路由地址为:/student/android/printRecord
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/printRecord")
public class APrintRecordController extends BaseController {

    private final IPrintRecordService printRecordService;

    private final IStudyPlanningRecordService studyPlanningRecordService;
    private final RocketMQTemplate rocketMQTemplate;

    @DubboReference
    private RemoteConfigService remoteConfigService;

    /**
     * 获取打印记录过期时间--平板端
     */
    @GetMapping("/getPrintRecordExpireTime")
    public R<Integer> getPrintRecordExpireTime() {
        return R.ok(remoteConfigService.selectPrintRecordExpireTime());
    }

    /**
     * 新增打印记录--平板端
     */
    @Log(title = "打印记录-平板端", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PrintRecordBo bo) {
        Long studentId = LoginHelper.getStudentId();
        if (bo.getStudyPlanRecordId() == null&&bo.getCourseId() == null) {
            R.fail("studyPlanRecordId和courseId不能同时为空");
        }

        //FIXME 平板端会传0过来，过滤一下
        if (Objects.equals(0L,bo.getStudyPlanRecordId())){
            bo.setStudyPlanRecordId(null);
        }

        //ai学习的打印
        if (bo.getCourseId()!=null&&bo.getCourseId()>0L){
            bo.setStudentId(LoginHelper.getStudentId());
            Boolean b = printRecordService.insertByBo(bo);
            if (Boolean.TRUE.equals(b)) {
                sendNotice(studentId);
            }
            return toAjax(b);
        }

        //正常学习规划的打印
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(bo.getStudyPlanRecordId());
        if (studyPlanningRecordVo == null) {
            return R.fail("studyPlanRecordId不存在");
        }
        bo.setCourseId(studyPlanningRecordVo.getCourseId());
        bo.setStudentId(LoginHelper.getStudentId());
        Boolean result = printRecordService.insertByBo(bo);

        if (Boolean.TRUE.equals(result)) {
            sendNotice(studentId);
        }
        return toAjax(result);
    }

    private void sendNotice(Long studentId) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(NotifyMessageConstant.CONTENT, NoticeBizTypeEnum.DAYINSQ.getDesc());

        RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder()
            .templateCode(NoticeBizTypeEnum.DAYINSQ.name())
            .bizType(NoticeBizTypeEnum.DAYINSQ.getCode())
            .noticeType(NoticeTypeEnum.INTERNAL.getCode())
            .content("向您申请进行打印申请审核操作")
            .studentId(studentId)
            .paramMap(paramMap)
            .build();
        rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);
    }

}
