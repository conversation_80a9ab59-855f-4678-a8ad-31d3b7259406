package com.jxw.shufang.student.mapper;

import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;

import java.util.Date;

/**
 * 学习记录Mapper接口
 *
 *
 * @date 2024-05-06
 */
public interface StudyRecordMapper extends BaseMapperPlus<StudyRecord, StudyRecordVo> {


    //FIXME 有问题
    Integer selectMyRank(@Param("bo") StudyRecordBo bo,@Param("studentId") Long studentId);

    Long countStudentStudyTime(Long studentId, Date startDate, Date endDate);
}
