package com.jxw.shufang.student.service.impl;

import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.mapper.StudentMapper;
import com.jxw.shufang.student.service.INewStudentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/15 13:56
 * @Version 1
 * @Description 新 学生api
 */
@Service
public class NewStudentServiceImpl implements INewStudentService, BaseService {
    @Resource
    private StudentMapper studentMapper;

    @Override
    public List<Long> getStudentIdListByBranchIdList(List<Long> branchIdList) {
        return studentMapper.batchSelectStudentIdListByBranchId(branchIdList);
    }
}
