package com.jxw.shufang.student.domain.vo;

import cn.hutool.core.annotation.Alias;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;


/**
 * EZKEco考勤记录视图对象 ezk_eco_transaction
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Data
@ExcelIgnoreUnannotated
public class EzkEcoTransactionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 流水号
     */
    @ExcelProperty(value = "流水号")
    private Long id;

    /**
     * 验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)
     */
    @ExcelProperty(value = "验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)")
    private String verify;

    /**
     * 打卡时间
     */
    @ExcelProperty(value = "打卡时间")
    private String checktime;

    /**
     * 设备序列号
     */
    @ExcelProperty(value = "设备序列号")
    private String sn;

    /**
     * 设备名称
     */
    @ExcelProperty(value = "设备名称")
    private String alias;

    /**
     * 人员编号
     */
    @ExcelProperty(value = "人员编号")
    private String pin;

    /**
     * 考勤状态说明
     */
    @ExcelProperty(value = "考勤状态说明")
    private String state;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long userId;

    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名")
    @Alias("ename")
    private String nickName;

    /**
     * 照片信息（Base64格式）
     */
    @ExcelProperty(value = "照片信息", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "B=ase64格式")
    private String photograph;

}
