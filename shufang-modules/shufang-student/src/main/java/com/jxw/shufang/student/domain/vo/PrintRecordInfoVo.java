package com.jxw.shufang.student.domain.vo;

import lombok.Data;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;

import java.io.Serial;
import java.io.Serializable;

@Data
public class PrintRecordInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long printRecordId;

    /**
     * 打印来源：1:学习规划 2:课程 3:试卷
     */
    private Integer printSource;

    /**
     * 会员信息
     */
    private StudentVo student;

    /**
     * 课程信息
     */
    private CourseVo course;

    /**
     * 资源类型
     */
    private KnowledgeResourceType resourceType;


    /**
     * 知识点资源，仅为ai学习或者学习规划时才会有
     */
    private RemoteKnowledgeResourceVo resource;

    /**
     * 二维码，仅为测试或者练习才会有
     */
    private String qrCodeBase64;

    /**
     * 试卷信息
     */
    private PaperVo paper;

    /**
     *试卷类型， 1代表原卷，2代表解析卷，3代表原卷+解析卷
     */
    private String paperType;
}


