package com.jxw.shufang.student.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudyPlanningService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyPlanningBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyPlanningRecordVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyPlanningVo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;
import com.jxw.shufang.student.service.IStudyPlanningRecordService;
import com.jxw.shufang.student.service.IStudyPlanningService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudyPlanningServiceImpl implements RemoteStudyPlanningService {

    private final IStudyPlanningService studyPlanningService;
    private final IStudyPlanningRecordService studyPlanningRecordService;

    @Override
    public List<RemoteStudyPlanningVo> queryPlanAndRecordList(RemoteStudyPlanningBo remoteStudyPlanningBo,boolean ignoreDataPermission) {
        StudyPlanningBo convert = MapstructUtils.convert(remoteStudyPlanningBo, StudyPlanningBo.class);
        List<StudyPlanningVo> studyPlanningVos = null;
        if (ignoreDataPermission){
            studyPlanningVos = DataPermissionHelper.ignore(() -> studyPlanningService.queryPlanAndRecordList(convert));
        }else {
            studyPlanningVos = studyPlanningService.queryPlanAndRecordList(convert);
        }
        return MapstructUtils.convert(studyPlanningVos, RemoteStudyPlanningVo.class);
    }

    @Override
    public Boolean checkStudyPlanStudyStatusAllComplete(RemoteStudyPlanningBo bo) {
        StudyPlanningBo convert = MapstructUtils.convert(bo, StudyPlanningBo.class);
        Long studentId = bo.getStudentId();
        List<StudyPlanningVo> studyPlanStudyStatus = studyPlanningService.getStudyPlanStudyStatus(convert, studentId);

        if (CollUtil.isEmpty(studyPlanStudyStatus)){
            return false;
        }

        for (StudyPlanningVo planStudyStatus : studyPlanStudyStatus) {
            if (CollUtil.isEmpty(planStudyStatus.getStudyPlanningRecordList())){
                return false;
            }
            List<StudyPlanningRecordVo> studyPlanningRecordList = planStudyStatus.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)){
                return false;
            }
            for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordList) {
                Boolean x = checkComplete(studyPlanningRecordVo);
                if (Boolean.FALSE.equals(x)){
                    return false;
                }
            }

        }

        return true;
    }


    @Override
    public Boolean checkStudyPlanStudyStatusAnyComplete(RemoteStudyPlanningBo bo) {
        StudyPlanningBo convert = MapstructUtils.convert(bo, StudyPlanningBo.class);
        Long studentId = bo.getStudentId();
        List<StudyPlanningVo> studyDateList = studyPlanningService.getStudyPlanStudyStatus(convert, studentId);
        if (CollUtil.isEmpty(studyDateList)){
            return false;
        }
        Integer checkDays = bo.getCheckDays();
        if (ObjectUtils.isEmpty(convert)||checkDays<=0){
            return true;
        }

        int completeDays = 0;
        for (StudyPlanningVo studyDate : studyDateList) {
            if (CollUtil.isEmpty(studyDate.getStudyPlanningRecordList())){
                return false;
            }
            List<StudyPlanningRecordVo> studyPlanningRecordList = studyDate.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)){
                return false;
            }
            if (Boolean.TRUE.equals(checkStudyPlanningIsComplete(studyPlanningRecordList))) {
                completeDays++;
                if (completeDays>=checkDays){
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public Boolean batchUpdateDownloadStatus(List<Long> recordIdList) {
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningRecordIdList(recordIdList);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordVos)) {
            return Boolean.FALSE;
        }
        studyPlanningRecordVos.forEach(i -> i.setDownloadStatus(1));
        return studyPlanningRecordService.updateBatchByVo(studyPlanningRecordVos);
    }

    @Override
    public List<RemoteStudyPlanningRecordVo> queryPlanRecordListByIds(List<Long> recordIdList) {
        if (CollectionUtil.isEmpty(recordIdList)) {
            return Collections.emptyList();
        }
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryStudyPlanningRecordListByIds(recordIdList);
        return studyPlanningRecordVos.stream().map(studyPlanningRecordVo -> {
            RemoteStudyPlanningRecordVo remoteStudyPlanningRecordVo = new RemoteStudyPlanningRecordVo();
            remoteStudyPlanningRecordVo.setStudyPlanningRecordId(studyPlanningRecordVo.getStudyPlanningRecordId());
            remoteStudyPlanningRecordVo.setStudyPlanningId(studyPlanningRecordVo.getStudyPlanningId());
            remoteStudyPlanningRecordVo.setStudentId(studyPlanningRecordVo.getStudentId());
            remoteStudyPlanningRecordVo.setCourseId(studyPlanningRecordVo.getCourseId());
            remoteStudyPlanningRecordVo.setStudyStartTime(studyPlanningRecordVo.getStudyStartTime());
            remoteStudyPlanningRecordVo.setStudyEndTime(studyPlanningRecordVo.getStudyEndTime());
            remoteStudyPlanningRecordVo.setStudyDuration(studyPlanningRecordVo.getStudyDuration());
            remoteStudyPlanningRecordVo.setStudyStatus(studyPlanningRecordVo.getStudyStatus());
            remoteStudyPlanningRecordVo.setStudyRecordStatus(studyPlanningRecordVo.getStudyRecordStatus());
            return remoteStudyPlanningRecordVo;
        }).collect(Collectors.toList());
    }

    private static boolean checkStudyPlanningIsComplete(List<StudyPlanningRecordVo> studyPlanningRecordList) {
        for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordList) {
            boolean checkComplete = checkComplete(studyPlanningRecordVo);
            if (Boolean.TRUE.equals(checkComplete)){
                return true;
            }

        }
        return false;
    }

    private static Boolean checkComplete(StudyPlanningRecordVo studyPlanningRecordVo) {
        CourseVo course = studyPlanningRecordVo.getCourse();
        if (course== null){
            return false;
        }
        // 判断是否完成，只要任一条件不符合，则返回false
        Boolean studyCompleteStatus = course.getStudyCompleteStatus();
        return studyCompleteStatus != null && !Boolean.FALSE.equals(studyCompleteStatus);
    }
}
