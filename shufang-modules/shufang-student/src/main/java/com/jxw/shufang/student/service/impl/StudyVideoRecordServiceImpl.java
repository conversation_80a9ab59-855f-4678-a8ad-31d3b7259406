package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.domain.dto.BatchQueryVideoRecordDTO;
import com.jxw.shufang.student.service.ICourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordStatisticVo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;
import com.jxw.shufang.student.mapper.StudyVideoRecordMapper;
import com.jxw.shufang.student.service.IStudyVideoRecordService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 学习视频记录（视频观看记录）Service业务层处理
 *
 * @date 2024-05-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StudyVideoRecordServiceImpl implements IStudyVideoRecordService, BaseService {

    private final StudyVideoRecordMapper baseMapper;
    private final ICourseService iCourseService;


    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;


    /**
     * 查询学习视频记录（视频观看记录）
     */
    @Override
    public StudyVideoRecordVo queryById(Long studyVideoRecordId) {
        return baseMapper.selectVoById(studyVideoRecordId);
    }

    /**
     * 查询学习视频记录（视频观看记录）列表
     */
    @Override
    public TableDataInfo<StudyVideoRecordVo> queryPageList(StudyVideoRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<StudyVideoRecord> lqw = buildQueryWrapper(bo);
        Page<StudyVideoRecordVo> result = baseMapper.selectStudyVideoRecordPage(pageQuery.build(), lqw);
        putWatchRecordOtherInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    //剩余时长，剩余分片数量
    private void putWatchRecordOtherInfo(List<StudyVideoRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        //取知识点id
        List<Long> list = records.stream().map(StudyVideoRecordVo::getCourse).filter(Objects::nonNull).map(CourseVo::getKnowledgeId).filter(Objects::nonNull).distinct().toList();
        //查询知识点信息
        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(list);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        Map<Long, RemoteVideoVo> videoVoMap = new HashMap<>();
        if (CollUtil.isEmpty(knowledgeVideoList)) {
            return;
        }
        for (RemoteKnowledgeVideoVo knowledgeVideo : knowledgeVideoList) {
            if (CollUtil.isNotEmpty(knowledgeVideo.getVideoList())) {
                //客户接口问题，只拿第一个视频
                RemoteVideoVo remoteVideoVo = knowledgeVideo.getVideoList().get(0);
                videoVoMap.put(knowledgeVideo.getKnowledgeId(), remoteVideoVo);
            }
        }
        if (videoVoMap.isEmpty()) {
            return;
        }
        //计算剩余时长，计算剩余分片数量
        for (StudyVideoRecordVo record : records) {
            RemoteVideoVo remoteVideoVo = videoVoMap.get(record.getCourse().getKnowledgeId());
            if (remoteVideoVo == null) {
                continue;
            }
            //视频时长
            Long duration = remoteVideoVo.getDuration();
            //获取这个分片记录里面最大的结束时间  00:18-00:41(23’’),00:42-00:45(3’’)
            String studyVideoSlices = record.getStudyVideoSlices();
            long maxEndTime = 0L;
            if (StringUtils.isNotBlank(studyVideoSlices)) {
                String[] slices = studyVideoSlices.split(",");
                studyVideoSlices = VideoSlicesUtils.mergeConsecutiveSlices(studyVideoSlices);
                for (String slice : slices) {
                    String endTime = VideoSlicesUtils.getEndTime(slice);
                    long l = DateUtils.timeToSeconds(endTime);
                    if (l > maxEndTime) {
                        maxEndTime = l;
                    }
                }
            }
            //剩余时长
            long remainingDuration = duration - maxEndTime > 0 ? duration - maxEndTime : 0;
            //剩余分片数量（剩余时长除以分片大小，向上取整）
            Integer remainingSlicesNum = new BigDecimal(remainingDuration).divide(new BigDecimal(UserConstants.VIDEO_SPLIT_SIZE), 0, RoundingMode.UP).intValue();
            record.setRemainingDuration(remainingDuration);
            record.setRemainingSlicesNum(remainingSlicesNum);
            // 展示新的时间分片
            record.setStudyVideoSlices(studyVideoSlices);
        }
    }


    /**
     * 查询学习视频记录（视频观看记录）列表
     */
    @Override
    public List<StudyVideoRecordVo> queryList(StudyVideoRecordBo bo) {
        QueryWrapper<StudyVideoRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.queryList(lqw,bo.getNonSelectStudyVideoSlicesField());
    }

    private LambdaQueryWrapper<StudyVideoRecord> buildLambdaQueryWrapper(StudyVideoRecordBo bo) {
        LambdaQueryWrapper<StudyVideoRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudyVideoRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, StudyVideoRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(bo.getVideoId() != null, StudyVideoRecord::getVideoId, bo.getVideoId());
        lqw.eq(bo.getCourseId() != null, StudyVideoRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudyVideoDuration() != null, StudyVideoRecord::getStudyVideoDuration, bo.getStudyVideoDuration());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), StudyVideoRecord::getStudentId, bo.getStudentIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyVideoSlices()), StudyVideoRecord::getStudyVideoSlices, bo.getStudyVideoSlices());
        lqw.between(bo.getVideoRecordCreateDateStart() != null && bo.getVideoRecordCreateDateEnd() != null, StudyVideoRecord::getCreateTime, bo.getVideoRecordCreateDateStart(), bo.getVideoRecordCreateDateEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), StudyVideoRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordIdList());
        lqw.ge(bo.getGeStudyVideoDuration() != null, StudyVideoRecord::getStudyVideoDuration, bo.getGeStudyVideoDuration());


        if (CollUtil.isNotEmpty(bo.getCreateDateList())) {
            StringBuilder sb = new StringBuilder();
            for (String date : bo.getCreateDateList()) {
                sb.append("'").append(date).append("',");
            }
            String createDateStr = sb.toString().substring(0, sb.length() - 1);
            lqw.apply("DATE_FORMAT(create_time,'%Y-%m-%d') in (" + createDateStr + ")");
        }

        if (Boolean.TRUE.equals(bo.getNonSelectStudyVideoSlicesField())) {
            lqw.select(StudyVideoRecord.class, i -> !"studyVideoSlices".equals(i.getProperty()));
        }
        if (StringUtils.isNotBlank(bo.getStudyRecordStatus())) {
            lqw.apply("study_planning_record_id in (select study_planning_record_id from study_planning_record where study_record_status = {0})", bo.getStudyRecordStatus());
        } else {
            lqw.apply("study_planning_record_id in (select study_planning_record_id from study_planning_record where study_record_status = {0})", UserConstants.STUDY_RECORD_STATUS_NORMAL);
        }

        return lqw;
    }

    private QueryWrapper<StudyVideoRecord> buildQueryWrapper(StudyVideoRecordBo bo) {
        QueryWrapper<StudyVideoRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, "t.study_planning_record_id", bo.getStudyPlanningRecordId());
        lqw.eq(bo.getVideoId() != null, "t.video_id", bo.getVideoId());
        lqw.eq(bo.getCourseId() != null, "t.course_id", bo.getCourseId());
        lqw.eq(bo.getStudyVideoDuration() != null, "t.study_video_duration", bo.getStudyVideoDuration());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyVideoSlices()), "t.study_video_slices", bo.getStudyVideoSlices());
        lqw.between(bo.getVideoRecordCreateDateStart() != null && bo.getVideoRecordCreateDateEnd() != null, "t.create_time", bo.getVideoRecordCreateDateStart(), bo.getVideoRecordCreateDateEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudyPlanningRecordIdList()), "t.study_planning_record_id", bo.getStudyPlanningRecordIdList());
        lqw.ge(bo.getGeStudyVideoDuration() != null, "t.study_video_duration", bo.getGeStudyVideoDuration());
        if (null != bo.getStudyModuleType()) {
            lqw.eq("t.study_module_type", bo.getStudyModuleType().getModuleEnum().getModuleCode());
        }
        if (CollUtil.isNotEmpty(bo.getCreateDateList())) {
            StringBuilder sb = new StringBuilder();
            for (String date : bo.getCreateDateList()) {
                sb.append("'").append(date).append("',");
            }
            String createDateStr = sb.toString().substring(0, sb.length() - 1);
            lqw.apply("DATE_FORMAT(create_time,'%Y-%m-%d') in (" + createDateStr + ")");
        }

        if (Boolean.TRUE.equals(bo.getNonSelectStudyVideoSlicesField())) {
            lqw.select(StudyVideoRecord.class, i -> !"studyVideoSlices".equals(i.getProperty()));
        }

        if (StringUtils.isNotBlank(bo.getStudyRecordStatus())) {
            lqw.eq("spr.study_record_status", bo.getStudyRecordStatus());
        } else {
            lqw.eq("spr.study_record_status", UserConstants.STUDY_RECORD_STATUS_NORMAL);
        }


        return lqw;
    }

    /**
     * 新增学习视频记录（视频观看记录）
     */
    @Override
    public Boolean insertByBo(StudyVideoRecordBo bo) {
        StudyVideoRecord add = MapstructUtils.convert(bo, StudyVideoRecord.class);
        if (ObjectUtils.isEmpty(add)){
            return false;
        }
        validEntityBeforeSave(add);
        //增量同步视频总时长 供统计使用
        CourseVo courseVo = iCourseService.queryById(bo.getCourseId());
        if (ObjectUtils.isNotEmpty(courseVo)){
            RemoteKnowledgeVideoVo knowledgeVideoListById = remoteExtVideoService.getKnowledgeVideoListById(courseVo.getKnowledgeId());
            List<RemoteVideoVo> videoList = knowledgeVideoListById.getVideoList();
            if (!CollectionUtils.isEmpty(videoList)){
                RemoteVideoVo remoteVideoVo = videoList.get(0);
                add.setDuration(remoteVideoVo.getDuration());
            }
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudyVideoRecordId(add.getStudyVideoRecordId());
        }
        return flag;
    }

    /**
     * 修改学习视频记录（视频观看记录）
     */
    @Override
    public Boolean updateByBo(StudyVideoRecordBo bo) {
        StudyVideoRecord update = MapstructUtils.convert(bo, StudyVideoRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyVideoRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学习视频记录（视频观看记录）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public StudyVideoRecordVo queryLastDayRecord(Long studentId, Long studyPlanningRecordId) {
        LambdaQueryWrapper<StudyVideoRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(StudyVideoRecord::getStudentId, studentId);
        lambdaQuery.eq(StudyVideoRecord::getStudyPlanningRecordId, studyPlanningRecordId);
        lambdaQuery.orderByDesc(StudyVideoRecord::getCreateTime);
        lambdaQuery.last("limit 1");
        List<StudyVideoRecordVo> studyVideoRecordVos = baseMapper.selectVoList(lambdaQuery);
        if (studyVideoRecordVos.isEmpty()) {
            return null;
        }
        return studyVideoRecordVos.get(0);
    }

    @Override
    public StudyVideoRecordStatisticVo videoRecordStatistic(Long studentId) {
        Date today = new Date();
        //查出所有记录，仅查创建日期，学习时长
        LambdaQueryWrapper<StudyVideoRecord> query = Wrappers.lambdaQuery();
        query.eq(StudyVideoRecord::getStudentId, studentId);
        query.select(StudyVideoRecord::getCreateTime, StudyVideoRecord::getStudyVideoDuration);
        List<StudyVideoRecord> studyVideoRecords = baseMapper.selectList(query);
        //统计学习时长
        long totalDuration = studyVideoRecords.stream().mapToLong(StudyVideoRecord::getStudyVideoDuration).filter(Objects::nonNull).sum();
        //近30天播放总时长
        //先算出今天30天前的时间，并按照00:00:00开始算
        Date thirtyDaysAgo = DateUtil.beginOfDay(DateUtils.addDays(today, -29));
        //统计30天内的播放时长
        long thirtyDaysDuration = studyVideoRecords.stream().filter(studyVideoRecord -> studyVideoRecord.getCreateTime().after(thirtyDaysAgo)).mapToLong(StudyVideoRecord::getStudyVideoDuration).filter(Objects::nonNull).sum();
        //近7天播放总时长
        //先算出今天7天前的时间，并按照00:00:00开始算
        Date sevenDaysAgo = DateUtil.beginOfDay(DateUtils.addDays(today, -6));
        //统计7天内的播放时长
        long sevenDaysDuration = studyVideoRecords.stream().filter(studyVideoRecord -> studyVideoRecord.getCreateTime().after(sevenDaysAgo)).mapToLong(StudyVideoRecord::getStudyVideoDuration).filter(Objects::nonNull).sum();

        StudyVideoRecordStatisticVo studyVideoRecordStatisticVo = new StudyVideoRecordStatisticVo();
        studyVideoRecordStatisticVo.setTotalDuration(totalDuration);
        studyVideoRecordStatisticVo.setThirtyDaysDuration(thirtyDaysDuration);
        studyVideoRecordStatisticVo.setSevenDaysDuration(sevenDaysDuration);
        return studyVideoRecordStatisticVo;
    }

    @Override
    public List<StudyVideoRecordVo> queryLastStudyVideoRecord(List<Long> studyPlanningRecordIdList, boolean showStudyVideoSlices) {
        return baseMapper.queryLastStudyVideoRecord(studyPlanningRecordIdList, showStudyVideoSlices);
    }

    @Override
    public boolean saveBatch(List<StudyVideoRecord> insertList) {
        return baseMapper.insertBatch(insertList);
    }

    @Override
    public boolean updateBatchById(List<StudyVideoRecord> updateList) {
        return baseMapper.updateBatchById(updateList);
    }

    @Override
    public Long queryStudyDaysByStudentId(Long studentId) {
        return baseMapper.queryStudyDaysByStudentId(studentId);
    }

    @Override
    public StudyVideoRecordVo queryOne(StudyVideoRecordBo studyVideoRecordBo) {
        QueryWrapper<StudyVideoRecord> queryWrapper = buildQueryWrapper(studyVideoRecordBo);
        queryWrapper.last("limit 1");
        return baseMapper.selectRecordOne(queryWrapper);
    }

    @Override
    public Long count(StudyVideoRecordBo convert) {
        LambdaQueryWrapper<StudyVideoRecord> queryWrapper = buildLambdaQueryWrapper(convert);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public List<StudyVideoRecord> batchQueryRecord(List<BatchQueryVideoRecordDTO> recordList) {
        if(CollectionUtil.isEmpty(recordList)){
            return Collections.emptyList();
        }
        List<Long> studentIds = recordList.stream().map(BatchQueryVideoRecordDTO::getStudentId).filter(Objects::nonNull).toList();
        List<Long> studyPlanningRecordIds = recordList.stream().map(BatchQueryVideoRecordDTO::getStudyPlanningRecordId).filter(Objects::nonNull).toList();
        List<String> studyModuleTypes = recordList.stream().map(BatchQueryVideoRecordDTO::getStudyModuleType).toList();

        LambdaQueryWrapper<StudyVideoRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StudyVideoRecord::getStudentId, studentIds);
        lambdaQueryWrapper.in(StudyVideoRecord::getStudyPlanningRecordId, studyPlanningRecordIds);
        lambdaQueryWrapper.in(StudyVideoRecord::getStudyModuleType, studyModuleTypes);
        return baseMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public void batchUpdate(List<StudyVideoRecord> updates) {
        if (CollUtil.isEmpty(updates)){
            return;
        }
        updates.forEach(baseMapper::updateById);
    }

    @Override
    public void batchInsert(List<StudyVideoRecord> inserts) {
        if (CollUtil.isEmpty(inserts)){
            return;
        }
        inserts.forEach(baseMapper::insert);
    }

    @Override
    public Long accumulateStudyVideo(StudyVideoRecordBo convert) {
        // 学习视频时长大于值
        Long geStudyVideoDuration = convert.getGeStudyVideoDuration();
        if (Objects.isNull(geStudyVideoDuration)) {
            return count(convert);
        }
        convert.setGeStudyVideoDuration(null);
        LambdaQueryWrapper<StudyVideoRecord> queryWrapper = buildLambdaQueryWrapper(convert);
        long accumulate = baseMapper.selectList(queryWrapper).stream()
            .filter(i -> Objects.nonNull(i.getStudyVideoDuration()))
            .mapToLong(StudyVideoRecord::getStudyVideoDuration)
            .sum();
        return accumulate >= geStudyVideoDuration ? 1L : 0L;
    }


    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
