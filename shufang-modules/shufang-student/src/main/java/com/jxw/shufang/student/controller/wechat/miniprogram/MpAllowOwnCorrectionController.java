package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo;
import com.jxw.shufang.student.service.IAllowOwnCorrectionService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 允许自主批改---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/allowOwnCorrection
 *
 *
 * @date 2024-05-07
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/allowOwnCorrection")
public class MpAllowOwnCorrectionController extends BaseController {

    private final IAllowOwnCorrectionService allowOwnCorrectionService;

    private final IStudentConsultantRecordService studentConsultantRecordService;


    /**
     * 修改允许自主批改
     *
     * @param studentId 学生id
     * @param type      应用类型，1学习规划  2ai学习
     * @param allowType 允许类型，1练习  2测试
     *
     * @date 2024/06/06 04:45:15
     */
    @RepeatSubmit
    @PutMapping("/changeAllowOwnCorrection")
    @Log(title = "修改允许自主批改--小程序端", businessType = BusinessType.UPDATE)
    public R<Void> changeAllowOwnCorrection(@NotNull(message = "会员id不能为空") Long studentId, @NotBlank(message = "type不能为空") String type, @NotBlank(message = "allowType不能为空") String allowType) {
        if (!LoginHelper.isBranchStaff()) {
            return R.fail("无权访问");
        }
        if (!type.equals("1") && !type.equals("2")) {
            return R.fail("type参数错误");
        }
        if (!allowType.equals("1") && !allowType.equals("2")) {
            return R.fail("allowType参数错误");
        }
        return toAjax(allowOwnCorrectionService.changeAllowOwnCorrection(studentId, type, allowType));
    }


    /**
     * 查询允许自主批改列表
     */
    @GetMapping("/list")
    public TableDataInfo<AllowOwnCorrectionVo> list(AllowOwnCorrectionBo bo, PageQuery pageQuery) {
        if (!LoginHelper.isBranchStaff()) {
            throw new ServiceException("无权访问");
        }
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        if ( StringUtils.isBlank(bo.getType())) {
            throw new ServiceException("参数不完整");
        }
        return allowOwnCorrectionService.queryPageList(bo, pageQuery);
    }


}
