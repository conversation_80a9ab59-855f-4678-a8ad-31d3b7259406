package com.jxw.shufang.student.domain.dto;

import com.jxw.shufang.student.domain.bo.NewStudyPlanningRecordBO;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/15 15:49
 * @Version 1
 * @Description
 */
@Data
public class QueryPlanningRecordDTO {
    /**
     * 学习规划日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date studyPlanningDate;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 会员ID
     */
    private List<Long> studentIdList;

    public static QueryPlanningRecordDTO of(NewStudyPlanningRecordBO bo, List<Long> studentIdList) {
        return QueryPlanningRecordDTO.of(bo.getStudyPlanningDate(), bo.getConsultantId(), bo.getStudentName(), studentIdList);
    }

    public static QueryPlanningRecordDTO of(Date studyPlanningDate, Long consultantId, String studentName, List<Long> studentIdList) {
        QueryPlanningRecordDTO queryPlanningRecordDTO = new QueryPlanningRecordDTO();
        queryPlanningRecordDTO.setStudyPlanningDate(studyPlanningDate);
        queryPlanningRecordDTO.setConsultantId(consultantId);
        queryPlanningRecordDTO.setStudentName(studentName);
        queryPlanningRecordDTO.setStudentIdList(studentIdList);
        return queryPlanningRecordDTO;
    }
}
