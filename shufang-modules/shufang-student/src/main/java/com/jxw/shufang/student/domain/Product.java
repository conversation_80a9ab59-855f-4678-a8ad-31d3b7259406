package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 产品（会员卡）对象 product
 *
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product")
public class Product extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    @TableId(value = "product_id")
    private Long productId;

    /**
     * 会员类型id
     */
    private Long studentTypeId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品有效天数
     */
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    private BigDecimal productPrice;

    /**
     * 产品状态（0上架 1下架）
     */
    private String productStatus;


}
