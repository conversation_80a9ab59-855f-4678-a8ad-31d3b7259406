package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * ai学习视频记录（视频观看记录）视图对象 ai_study_video_record
 *
 *
 * @date 2024-05-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiStudyVideoRecord.class)
public class AiStudyVideoRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习视频记录id
     */
    @ExcelProperty(value = "学习视频记录id")
    private Long aiStudyVideoRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 对应外部资源的视频Id
     */
    @ExcelProperty(value = "对应外部资源的视频Id")
    private Long videoId;

    /**
     * 章节ID
     */
    @ExcelProperty(value = "章节ID")
    private Long courseId;

    /**
     * 播放时长（单位秒 按日累加）
     */
    @ExcelProperty(value = "播放时长", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "单=位秒,按=日累加")
    private Long studyVideoDuration;

    /**
     * 视频总时长
     */
    private Long duration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    @ExcelProperty(value = "分片值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=,0=0:18-00:41(23’’),00:42-00:45(3’’),，=非需要展示情况下，尽可能不查此字段")
    private String studyVideoSlices;


    /**
     * 创建时间
     */
    private Date createTime;

    private Date updateTime;


}
