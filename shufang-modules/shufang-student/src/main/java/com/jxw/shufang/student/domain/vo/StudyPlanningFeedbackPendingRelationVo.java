package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.student.domain.StudyPlanningFeedbackPendingRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 学习规划反馈报告关联学习记录视图对象 study_planning_feedback_pending_relation
 *
 * @date 2024-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanningFeedbackPendingRelation.class)
public class StudyPlanningFeedbackPendingRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 反馈报告ID
     */
    @ExcelProperty(value = "反馈报告ID")
    private Long reportId;

    /**
     * 需学习规划记录ID
     */
    @ExcelProperty(value = "需学习规划记录ID")
    private Long pendingId;

    /**
     * 创建人ID
     */
    @ExcelProperty(value = "创建人ID")
    private Long creatorId;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新人ID
     */
    @ExcelProperty(value = "更新人ID")
    private Long updaterId;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updatedTime;
}
