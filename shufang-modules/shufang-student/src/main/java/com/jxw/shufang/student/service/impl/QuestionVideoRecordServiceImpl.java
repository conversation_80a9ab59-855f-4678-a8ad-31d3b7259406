package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.domain.dto.BatchQueryQuestionRecordDTO;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import com.jxw.shufang.student.domain.vo.QuestionVideoRecordVo;
import com.jxw.shufang.student.mapper.QuestionVideoRecordMapper;
import com.jxw.shufang.student.service.IQuestionVideoRecordService;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 题目视频记录（题目视频观看记录）Service业务层处理
 * @date 2024-05-29
 */
@RequiredArgsConstructor
@Service
public class QuestionVideoRecordServiceImpl implements IQuestionVideoRecordService, BaseService {

    private final QuestionVideoRecordMapper baseMapper;

    /**
     * 查询题目视频记录（题目视频观看记录）
     */
    @Override
    public QuestionVideoRecordVo queryById(Long questionVideoRecordId){
        return baseMapper.selectVoById(questionVideoRecordId);
    }

    /**
     * 查询题目视频记录（题目视频观看记录）列表
     */
    @Override
    public TableDataInfo<QuestionVideoRecordVo> queryPageList(QuestionVideoRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionVideoRecord> lqw = buildQueryWrapper(bo);
        Page<QuestionVideoRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询题目视频记录（题目视频观看记录）列表
     */
    @Override
    public List<QuestionVideoRecordVo> queryList(QuestionVideoRecordBo bo) {
        LambdaQueryWrapper<QuestionVideoRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QuestionVideoRecord> buildQueryWrapper(QuestionVideoRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QuestionVideoRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, QuestionVideoRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, QuestionVideoRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(bo.getVideoId() != null, QuestionVideoRecord::getVideoId, bo.getVideoId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionType()), QuestionVideoRecord::getQuestionType, bo.getQuestionType());
        lqw.eq(bo.getCourseId() != null, QuestionVideoRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getQuestionId() != null, QuestionVideoRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getStudyVideoDuration() != null, QuestionVideoRecord::getStudyVideoDuration, bo.getStudyVideoDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyVideoSlices()), QuestionVideoRecord::getStudyVideoSlices, bo.getStudyVideoSlices());
        lqw.in(CollUtil.isNotEmpty(bo.getQuestionTypeList()), QuestionVideoRecord::getQuestionType, bo.getQuestionTypeList());
        lqw.in(CollUtil.isNotEmpty(bo.getQuestionIdList()), QuestionVideoRecord::getQuestionId, bo.getQuestionIdList());
        return lqw;
    }

    /**
     * 新增题目视频记录（题目视频观看记录）
     */
    @Override
    public Boolean insertByBo(QuestionVideoRecordBo bo) {
        QuestionVideoRecord add = MapstructUtils.convert(bo, QuestionVideoRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuestionVideoRecordId(add.getQuestionVideoRecordId());
        }
        return flag;
    }

    /**
     * 修改题目视频记录（题目视频观看记录）
     */
    @Override
    public Boolean updateByBo(QuestionVideoRecordBo bo) {
        QuestionVideoRecord update = MapstructUtils.convert(bo, QuestionVideoRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QuestionVideoRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除题目视频记录（题目视频观看记录）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean saveBatch(List<QuestionVideoRecord> insertList) {
        return baseMapper.insertBatch(insertList);
    }

    @Override
    public Boolean updateBatchById(List<QuestionVideoRecord> updateList) {
        return baseMapper.updateBatchById(updateList);
    }

    @Override
    public List<QuestionVideoRecord> batchQuerySameStudentRecord(List<BatchQueryQuestionRecordDTO> recordList) {
        if(CollectionUtil.isEmpty(recordList)){
            return Collections.emptyList();
        }
        List<Long> studentIds = recordList.stream().map(BatchQueryQuestionRecordDTO::getStudentId).filter(Objects::nonNull).toList();
        List<Long> courseIds = recordList.stream().map(BatchQueryQuestionRecordDTO::getCourseId).filter(Objects::nonNull).toList();
        List<String> questionTypes = recordList.stream().map(BatchQueryQuestionRecordDTO::getQuestionType).filter(Objects::nonNull).toList();
        List<Long> questionIds = recordList.stream().map(BatchQueryQuestionRecordDTO::getQuestionId).filter(Objects::nonNull).toList();
        List<String> moduleTypes = recordList.stream().map(BatchQueryQuestionRecordDTO::getModuleType).filter(Objects::nonNull).toList();
        List<Long> studyPlanningRecordIds = recordList.stream().map(BatchQueryQuestionRecordDTO::getStudyPlanningRecordId)
            .filter(Objects::nonNull).toList();
        List<Long> videoIds = recordList.stream().map(BatchQueryQuestionRecordDTO::getVideoId).filter(Objects::nonNull).toList();
        LambdaQueryWrapper<QuestionVideoRecord> lqw = Wrappers.lambdaQuery();
        lqw.in(CollUtil.isNotEmpty(studentIds), QuestionVideoRecord::getStudentId, studentIds);
        lqw.in(CollUtil.isNotEmpty(courseIds), QuestionVideoRecord::getCourseId, courseIds);
        lqw.in(CollUtil.isNotEmpty(questionTypes), QuestionVideoRecord::getQuestionType, questionTypes);
        lqw.in(CollUtil.isNotEmpty(moduleTypes), QuestionVideoRecord::getModuleGroup, moduleTypes);
        lqw.in(CollectionUtil.isNotEmpty(studyPlanningRecordIds), QuestionVideoRecord::getStudyPlanningRecordId, studyPlanningRecordIds);
        lqw.in(CollUtil.isNotEmpty(questionIds), QuestionVideoRecord::getQuestionId, questionIds);
        lqw.in(CollUtil.isNotEmpty(videoIds), QuestionVideoRecord::getVideoId, videoIds);
        return baseMapper.selectList(lqw);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
