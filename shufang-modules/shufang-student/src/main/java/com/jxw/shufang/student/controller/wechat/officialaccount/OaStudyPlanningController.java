package com.jxw.shufang.student.controller.wechat.officialaccount;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.vo.StudyPlanningVo;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudyPlanningService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 学习规划---公众号端
 * 前端访问路由地址为:/student/officialAccount/studyPlanning
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/officialAccount/studyPlanning")
public class OaStudyPlanningController extends BaseController {

    private final IStudyPlanningService studyPlanningService;

    private final IStudentConsultantRecordService studentConsultantRecordService;


    /**
     * 获取某日某学生的的学习计划和记录列表
     *
     *
     * @date 2024/06/07 02:42:27
     */
    @GetMapping("/queryStudyPlan")
    public R<StudyPlanningVo> queryStudyPlanPage(StudyPlanningBo studyPlanningBo) {
        if (studyPlanningBo.getStudentId()==null){
            return R.fail("请选择会员");
        }

        List<StudyPlanningVo> studyPlanningVos = DataPermissionHelper.ignore(() -> studyPlanningService.queryPlanAndRecordList(studyPlanningBo));
        //某一个会员再某一天至多只能有一个学习计划
        if(CollUtil.isNotEmpty(studyPlanningVos)){
            return R.ok(studyPlanningVos.get(0));
        }
        return R.ok();
    }

    /**
     * 获取学生的学习规划列表
     * 默认当天
     */
    @GetMapping("/queryStudyPlanList")
    public R<TableDataInfo<StudyPlanningVo>> queryStudyPlanList(StudyPlanningBo studyPlanningBo, PageQuery pageQuery) {
        TableDataInfo<StudyPlanningVo> studyPlanningVos = DataPermissionHelper.ignore(() -> studyPlanningService.queryStudyPlanList(studyPlanningBo,pageQuery));
        return R.ok(studyPlanningVos);
    }



}
