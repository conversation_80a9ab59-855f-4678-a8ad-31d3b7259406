package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.student.service.IStudentPaperQuestionService;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户答题试卷记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/paperRecordQuestion")
public class AStudentPaperQuestionController extends BaseController {

    private final IStudentPaperQuestionService service;


    @GetMapping("/queryQuestion")
    public R<List<RemoteVideoVo>> queryById(Long questionId) {
        return R.ok(service.queryById(questionId));
    }

    @GetMapping("/queryKnowledge")
    public R<List<RemoteVideoVo>> queryKnowledge(@RequestParam  List<Long> knowledgeIdList) {
        return R.ok(service.queryKnowledge(knowledgeIdList));
    }


}
