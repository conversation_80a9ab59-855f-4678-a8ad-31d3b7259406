package com.jxw.shufang.student.domain.bo;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class PaperDocBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 试卷名称
     */
    private String docName;

    /**
     * 原卷名称
     */
    private String originalDoc;

    /**
     * 原卷ossId
     */
    private Long originalDocOssId;

    /**
     * 解析卷名称
     */
    private String analysisDoc;

    /**
     * 解析卷ossId
     */
    private Long analysisDocOssId;

    /**
     * 原卷+解析卷 名称
     */
    private String originalWithAnalysisDoc;

    /**
     * 原卷+解析卷 ossId
     */
    private Long originalWithAnalysisDocOssId;

    /**
     * 状态 0 存在规则错误  1 正常
     */
    private String status;

    /**
     * 原因
     */
    private String reason;
}
