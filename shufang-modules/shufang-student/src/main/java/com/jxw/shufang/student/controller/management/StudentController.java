package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.encrypt.annotation.ApiEncrypt;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.common.core.enums.PurchasedCardFlag;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员-管理端
 * 前端访问路由地址为:/student/management/student
 *
 *
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/student")
public class StudentController extends BaseController {

    private final IStudentService studentService;

    private final IStudentTypeService studentTypeService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IAllowOwnCorrectionService allowOwnCorrectionService;

    private final ICourseService courseService;

    private final IStudentAiCourseRecordService studentAiCourseRecordService;

    private final IStudentAiCourseRecordInfoService studentAiCourseRecordInfoService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    @DubboReference
    private final RemoteBranchAuthTypeService remoteBranchAuthTypeService;
    @DubboReference
    private RemoteOrderService remoteOrderService;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    /**
     * 查询会员列表
     */
    @SaCheckPermission("student:student:list")
    @GetMapping("/list")
    public TableDataInfo<StudentVo> list(StudentBo bo, PageQuery pageQuery, HttpServletRequest request) {

        String referer =  request.getHeader("referer");
        if(StringUtils.isNotBlank(referer)){
            if(referer.contains("/service/applyCorrectionRecord")){//只筛选未允许自主批改的数据
                //把允许的查出来
                AllowOwnCorrectionBo allowOwnCorrectionBo = new AllowOwnCorrectionBo();
                allowOwnCorrectionBo.setAllowType(bo.getAllowType());
                allowOwnCorrectionBo.setType(bo.getType());
                List<AllowOwnCorrectionVo> voList = allowOwnCorrectionService.queryList(allowOwnCorrectionBo);
                List<Long> notInStudentIds = voList.stream().map(AllowOwnCorrectionVo::getStudentId).collect(Collectors.toList());
                bo.setNotInStudentIds(notInStudentIds);
            }
            if(referer.contains("/service/workbench")){//只筛选当日没有规划的

                List<Long> studentIdList = new ArrayList<>();

                //剩余规划天数≦3天的未过期用户, 含体验会员
                List<StudentVo> studentVoList = studentService.queryList(new StudentBo());
                if(null != studentVoList && studentVoList.size()>0){

                    LocalDate today = LocalDate.now();
                    studentVoList.parallelStream().forEach(vo->{
                        if(null != vo.getRemainingDay() && vo.getRemainingDay() <= 3 && vo.getRemainingDay()>0){
                            Boolean flag = false;
                            for(int i=0;i<vo.getRemainingDay();i++){
                                LocalDate nextDay = today.plus(i, ChronoUnit.DAYS);
                                StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
                                studyPlanningRecordBo.setStudentId(vo.getStudentId());
                                studyPlanningRecordBo.setStudyPlanningDate(DateUtils.toDate(nextDay));
                                if(studyPlanningRecordService.queryList(studyPlanningRecordBo).size()>0){
                                    flag = true;
                                    break;
                                }
                            }
                            if(!flag) {
                                studentIdList.add(vo.getStudentId());
                            }
                        }
                    });
                }

                if(null != studentIdList && studentIdList.size()>0){
                    bo.setStudentIds(studentIdList);
                }else {
                    bo.setStudentIds(List.of(-1L));
                }

            }
        }

        bo.setWithBranchInfo(Boolean.TRUE);
        bo.setWithSysUserInfo(Boolean.TRUE);
        bo.setWithStudentLastPayOrderProductInfo(Boolean.TRUE);
        bo.setWithConsultantInfo(Boolean.TRUE);
        bo.setWithStudentLastAiCourseRecordInfoSummary(Boolean.TRUE);

        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }

        return studentService.queryPageList(bo, pageQuery);
    }

    /**
     * 查看会员卡类型列表
     */
    @GetMapping("/type/list")
    public R<List<StudentTypeVo>> list(StudentTypeBo bo) {
        return R.ok(studentTypeService.options(bo));
    }


    /**
     * 导出会员列表
     */
    @SaCheckPermission("student:student:export")
    @Log(title = "会员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentBo bo, HttpServletResponse response) {
        List<StudentVo> list = studentService.queryList(bo);
        list.forEach(studentVo -> studentVo
            .setHasPurchasedCardStr(PurchasedCardFlag.getFlagByCode(studentVo.getPurchasedCardFlag()).getDesc()));
        ExcelUtil.exportExcel(list, "会员", StudentVo.class, response);
    }

    /**
     * 获取会员详细信息
     *
     * @param studentBo 会员bo
     */
    @SaCheckPermission("student:student:query")
    @GetMapping("/getInfo")
    public R<StudentVo> getInfo(StudentBo studentBo) {
        if (studentBo.getStudentId() == null) {
            return R.fail("会员id不能为空");
        }
        return R.ok(studentService.queryById(studentBo));
    }

    /**
     * 新增会员
     */
    @SaCheckPermission("student:student:add")
    @Log(title = "会员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentBo bo) {
        if (LoginHelper.isBranchUser()&&LoginHelper.getBranchId() != null){
            bo.setBranchId(LoginHelper.getBranchId());
        }
        if(bo.getBranchId() == null){
            return R.fail("请选择门店");
        }
        if (StringUtils.isNotBlank(bo.getStudentPassword())&&bo.getStudentPassword().length()<6){
            return R.fail("密码长度不能小于6位");
        }
        if(null != bo.getIntroduceStudent()){
            if(null == bo.getIntroduceStudent().getStudentId()){
                return R.fail("请选择介绍人信息");
            }
        }
        studentService.insertByBo(bo);
        return R.ok();
    }

    /**
     * 修改会员
     */
    @SaCheckPermission("student:student:edit")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentBo bo) {
        studentService.updateByBo(bo);
        return R.ok();
    }

    /**
     * 删除会员
     *
     * @param studentIds 主键串
     */
    @SaCheckPermission("student:student:remove")
    @Log(title = "会员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentIds) {
        return toAjax(studentService.deleteWithValidByIds(List.of(studentIds), true));
    }

    /**
     * 选项列表
     *
     * @param bo bo
     *
     * @date 2024/02/29 03:41:49
     */
    @SaCheckPermission("student:student:optionList")
    @GetMapping("/queryOptionList")
    public TableDataInfo<StudentVo> queryOptionList(StudentBo bo, PageQuery pageQuery) {

         return studentService.queryOptionList(bo,pageQuery);
    }

    @GetMapping("/queryDemoStudent")
    public R<Boolean> queryDemoStudent(StudentBo bo) {
        Long branchId = bo.getBranchId();
        if (Objects.isNull(branchId)) {
            return R.ok(true);
        }
        bo.setStudentAccountType(1);
        bo.setBranchId(branchId);
        List<Long> longs = studentService.queryStudentIdList(bo);
        return R.ok(CollectionUtils.isEmpty(longs));
    }

    /**
     * 禁用账号
     * @param studentId 会员id
     */
    @SaCheckPermission("student:student:edit")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@NotNull Long studentId,@NotNull String studentStatus) {
        studentService.changeStatus(studentId,studentStatus);
        return R.ok();
    }

    /**
     * 强制下线
     * @param studentId 会员id
     */
    @SaCheckPermission("student:student:edit")
    @Log(title = "会员", businessType = BusinessType.FORCE)
    @DeleteMapping("/forceOffline/{studentId}")
    public R<Void> forceOffline(@PathVariable Long studentId) {
        studentService.forceOffline(studentId);
        return R.ok();
    }

    // 修改密码
    @ApiEncrypt
    @SaCheckPermission("branch:branch:resetPwd")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @PutMapping("/changePwd")
    public R<Void> changePwd(@RequestBody StudentBo bo) {
        studentService.restPwd(bo);
        return R.ok();
    }


    /**
     * 获取 科目观看范围 SubjectViewingRange
     *
     * @param studentBo 会员bo
     */
    @GetMapping("/getSubjectViewingRange")
    public R<SubjectViewingRangeVo> getSubjectViewingRange(StudentBo studentBo) {

        SubjectViewingRangeVo subjectViewingRangeVo = new SubjectViewingRangeVo();

        StudentVo studentVo = studentService.queryById(studentBo);
        if (studentVo.getExpireTime()==null) {
            return R.fail("无有效会员卡");
        }
        if (studentVo.getExpireTime().getTime()<System.currentTimeMillis()) {
            return R.fail("会员卡已过期");
        }
        RemoteDeptVo preDeptByDeptVo = remoteDeptService.getPreDeptByDeptId(studentVo.getCreateDept());
        // 根据这个deptId查询到关联的模板ID
        RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
        remoteStudentProductTemplateAuthBo.setDeptId(preDeptByDeptVo.getDeptId());//代理商ID
        remoteStudentProductTemplateAuthBo.setTemplateType(1);//课程模板查询
        List<Long> longs = remoteOrderService.queryAuthCourseIds(remoteStudentProductTemplateAuthBo);

        if(null != studentVo && null != studentVo.getStudentAiCourseRecordId()){
            //配置的课程
            StudentAiCourseRecordInfoBo studentAiCourseRecordInfoBo = new StudentAiCourseRecordInfoBo();
            studentAiCourseRecordInfoBo.setStudentAiCourseRecordId(studentVo.getStudentAiCourseRecordId());
            List<StudentAiCourseRecordInfoVo> voList = studentAiCourseRecordInfoService.queryList(studentAiCourseRecordInfoBo);
            if(null != voList && voList.size()>0){
                String studentAiCourseIds = voList.stream().map(StudentAiCourseRecordInfoVo::getCourseId).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
                subjectViewingRangeVo.setStudentAiCourseIds(studentAiCourseIds);
            }
        }else {
            if(null != longs && !longs.isEmpty()){
                String studentAiCourseIds = longs.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
                subjectViewingRangeVo.setStudentAiCourseIds(studentAiCourseIds);
            }
        }

        //变更记录
        {
            StudentAiCourseRecordBo studentAiCourseRecordBo = new StudentAiCourseRecordBo();
            studentAiCourseRecordBo.setStudentId(studentBo.getStudentId());
            studentAiCourseRecordBo.setWithSysUserInfo(true);
            List<StudentAiCourseRecordVo> studentAiCourseRecordVoList = studentAiCourseRecordService.queryList(studentAiCourseRecordBo);

            List<ChangeRecordInfoVo> changeRecordInfoVoList = new ArrayList<>();

            studentAiCourseRecordVoList.parallelStream().forEach(vo->{
                StudentAiCourseRecordInfoBo studentAiCourseRecordInfoBo = new StudentAiCourseRecordInfoBo();
                studentAiCourseRecordInfoBo.setStudentAiCourseRecordId(vo.getStudentAiCourseRecordId());
                studentAiCourseRecordInfoBo.setWithCourseInfo(true);
                List<StudentAiCourseRecordInfoVo> voList = studentAiCourseRecordInfoService.queryList(studentAiCourseRecordInfoBo);

                if(null != voList && voList.size()>0){
                    ChangeRecordInfoVo changeRecordInfoVo = new ChangeRecordInfoVo();
                    changeRecordInfoVo.setChangeDate(vo.getCreateTime());//变更日期
                    changeRecordInfoVo.setChangeName(vo.getSysUser().getNickName());// 变更人

                    StringBuilder info = new StringBuilder("变更后：【");//变更明细

                    for(int i=0;i<voList.size();i++){
                        CourseVo course = voList.get(i).getCourse();
                        if (ObjectUtils.isNotEmpty(course)) {
                            info.append(course.getCourseName() + (i == voList.size() - 1 ? "】" : "，"));
                        }
                    }

                    changeRecordInfoVo.setInfo(info.toString());
                    changeRecordInfoVoList.add(changeRecordInfoVo);
                }
            });

            subjectViewingRangeVo.setChangeList(changeRecordInfoVoList);
        }
        if (CollectionUtils.isEmpty(longs)){
            return R.ok(subjectViewingRangeVo);
        }
        CourseBo courseBo = new CourseBo();
        //不要孩子节点
        courseBo.setNeedChild(false);
        courseBo.setCourseIdList(longs);
        List<Tree<Long>> treeList = courseService.treeByStage(courseBo);
        subjectViewingRangeVo.setCourseTree(treeList);

        return R.ok(subjectViewingRangeVo);
    }

    /**
     * 设置 科目观看范围
     */
    @Log(title = "设置 科目观看范围", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/setSubjectViewingRange")
    public R<Void> setSubjectViewingRange(@Validated(AddGroup.class) @RequestBody StudentAiCourseRecordBo bo) {
        if(null == bo.getStudentId()){
            return R.fail("参数丢失");
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(bo.getStudentId());
        StudentVo studentVo = studentService.queryById(studentBo);
        if (studentVo.getExpireTime()==null) {
            return R.fail("无有效会员卡");
        }
        if (studentVo.getExpireTime().getTime()<System.currentTimeMillis()) {
            return R.fail("会员卡已过期");
        }
        return toAjax(studentAiCourseRecordService.insertByBo(bo));
    }



    /**
     * 获取会员详细信息
     *
     * @param studentBo 会员bo
     */
    @GetMapping("/getPreferentialAmount")
    public R<StudentVo> getPreferentialAmount(StudentBo studentBo) {
        if (studentBo.getStudentId() == null) {
            return R.fail("会员id不能为空");
        }
        return R.ok(studentService.getPreferentialAmount(studentBo));
    }


    /**
     * 管控-获取会员信息列表接口
     */
    @GetMapping("/queryControlledStudentList")
    public TableDataInfo<StudentVo> getControlledStudentList(@RequestParam("branchFeatureTemplateId") Long branchFeatureTemplateId,
                                                             StudentBo bo, PageQuery pageQuery) {
        return studentService.queryControlledStudentList(branchFeatureTemplateId,bo, pageQuery);
    }

    /**
     * 更新快叮岛会员权益
     *
     * @return
     */
    @PostMapping("updateKuaidingPrivilege")
    public R<Void> updateKuaidingPrivilege(@RequestBody UpdateKuaidingPrivilegeBo bo) {
        return studentService.updateKuaidingPrivilege(bo) ? R.ok() : R.fail("更新失败");
    }

}
