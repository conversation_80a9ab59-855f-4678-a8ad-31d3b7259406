package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.QuestionVideoRecord;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import com.jxw.shufang.student.domain.dto.BatchQueryQuestionRecordDTO;
import com.jxw.shufang.student.domain.vo.QuestionVideoRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 题目视频记录（题目视频观看记录）Service接口
 * @date 2024-05-29
 */
public interface IQuestionVideoRecordService {

    /**
     * 查询题目视频记录（题目视频观看记录）
     */
    QuestionVideoRecordVo queryById(Long questionVideoRecordId);

    /**
     * 查询题目视频记录（题目视频观看记录）列表
     */
    TableDataInfo<QuestionVideoRecordVo> queryPageList(QuestionVideoRecordBo bo, PageQuery pageQuery);

    /**
     * 查询题目视频记录（题目视频观看记录）列表
     */
    List<QuestionVideoRecordVo> queryList(QuestionVideoRecordBo bo);

    /**
     * 新增题目视频记录（题目视频观看记录）
     */
    Boolean insertByBo(QuestionVideoRecordBo bo);

    /**
     * 修改题目视频记录（题目视频观看记录）
     */
    Boolean updateByBo(QuestionVideoRecordBo bo);

    /**
     * 校验并批量删除题目视频记录（题目视频观看记录）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean saveBatch(List<QuestionVideoRecord> insertList);

    Boolean updateBatchById(List<QuestionVideoRecord> updateList);

    List<QuestionVideoRecord> batchQuerySameStudentRecord(List<BatchQueryQuestionRecordDTO> recordList);

}
