package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.ApplyCorrectionRecord;
import com.jxw.shufang.student.domain.bo.AllowOwnCorrectionBo;
import com.jxw.shufang.student.domain.bo.ApplyCorrectionRecordBo;
import com.jxw.shufang.student.mapper.ApplyCorrectionRecordMapper;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 申请批改记录Service业务层处理
 *
 *
 * @date 2024-05-07
 */
@RequiredArgsConstructor
@Service
public class ApplyCorrectionRecordServiceImpl implements IApplyCorrectionRecordService, BaseService {

    private final ApplyCorrectionRecordMapper baseMapper;

    private final IAllowOwnCorrectionService allowOwnCorrectionService;
    private final ICourseService courseService;
    private final IStudentConsultantRecordService studentConsultantRecordService;
    private final IStudyRecordService studyRecordService;
    private final IStudentService studentService;
    private final RocketMQTemplate rocketMQTemplate;


    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    /**
     * 查询申请批改记录
     */
    @Override
    public ApplyCorrectionRecordVo queryById(Long applyCorrectionRecordId) {
        return baseMapper.selectVoById(applyCorrectionRecordId);
    }

    /**
     * 查询申请批改记录列表
     */
    @Override
    public TableDataInfo<ApplyCorrectionRecordVo> queryPageList(ApplyCorrectionRecordBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<ApplyCorrectionRecord> lqw = buildQueryWrapper(bo);
        Page<ApplyCorrectionRecordVo> result = baseMapper.selectRecordVoPage(pageQuery.build(), lqw);

        putTopCourseInfo(result.getRecords(), true);

        if (Boolean.TRUE.equals(bo.getWithAllowOwnCorrection())) {
            putAllowOwnCorrection(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentUserInfo())) {
            putSysUserInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithTPState())) {
            putTPState(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    public void putTPState(List<ApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        for (ApplyCorrectionRecordVo record : records) {

            StudyRecordVo studyRecordVo = studyRecordService.queryByStudyPlanningRecordId(record.getStudyPlanningRecordId());

            record.setTestState(null != studyRecordVo && StringUtils.isNotBlank(studyRecordVo.getTestState()) ? studyRecordVo.getTestState() : "1");

            record.setPracticeState(null != studyRecordVo && StringUtils.isNotBlank(studyRecordVo.getPracticeState()) ? studyRecordVo.getPracticeState() : "1");
        }
    }

    public void putSysUserInfo(List<ApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(ApplyCorrectionRecordVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        remoteUserBo.setGetAvatarUrl(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    //会员顾问记录详情
    private void putConsultantInfo(List<ApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(ApplyCorrectionRecordVo::getStudent).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentConsultantRecordId = studentVos.stream().map(StudentVo::getStudentConsultantRecordId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantRecordId)) {
            return;
        }
        List<StudentConsultantRecordVo> studentConsultantRecordVos = studentConsultantRecordService.queryByIdList(studentConsultantRecordId);
        if (CollUtil.isEmpty(studentConsultantRecordVos)) {
            return;
        }
        List<Long> studentConsultantIdList = studentConsultantRecordVos.stream().map(StudentConsultantRecordVo::getStudentConsultantId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(studentConsultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        studentVos.forEach(studentVo -> {
            StudentConsultantRecordVo studentConsultantRecordVo = studentConsultantRecordVos.stream().filter(vo -> vo.getStudentConsultantRecordId().equals(studentVo.getStudentConsultantRecordId())).findFirst().orElse(null);
            if (studentConsultantRecordVo != null) {
                RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(studentConsultantRecordVo.getStudentConsultantId());
                studentVo.setConsultant(remoteStaffVo);
            }
        });

    }

    private void putAllowOwnCorrection(List<ApplyCorrectionRecordVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(ApplyCorrectionRecordVo::getStudentId).toList();
        AllowOwnCorrectionBo allowOwnCorrectionBo = new AllowOwnCorrectionBo();
        allowOwnCorrectionBo.setStudentIdList(studentIdList);
        allowOwnCorrectionBo.setType("1");
        List<AllowOwnCorrectionVo> allowOwnCorrectionList = allowOwnCorrectionService.queryList(allowOwnCorrectionBo);
        if (CollUtil.isEmpty(allowOwnCorrectionList)) {
            return;
        }

        Map<String, AllowOwnCorrectionVo> allowOwnCorrectionMap = StreamUtils.toIdentityMap(allowOwnCorrectionList, e -> e.getStudentId() + "_" + e.getAllowType());
        for (ApplyCorrectionRecordVo record : records) {
            AllowOwnCorrectionVo vo = allowOwnCorrectionMap.get(record.getStudentId() + "_" + record.getApplyType());
            record.setAllowOwnCorrection(vo);
            record.setAllowOwnType(ObjectUtil.isEmpty(vo) ? "2" : "1");//是否允许自主批改（1允许自主批改 2不允许自主批改）
        }
    }

    private void putTopCourseInfo(List<ApplyCorrectionRecordVo> records, boolean queryDetailInfo) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<CourseVo> courseList = records.stream().map(ApplyCorrectionRecordVo::getCourse).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(courseList)) {
            return;
        }
        courseService.putTopmostCourseInfo(courseList, queryDetailInfo);
        if (queryDetailInfo) {
            List<CourseVo> list = courseList.stream().map(CourseVo::getTopmostCourse).toList();
            courseService.putCourseDetail(list, true);
        }
    }


    /**
     * 查询申请批改记录列表
     */
    @Override
    public List<ApplyCorrectionRecordVo> queryList(ApplyCorrectionRecordBo bo) {
        handleQueryParam(bo);
        LambdaQueryWrapper<ApplyCorrectionRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ApplyCorrectionRecord> buildLambdaQueryWrapper(ApplyCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ApplyCorrectionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, ApplyCorrectionRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, ApplyCorrectionRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), ApplyCorrectionRecord::getApplyType, bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyResult()), ApplyCorrectionRecord::getApplyResult, bo.getApplyResult());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), ApplyCorrectionRecord::getStudentId, bo.getStudentIdList());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.apply(StringUtils.isNotBlank(bo.getTimeLimit()), "create_time BETWEEN {0} AND {1}", StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[0] : null, StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[1] : null);
        return lqw;
    }

    private QueryWrapper<ApplyCorrectionRecord> buildQueryWrapper(ApplyCorrectionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<ApplyCorrectionRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getStudyPlanningRecordId() != null, "t.study_planning_record_id", bo.getStudyPlanningRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyType()), "t.apply_type", bo.getApplyType());
        lqw.eq(StringUtils.isNotBlank(bo.getApplyResult()), "t.apply_result", bo.getApplyResult());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        if (StringUtils.isNotBlank(bo.getAllowOwnType()) && StringUtils.isNotBlank(bo.getApplyType())) {
            AllowOwnCorrectionBo allowOwnCorrectionBo = new AllowOwnCorrectionBo();
            allowOwnCorrectionBo.setType("1");
            allowOwnCorrectionBo.setAllowType(bo.getApplyType());
            List<AllowOwnCorrectionVo> voList = allowOwnCorrectionService.queryList(allowOwnCorrectionBo);
            List<Long> studentIdList = voList.stream().map(AllowOwnCorrectionVo::getStudentId).filter(Objects::nonNull).collect(Collectors.toList());
            if ("1".equals(bo.getAllowOwnType())) {
                if (null != studentIdList && studentIdList.size() > 0) {
                    lqw.in(StringUtils.isNotBlank(bo.getAllowOwnType()), "t.student_id", studentIdList);
                }
            } else if ("2".equals(bo.getAllowOwnType())) {
                if (null != studentIdList && studentIdList.size() > 0) {
                    lqw.notIn(StringUtils.isNotBlank(bo.getAllowOwnType()), "t.student_id", studentIdList);
                }
            }
        }
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like {0}", "%" + bo.getNameWithPhone() + "%");
        lqw.apply(StringUtils.isNotBlank(bo.getTimeLimit()),
            "t.apply_time is not null and t.apply_time BETWEEN {0} AND {1}",
            StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[0] : null,
            StringUtils.isNotBlank(bo.getTimeLimit()) ? bo.getTimeLimit().split(" 至 ")[1] : null);
        return lqw;
    }

    /**
     * 新增申请批改记录
     */
    @Override
    public Boolean insertByBo(ApplyCorrectionRecordBo bo) {
        Long studentId = LoginHelper.getStudentId();
        ApplyCorrectionRecord add = MapstructUtils.convert(bo, ApplyCorrectionRecord.class);
        ApplyCorrectionRecordVo applyCorrectionRecordVo = validEntityBeforeSave(add, true);
        if (applyCorrectionRecordVo != null) {
            if (updateApplyTime(applyCorrectionRecordVo)) {
                return true;
            } else {
                throw new ServiceException("重新申请失败");
            }
        }

        //始终允许自己批改
        AllowOwnCorrectionVo allowOwnCorrectionVo = allowOwnCorrectionService.queryByStudentId(bo.getStudentId(), "1", bo.getApplyType());
        if (allowOwnCorrectionVo != null) {
            add.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
        } else {
            add.setApplyResult(UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT);
        }
        // 设置申请时间
        add.setApplyTime(new Date());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT.equals(add.getApplyResult())) {
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put(NotifyMessageConstant.CONTENT, NoticeBizTypeEnum.ZZPGSQ.getDesc());

                RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder()
                    .templateCode(NoticeBizTypeEnum.ZZPGSQ.name())
                    .bizType(NoticeBizTypeEnum.ZZPGSQ.getCode())
                    .noticeType(NoticeTypeEnum.INTERNAL.getCode())
                    .content("向您申请进行自主批改申请审核操作")
                    .studentId(studentId)
                    .paramMap(paramMap)
                    .build();
                rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);
            }
            bo.setApplyCorrectionRecordId(add.getApplyCorrectionRecordId());
        }
        return flag;
    }

    /**
     * 修改申请批改记录
     */
    @Override
    public Boolean updateByBo(ApplyCorrectionRecordBo bo) {
        ApplyCorrectionRecord update = MapstructUtils.convert(bo, ApplyCorrectionRecord.class);
        validEntityBeforeSave(update, false);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @return
     */
    private ApplyCorrectionRecordVo validEntityBeforeSave(ApplyCorrectionRecord entity, boolean forAdd) {
        //做一些数据校验,如唯一约束
        if (entity.getApplyCorrectionRecordId() == null && entity.getStudentId() != null && entity.getStudyPlanningRecordId() != null && StringUtils.isNotBlank(entity.getApplyType())) {
            LambdaQueryWrapper<ApplyCorrectionRecord> lqw = Wrappers.lambdaQuery();
            lqw.eq(ApplyCorrectionRecord::getStudentId, entity.getStudentId());
            lqw.eq(ApplyCorrectionRecord::getStudyPlanningRecordId, entity.getStudyPlanningRecordId());
            lqw.eq(ApplyCorrectionRecord::getApplyType, entity.getApplyType());
            lqw.in(ApplyCorrectionRecord::getApplyResult, UserConstants.APPLY_CORRECTION_RECORD_STATUS_WAIT, UserConstants.APPLY_CORRECTION_RECORD_STATUS_ALLOW);
            lqw.orderByDesc(ApplyCorrectionRecord::getApplyCorrectionRecordId).last("limit 1 ");
            ApplyCorrectionRecordVo applyCorrectionRecordVo = baseMapper.selectVoOne(lqw);
            if (applyCorrectionRecordVo != null) {
                if (forAdd && !wheatherApplyRecordVisiable(applyCorrectionRecordVo)) {
                    return applyCorrectionRecordVo;
                }
                throw new ServiceException("存在已通过或待审核的申请记录，不允许再新增申请");
            }
        }
        return null;
    }

    /**
     * 重新申请更新申请记录的申请时间
     *
     * @param aiApplyCorrectionRecordVo
     * @return
     */
    private boolean updateApplyTime(ApplyCorrectionRecordVo aiApplyCorrectionRecordVo) {
        if (aiApplyCorrectionRecordVo == null || aiApplyCorrectionRecordVo.getApplyCorrectionRecordId() == null) {
            return false;
        }
        ApplyCorrectionRecordBo updateBo = new ApplyCorrectionRecordBo();
        updateBo.setApplyCorrectionRecordId(aiApplyCorrectionRecordVo.getApplyCorrectionRecordId());
        updateBo.setApplyTime(new Date());
        return updateByBo(updateBo);
    }

    /**
     * 批量删除申请批改记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public ApplyCorrectionRecordVo queryLastRecord(ApplyCorrectionRecordBo applyCorrectionRecordBo) {
        LambdaQueryWrapper<ApplyCorrectionRecord> applyCorrectionRecordLambdaQueryWrapper = buildLambdaQueryWrapper(applyCorrectionRecordBo);
        applyCorrectionRecordLambdaQueryWrapper.orderByDesc(ApplyCorrectionRecord::getCreateTime);
        applyCorrectionRecordLambdaQueryWrapper.last("limit 1");
        return baseMapper.selectVoOne(applyCorrectionRecordLambdaQueryWrapper);
    }

    @Override
    public boolean updateApplyResult(Long applyCorrectionRecordId, String applyResult) {
        ApplyCorrectionRecord applyCorrectionRecord = new ApplyCorrectionRecord();
        applyCorrectionRecord.setApplyCorrectionRecordId(applyCorrectionRecordId);
        applyCorrectionRecord.setApplyResult(applyResult);
        return baseMapper.updateById(applyCorrectionRecord) > 0;
    }

    @Override
    public Long count(ApplyCorrectionRecordBo bo) {
        handleQueryParam(bo);
        LambdaQueryWrapper<ApplyCorrectionRecord> queryWrapper = buildLambdaQueryWrapper(bo);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public boolean updateBatchById(List<ApplyCorrectionRecordBo> updateList) {
        List<ApplyCorrectionRecord> convert = MapstructUtils.convert(updateList, ApplyCorrectionRecord.class);
        return baseMapper.updateBatchById(convert);
    }

    @Override
    public boolean wheatherApplyRecordVisiable(ApplyCorrectionRecordVo applyCorrectionRecord) {
        if (applyCorrectionRecord == null
            || (applyCorrectionRecord.getCreateTime() == null && applyCorrectionRecord.getApplyTime() == null)) {
            return false;
        }
        LocalDateTime today3AM = LocalDate.now().atTime(3, 0);
        Date today3AMDate = Date.from(today3AM.atZone(ZoneId.systemDefault()).toInstant());
        Date lastApplyTime = applyCorrectionRecord.getApplyTime() == null ? applyCorrectionRecord.getCreateTime()
            : applyCorrectionRecord.getApplyTime();
        return !lastApplyTime.before(today3AMDate);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void handleQueryParam(ApplyCorrectionRecordBo record) {
        if (record.getStudentId() != null) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else if (null != LoginHelper.getBranchId()) {
            List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
            }
        } else {
            record.setStudentIdList(List.of(-1L));
        }

    }


}
