package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceV2Vo;
import com.jxw.shufang.student.domain.bo.CourseResourceBo;
import com.jxw.shufang.student.domain.bo.CourseResourceKnowledgeBo;
import com.jxw.shufang.student.domain.bo.CourseResourceV2Bo;
import com.jxw.shufang.student.domain.dto.CourseWithTopCourseDTO;
import com.jxw.shufang.student.domain.dto.DownloadMergeResourceDTO;
import com.jxw.shufang.student.domain.vo.CourseResourceV2Vo;
import com.jxw.shufang.student.domain.vo.CourseResourceVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 课程资源（绑定到课程的资源）Service接口
 *
 *
 * @date 2024-05-13
 */
public interface ICourseResourceService {

    /**
     * 查询课程资源（绑定到课程的资源）
     */
    CourseResourceVo queryById(Long courseResourceId);

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    TableDataInfo<CourseResourceVo> queryPageList(CourseResourceBo bo, PageQuery pageQuery);

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    List<CourseResourceVo> queryList(CourseResourceBo bo);

    /**
     * 新增课程资源（绑定到课程的资源）
     */
    Boolean insertByBo(CourseResourceBo bo);

    /**
     * 修改课程资源（绑定到课程的资源）
     */
    Boolean updateByBo(CourseResourceBo bo);

    /**
     * 校验并批量删除课程资源（绑定到课程的资源）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    CourseResourceV2Vo getInfo(CourseResourceV2Bo bo);

    String preview(CourseResourceKnowledgeBo bo);

    List<String> multiGetUrls(List<CourseResourceKnowledgeBo> knowledgeList);

    List<RemoteGroupResourceV2Vo> multiGetUrlsVo(List<CourseResourceKnowledgeBo> knowledgeList);

    /**
     * 下载合并资源
     *
     */
    Map<Long, CourseWithTopCourseDTO> getMergeCourseResource(DownloadMergeResourceDTO resourceDTO);
}
