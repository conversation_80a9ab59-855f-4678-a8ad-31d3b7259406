package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGradeVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGradeVolumeVo;
import com.jxw.shufang.extresource.api.domain.vo.RemotePublisherVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteSubjectVo;
import com.jxw.shufang.student.domain.StudentPaperRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 用户答题试卷记录视图对象 student_paper_record
 *
 * <AUTHOR>
 * @date 2024-08-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentPaperRecord.class)
public class StudentPaperRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long studentPaperRecordId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long studentId;

    /**
     * 学生姓名
     */
    @ExcelProperty(value = "学生姓名")
    private String studentName;

    @ExcelProperty(value = "性别")
    private String sex;

    /**
     * 试卷id
     */
    @ExcelProperty(value = "试卷id")
    private Long testPaperId;

    /**
     * 试卷名称
     */
    @ExcelProperty(value = "试卷名称")
    private String testPaperName;


    /**
     * 评测范围
     */
    @ExcelProperty(value = "评测范围")
    private String paperTypeName;
    /**
     * 试卷类型
     */
    @ExcelProperty(value = "试卷类型")
    private Integer testPaperType;

    /**
     * 试卷科目id
     */
    @ExcelProperty(value = "试卷科目id")
    private Integer subjectId;

    private RemoteSubjectVo subjectVo;

    /**
     * 年级id
     */
    @ExcelProperty(value = "年级id")
    private Integer gradeId;

    private RemoteGradeVo gradeVo;

    @ExcelProperty(value = "学册id")
    private Integer gradeVolumeId;
    private RemoteGradeVolumeVo gradeVolumeVo;

    @ExcelProperty(value = "细分出版社")
    private Integer editionId;

    private RemotePublisherVo editionVo;

    @ExcelProperty(value = "学段id")
    private Integer phaseId;
    @ExcelProperty(value = "学段名称")
    private String phaseName;

    @ExcelProperty(value = "总用时")
    private Long durationMillis;

    /**
     * 正确率
     */
    @ExcelProperty(value = "正确率或者分数")
    private Integer score;
    @ExcelProperty(value = "上一次的正确率")
    private Integer lastScore;
    /**
     * 学习记录状态（0未完成 1已完成）
     */
    @ExcelProperty(value = "学习记录状态")
    @ExcelDictFormat(readConverterExp = "0=未完成,1=已完成")
    private Integer recordStatus;

    /**
     * pdf报告url
     */
    @ExcelProperty(value = "pdf报告url")
    private String reportUrl;

    /**
     * （来源） XWSF=学王书房
     */
    @ExcelProperty(value = "来源")
    private String source;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间")
    private Date finishTime;

    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * "会员顾问姓名
     */
    @ExcelProperty(value = "会员顾问姓名")
    private String staffName;
}
