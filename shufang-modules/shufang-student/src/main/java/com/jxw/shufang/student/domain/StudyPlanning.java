package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习规划对象 study_planning
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_planning")
public class StudyPlanning extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划id
     */
    @TableId(value = "study_planning_id")
    private Long studyPlanningId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 学习规划日期
     */
    private Date studyPlanningDate;

    /**
     * 学习规划保存状态（1暂存 2完成）
     */
    private String studyPlanningStatus;


}
