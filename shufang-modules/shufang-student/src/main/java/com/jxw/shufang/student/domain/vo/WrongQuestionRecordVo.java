package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.student.domain.WrongQuestionRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 错题记录视图对象 wrong_question_record
 *
 *
 * @date 2024-05-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WrongQuestionRecord.class)
public class WrongQuestionRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题id
     */
    @ExcelProperty(value = "错题id")
    private Long wrongQuestionRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 学习规划记录ID
     */
    @ExcelProperty(value = "学习规划记录ID")
    private Long studyPlanningRecordId;

    /**
     * 问题ID
     */
    @ExcelProperty(value = "问题ID")
    private Long questionId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 题目序号
     */
    @ExcelProperty(value = "题目序号")
    private String questionNo;

    /**
     * 作答结果（2全错 3半错）
     */
    @ExcelProperty(value = "作答结果", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如全错,半=错")
    private String answerResult;

    /**
     * 来源类型（1测试 2练习）
     */
    private String sourceType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 解析视频
     */
    private RemoteVideoVo questionVideo;

    /**
     * 题目题干
     */
    private RemoteQuestionVo questionVo;

    /**
     * 课程信息
     */
    private CourseVo course;

    /**
     * 做题截图，ossId，逗号分开的
     */
    private String correctionScreenshots;

    private Integer wrongCount;

    /**
     * 订正状态 0-否 1-是
     */
    private Integer reviseStatus;

    /**
     * 订正时间
     */
    private Date reviseTime;

    /**
     * 订正截图（oss_id，多个，逗号隔开)
     */
    private String reviseScreenshots;

    public Double takeQuestionNo(){
        if (null == this.questionNo|| StringUtils.isBlank(this.questionNo)){
            return null;
        }
        //题目形式 1    1.2   1.2.1    1.3.4 之类的
        //将题目序号转换为小数，去除第一个小数点以后的小数点，能达到正确的排序效果
        String[] split = this.questionNo.split("\\.");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < split.length; i++) {
            if (i == 0){
                if (split.length==1){
                    sb.append(split[i]);
                }else {
                    sb.append(split[i]).append(".");
                }
            }else {
                sb.append(split[i]);
            }
        }
        return Double.valueOf(sb.toString());

    }
}
