package com.jxw.shufang.student.domain.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/26 16:04
 * @Version 1
 * @Description
 */
@Data
public class DownloadMergeResourceDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -5313137392275293827L;
    /**
     * 学生ID
     */
    @NotNull(message = "学生ID不可为空")
    private Long studentId;

    /**
     * 下载的课程id
     */
    @NotNull(message = "下载的课程id不可为空")
    private List<Long> courseIds;

    /**
     * 试卷资源类型 KnowledgeResourceType
     */
    @NotEmpty(message = "试卷资源类型不可为空")
    private List<String> resourceTypes;
}
