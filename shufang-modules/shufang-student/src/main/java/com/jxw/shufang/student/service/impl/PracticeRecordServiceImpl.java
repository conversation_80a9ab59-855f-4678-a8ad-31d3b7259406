package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.PracticeRecord;
import com.jxw.shufang.student.domain.bo.PracticeRecordBo;
import com.jxw.shufang.student.domain.vo.PracticeRecordVo;
import com.jxw.shufang.student.mapper.PracticeRecordMapper;
import com.jxw.shufang.student.service.IPracticeRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 练习记录Service业务层处理
 *
 *
 * @date 2024-05-09
 */
@RequiredArgsConstructor
@Service
public class PracticeRecordServiceImpl implements IPracticeRecordService, BaseService {

    private final PracticeRecordMapper baseMapper;

    /**
     * 查询练习记录
     */
    @Override
    public PracticeRecordVo queryById(Long practiceRecordId){
        return baseMapper.selectVoById(practiceRecordId);
    }

    /**
     * 查询练习记录列表
     */
    @Override
    public TableDataInfo<PracticeRecordVo> queryPageList(PracticeRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PracticeRecord> lqw = buildQueryWrapper(bo);
        Page<PracticeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询练习记录列表
     */
    @Override
    public List<PracticeRecordVo> queryList(PracticeRecordBo bo) {
        LambdaQueryWrapper<PracticeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PracticeRecord> buildQueryWrapper(PracticeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PracticeRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudyPlanningRecordId() != null, PracticeRecord::getStudyPlanningRecordId, bo.getStudyPlanningRecordId());
        lqw.eq(bo.getStudentId() != null, PracticeRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getQuestionId() != null, PracticeRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getCourseResourceId() != null, PracticeRecord::getCourseResourceId, bo.getCourseResourceId());
        lqw.eq(bo.getResourceContent() != null, PracticeRecord::getResourceContent, bo.getResourceContent());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), PracticeRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), PracticeRecord::getAnswerResult, bo.getAnswerResult());
        lqw.eq(bo.getAnswerImg() != null, PracticeRecord::getAnswerImg, bo.getAnswerImg());
        return lqw;
    }

    /**
     * 新增练习记录
     */
    @Override
    public Boolean insertByBo(PracticeRecordBo bo) {
        PracticeRecord add = MapstructUtils.convert(bo, PracticeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPracticeRecordId(add.getPracticeRecordId());
        }
        return flag;
    }

    /**
     * 修改练习记录
     */
    @Override
    public Boolean updateByBo(PracticeRecordBo bo) {
        PracticeRecord update = MapstructUtils.convert(bo, PracticeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PracticeRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除练习记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<PracticeRecordBo> convert) {
        List<PracticeRecord> practiceRecordList = MapstructUtils.convert(convert, PracticeRecord.class);
        return baseMapper.insertBatch(practiceRecordList);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
