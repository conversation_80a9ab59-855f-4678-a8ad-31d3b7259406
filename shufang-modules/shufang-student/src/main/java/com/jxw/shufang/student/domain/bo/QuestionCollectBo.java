package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.DeleteGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.QuestionCollect;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QuestionCollect.class, reverseConvertGenerate = false)
public class QuestionCollectBo extends BaseEntity {
    /**
     * 收藏题目id
     */
    private Long questionCollectId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 题目ID
     */
    @NotNull(message = "题目id不能为空", groups = {AddGroup.class, DeleteGroup.class})
    private Long questionId;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;
}
