package com.jxw.shufang.student.domain.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class CourseTreeNodeDTO {

    @NotNull(message = "id不能为空")
    private Long id;

    @NotNull(message = "课程名称不能为空")
    private String name;

    @NotNull(message = "节点标识不能为空")
    private Integer leaf;

    private List<CourseTreeNodeDTO> children;

}
