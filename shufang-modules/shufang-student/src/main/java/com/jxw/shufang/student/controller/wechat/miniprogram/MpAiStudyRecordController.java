package com.jxw.shufang.student.controller.wechat.miniprogram;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiStudyRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyRecordVo;
import com.jxw.shufang.student.service.IAiStudyRecordService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * AI学习记录---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/aiStudyRecord
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/aiStudyRecord")
public class MpAiStudyRecordController extends BaseController {

    private final IAiStudyRecordService aiStudyRecordService;
    private final IStudentConsultantRecordService studentConsultantRecordService;


    private void putStaffResponsibleStudentIdList(AiStudyRecordBo bo) {
        List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
        bo.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList)?List.of(-1L):staffResponsibleStudentIdList);
    }

    /**
     * 批改记录
     */
    @GetMapping("/pageList")
    public TableDataInfo<AiStudyRecordVo> pageList(AiStudyRecordBo bo , PageQuery pageQuery) {
        putStaffResponsibleStudentIdList(bo);
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return aiStudyRecordService.queryStudyRecordPage(bo, pageQuery);
    }

    /**
     * 查询批改状态
     * @param courseId 课程ID
     * @param studentId 会员id
     * @param resourceType 批改类型  PRACTICE=练习，TEST=考试
     * @return 批改状态 1=未批改,2=批改中,3=已批改
     */
    @GetMapping("/queryCorrectionStatus")
    public R<Integer> queryCorrectionStatus(@NotNull(message = "courseId不能为空") Long courseId,
                                            @NotNull(message = "studentId不能为空") Long studentId,
                                            @NotNull(message = "correctionType不能为空") KnowledgeResourceType resourceType) {

        AiStudyRecordVo aiStudyRecordVo = aiStudyRecordService.queryByStudentIdAndCourseId(studentId, courseId);
        if (aiStudyRecordVo == null) {
            return R.ok(1);
        }
        if (KnowledgeResourceType.PRACTICE.equals(resourceType)){
            return StringUtils.isNotBlank(aiStudyRecordVo.getPracticeState())? R.ok(Integer.parseInt(aiStudyRecordVo.getPracticeState())) : R.ok(1);
        }
        if (KnowledgeResourceType.TEST.equals(resourceType)){
            return StringUtils.isNotBlank(aiStudyRecordVo.getTestState())? R.ok(Integer.parseInt(aiStudyRecordVo.getTestState())) : R.ok(1);
        }

        return R.fail("未知的批改类型");
    }


}
