package com.jxw.shufang.student.domain.convert;

import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionVoConvert extends BaseMapper<RemoteQuestionVo, RemoteQuestionVo> {
}
