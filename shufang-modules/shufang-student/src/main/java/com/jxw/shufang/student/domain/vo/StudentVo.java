package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 会员视图对象 student
 *
 *
 * @date 2024-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Student.class)
public class StudentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 登录账号（可以用对应的sys_user表中的数据）
     */
    @ExcelProperty(value = "登录账号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=以用对应的sys_user表中的数据")
    private String studentAccount;

    /**
     * 登录密码（可以用对应的sys_user表中的数据）
     */
    @ExcelProperty(value = "登录密码", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "可=以用对应的sys_user表中的数据")
    private String studentPassword;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String studentName;

    /**
     * 性别（0男 1女） sys_user_sex
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String studentSex;

    /**
     * 年级（对应字典值） student_grade
     */
    @ExcelProperty(value = "年级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String studentGrade;

    /**
     * 来源（对应字典值） student_source
     */
    @ExcelProperty(value = "来源", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String studentSource;

    /**
     * 是否首次购卡
     */
    private Integer purchasedCardFlag;

    /**
     * 是否首次购卡导出内容
     */
    @ExcelProperty(value = "会员类型")
    private String hasPurchasedCardStr;

    /**
     * 家长电话
     */
    @ExcelProperty(value = "家长电话")
    private String studentParentPhone;

    /**
     * 备用电话（家长电话2）
     */
    @ExcelProperty(value = "备用电话", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "家=长电话2")
    private String studentBackupPhone;

    /**
     * 会员顾问记录id
     */
    @ExcelProperty(value = "会员顾问记录id")
    private Long studentConsultantRecordId;

    /**
     * 会员绑定家长记录id
     */
    @ExcelProperty(value = "会员绑定家长记录id")
    private Long studentParentRecordId;

    /**
     * 会员AI课程分配记录表id
     */
    @ExcelProperty(value = "会员AI课程分配记录表id")
    private Long studentAiCourseRecordId;

    /**
     * 会员备注
     */
    @ExcelProperty(value = "会员备注")
    private String studentRemark;

    /**
     * 最后登录时间（最近登录时间）
     */
    @ExcelProperty(value = "最后登录时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "最=近登录时间")
    private Date lastLoginTime;

    /**
     * 会员所拥有的产品中，最大的那个过期时间（已经向下取整）
     */
    private Date expireTime;

    /**
     * 姓名(电话后四位)，电话在这里其实就是用户名
     */
    private String nameWithPhone;

    /**
     * 门店信息
     */
    private RemoteBranchVo branch;

    /**
     * 会员顾问信息
     */
    private RemoteStaffVo consultant;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建dept
     */
    private Long createDept;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新dept
     */
    private Date updateTime;

    /**
     * sys用户(会员的一些其他信息在这里面)
     */
    private RemoteUserVo sysUser;

    /**
     * 会员信息
     */
    private StudentInfoVo studentInfo;

    /**
     * 会员家长记录
     */
    private StudentParentRecordVo studentParentRecord;

    /**
     * 会员标签
     */
    private List<StudentSignVo> studentSignList;

    /**
     * 会员最后一个已支付订单的产品清单
     */
    private List<RemoteOrderProductInfoVo> studentProductList;

    /**
     * 会员最后一个对应的AI课程分配汇总 如 初中2科 | 高中1科
     */
    private String aiCourseRecordInfoSummary;


    /**
     * 会员有产品名称，逗号分隔
     */
    private String studentHasProductNames;

    /**
     * 总积分
     */
    private BigDecimal totalIntegral;

    /**
     * 剩余有效期时长
     */
    private Long remainingHour;

    /**
     * 剩余学习天数
     */
    private Long remainingDay;

    /**
     * 考勤状态
     */
    private Boolean attendanceStatus;

    /**
     * 优惠额度
     */
    private BigDecimal preferentialAmount;

    /**
     * 冻结优惠额度
     */
    private BigDecimal frozenPreferentialAmount;

    /**
     * 优惠额度版本
     */
    private Integer preferentialAmountVersion;
    /**
     * 介绍人信息
     */
    private StudentVo introduceStudent;

    /**
     * 会员卡产品开始时间
     */
    private Date membershipBeginDate;

    /**
     * 会员卡产品结束时间
     */
    private Date membershipEndDate;

    /**
     * 存在未缴齐的订单
     */
    private Boolean hasSupplementOrder;

    /**
     * 会员卡最早开始时间
     */
    private Date earliestMemberCardStartDate;

}
