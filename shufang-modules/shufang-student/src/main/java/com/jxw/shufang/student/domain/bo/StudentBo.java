package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Student;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 会员业务对象 student
 *
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Student.class, reverseConvertGenerate = false)
public class StudentBo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 5995888578716433827L;
    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { EditGroup.class })
    private Long studentId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 登录账号（可以用对应的sys_user表中的数据）
     */
    @NotBlank(message = "登录账号（可以用对应的sys_user表中的数据）不能为空", groups = { AddGroup.class})
    private String studentAccount;

    private List<String> studentAccounts;

    /**
     * 登录密码（可以用对应的sys_user表中的数据）
     */
    private String studentPassword;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentName;

    /**
     * 性别（0男 1女）
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentSex;

    /**
     * 年级（对应字典值）
     */
    @NotBlank(message = "年级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentGrade;

    /**
     * 来源（对应字典值）
     */
    @NotBlank(message = "来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentSource;

    /**
     * 家长电话
     */
    @NotBlank(message = "家长电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentParentPhone;

    /**
     * 会员账号类型(0正常  1演示)
     */
    private Integer studentAccountType;

    /**
     * 备用电话（家长电话2）
     */
    //@NotBlank(message = "备用电话（家长电话2）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentBackupPhone;

    /**
     * 会员顾问记录id
     */
    //@NotNull(message = "会员顾问记录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentConsultantRecordId;

    /**
     * 会员绑定家长记录id
     */
    //@NotNull(message = "会员绑定家长记录id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentParentRecordId;

    /**
     * 会员AI课程分配记录表id
     */
    //@NotNull(message = "会员AI课程分配记录表id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentAiCourseRecordId;

    /**
     * 会员备注
     */
    //@NotBlank(message = "会员备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentRemark;

    /**
     * 最后登录时间（最近登录时间）
     */
    //@NotNull(message = "最后登录时间（最近登录时间）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date lastLoginTime;

    /**
     * 会员所拥有的产品中，最大的那个过期时间（已经向下取整）
     */
    private Date expireTime;


    /**
     * 姓名(电话后四位)，电话在这里其实就是用户名
     */
    private String nameWithPhone;

    /**
     * 是否查询分店信息
     */
    private Boolean withBranchInfo;

    /**
     * 是否查询会员顾问信息
     */
    private Boolean withConsultantInfo;

    /**
     * 是否查询会员对应的系统用户信息
     */
    private Boolean withSysUserInfo;

    /**
     * 是否查询会员对应的标签信息
     */
    private Boolean withStudentSignInfo;

    /**
     * 是否查询会员最后一个已支付订单的产品信息
     */
    private Boolean withStudentLastPayOrderProductInfo;

    /**
     * 是否查询会员最后一个对应的AI课程分配记录
     */
    private Boolean withStudentLastAiCourseRecordInfoSummary;

    /**
     * 是否查询会员剩余积分数量
     */
    private Boolean withStudentIntegralCount;

    /**
     * 是否查询转介绍信息
     */
    private Boolean withStudentIntroduce;


    /**
     * 顾问姓名
     */
    private String consultantName;

    /**
     * 会员状态（0正常 1停用）
     */
    private String studentStatus;

    /**
     * 会员ID
     */
    private List<Long> studentIds;

    /**
     * 不在会员ID
     */
    private List<Long> notInStudentIds;

    /**
     * 会员标签
     */
    private List<StudentSignBo> studentSignList;

    /**
     * 会员信息
     */
    private StudentInfoBo studentInfo;

    /**
     * 顾问id
     */
    @NotNull(message = "顾问id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long consultantId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 会员类型ID
     */
    private Long studentTypeId;

    /**
     * 是否绑定父母
     */
    private Boolean whetherBindParents;

    /**
     * 小于过期天数
     */
    private Integer lessThanExpireDays;

    /**
     * 小于过期小时
    */
    private Integer lessThanExpireHours;

    /**
     * 过期时间小于某个时间
     */
    private Date lessThanExpireTime;

    /**
     * 查询某一天过期的，只精确到年月日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date expireDate;


    /**
     * 学习规划日期(非数据库字段，仅用于查询条件)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDate;


    /**
     * 应用类型，1学习规划  2ai学习
     */
    private String type;

    /**
     * 允许类型，1练习  2测试
     */
    private String allowType;

    private List<Long> branchIdList;

    /**
     * 会员对应的系统用户idList
     */
    private List<Long> userIdList;
    /**
     * 介绍人信息
     */
    private StudentBo introduceStudent;

    /**
     * 优惠额度
     */
    private BigDecimal preferentialAmount;

    /**
     * 优惠额度版本
     */
    private Integer preferentialAmountVersion;

    /**
     * 调整类型 1-更换介绍人 2-调整会员渠道由转介绍调整为非转介绍 3-调整会员渠道由非转介绍调整为转介绍
     */
    private Integer updateStudentIntroduceRecordType;

    /**
     * 学生账号（允许跨店）
     */
    private String studentAccountAcrossBranch;

    /**
     * 存在未缴齐的订单
     */
    private Boolean existPendingPayOrder;

    private List<String> orderOperateStatusList;

    private Boolean excludeRemainHourInfo;

    /**
     * 是否首次购卡
     */
    private Integer purchasedCardFlag;
}
