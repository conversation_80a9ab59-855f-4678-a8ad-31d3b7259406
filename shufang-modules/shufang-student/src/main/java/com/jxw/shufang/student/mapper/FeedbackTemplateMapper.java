package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.FeedbackTemplate;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateVo;

/**
 * 反馈模板Mapper接口
 *
 *
 * @date 2024-03-18
 */
public interface FeedbackTemplateMapper extends BaseMapperPlus<FeedbackTemplate, FeedbackTemplateVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept"),
        @DataColumn(key = "userName", value = "t.create_by")
    })
    Page<FeedbackTemplateVo> selectFeedbackTemplatePage(@Param("page") Page<FeedbackTemplate> build,@Param(Constants.WRAPPER) QueryWrapper<FeedbackTemplate> lqw);
}
