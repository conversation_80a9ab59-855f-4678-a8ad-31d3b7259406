package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.QuestionVideoRecordBo;
import com.jxw.shufang.student.domain.vo.QuestionVideoRecordVo;
import com.jxw.shufang.student.service.IQuestionVideoRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 题目视频记录（题目视频观看记录）
 * 前端访问路由地址为:/student/questionVideoRecord
 *
 *
 * @date 2024-05-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/questionVideoRecord")
public class QuestionVideoRecordController extends BaseController {

    private final IQuestionVideoRecordService questionVideoRecordService;

    /**
     * 查询题目视频记录（题目视频观看记录）列表
     */
    @SaCheckPermission("student:questionVideoRecord:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionVideoRecordVo> list(QuestionVideoRecordBo bo, PageQuery pageQuery) {
        return questionVideoRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出题目视频记录（题目视频观看记录）列表
     */
    @SaCheckPermission("student:questionVideoRecord:export")
    @Log(title = "题目视频记录（题目视频观看记录）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionVideoRecordBo bo, HttpServletResponse response) {
        List<QuestionVideoRecordVo> list = questionVideoRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "题目视频记录（题目视频观看记录）", QuestionVideoRecordVo.class, response);
    }

    /**
     * 获取题目视频记录（题目视频观看记录）详细信息
     *
     * @param questionVideoRecordId 主键
     */
    @SaCheckPermission("student:questionVideoRecord:query")
    @GetMapping("/{questionVideoRecordId}")
    public R<QuestionVideoRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long questionVideoRecordId) {
        return R.ok(questionVideoRecordService.queryById(questionVideoRecordId));
    }

    /**
     * 新增题目视频记录（题目视频观看记录）
     */
    @SaCheckPermission("student:questionVideoRecord:add")
    @Log(title = "题目视频记录（题目视频观看记录）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionVideoRecordBo bo) {
        return toAjax(questionVideoRecordService.insertByBo(bo));
    }

    /**
     * 修改题目视频记录（题目视频观看记录）
     */
    @SaCheckPermission("student:questionVideoRecord:edit")
    @Log(title = "题目视频记录（题目视频观看记录）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionVideoRecordBo bo) {
        return toAjax(questionVideoRecordService.updateByBo(bo));
    }

    /**
     * 删除题目视频记录（题目视频观看记录）
     *
     * @param questionVideoRecordIds 主键串
     */
    @SaCheckPermission("student:questionVideoRecord:remove")
    @Log(title = "题目视频记录（题目视频观看记录）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionVideoRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] questionVideoRecordIds) {
        return toAjax(questionVideoRecordService.deleteWithValidByIds(List.of(questionVideoRecordIds), true));
    }
}
