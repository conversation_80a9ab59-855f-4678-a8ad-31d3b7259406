package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AiWrongQuestionRecord;
import com.jxw.shufang.student.domain.bo.AiWrongQuestionRecordBo;
import com.jxw.shufang.student.domain.vo.AiWrongQuestionRecordVo;
import com.jxw.shufang.student.mapper.AiWrongQuestionRecordMapper;
import com.jxw.shufang.student.service.IAiWrongQuestionRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Ai学习错题记录Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class AiWrongQuestionRecordServiceImpl implements IAiWrongQuestionRecordService, BaseService {

    private final AiWrongQuestionRecordMapper baseMapper;

    /**
     * 查询Ai学习错题记录
     */
    @Override
    public AiWrongQuestionRecordVo queryById(Long aiWrongQuestionRecordId){
        return baseMapper.selectVoById(aiWrongQuestionRecordId);
    }

    /**
     * 查询Ai学习错题记录列表
     */
    @Override
    public TableDataInfo<AiWrongQuestionRecordVo> queryPageList(AiWrongQuestionRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiWrongQuestionRecord> lqw = buildQueryWrapper(bo);
        Page<AiWrongQuestionRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询Ai学习错题记录列表
     */
    @Override
    public List<AiWrongQuestionRecordVo> queryList(AiWrongQuestionRecordBo bo) {
        LambdaQueryWrapper<AiWrongQuestionRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiWrongQuestionRecord> buildQueryWrapper(AiWrongQuestionRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiWrongQuestionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AiWrongQuestionRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getCourseId() != null, AiWrongQuestionRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getQuestionId() != null, AiWrongQuestionRecord::getQuestionId, bo.getQuestionId());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionNo()), AiWrongQuestionRecord::getQuestionNo, bo.getQuestionNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), AiWrongQuestionRecord::getSourceType, bo.getSourceType());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerResult()), AiWrongQuestionRecord::getAnswerResult, bo.getAnswerResult());
        return lqw;
    }

    /**
     * 新增Ai学习错题记录
     */
    @Override
    public Boolean insertByBo(AiWrongQuestionRecordBo bo) {
        AiWrongQuestionRecord add = MapstructUtils.convert(bo, AiWrongQuestionRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiWrongQuestionRecordId(add.getAiWrongQuestionRecordId());
        }
        return flag;
    }

    /**
     * 修改Ai学习错题记录
     */
    @Override
    public Boolean updateByBo(AiWrongQuestionRecordBo bo) {
        AiWrongQuestionRecord update = MapstructUtils.convert(bo, AiWrongQuestionRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiWrongQuestionRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除Ai学习错题记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean insertBatchByBo(List<AiWrongQuestionRecordBo> wrongList) {
        List<AiWrongQuestionRecord> convert = MapstructUtils.convert(wrongList, AiWrongQuestionRecord.class);
        return baseMapper.insertBatch(convert);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
