package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;


/**
 * 反馈记录剔除学习规划关联表对象 feedback_record_exclusion
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("feedback_record_exclusion")
public class FeedbackRecordExclusion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "feedback_exclusion_id") // MyBatis-Plus 主键注解
    private Long feedbackExclusionId;

    /**
     * 关联的反馈记录id
     */
    private Long feedbackRecordId;

    /**
     * 需要剔除的学习规划id
     */
    private Long studyPlanningRecordId;

}
