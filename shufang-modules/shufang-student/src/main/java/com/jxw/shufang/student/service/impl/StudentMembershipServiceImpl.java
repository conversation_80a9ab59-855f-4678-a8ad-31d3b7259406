package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.order.api.RemoteStudentMembershipService;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.mapper.StudentMapper;
import com.jxw.shufang.student.service.IStudentMembershipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学生会员卡服务实现
 * 通过 Dubbo 调用 order 模块的会员卡服务
 *
 * @date 2024-06-14
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StudentMembershipServiceImpl implements IStudentMembershipService {

    private final StudentMapper studentMapper;

    @DubboReference
    private RemoteStudentMembershipService remoteStudentMembershipService;

    /**
     * 查询指定时间段内的在籍会员
     * 根据student_membership_card表中的会员卡有效期判断是否在籍
     */
    @Override
    public List<StudentVo> getEnrolledStudentsInPeriod(Date startDate, Date endDate) {
        //需要查询出student_membership_card表中的所有在籍会员有效期以及ID
        //然后根据有效期判断是否在当前模式的时间段内
        //如果在，则生成待规划学生记录
        //春秋模式：当前周六日的在籍会员  只会生成一条
        //寒暑模式：下周一至周五的在籍会员  会生成多条，每天一条
        
        try {
            // 1. 通过 Dubbo 调用 order 模块查询有效的会员卡记录
            List<RemoteStudentMembershipCardVo> validMembershipCards = 
                remoteStudentMembershipService.getEnrolledStudentsInPeriod(startDate, endDate);
            
            if (CollUtil.isEmpty(validMembershipCards)) {
                log.info("查询时间段内没有有效的会员卡记录，开始时间：{}，结束时间：{}", 
                    DateUtil.formatDateTime(startDate), DateUtil.formatDateTime(endDate));
                return List.of();
            }
            
            // 2. 提取学生ID列表
            List<Long> studentIds = validMembershipCards.stream()
                .map(RemoteStudentMembershipCardVo::getStudentId)
                .distinct()
                .collect(Collectors.toList());
            
            // 3. 查询学生基本信息
            LambdaQueryWrapper<Student> studentWrapper = Wrappers.lambdaQuery();
            studentWrapper.in(Student::getStudentId, studentIds);
            studentWrapper.orderByDesc(Student::getCreateTime);
            
            List<Student> students = studentMapper.selectList(studentWrapper);
            
            if (CollUtil.isEmpty(students)) {
                log.info("查询到的学生信息为空");
                return List.of();
            }
            
            // 4. 创建学生ID到会员卡信息的映射
            Map<Long, RemoteStudentMembershipCardVo> membershipCardMap = validMembershipCards.stream()
                .collect(Collectors.toMap(
                    RemoteStudentMembershipCardVo::getStudentId,
                    card -> card,
                    (existing, replacement) -> {
                        // 如果有多张卡，选择结束时间最晚的
                        return existing.getProductEndDate().after(replacement.getProductEndDate()) ? existing : replacement;
                    }
                ));
            
            // 5. 转换为StudentVo并设置会员卡信息
            List<StudentVo> result = students.stream()
                .map(student -> {
                    StudentVo vo = convertToVo(student);
                    RemoteStudentMembershipCardVo membershipCard = membershipCardMap.get(student.getStudentId());
                    if (membershipCard != null) {
                        vo.setMembershipBeginDate(membershipCard.getProductBeginDate());
                        vo.setMembershipEndDate(membershipCard.getProductEndDate());
                    }
                    return vo;
                })
                .collect(Collectors.toList());
            
            log.info("查询到{}个在籍会员，开始时间：{}，结束时间：{}", 
                result.size(), DateUtil.formatDateTime(startDate), DateUtil.formatDateTime(endDate));
            
            return result;
            
        } catch (Exception e) {
            log.error("查询在籍会员失败", e);
            return List.of();
        }
    }

    /**
     * 查询所有在籍会员
     * 会员卡有效期大于当前时间的会员
     */
    @Override
    public List<StudentVo> getAllEnrolledStudents() {
        return getEnrolledStudentsInPeriod(null, null);
    }

    /**
     * 检查学生是否在籍
     */
    @Override
    public Boolean isStudentEnrolled(Long studentId) {
        if (studentId == null) {
            return false;
        }
        
        try {
            // 通过 Dubbo 调用远程服务检查学生是否有有效的会员卡
            return remoteStudentMembershipService.isStudentEnrolled(studentId);
        } catch (Exception e) {
            log.error("检查学生{}是否在籍失败", studentId, e);
            return false;
        }
    }

    /**
     * 检查学生在指定时间段内是否在籍
     */
    @Override
    public Boolean isStudentEnrolledInPeriod(Long studentId, Date startDate, Date endDate) {
        if (studentId == null) {
            return false;
        }
        
        try {
            // 通过 Dubbo 调用远程服务检查学生在指定时间段内是否有有效的会员卡
            return remoteStudentMembershipService.isStudentEnrolledInPeriod(studentId, startDate, endDate);
        } catch (Exception e) {
            log.error("检查学生{}在时间段内是否在籍失败", studentId, e);
            return false;
        }
    }

    /**
     * 获取学生的会员卡过期时间
     */
    @Override
    public Date getStudentExpireTime(Long studentId) {
        if (studentId == null) {
            return null;
        }
        
        try {
            // 通过 Dubbo 调用远程服务获取学生的会员卡过期时间
            return remoteStudentMembershipService.getStudentExpireTime(studentId);
        } catch (Exception e) {
            log.error("获取学生{}的会员卡过期时间失败", studentId, e);
            return null;
        }
    }

    /**
     * 转换Student实体为StudentVo
     */
    private StudentVo convertToVo(Student student) {
        StudentVo vo = new StudentVo();
        
        vo.setStudentId(student.getStudentId());
        vo.setBranchId(student.getBranchId());
        vo.setStudentAccount(student.getStudentAccount());
        vo.setStudentName(student.getStudentName());
        vo.setStudentSex(student.getStudentSex());
        vo.setStudentGrade(student.getStudentGrade());
        vo.setStudentSource(student.getStudentSource());
        vo.setStudentParentPhone(student.getStudentParentPhone());
        vo.setStudentBackupPhone(student.getStudentBackupPhone());
        vo.setStudentConsultantRecordId(student.getStudentConsultantRecordId());
        vo.setStudentParentRecordId(student.getStudentParentRecordId());
        vo.setStudentAiCourseRecordId(student.getStudentAiCourseRecordId());
        vo.setStudentRemark(student.getStudentRemark());
        vo.setLastLoginTime(student.getLastLoginTime());
        vo.setExpireTime(student.getExpireTime());
        vo.setPreferentialAmount(student.getPreferentialAmount());
        vo.setPreferentialAmountVersion(student.getPreferentialAmountVersion());
        vo.setCreateTime(student.getCreateTime());
        vo.setUpdateTime(student.getUpdateTime());
        
        // 生成姓名(电话后四位)
        if (student.getStudentName() != null && student.getStudentAccount() != null && student.getStudentAccount().length() >= 4) {
            String lastFour = student.getStudentAccount().substring(student.getStudentAccount().length() - 4);
            vo.setNameWithPhone(student.getStudentName() + "(" + lastFour + ")");
        } else {
            vo.setNameWithPhone(student.getStudentName());
        }
        
        return vo;
    }
}
