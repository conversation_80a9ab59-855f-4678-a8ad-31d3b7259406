package com.jxw.shufang.student.service;

import com.jxw.shufang.student.domain.vo.StudentVo;

import java.util.Date;
import java.util.List;

/**
 * 学生会员卡服务接口
 * 通过 Dubbo 调用 order 模块的会员卡服务
 *
 * @date 2024-06-14
 */
public interface IStudentMembershipService {

    /**
     * 查询指定时间段内的在籍会员
     * 根据会员卡有效期判断是否在籍
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 在籍会员列表
     */
    List<StudentVo> getEnrolledStudentsInPeriod(Date startDate, Date endDate);

    /**
     * 查询所有在籍会员
     * 会员卡有效期大于当前时间的会员
     * 
     * @return 在籍会员列表
     */
    List<StudentVo> getAllEnrolledStudents();

    /**
     * 检查学生是否在籍
     * 
     * @param studentId 学生ID
     * @return 是否在籍
     */
    Boolean isStudentEnrolled(Long studentId);

    /**
     * 检查学生在指定时间段内是否在籍
     * 
     * @param studentId 学生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 是否在籍
     */
    Boolean isStudentEnrolledInPeriod(Long studentId, Date startDate, Date endDate);

    /**
     * 获取学生的会员卡过期时间
     * 
     * @param studentId 学生ID
     * @return 过期时间
     */
    Date getStudentExpireTime(Long studentId);
}
