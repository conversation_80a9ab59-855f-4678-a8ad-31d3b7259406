package com.jxw.shufang.student.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityBo;
import com.jxw.shufang.student.domain.bo.FeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 会员每日打卡登录情况表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04 11:57:25
 */
public interface AttendanceDailyActivityService extends IService<AttendanceDailyActivity> {

    void saveAttendanceDailyActivity(AttendanceDailyActivityBo bo);

    /**
     * 批量保存会员每日打卡登录情况
     * @return 批量保存结果统计信息
     */
    void saveBatchAttendanceDailyActivity(List<AttendanceDailyActivityBo> boList);

    TableDataInfo<AttendanceDailyActivityVo> queryNotFeedbackStudentList(AttendanceDailyActivityBo bo, PageQuery pageQuery);


    /**
     * 根据学生id和日期 查询学生是否有打卡或者登录设备
     * @param studentId 学生ID
     * @param feedbackStartDate 反馈日期
     * @return 会员每日打卡登录记录
     */
    AttendanceDailyActivityVo queryAttendanceDailyActivityByStudentIdAndDate(Long studentId, Date feedbackStartDate);

    List<AttendanceDailyActivityVo> queryAttendanceDailyActivitiesByStudentIdsAndDateList(Set<Long> studentId, Set<Date> feedbackStartDateList);

    /**
     * 根据反馈状态查询会员每日打卡登录情况列表
     * @param feedbackStatus 反馈状态
     * @return 不同反馈状态的会员每日打卡登录情况列表
     */
    List<AttendanceDailyActivityVo> queryListByFeedbackStatus(Integer feedbackStatus);

    /**
     * 批量保存结果类
     */
    class BatchSaveResult {
        private int totalCount;      // 总记录数
        private int successCount;    // 成功保存数
        private int skipCount;       // 跳过数（重复记录）
        private int failCount;       // 失败数
        private String description;  // 操作描述
        private List<String> errorMessages; // 错误信息列表

        public BatchSaveResult() {
            this.errorMessages = new java.util.ArrayList<>();
        }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getSkipCount() { return skipCount; }
        public void setSkipCount(int skipCount) { this.skipCount = skipCount; }

        public int getFailCount() { return failCount; }
        public void setFailCount(int failCount) { this.failCount = failCount; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }

        public void addErrorMessage(String message) { this.errorMessages.add(message); }
    }
}
