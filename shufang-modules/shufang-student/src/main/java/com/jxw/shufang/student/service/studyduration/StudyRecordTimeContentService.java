package com.jxw.shufang.student.service.studyduration;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.domain.bo.ListVideoBO;
import com.jxw.shufang.extresource.api.domain.vo.VideoDTO;
import com.jxw.shufang.student.domain.*;
import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.AiStudyDurationProcessingContextDTO;
import com.jxw.shufang.student.domain.dto.BatchQueryQuestionRecordDTO;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.*;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/8 1:49
 * @Version 1
 * @Description 学习时长记录
 */
@Service
public class StudyRecordTimeContentService {
    @Resource
    private IStudyRecordService studyRecordService;
    @Resource
    private IQuestionVideoRecordService questionVideoRecordService;
    @Resource
    private IStudentService studentService;
    @Resource
    private ICourseService iCourseService;
    @Resource
    private IAiStudyRecordService aiStudyRecordService;
    @Resource
    private IAiStudyVideoRecordService aiStudyVideoRecordService;
    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;
    @DubboReference
    private RemoteCdsCommonService remoteCdsCommonService;

    /**
     * AI伴学学习时长上下文数据
     *
     * @param records
     * @param studyModuleTypeEnum
     * @return
     */
    public AiStudyDurationProcessingContextDTO aiStudyContext(List<AiStudyVideoRecordBo> records,
                                                              StudyModuleAndGroupEnum studyModuleTypeEnum) {
        List<Long> studentIds = records.stream().map(AiStudyVideoRecordBo::getStudentId).toList();
        List<Long> courseIds = records.stream().map(AiStudyVideoRecordBo::getCourseId).toList();
        List<Long> videoIds = records.stream().map(AiStudyVideoRecordBo::getVideoId).filter(Objects::nonNull).collect(Collectors.toList());

        AiStudyDurationProcessingContextDTO contextData = new AiStudyDurationProcessingContextDTO();
        contextData.setExistAiVideoRecord(this.getAiVideoRecord(studentIds, courseIds, studyModuleTypeEnum.getModuleEnum()));
        contextData.setExistAiStudyRecord(this.batchQueryByStudentIdAndTestCourseId(studentIds, courseIds));
        contextData.setStudentMap(this.studentMap(studentIds));
        contextData.setStudyModuleTypeEnum(studyModuleTypeEnum);
        contextData.setCourseDurationMap(this.courseDurationMap(videoIds));
        return contextData;
    }

    /**
     * 学生信息
     *
     * @param studentIds
     * @return
     */
    public Map<Long, Student> studentMap(List<Long> studentIds) {
        return studentService.batchQueryMapStudents(studentIds);
    }

    /**
     * 学习记录
     *
     * @param studyPlanningRecordIds
     * @return
     */
    public Map<Long, StudyRecord> studyRecordMap(List<Long> studyPlanningRecordIds) {
        return studyRecordService.batchQueryMapByPlanningIds(studyPlanningRecordIds);
    }

    /**
     * AI伴学学习记录
     *
     * @param studentIds
     * @param testPaperId
     * @return
     */
    public List<AiStudyRecord> getAiStudyRecord(List<Long> studentIds, List<Long> testPaperId) {
        return aiStudyRecordService.batchQueryByStudentIdAndTestPaperId(studentIds, testPaperId);
    }

    /**
     * 通过学生ID和课程ID批量查询aI伴学学习记录
     *
     * @param studentIds
     * @param courseIds
     * @return
     */
    public List<AiStudyRecord> batchQueryByStudentIdAndTestCourseId(List<Long> studentIds, List<Long> courseIds) {
        return aiStudyRecordService.batchQueryByStudentIdAndTestCourseId(studentIds, courseIds);
    }

    /**
     * 通过学生ID和类型和视频ID获取AI伴学学习记录
     *
     * @param studentIds
     * @param moduleTypeEnum
     * @param videoIds
     * @return
     */
    public List<AiStudyVideoRecord> getAiVideoRecord(List<Long> studentIds, StudyModuleTypeEnum moduleTypeEnum, List<Long> videoIds) {
        return aiStudyVideoRecordService.batchQueryByStudentIdAndVideoId(studentIds, moduleTypeEnum, videoIds);
    }

    /**
     * 通过学生ID和课程ID批量查询aI伴学学习记录
     *
     * @param studentIds
     * @param courseIds
     * @param moduleTypeEnum
     * @return
     */
    public List<AiStudyVideoRecord> getAiVideoRecord(List<Long> studentIds, List<Long> courseIds, StudyModuleTypeEnum moduleTypeEnum) {
        return aiStudyVideoRecordService.batchQueryByStudentIdAndCouredId(studentIds, courseIds, moduleTypeEnum);
    }

    /**
     * 查询题目视频记录记录
     *
     * @param recordList
     * @return
     */
    public Map<Long, List<QuestionVideoRecord>> studentQuestionVideoRecordMap(List<BatchQueryQuestionRecordDTO> recordList) {
        List<QuestionVideoRecord> questionVideoRecords = questionVideoRecordService.batchQuerySameStudentRecord(recordList);
        return questionVideoRecords.stream()
            .collect(Collectors.groupingBy(QuestionVideoRecord::getStudentId));
    }

    /**
     * 课程时长
     *
     * @param videoIds
     * @return
     */
    public Map<Long, Long> courseDurationMap(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return new HashMap<>();
        }
        ListVideoBO videoRequest = new ListVideoBO();
        videoRequest.setFileIds(new HashSet<>(videoIds));
        List<VideoDTO> videoList = remoteCdsCommonService.listVideosV2(videoRequest);
        return videoList.stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(VideoDTO::getId, VideoDTO::getDuration, (v1, v2) -> v1));
    }
}
