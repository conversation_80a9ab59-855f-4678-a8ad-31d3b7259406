package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.SourceEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudyPlanningTemplate;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningTemplateInfoBo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo;
import com.jxw.shufang.student.mapper.StudyPlanningTemplateMapper;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.student.service.IStudyPlanningTemplateInfoService;
import com.jxw.shufang.student.service.IStudyPlanningTemplateService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 学习规划模板Service业务层处理
 *
 *
 * @date 2024-06-14
 */
@RequiredArgsConstructor
@Service
public class StudyPlanningTemplateServiceImpl implements IStudyPlanningTemplateService, BaseService {

    private final StudyPlanningTemplateMapper baseMapper;

    private final ICourseService courseService;

    private final IStudyPlanningTemplateInfoService studyPlanningTemplateInfoService;

    @DubboReference
    private RemoteBranchService remoteBranchService;


    @Override
    public StudyPlanningTemplateVo queryById(Long studyPlanningTemplateId, Boolean withTemplateInfo) {
        return baseMapper.selectByTemplateId(studyPlanningTemplateId, withTemplateInfo);
    }

    /**
     * 查询学习规划模板列表
     */
    @Override
    public TableDataInfo<StudyPlanningTemplateVo> queryPageList(StudyPlanningTemplateBo bo, PageQuery pageQuery) {
        QueryWrapper<StudyPlanningTemplate> lqw = buildQueryWrapper(bo);
        Page<StudyPlanningTemplateVo> result = baseMapper.selectTemplatePage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void putBranchInfo(List<StudyPlanningTemplateVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> branchIds = records.stream().filter(e -> SourceEnum.BRANCH.toString().equals(e.getSourceType())).map(StudyPlanningTemplateVo::getBranchId).distinct().toList();
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(branchIds);
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo);
        Map<Long, RemoteBranchVo> map = StreamUtils.toIdentityMap(remoteBranchVos, RemoteBranchVo::getBranchId);

        for (StudyPlanningTemplateVo record : records) {
            if (SourceEnum.BRANCH.toString().equals(record.getSourceType())) {
                record.setBranch(map.get(record.getBranchId()));
            }
        }
    }

    /**
     * 查询学习规划模板列表
     */
    @Override
    public List<StudyPlanningTemplateVo> queryList(StudyPlanningTemplateBo bo) {
        LambdaQueryWrapper<StudyPlanningTemplate> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudyPlanningTemplate> buildLambdaQueryWrapper(StudyPlanningTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyPlanningTemplate> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTemplateName()), StudyPlanningTemplate::getTemplateName, bo.getTemplateName());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateInfo()), StudyPlanningTemplate::getTemplateInfo, bo.getTemplateInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateDesc()), StudyPlanningTemplate::getTemplateDesc, bo.getTemplateDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseStage()), StudyPlanningTemplate::getCourseStage, bo.getCourseStage());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseGrades()), StudyPlanningTemplate::getCourseGrades, bo.getCourseGrades());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateStatus()), StudyPlanningTemplate::getTemplateStatus, bo.getTemplateStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), StudyPlanningTemplate::getSourceType, bo.getSourceType());
        lqw.eq(bo.getBranchId() != null, StudyPlanningTemplate::getBranchId, bo.getBranchId());

        if (bo.getBranchId() == null) {
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                lqw.and(i -> i
                    .eq(StudyPlanningTemplate::getSourceType, SourceEnum.MANAGEMENT.toString())
                    .or()
                    .in(StudyPlanningTemplate::getBranchId, LoginHelper.getBranchIdList()));
            }else if (LoginHelper.getBranchId() != null){
                lqw.and(i -> i
                    .eq(StudyPlanningTemplate::getSourceType, SourceEnum.MANAGEMENT.toString())
                    .or()
                    .eq(StudyPlanningTemplate::getBranchId, LoginHelper.getBranchId()));
            }
        }

        return lqw;
    }

    private QueryWrapper<StudyPlanningTemplate> buildQueryWrapper(StudyPlanningTemplateBo bo) {
        QueryWrapper<StudyPlanningTemplate> lqw = Wrappers.query();
        lqw.like(StringUtils.isNotBlank(bo.getTemplateName()), "t.template_name", bo.getTemplateName());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateInfo()), "t.template_info", bo.getTemplateInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateDesc()), "t.template_desc", bo.getTemplateDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseStage()), "t.course_stage", bo.getCourseStage());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseGrades()), "t.course_grades", bo.getCourseGrades());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateStatus()), "t.template_status", bo.getTemplateStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceType()), "t.source_type", bo.getSourceType());
        lqw.eq(bo.getBranchId() != null, "t.branch_id", bo.getBranchId());

        if (bo.getBranchId() == null) {
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                lqw.and(i -> i
                    .eq("t.source_type", SourceEnum.MANAGEMENT.toString())
                    .or()
                    .in("t.branch_id", LoginHelper.getBranchIdList()));
            }else if (LoginHelper.getBranchId() != null){
                lqw.and(i -> i
                    .eq("t.source_type", SourceEnum.MANAGEMENT.toString())
                    .or()
                    .eq("t.branch_id", LoginHelper.getBranchId()));
            }
        }

        return lqw;
    }

    /**
     * 新增学习规划模板
     */
    @Override
    public Boolean insertByBo(StudyPlanningTemplateBo bo) {
        if (LoginHelper.getBranchId() != null) {
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setSourceType(SourceEnum.BRANCH.toString());
        } else {
            bo.setSourceType(SourceEnum.MANAGEMENT.toString());
        }
        StudyPlanningTemplate add = MapstructUtils.convert(bo, StudyPlanningTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            return false;
        }
        bo.setStudyPlanningTemplateId(add.getStudyPlanningTemplateId());
        List<StudyPlanningTemplateInfoBo> studyPlanningTemplateInfoBoList = bo.getStudyPlanningTemplateInfoBoList();
        if (CollUtil.isEmpty(studyPlanningTemplateInfoBoList)) {
            throw new ServiceException("必须选择课程");
        }
        for (StudyPlanningTemplateInfoBo item : studyPlanningTemplateInfoBoList) {
            item.setStudyPlanningTemplateId(bo.getStudyPlanningTemplateId());
        }
        //获取章节信息
        List<Long> list = studyPlanningTemplateInfoBoList.stream().map(StudyPlanningTemplateInfoBo::getCourseId).distinct().toList();
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(list);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            throw new ServiceException("找不到对应的课程信息");
        }
        Map<Long, String> map = StreamUtils.toMap(courseVos, CourseVo::getCourseId, CourseVo::getAncestors);


        for (StudyPlanningTemplateInfoBo item : studyPlanningTemplateInfoBoList) {
            String ancestors = map.get(item.getCourseId());
            if (StringUtils.isBlank(ancestors)) {
                throw new ServiceException("找不到对应的课程信息");
            }
            String[] split = ancestors.split(",");
            Long topmostCourseId = Convert.toLong(split[1]);
            item.setTopmostCourseId(topmostCourseId);
        }
        boolean flag1 = studyPlanningTemplateInfoService.insertBatchByBo(studyPlanningTemplateInfoBoList);
        if (!flag1) {
            throw new ServiceException("保存学习规划模板信息失败");
        }
        return true;
    }

    /**
     * 修改学习规划模板
     */
    @Override
    public Boolean updateByBo(StudyPlanningTemplateBo bo) {
        StudyPlanningTemplate update = MapstructUtils.convert(bo, StudyPlanningTemplate.class);
        validEntityBeforeSave(update);
        boolean b = baseMapper.updateById(update) > 0;
        if (!b) {
            return false;
        }
        List<StudyPlanningTemplateInfoBo> studyPlanningTemplateInfoBoList = bo.getStudyPlanningTemplateInfoBoList();
        if (CollUtil.isEmpty(studyPlanningTemplateInfoBoList)) {
            throw new ServiceException("必须选择课程");
        }
        //获取章节信息
        List<Long> list = studyPlanningTemplateInfoBoList.stream().map(StudyPlanningTemplateInfoBo::getCourseId).distinct().toList();
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(list);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            throw new ServiceException("找不到对应的课程信息");
        }
        Map<Long, String> map = StreamUtils.toMap(courseVos, CourseVo::getCourseId, CourseVo::getAncestors);


        for (StudyPlanningTemplateInfoBo item : studyPlanningTemplateInfoBoList) {
            item.setStudyPlanningTemplateId(bo.getStudyPlanningTemplateId());
            String ancestors = map.get(item.getCourseId());
            if (StringUtils.isBlank(ancestors)) {
                throw new ServiceException("找不到对应的课程信息");
            }
            String[] split = ancestors.split(",");
            Long topmostCourseId = Convert.toLong(split[1]);
            item.setTopmostCourseId(topmostCourseId);
        }

        //删除原本的
        boolean b1 = studyPlanningTemplateInfoService.deleteByStudyPlanningTemplateId(bo.getStudyPlanningTemplateId());
        if (!b1) {
            throw new ServiceException("删除旧的学习规划模板信息失败");
        }

        boolean flag1 = studyPlanningTemplateInfoService.insertBatchByBo(studyPlanningTemplateInfoBoList);
        if (!flag1) {
            throw new ServiceException("保存学习规划模板信息失败");
        }
        return true;

    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyPlanningTemplate entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学习规划模板
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean changeStatus(StudyPlanningTemplateBo bo) {
        if (bo.getStudyPlanningTemplateId() == null){
            throw new ServiceException("学习规划模板id不能为空");
        }
        if (StringUtils.isBlank(bo.getTemplateStatus())) {
            throw new ServiceException("学习规划模板状态不能为空");
        }
        StudyPlanningTemplate convert = MapstructUtils.convert(bo, StudyPlanningTemplate.class);
        return baseMapper.updateById(convert) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
