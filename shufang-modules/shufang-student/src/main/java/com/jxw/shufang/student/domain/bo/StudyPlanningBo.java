package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanning;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 学习规划业务对象 study_planning
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanning.class, reverseConvertGenerate = false)
public class StudyPlanningBo extends BaseEntity {

    /**
     * 学习规划id
     */
    @NotNull(message = "学习规划id不能为空", groups = { EditGroup.class })
    private Long studyPlanningId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 学习规划日期
     */
    @NotNull(message = "学习规划日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDate;

    /**
     * 学习规划保存状态（1暂存 2完成）
     */
    @NotBlank(message = "学习规划保存状态（1暂存 2完成）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyPlanningStatus;

    /**
     * 学习状态（0未开始 1进行中 2已完成）
     */
    private String studyStatus;

    /**
     * 学习记录状态（0正常 1覆盖 2删除 ）
     */
    private String studyRecordStatus;

    /**
     * 学习规划开始时间--范围查询,需配合studyPlanningDateEnd使用
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDateStart;

    /**
     * 学习规划结束时间--范围查询,需配合studyPlanningDateStart使用
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDateEnd;

    /**
     * 学科
     */
    private String affiliationSubject;

    private List<Long> studentIdList;


    /**
     * 学习记录列表
     */
    private List<StudyPlanningRecordBo> studyPlanningRecordList;

    /**
     * 是否包含学生信息
     */
    private Boolean withStudentInfo;

    /**
     * 是否包含会员系统用户表信息（仅在withStudentInfo=true时有效）
     */
    private Boolean withStudentSysUserInfo;

    /**
     * 是否包含学习规划详情
     */
    private Boolean withStudyPlanningRecord;

    /**
     * 是否包含课次信息(仅在withStudyPlanningRecord=true时有效)
     */
    private Boolean withCourseInfo;


    /**
     * 是否包含顶级课程信息(仅在withStudyPlanningRecord=true和withCourseInfo=true时有效)
     */
    private Boolean withTopCourseInfo;

    /**
     * 是否包含学习记录信息
     */
    private Boolean withStudyRecordInfo;

    /**
     * 是否包含学习反馈信息
     */
    private Boolean withFeedbackRecordInfo;

    /**
     * 会员顾问Id
     */
    private Long consultantId;

    /**
     * 会员名称（带手机号后四位）
     */
    private String nameWithPhone;

    /**
     *  学习规划日期(非数据库字段，仅用于查询条件)（这个只包含年月）yyyy-MM
     */
    private String studyPlanningDateYearMonth;

    private List<Long> notInStudyPlanningRecordIdList;


    private Boolean dontHandleQueryParam;

    /**
     * 小于等于学习规划日期 yyyy-MM-dd
     */
    private String leStudyPlanningDate;
    /**
     * 大于等于学习规划日期 yyyy-MM-dd
     */
    private String qeStudyPlanningDate;

    /**
     * 是否带测试批改记录信息
     */
    private Boolean withTestCorrectionRecord;

    /**
     * 是否携带练习批改记录信息
     */
    private Boolean withPracticeCorrectionRecord;
}
