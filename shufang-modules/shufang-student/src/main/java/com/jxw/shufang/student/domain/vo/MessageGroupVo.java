package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
@Data
public class MessageGroupVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息发送者类型 1 员工（或门店管理员） 2会员
     */
    private String sendUserType;

    /**
     * 显示的名字
     */
    private String userShowName;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型 1文本 2图片
     */
    private String contentType;

    private Long messageId;

    private String readStatus;

}
