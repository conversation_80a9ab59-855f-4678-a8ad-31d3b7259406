package com.jxw.shufang.student.controller.android;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.AnswerProcessVo;
import com.jxw.shufang.student.domain.vo.LearnedCoursesRatesVo;
import com.jxw.shufang.student.domain.vo.StudyProcessVo;
import com.jxw.shufang.student.domain.vo.StudyTimeRankVo;
import com.jxw.shufang.student.service.IStudentDataStatisticsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据统计-平板端
 * 前端访问路由地址为:/student/android/studentDataStatistics
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/studentDataStatistics")
public class AStudentDataStatisticsController extends BaseController {

    private final IStudentDataStatisticsService studentDataStatisticsService;

    /**
     * 获取学习情况
     */
    @GetMapping("/getStudyProcess")
    public R<StudyProcessVo> getStudyProcess() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentDataStatisticsService.getStudyProcess(LoginHelper.getStudentId()));
    }

    /**
     * 获取答题情况
     */
    @GetMapping("/getAnswerProcess")
    public R<AnswerProcessVo> getAnswerProcess() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentDataStatisticsService.getAnswerProcess(LoginHelper.getStudentId()));
    }

    /**
     * 获取已学课程分布
     */
    @GetMapping("/getLearnedCoursesRates")
    public R<LearnedCoursesRatesVo> getLearnedCoursesRates() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentDataStatisticsService.getLearnedCoursesRates(LoginHelper.getStudentId()));
    }

    /**
     * 获取学习时长排行榜
     * @param durType   时间类型 可选：今日D， 本周W， 本月M
     * @param self  是否查询自己 可选：是：1, 否：0
     * @return
     */
    @GetMapping("/getStudyTimeRankList")
    public R<StudyTimeRankVo> getStudyTimeRankList(@NotBlank(message = "时间类型不能为空") String durType, @NotNull(message = "是否查询自己不能为空") Integer self, PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentDataStatisticsService.getStudyTimeRankListCache(durType, self,pageQuery));
    }
}
