package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.student.domain.CourseGrade;
import com.jxw.shufang.student.domain.vo.CourseGradeVo;
import com.jxw.shufang.student.mapper.CourseGradeMapper;
import com.jxw.shufang.student.service.ICourseGradeService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: cyj
 * @date: 2025/3/28
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class CourseGradeServiceImpl implements ICourseGradeService, BaseService {

    private final CourseGradeMapper baseMapper;

    @Override
    public boolean saveBatch(List<CourseGrade> courseGradeList) {
        return baseMapper.insertBatch(courseGradeList);
    }

    @Override
    public List<CourseGradeVo> listByCourseId(Long courseId) {
        LambdaQueryWrapper<CourseGrade> wrapper = Wrappers.lambdaQuery(CourseGrade.class);
        wrapper.eq(CourseGrade::getCourseId, courseId);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public Boolean updateCourseGrade(Course course, List<CourseGrade> courseGradeList) {
        List<CourseGradeVo> originCourseGrades = listByCourseId(course.getCourseId());
        Set<String> gradeSet = courseGradeList.stream().map(CourseGrade::getGrade).collect(Collectors.toSet());
        List<CourseGradeVo> removeList = new ArrayList<>();
        Set<String> existGradeSet = new HashSet<>();
        originCourseGrades.forEach(courseGradeVo -> {
            if (!gradeSet.contains(courseGradeVo.getGrade())) {
                removeList.add(courseGradeVo);
            } else {
                existGradeSet.add(courseGradeVo.getGrade());
            }
        });
        List<CourseGrade> addList = courseGradeList.stream()
            .filter(courseGrade -> !existGradeSet.contains(courseGrade.getGrade())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(addList)) {
            if (!baseMapper.insertBatch(addList)) {
                return false;
            }
        }
        if (CollectionUtil.isNotEmpty(removeList)) {
            if (baseMapper.deleteBatchIds(removeList.stream().map(CourseGradeVo::getCourseGradeId).toList()) <= 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<CourseGrade> listByCourseIds(List<Long> courseIds) {
        if (CollectionUtil.isEmpty(courseIds)) {
            return new ArrayList<>(1);
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(CourseGrade.class).in(CourseGrade::getCourseId, courseIds));
    }

}
