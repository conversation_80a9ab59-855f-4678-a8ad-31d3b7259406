package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordInfoVo;
import com.jxw.shufang.student.service.IAiCorrectionRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Ai批改记录详情---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/aiCorrectionRecordInfo
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/aiCorrectionRecordInfo")
public class MpAiCorrectionRecordInfoController extends BaseController {

    private final IAiCorrectionRecordInfoService aiCorrectionRecordInfoService;

    @GetMapping("/{aiCorrectionRecordId}")
    public R<List<AiCorrectionRecordInfoVo>> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long aiCorrectionRecordId) {
        AiCorrectionRecordInfoBo bo = new AiCorrectionRecordInfoBo();
        bo.setAiCorrectionRecordId(aiCorrectionRecordId);
        return R.ok(aiCorrectionRecordInfoService.queryList(bo));
    }
}
