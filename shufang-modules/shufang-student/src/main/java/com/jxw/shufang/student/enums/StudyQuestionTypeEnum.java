package com.jxw.shufang.student.enums;

/**
 * <AUTHOR>
 * @Date 2025/5/8 16:05
 * @Version 1
 * @Description
 */
public enum StudyQuestionTypeEnum {

    TEST("1", "测试"),
    PRACTICE("2", "练习");
    private String questionType;
    private String questionTypeName;

    StudyQuestionTypeEnum(String questionType, String questionTypeName) {
        this.questionType = questionType;
        this.questionTypeName = questionTypeName;
    }

    public String getQuestionType() {
        return questionType;
    }

    public void setQuestionType(String questionType) {
        this.questionType = questionType;
    }

    public String getQuestionTypeName() {
        return questionTypeName;
    }

    public void setQuestionTypeName(String questionTypeName) {
        this.questionTypeName = questionTypeName;
    }
}
