package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentAiCourseRecord;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordBo;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordInfoBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordVo;
import com.jxw.shufang.student.mapper.StudentAiCourseRecordMapper;
import com.jxw.shufang.student.service.IStudentAiCourseRecordInfoService;
import com.jxw.shufang.student.service.IStudentAiCourseRecordService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）Service业务层处理
 *
 *
 * @date 2024-05-23
 */
@RequiredArgsConstructor
@Service
public class StudentAiCourseRecordServiceImpl implements IStudentAiCourseRecordService, BaseService {

    private final StudentAiCourseRecordMapper baseMapper;

    private final IStudentService studentService;

    private final IStudentAiCourseRecordInfoService studentAiCourseRecordInfoService;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public StudentAiCourseRecordVo queryById(Long studentAiCourseRecordId){
        return baseMapper.selectVoById(studentAiCourseRecordId);
    }

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public TableDataInfo<StudentAiCourseRecordVo> queryPageList(StudentAiCourseRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentAiCourseRecord> lqw = buildQueryWrapper(bo);
        Page<StudentAiCourseRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public List<StudentAiCourseRecordVo> queryList(StudentAiCourseRecordBo bo) {
        LambdaQueryWrapper<StudentAiCourseRecord> lqw = buildQueryWrapper(bo);
        List<StudentAiCourseRecordVo> studentAiCourseRecordVoList = baseMapper.selectVoList(lqw);
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putSysUserInfo(studentAiCourseRecordVoList);
        }
        return studentAiCourseRecordVoList;
    }

    public void putSysUserInfo(List<StudentAiCourseRecordVo> studentAiCourseRecordVos) {
        if (CollUtil.isEmpty(studentAiCourseRecordVos)) {
            return;
        }
        List<Long> sysUserIdList = studentAiCourseRecordVos.stream().map(StudentAiCourseRecordVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentAiCourseRecordVos.forEach(vo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(vo.getCreateBy());
            vo.setSysUser(remoteUserVo);
        });
    }


    private LambdaQueryWrapper<StudentAiCourseRecord> buildQueryWrapper(StudentAiCourseRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentAiCourseRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentAiCourseRecord::getStudentId, bo.getStudentId());
        return lqw;
    }

    /**
     * 新增会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(StudentAiCourseRecordBo bo) {
        StudentAiCourseRecord add = MapstructUtils.convert(bo, StudentAiCourseRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentAiCourseRecordId(add.getStudentAiCourseRecordId());

            if(null != bo.getCourseIdList() && bo.getCourseIdList().size()>0){
                StudentBo studentBo = new StudentBo();
                studentBo.setStudentId(add.getStudentId());
                studentBo.setStudentAiCourseRecordId(add.getStudentAiCourseRecordId());
                studentService.updateStudentAiCourseRecordIdByBo(studentBo);

                bo.getCourseIdList().parallelStream().forEach(courseId->{
                    StudentAiCourseRecordInfoBo studentAiCourseRecordInfoBo = new StudentAiCourseRecordInfoBo();
                    studentAiCourseRecordInfoBo.setCourseId(courseId);
                    studentAiCourseRecordInfoBo.setStudentAiCourseRecordId(add.getStudentAiCourseRecordId());

                    studentAiCourseRecordInfoService.insertByBo(studentAiCourseRecordInfoBo);
                });
            }else {
                StudentBo studentBo = new StudentBo();
                studentBo.setStudentId(add.getStudentId());
                studentBo.setStudentAiCourseRecordId(null);
                studentService.updateStudentAiCourseRecordIdByBo(studentBo);

            }
        }
        return flag;
    }

    /**
     * 修改会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean updateByBo(StudentAiCourseRecordBo bo) {
        StudentAiCourseRecord update = MapstructUtils.convert(bo, StudentAiCourseRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentAiCourseRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
