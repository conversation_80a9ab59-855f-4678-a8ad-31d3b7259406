package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchMachineSeatService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.CorrectionStatusEnum;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyPlanning;
import com.jxw.shufang.student.domain.StudyPlanningRecord;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.enums.CourseLabelDictEnum;
import com.jxw.shufang.student.mapper.StudyPlanningMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 学习规划Service业务层处理
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudyPlanningServiceImpl implements IStudyPlanningService {

    private final StudyPlanningMapper baseMapper;

    private final IStudentService studentService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final ICourseService courseService;

    private final IStudyVideoRecordService studyVideoRecordService;

    private final IStudyRecordService studyRecordService;

    private final IFeedbackRecordService feedbackRecordService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final ICorrectionRecordService correctionRecordService;

    private final IStudyPlanningPendingService studyPlanningPendingService;

    @DubboReference
    private RemoteExtVideoService remoteExtVideoService;

    @DubboReference
    private RemoteExtResourceService remoteResourceService;

    @DubboReference
    private RemoteQuestionService remoteQuestionService;

    @DubboReference
    RemoteBranchMachineSeatService remoteBranchMachineSeatService;

    @DubboReference
    private RemoteUserService remoteUserService;


    //周日开始
    private final String[] WEEK_ARR = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    /**
     * 查询学习规划
     */
    @Override
    public StudyPlanningVo queryById(Long studyPlanningId) {
        return baseMapper.selectVoById(studyPlanningId);
    }

    /**
     * 查询学习规划列表
     */
    @Override
    public TableDataInfo<StudyPlanningVo> queryPageList(StudyPlanningBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);

        LambdaQueryWrapper<StudyPlanning> lqw = buildLambdaQueryWrapper(bo);
        Page<StudyPlanningVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<StudyPlanningVo> queryPlanAndRecordList(StudyPlanningBo studyPlanningBo) {
        handleQueryParam(studyPlanningBo);

        QueryWrapper<StudyPlanning> wrapper = buildQueryWrapper(studyPlanningBo);
        List<StudyPlanningVo> studyPlanningVos = baseMapper.queryPlanAndRecordList(wrapper);

        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudentInfo())) {
            putStudentInfo(studyPlanningVos);
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudentInfo())
            && Boolean.TRUE.equals(studyPlanningBo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(studyPlanningVos);
        }

        //这个不需要，sql查了
        //if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())) {
        //    putStudyPlanningRecordInfo(result.getRecords());
        //}
        if (Boolean.TRUE.equals(studyPlanningBo.getWithCourseInfo())) {
            putCourseInfo(studyPlanningVos);
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithCourseInfo())
            && Boolean.TRUE.equals(studyPlanningBo.getWithTopCourseInfo())) {
            putTopCourseInfo(studyPlanningVos);
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyRecordInfo())) {
            putStudyRecordInfo(studyPlanningVos);
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithFeedbackRecordInfo())) {
            putFeedbackRecordInfo(studyPlanningVos);
        }

        // 获取批改记录
        if (Boolean.TRUE.equals(studyPlanningBo.getWithTestCorrectionRecord()) || Boolean.TRUE.equals(studyPlanningBo.getWithPracticeCorrectionRecord())) {
            putCorrectionRecordInfo(studyPlanningVos, studyPlanningBo.getWithTestCorrectionRecord(), studyPlanningBo.getWithPracticeCorrectionRecord());
        }
        return studyPlanningVos;
    }


    private void putCorrectionRecordInfo(List<StudyPlanningVo> studyPlanningVos, Boolean withTestCorrectionRecord, Boolean withPracticeCorrectionRecord) {

        studyPlanningVos.parallelStream().forEach(studyPlanningVo -> {
            List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningVo.getStudyPlanningRecordList();
            List<Long> studyPlanningRecordIdList = studyPlanningRecordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).toList();

            CorrectionRecordBo correctionRecordBo = new CorrectionRecordBo();
            correctionRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
            List<String> correctionTypes = new ArrayList<>();
            if (Boolean.TRUE.equals(withTestCorrectionRecord)) {
                correctionTypes.add(UserConstants.CORRECTION_TYPE_TEST);
            }
            if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
                correctionTypes.add(UserConstants.CORRECTION_TYPE_PRACTICE);
            }
            if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
                correctionTypes.add(UserConstants.CORRECTION_TYPE_PREVIEW);
            }
            if (Boolean.TRUE.equals(withPracticeCorrectionRecord)) {
                correctionTypes.add(UserConstants.CORRECTION_TYPE_SPEAK);
            }
            if (correctionTypes.size() > 1) {
                correctionRecordBo.setCorrectionTypeList(correctionTypes);
            }
            // 查询批改记录,会携带正确题目数量，错误题目数量，半对错题目数量，未做题目数量
            List<CorrectionRecordVo> correctionRecordVos = correctionRecordService.queryRecordAndRightWrongInfo(correctionRecordBo);
            if (CollUtil.isEmpty(correctionRecordVos)) {
                return;
            }
            //按照study_planning_record_id分组
            Map<Long, List<CorrectionRecordVo>> correctionRecordVoMap = correctionRecordVos.stream()
                .collect(Collectors.groupingBy(CorrectionRecordVo::getStudyPlanningRecordId));

            studyPlanningRecordList.forEach(record -> {
                Long studyPlanningRecordId = record.getStudyPlanningRecordId();
                List<CorrectionRecordVo> correctionRecordVoList = correctionRecordVoMap.get(studyPlanningRecordId);
                if (CollUtil.isNotEmpty(correctionRecordVoList)) {
                    for (CorrectionRecordVo correctionRecordVo : correctionRecordVoList) {
                        switch (correctionRecordVo.getCorrectionType()) {
                            case UserConstants.CORRECTION_TYPE_PRACTICE:
                                record.setPracticeCorrectionRecord(correctionRecordVo);
                                break;
                            case UserConstants.CORRECTION_TYPE_TEST:
                                record.setTestCorrectionRecord(correctionRecordVo);
                                break;
                            case UserConstants.CORRECTION_TYPE_PREVIEW:
                                record.setPreviewCorrectionRecord(correctionRecordVo);
                                break;
                            case UserConstants.CORRECTION_TYPE_SPEAK:
                                record.setSpeakCorrectionRecord(correctionRecordVo);
                                break;
                            default:
                                log.warn("未知的 correctionType: {}", correctionRecordVo.getCorrectionType());
                                break;
                        }
                    }
                }
            });
        });
    }

    /**
     * 查询学习规划列表
     */
    @Override
    public List<StudyPlanningVo> queryList(StudyPlanningBo bo) {
        if (bo.getDontHandleQueryParam() == null || !bo.getDontHandleQueryParam()) {
            handleQueryParam(bo);
        }
        LambdaQueryWrapper<StudyPlanning> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private QueryWrapper<StudyPlanning> buildQueryWrapper(StudyPlanningBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<StudyPlanning> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getStudyPlanningDate() != null, "t.study_planning_date", bo.getStudyPlanningDate() != null ? DateUtils.dateTime(bo.getStudyPlanningDate()) : null);
        lqw.eq(StringUtils.isNotBlank(bo.getStudyStatus()), "spr.study_status", bo.getStudyStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyPlanningDateYearMonth()), "DATE_FORMAT(t.study_planning_date, '%Y-%m')", bo.getStudyPlanningDateYearMonth());
        if (bo.getStudyPlanningDateStart() != null && bo.getStudyPlanningDateEnd() != null) {
            lqw.apply("t.study_planning_date between {0} and {1}", DateUtils.dateTime(bo.getStudyPlanningDateStart()), DateUtils.dateTime(bo.getStudyPlanningDateEnd()));
        }
        if (StringUtils.isNotBlank(bo.getLeStudyPlanningDate())) {
            lqw.apply("date(t.study_planning_date) <= {0}", bo.getLeStudyPlanningDate());
        }
        if (StringUtils.isNotBlank(bo.getLeStudyPlanningDate())) {
            lqw.apply("date(t.study_planning_date) >= {0}", bo.getQeStudyPlanningDate());
        }
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudyPlanningRecordIdList()), "spr.study_planning_record_id", bo.getNotInStudyPlanningRecordIdList());
        if (StringUtils.isNotBlank(bo.getStudyRecordStatus())) {
            lqw.eq("spr.study_record_status", bo.getStudyRecordStatus());
        } else {
            lqw.eq("spr.study_record_status", UserConstants.STUDY_RECORD_STATUS_NORMAL);
        }
        if (StringUtils.isNotBlank(bo.getStudyPlanningStatus())) {
            lqw.eq("t.study_planning_status", bo.getStudyPlanningStatus());
        } else {
            lqw.eq("t.study_planning_status", UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        }
        lqw.orderByAsc(List.of("spr.study_start_time","spr.study_end_time"));
        if (bo.getConsultantId() != null || StringUtils.isNotBlank(bo.getNameWithPhone())) {
            StudentBo studentBo = new StudentBo();
            studentBo.setConsultantId(bo.getConsultantId());
            studentBo.setNameWithPhone(bo.getNameWithPhone());
            List<Long> studentIds = studentService.queryStudentIdList(studentBo);
            if (CollUtil.isNotEmpty(studentIds)) {
                lqw.in("t.student_id", studentIds);
            } else {
                lqw.in("t.student_id", -1);
            }
        }
        return lqw;
    }

    private LambdaQueryWrapper<StudyPlanning> buildLambdaQueryWrapper(StudyPlanningBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudyPlanning> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudyPlanning::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudyPlanningDate() != null, StudyPlanning::getStudyPlanningDate, bo.getStudyPlanningDate() != null ? DateUtils.dateTime(bo.getStudyPlanningDate()) : null);
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), StudyPlanning::getStudentId, bo.getStudentIdList());
        lqw.between(bo.getStudyPlanningDateStart() != null && bo.getStudyPlanningDateEnd() != null, StudyPlanning::getStudyPlanningDate, bo.getStudyPlanningDateStart(), bo.getStudyPlanningDateEnd());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyPlanningStatus()), StudyPlanning::getStudyPlanningStatus, bo.getStudyPlanningStatus());
        if (StringUtils.isBlank(bo.getStudyRecordStatus())) {
            lqw.eq(StudyPlanning::getStudyPlanningStatus, UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        }

        if (bo.getConsultantId() != null || StringUtils.isNotBlank(bo.getNameWithPhone())) {
            StudentBo studentBo = new StudentBo();
            studentBo.setConsultantId(bo.getConsultantId());
            studentBo.setNameWithPhone(bo.getNameWithPhone());
            List<Long> studentIds = studentService.queryStudentIdList(studentBo);
            if (CollUtil.isNotEmpty(studentIds)) {
                lqw.in(StudyPlanning::getStudentId, studentIds);
            } else {
                lqw.in(StudyPlanning::getStudentId, -1);
            }
        }

        return lqw;
    }

    /**
     * 新增学习规划
     */
    @Override
    public Boolean insertByBo(StudyPlanningBo bo) {
        StudyPlanning add = MapstructUtils.convert(bo, StudyPlanning.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudyPlanningId(add.getStudyPlanningId());
        }
        return flag;
    }

    /**
     * 修改学习规划
     */
    @Override
    public Boolean updateByBo(StudyPlanningBo bo) {
        StudyPlanning update = MapstructUtils.convert(bo, StudyPlanning.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudyPlanning entity) {
        //做一些数据校验,如唯一约束
    }


    @Override
    public List<StudyPlanningVo> getStudyPlanStudyStatus(StudyPlanningBo bo, Long studentId) {
        bo.setStudentId(studentId);
        List<StudyPlanningVo> list = queryPlanAndRecordList(bo);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        //过滤有规划，但是规划内容位空的数据
        list.removeIf(item -> CollUtil.isEmpty(item.getStudyPlanningRecordList()));

        //取出所有规划记录信息
        List<StudyPlanningRecordVo> recordList = list.stream().map(StudyPlanningVo::getStudyPlanningRecordList).flatMap(List::stream).toList();
        if (CollUtil.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        //获取课程信息
        studyPlanningRecordService.putCourseInfo(recordList);
        //屏蔽被删除课程
        recordList = recordList.stream().filter(item -> item.getCourse() != null).toList();
        if (CollUtil.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        //获取课程最顶级的课程名称
        List<CourseVo> courseVoList = recordList.stream().filter(item -> item.getCourse() != null).map(StudyPlanningRecordVo::getCourse).collect(Collectors.toList());
        courseService.putTopmostCourseInfo(courseVoList, false);

        // 设置课程路径
        Map<Long, List<Tree<Long>>> courseTreeMap = courseService.queryCourseTreePath(courseVoList);
        recordList.forEach(record -> {
            if (Objects.nonNull(record.getCourse())) {
                record.setCourseTreePath(courseTreeMap.get(record.getCourse().getCourseId()));
            }
        });

        //获取所有章节的知识点id关联的视频
        List<Long> knowledgeIdList = courseVoList.stream().filter(e -> e.getKnowledgeId() != null).map(CourseVo::getKnowledgeId).collect(Collectors.toList());

        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);

        //处理视频和学习规划记录的关系
        if (CollUtil.isNotEmpty(knowledgeVideoList)) {
            Map<Long, RemoteKnowledgeVideoVo> map = StreamUtils.toMap(knowledgeVideoList, RemoteKnowledgeVideoVo::getKnowledgeId, Function.identity());
            for (StudyPlanningRecordVo studyPlanningRecordVo : recordList) {
                CourseVo course = studyPlanningRecordVo.getCourse();
                if (ObjectUtils.isEmpty(course)){
                    continue;
                }
                Long knowledgeId = course.getKnowledgeId();
                if (ObjectUtils.isEmpty(knowledgeId)){
                    continue;
                }
                RemoteKnowledgeVideoVo remoteKnowledgeVideoVo = map.get(knowledgeId);
                course.setRemoteKnowledgeVideoVo(remoteKnowledgeVideoVo);
            }
        }

        //处理学习记录
        //查询学习记录
        List<Long> studyPlanningRecordIdList = recordList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).toList();

        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        studyRecordBo.setStudentId(studentId);
        List<StudyRecordVo> studyRecordVoList = studyRecordService.queryList(studyRecordBo);
        List<StudyVideoRecordVo> studyVideoRecordVos = studyVideoRecordService.queryLastStudyVideoRecord(studyPlanningRecordIdList, false);
        Map<Long, StudyVideoRecordVo> studyVideoRecordMap =  StreamUtils.toMap(studyVideoRecordVos, StudyVideoRecordVo::getStudyPlanningRecordId, Function.identity());

        Map<Long, StudyRecordVo> map = StreamUtils.toMap(studyRecordVoList, StudyRecordVo::getStudyPlanningRecordId, Function.identity());
        for (StudyPlanningRecordVo studyPlanningRecordVo : recordList) {
            Long studyPlanningRecordId = studyPlanningRecordVo.getStudyPlanningRecordId();
            StudyRecordVo studyRecordVo = map.get(studyPlanningRecordId);
            CourseVo course = studyPlanningRecordVo.getCourse();
            if (studyRecordVo != null && course != null) {
                course.setStudyRecord(studyRecordVo);
                course.setTestStatus(UserConstants.CORRECTION_STATUS_DONE.equals(studyRecordVo.getTestState()));
                course.setPracticeStatus(UserConstants.CORRECTION_STATUS_DONE.equals(studyRecordVo.getPracticeState()));
                course.setStudyCompleteStatus(false);
                checkStudyComplete(course, studyVideoRecordMap.get(studyPlanningRecordId));
            }
        }

        //计算学习规划是周几的
        for (StudyPlanningVo studyPlanningVo : list) {
            studyPlanningVo.setWeek(WEEK_ARR[DateUtil.dayOfWeek(studyPlanningVo.getStudyPlanningDate()) - 1]);
        }

        putCourseRepeatStatus(studentId,list);
        //按照日期排序，从小到大
        list.sort((o1, o2) -> (int) (o1.getStudyPlanningDate().getTime() - o2.getStudyPlanningDate().getTime()));
        return list;

    }

    private void checkStudyComplete(CourseVo course, StudyVideoRecordVo studyVideoRecordVo) {
        Boolean testStatus = course.getTestStatus();
        Boolean practiceStatus = course.getPracticeStatus();
        if (Boolean.TRUE.equals(testStatus) &&Boolean.TRUE.equals(practiceStatus) && Objects.nonNull(studyVideoRecordVo )) {
            Long studyVideoDuration = studyVideoRecordVo.getStudyVideoDuration();
            Long duration = studyVideoRecordVo.getDuration();
            //计算视频的完播率大于95%
            if (duration != null && studyVideoDuration != null) {
                BigDecimal divide = new BigDecimal(studyVideoDuration)
                    .divide(new BigDecimal(duration), 2, RoundingMode.HALF_UP);
                if (divide.compareTo(new BigDecimal("0.95")) >= 0) {
                    course.setStudyCompleteStatus(true);
                }
            }
        }

    }

    private void putCourseRepeatStatus(Long studentId, List<StudyPlanningVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> courseIdList = list.stream()
            .filter(v -> !CollectionUtils.isEmpty(v.getStudyPlanningRecordList()))
            .flatMap(v -> v.getStudyPlanningRecordList().stream())
            .map(StudyPlanningRecordVo::getCourseId)
            .filter(courseId -> !ObjectUtils.isEmpty(courseId))
            .distinct()
            .toList();
        Map<Long, List<StudyPlanningRecordVo>> longListMap = studyPlanningRecordService.checkCourseRepeat(courseIdList, studentId);

        for (StudyPlanningVo studyPlanningVo : list) {
            List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningVo.getStudyPlanningRecordList();
            if (CollectionUtils.isEmpty(studyPlanningRecordList)) {
                continue;
            }
            for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordList) {
                CourseVo studyRecordVo = studyPlanningRecordVo.getCourse();
                if (!ObjectUtils.isEmpty(studyRecordVo)) {
                    List<StudyPlanningRecordVo> studyList = longListMap.getOrDefault(studyPlanningRecordVo.getCourseId(), List.of());
                    if (CollectionUtils.isEmpty(studyList) || studyList.size() == 1) {
                        studyRecordVo.setRepeatStatus(false);
                        continue;
                    }
                    StudyPlanningRecordVo oldStudyRecord = studyList.stream().min(Comparator.comparing(StudyPlanningRecordVo::getLatestTime)).orElse(null);
                    if (studyPlanningRecordVo.getStudyPlanningRecordId().equals(oldStudyRecord.getStudyPlanningRecordId())) {
                        studyRecordVo.setRepeatStatus(false);
                    } else {
                        studyRecordVo.setRepeatStatus(true);
                    }
                }

            }
        }
    }

    @Override
    public CourseVo getChapterResource(Long studyPlanningRecordId, Long studentId) {
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(studyPlanningRecordId);
        Long courseId = studyPlanningRecordVo.getCourseId();
        CourseVo courseVo = courseService.queryById(courseId, Boolean.FALSE);
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //获取章节的顶级课程信息
        courseService.putTopmostCourseInfo(List.of(courseVo), true);
        if (courseVo.getTopmostCourse() != null) {
            courseService.putCourseDetail(List.of(courseVo.getTopmostCourse()), false);
        }

        //获取知识点
        Long knowledgeId = courseVo.getKnowledgeId();
        if (knowledgeId == null) {
            return courseVo;
        }
        //获取课程资源
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setTypeList(List.of(KnowledgeResourceType.HANDOUT.getType(), KnowledgeResourceType.TEST.getType(), KnowledgeResourceType.PRACTICE.getType()));
        List<RemoteGroupResourceVo> knowledgeResourceList = remoteResourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);

        if (CollUtil.isNotEmpty(knowledgeResourceList)) {
            for (RemoteGroupResourceVo remoteGroupResourceVo : knowledgeResourceList) {
                KnowledgeResourceType type = remoteGroupResourceVo.getType();
                switch (type) {
                    case HANDOUT:
                        courseVo.setHandout(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    case TEST:
                        courseVo.setTest(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    case PRACTICE:
                        courseVo.setPractice(remoteGroupResourceVo.getKnowledgeResource());
                        break;
                    default:
                        log.error("未知的资源类型");
                        break;
                }
            }
        }
        putResourceCompletionStatus(courseVo, LoginHelper.getStudentId(), studyPlanningRecordId);
        return courseVo;
    }

    @Override
    public RemoteKnowledgeResourceVo getExtResource(Long studyPlanningRecordId, KnowledgeResourceType resourceType) {
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(studyPlanningRecordId);
        Long courseId = studyPlanningRecordVo.getCourseId();
        CourseVo courseVo = courseService.queryById(courseId, Boolean.FALSE);
        if (courseVo == null) {
            throw new ServiceException("课程不存在");
        }
        //获取知识点
        Long knowledgeId = courseVo.getKnowledgeId();
        if (knowledgeId == null) {
            throw new ServiceException("课程没有关联知识点");
        }
        //获取课程资源
        RemoteGroupResourceVo knowledgeResource = remoteResourceService.getKnowledgeResourceById(knowledgeId, resourceType.getType());
        return knowledgeResource != null ? knowledgeResource.getKnowledgeResource() : null;
    }

    @Override
    public RemoteKnowledgeResourceVo getExtResourceWithNoException(Long studyPlanningRecordId, KnowledgeResourceType resourceType) {
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(studyPlanningRecordId);
        if (ObjectUtils.isEmpty(studyPlanningRecordVo)) {
            return null;
        }
        Long courseId = studyPlanningRecordVo.getCourseId();
        CourseVo courseVo = courseService.queryById(courseId, Boolean.FALSE);
        if (courseVo == null) {
            return null;
        }
        //获取知识点
        Long knowledgeId = courseVo.getKnowledgeId();
        if (knowledgeId == null) {
            return null;
        }
        //获取课程资源
        RemoteGroupResourceVo knowledgeResource = remoteResourceService.getKnowledgeResourceById(knowledgeId, resourceType.getType());
        return knowledgeResource != null ? knowledgeResource.getKnowledgeResource() : null;
    }

    @Override
    //TODO 暂时不处理父子题目
    public List<RemoteQuestionVo> getKnowledgeQuestionList(Long studyPlanningRecordId, Long courseId, KnowledgeResourceType resourceType, Boolean withAnalysis, Boolean withAnswer) {
        if (!resourceType.getIsQuestionType()) {
            throw new ServiceException("不支持" + resourceType.getDescription());
        }
        if (null == courseId && null != studyPlanningRecordId) {
            StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(studyPlanningRecordId);
            courseId = studyPlanningRecordVo.getCourseId();
        }
        return courseService.getKnowledgeQuestionList(courseId, resourceType, withAnalysis, withAnswer, true);
    }

    @Override
    public StudyPlanCompleteVo getPlanComplete(StudyPlanningBo bo, Long studentId, Boolean queryMyRank) {
        bo.setStudentId(studentId);
        StudyPlanCompleteVo studyPlanCompleteVo = new StudyPlanCompleteVo();
        studyPlanCompleteVo.setStudyDuration(0L);
        //避免分母为0
        studyPlanCompleteVo.setVideoTotalDuration(1L);
        studyPlanCompleteVo.setQuestionTotal(0L);
        studyPlanCompleteVo.setQuestionAccuracy(BigDecimal.ZERO);

        List<StudyPlanningVo> studyPlanningVos = queryPlanAndRecordList(bo);
        if (CollUtil.isEmpty(studyPlanningVos)) {
            return studyPlanCompleteVo;
        }
        //过滤掉没有学习规划详情的学习规划
        studyPlanningVos = studyPlanningVos.stream().filter(item -> CollUtil.isNotEmpty(item.getStudyPlanningRecordList())).collect(Collectors.toList());

        if (CollUtil.isEmpty(studyPlanningVos)) {
            return studyPlanCompleteVo;
        }
        //计算视频总时长
        //拿到所有课程ID
        List<Long> courseIdList = studyPlanningVos.stream().map(StudyPlanningVo::getStudyPlanningRecordList).flatMap(List::stream).map(StudyPlanningRecordVo::getCourseId).distinct().toList();
        //查课程信息
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVoList = courseService.queryList(courseBo);
        //拿到知识点ID
        List<Long> knowledgeIdList = courseVoList.stream().filter(e -> e.getKnowledgeId() != null).map(CourseVo::getKnowledgeId).distinct().toList();
        //查视频信息
        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);


        //计算视频总时长
        if (CollUtil.isNotEmpty(knowledgeVideoList)) {
            long videoTotalDuration = 0L;

            Map<Long, CourseVo> courseVoMap = StreamUtils.toIdentityMap(courseVoList, CourseVo::getCourseId);
            Map<Long, RemoteKnowledgeVideoVo> knowledgeVideoVoMap = StreamUtils.toIdentityMap(knowledgeVideoList, RemoteKnowledgeVideoVo::getKnowledgeId);
            List<StudyPlanningRecordVo> studyPlanningRecordVoList = studyPlanningVos.stream().map(StudyPlanningVo::getStudyPlanningRecordList).flatMap(List::stream).toList();

            for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordVoList) {
                Long courseId = studyPlanningRecordVo.getCourseId();
                CourseVo courseVo = courseVoMap.get(courseId);
                if (courseVo == null) {
                    continue;
                }
                Long knowledgeId = courseVo.getKnowledgeId();
                if (knowledgeId == null) {
                    continue;
                }
                RemoteKnowledgeVideoVo remoteKnowledgeVideoVo = knowledgeVideoVoMap.get(knowledgeId);
                if (remoteKnowledgeVideoVo != null && remoteKnowledgeVideoVo.getVideoTotalDuration() != null && remoteKnowledgeVideoVo.getVideoTotalDuration() > 0) {
                    videoTotalDuration += remoteKnowledgeVideoVo.getVideoTotalDuration();
                }
            }
            studyPlanCompleteVo.setVideoTotalDuration(videoTotalDuration);
        }

        //拿到所有学习规划详情对应的学习记录
        List<Long> studyPlanningRecordIdList = studyPlanningVos.stream().map(StudyPlanningVo::getStudyPlanningRecordList).flatMap(List::stream).map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
        //查学习记录

        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return studyPlanCompleteVo;
        }

        //计算已学时长
        long studyDuration = studyRecordVos.stream().filter(e -> e.getStudyVideoTotalDuration() != null && e.getStudyVideoTotalDuration() > 0).mapToLong(StudyRecordVo::getStudyVideoTotalDuration).sum();
        studyPlanCompleteVo.setStudyDuration(studyDuration);

        //计算做题总数(测试，练习，正确+错误)
        //practiceRightNum+practiceWrongNum+testRightNum+testWrongNum
        long questionTotal = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getPracticeRightNum(), 0L)
            + ObjectUtil.defaultIfNull(e.getPracticeWrongNum(), 0L)
            + ObjectUtil.defaultIfNull(e.getTestRightNum(), 0L)
            + ObjectUtil.defaultIfNull(e.getTestWrongNum(), 0L)
        ).sum();
        studyPlanCompleteVo.setQuestionTotal(questionTotal);

        //计算做题正确率 ：（练习正确题目数量+测试正确题目数量）/(练习正确题目数量+测试正确题目数量+练习错误题目数量+测试错误题目数量+练习未做题目数量+测试做题题目数量)
        long practiceRightNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getPracticeRightNum(), 0L)).sum();
        long practiceWrongNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getPracticeWrongNum(), 0L)).sum();
        long testRightNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getTestRightNum(), 0L)).sum();
        long testWrongNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getTestWrongNum(), 0L)).sum();
        long practiceUnansweredNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getPracticeUnansweredNum(), 0L)).sum();
        long testUnansweredNum = studyRecordVos.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getTestUnansweredNum(), 0L)).sum();
        long totalNum = practiceRightNum + practiceWrongNum + testRightNum + testWrongNum + practiceUnansweredNum + testUnansweredNum;
        if (totalNum > 0) {
            BigDecimal divide = new BigDecimal(practiceRightNum + testRightNum).divide(new BigDecimal(totalNum), 4, RoundingMode.HALF_UP);
            studyPlanCompleteVo.setQuestionAccuracy(divide);
        }

        if (queryMyRank) {
            //查排名
            StudyRecordBo studyRecordBoQuery = new StudyRecordBo();
            studyRecordBoQuery.setStudyPlanningDateStart(bo.getStudyPlanningDateStart());
            studyRecordBoQuery.setStudyPlanningDateEnd(bo.getStudyPlanningDateEnd());
            studyPlanCompleteVo.setMyRank(studyRecordService.queryMyRank(studyRecordBoQuery, studentId));
        }
        return studyPlanCompleteVo;
    }

    @Override
    public List<SubjectCompleteVo> getSubjectCompleteByDateLimit(Date studyPlanningDateStart, Date studyPlanningDateEnd, Long studentId) {
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudyPlanningDateStart(studyPlanningDateStart);
        studyPlanningBo.setStudyPlanningDateEnd(studyPlanningDateEnd);
        studyPlanningBo.setStudentId(studentId);
        List<StudyPlanningVo> studyPlanningVos = queryPlanAndRecordList(studyPlanningBo);


        if (CollUtil.isEmpty(studyPlanningVos)) {
            return List.of();
        }
        //过滤掉没有学习规划详情的学习规划
        studyPlanningVos = studyPlanningVos.stream().filter(item -> CollUtil.isNotEmpty(item.getStudyPlanningRecordList())).collect(Collectors.toList());

        if (CollUtil.isEmpty(studyPlanningVos)) {
            return List.of();
        }

        //所有学习规划详情拿出来
        List<StudyPlanningRecordVo> studyPlanningRecordVoList = studyPlanningVos.stream().map(StudyPlanningVo::getStudyPlanningRecordList).flatMap(List::stream).collect(Collectors.toList());

        //计算视频总时长
        //拿到所有课程ID
        List<Long> courseIdList = studyPlanningRecordVoList.stream().map(StudyPlanningRecordVo::getCourseId).distinct().toList();

        //查课程信息
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVoList = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVoList)) {
            return List.of();
        }

        courseService.putTopmostCourseInfo(courseVoList, Boolean.FALSE);

        //过滤掉没有顶级课程，没有学科的课程
        courseVoList = courseVoList.stream().filter(e -> e.getTopmostCourse() != null && StringUtils.isNotBlank(e.getTopmostCourse().getAffiliationSubject())).toList();

        if (CollUtil.isEmpty(courseVoList)) {
            return List.of();
        }

        //新增学科统计信息列表
        List<SubjectCompleteVo> subjectCompleteVoList = new ArrayList<>();
        courseVoList.stream().map(e -> e.getTopmostCourse().getAffiliationSubject()).distinct().forEach(affiliationSubject -> {
            SubjectCompleteVo subjectCompleteVo = new SubjectCompleteVo();
            subjectCompleteVo.setAffiliationSubject(affiliationSubject);
            subjectCompleteVo.setCompletionRatio(BigDecimal.ZERO);
            subjectCompleteVoList.add(subjectCompleteVo);
        });

        //拿到知识点ID
        List<Long> knowledgeIdList = courseVoList.stream().filter(e -> e.getKnowledgeId() != null).map(CourseVo::getKnowledgeId).distinct().toList();
        //查视频信息
        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        if (CollUtil.isEmpty(knowledgeVideoList)) {
            return subjectCompleteVoList;
        }

        //拿到所有学习规划详情对应的学习记录
        List<Long> studyPlanningRecordIdList = studyPlanningRecordVoList.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).distinct().toList();
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return subjectCompleteVoList;
        }

        //开始计算各科完成度

        //按照学科分组学习
        Map<String, List<CourseVo>> courseMap = courseVoList.stream().collect(Collectors.groupingBy(e -> e.getTopmostCourse().getAffiliationSubject()));
        //学习规划详情按照课程ID分组
        Map<Long, List<StudyPlanningRecordVo>> studyPlanningRecordMap = studyPlanningRecordVoList.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getCourseId));
        //知识点视频按照知识点ID分组
        Map<Long, RemoteKnowledgeVideoVo> knowledgeVideoMap = knowledgeVideoList.stream().collect(Collectors.toMap(RemoteKnowledgeVideoVo::getKnowledgeId, Function.identity()));
        //学习记录按照学习规划详情ID分组
        Map<Long, StudyRecordVo> studyRecordMap = studyRecordVos.stream().collect(Collectors.toMap(StudyRecordVo::getStudyPlanningRecordId, Function.identity()));
        for (SubjectCompleteVo subjectCompleteVo : subjectCompleteVoList) {
            String affiliationSubject = subjectCompleteVo.getAffiliationSubject();
            BigDecimal totalVideoDuration = new BigDecimal(0);
            BigDecimal totalStudyDuration = new BigDecimal(0);
            List<CourseVo> courseList = courseMap.get(affiliationSubject);
            for (CourseVo courseVo : courseList) {
                Long courseId = courseVo.getCourseId();
                Long knowledgeId = courseVo.getKnowledgeId();
                RemoteKnowledgeVideoVo knowledgeVideo = knowledgeVideoMap.get(knowledgeId);
                List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningRecordMap.get(courseId);
                for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordList) {
                    if (knowledgeVideo != null && knowledgeVideo.getVideoTotalDuration() != null) {
                        //为什么放在里面的循环，是因为可能多个学习规划对应的是同一个课程，所以需要累加视频时长
                        totalVideoDuration = totalVideoDuration.add(new BigDecimal(knowledgeVideo.getVideoTotalDuration()));
                    }
                    StudyRecordVo studyRecordVo = studyRecordMap.get(studyPlanningRecordVo.getStudyPlanningRecordId());
                    if (studyRecordVo != null && studyRecordVo.getStudyVideoTotalDuration() != null) {
                        totalStudyDuration = totalStudyDuration.add(new BigDecimal(studyRecordVo.getStudyVideoTotalDuration()));
                    }
                }
            }
            if (totalVideoDuration.compareTo(BigDecimal.ZERO) > 0) {
                subjectCompleteVo.setCompletionRatio(totalStudyDuration.divide(totalVideoDuration, 4, RoundingMode.HALF_UP));
            }
        }
        return subjectCompleteVoList;
    }

    private void putResourceCompletionStatus(CourseVo courseVo, Long studentId, Long studyPlanningRecordId) {
        if (courseVo == null) {
            return;
        }
        //获取学习记录
        //查总视频记录


        //查询学习记录数据
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordId(studyPlanningRecordId);
        studyRecordBo.setStudentId(studentId);
        StudyRecordVo studyRecordVo = studyRecordService.queryOnce(studyRecordBo);
        studyRecordVo = studyRecordVo == null ? new StudyRecordVo() : studyRecordVo;
        RemoteKnowledgeResourceVo handout = courseVo.getHandout();
        RemoteKnowledgeResourceVo test = courseVo.getTest();
        RemoteKnowledgeResourceVo practice = courseVo.getPractice();
        //获取视频
        RemoteKnowledgeVideoVo knowledgeVideo = remoteExtVideoService.getKnowledgeVideoListById(courseVo.getKnowledgeId());
        courseVo.setRemoteKnowledgeVideoVo(knowledgeVideo);

        if (studyRecordVo != null && knowledgeVideo != null && CollUtil.isNotEmpty(knowledgeVideo.getVideoList())) {
            knowledgeVideo.getVideoList().get(0).setTotalStudyDuration(studyRecordVo.getStudyVideoTotalDuration());
            //视频记录是按照天来记的，所以只需要查最后一天的即可
            StudyVideoRecordVo studyVideoRecordVo = studyVideoRecordService.queryLastDayRecord(studentId, studyPlanningRecordId);
            if (studyVideoRecordVo != null && StringUtils.isNotBlank(studyVideoRecordVo.getStudyVideoSlices())) {
                String[] split = studyVideoRecordVo.getStudyVideoSlices().split(",");
                knowledgeVideo.getVideoList().get(0).setLastDayStudyVideoSlices(split[split.length - 1]);
            }
        }

        if (handout != null) {
            //nothing
        }
        if (test != null) {
            String testState = studyRecordVo.getTestState();
            test.setCompletionStatus(CorrectionStatusEnum.CORRECTED.getCode().equals(testState));
        }
        if (practice != null) {
            String practiceState = studyRecordVo.getPracticeState();
            practice.setCompletionStatus(CorrectionStatusEnum.CORRECTED.getCode().equals(practiceState));
        }
        courseVo.setStudyRecord(studyRecordVo);
    }

    /**
     * 检查学习规划冲突，返回值为空则说明没有冲突，否则返回冲突列表
     */
    @Override
    public List<StudyPlanningConflictVo> checkStudyPlanningConflict(List<StudyPlanningRecordBo> studyPlanningRecordBoList) {
        if (CollUtil.isEmpty(studyPlanningRecordBoList)) {
            return List.of();
        }
        if (studyPlanningRecordBoList.stream().anyMatch(e -> e.getStudyPlanningDate() == null)) {
            throw new ServiceException("请检查学习规划的日期是否为空");
        }

        if (studyPlanningRecordBoList.stream().anyMatch(e -> e.getStudyStartTime() == null || e.getStudyEndTime() == null)) {
            throw new ServiceException("请检查学习规划详情的学习时间是否为空");
        }

        if (studyPlanningRecordBoList.stream().anyMatch(e -> e.getStudentId() == null)) {
            throw new ServiceException("请检查学习规划详情的会员是否为空");
        }

        //取出最大时间和最小时间
        Date maxDate = studyPlanningRecordBoList.stream().map(StudyPlanningRecordBo::getStudyPlanningDate).max(Date::compareTo).get();
        Date minDate = studyPlanningRecordBoList.stream().map(StudyPlanningRecordBo::getStudyPlanningDate).min(Date::compareTo).get();

        minDate = DateUtil.beginOfDay(minDate);
        maxDate = DateUtil.endOfDay(maxDate);

        //找出这在这个时间区间内的学习规划及其详情
        StudyPlanningBo studyPlanningBoQuery = new StudyPlanningBo();
        studyPlanningBoQuery.setStudyPlanningDateStart(DateUtil.beginOfDay(minDate));
        studyPlanningBoQuery.setStudyPlanningDateEnd(DateUtil.endOfDay(maxDate));
        //studyPlanningBoQuery.setStudentId(studentId);
        List<StudyPlanningVo> studyPlanningVos = queryPlanAndRecordList(studyPlanningBoQuery);

        //为空则说明没有冲突
        if (CollUtil.isEmpty(studyPlanningVos)) {
            return List.of();
        }
        //展开查出来的学习规划详情，并给详情赋值规划日期

        List<StudyPlanningRecordVo> onlineStudyPlanRecordVoList = new ArrayList<>();
        for (StudyPlanningVo studyPlanningVo : studyPlanningVos) {
            studyPlanningVo.getStudyPlanningRecordList().forEach(e -> e.setStudyPlanningDate(studyPlanningVo.getStudyPlanningDate()));
            onlineStudyPlanRecordVoList.addAll(studyPlanningVo.getStudyPlanningRecordList());
        }

        //有可能有去拖动以前已经规划好的学习规划，我们这里把线上查的去掉他们
        List<Long> list = studyPlanningRecordBoList.stream().map(StudyPlanningRecordBo::getStudyPlanningRecordId).filter(Objects::nonNull).toList();
        if (CollUtil.isNotEmpty(list)) {
            onlineStudyPlanRecordVoList.removeIf(e -> list.contains(e.getStudyPlanningRecordId()));
        }


        //先按照会员ID分组
        Map<Long, List<StudyPlanningRecordBo>> offlinestudyPlanningBoMap = studyPlanningRecordBoList.stream().collect(Collectors.groupingBy(StudyPlanningRecordBo::getStudentId));
        Map<Long, List<StudyPlanningRecordVo>> onlineStudyPlanningVoMap = onlineStudyPlanRecordVoList.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getStudentId));

        //冲突列表
        List<StudyPlanningConflictVo> conflictList = new ArrayList<>();

        //按照学生来处理冲突
        for (Map.Entry<Long, List<StudyPlanningRecordBo>> studyPlanningBoEntry : offlinestudyPlanningBoMap.entrySet()) {
            Long studentId = studyPlanningBoEntry.getKey();

            //线上数据
            List<StudyPlanningRecordVo> studyPlanningVoList = onlineStudyPlanningVoMap.get(studentId);

            //线上的按照日期分组(只要年月日，因为数据和java数据类型的原因，数据库就算只有年月日，java的date类型也会有时分秒)
            Map<String, List<StudyPlanningRecordVo>> onlineMap = StreamUtils.groupByKey(studyPlanningVoList, e -> DateUtils.dateTime(e.getStudyPlanningDate()));

            //开始遍历线下提交上来的
            for (StudyPlanningRecordBo studyPlanningRecordBo : studyPlanningBoEntry.getValue()) {
                //检查日期同一天的
                Date studyPlanningDate = studyPlanningRecordBo.getStudyPlanningDate();
                String dateStr = DateUtils.dateTime(studyPlanningDate);
                List<StudyPlanningRecordVo> studyPlanningRecordVos = onlineMap.get(dateStr);
                //没有同一天的，那细节就不必检查，肯定没有冲突
                if (CollUtil.isEmpty(studyPlanningRecordVos)) {
                    continue;
                }

                //检查学习规划详情是否冲突

                //线下的信息
                long newStudyStartTime = DateUtils.timeToSeconds(studyPlanningRecordBo.getStudyStartTime());
                long newStudyEndTime = DateUtils.timeToSeconds(studyPlanningRecordBo.getStudyEndTime());

                //开始遍历线上的数据，判断是否和线下的有冲突
                for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordVos) {
                    long onlineStudyStartTime = DateUtils.timeToSeconds(studyPlanningRecordVo.getStudyStartTime());
                    long onlineStudyEndTime = DateUtils.timeToSeconds(studyPlanningRecordVo.getStudyEndTime());

                    //允许开始时间和另外一组的结束时间重叠
                    if (!(newStudyStartTime < onlineStudyEndTime && newStudyEndTime > onlineStudyStartTime)) {
                        continue;
                    }

                    // 有交集，说明冲突
                    StudyPlanningConflictVo conflictVo = new StudyPlanningConflictVo();
                    conflictVo.setStudentId(studentId);
                    conflictVo.setConflictDate(DateUtils.dateTime(studyPlanningDate));
                    conflictVo.setOfflineStartTime(DateUtils.getTime(studyPlanningRecordBo.getStudyStartTime()));
                    conflictVo.setOfflineEndTime(DateUtils.getTime(studyPlanningRecordBo.getStudyEndTime()));
                    conflictVo.setOnlineStartTime(DateUtils.getTime(studyPlanningRecordVo.getStudyStartTime()));
                    conflictVo.setOnlineEndTime(DateUtils.getTime(studyPlanningRecordVo.getStudyEndTime()));
                    conflictVo.setOfflineCourseId(studyPlanningRecordBo.getCourseId());
                    conflictVo.setOnlineCourseId(studyPlanningRecordVo.getCourseId());
                    conflictVo.setOnlineStudyPlanningRecordId(studyPlanningRecordVo.getStudyPlanningRecordId());
                    conflictVo.setOfflineStudyPlanningRecordId(studyPlanningRecordBo.getStudyPlanningRecordId());
                    conflictList.add(conflictVo);
                }
            }
        }


        if (CollUtil.isEmpty(conflictList)) {
            return List.of();
        }

        List<Long> studentIdList = conflictList.stream().map(StudyPlanningConflictVo::getStudentId).toList();
        Set<Long> courseIdList = new HashSet<>();
        for (StudyPlanningConflictVo conflictVo : conflictList) {
            courseIdList.add(conflictVo.getOfflineCourseId());
            courseIdList.add(conflictVo.getOnlineCourseId());
        }

        //查询会员信息
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));


        //查询课程信息
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(new ArrayList<>(courseIdList));
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        Map<Long, CourseVo> courseVoMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, Function.identity()));

        //组装数据
        for (StudyPlanningConflictVo studyPlanningConflictVo : conflictList) {
            StudentVo studentVo = studentVoMap.get(studyPlanningConflictVo.getStudentId());
            CourseVo offlineCourseVo = courseVoMap.get(studyPlanningConflictVo.getOfflineCourseId());
            CourseVo onlineCourseVo = courseVoMap.get(studyPlanningConflictVo.getOnlineCourseId());
            studyPlanningConflictVo.setStudent(studentVo);
            studyPlanningConflictVo.setOfflineCourse(offlineCourseVo);
            studyPlanningConflictVo.setOnlineCourse(onlineCourseVo);
        }
        //按照会员id排序
        conflictList.sort(Comparator.comparing(StudyPlanningConflictVo::getStudentId));

        return conflictList;

    }


    @Override
    public List<StudyPlanningCourseRepeatVo> checkStudyPlanningRepeatCourse(StudyCheckCourseRepeatBo repeatBo) {
        Long courseId = repeatBo.getCourseId();
        List<Long> studentIdList = repeatBo.getStudentIdList();
        if (ObjectUtils.isEmpty(courseId)){
            return List.of();
        }
        if (CollectionUtils.isEmpty(studentIdList)){
            return List.of();
        }
        List<StudyPlanningCourseRepeatVo> repeatStudentList=Lists.newArrayList();
       Set<Long> studentIdSet = studyPlanningRecordService.checkCourseRepeat(courseId,studentIdList);
        for (Long studentId : studentIdList) {
            if (studentIdSet.contains(studentId)) {
                StudyPlanningCourseRepeatVo repeatVo = new StudyPlanningCourseRepeatVo();
                repeatVo.setCourseId(courseId);
                repeatVo.setStudentId(studentId);
                repeatStudentList.add(repeatVo);
            }
        }
        if (!CollectionUtils.isEmpty(repeatStudentList)){
            List<Long> list = repeatStudentList.stream().map(StudyPlanningCourseRepeatVo::getStudentId).toList();
            Map<Long, String> studentNameMap = studentService.queryStudentName(list);
            for (StudyPlanningCourseRepeatVo studyPlanningCourseRepeatVo : repeatStudentList) {
                Long studentId = studyPlanningCourseRepeatVo.getStudentId();
                String name = studentNameMap.get(studentId);
                studyPlanningCourseRepeatVo.setStudentName(name);
            }
        }

        return repeatStudentList;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void arrangementStudyPlanning(List<ArrangementStudyPlanningBo> list, Integer arrangementMode, Boolean noRepeatLessons) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentIdList = list.stream().map(ArrangementStudyPlanningBo::getStudentId).toList();
        //查询会员信息
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos) || studentVos.size() != studentIdList.size()) {
            throw new ServiceException("有不存在的会员");
        }

        List<RemoteBranchMachineSeatBo> seatInsertList = new ArrayList<>();


        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));

        Date maxDate = null;
        Date minDate = null;


        Set<Long> allDelList = new HashSet<>();
        List<StudyPlanningRecordBo> addStudyPlanningRecordBoList = new ArrayList<>();
        List<StudyPlanningBo> addStudyPlanningBoList = new ArrayList<>();

        if (Boolean.TRUE.equals(noRepeatLessons)) {
            list = filterRepeatLessons(list);
        }
        if (CollUtil.isEmpty(list)) {
            return;
        }

        for (ArrangementStudyPlanningBo arrangementStudyPlanningBo : list) {
            Long studentId = arrangementStudyPlanningBo.getStudentId();
            StudentVo studentVo = studentVoMap.get(studentId);

            List<Long> delList = arrangementStudyPlanningBo.getDelList();
            if (CollUtil.isNotEmpty(delList)) {
                allDelList.addAll(delList);
            }

            //详情记录列表
            List<StudyPlanningRecordBo> studyPlanningRecordList = arrangementStudyPlanningBo.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)) {
                continue;
            }

            //按照日期分组
            Map<String, List<StudyPlanningRecordBo>> map = studyPlanningRecordList.stream().collect(Collectors.groupingBy(e -> DateUtils.dateTime(e.getStudyPlanningDate())));

            //要查一下之前有没有这个日期的学习规划主体，有的话就给下Id
            List<StudyPlanningVo> studyPlanningVoList = queryStudyPlanningByDateStrList(studentId, map.keySet());
            Map<String, Long> oldStudyPlanningIdMap = studyPlanningVoList.stream().collect(Collectors.toMap(e -> DateUtils.dateTime(e.getStudyPlanningDate()), StudyPlanningVo::getStudyPlanningId));


            for (Map.Entry<String, List<StudyPlanningRecordBo>> entry : map.entrySet()) {
                String dateStr = entry.getKey();
                Date date = DateUtils.parseDate(dateStr);
                if (minDate == null || date.getTime() < minDate.getTime()) {
                    minDate = date;
                }
                if (maxDate == null || date.getTime() > maxDate.getTime()) {
                    maxDate = date;
                }

                //先新建这个日期的学习规划主体
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentId(studentId);
                studyPlanningBo.setStudyPlanningDate(date);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                studyPlanningBo.setCreateBy(studentVo.getCreateBy());
                studyPlanningBo.setCreateDept(studentVo.getCreateDept());
                //有老的用老的
                if (oldStudyPlanningIdMap.containsKey(dateStr)) {
                    studyPlanningBo.setStudyPlanningId(oldStudyPlanningIdMap.get(dateStr));
                }

                addStudyPlanningBoList.add(studyPlanningBo);
                //再新建这个日期的学习规划详情
                for (StudyPlanningRecordBo studyPlanningRecordBo : entry.getValue()) {
                    studyPlanningRecordBo.setStudyPlanningRecordId(null);
                    studyPlanningRecordBo.setStudyPlanningId(null);
                    studyPlanningRecordBo.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_NORMAL);
                    studyPlanningRecordBo.setStudentId(studentId);
                    studyPlanningRecordBo.setCreateBy(studentVo.getCreateBy());
                    studyPlanningRecordBo.setCreateDept(studentVo.getCreateDept());
                    addStudyPlanningRecordBoList.add(studyPlanningRecordBo);


                    //座位
                    if (studyPlanningRecordBo.getBranchMachineSeat() != null && studyPlanningRecordBo.getBranchMachineSeat().getSeatNo() != null && studyPlanningRecordBo.getBranchMachineSeat().getBranchMachineRegionId() != null) {
                        Date useStartTime = DateUtils.parseDate(dateStr + " " + DateUtils.getTime(studyPlanningRecordBo.getStudyStartTime()));
                        Date useEndTime = DateUtils.parseDate(dateStr + " " + DateUtils.getTime(studyPlanningRecordBo.getStudyEndTime()));
                        RemoteBranchMachineSeatBo seatBo = new RemoteBranchMachineSeatBo();
                        seatBo.setBranchMachineRegionId(studyPlanningRecordBo.getBranchMachineSeat().getBranchMachineRegionId());
                        seatBo.setStudentId(studentId);
                        seatBo.setSeatNo(studyPlanningRecordBo.getBranchMachineSeat().getSeatNo());
                        seatBo.setUseStartTime(useStartTime);
                        seatBo.setUseEndTime(useEndTime);
                        seatInsertList.add(seatBo);
                    }
                }
            }
        }

        //多人排课要把这些人之前的排课记录都删掉（默认所有会员的排课日期范围都是一样的）（如果已经前端已经实现了多人排课时候的冲突检测，那么这里就不用做了）
        //得出一个时间范围，然后查出这个时间范围内的学习规划详情，然后放到删除列表
        //if (Objects.equals(arrangementMode, 2)) {
        //    //先查出这个时间范围内的学习规划详情
        //    StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        //    studyPlanningBo.setStudyPlanningDateStart(DateUtil.beginOfDay(minDate));
        //    studyPlanningBo.setStudyPlanningDateEnd(DateUtil.endOfDay(maxDate));
        //    studyPlanningBo.setStudentIdList(studentIdList);
        //    List<StudyPlanningVo> studyPlanningVos = queryPlanAndRecordList(studyPlanningBo);
        //    List<Long> studyPlanningRecordIdList = studyPlanningVos.stream().flatMap(e -> e.getStudyPlanningRecordList().stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId)).toList();
        //    //放到删除列表
        //    allDelList.addAll(studyPlanningRecordIdList);
        //}

        //新增
        //先新增学习规划主体（studyPlanningId没有的才新增）
        List<StudyPlanning> studyPlanningList = MapstructUtils.convert(addStudyPlanningBoList, StudyPlanning.class);
        List<StudyPlanning> needInsertList = studyPlanningList.stream().filter(e -> e.getStudyPlanningId() == null).toList();
        List<StudyPlanning> oldStudyPlanningList = studyPlanningList.stream().filter(e -> e.getStudyPlanningId() != null).toList();
        if (CollUtil.isNotEmpty(needInsertList)) {
            boolean flag = SpringUtils.getBean(IStudyPlanningService.class).insertBatch(needInsertList);
            if (!flag) {
                throw new ServiceException("新增学习规划主体失败");
            }
        }
        studyPlanningList = new ArrayList<>();
        studyPlanningList.addAll(needInsertList);
        studyPlanningList.addAll(oldStudyPlanningList);

        //再新增学习规划详情
        //先关联上学习规划主体
        //按照会员分组
        Map<Long, List<StudyPlanningRecordBo>> map = addStudyPlanningRecordBoList.stream().collect(Collectors.groupingBy(StudyPlanningRecordBo::getStudentId));
        for (StudyPlanning studyPlanning : studyPlanningList) {
            Long studyPlanningId = studyPlanning.getStudyPlanningId();
            List<StudyPlanningRecordBo> studyPlanningRecordBoList = map.get(studyPlanning.getStudentId());
            String studyPlanningDateStr = DateUtils.dateTime(studyPlanning.getStudyPlanningDate());
            //过滤出同一天的，并关联上学习规划主体
            studyPlanningRecordBoList.stream().filter(e -> DateUtils.dateTime(e.getStudyPlanningDate()).equals(studyPlanningDateStr)).forEach(e -> {
                e.setStudyPlanningId(studyPlanningId);
            });
        }
        if (CollUtil.isNotEmpty(addStudyPlanningRecordBoList)) {
            boolean flag = studyPlanningRecordService.insertBatchByBo(addStudyPlanningRecordBoList);
            if (!flag) {
                throw new ServiceException("新增学习规划记录失败");
            }
        }


        //最后再删除，因为不先新增的话，后面覆盖的时候，要删除教学规划，可能会误判复用到的老规划(默认的事务事务隔离级别是可重复读，会读取到修改的，所以会保证这里是对的)
        //先删除（其实就是覆盖状态）
        if (CollUtil.isNotEmpty(allDelList)) {
            boolean flag = SpringUtils.getBean(IStudyPlanningService.class).batchUpdateCover(new ArrayList<>(allDelList));
            if (!flag) {
                throw new ServiceException("删除学习规划详情失败");
            }
        }


        //新增座位
        if (CollUtil.isNotEmpty(seatInsertList)) {
            boolean flag = remoteBranchMachineSeatService.insertBatchByBo(seatInsertList);
            if (!flag) {
                throw new ServiceException("新增座位失败");
            }
        }

        // 仿照studyFeedBackReport的add里面的Dofeedback，将需学习规划学生列表变成已规划
        // 根据实际的StudyPlanningBo的日期，只要StudyPlanning命中pending的开始时间和结束时间，就能获取
        if (CollUtil.isNotEmpty(addStudyPlanningBoList)) {
            log.info("开始更新学生规划状态，将需学习规划学生列表变成已规划, 学习规划数: {}",
                addStudyPlanningBoList.size());

            // 按学习日期分组处理，以日期为主体，批量处理学生
            Map<Date, List<StudyPlanningBo>> datePlanningMap = addStudyPlanningBoList.stream()
                .filter(planning -> planning.getStudyPlanningDate() != null)
                .collect(Collectors.groupingBy(StudyPlanningBo::getStudyPlanningDate));

            for (Map.Entry<Date, List<StudyPlanningBo>> entry : datePlanningMap.entrySet()) {
                Date planningDate = entry.getKey();
                List<StudyPlanningBo> datePlannings = entry.getValue();

                try {
                    // 获取该日期下的所有学生ID
                    List<Long> studentIds = datePlannings.stream()
                        .map(StudyPlanningBo::getStudentId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

                    log.info("处理学习规划日期: {}, 学生数: {}", planningDate, studentIds.size());

                    // 批量查询该日期下所有学生的pending记录
                    Map<Long, List<Long>> studentPendingMap = studyPlanningPendingService.findPendingIdsByStudentsAndDate(
                        studentIds, planningDate);

                    // 批量查询该日期下所有学生的学习规划记录ID（只查询ID字段）
                    List<Long> studyPlanningIds = datePlannings.stream()
                        .map(StudyPlanningBo::getStudyPlanningId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

                    Map<Long, List<Long>> studentPlanningRecordMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(studyPlanningIds)) {
                        // 使用新的方法只查询ID字段，按学生ID分组
                        studentPlanningRecordMap = studyPlanningRecordService.queryRecordIdsByStudentIdMap(studyPlanningIds);
                    }

                    // 统计处理结果
                    int successCount = 0;
                    int failCount = 0;
                    int noRecordCount = 0;
                    int noPendingCount = 0;

                    // 为该日期下的每个学生处理pending状态更新
                    for (Long studentId : studentIds) {
                        try {
                            // 从预先分组的Map中获取该学生的学习规划记录ID
                            List<Long> studentPlanningRecordIds = studentPlanningRecordMap.get(studentId);

                            if (CollUtil.isEmpty(studentPlanningRecordIds)) {
                                continue;
                            }

                            // 从批量查询结果中获取该学生的pending记录
                            List<Long> pendingIds = studentPendingMap.get(studentId);

                            if (CollUtil.isNotEmpty(pendingIds)) {
                                // 批量处理该学生的所有pending记录
                                boolean studentSuccess = true;
                                for (Long pendingId : pendingIds) {
                                    try {
                                        studyPlanningPendingService.markAsPlannedByPendingId(pendingId, studentPlanningRecordIds);
                                    } catch (Exception e) {
                                        log.error("更新pending规划状态失败, pendingId: {}, studentId: {}, 日期: {}",
                                            pendingId, studentId, planningDate, e);
                                        studentSuccess = false;
                                        // 继续处理其他pending记录，不中断整个流程
                                    }
                                }

                                if (studentSuccess) {
                                    successCount++;
                                    log.debug("成功更新学生规划状态, studentId: {}, pendingIds: {}, 规划记录数: {}, 学习规划日期: {}",
                                        studentId, pendingIds, studentPlanningRecordIds.size(), planningDate);
                                } else {
                                    failCount++;
                                }
                            } else {
                                noPendingCount++;
                            }
                        } catch (Exception e) {
                            log.error("更新学生规划状态失败, studentId: {}, 日期: {}", studentId, planningDate, e);
                            failCount++;
                            // 继续处理其他学生，不中断整个流程
                        }
                    }

                    // 汇总日志输出
                    log.info("处理学习规划日期: {} 完成, 总学生数: {}, 成功: {}, 失败: {}, 无记录: {}, 无pending: {}",
                        planningDate, studentIds.size(), successCount, failCount, noRecordCount, noPendingCount);
                } catch (Exception e) {
                    log.error("处理学习规划日期失败, 日期: {}", planningDate, e);
                    // 继续处理其他日期，不中断整个流程
                }
            }
        }
    }

    private @NotNull List<ArrangementStudyPlanningBo> filterRepeatLessons(List<ArrangementStudyPlanningBo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        for (ArrangementStudyPlanningBo arrangementStudyPlanningBo : list) {
            List<StudyPlanningRecordBo> studyPlanningRecordList = arrangementStudyPlanningBo.getStudyPlanningRecordList();
            if (CollectionUtils.isEmpty(studyPlanningRecordList)) {
                continue;
            }
            for (StudyPlanningRecordBo studyPlanningRecordBo : studyPlanningRecordList) {
                studyPlanningRecordBo.setStudentId(arrangementStudyPlanningBo.getStudentId());
            }
        }
        // 提取所有非空的 StudyPlanningRecord 的 courseId 和 studentId 组合
        Map<Long, List<Long>> courseStudentMap = list.stream()
            .filter(v -> !CollectionUtils.isEmpty(v.getStudyPlanningRecordList()))
            .flatMap(v -> v.getStudyPlanningRecordList().stream())
            .filter(v -> !ObjectUtils.isEmpty(v.getCourseId()) && !ObjectUtils.isEmpty(v.getStudentId()))
            .collect(Collectors.groupingBy(
                StudyPlanningRecordBo::getCourseId,
                Collectors.mapping(StudyPlanningRecordBo::getStudentId, Collectors.toList())
            ));

        // 调用 checkCourseRepeat 方法，获取重复的学生 ID
        Map<Long, Set<Long>> repeatedStudentIdsByCourse = courseStudentMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> studyPlanningRecordService.checkCourseRepeat(entry.getKey(), entry.getValue())
            ));

        // 过滤掉重复的课程和学生组合
        list = list.stream()
            .filter(v -> {
                if (CollectionUtils.isEmpty(v.getStudyPlanningRecordList())) {
                    return false;
                }
                List<StudyPlanningRecordBo> filteredRecordList = v.getStudyPlanningRecordList().stream()
                    .filter(record -> !repeatedStudentIdsByCourse.getOrDefault(record.getCourseId(), Collections.emptySet()).contains(record.getStudentId()))
                    .toList();
                if (CollectionUtils.isEmpty(filteredRecordList)) {
                    return false;
                }
                v.setStudyPlanningRecordList(filteredRecordList);
                return true;
            }).toList();
        return list;
    }


    private List<StudyPlanningVo> queryStudyPlanningByDateStrList(Long studentId, Collection<String> dateStrList) {
        LambdaQueryWrapper<StudyPlanning> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StudyPlanning::getStudentId, studentId);
        queryWrapper.in(StudyPlanning::getStudyPlanningDate, dateStrList);
        queryWrapper.eq(StudyPlanning::getStudyPlanningStatus, UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        return baseMapper.selectVoList(queryWrapper);
    }

    @Override
    public boolean insertBatch(List<StudyPlanning> addStudyPlanningList) {
        if (CollUtil.isEmpty(addStudyPlanningList)) {
            return true;
        }
        return baseMapper.insertBatch(addStudyPlanningList);
    }

    /**
     * 批量删除学习规划
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        List<Long> studyPlanningRecordIdList = new ArrayList<>(ids);
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        studyPlanningRecordBo.setWithBranchMachineSeatInfo(true);
        studyPlanningRecordBo.setWithTestCorrectionRecord(true);
        studyPlanningRecordBo.setWithPracticeCorrectionRecord(true);
        studyPlanningRecordBo.setWithLastStudyVideoRecord(true);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningRecordBo);
        if (CollUtil.isEmpty(studyPlanningRecordVos)) {
            return true;
        }
        if (isValid) {
            studyPlanningRecordVos = studyPlanningRecordVos.stream().filter(v -> {
                CorrectionRecordVo testCorrectionRecord = v.getTestCorrectionRecord();
                if (ObjectUtils.isEmpty(testCorrectionRecord)) {
                    return true;
                }
                CorrectionRecordVo practiceCorrectionRecord = v.getPracticeCorrectionRecord();
                if (ObjectUtils.isEmpty(practiceCorrectionRecord)) {
                    return true;
                }
                StudyVideoRecordVo lastStudyVideoRecord = v.getLastStudyVideoRecord();
                if (ObjectUtils.isEmpty(lastStudyVideoRecord)) {
                    return true;
                }
                Long studyVideoDuration = lastStudyVideoRecord.getStudyVideoDuration();
                Long duration = lastStudyVideoRecord.getDuration();
                if (ObjectUtils.isEmpty(studyVideoDuration) || ObjectUtils.isEmpty(duration)) {
                    return true;
                }
                try {
                    double completionRate = (double) studyVideoDuration / duration * 100;
                    return completionRate < 95;
                } catch (Exception e) {
                    return true;
                }
            }).toList();

            //判断是否有删除现在之前的记录，有的话记录错误，返回回去
            //2025.2.20 删除只能删除未完成的记录
//            Date date = new Date();
//            List<StudyPlanningRecordVo> errList = studyPlanningRecordVos.stream().filter(e -> {
//                Date studyPlanningDate = e.getStudyPlanning().getStudyPlanningDate(); //学习规划日期   取日期
//                Date studyStartTime = e.getStudyStartTime();    //开始时间  取时分秒
//                // 组装
//                Date useStartTime = DateUtils.parseDate(DateUtils.dateTime(studyPlanningDate) + " " + DateUtils.getTime(studyStartTime));
//                return useStartTime.before(date);
//            }).toList();
//            if (CollUtil.isNotEmpty(errList)) {
//                throw new ServiceException("存在部分规划开始时间早于当前时间，无法删除，请重新选择");
//            }


        }
        List<Long> studyPlanningIdList = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getStudyPlanningId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studyPlanningIdList)) {
            return true;
        }
        //查出所有同主体的学习规划详情记录
        Map<Long, List<Long>> map = studyPlanningRecordService.queryIdMapByStudyPlanningIdList(studyPlanningIdList);

        //先修改记录为删除状态
        List<StudyPlanningRecordBo> updateList = new ArrayList<>();
        for (Long l : studyPlanningRecordIdList) {
            StudyPlanningRecordBo studyPlanningRecord = new StudyPlanningRecordBo();
            studyPlanningRecord.setStudyPlanningRecordId(l);
            studyPlanningRecord.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_DELETE);
            updateList.add(studyPlanningRecord);
        }
        boolean b = studyPlanningRecordService.updateBatchByBo(updateList);
        if (!b) {
            return false;
        }

        //判断规划主体哪些在删除以后，为空了，可以删除
        List<StudyPlanning> delList = new ArrayList<>();

        for (Map.Entry<Long, List<Long>> entry : map.entrySet()) {
            List<Long> value = entry.getValue();
            value.removeIf(studyPlanningRecordIdList::contains);
            if (CollUtil.isEmpty(value)) {
                StudyPlanning studyPlanning = new StudyPlanning();
                studyPlanning.setStudyPlanningId(entry.getKey());
                studyPlanning.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_DELETE);
                delList.add(studyPlanning);
            }
        }

        if (CollUtil.isNotEmpty(delList)) {
            //删除学习规划主体
            boolean b1 = baseMapper.updateBatchById(delList);
            if (!b1) {
                throw new ServiceException("删除学习规划主体失败");
            }
        }

        //删除座位
        List<Long> list = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getBranchMachineSeat).filter(Objects::nonNull).map(RemoteBranchMachineSeatVo::getBranchMachineSeatId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isNotEmpty(list)) {
            Boolean flag = remoteBranchMachineSeatService.delBySeatIdList(list);
            if (!flag) {
                throw new ServiceException("删除学习规划关联座位信息失败");
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCover(List<Long> studyPlanningRecordIdList) {
        if (CollUtil.isEmpty(studyPlanningRecordIdList)) {
            return true;
        }
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        studyPlanningRecordBo.setWithBranchMachineSeatInfo(true);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryStudyPlanningRecordList(studyPlanningRecordBo);
        List<Long> studyPlanningIdList = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getStudyPlanningId).filter(Objects::nonNull).distinct().toList();
        //查出所有同主体的学习规划详情记录
        Map<Long, List<Long>> map = studyPlanningRecordService.queryIdMapByStudyPlanningIdList(studyPlanningIdList);

        //先修改记录为覆盖状态
        List<StudyPlanningRecordBo> updateList = new ArrayList<>();
        for (Long l : studyPlanningRecordIdList) {
            StudyPlanningRecordBo studyPlanningRecord = new StudyPlanningRecordBo();
            studyPlanningRecord.setStudyPlanningRecordId(l);
            studyPlanningRecord.setStudyRecordStatus(UserConstants.STUDY_RECORD_STATUS_COVER);
            updateList.add(studyPlanningRecord);
        }
        boolean b = studyPlanningRecordService.updateBatchByBo(updateList);
        if (!b) {
            return false;
        }

        //判断规划主体哪些在覆盖以后，为空了，可以删除
        List<StudyPlanning> delList = new ArrayList<>();

        for (Map.Entry<Long, List<Long>> entry : map.entrySet()) {
            List<Long> value = entry.getValue();
            value.removeIf(studyPlanningRecordIdList::contains);
            if (CollUtil.isEmpty(value)) {
                StudyPlanning studyPlanning = new StudyPlanning();
                studyPlanning.setStudyPlanningId(entry.getKey());
                studyPlanning.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_DELETE);
                delList.add(studyPlanning);
            }
        }

        if (CollUtil.isNotEmpty(delList)) {
            //删除学习规划主体
            boolean b1 = baseMapper.updateBatchById(delList);
            if (!b1) {
                throw new ServiceException("删除学习规划主体失败");
            }
        }

        //删除座位
        List<Long> list = studyPlanningRecordVos.stream().map(StudyPlanningRecordVo::getBranchMachineSeat).filter(Objects::nonNull).map(RemoteBranchMachineSeatVo::getBranchMachineSeatId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isNotEmpty(list)) {
            Boolean flag = remoteBranchMachineSeatService.delBySeatIdList(list);
            if (!flag) {
                throw new ServiceException("删除座位失败");
            }
        }


        return true;

    }


    @Override
    public TableDataInfo<StudyPlanningVo> queryStudyPlanPage(StudyPlanningBo studyPlanningBo, PageQuery pageQuery) {
        handleQueryParam(studyPlanningBo);

        LambdaQueryWrapper<StudyPlanning> lqw = buildLambdaQueryWrapper(studyPlanningBo);
        Page<StudyPlanningVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudentInfo())) {
            putStudentInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudentInfo())
            && Boolean.TRUE.equals(studyPlanningBo.getWithStudentSysUserInfo())) {
            putStudentSysUserInfo(result.getRecords());
        }

        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())) {
            putStudyPlanningRecordInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())
            && Boolean.TRUE.equals(studyPlanningBo.getWithCourseInfo())) {
            putCourseInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())
            && Boolean.TRUE.equals(studyPlanningBo.getWithCourseInfo())
            && Boolean.TRUE.equals(studyPlanningBo.getWithTopCourseInfo())) {
            putTopCourseInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())
            && Boolean.TRUE.equals(studyPlanningBo.getWithStudyRecordInfo())) {
            putStudyRecordInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(studyPlanningBo.getWithStudyPlanningRecord())
            && Boolean.TRUE.equals(studyPlanningBo.getWithFeedbackRecordInfo())) {
            putFeedbackRecordInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    @Override
    public StudyPlanningVo recodeCountAndStudentCount(StudyPlanningBo bo) {
        StudyPlanningVo studyPlanningVo = new StudyPlanningVo();
        studyPlanningVo.setStudentCount(0L);
        studyPlanningVo.setRecordCount(0L);
        handleQueryParam(bo);
        QueryWrapper<StudyPlanning> lqw = buildQueryWrapper(bo);
        List<StudyPlanningVo> studyPlanningVos = baseMapper.queryPlanAndRecordList(lqw);
        if (CollUtil.isEmpty(studyPlanningVos)) {
            return studyPlanningVo;
        }
        long studentCount = studyPlanningVos.stream().map(StudyPlanningVo::getStudentId).filter(Objects::nonNull).distinct().count();
        long recordCount = studyPlanningVos.stream().map(StudyPlanningVo::getStudyPlanningRecordList).filter(Objects::nonNull).mapToLong(List::size).sum();
        studyPlanningVo.setStudentCount(studentCount);
        studyPlanningVo.setRecordCount(recordCount);
        return studyPlanningVo;
    }

    @Override
    public List<Long> existStudyPlanningStuList(List<Long> studentIdList, String attendanceDate) {
        QueryWrapper<StudyPlanning> queryWrapper = Wrappers.query();
        queryWrapper.in("student_id", studentIdList);
        queryWrapper.eq("date(study_planning_date)", attendanceDate);
        queryWrapper.eq("study_planning_status", UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
        queryWrapper.select("student_id");
        List<StudyPlanning> studyPlannings = baseMapper.selectList(queryWrapper);
        return studyPlannings.stream().map(StudyPlanning::getStudentId).distinct().toList();
    }

    @Override
    public Long countStudentStudyTime(Long studentId, Date startDate, Date endDate) {
        return studyRecordService.countStudentStudyTime(studentId, startDate, endDate);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean updateByBoV2(StudyPlanningUpdateBoV2 bo) {

        Long studyPlanningRecordId = bo.getStudyPlanningRecordId();
        StudyPlanningRecordVo studyPlanningRecordVo = studyPlanningRecordService.queryById(studyPlanningRecordId);
        if (ObjectUtils.isEmpty(studyPlanningRecordVo)) {
            return true;
        }
        StudyPlanningVo oldStudyPlanning = queryById(studyPlanningRecordVo.getStudyPlanningId());

        Date newDate = bo.getStudyPlanningDate();
        Date oldDate = oldStudyPlanning.getStudyPlanningDate();
        Long studyPlanningId = oldStudyPlanning.getStudyPlanningId();
        Long oldStudyPlanningId = null;
        if (!DateUtils.isSameDay(newDate, oldDate)) {
            //如果日期不为同一天，则替换到新日期的学习规划日期；
            //根据学生ID获取对应的学习规划日期
            Long studentId = oldStudyPlanning.getStudentId();
            List<StudyPlanningVo> studyPlanningVos = queryStudyPlanningByDateStrList(studentId, Collections.singleton(DateUtils.dateTime(newDate)));
            //说明这个日期没有规划过学习日期，需要新建学习规划
            if (CollUtil.isEmpty(studyPlanningVos)) {
                Student student = studentService.queryStudentById(studentId);

                //先新建这个日期的学习规划主体
                StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
                studyPlanningBo.setStudentId(studentId);
                studyPlanningBo.setStudyPlanningDate(newDate);
                studyPlanningBo.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_COMPLETE);
                if (!ObjectUtils.isEmpty(student)) {
                    studyPlanningBo.setCreateBy(student.getCreateBy());
                    studyPlanningBo.setCreateDept(student.getCreateDept());
                }
                boolean flag = SpringUtils.getBean(IStudyPlanningService.class).insertByBo(studyPlanningBo);
                if (!flag) {
                    throw new ServiceException("新增学习规划详情失败");
                }
                oldStudyPlanningId = studyPlanningId;
                studyPlanningId = studyPlanningBo.getStudyPlanningId();


            } else {
                StudyPlanningVo newDateStudyPlanning = studyPlanningVos.get(0);
                studyPlanningId = newDateStudyPlanning.getStudyPlanningId();
            }
        }


        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningRecordId(studyPlanningRecordVo.getStudyPlanningRecordId());
        studyPlanningRecordBo.setStudyPlanningId(studyPlanningId);
        studyPlanningRecordBo.setStudyStartTime(bo.getStudyStartTime());
        studyPlanningRecordBo.setStudyEndTime(bo.getStudyEndTime());
        Boolean b = studyPlanningRecordService.updateByBo(studyPlanningRecordBo);
        if (Boolean.FALSE.equals(b)) {
            throw new ServiceException("更新学习规划详情失败");
        }

        //检测旧的学习规划是否没有记录了，如果没有记录则删除
        if (!ObjectUtils.isEmpty(oldStudyPlanningId)) {
            List<Long> longs = studyPlanningRecordService.queryStudyPlanningRecordIdListByPlanningIdList(Collections.singletonList(oldStudyPlanningId));
            if (CollUtil.isNotEmpty(longs)) {
                return true;
            }

            StudyPlanning studyPlanning = new StudyPlanning();
            studyPlanning.setStudyPlanningId(oldStudyPlanningId);
            studyPlanning.setStudyPlanningStatus(UserConstants.STUDY_PLANNING_STATUS_DELETE);
            //删除学习规划主体
            int b1 = baseMapper.updateById(studyPlanning);
            if (b1 <= 0) {
                throw new ServiceException("删除学习规划主体失败");
            }
        }
        return true;
    }

    private final DictService dictService;

    @Override
    public TableDataInfo<StudyPlanningVo> queryStudyPlanList(StudyPlanningBo studyPlanningBo, PageQuery pageQuery) {
        log.info("【获取学生的学习规划列表】查询参数:{}", studyPlanningBo);

        // 根据学生ID和日期 查询出这个学生的学习规划记录
        TableDataInfo<StudyPlanningVo> studyPlanningVoPageList = this.queryStudyPlanPage(studyPlanningBo, pageQuery);
        List<StudyPlanningVo> studyPlanningVoList = studyPlanningVoPageList.getRows();
        if (CollUtil.isEmpty(studyPlanningVoList)) {
            return TableDataInfo.build();
        }

        List<Long> studyPlanningIds = studyPlanningVoList.stream().map(StudyPlanningVo::getStudyPlanningId).toList();

        // 根据学习规划记录查询出详情信息
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIds);
        List<StudyPlanningRecordVo> studyPlanningRecordVoList = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        List<Long> courseIds = studyPlanningRecordVoList.stream().map(StudyPlanningRecordVo::getCourseId).toList();
        List<CourseVo> coursesList = courseService.getCourseInfoWithParentNameByCourseId(courseIds);

        // 设置学科信息
        coursesList.forEach(courseVo -> courseVo.setAffiliationSubjectName(dictService.getDictLabel(CourseLabelDictEnum.COURSE_AFFILIATION_SUBJECT.getCode(), courseVo.getAffiliationSubject())));
        Map<Long, CourseVo> collect = coursesList.stream().collect(Collectors.toMap(CourseVo::getCourseId, Function.identity()));

        // 添加课程信息
        studyPlanningRecordVoList.forEach(studyPlanningRecordVo -> studyPlanningRecordVo.setCourse(collect.get(studyPlanningRecordVo.getCourseId())));
        Map<Long, List<StudyPlanningRecordVo>> listMap = studyPlanningRecordVoList.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getStudyPlanningId, Collectors.toList()));
        studyPlanningVoList.forEach(studyPlanningVo -> studyPlanningVo.setStudyPlanningRecordList(listMap.get(studyPlanningVo.getStudyPlanningId())));

        log.info("学生的学习规划：{}", studyPlanningRecordVoList);

        return studyPlanningVoPageList;
    }

    /**
     * 放入反馈信息
     *
     * @param list
     */
    private void putFeedbackRecordInfo(List<StudyPlanningVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        list.parallelStream().forEach(studyPlanningVo -> {

            List<StudyPlanningRecordVo> records = studyPlanningVo.getStudyPlanningRecordList();
            if (null != records) {

                TreeMap<Date, Integer> treeMap = new TreeMap<>();
                records.forEach(record -> {
                    Date studyStartTime = DateUtils.parseDate(DateUtils.dateTime(studyPlanningVo.getStudyPlanningDate()) + " " + DateUtils.getTime(record.getStudyStartTime()));
                    Date studyEndTime = DateUtils.parseDate(DateUtils.dateTime(studyPlanningVo.getStudyPlanningDate()) + " " + DateUtils.getTime(record.getStudyEndTime()));

                    treeMap.put(studyStartTime, 1);
                    treeMap.put(studyEndTime, 1);
                });

                List<Date> keyList = new ArrayList<>(treeMap.keySet());
                if (null != keyList && keyList.size() > 1) {
                } else {
                    return;
                }

                //拿出最大日期和最小日期
                Date maxDate = keyList.get(0);
                Date minDate = keyList.get(keyList.size() - 1);

                if (null != maxDate && null != minDate) {
                    List<Long> studentIdList = records.stream().map(StudyPlanningRecordVo::getStudentId).distinct().toList();
                    //按照时间区间查找
                    FeedbackRecordBo feedbackRecordBo = new FeedbackRecordBo();
                    feedbackRecordBo.setFeedbackStartDateLimit(minDate);
                    feedbackRecordBo.setFeedbackEndDate(maxDate);
                    feedbackRecordBo.setStudentIdList(studentIdList);
                    List<FeedbackRecordVo> feedbackRecordVoList = feedbackRecordService.queryList(feedbackRecordBo);
                    studyPlanningVo.setFeedbackRecordList(feedbackRecordVoList);

                }
            }
        });
    }

    /**
     * 放入学习记录
     *
     * @param list
     */
    private void putStudyRecordInfo(List<StudyPlanningVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<StudyPlanningRecordVo> records = list.stream().map(StudyPlanningVo::getStudyPlanningRecordList).filter(Objects::nonNull).flatMap(List::stream).toList();
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studyPlanningRecordIdList = records.stream().map(StudyPlanningRecordVo::getStudyPlanningRecordId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studyPlanningRecordIdList)) {
            return;
        }
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        List<StudyRecordVo> studyRecordVos = studyRecordService.queryList(studyRecordBo);
        if (CollUtil.isEmpty(studyRecordVos)) {
            return;
        }

        //查知识点对应的视频资源
        //List<Long> knowledgeIdList = records.stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).map(CourseVo::getKnowledgeId).filter(Objects::nonNull).distinct().toList();
        //RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        //remoteKnowledgeVideoBo.setKnowledgeIdList(knowledgeIdList);
        //List<RemoteKnowledgeVideoVo> knowledgeVideoList = remoteExtVideoService.getKnowledgeVideoList(remoteKnowledgeVideoBo);
        //Map<Long, RemoteVideoVo> knowledgeVideoMap = new HashMap<>();
        //if (CollUtil.isNotEmpty(knowledgeVideoList)) {
        //    for (RemoteKnowledgeVideoVo remoteKnowledgeVideoVo : knowledgeVideoList) {
        //        List<RemoteVideoVo> videoList = remoteKnowledgeVideoVo.getVideoList();
        //        if (CollUtil.isEmpty(videoList)) {
        //            continue;
        //        }
        //        //客户接口问题，只取第一个视频
        //        RemoteVideoVo remoteVideoVo = videoList.get(0);
        //        knowledgeVideoMap.put(remoteKnowledgeVideoVo.getKnowledgeId(), remoteVideoVo);
        //    }
        //}

        Map<Long, StudyRecordVo> studyRecordVoMap = studyRecordVos.stream().collect(Collectors.toMap(StudyRecordVo::getStudyPlanningRecordId, vo -> vo));
        records.forEach(record -> {
            Long studyPlanningRecordId = record.getStudyPlanningRecordId();
            StudyRecordVo studyRecordVo = studyRecordVoMap.get(studyPlanningRecordId);
            if (studyRecordVo == null) {
                return;
            }
            record.setStudyRecord(studyRecordVo);
            //CourseVo course = record.getCourse();
            //if (course == null || course.getKnowledgeId() == null) {
            //    return;
            //}
            //RemoteVideoVo remoteVideoVo = knowledgeVideoMap.get(course.getKnowledgeId());
            //record.setVideo(remoteVideoVo);
        });
    }

    /**
     * 放入课次的顶级课程信息
     *
     * @param records
     */
    private void putTopCourseInfo(List<StudyPlanningVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudyPlanningRecordVo> studyPlanningRecordVoList = records.stream().map(StudyPlanningVo::getStudyPlanningRecordList).filter(Objects::nonNull).flatMap(List::stream).toList();
        if (CollUtil.isEmpty(studyPlanningRecordVoList)) {
            return;
        }

        List<CourseVo> collect = studyPlanningRecordVoList.stream().map(StudyPlanningRecordVo::getCourse).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return;
        }
        courseService.putTopmostCourseInfo(collect, true);
        //List<CourseVo> topmostCourse = collect.stream().map(CourseVo::getTopmostCourse).filter(Objects::nonNull).distinct().toList();
        //courseService.putCourseDetail(topmostCourse,false);
    }

    /**
     * 放入课次的课程信息
     *
     * @param recordList
     */
    public void putCourseInfo(List<StudyPlanningVo> recordList) {
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        List<StudyPlanningRecordVo> studyPlanningRecordVoList = recordList.stream().map(StudyPlanningVo::getStudyPlanningRecordList).filter(Objects::nonNull).flatMap(List::stream).toList();
        if (CollUtil.isEmpty(studyPlanningRecordVoList)) {
            return;
        }
        List<Long> courseIdList = studyPlanningRecordVoList.stream().map(StudyPlanningRecordVo::getCourseId).distinct().collect(Collectors.toList());
        CourseBo courseBo = new CourseBo();
        courseBo.setCourseIdList(courseIdList);
        List<CourseVo> courseVos = courseService.queryList(courseBo);
        if (CollUtil.isEmpty(courseVos)) {
            return;
        }
        Map<Long, CourseVo> courseMap = courseVos.stream().collect(Collectors.toMap(CourseVo::getCourseId, vo -> vo));
        studyPlanningRecordVoList.forEach(record -> {
            CourseVo courseVo = courseMap.get(record.getCourseId());
            record.setCourse(courseVo);
        });
    }


    /**
     * 放入学习规划详情信息
     *
     * @param records
     */
    private void putStudyPlanningRecordInfo(List<StudyPlanningVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studyPlanningIdList = records.stream().map(StudyPlanningVo::getStudyPlanningId).filter(Objects::nonNull).toList();
        StudyPlanningRecordBo studyPlanningRecordBo = new StudyPlanningRecordBo();
        studyPlanningRecordBo.setStudyPlanningIdList(studyPlanningIdList);
        List<StudyPlanningRecordVo> studyPlanningRecordVos = studyPlanningRecordService.queryList(studyPlanningRecordBo);
        Map<Long, List<StudyPlanningRecordVo>> map = studyPlanningRecordVos.stream().collect(Collectors.groupingBy(StudyPlanningRecordVo::getStudyPlanningId));
        records.forEach(record -> {
            record.setStudyPlanningRecordList(map.get(record.getStudyPlanningId()));
        });
    }

    /**
     * 放入会员系统用户信息
     *
     * @param records
     */
    private void putStudentSysUserInfo(List<StudyPlanningVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<StudentVo> studentVos = records.stream().map(StudyPlanningVo::getStudent).filter(Objects::nonNull).toList();

        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> sysUserIdList = studentVos.stream().map(StudentVo::getCreateBy).collect(Collectors.toList());
        sysUserIdList.remove(null);
        if (CollUtil.isEmpty(sysUserIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(sysUserIdList);
        remoteUserBo.setGetAvatarUrl(true);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, vo -> vo));
        studentVos.forEach(studentVo -> {
            RemoteUserVo remoteUserVo = remoteUserVoMap.get(studentVo.getCreateBy());
            studentVo.setSysUser(remoteUserVo);
        });
    }

    /**
     * 放入会员信息
     *
     * @param records
     */
    private void putStudentInfo(List<StudyPlanningVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(StudyPlanningVo::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        Map<Long, StudentVo> studentVoMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));
        records.forEach(record -> {
            Long studentId = record.getStudentId();
            StudentVo studentVo = studentVoMap.get(studentId);
            record.setStudent(studentVo);
        });
    }


    private void handleQueryParam(StudyPlanningBo record) {
        if (record.getStudentId() != null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }

        }
    }


}


























