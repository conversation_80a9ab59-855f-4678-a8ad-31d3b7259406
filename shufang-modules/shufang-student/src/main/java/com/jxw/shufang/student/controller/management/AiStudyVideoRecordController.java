package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyVideoRecordVo;
import com.jxw.shufang.student.service.IAiStudyVideoRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ai学习视频记录（视频观看记录）
 * 前端访问路由地址为:/student/aiStudyVideoRecord
 *
 *
 * @date 2024-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiStudyVideoRecord")
public class AiStudyVideoRecordController extends BaseController {

    private final IAiStudyVideoRecordService aiStudyVideoRecordService;

    /**
     * 查询ai学习视频记录（视频观看记录）列表
     */
    @SaCheckPermission("student:aiStudyVideoRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiStudyVideoRecordVo> list(AiStudyVideoRecordBo bo, PageQuery pageQuery) {
        return aiStudyVideoRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai学习视频记录（视频观看记录）列表
     */
    @SaCheckPermission("student:aiStudyVideoRecord:export")
    @Log(title = "ai学习视频记录（视频观看记录）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiStudyVideoRecordBo bo, HttpServletResponse response) {
        List<AiStudyVideoRecordVo> list = aiStudyVideoRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai学习视频记录（视频观看记录）", AiStudyVideoRecordVo.class, response);
    }

    /**
     * 获取ai学习视频记录（视频观看记录）详细信息
     *
     * @param aiStudyVideoRecordId 主键
     */
    @SaCheckPermission("student:aiStudyVideoRecord:query")
    @GetMapping("/{aiStudyVideoRecordId}")
    public R<AiStudyVideoRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiStudyVideoRecordId) {
        return R.ok(aiStudyVideoRecordService.queryById(aiStudyVideoRecordId));
    }

    /**
     * 新增ai学习视频记录（视频观看记录）
     */
    @SaCheckPermission("student:aiStudyVideoRecord:add")
    @Log(title = "ai学习视频记录（视频观看记录）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiStudyVideoRecordBo bo) {
        return toAjax(aiStudyVideoRecordService.insertByBo(bo));
    }

    /**
     * 修改ai学习视频记录（视频观看记录）
     */
    @SaCheckPermission("student:aiStudyVideoRecord:edit")
    @Log(title = "ai学习视频记录（视频观看记录）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiStudyVideoRecordBo bo) {
        return toAjax(aiStudyVideoRecordService.updateByBo(bo));
    }

    /**
     * 删除ai学习视频记录（视频观看记录）
     *
     * @param aiStudyVideoRecordIds 主键串
     */
    @SaCheckPermission("student:aiStudyVideoRecord:remove")
    @Log(title = "ai学习视频记录（视频观看记录）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiStudyVideoRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiStudyVideoRecordIds) {
        return toAjax(aiStudyVideoRecordService.deleteWithValidByIds(List.of(aiStudyVideoRecordIds), true));
    }
}
