package com.jxw.shufang.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StuCourseResourceCollect;
import com.jxw.shufang.student.domain.bo.StuCourseResourceCollectBo;
import com.jxw.shufang.student.domain.vo.StuCourseResourceCollectVo;
import com.jxw.shufang.student.mapper.StuCourseResourceCollectMapper;
import com.jxw.shufang.student.service.IStuCourseResourceCollectService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 会员对课程资源的收藏Service业务层处理
 *
 *
 * @date 2024-05-13
 */
@RequiredArgsConstructor
@Service
public class StuCourseResourceCollectServiceImpl implements IStuCourseResourceCollectService, BaseService {

    private final StuCourseResourceCollectMapper baseMapper;

    /**
     * 查询会员对课程资源的收藏
     */
    @Override
    public StuCourseResourceCollectVo queryById(Long resourceCollectId){
        return baseMapper.selectVoById(resourceCollectId);
    }

    /**
     * 查询会员对课程资源的收藏列表
     */
    @Override
    public TableDataInfo<StuCourseResourceCollectVo> queryPageList(StuCourseResourceCollectBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StuCourseResourceCollect> lqw = buildQueryWrapper(bo);
        Page<StuCourseResourceCollectVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员对课程资源的收藏列表
     */
    @Override
    public List<StuCourseResourceCollectVo> queryList(StuCourseResourceCollectBo bo) {
        LambdaQueryWrapper<StuCourseResourceCollect> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StuCourseResourceCollect> buildQueryWrapper(StuCourseResourceCollectBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StuCourseResourceCollect> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getResourceCollectId() != null, StuCourseResourceCollect::getResourceCollectId, bo.getResourceCollectId());
        lqw.eq(bo.getCourseResourceId() != null, StuCourseResourceCollect::getCourseResourceId, bo.getCourseResourceId());
        lqw.eq(bo.getStudentId() != null, StuCourseResourceCollect::getStudentId, bo.getStudentId());
        return lqw;
    }

    /**
     * 新增会员对课程资源的收藏
     */
    @Override
    public Boolean insertByBo(StuCourseResourceCollectBo bo) {
        StuCourseResourceCollect add = MapstructUtils.convert(bo, StuCourseResourceCollect.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setResourceCollectId(add.getResourceCollectId());
        }
        return flag;
    }

    /**
     * 修改会员对课程资源的收藏
     */
    @Override
    public Boolean updateByBo(StuCourseResourceCollectBo bo) {
        StuCourseResourceCollect update = MapstructUtils.convert(bo, StuCourseResourceCollect.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StuCourseResourceCollect entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员对课程资源的收藏
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
