package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 打印记录对象 print_record
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("print_record")
public class PrintRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 打印id
     */
    @TableId(value = "print_record_id")
    private Long printRecordId;

    /**
     * 打印类型（1单份 2合并）
     */
    private String printType;

    /**
     * 来源课程id
     */
    private Long courseId;

    /**
     * 打印内容,KnowledgeResourceType枚举，多个用数组分开
     */
    private String printContent;

    /**
     * 学习规划Id
     */
    private Long studyPlanRecordId;

    /**
     * 会员ID
     */
    private Long studentId;

    /**
     * 试卷id,打印试卷时会用得上
     */
    private Long paperId;

    /**
     * 1代表原卷，2代表解析卷，3代表原卷+解析卷
     */
    private String paperType;


}
