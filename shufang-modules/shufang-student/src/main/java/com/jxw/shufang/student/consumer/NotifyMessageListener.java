package com.jxw.shufang.student.consumer;

import com.alibaba.fastjson.JSONObject;
import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.student.domain.dto.RemoteMessageDTO;
import com.jxw.shufang.student.service.IRemoteMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = NoticeMessageConstants.NOTICE_INTERNAL_TOPIC,
    consumerGroup = NoticeMessageConstants.NOTICE_INTERNAL_CONSUMER_GROUP,
    messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.CONCURRENTLY
)
public class NotifyMessageListener implements RocketMQListener<String> {

    private final IRemoteMessageService remoteMessageService;

    @Override
    public void onMessage(String s) {
        log.info("发送通知消息{}", s);
        try {
            RemoteMessageDTO remoteMessageDTO = JSONObject.parseObject(s, RemoteMessageDTO.class);
            remoteMessageService.sendMessage(remoteMessageDTO);
        } catch (Exception e) {
            log.error("通知消息发送失败{}", s, e);
        }

    }
}
