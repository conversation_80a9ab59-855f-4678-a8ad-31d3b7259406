package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 课程专题，包含课程树
 * @date 2024-02-29
 */
@Data
public class SpecialTopicCourseV2Vo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专题(对应字典course_special_topic)
     */
    private String specialTopic;

    /**
     * 专题类型
     */
    private String specialType;

    private String specialTopicName;

    /**
     * 课程列表
     */
    private List<CourseVo> courseList;
}
