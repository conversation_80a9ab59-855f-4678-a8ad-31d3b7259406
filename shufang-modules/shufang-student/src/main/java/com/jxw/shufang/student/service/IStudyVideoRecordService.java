package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.BatchQueryVideoRecordDTO;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordStatisticVo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 学习视频记录（视频观看记录）Service接口
 *
 *
 * @date 2024-05-06
 */
public interface IStudyVideoRecordService {

    /**
     * 查询学习视频记录（视频观看记录）
     */
    StudyVideoRecordVo queryById(Long studyVideoRecordId);

    /**
     * 查询学习视频记录（视频观看记录）列表
     */
    TableDataInfo<StudyVideoRecordVo> queryPageList(StudyVideoRecordBo bo, PageQuery pageQuery);

    /**
     * 查询学习视频记录（视频观看记录）列表
     */
    List<StudyVideoRecordVo> queryList(StudyVideoRecordBo bo);

    /**
     * 新增学习视频记录（视频观看记录）
     */
    Boolean insertByBo(StudyVideoRecordBo bo);

    /**
     * 修改学习视频记录（视频观看记录）
     */
    Boolean updateByBo(StudyVideoRecordBo bo);

    /**
     * 校验并批量删除学习视频记录（视频观看记录）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询最后一天记录
     *
     * @param studentId             学生id
     * @param studyPlanningRecordId 研究计划记录id
     *
     * @date 2024/05/06 11:18:25
     */
    StudyVideoRecordVo queryLastDayRecord(Long studentId, Long studyPlanningRecordId);

    /**
     * 学习记录统计数据，包含累计播放，近30天播放，近7天播放
     * @return
     */
    StudyVideoRecordStatisticVo videoRecordStatistic(Long studentId);

    /**
     * 查询学生最后一次学习视频记录
     * @param studyPlanningRecordIdList
     * @param showStudyVideoSlices
     * @return
     */
    List<StudyVideoRecordVo> queryLastStudyVideoRecord(List<Long> studyPlanningRecordIdList, boolean showStudyVideoSlices);


    boolean saveBatch(List<StudyVideoRecord> insertList);

    boolean updateBatchById(List<StudyVideoRecord> updateList);

    Long queryStudyDaysByStudentId(Long studentId);

    StudyVideoRecordVo queryOne(StudyVideoRecordBo studyVideoRecordBo);

    Long count(StudyVideoRecordBo convert);

    List<StudyVideoRecord> batchQueryRecord(List<BatchQueryVideoRecordDTO> recordList);

    void batchUpdate(List<StudyVideoRecord> updates);

    void batchInsert(List<StudyVideoRecord> inserts);

    Long accumulateStudyVideo(StudyVideoRecordBo convert);
}
