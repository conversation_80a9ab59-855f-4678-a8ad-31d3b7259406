package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 学生学习计划各科完成度
 *
 *
 * @date 2024-05-19
 */
@Data
public class SubjectCompleteVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 科目（来自字典course_affiliation_subject，需调用字典接口翻译成label）
     */
    private String affiliationSubject;


    /**
     * 完成度，1-0
     */
    private BigDecimal completionRatio;
}
