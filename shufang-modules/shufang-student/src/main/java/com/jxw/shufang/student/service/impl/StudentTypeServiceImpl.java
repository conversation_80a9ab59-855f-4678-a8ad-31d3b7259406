package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentProductTemplateVo;
import com.jxw.shufang.student.domain.StudentType;
import com.jxw.shufang.student.domain.bo.ProductBo;
import com.jxw.shufang.student.domain.bo.StudentTypeBo;
import com.jxw.shufang.student.domain.vo.ProductVo;
import com.jxw.shufang.student.domain.vo.StudentTypeVo;
import com.jxw.shufang.student.mapper.StudentTypeMapper;
import com.jxw.shufang.student.service.IProductService;
import com.jxw.shufang.student.service.IStudentTypeService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）Service业务层处理
 *
 *
 * @date 2024-03-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentTypeServiceImpl implements IStudentTypeService, BaseService {

    private final StudentTypeMapper baseMapper;

    private final IProductService productService;

    @DubboReference
    private RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @Override
    public StudentTypeVo queryById(Long studentTypeId) {
        return baseMapper.selectVoById(studentTypeId);
    }

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    @Override
    public TableDataInfo<StudentTypeVo> queryPageList(StudentTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentType> lqw = buildQueryWrapper(bo);
        lqw.orderByAsc(StudentType::getStudentTypeSort);
        Page<StudentTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        putIsDefault(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void putIsDefault(List<StudentTypeVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        String[] defaultStudentTypeArr = UserConstants.DEFAULT_STUDENT_TYPE_ARR;
        List<String> list = Arrays.asList(defaultStudentTypeArr);
        for (StudentTypeVo record : records) {
            record.setIsDefault(list.contains(record.getStudentTypeName()));
        }
    }

    /**
     * 查询会员类型（会员卡的类型，默认有一个 体验卡 类型）列表
     */
    @Override
    public List<StudentTypeVo> queryList(StudentTypeBo bo) {
        LambdaQueryWrapper<StudentType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentType> buildQueryWrapper(StudentTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentType> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getStudentTypeName()), StudentType::getStudentTypeName, bo.getStudentTypeName());
        lqw.eq(bo.getStudentTypeSort() != null, StudentType::getStudentTypeSort, bo.getStudentTypeSort());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentTypeIds()), StudentType::getStudentTypeId, bo.getStudentTypeIds());
        return lqw;
    }

    /**
     * 新增会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @Override
    public Boolean insertByBo(StudentTypeBo bo) {
        StudentType add = MapstructUtils.convert(bo, StudentType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentTypeId(add.getStudentTypeId());
        }
        return flag;
    }

    /**
     * 修改会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @Override
    public Boolean updateByBo(StudentTypeBo bo) {
        StudentType update = MapstructUtils.convert(bo, StudentType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentType entity) {
        //做一些数据校验,如唯一约束
        //名字唯一性确认
        LambdaQueryWrapper<StudentType> lqw = Wrappers.lambdaQuery();
        lqw.eq(StudentType::getStudentTypeName, entity.getStudentTypeName());
        lqw.ne(entity.getStudentTypeId() != null, StudentType::getStudentTypeId, entity.getStudentTypeId());
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("会员类型名称已存在");
        }


    }

    /**
     * 批量删除会员类型（会员卡的类型，默认有一个 体验卡 类型）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<StudentTypeVo> options(StudentTypeBo bo) {
        return getStudentTypesByAuth(bo, true);
    }

    public void putProductList(List<StudentTypeVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentTypeIdList = list.stream().map(StudentTypeVo::getStudentTypeId).collect(Collectors.toList());
        ProductBo productBo = new ProductBo();
        productBo.setStudentTypeIdList(studentTypeIdList);
        productBo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
        List<ProductVo> productList = productService.queryList(productBo);
        Map<Long, List<ProductVo>> productMap = productList.stream().collect(Collectors.groupingBy(ProductVo::getStudentTypeId));
        list.forEach(item -> {
            item.setProductList(productMap.get(item.getStudentTypeId()));
        });

    }


    @Cacheable(value = "studentType", key = "#studentTypeId", condition = "#studentTypeId != null")
    @Override
    public StudentType queryStudentTypeById(Long studentTypeId) {
        return baseMapper.selectById(studentTypeId);
    }

    @CacheEvict(value = "studentType", allEntries = true)
    public void cleanCache() {
        log.info("===========studentTypeService cleanCache===========");
    }

    @Override
    public StudentTypeVo getExperienceStudentType() {
        LambdaQueryWrapper<StudentType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StudentType::getStudentTypeName, "体验卡");
        return baseMapper.selectVoOne(wrapper);
    }

    private void getAllProduct(StudentTypeBo bo, List<StudentTypeVo> studentTypeVos, List<Long> productIdList) {
        ProductBo allProductBo = new ProductBo();
        allProductBo.setStudentTypeIdList(studentTypeVos.stream().map(StudentTypeVo::getStudentTypeId).collect(Collectors.toList()));
        allProductBo.setProductIdList(productIdList);
        allProductBo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
        List<ProductVo> productList = productService.queryList(allProductBo);
        Map<Long, List<ProductVo>> productMap = productList.stream().collect(Collectors.groupingBy(ProductVo::getStudentTypeId));
        studentTypeVos.forEach(item -> {
            item.setProductList(productMap.get(item.getStudentTypeId()));
        });
    }

    @Override
    public List<StudentTypeVo> auth(StudentTypeBo bo) {
        return getStudentTypesByAuth(bo, false);
    }

    /**
     * 根据授权信息获取会员类型列表的通用方法
     * @param bo 查询条件
     * @param checkBranchUser 是否检查分店用户权限
     * @return 会员类型列表
     */
    private List<StudentTypeVo> getStudentTypesByAuth(StudentTypeBo bo, boolean checkBranchUser) {
        List<ProductVo> productList = new ArrayList<>();

        // 根据checkBranchUser参数决定是否检查分店用户权限
        if (!checkBranchUser || !LoginHelper.isBranchUser()) {
            //获取可授权的产品ID
            //获取授权的的产品+所属子机构生产的所有的产品
            //获取所有下属子机构
            if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
                ProductBo productBo = new ProductBo();
                productBo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
                productList = productService.queryList(productBo);
            } else {
                //获取所有子机构生产的产品ID
                List<RemoteDeptVo> deptList = remoteDeptService.getDeptChildrenList(LoginHelper.getDeptId());
                if (CollUtil.isNotEmpty(deptList)) {
                    List<Long> deptIdList = new ArrayList<>(deptList.stream().map(RemoteDeptVo::getDeptId).toList());
                    deptIdList.add(LoginHelper.getDeptId());
                    ProductBo productBo = new ProductBo();
                    productBo.setProductStatus(UserConstants.PRODUCT_STATUS_UP);
                    productBo.setDeptIdList(deptIdList);
                    productList = productService.queryList(productBo);
                }
            }
        }
        //获取所属子机构生产的所有上架的产品
        List<Long> productIdList = new ArrayList<>();

        if (CollUtil.isNotEmpty(productList)) {
            productList.stream()
                .map(ProductVo::getProductId)
                .forEach(productIdList::add);
        }

        //2B2C合并，授权模式更改，根据门店ID，查询该门店上级代理商的会员卡授权模板对象
        RemoteStudentProductTemplateAuthBo remoteStudentProductTemplateAuthBo = new RemoteStudentProductTemplateAuthBo();
        remoteStudentProductTemplateAuthBo.setTemplateType(0);//会员卡类型的授权模板
        //获取上级代理商ID
        //如果当前是门店用户，则获取上级代理商ID

        boolean b = remoteDeptService.deptIsShop(LoginHelper.getDeptId());
        if (Boolean.TRUE.equals(b)){
            RemoteDeptVo preDeptByDeptId = remoteDeptService.getPreDeptByDeptId(LoginHelper.getSelectDeptId());
            remoteStudentProductTemplateAuthBo.setDeptIds(Collections.singletonList(preDeptByDeptId.getDeptId()));
        }else{
            remoteStudentProductTemplateAuthBo.setDeptIds(Collections.singletonList(LoginHelper.getDeptId()));//所属代理商ID
        }


        remoteStudentProductTemplateAuthBo.setShowAuth(true);//只返回授权的产品ID
        List<RemoteStudentProductTemplateVo> remoteStudentBaseTemplateAuthVos = remoteOrderService.queryAuthList(remoteStudentProductTemplateAuthBo);

        log.info("查询到上级代理商授权模板对象集合：{}", remoteStudentBaseTemplateAuthVos);
        if (ObjectUtil.isNotEmpty(remoteStudentBaseTemplateAuthVos)) {
            remoteStudentBaseTemplateAuthVos.stream()
                .map(RemoteStudentProductTemplateVo::getResIds)
                .filter(ObjectUtil::isNotEmpty)
                .flatMap(Collection::stream)
                .forEach(productIdList::add);
        }

        log.info("获得产品ID集合：{}", productIdList);
        if (CollUtil.isEmpty(productIdList)){
            return List.of();
        }
        //去重productIdList
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        //查这些产品对应的会员类型
        List<Long> studentTypeIdList = productService.queryStudentTypeIdByProductIds(productIdList);
        if (CollUtil.isEmpty(studentTypeIdList)) {
            return List.of();
        }
        bo.setStudentTypeIds(studentTypeIdList);

        LambdaQueryWrapper<StudentType> lqw = buildQueryWrapper(bo);
        List<StudentTypeVo> studentTypeVos = DataPermissionHelper.ignore(() -> baseMapper.selectVoList(lqw));
        if (CollUtil.isNotEmpty(studentTypeVos)) {
            //按照字段studentTypeSort从小到大排序
            studentTypeVos.sort((o1, o2) -> {
                if (o1.getStudentTypeSort() == null) {
                    return 1;
                }
                if (o2.getStudentTypeSort() == null) {
                    return -1;
                }
                return o1.getStudentTypeSort().compareTo(o2.getStudentTypeSort());
            });
        }
        getAllProduct(bo, studentTypeVos, productIdList);
        return studentTypeVos;
    }

    @Override
    public void init() {
        IStudentTypeService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentTypeService init===========");
        LambdaQueryWrapper<StudentType> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentType::getStudentTypeId);
        List<StudentType> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========studentTypeService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentTypeById(item.getStudentTypeId());
        });
        log.info("===========studentTypeService init end===========");
    }

}
