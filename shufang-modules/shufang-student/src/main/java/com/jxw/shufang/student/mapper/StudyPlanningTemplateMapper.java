package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudyPlanningTemplate;
import com.jxw.shufang.student.domain.vo.StudyPlanningTemplateVo;

/**
 * 学习规划模板Mapper接口
 *
 *
 * @date 2024-06-14
 */
public interface StudyPlanningTemplateMapper extends BaseMapperPlus<StudyPlanningTemplate, StudyPlanningTemplateVo> {

    StudyPlanningTemplateVo selectByTemplateId(Long studyPlanningTemplateId, Boolean withTemplateInfo);

    Page<StudyPlanningTemplateVo> selectTemplatePage(@Param("page") Page<StudyPlanningTemplate> build,@Param(Constants.WRAPPER) QueryWrapper<StudyPlanningTemplate> lqw);
}
