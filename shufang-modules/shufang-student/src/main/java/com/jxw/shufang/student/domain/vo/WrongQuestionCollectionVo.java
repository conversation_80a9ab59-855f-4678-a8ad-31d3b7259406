package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.WrongQuestionCollection;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 错题合集视图对象 wrong_question_collection
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WrongQuestionCollection.class)
public class WrongQuestionCollectionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题合集id
     */
    @ExcelProperty(value = "错题合集id")
    private Long wrongQuestionCollectionId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 错题合集状态 0-未订正 1-已订正
     */
    @ExcelProperty(value = "错题合集状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wrong_collection_status")
    private Integer collectionStatus;

    /**
     * 错题合集类型 1-打印 2-上周错题汇总
     */
    @ExcelProperty(value = "错题合集类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wrong_collection_type")
    private Integer collectionType;

    /**
     * 订正截图（oss_id，多个，逗号隔开）
     */
    @ExcelProperty(value = "订正截图")
    private String reviseScreenshots;

    /**
     * 错题评语
     */
    @ExcelProperty(value = "错题评语")
    private String remark;

    /**
     * 错题数量
     */
    private Integer questionCount;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

}
