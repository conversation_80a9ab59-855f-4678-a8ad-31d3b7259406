package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 学习规划记录视图对象 study_planning_record
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
public class StudyPlanningRecordExcelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "日期")
    private String studyDate;
    @ExcelProperty(value = "会员顾问")
    private String staff;
    @ExcelProperty(value = "学习时间")
    private String studyTime;
    @ExcelProperty(value = "学习内容")
    private String studyContent;
    @ExcelProperty(value = "内容信息")
    private String courseDetail;

}
