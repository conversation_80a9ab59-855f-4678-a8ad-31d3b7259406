package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/1 21:43
 * @Version 1
 * @Description 学习规划-预习学习时长入参
 */
@Data
public class PreviewStudyDurationTimeBO {
    /**
     * 学习规划ID
     */
    @NotNull(message = "学习规划ID不能为空")
    private Long studyPlanningRecordId;
    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    /**
     * 视频ID
     */
    @NotNull(message = "视频ID不能为空")
    private Long videoId;

    /**
     * 停留页面时长
     */
    @NotNull(message = "停留页面时长不能为空")
    private Long stayPageTime;
}
