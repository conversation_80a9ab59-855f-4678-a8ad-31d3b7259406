package com.jxw.shufang.student.listener;

import java.util.ArrayList;
import java.util.List;

public class GenerateCmd {
	public static List<String> cmd() {
		List<String> cmds = new ArrayList<>();

        //官方demo里面没有这个文件，经过测试，这里不执行也没问题
		//StringBuilder cmdTemp = new StringBuilder();
        //StringBuilder sb = new StringBuilder();
		//int count = 1;
		//try {
		//	FileInputStream fis = new FileInputStream(new File("d://zkteco.txt"));
		//	BufferedReader br = new BufferedReader(new InputStreamReader(fis));
		//	String line = "";
		//	while((line = br.readLine())!=null) {
		//		if(line.equals("")) {
		//			//cmdTemp.append("\r\n");
		//			System.out.println(cmdTemp);
		//			cmds.add(cmdTemp.toString());
		//			cmdTemp.setLength(0);
		//			//System.out.println(1);
		//			sb.append("\r\n");
		//		}else {
		//			if(!format(line)){
		//				cmdTemp.append("C:"+(count++)+":"+line+"\r\n");
		//				//sb.append("C:"+(count++)+":"+line+"\r\n");
		//			}else{
		//				cmdTemp.append(line+"\r\n");
        //
		//				sb.append(line);
		//			}
		//			//System.out.println(line);
		//		}
		//	}
		//	//System.out.println(sb);
		//}catch (Exception e) {
		//}
		//sb.append("\r\n");
		cmds.add("DATA DELETE multimcard *\r\n");

		System.out.println(cmds.size());
		return cmds;
	}

	private static boolean format(String line) {
		return line.startsWith("ID")||line.startsWith("Number")||line.startsWith("Reader");
	}

	public static void main(String[] args) {
		cmd();
	}
}
