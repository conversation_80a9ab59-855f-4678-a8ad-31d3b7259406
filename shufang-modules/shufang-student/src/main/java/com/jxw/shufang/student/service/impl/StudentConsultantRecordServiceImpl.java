package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.student.domain.dto.StudentWithConsultantDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.StudentConsultantRecord;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentConsultantRecordBo;
import com.jxw.shufang.student.domain.vo.StudentConsultantRecordVo;
import com.jxw.shufang.student.mapper.StudentConsultantRecordMapper;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudentService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员顾问记录（时间逆序的最后一条记录和会员中的对应）Service业务层处理
 *
 *
 * @date 2024-02-29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class StudentConsultantRecordServiceImpl implements IStudentConsultantRecordService, BaseService {

    private final StudentConsultantRecordMapper baseMapper;

    private final IStudentService studentService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public StudentConsultantRecordVo queryById(Long studentConsultantRecordId){
        return baseMapper.selectVoById(studentConsultantRecordId);
    }

    @Override
    public List<StudentConsultantRecordVo> queryByIdList(List<Long> studentConsultantRecordIdList) {
        return baseMapper.selectVoBatchIds(studentConsultantRecordIdList);
    }

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public TableDataInfo<StudentConsultantRecordVo> queryPageList(StudentConsultantRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentConsultantRecord> lqw = buildQueryWrapper(bo);
        Page<StudentConsultantRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithStaffInfo())) {
           putStaffInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void putStaffInfo(List<StudentConsultantRecordVo> records) {
        if (CollUtil.isEmpty(records)){
            return;
        }
        List<Long> staffIdList = records.stream().map(StudentConsultantRecordVo::getStudentConsultantId).collect(Collectors.toList());
        staffIdList.remove(null);
        if (CollUtil.isEmpty(staffIdList)){
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(staffIdList);
        remoteStaffBo.setWithSysUserInfo(true);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)){
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, Function.identity()));
        records.forEach(e -> {
            if (e.getStudentConsultantId() != null){
                e.setStaff(remoteStaffVoMap.get(e.getStudentConsultantId()));
            }
        });



    }

    /**
     * 查询会员顾问记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @Override
    public List<StudentConsultantRecordVo> queryList(StudentConsultantRecordBo bo) {
        LambdaQueryWrapper<StudentConsultantRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentConsultantRecord> buildQueryWrapper(StudentConsultantRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentConsultantRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentConsultantRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getStudentConsultantId() != null, StudentConsultantRecord::getStudentConsultantId, bo.getStudentConsultantId());
        lqw.eq(StringUtils.isNotBlank(bo.getRecordRemark()), StudentConsultantRecord::getRecordRemark, bo.getRecordRemark());
        return lqw;
    }

    /**
     * 新增会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean insertByBo(StudentConsultantRecordBo bo) {
        StudentConsultantRecord add = MapstructUtils.convert(bo, StudentConsultantRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentConsultantRecordId(add.getStudentConsultantRecordId());
        }
        return flag;
    }

    /**
     * 修改会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean updateByBo(StudentConsultantRecordBo bo) {
        StudentConsultantRecord update = MapstructUtils.convert(bo, StudentConsultantRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentConsultantRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员顾问记录（时间逆序的最后一条记录和会员中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Map<Long,List<Long>> getStaffResponsibleStudentIdMap(List<Long> staffIdList) {
        List<StudentConsultantRecordVo> studentConsultantRecords = baseMapper.selectNewResponsibleRecord(staffIdList, null);
        if (CollUtil.isNotEmpty(studentConsultantRecords)){
            List<Long> list = studentConsultantRecords.stream().map(StudentConsultantRecordVo::getStudentId).toList();
            List<Long> notDelStudentIdList = filterDelStudentIds(list);
            return studentConsultantRecords.stream()
                .filter(e -> notDelStudentIdList.contains(e.getStudentId()))
                .collect(Collectors.groupingBy(StudentConsultantRecordVo::getStudentConsultantId, Collectors.mapping(StudentConsultantRecordVo::getStudentId, Collectors.toList())));
        }
        return Map.of();
    }

    @Override
    public List<Long> getStaffResponsibleStudentIdList(Long staffId) {
        Map<Long, List<Long>> staffResponsibleStudentIdMap = getStaffResponsibleStudentIdMap(List.of(staffId));
        return staffResponsibleStudentIdMap.getOrDefault(staffId, List.of());
    }


    @Override
    public Map<Long,Long> getStudentConsultantIdMap(List<Long> studentIdList) {
        List<StudentConsultantRecordVo> studentConsultantRecordVos = baseMapper.selectNewResponsibleRecord(null, studentIdList);
        if (CollUtil.isNotEmpty(studentConsultantRecordVos)){
            return studentConsultantRecordVos.stream().collect(Collectors.toMap(StudentConsultantRecordVo::getStudentId, StudentConsultantRecordVo::getStudentConsultantId));
        }
        return Map.of();
    }


    /**
     * 过滤掉删除的会员id
     *
     * @param studentIds 会员ID
     *
     * @date 2024/03/14 05:31:40
     */
    public List<Long> filterDelStudentIds(List<Long> studentIds) {
        if (studentIds == null || studentIds.isEmpty()) {
            return List.of();
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIds);
        studentBo.setStudentStatus(UserConstants.USER_NORMAL);
        return studentService.queryStudentIdList(studentBo);
    }

    @Cacheable(value = "studentConsultantRecord", key = "#studentConsultantRecordId",condition = "#studentConsultantRecordId != null")
    @Override
    public StudentConsultantRecord queryStudentConsultantRecordById(Long studentConsultantRecordId) {
        return baseMapper.selectById(studentConsultantRecordId);
    }

    @CacheEvict(value = "studentConsultantRecord",allEntries= true)
    public void cleanCache(){
        log.info("===========studentConsultantRecordService cleanCache===========");
    }

    @Override
    public List<StudentWithConsultantDTO> queryConsultantRecordListByStudentId(List<Long> studentIds) {
        if (CollectionUtil.isEmpty(studentIds)) {
            return Collections.emptyList();
        }
        return baseMapper.queryConsultantRecordListByStudentId(studentIds);
    }

    @Override
    public void init() {
        IStudentConsultantRecordService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentConsultantRecordService init===========");
        LambdaQueryWrapper<StudentConsultantRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentConsultantRecord::getStudentConsultantRecordId);
        List<StudentConsultantRecord> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========studentConsultantRecordService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentConsultantRecordById(item.getStudentConsultantRecordId());
        });
        log.info("===========studentConsultantRecordService init end===========");
    }

}
