package com.jxw.shufang.student.service.impl;

import com.google.common.collect.Lists;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.student.service.IStudentPaperQuestionService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 用户答题试卷记录Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class StudentPaperQuestionServiceImpl implements IStudentPaperQuestionService, BaseService {

    @DubboReference
    private RemoteQuestionService remoteQuestionService;
    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;

    /**
     * 查询用户答题试卷记录
     */
    @Override
    public List<RemoteVideoVo> queryById(Long questionId) {
        if (ObjectUtils.isEmpty(questionId)) {
            return Lists.newArrayList();
        }

        RemoteQuestionVideoBo remoteQuestionVideoBo = new RemoteQuestionVideoBo();
        remoteQuestionVideoBo.setQuestionId(questionId);
        List<RemoteGroupVideoVo> questionVideoList = remoteQuestionService.getQuestionVideoList(remoteQuestionVideoBo);

        List<RemoteVideoVo> videoVoList = Lists.newArrayList();
        for (RemoteGroupVideoVo remoteGroupVideoVo : questionVideoList) {
            List<RemoteVideoVo> videoList = remoteGroupVideoVo.getVideoVoList();
            if (CollectionUtils.isEmpty(videoList)) {
                continue;
            }
            videoVoList.addAll(videoList);
        }

        return videoVoList;
    }


    @Override
    public List<RemoteVideoVo> queryKnowledge(List<Long> knowledgeIdList) {
        return remoteKnowledgeService.queryKnowledge(knowledgeIdList);
    }


    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
