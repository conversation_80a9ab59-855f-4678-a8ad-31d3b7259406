package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.enums.SourceEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.FeedbackTemplate;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateSourceVo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateVo;
import com.jxw.shufang.student.mapper.FeedbackTemplateMapper;
import com.jxw.shufang.student.service.IFeedbackTemplateCollectService;
import com.jxw.shufang.student.service.IFeedbackTemplateService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 反馈模板Service业务层处理
 *
 *
 * @date 2024-03-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class FeedbackTemplateServiceImpl implements IFeedbackTemplateService, BaseService {

    private final FeedbackTemplateMapper baseMapper;

    private final IFeedbackTemplateCollectService feedbackTemplateCollectService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询反馈模板
     */
    @Override
    public FeedbackTemplateVo queryById(Long feedbackTemplateId) {
        return baseMapper.selectVoById(feedbackTemplateId);
    }

    /**
     * 查询反馈模板列表
     */
    @Override
    public TableDataInfo<FeedbackTemplateVo> queryPageList(FeedbackTemplateBo bo, PageQuery pageQuery) {
        QueryWrapper<FeedbackTemplate> lqw = buildQueryWrapper(bo);
        Page<FeedbackTemplateVo> result = baseMapper.selectFeedbackTemplatePage(pageQuery.build(), lqw);
        if (bo.getWithDeptInfo()) {
            putDeptInfo(result.getRecords());
        }
        if (LoginHelper.getUserId()!=null){
            putIsCollect(result.getRecords());
        }

        return TableDataInfo.build(result);
    }

    private void putIsCollect(List<FeedbackTemplateVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Long userId = LoginHelper.getUserId();
        if (userId == null) {
            return;
        }
        List<Long> templateIdList = feedbackTemplateCollectService.queryTemplateIdListByCreateUserId(userId);
        if (CollUtil.isEmpty(templateIdList)) {
            return;
        }
        records.forEach(vo -> {
            if (templateIdList.contains(vo.getFeedbackTemplateId())) {
                vo.setIsCollect(true);
            }
        });
    }

    /**
     * 查询反馈模板列表
     */
    @Override
    public List<FeedbackTemplateVo> queryList(FeedbackTemplateBo bo) {
        LambdaQueryWrapper<FeedbackTemplate> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FeedbackTemplate> buildLambdaQueryWrapper(FeedbackTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FeedbackTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackType()), FeedbackTemplate::getFeedbackType, bo.getFeedbackType());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateContent()), FeedbackTemplate::getFeedbackTemplateContent, bo.getFeedbackTemplateContent());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateSource()), FeedbackTemplate::getFeedbackTemplateSource, bo.getFeedbackTemplateSource());
        return lqw;
    }

    private QueryWrapper<FeedbackTemplate> buildQueryWrapper(FeedbackTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<FeedbackTemplate> lqw = Wrappers.query();
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackType()), "t.feedback_type", bo.getFeedbackType());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateContent()), "t.feedback_template_content", bo.getFeedbackTemplateContent());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateSource()), "t.feedback_template_source", bo.getFeedbackTemplateSource());
        lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), "t.del_flag", bo.getDelFlag());

        if (bo.getFeedbackTemplateSourceVo() != null && bo.getFeedbackTemplateSourceVo().getSource() != null) {
            lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateSourceVo().getSource().toString()), "t.feedback_template_source", bo.getFeedbackTemplateSourceVo().getSource());
            if (SourceEnum.BRANCH.toString().equals(bo.getFeedbackTemplateSourceVo().getSource().toString())) {
                lqw.eq(StringUtils.isNotBlank(bo.getFeedbackTemplateSourceVo().getSourceId().toString()), "t.create_dept", bo.getFeedbackTemplateSourceVo().getSourceId());
            }
        }
        if (Boolean.TRUE.equals(bo.getIsCollect())) {
            Long userId = LoginHelper.getUserId();
            if (userId == null) {
                throw new ServiceException("用户未登录");
            }
            List<Long> templateIdList = feedbackTemplateCollectService.queryTemplateIdListByCreateUserId(userId);
            if (CollUtil.isEmpty(templateIdList)) {
                lqw.eq("t.feedback_template_id", -1);
            } else {
                lqw.in("t.feedback_template_id", templateIdList);
            }
        }
        //当登录的是门店用户的时候，只能查看管理端的和自己门店的
        if (LoginHelper.isBranchUser()){
            //lqw.and(wrapper -> wrapper.eq("t.create_dept", LoginHelper.getDeptId()).or().eq("t.feedback_template_source", SourceEnum.MANAGEMENT.toString()));
            Long deptId = LoginHelper.getSelectDeptId()!=null?LoginHelper.getSelectDeptId():LoginHelper.getDeptId();
            lqw.and(wrapper->{
                wrapper.and(wrapper1 -> wrapper1.eq("t.create_dept",deptId ).eq("t.feedback_template_source", SourceEnum.BRANCH.toString()));
                wrapper.or().eq("t.feedback_template_source", SourceEnum.MANAGEMENT.toString());
            });
        }

        return lqw;
    }

    /**
     * 新增反馈模板
     */
    @Override
    public Boolean insertByBo(FeedbackTemplateBo bo) {
        FeedbackTemplate add = MapstructUtils.convert(bo, FeedbackTemplate.class);
        validEntityBeforeSave(add);

        add.setTemplateUseCount(0);
        //获取当前的部门
        if (LoginHelper.isBranchUser()) {
            add.setFeedbackTemplateSource(SourceEnum.BRANCH.toString());
        } else {
            add.setFeedbackTemplateSource(SourceEnum.MANAGEMENT.toString());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFeedbackTemplateId(add.getFeedbackTemplateId());
        }
        return flag;
    }

    /**
     * 修改反馈模板
     */
    @Override
    public Boolean updateByBo(FeedbackTemplateBo bo) {
        FeedbackTemplate update = MapstructUtils.convert(bo, FeedbackTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FeedbackTemplate entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除反馈模板
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public void putDeptInfo(List<FeedbackTemplateVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> deptIdList = list.stream().filter(Objects::nonNull).filter(e -> SourceEnum.BRANCH.toString().equals(e.getFeedbackTemplateSource())).map(FeedbackTemplateVo::getCreateDept).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(deptIdList)) {
            return;
        }
        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setDeptIdList(deptIdList);
        List<RemoteDeptVo> deptList = remoteDeptService.getDeptList(remoteDeptBo);
        if (CollUtil.isEmpty(deptList)) {
            return;
        }
        Map<Long, RemoteDeptVo> deptMap = deptList.stream().collect(Collectors.toMap(RemoteDeptVo::getDeptId, vo -> vo));

        list.forEach(vo -> {
            RemoteDeptVo remoteDeptVo = null;
            if (SourceEnum.BRANCH.toString().equals(vo.getFeedbackTemplateSource())) {
                remoteDeptVo = deptMap.get(vo.getCreateDept());
            } else {
                remoteDeptVo = new RemoteDeptVo();
                remoteDeptVo.setDeptName("管理端");
            }
            vo.setCreateDeptEntity(remoteDeptVo);
        });
    }

    @Override
    public List<FeedbackTemplateSourceVo> sourceOptions() {
        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setIsStore(true);
        List<RemoteDeptVo> deptList = remoteDeptService.getDeptList(remoteDeptBo);


        List<FeedbackTemplateSourceVo> sourceVoList = new ArrayList<>();

        FeedbackTemplateSourceVo feedbackTemplateSourceVo = new FeedbackTemplateSourceVo();
        feedbackTemplateSourceVo.setSource(SourceEnum.MANAGEMENT);
        feedbackTemplateSourceVo.setSourceName("管理端");
        sourceVoList.add(feedbackTemplateSourceVo);

        if (CollUtil.isNotEmpty(deptList)) {
            deptList.forEach(dept -> {
                FeedbackTemplateSourceVo sourceVo = new FeedbackTemplateSourceVo();
                sourceVo.setSource(SourceEnum.BRANCH);
                sourceVo.setSourceName(dept.getDeptName());
                sourceVo.setSourceId(dept.getDeptId());
                sourceVoList.add(sourceVo);
            });
        }
        return sourceVoList;
    }


    @Cacheable(value = "feedbackTemplate", key = "#feedbackTemplateId", condition = "#feedbackTemplateId != null")
    @Override
    public FeedbackTemplate queryFeedbackTemplateById(Long feedbackTemplateId) {
        return baseMapper.selectById(feedbackTemplateId);
    }


    @CacheEvict(value = "feedbackTemplate", allEntries = true)
    public void cleanCache() {
        log.info("===========feedbackTemplateService cleanCache===========");
    }

    @Override
    public Boolean stepTemplateUseCount(Long feedbackTemplateId) {
        LambdaUpdateWrapper<FeedbackTemplate> feedbackTemplateLambdaUpdateWrapper = Wrappers.lambdaUpdate(FeedbackTemplate.class)
            .eq(FeedbackTemplate::getFeedbackTemplateId, feedbackTemplateId)
            .setSql("template_use_count = template_use_count + 1");

        return baseMapper.update(null, feedbackTemplateLambdaUpdateWrapper) > 0;
    }

    @Override
    public void init() {
        IFeedbackTemplateService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========feedbackTemplateService init===========");
        LambdaQueryWrapper<FeedbackTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(FeedbackTemplate::getFeedbackTemplateId);
        List<FeedbackTemplate> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========feedbackTemplateService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryFeedbackTemplateById(item.getFeedbackTemplateId());
        });
        log.info("===========feedbackTemplateService init end===========");
    }


}
