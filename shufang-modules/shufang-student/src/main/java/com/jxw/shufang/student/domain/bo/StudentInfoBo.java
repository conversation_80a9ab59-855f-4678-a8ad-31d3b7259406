package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentInfo;

import java.util.List;

/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）业务对象 student_info
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentInfo.class, reverseConvertGenerate = false)
public class StudentInfoBo extends BaseEntity {

    /**
     * 会员信息id
     */
    @NotNull(message = "会员信息id不能为空", groups = { EditGroup.class })
    private Long studentInfoId;

    /**
     * 会员id
     */
    //@NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 省
     */
    //@NotBlank(message = "省不能为空", groups = { AddGroup.class, EditGroup.class })
    private String province;

    /**
     * 市
     */
    //@NotBlank(message = "市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 县
     */
    //@NotBlank(message = "县不能为空", groups = { AddGroup.class, EditGroup.class })
    private String county;

    /**
     * 就读学校（对应字典值）
     */
    //@NotBlank(message = "就读学校（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attendingSchool;

    /**
     * 在校班级（对应字典值）
     */
    //@NotBlank(message = "在校班级（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String schoolClass;

    /**
     * 文理科（对应字典值）
     */
    //@NotBlank(message = "文理科（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String schoolMajor;

    /**
     * 住校情况（对应字典值）
     */
    //@NotBlank(message = "住校情况（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String schoolStayType;

    /**
     * 家庭住址
     */
    //@NotBlank(message = "家庭住址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentAddress;

    private List<Long> studentIdList;

}
