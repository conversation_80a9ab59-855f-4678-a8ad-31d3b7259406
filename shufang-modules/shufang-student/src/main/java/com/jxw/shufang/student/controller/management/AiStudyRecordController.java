package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiStudyRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyRecordVo;
import com.jxw.shufang.student.service.IAiStudyRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ai学习记录
 * 前端访问路由地址为:/student/aiStudyRecord
 *
 *
 * @date 2024-05-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiStudyRecord")
public class AiStudyRecordController extends BaseController {

    private final IAiStudyRecordService aiStudyRecordService;

    /**
     * 查询ai学习记录列表
     */
    @SaCheckPermission("student:aiStudyRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiStudyRecordVo> list(AiStudyRecordBo bo, PageQuery pageQuery) {
        return aiStudyRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai学习记录列表
     */
    @SaCheckPermission("student:aiStudyRecord:export")
    @Log(title = "ai学习记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiStudyRecordBo bo, HttpServletResponse response) {
        List<AiStudyRecordVo> list = aiStudyRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai学习记录", AiStudyRecordVo.class, response);
    }

    /**
     * 获取ai学习记录详细信息
     *
     * @param aiStudyRecordId 主键
     */
    @SaCheckPermission("student:aiStudyRecord:query")
    @GetMapping("/{aiStudyRecordId}")
    public R<AiStudyRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiStudyRecordId) {
        return R.ok(aiStudyRecordService.queryById(aiStudyRecordId));
    }

    /**
     * 新增ai学习记录
     */
    @SaCheckPermission("student:aiStudyRecord:add")
    @Log(title = "ai学习记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiStudyRecordBo bo) {
        return toAjax(aiStudyRecordService.insertByBo(bo));
    }

    /**
     * 修改ai学习记录
     */
    @SaCheckPermission("student:aiStudyRecord:edit")
    @Log(title = "ai学习记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiStudyRecordBo bo) {
        return toAjax(aiStudyRecordService.updateByBo(bo));
    }

    /**
     * 删除ai学习记录
     *
     * @param aiStudyRecordIds 主键串
     */
    @SaCheckPermission("student:aiStudyRecord:remove")
    @Log(title = "ai学习记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiStudyRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiStudyRecordIds) {
        return toAjax(aiStudyRecordService.deleteWithValidByIds(List.of(aiStudyRecordIds), true));
    }
}
