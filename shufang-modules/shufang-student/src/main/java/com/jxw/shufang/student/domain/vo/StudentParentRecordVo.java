package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudentParentRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）视图对象 student_parent_record
 *
 *
 * @date 2024-03-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentParentRecord.class)
public class StudentParentRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员绑定家长记录id
     */
    @ExcelProperty(value = "会员绑定家长记录id")
    private Long studentParentRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 家长姓名
     */
    @ExcelProperty(value = "家长姓名")
    private String parentName;

    /**
     * 家长微信昵称
     */
    @ExcelProperty(value = "家长微信昵称")
    private String parentWechatNickname;

    /**
     * 家长微信号
     */
    @ExcelProperty(value = "家长微信号")
    private String parentWechatNo;

    /**
     * 家长微信头像
     */
    @ExcelProperty(value = "家长微信头像")
    private String parentWechatImg;

    /**
     * 家长微信openId
     */
    @ExcelProperty(value = "家长微信openId")
    private String parentWechatOpenId;

    /**
     * 家长微信unionId
     */
    @ExcelProperty(value = "家长微信unionId")
    private String parentWechatUnionId;

    /**
     * 是否关注公众号（0是 1否）
     */
    @ExcelProperty(value = "是否关注公众号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=是,1=否")
    private String isFollow;

    /**
     * 会员信息
     */
    private StudentVo student;

    /**
     * 创建时间
     */
    private Date createTime;
}
