package com.jxw.shufang.student;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.SensitiveGroup;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.domain.Sensitive;
import com.jxw.shufang.student.domain.StudentType;
import com.jxw.shufang.student.mapper.SensitiveMapper;
import com.jxw.shufang.student.mapper.StudentTypeMapper;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import static com.jxw.shufang.common.core.constant.UserConstants.TOP_DEPT_ID;


@Component
@RequiredArgsConstructor
@Slf4j
public class StudentApplicationRunner implements ApplicationRunner {

    private final StudentTypeMapper studentTypeMapper;

    private final SensitiveMapper sensitiveMapper;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        intiStudentType();
        initSensitive();
    }


    public void intiStudentType() {
        log.info("初始化会员类型 start");
        String[] defaultStudentTypeArr = UserConstants.DEFAULT_STUDENT_TYPE_ARR;
        //获取表中最后的排序值

        DataPermissionHelper.ignore(()->{
            StudentType studentType = studentTypeMapper.selectOne(new LambdaQueryWrapper<StudentType>()
                .select(StudentType::getStudentTypeSort)
                .orderByDesc(StudentType::getStudentTypeSort)
                .last("limit 1"));
            Long studentTypeSort = 0L;
            if (studentType != null) {
                studentTypeSort = studentType.getStudentTypeSort();
            }

            for (String studentTypeName : defaultStudentTypeArr) {
                LambdaQueryWrapper<StudentType> queryWrapper = Wrappers.lambdaQuery(StudentType.class);
                queryWrapper.eq(StudentType::getStudentTypeName, studentTypeName);
                boolean exists = studentTypeMapper.exists(queryWrapper);
                if (!exists) {
                    log.info("初始化会员类型：{}", studentTypeName);
                    StudentType insertBean = new StudentType();
                    insertBean.setStudentTypeName(studentTypeName);
                    insertBean.setStudentTypeSort(++studentTypeSort);
                    insertBean.setCreateBy(UserConstants.SUPER_ADMIN_ID);
                    insertBean.setCreateDept(TOP_DEPT_ID);
                    int insert = studentTypeMapper.insert(insertBean);
                    if (insert <= 0) {
                        throw new ServiceException("初始化会员类型失败");
                    }
                    log.info("初始化会员类型成功：{}", studentTypeName);
                }
            }
            log.info("初始化会员类型 end");
        });
    }

    public void initSensitive(){
        log.info("初始化敏感词 start");
        SensitiveGroup[] values = SensitiveGroup.values();

        DataPermissionHelper.ignore(()->{
            for (SensitiveGroup sensitiveGroupName : values) {
                LambdaQueryWrapper<Sensitive> queryWrapper = Wrappers.lambdaQuery(Sensitive.class);
                queryWrapper.eq(Sensitive::getSensitiveGroupName, sensitiveGroupName.toString());
                boolean exists = sensitiveMapper.exists(queryWrapper);
                if (!exists) {
                    log.info("初始化敏感词：{}", sensitiveGroupName.toString());
                    Sensitive insertBean = new Sensitive();
                    insertBean.setSensitiveGroupName(sensitiveGroupName.toString());
                    insertBean.setCreateBy(UserConstants.SUPER_ADMIN_ID);
                    insertBean.setCreateDept(TOP_DEPT_ID);
                    int insert = sensitiveMapper.insert(insertBean);
                    if (insert <= 0) {
                        throw new ServiceException("初始化敏感词失败");
                    }
                    log.info("初始化敏感词成功：{}", sensitiveGroupName.toString());
                }
            }
            log.info("初始化敏感词 end");
        });
    }


}
