package com.jxw.shufang.student.controller.management;

import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStaffEzkecoBo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.bo.FaceBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.FaceVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IAttendanceLogStaffEzkecoService;
import com.jxw.shufang.student.service.IAttendanceLogStudentEzkecoService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/management/face")
public class AFacePushProcessorController {


    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStaffService remoteStaffService;


    private final IStudentService studentService;

    private final IAttendanceLogStaffEzkecoService attendanceLogStaffEzkecoService;

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;


    /**
     * 人脸识别考勤机打卡成功
     */
    @RequestMapping(value = "/cdata", method = RequestMethod.POST)
    public FaceVo init(@RequestBody FaceBo faceBo) {
        log.info("人脸识别 打卡成功: faceBo:{}", faceBo);
        if (ObjectUtils.isEmpty(getIdCard(faceBo))) {
            return getSuccessString(faceBo.getMsgID(), faceBo.getDeviceNo());
        }
        try {
            //处理考勤记录
            //查询对应的人员信息
            String phone = getIdCard(faceBo);
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setUserNames(Collections.singletonList(phone));
            List<RemoteUserVo> userVoList = remoteUserService.queryUserList(remoteUserBo, true);

            //按照手机号码转成map
            Map<String, RemoteUserVo> userMap = userVoList.stream().collect(Collectors.toMap(RemoteUserVo::getUserName, item -> item));
            String deviceNo = faceBo.getDeviceNo();

            List<AttendanceLogStudentEzkecoBo> attendanceLogStudentEzkecoBoList = new ArrayList<>();
            List<AttendanceLogStaffEzkecoBo> attendanceLogStaffEzkecoBoList = new ArrayList<>();

            RemoteUserVo userVo = userMap.get(phone);
            if (userVo == null) {
                log.error("未找到对应的人员信息，请检查上传数据！, mobile={}", phone);
                return getSuccessString(faceBo.getMsgID(), faceBo.getDeviceNo());
            }
            //组装日期和时间
            //查询这个点是不是已经打卡了，如果是已经打卡了，则删除

            //先看看是会员还是员工
            String userTypeStr = userVo.getUserType();
            UserType userType = UserType.getUserType(userTypeStr);
            log.info("用户类型是：{}", userType);
            switch (userType) {
                case APP_STU_USER -> {
                    AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo = new AttendanceLogStudentEzkecoBo();
                    attendanceLogStudentEzkecoBo.setUserId(userVo.getUserId());
                    attendanceLogStudentEzkecoBo.setChecktime(DateUtils.parseDate(faceBo.getPassTime()));
                    Boolean exist = attendanceLogStudentEzkecoService.exist(attendanceLogStudentEzkecoBo);
                    if (exist) {
                        return getSuccessString(faceBo.getMsgID(), faceBo.getDeviceNo());
                    }
                    attendanceLogStudentEzkecoBo.setSn(deviceNo);
                    attendanceLogStudentEzkecoBoList.add(attendanceLogStudentEzkecoBo);
                }
                case STAFF_USER -> {
                    AttendanceLogStaffEzkecoBo attendanceLogStaffEzkecoBo = new AttendanceLogStaffEzkecoBo();
                    attendanceLogStaffEzkecoBo.setUserId(userVo.getUserId());
                    attendanceLogStaffEzkecoBo.setChecktime(DateUtils.parseDate(faceBo.getPassTime()));
                    Boolean exist = attendanceLogStaffEzkecoService.exist(attendanceLogStaffEzkecoBo);
                    if (exist) {
                        return getSuccessString(faceBo.getMsgID(), faceBo.getDeviceNo());
                    }
                    attendanceLogStaffEzkecoBo.setSn(deviceNo);
                    attendanceLogStaffEzkecoBoList.add(attendanceLogStaffEzkecoBo);
                }
            }

            if (!attendanceLogStaffEzkecoBoList.isEmpty()) {
                List<Long> userIdList = attendanceLogStaffEzkecoBoList.stream().map(AttendanceLogStaffEzkecoBo::getUserId).distinct().toList();
                RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
                remoteStaffBo.setUserIdList(userIdList);
                List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
                Map<Long, RemoteStaffVo> staffMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getCreateBy, item -> item));

                attendanceLogStaffEzkecoBoList.forEach(item -> {
                    RemoteStaffVo remoteStaffVo = staffMap.get(item.getUserId());
                    item.setCreateBy(item.getUserId());
                    if (remoteStaffVo == null) {
                        return;
                    }
                    item.setCreateDept(remoteStaffVo.getCreateDept());
                });
                attendanceLogStaffEzkecoService.insertBatch(attendanceLogStaffEzkecoBoList);
            }
            if (!attendanceLogStudentEzkecoBoList.isEmpty()) {
                List<Long> userIdList = attendanceLogStudentEzkecoBoList.stream().map(AttendanceLogStudentEzkecoBo::getUserId).distinct().toList();
                StudentBo studentBo = new StudentBo();
                studentBo.setUserIdList(userIdList);
                List<StudentVo> studentVos = studentService.queryList(studentBo);
                Map<Long, StudentVo> studentMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getCreateBy, item -> item));
                attendanceLogStudentEzkecoBoList.forEach(item -> {
                    StudentVo studentVo = studentMap.get(item.getUserId());
                    item.setCreateBy(item.getUserId());
                    if (studentVo == null) {
                        return;
                    }
                    item.setCreateDept(studentVo.getCreateDept());
                    item.setStudentId(studentVo.getStudentId());
                });
                attendanceLogStudentEzkecoService.insertBatch(attendanceLogStudentEzkecoBoList);
            }
        } catch (Exception e) {
            log.error("上传的考勤记录格式不正确，请检查上传数据！", e);
        }
        return getSuccessString(faceBo.getMsgID(), faceBo.getDeviceNo());
    }

    private static String getIdCard(FaceBo faceBo) {
        return faceBo.getIdCard();
    }

    private static @NotNull FaceVo getSuccessString(String msgID, String deviceNo) {
        FaceVo faceVo = new FaceVo();
        faceVo.setErrorCode("100");
        faceVo.setMsgID(msgID);
        faceVo.setMsgType("uploadRecordResp");
//        faceVo.setControlState("open");
        faceVo.setDeviceNo(deviceNo);
//        faceVo.setMsg("欢迎光临");
        return faceVo;
    }


}

