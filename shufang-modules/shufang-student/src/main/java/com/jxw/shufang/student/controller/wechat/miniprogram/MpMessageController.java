package com.jxw.shufang.student.controller.wechat.miniprogram;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.MessageBo;
import com.jxw.shufang.student.domain.vo.MessageVo;
import com.jxw.shufang.student.service.IMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息控制器--小程序端
 * 前端访问路由地址为:/student/miniProgram/message
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/message")
public class MpMessageController extends BaseController {

    private final IMessageService messageService;



    /**
     * 查询会员消息列表（group）
     */
    @GetMapping("/messageList")
    public R<List<MessageVo>> messageList(MessageBo messageBo) {
        Long branchStaffId = LoginHelper.getBranchStaffId();
        if (branchStaffId==null){
            return R.fail("当前用户不是分店员工，无法查看消息列表");
        }
        messageBo.setConsultantId(branchStaffId);
        List<MessageVo> messageVos = messageService.staffShowMessageList(messageBo);
        //按照lastCreateTime倒序排序
        if (CollUtil.isNotEmpty(messageVos)) {
            messageVos.sort((o1, o2) -> o2.getLastCreateTime()!= null&&o1.getLastCreateTime()!= null? o2.getLastCreateTime().compareTo(o1.getLastCreateTime()) : 0);
            return R.ok(messageVos);
        }
        return R.ok();
    }


    /**
     * 查询未读消息数量
     */
    @GetMapping("/unreadCount")
    public R<Long> unreadCount() {
        Long userId = LoginHelper.getUserId();
        //因为是会员查询，所以只查员工（以及门店管理员）发的即可
        return R.ok(messageService.unreadCount(userId, UserConstants.MESSAGE_SENDER_TYPE_STUDENT));
    }

    /**
     * 查询某个会员的消息列表详情，流式分页
     */
    @GetMapping("/messageDetailPage")
    public TableDataInfo<MessageVo> list(MessageBo bo, PageQuery pageQuery) {
        if (pageQuery.getSearchCount()==null) {
            pageQuery.setSearchCount(false);
        }
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            //如果按照默认的雪花算法，新数据永远比旧数据的ID大
            pageQuery.setOrderByColumn("t.message_id");
            pageQuery.setIsAsc("desc");
        }
        TableDataInfo<MessageVo> messageVoTableDataInfo = messageService.queryPageList(bo, pageQuery);
        List<MessageVo> rows = messageVoTableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是小程序端的的消息，也就是员工（门店管理员）接受的，我们只需要把会员的的消息标记为已读即可
            List<Long> messageIdList = rows.stream().map(MessageVo::getMessageId).toList();
            messageService.readMessages(messageIdList, UserConstants.MESSAGE_SENDER_TYPE_STUDENT);
        }
        return messageVoTableDataInfo;
    }

    /**
     * 新增消息
     */
    @Log(title = "小程序端-发送会员消息", businessType = BusinessType.INSERT)
    @RepeatSubmit(interval = 1000, message = "请勿发送消息过快")
    @PostMapping()
    public R<MessageVo> add(@Validated(AddGroup.class) @RequestBody MessageBo bo) {
        if (!LoginHelper.isBranchUser()) {
            return R.fail("非门店用户，无权操作");
        }
        if (bo.getMessageStudentId() == null) {
            return R.fail("会员ID不能为空");
        }
        if (UserConstants.MESSAGE_TYPE_TEXT.equals(bo.getContentType()) && StringUtils.isBlank(bo.getMessageConcat())) {
            return R.fail("文本消息内容不能为空");
        }
        if (UserConstants.MESSAGE_TYPE_IMAGE.equals(bo.getContentType()) && bo.getMessageResources() == null) {
            return R.fail("图片消息资源不能为空");
        }
        bo.setSendUserType(UserConstants.MESSAGE_SENDER_TYPE_STAFF);
        bo.setMessageStaffId(LoginHelper.getUserId());
        String s = messageService.insertByBo(bo);
        if (StringUtils.isNotBlank(s)) {
            return R.fail(s);
        }
        return R.ok(messageService.queryById(bo.getMessageId()));
    }


    /**
     * 查询新消息列表
     */
    @GetMapping("/newMessageList")
    public R<List<MessageVo>> newMessageList(MessageBo bo) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        pageQuery.setOrderByColumn("t.message_id");
        pageQuery.setIsAsc("desc");
        pageQuery.setSearchCount(Boolean.FALSE);
        TableDataInfo<MessageVo> messageVoTableDataInfo = messageService.queryPageList(bo, pageQuery);
        List<MessageVo> rows = messageVoTableDataInfo.getRows();
        if (CollUtil.isNotEmpty(rows)) {
            //标记已读，因为这个是管理端的消息，也就是员工（门店管理员）接受的，我们只需要把会员的的消息标记为已读即可
            List<Long> messageIdList = rows.stream().map(MessageVo::getMessageId).toList();
            messageService.readMessages(messageIdList, UserConstants.MESSAGE_SENDER_TYPE_STUDENT);
        }
        return R.ok(rows);
    }

}
