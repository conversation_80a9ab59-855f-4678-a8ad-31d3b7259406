package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.StudentSign;
import com.jxw.shufang.student.domain.bo.StudentSignBo;
import com.jxw.shufang.student.domain.vo.StudentSignVo;
import com.jxw.shufang.student.mapper.StudentSignMapper;
import com.jxw.shufang.student.service.IStudentSignService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 会员标签Service业务层处理
 *
 *
 * @date 2024-03-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentSignServiceImpl implements IStudentSignService, BaseService {

    private final StudentSignMapper baseMapper;

    /**
     * 查询会员标签
     */
    @Override
    public StudentSignVo queryById(Long studentSignId) {
        return baseMapper.selectVoById(studentSignId);
    }

    /**
     * 查询会员标签列表
     */
    @Override
    public TableDataInfo<StudentSignVo> queryPageList(StudentSignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentSign> lqw = buildQueryWrapper(bo);
        Page<StudentSignVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询会员标签列表
     */
    @Override
    public List<StudentSignVo> queryList(StudentSignBo bo) {
        LambdaQueryWrapper<StudentSign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentSign> buildQueryWrapper(StudentSignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentSign::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getSignContent()), StudentSign::getSignContent, bo.getSignContent());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIds()), StudentSign::getStudentId, bo.getStudentIds());
        return lqw;
    }

    /**
     * 新增会员标签
     */
    @Override
    public Boolean insertByBo(StudentSignBo bo) {
        StudentSign add = MapstructUtils.convert(bo, StudentSign.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentSignId(add.getStudentSignId());
        }
        return flag;
    }

    @Override
    public Boolean insertBatchByBo(List<StudentSignBo> studentSignList) {
        List<StudentSign> convert = MapstructUtils.convert(studentSignList, StudentSign.class);
        for (StudentSign studentSign : convert) {
            validEntityBeforeSave(studentSign);
        }
        boolean flag = baseMapper.insertBatch(convert);
        if (flag) {
            for (int i = 0; i < studentSignList.size(); i++) {
                studentSignList.get(i).setStudentSignId(convert.get(i).getStudentSignId());
            }
        }
        return flag;
    }

    /**
     * 修改会员标签
     */
    @Override
    public Boolean updateByBo(StudentSignBo bo) {
        StudentSign update = MapstructUtils.convert(bo, StudentSign.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentSign entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除会员标签
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Cacheable(value = "studentSign", key = "#studentSignId",condition = "#studentSignId != null")
    @Override
    public StudentSign queryStudentSignById(Long studentSignId) {
        return baseMapper.selectById(studentSignId);
    }

    @CacheEvict(value = "studentSign",allEntries= true)
    public void cleanCache(){
        log.info("===========studentSignService cleanCache===========");
    }

    @Override
    public void init() {
        IStudentSignService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========studentSignService init===========");
        LambdaQueryWrapper<StudentSign> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(StudentSign::getStudentSignId);
        List<StudentSign> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========studentSignService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryStudentSignById(item.getStudentSignId());
        });
        log.info("===========studentSignService init end===========");
    }

}
