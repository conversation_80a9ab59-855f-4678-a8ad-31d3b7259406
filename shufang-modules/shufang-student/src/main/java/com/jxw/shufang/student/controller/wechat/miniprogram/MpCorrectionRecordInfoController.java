package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordInfoVo;
import com.jxw.shufang.student.service.ICorrectionRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 批改记录详情---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/correctionRecordInfo
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/correctionRecordInfo")
public class MpCorrectionRecordInfoController extends BaseController {

    private final ICorrectionRecordInfoService correctionRecordInfoService;

    @GetMapping("/{correctionRecordId}")
    public R<List<CorrectionRecordInfoVo>> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long correctionRecordId) {
        CorrectionRecordInfoBo bo = new CorrectionRecordInfoBo();
        bo.setCorrectionRecordId(correctionRecordId);
        return R.ok(correctionRecordInfoService.queryList(bo));
    }
}
