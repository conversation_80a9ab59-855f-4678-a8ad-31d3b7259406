package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 申请批改记录对象 apply_correction_record
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_apply_correction_record")
public class AiApplyCorrectionRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 申请批改记录id
     */
    @TableId(value = "ai_apply_correction_record_id")
    private Long aiApplyCorrectionRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 课程Id
     */
    private Long courseId;

    /**
     * 申请批改类型（1练习  2测试）
     */
    private String applyType;

    /**
     * 申请结果（0待审核 1允许 2拒绝）
     */
    private String applyResult;


    /**
     * 最新一次重新提交时间，超过一定时间后，员工无法查询到该自助申请订单，允许用户再次提交，更新”applyTime“
     */
    private Date applyTime;
}
