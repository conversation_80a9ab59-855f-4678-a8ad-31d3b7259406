package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.PaperCollectionBo;
import com.jxw.shufang.student.domain.vo.PaperCollectionVo;
import com.jxw.shufang.student.service.IPaperCollectionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 试卷收藏
 * 前端访问路由地址为:/student/management/paperCollection
 *
 *
 * @date 2024-05-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/paperCollection")
public class PaperCollectionController extends BaseController {

    private final IPaperCollectionService paperCollectionService;

    /**
     * 查询试卷收藏列表
     */
    @SaCheckPermission("student:paperCollection:list")
    @GetMapping("/list")
    public TableDataInfo<PaperCollectionVo> list(PaperCollectionBo bo, PageQuery pageQuery) {
        return paperCollectionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出试卷收藏列表
     */
    @SaCheckPermission("student:paperCollection:export")
    @Log(title = "试卷收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PaperCollectionBo bo, HttpServletResponse response) {
        List<PaperCollectionVo> list = paperCollectionService.queryList(bo);
        ExcelUtil.exportExcel(list, "试卷收藏", PaperCollectionVo.class, response);
    }

    /**
     * 获取试卷收藏详细信息
     *
     * @param collectionId 主键
     */
    @SaCheckPermission("student:paperCollection:query")
    @GetMapping("/{collectionId}")
    public R<PaperCollectionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long collectionId) {
        return R.ok(paperCollectionService.queryById(collectionId));
    }

    /**
     * 新增试卷收藏
     */
    @SaCheckPermission("student:paperCollection:add")
    @Log(title = "试卷收藏", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PaperCollectionBo bo) {
        return toAjax(paperCollectionService.insertByBo(bo));
    }

    /**
     * 修改试卷收藏
     */
    @SaCheckPermission("student:paperCollection:edit")
    @Log(title = "试卷收藏", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PaperCollectionBo bo) {
        return toAjax(paperCollectionService.updateByBo(bo));
    }

    /**
     * 删除试卷收藏
     *
     * @param collectionIds 主键串
     */
    @SaCheckPermission("student:paperCollection:remove")
    @Log(title = "试卷收藏", businessType = BusinessType.DELETE)
    @DeleteMapping("/{collectionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] collectionIds) {
        return toAjax(paperCollectionService.deleteWithValidByIds(List.of(collectionIds), true));
    }
}
