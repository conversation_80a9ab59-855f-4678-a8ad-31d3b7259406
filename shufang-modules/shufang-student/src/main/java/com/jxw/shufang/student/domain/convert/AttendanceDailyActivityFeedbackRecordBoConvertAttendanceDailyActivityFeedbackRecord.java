package com.jxw.shufang.student.domain.convert;

import com.jxw.shufang.student.domain.AttendanceDailyActivityFeedbackRecord;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityFeedbackRecordBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AttendanceDailyActivityFeedbackRecordBoConvertAttendanceDailyActivityFeedbackRecord extends BaseMapper<AttendanceDailyActivityFeedbackRecordBo, AttendanceDailyActivityFeedbackRecord> {

}
