package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.BeanUtils;
import com.jxw.shufang.common.mqtt.MqttProperties;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.student.domain.vo.MqttConfigVo;
import com.jxw.shufang.student.enums.MqttProtocolEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/push")
public class PushController {

    private final MqttProperties mqttProperties;

    @GetMapping("/mqtt/config")
    public R<MqttConfigVo> push(@RequestParam String deviceId,
                                @RequestParam String protocol) {

        MqttConfigVo vo = new MqttConfigVo();
        BeanUtils.copyProperties(mqttProperties, vo);

        if (MqttProtocolEnum.TCP.getCode().equals(protocol)) {
            vo.setServerUrl(mqttProperties.getTcpUrl());
        } else if (MqttProtocolEnum.WS.getCode().equals(protocol)) {
            vo.setServerUrl(mqttProperties.getWsUrl());
        } else if (MqttProtocolEnum.WSS.getCode().equals(protocol)) {
            vo.setServerUrl(mqttProperties.getWssUrl());
        }
        vo.setClientId(String.join("_", vo.getClientId(), deviceId, String.valueOf(LoginHelper.getUserId())));

        return R.ok(vo);
    }

}
