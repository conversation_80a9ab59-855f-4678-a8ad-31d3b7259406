package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.vo.RemoteCorrectionRecordVo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CorrectionRecordVoConvertRemoteCorrectionRecordVo extends BaseMapper<CorrectionRecordVo, RemoteCorrectionRecordVo> {

}
