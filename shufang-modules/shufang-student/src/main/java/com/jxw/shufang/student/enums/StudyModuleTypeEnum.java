package com.jxw.shufang.student.enums;

/**
 * <AUTHOR>
 * @Date 2025/4/2 15:22
 * @Version 1
 * @Description 模块类型
 */
public enum StudyModuleTypeEnum {
    PREVIEW("preview", "预览"),
    STUDY("study", "学习"),
    PRACTICE("practice", "练习"),
    TEST("test", "测试"),
    SELF_SPEECH("self_speech", "自讲"),
    AI_TEST("ai_test", "AI测试"),
    AI_TEST_ERROR_VIDEO("ai_test_error_video", "AI错题视频");
    private String moduleCode;
    private String moduleName;

    StudyModuleTypeEnum(String moduleCode, String moduleName) {
        this.moduleCode = moduleCode;
        this.moduleName = moduleName;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public static StudyModuleTypeEnum getByCode(String moduleCode) {
        for (StudyModuleTypeEnum studyModuleTypeEnum : StudyModuleTypeEnum.values()) {
            if (studyModuleTypeEnum.getModuleCode().equals(moduleCode)) {
                return studyModuleTypeEnum;
            }
        }
        return null;
    }
}
