package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import com.jxw.shufang.student.domain.vo.StudentVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@AutoMapper(target = StudentIntroduceRecord.class)
@NoArgsConstructor
public class StudentIntroduceRecordBo extends BaseEntity {
    /**
     * 入学时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkInTimeStart;
    /**
     * 入学时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkInTimeEnd;
    /**
     * 门店id
     */
    private Long branchId;
    /**
     * 介绍人
     */
    private Long introduceStudentId;
    /**
     * 年级
     */
    private String studentGrade;
    /**
     * 学生id
     */
    private Long studentId;
    /**
     * 学生id列表
     */
    private List<Long> studentIds;

    private Long introduceRecordId;
    /**
     * 介绍人名称
     */
    private String introduceStudentName;
    /**
     * 会员姓名
     */
    private String studentName;
    /**
     * 所属门店名称
     */
    private String branchName;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 是否发放优惠
     */
    private Boolean doReferent;
    /**
     * 备注
     */
    private String remark;
    /**
     * 购买会员类型
     */
    private String branchAuthTypeName;
    /**
     * 赠送的优惠额度
     */
    private BigDecimal preferentialAmount;
    /**
     * 优惠额度解冻时间（优惠可使用时间）
     */
    private Date unFrozenTime;
    /**
     * 创建者
     */
    private Long createBy;
    /**
     * 创建部门
     */
    private Long createDept;
    /**
     * 创建时间
     */
    private Date createTime;

    public StudentIntroduceRecordBo(Student student, StudentVo introduceStudent, RemoteBranchVo branchBo) {
        branchId = branchBo.getBranchId();
        branchName = branchBo.getBranchName();
        introduceStudentId = introduceStudent.getStudentId();
        introduceStudentName = introduceStudent.getStudentName();
        studentId = student.getStudentId();
        studentName = student.getStudentName();
        studentGrade = student.getStudentGrade();
        createTime = student.getCreateTime();
        createDept = student.getCreateDept();
    }
}
