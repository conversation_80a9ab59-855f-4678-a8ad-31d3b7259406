package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AllowOwnCorrection;

import java.util.List;

/**
 * 允许自主批改业务对象 allow_own_correction
 *
 *
 * @date 2024-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AllowOwnCorrection.class, reverseConvertGenerate = false)
public class AllowOwnCorrectionBo extends BaseEntity {

    /**
     * 允许自主批改id
     */
    @NotNull(message = "允许自主批改id不能为空", groups = { EditGroup.class })
    private Long allowOwnCorrectionId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 应用类型，1学习规划  2ai学习
     */
    private String type;

    /**
     * 允许类型，1练习  2测试
     */
    private String allowType;


    private String nameWithPhone;

    private List<Long> studentIdList;

    /**
     * 携带学生系统用户信息
     */
    private Boolean withStudentUserInfo;

    /**
     * 携带操作用户信息
     */
    private Boolean withCreateUserInfo;

}
