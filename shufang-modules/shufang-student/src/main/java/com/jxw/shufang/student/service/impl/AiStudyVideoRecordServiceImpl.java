package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;
import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyVideoRecordVo;
import com.jxw.shufang.student.mapper.AiStudyVideoRecordMapper;
import com.jxw.shufang.student.service.IAiStudyVideoRecordService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * ai学习视频记录（视频观看记录）Service业务层处理
 *
 * @date 2024-05-21
 */
@RequiredArgsConstructor
@Service
public class AiStudyVideoRecordServiceImpl implements IAiStudyVideoRecordService, BaseService {

    private final AiStudyVideoRecordMapper baseMapper;

    /**
     * 查询ai学习视频记录（视频观看记录）
     */
    @Override
    public AiStudyVideoRecordVo queryById(Long aiStudyVideoRecordId) {
        return baseMapper.selectVoById(aiStudyVideoRecordId);
    }

    /**
     * 查询ai学习视频记录（视频观看记录）列表
     */
    @Override
    public TableDataInfo<AiStudyVideoRecordVo> queryPageList(AiStudyVideoRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiStudyVideoRecord> lqw = buildQueryWrapper(bo);
        Page<AiStudyVideoRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ai学习视频记录（视频观看记录）列表
     */
    @Override
    public List<AiStudyVideoRecordVo> queryList(AiStudyVideoRecordBo bo) {
        LambdaQueryWrapper<AiStudyVideoRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiStudyVideoRecord> buildQueryWrapper(AiStudyVideoRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiStudyVideoRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, AiStudyVideoRecord::getStudentId, bo.getStudentId());
        lqw.eq(bo.getVideoId() != null, AiStudyVideoRecord::getVideoId, bo.getVideoId());
        lqw.eq(bo.getCourseId() != null, AiStudyVideoRecord::getCourseId, bo.getCourseId());
        lqw.eq(bo.getStudyVideoDuration() != null, AiStudyVideoRecord::getStudyVideoDuration, bo.getStudyVideoDuration());
        lqw.eq(StringUtils.isNotBlank(bo.getStudyVideoSlices()), AiStudyVideoRecord::getStudyVideoSlices, bo.getStudyVideoSlices());
        lqw.in(CollUtil.isNotEmpty(bo.getCourseIdList()), AiStudyVideoRecord::getCourseId, bo.getCourseIdList());
        if (CollUtil.isNotEmpty(bo.getCreateDateList())) {
            StringBuilder sb = new StringBuilder();
            for (String date : bo.getCreateDateList()) {
                sb.append("'").append(date).append("',");
            }
            String createDateStr = sb.toString().substring(0, sb.length() - 1);
            lqw.apply("DATE_FORMAT(create_time,'%Y-%m-%d') in (" + createDateStr + ")");
        }


        return lqw;
    }

    /**
     * 新增ai学习视频记录（视频观看记录）
     */
    @Override
    public Boolean insertByBo(AiStudyVideoRecordBo bo) {
        AiStudyVideoRecord add = MapstructUtils.convert(bo, AiStudyVideoRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAiStudyVideoRecordId(add.getAiStudyVideoRecordId());
        }
        return flag;
    }

    /**
     * 修改ai学习视频记录（视频观看记录）
     */
    @Override
    public Boolean updateByBo(AiStudyVideoRecordBo bo) {
        AiStudyVideoRecord update = MapstructUtils.convert(bo, AiStudyVideoRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiStudyVideoRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ai学习视频记录（视频观看记录）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean saveBatch(List<AiStudyVideoRecord> insertList) {
        return baseMapper.insertBatch(insertList);
    }

    @Override
    public Boolean updateBatchById(List<AiStudyVideoRecord> updateList) {
        return baseMapper.updateBatchById(updateList);
    }

    @Override
    public AiStudyVideoRecordVo queryOne(AiStudyVideoRecordBo aiStudyVideoRecordBo) {
        LambdaQueryWrapper<AiStudyVideoRecord> queryWrapper = buildQueryWrapper(aiStudyVideoRecordBo);
        queryWrapper.last("limit 1");
        return baseMapper.selectVoOne(queryWrapper);
    }

    @Override
    public AiStudyVideoRecordVo queryLastDayRecord(Long studentId, Long courseId) {
        LambdaQueryWrapper<AiStudyVideoRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AiStudyVideoRecord::getStudentId, studentId);
        lambdaQuery.eq(AiStudyVideoRecord::getCourseId, courseId);
        lambdaQuery.orderByDesc(AiStudyVideoRecord::getCreateTime);
        lambdaQuery.last("limit 1");
        List<AiStudyVideoRecordVo> aiStudyVideoRecordVos = baseMapper.selectVoList(lambdaQuery);
        if (aiStudyVideoRecordVos.isEmpty()) {
            return null;
        }
        return aiStudyVideoRecordVos.get(0);
    }

    @Override
    public List<AiStudyVideoRecord> batchQueryByStudentIdAndVideoId(List<Long> studentIds, StudyModuleTypeEnum moduleTypeEnum, List<Long> videoIds) {
        if (CollectionUtil.isEmpty(studentIds)) {
            throw new ServiceException("参数不能为空");
        }
        LambdaQueryWrapper<AiStudyVideoRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(AiStudyVideoRecord::getStudentId, studentIds);
        lambdaQuery.eq(AiStudyVideoRecord::getStudyModuleType, moduleTypeEnum.getModuleCode());
        lambdaQuery.in(AiStudyVideoRecord::getVideoId, videoIds);
        return baseMapper.selectList(lambdaQuery);
    }

    @Override
    public List<AiStudyVideoRecord> batchQueryByStudentIdAndCouredId(List<Long> studentIds, List<Long> courseIds, StudyModuleTypeEnum moduleTypeEnum) {
        if (CollectionUtil.isEmpty(studentIds) || CollectionUtil.isEmpty(courseIds)) {
            throw new ServiceException("参数不能为空");
        }
        LambdaQueryWrapper<AiStudyVideoRecord> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(AiStudyVideoRecord::getStudentId, studentIds);
        lambdaQuery.eq(AiStudyVideoRecord::getStudyModuleType, moduleTypeEnum.getModuleCode());
        lambdaQuery.in(AiStudyVideoRecord::getCourseId, courseIds);
        return baseMapper.selectList(lambdaQuery);
    }

    @Override
    public void batchUpdate(List<AiStudyVideoRecord> videoProcessorUpdates) {
        if (CollectionUtil.isEmpty(videoProcessorUpdates)) {
            return;
        }
        videoProcessorUpdates.forEach(baseMapper::updateById);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
