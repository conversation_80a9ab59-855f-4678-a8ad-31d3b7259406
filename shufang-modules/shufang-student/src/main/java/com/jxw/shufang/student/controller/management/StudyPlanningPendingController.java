package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudyPlanningPendingBo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningPendingVo;
import com.jxw.shufang.student.service.IStudyPlanningPendingService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 需学习规划学生管理
 * 前端访问路由地址为:/student/management/planningPending
 *
 * @date 2024-06-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/planning-pending")
@Slf4j
public class StudyPlanningPendingController extends BaseController {

    private final IStudyPlanningPendingService studyPlanningPendingService;

    /**
     * 查询需学习规划学生列表
     */
    @SaCheckPermission("student:studyPlanningRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudyPlanningPendingVo> list(StudyPlanningPendingBo bo, PageQuery pageQuery) {
        return studyPlanningPendingService.queryPageList(bo, pageQuery);
    }


    /**
     * 根据StudyPlanningPendingId获取最新关联的学习规划反馈记录
     */
    @GetMapping("/latest-feedback-report/{pendingId}")
    public R<StudyFeedbackReportVo> getLatestFeedbackReport(@PathVariable Long pendingId) {
        try {
            StudyFeedbackReportVo latestReport = studyPlanningPendingService.getLatestFeedbackReportByPendingId(pendingId);

            if (latestReport != null) {
                return R.ok(latestReport);
            } else {
                return R.fail("未找到关联的学习规划反馈记录");
            }
        } catch (Exception e) {
            log.error("获取最新反馈记录失败", e);
            return R.fail("获取最新反馈记录失败：" + e.getMessage());
        }
    }


    /**
     * 导出需学习规划学生列表
     */
    @SaCheckPermission("student:studyPlanningRecord:export")
    @Log(title = "需学习规划学生", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudyPlanningPendingBo bo, HttpServletResponse response) {
        List<StudyPlanningPendingVo> list = studyPlanningPendingService.queryList(bo);
        ExcelUtil.exportExcel(list, "需学习规划学生", StudyPlanningPendingVo.class, response);
    }

    /**
     * 获取需学习规划学生详细信息
     */
    @SaCheckPermission("student:studyPlanningRecord:query")
    @GetMapping("/{id}")
    public R<StudyPlanningPendingVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long id) {
        return R.ok(studyPlanningPendingService.queryById(id));
    }

    /**
     * 新增需学习规划学生
     */
    @SaCheckPermission("student:studyPlanningRecord:add")
    @Log(title = "需学习规划学生", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudyPlanningPendingBo bo) {
        return toAjax(studyPlanningPendingService.insertByBo(bo));
    }

    /**
     * 修改需学习规划学生
     */
    @SaCheckPermission("student:studyPlanningRecord:edit")
    @Log(title = "需学习规划学生", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudyPlanningPendingBo bo) {
        return toAjax(studyPlanningPendingService.updateByBo(bo));
    }

    /**
     * 删除需学习规划学生
     */
    @SaCheckPermission("student:studyPlanningRecord:remove")
    @Log(title = "需学习规划学生", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(studyPlanningPendingService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 生成待规划学生记录
     */
    @PostMapping("/generate")
    public R<Void> generatePendingStudents() {
        return toAjax(studyPlanningPendingService.generatePendingStudents());
    }

    /**
     * 标记学生为已规划
     */
    @SaCheckPermission("student:studyPlanningRecord:edit")
    @Log(title = "标记学生为已规划", businessType = BusinessType.UPDATE)
    @PostMapping("/markPlanned")
    public R<Void> markAsPlanned(@RequestParam Long studentId,
                                 @RequestBody List<Long> planningRecordIds) {
        return toAjax(studyPlanningPendingService.markAsPlanned(studentId, planningRecordIds));
    }


}
