package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceV2Vo;
import com.jxw.shufang.student.domain.bo.CourseResourceBo;
import com.jxw.shufang.student.domain.bo.CourseResourceKnowledgeBo;
import com.jxw.shufang.student.domain.bo.CourseResourceV2Bo;
import com.jxw.shufang.student.domain.vo.CourseResourceV2Vo;
import com.jxw.shufang.student.domain.vo.CourseResourceVo;
import com.jxw.shufang.student.service.ICourseResourceService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程资源（绑定到课程的资源）
 * 前端访问路由地址为:/student/management/courseResource
 *
 * @date 2024-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/courseResource")
@Slf4j
public class CourseResourceController extends BaseController {

    private final ICourseResourceService courseResourceService;


    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    @SaCheckPermission("student:courseResource:list")
    @GetMapping("/list")
    public TableDataInfo<CourseResourceVo> list(CourseResourceBo bo, PageQuery pageQuery) {
        return courseResourceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出课程资源（绑定到课程的资源）列表
     */
    @SaCheckPermission("student:courseResource:export")
    @Log(title = "课程资源（绑定到课程的资源）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CourseResourceBo bo, HttpServletResponse response) {
        List<CourseResourceVo> list = courseResourceService.queryList(bo);
        ExcelUtil.exportExcel(list, "课程资源（绑定到课程的资源）", CourseResourceVo.class, response);
    }

    /**
     * 获取课程资源（绑定到课程的资源）详细信息
     *
     * @param courseResourceId 主键
     */
    @SaCheckPermission("student:courseResource:query")
    @GetMapping("/{courseResourceId}")
    public R<CourseResourceVo> getInfo(@NotNull(message = "主键不能为空")
                                       @PathVariable Long courseResourceId) {
        return R.ok(courseResourceService.queryById(courseResourceId));
    }

    /**
     * 新增课程资源（绑定到课程的资源）
     */
    @SaCheckPermission("student:courseResource:add")
    @Log(title = "课程资源（绑定到课程的资源）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CourseResourceBo bo) {
        return toAjax(courseResourceService.insertByBo(bo));
    }

    /**
     * 修改课程资源（绑定到课程的资源）
     */
    @SaCheckPermission("student:courseResource:edit")
    @Log(title = "课程资源（绑定到课程的资源）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CourseResourceBo bo) {
        return toAjax(courseResourceService.updateByBo(bo));
    }

    /**
     * 删除课程资源（绑定到课程的资源）
     *
     * @param courseResourceIds 主键串
     */
    @SaCheckPermission("student:courseResource:remove")
    @Log(title = "课程资源（绑定到课程的资源）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{courseResourceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] courseResourceIds) {
        return toAjax(courseResourceService.deleteWithValidByIds(List.of(courseResourceIds), true));
    }

    @PostMapping("/multiDownload")
    public R<List<RemoteGroupResourceV2Vo>> downloadMultiFiles(@RequestBody List<CourseResourceKnowledgeBo> knowledgeList) {
        if (ObjectUtils.isEmpty(knowledgeList)) {
            return R.ok(List.of());
        }

        List<RemoteGroupResourceV2Vo> resourceV2Vos = courseResourceService.multiGetUrlsVo(knowledgeList);
        if (CollectionUtils.isEmpty(resourceV2Vos)) {
            return R.ok(List.of());
        }
        return R.ok(resourceV2Vos);
    }

//    @PostMapping("/multiDownload")
//    public ResponseEntity<InputStreamResource> downloadMultiFiles(@RequestBody List<CourseResourceKnowledgeBo> knowledgeList) {
//        if (ObjectUtils.isEmpty(knowledgeList)) {
//            return ResponseEntity.status(HttpStatus.OK).body(null);
//        }
//
//        List<String> urls =courseResourceService.multiGetUrls(knowledgeList);
//        if (CollectionUtils.isEmpty(knowledgeList)) {
//            return ResponseEntity.status(HttpStatus.OK).body(null);
//        }
//        try {
//            // 创建ZIP文件输出流
//            ByteArrayOutputStream baos = new ByteArrayOutputStream();
//            ZipArchiveOutputStream zipOut = new ZipArchiveOutputStream(baos);
//
//            // 遍历URL列表并下载文件
//            for (String urlStr : urls) {
//                URL url = new URL(urlStr);
//                String fileName = new File(url.getPath()).getName();
//
//                // 下载文件
//                InputStream inputStream = url.openStream();
//
//                // 创建ZIP条目
//                ZipArchiveEntry zipEntry = new ZipArchiveEntry(fileName);
//                zipOut.putArchiveEntry(zipEntry);
//
//                // 将文件内容写入ZIP条目
//                IOUtils.copy(inputStream, zipOut);
//
//                // 关闭输入流和ZIP条目
//                inputStream.close();
//                zipOut.closeArchiveEntry();
//            }
//
//            // 关闭ZIP输出流
//            zipOut.close();
//
//            // 创建响应
//            byte[] zipBytes = baos.toByteArray();
//            InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(zipBytes));
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="+System.currentTimeMillis()+".zip");
//            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
//
//            return ResponseEntity.ok()
//                .headers(headers)
//                .contentLength(zipBytes.length)
//                .contentType(MediaType.APPLICATION_OCTET_STREAM)
//                .body(resource);
//
//        } catch (Exception e) {
//            log.error("导出文件失败，url地址:{}", urls, e);
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
//        }
//    }

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    @GetMapping("/getDownloadFile")
    public R<CourseResourceV2Vo> getInfo(CourseResourceV2Bo bo) {
        return R.ok(courseResourceService.getInfo(bo));
    }

    /**
     * 查询课程资源（绑定到课程的资源）列表
     */
    @GetMapping("/preview")
    public R<String> preview(CourseResourceKnowledgeBo bo) {
        return R.ok("操作成功", courseResourceService.preview(bo));
    }
}
