package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.bo.NewStudyPlanningRecordBO;
import com.jxw.shufang.student.domain.dto.*;
import com.jxw.shufang.student.domain.vo.MergeStudyPlanningRecordVO;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;
import com.jxw.shufang.student.domain.vo.StudyVideoRecordVo;
import com.jxw.shufang.student.mapper.StudyPlanningRecordMapper;
import com.jxw.shufang.student.service.*;
import org.springframework.stereotype.Service;
import com.jxw.shufang.common.satoken.utils.LoginHelper;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jxw.shufang.common.satoken.utils.LoginHelper.getBranchId;

/**
 * <AUTHOR>
 * @Date 2025/2/15 10:49
 * @Version 1
 * @Description
 */
@Service
public class NewStudyPlanningRecordServiceImpl implements INewStudyPlanningRecordService {

    /**
     * 视频完播率阈值：95%
     */
    private static final BigDecimal VIDEO_COMPLETION_THRESHOLD = new BigDecimal("0.95");
    @Resource
    private IStudentConsultantRecordService studentConsultantRecordService;
    @Resource
    private INewStudentService newStudentService;
    @Resource
    private StudyPlanningRecordMapper planningRecordMapper;

    @Resource
    private ICourseDetailService courseDetailService;

    @Resource
    private IStudyRecordService studyRecordService;

    @Resource
    private IStudyVideoRecordService studyVideoRecordService;


    @Override
    public TableDataInfo<MergeStudyPlanningRecordVO> queryMergeStudyPlanningRecordPage(NewStudyPlanningRecordBO requestBO,
                                                                                       PageQuery pageQuery) {
        List<Long> studentIdList = this.queryCurrentUserStudentIdList();

        // 通过会员ID分页查找排课记录
        Page<PlanningRecordStudentIdDTO> recordStudentIdPage = planningRecordMapper.queryPlanningRecordStudentIdPage(pageQuery.build(),
            QueryPlanningRecordDTO.of(requestBO, studentIdList));
        List<Long> studentIds = recordStudentIdPage.getRecords().stream()
            .map(PlanningRecordStudentIdDTO::getStudentId)
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(studentIds)) {
            return TableDataInfo.build(Collections.emptyList());
        }

        // 页面展示的会员
        List<StudentWithPlanningRecordDTO> planningRecordList = this.getPlanningRecordsWithStudentInfo(requestBO, studentIds);

        // 顾问信息
        Map<Long, StudentWithConsultantDTO> studentConsultantMap = this.getConsultantRecordsByStudentId(studentIds);

        // 查询课程
        Set<Long> courseId = planningRecordList.stream().map(StudentWithPlanningRecordDTO::getCourseId).collect(Collectors.toSet());
        Map<Long, CourseWithTopCourseDTO> courseMap = this.getCourseMap(courseId);

        // 转换为合并学习记录
        List<MergeStudyRecordDTO> mergeStudyRecords = planningRecordList.stream()
            .map(record -> this.convertToMergeStudyRecord(studentConsultantMap, courseMap, record))
            .toList();
        List<MergeStudyPlanningRecordVO> afterMergeStudyRecords = MergeStudyPlanningRecordVO.mergeByStudent(mergeStudyRecords);

        // 设置学习规划记录中课程完成状态
        // 收集所有学习规划记录
        List<StudyPlanningRecordInfoVO> studyPlanningRecordInfoVos = afterMergeStudyRecords.stream()
            .filter(record -> CollUtil.isNotEmpty(record.getRecords()))
            .flatMap(record -> record.getRecords().stream())
            .collect(Collectors.toList());

        setCourseCompleteStatusInternal(studyPlanningRecordInfoVos);

        return TableDataInfo.build(afterMergeStudyRecords, recordStudentIdPage.getTotal());
    }

    @Override
    public Map<Long, CourseWithTopCourseDTO> getCourseMap(Set<Long> courseIdSet) {
        List<CourseWithTopCourseDTO> courseWithTopCourseDTO = courseDetailService.queryCourseWithTopInfo(courseIdSet);
        if (CollectionUtil.isEmpty(courseWithTopCourseDTO)) {
            return Collections.emptyMap();
        }
        return courseWithTopCourseDTO.stream()
            .collect(Collectors.toMap(CourseWithTopCourseDTO::getCourseId, Function.identity(), (v1, v2) -> v2,LinkedHashMap::new));
    }

    /**
     * 获取顾问信息
     *
     * @param studentIds 会员ID
     * @return
     */
    private Map<Long, StudentWithConsultantDTO> getConsultantRecordsByStudentId(List<Long> studentIds) {
        List<StudentWithConsultantDTO> consultantList = studentConsultantRecordService.queryConsultantRecordListByStudentId(studentIds);
        return consultantList.stream()
            .collect(Collectors.toMap(StudentWithConsultantDTO::getStudentId, Function.identity(), (v1, v2) -> v2));
    }

    /**
     * 查询规划记录
     *
     * @param recordBO   请求参数
     * @param studentIds 会员ID
     * @return
     */
    private List<StudentWithPlanningRecordDTO> getPlanningRecordsWithStudentInfo(NewStudyPlanningRecordBO recordBO,
                                                                                 List<Long> studentIds) {
        return planningRecordMapper.queryStudyPlanningWithStudentInfo(QueryPlanningRecordDTO.of(recordBO, studentIds));
    }

    private MergeStudyRecordDTO convertToMergeStudyRecord(Map<Long, StudentWithConsultantDTO> studentConsultantMap,
                                                          Map<Long, CourseWithTopCourseDTO> courseMap,
                                                          StudentWithPlanningRecordDTO record) {
        // build
        MergeStudyRecordDTO mergeStudyRecordDTO = new MergeStudyRecordDTO();
        mergeStudyRecordDTO.setStudentId(record.getStudentId());
        mergeStudyRecordDTO.setStudentName(record.getStudentName());
        mergeStudyRecordDTO.setPlanningRecord(record);
        mergeStudyRecordDTO.setConsultant(studentConsultantMap.get(record.getStudentId()));
        mergeStudyRecordDTO.setCourse(courseMap.get(record.getCourseId()));
        mergeStudyRecordDTO.setStudentAccount(record.getStudentAccount());
        return mergeStudyRecordDTO;
    }

    /**
     * 查询当前登录用户可见的会员ID列表
     */
    private List<Long> queryCurrentUserStudentIdList() {

        List<Long> branchIdList = this.getBranchIdListFromLogin().stream().toList();
        if (CollUtil.isEmpty(branchIdList)) {
            if (LoginHelper.isSuperAdmin()|| LoginHelper.isTenantAdmin()){
                return Collections.emptyList();
            }
            return Collections.singletonList(-1L) ;
        }
        List<Long> studentIdList = newStudentService.getStudentIdListByBranchIdList(branchIdList);
        return CollUtil.isEmpty(studentIdList) ? Collections.singletonList(-1L) : studentIdList;
    }

    /**
     * 从登陆信息中获取当前登录的branchIdList
     *
     * @return
     */
    private Set<Long> getBranchIdListFromLogin() {
        Set<Long> result = new HashSet<>();
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            List<Long> branchIdList = LoginHelper.getBranchIdList();
            result.addAll(branchIdList);
        }else if (null != LoginHelper.getBranchId()) {
            Long branchId = getBranchId();
            result.add(branchId);
        }
        return result;
    }

    /**
     * 设置学习规划记录中课程完成状态（公共方法）
     * 参考 StudyPlanningServiceImpl.checkStudyComplete 方法的逻辑
     *
     * 通用方法，可用于多种场景
     *
     * @param recordInfoList 学习规划记录信息列表
     */
    @Override
    public void setCourseCompleteStatus(List<StudyPlanningRecordInfoVO> recordInfoList) {
        setCourseCompleteStatusInternal(recordInfoList);
    }

    /**
     * 设置学习规划记录中课程完成状态（内部方法）
     * 参考 StudyPlanningServiceImpl.checkStudyComplete 方法的逻辑
     *
     * @param recordInfoList 学习规划记录信息列表
     */
    private void setCourseCompleteStatusInternal(List<StudyPlanningRecordInfoVO> recordInfoList) {
        if (CollUtil.isEmpty(recordInfoList)) {
            return;
        }

        // 从记录列表中提取学习规划记录ID和学生ID
        Set<Long> studyPlanningRecordIds = recordInfoList.stream()
            .map(StudyPlanningRecordInfoVO::getStudyPlanningRecordId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Set<Long> studentIds = recordInfoList.stream()
            .map(StudyPlanningRecordInfoVO::getStudentId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (studyPlanningRecordIds.isEmpty() || studentIds.isEmpty()) {
            return;
        }

        // 批量查询学习记录和视频记录
        Map<Long, StudyRecordVo> studyRecordMap = queryStudyRecords(studyPlanningRecordIds, studentIds);
        Map<Long, StudyVideoRecordVo> videoRecordMap = queryVideoRecords(studyPlanningRecordIds);

        // 使用并行流为每个记录设置课程完成状态（适用于大数据量）
        recordInfoList.parallelStream()
            .forEach(recordInfo -> {
                Long studyPlanningRecordId = recordInfo.getStudyPlanningRecordId();
                StudyRecordVo studyRecord = studyRecordMap.get(studyPlanningRecordId);
                StudyVideoRecordVo videoRecord = videoRecordMap.get(studyPlanningRecordId);

                // 设置课程完成状态
                recordInfo.setStudyCompleteStatus(checkStudyComplete(studyRecord, videoRecord));
            });
    }

    /**
     * 检查课程是否完成
     * 参考 StudyPlanningServiceImpl.checkStudyComplete 方法的逻辑
     *
     * @param studyRecord 学习记录
     * @param videoRecord 视频记录
     * @return 是否完成
     */
    private Boolean checkStudyComplete(StudyRecordVo studyRecord, StudyVideoRecordVo videoRecord) {
        // 早期返回：学习记录为空
        if (studyRecord == null) {
            return false;
        }

        // 检查测试和练习状态是否都已批改
        boolean testCompleted = UserConstants.CORRECTION_STATUS_DONE.equals(studyRecord.getTestState());
        boolean practiceCompleted = UserConstants.CORRECTION_STATUS_DONE.equals(studyRecord.getPracticeState());

        // 早期返回：测试或练习未完成
        if (!testCompleted || !practiceCompleted) {
            return false;
        }

        // 检查视频完播率
        return checkVideoCompletionRate(videoRecord);
    }

    /**
     * 检查视频完播率是否达到阈值
     *
     * @param videoRecord 视频记录
     * @return 是否达到95%完播率
     */
    private boolean checkVideoCompletionRate(StudyVideoRecordVo videoRecord) {
        if (videoRecord == null) {
            return false;
        }

        Long studyVideoDuration = videoRecord.getStudyVideoDuration();
        Long totalDuration = videoRecord.getDuration();

        // 检查数据有效性
        if (studyVideoDuration == null || totalDuration == null || totalDuration <= 0) {
            return false;
        }

        // 计算完播率并与阈值比较
        BigDecimal completionRate = new BigDecimal(studyVideoDuration)
            .divide(new BigDecimal(totalDuration), 2, RoundingMode.HALF_UP);

        return completionRate.compareTo(VIDEO_COMPLETION_THRESHOLD) >= 0;
    }

    /**
     * 批量查询学习记录
     *
     * @param studyPlanningRecordIds 学习规划记录ID集合
     * @param studentIds 学生ID集合
     * @return 学习记录映射表
     */
    private Map<Long, StudyRecordVo> queryStudyRecords(Set<Long> studyPlanningRecordIds, Set<Long> studentIds) {
        StudyRecordBo studyRecordBo = new StudyRecordBo();
        studyRecordBo.setStudyPlanningRecordIdList(new ArrayList<>(studyPlanningRecordIds));
        studyRecordBo.setStudentIdList(new ArrayList<>(studentIds));

        return studyRecordService.queryList(studyRecordBo).stream()
            .collect(Collectors.toMap(
                StudyRecordVo::getStudyPlanningRecordId,
                Function.identity(),
                (existing, replacement) -> replacement
            ));
    }

    /**
     * 批量查询视频记录
     *
     * @param studyPlanningRecordIds 学习规划记录ID集合
     * @return 视频记录映射表
     */
    private Map<Long, StudyVideoRecordVo> queryVideoRecords(Set<Long> studyPlanningRecordIds) {
        List<StudyVideoRecordVo> videoRecordList = studyVideoRecordService.queryLastStudyVideoRecord(
            new ArrayList<>(studyPlanningRecordIds), false);

        return videoRecordList.stream()
            .collect(Collectors.toMap(
                StudyVideoRecordVo::getStudyPlanningRecordId,
                Function.identity(),
                (existing, replacement) -> replacement
            ));
    }
}
