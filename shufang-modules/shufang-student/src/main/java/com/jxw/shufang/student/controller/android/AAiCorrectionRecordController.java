package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionQuestionRecordVo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordVo;
import com.jxw.shufang.student.service.IAiCorrectionRecordService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * Ai批改记录--平板端
 * 前端访问路由地址为:/student/aiCorrectionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/aiCorrectionRecord")
public class AAiCorrectionRecordController extends BaseController {

    private final IAiCorrectionRecordService aiCorrectionRecordService;


    /**
     * 提交批改记录
     */
    @RepeatSubmit
    @PostMapping()
    @Log(title = "Ai提交批改记录--平板端", businessType = BusinessType.INSERT)
    public R<BigDecimal> submitCorrectionRecord(@RequestBody @Validated(AddGroup.class) AiCorrectionRecordBo correctionRecordBo) {
        correctionRecordBo.setStudentId(LoginHelper.getStudentId());
        correctionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STUDENT);
        return R.ok(aiCorrectionRecordService.insertByBo(correctionRecordBo));
    }

    /**
     * 查询批改的题目列表列表
     * @param courseId 课程Id
     * @param correctionType 批改类型
     */
    @GetMapping("/queryQuestionRecord")
    public R<AiCorrectionQuestionRecordVo> queryQuestionRecord(@NotNull(message = "学习规划记录ID不能为空") Long courseId,
                                                               @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(aiCorrectionRecordService.queryQuestionRecord(courseId, LoginHelper.getStudentId(),correctionType));
    }

    /**
     * 提交批改记录
     */
    @RepeatSubmit
    @PostMapping("/withoutInfo")
    @Log(title = "预习、自讲记录--平板端", businessType = BusinessType.INSERT)
    public R<Boolean> submitWithoutCorrectionRecordInfo(@RequestBody AiCorrectionRecordBo correctionRecordBo) {
        correctionRecordBo.setStudentId(LoginHelper.getStudentId());
        correctionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STUDENT);
        return R.ok(aiCorrectionRecordService.submitWithoutCorrection(correctionRecordBo));
    }

    /**
     * 查询批改列表
     */
    @GetMapping("/queryRecord")
    public R<AiCorrectionRecordVo> queryRecord(@NotNull(message = "课程ID不能为空") Long courseId,
                                               @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(aiCorrectionRecordService.queryRecord(courseId,LoginHelper.getStudentId(),correctionType));
    }

}
