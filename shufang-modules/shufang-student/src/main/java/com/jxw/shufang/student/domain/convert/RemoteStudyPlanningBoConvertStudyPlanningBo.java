package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyPlanningBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteStudyPlanningBoConvertStudyPlanningBo extends BaseMapper<RemoteStudyPlanningBo, StudyPlanningBo> {

}
