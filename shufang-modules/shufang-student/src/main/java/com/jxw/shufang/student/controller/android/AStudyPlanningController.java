package com.jxw.shufang.student.controller.android;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.jxw.shufang.student.domain.vo.*;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeResourceVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.service.IStudyPlanningRecordService;
import com.jxw.shufang.student.service.IStudyPlanningService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.*;
import java.util.*;

/**
 * 学习规划-平板端
 * 前端访问路由地址为:/student/android/studyPlanning
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/studyPlanning")
public class AStudyPlanningController extends BaseController {

    private final IStudyPlanningService studyPlanningService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    /**
     * 查询学习规划学习状态
     */
    @GetMapping("/getStudyPlanStudyStatus")
    public R<List<StudyPlanningVo>> getStudyPlanStudyStatus(StudyPlanningBo bo) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        if (Objects.isNull(bo.getStudyPlanningDateStart())||Objects.isNull(bo.getStudyPlanningDateEnd())) {
            DateTime now = DateUtil.date();
            DateTime weekStart = DateUtil.beginOfWeek(now);
            DateTime weekEnd = DateUtil.endOfWeek(now);
            bo.setStudyPlanningDateStart(weekStart);
            bo.setStudyPlanningDateEnd(weekEnd);
        }
        return R.ok(studyPlanningService.getStudyPlanStudyStatus(bo, LoginHelper.getStudentId()));
    }

    /**
     * 本周规划完成度
     */
    @GetMapping("/getWeekPlanComplete")
    public R<StudyPlanCompleteVo> getWeekPlanComplete(StudyPlanningBo bo) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        //推断出本周的日期区间
        DateTime now = DateUtil.date();
        DateTime weekStart = DateUtil.beginOfWeek(now);
        DateTime weekEnd = DateUtil.endOfWeek(now);
        bo.setStudyPlanningDateStart(weekStart);
        bo.setStudyPlanningDateEnd(weekEnd);
        StudyPlanCompleteVo studyPlanCompleteVo = studyPlanningService.getPlanComplete(bo, LoginHelper.getStudentId(), true);
        return R.ok(studyPlanCompleteVo);
    }


    /**
     * 各科完成情况 ，需要传入时间区间
     */
    @GetMapping("/getMonthSubjectComplete")
    public R<List<SubjectCompleteVo>> getMonthSubjectComplete(StudyPlanningBo bo) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        if (bo.getStudyPlanningDateStart() == null || bo.getStudyPlanningDateEnd() == null) {
            return R.fail("日期不能为空");
        }

        List<SubjectCompleteVo> subjectCompleteVos = studyPlanningService.getSubjectCompleteByDateLimit(bo.getStudyPlanningDateStart(), bo.getStudyPlanningDateEnd(), LoginHelper.getStudentId());
        return R.ok(subjectCompleteVos);
    }


    /**
     * 查询学习规划章节资源(包括视频，讲义，练习，测试)
     */
    @GetMapping("/getChapterResource/{studyPlanningRecordId}")
    public R<CourseVo> getChapterResource(@NotNull(message = "学习规划记录Id不能为空") @PathVariable Long studyPlanningRecordId) {
        return R.ok(studyPlanningService.getChapterResource(studyPlanningRecordId, LoginHelper.getStudentId()));
    }


    /**
     * 查询学习规划对应的课程外部资源（讲义，练习，测试，练习带解析，测试带解析）
     *
     * @param studyPlanningRecordId 学习规划记录Id
     * @param resourceType          资源类型
     *
     * @date 2024/05/09 03:20:51
     */
    @GetMapping("/getExtResource")
    public R<RemoteKnowledgeResourceVo> getExtResource(@NotNull(message = "学习规划记录Id不能为空") Long studyPlanningRecordId, @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType) {
        return R.ok(studyPlanningService.getExtResource(studyPlanningRecordId, resourceType));
    }


    /**
     * 查询学习规划对应的知识点的题目列表
     *
     * @param studyPlanningRecordId 研究计划记录id
     * @param resourceType          测试 TEST    练习 PRACTICE
     * @param withAnalysis          带分析
     * @param withAnswer            有答案
     *
     * @date 2024/05/09 04:03:47
     */
    @GetMapping("/getKnowledgeQuestionList")
    public R<List<RemoteQuestionVo>> getKnowledgeQuestionList(Long studyPlanningRecordId,
                                                              Long courseId,
                                                              @NotNull(message = "资源类型不能为空") KnowledgeResourceType resourceType,
                                                              Boolean withAnalysis,
                                                              Boolean withAnswer) {


        if(null == studyPlanningRecordId && null == courseId){
            return R.fail("参数有误");
        }

        return R.ok(studyPlanningService.getKnowledgeQuestionList(studyPlanningRecordId , courseId , resourceType, withAnalysis, withAnswer));
    }

    /**
     * 按日期分组查询学习记录
     */
    @GetMapping("/getStudyRecordByDate")
    public TableDataInfo<StudyPlanRecordGroupVo> getStudyRecordByDate(StudyPlanningRecordBo bo, PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            throw new ServiceException("当前用户不是会员");
        }
        bo.setStudentId(LoginHelper.getStudentId());
        bo.setWithStudyRecord(true);
        bo.setStudyRecordHas(true);
        bo.setStudyRecordMustHas(true);
        return studyPlanningRecordService.getStudyRecordByDate(bo, pageQuery);
    }

}
