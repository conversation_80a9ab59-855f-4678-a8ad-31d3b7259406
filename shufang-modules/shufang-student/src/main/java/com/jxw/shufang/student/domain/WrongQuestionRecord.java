package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 错题记录对象 wrong_question_record
 *
 *
 * @date 2024-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wrong_question_record")
public class WrongQuestionRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 错题id
     */
    @TableId(value = "wrong_question_record_id")
    private Long wrongQuestionRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 学习规划记录ID
     */
    private Long studyPlanningRecordId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 题目序号
     */
    private String questionNo;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    private String answerResult;

    /**
     * 来源类型（1测试 2练习）
     */
    private String sourceType;
    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 订正状态 0-否 1-是
     */
    private Integer reviseStatus;

    /**
     * 订正时间
     */
    private Date reviseTime;

    /**
     * 订正截图（oss_id，多个，逗号隔开)
     */
    private String reviseScreenshots;

}
