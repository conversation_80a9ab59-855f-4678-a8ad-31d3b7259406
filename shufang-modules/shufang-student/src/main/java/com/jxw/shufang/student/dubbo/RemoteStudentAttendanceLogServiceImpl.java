package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.student.api.RemoteStudentAttendanceLogService;
import com.jxw.shufang.student.service.IAttendanceLogStudentEzkecoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: cyj
 * @date: 2025/7/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentAttendanceLogServiceImpl implements RemoteStudentAttendanceLogService {

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;

    @Override
    public Map<Long, Integer> getStudentAttendanceDayNumsMap(List<Long> studentIds, Date rangeStartTime,
        Date rangeEndTime) {
        return attendanceLogStudentEzkecoService.getStudentAttendanceDayNumsMap(studentIds, rangeStartTime,
            rangeEndTime);
    }
}
