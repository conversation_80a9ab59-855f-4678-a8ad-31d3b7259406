package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = AttendanceDailyActivity.class)
@ExcelIgnoreUnannotated
public class AttendanceDailyActivityVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceDailyActivityId;

    private Long attendanceDailyActivityFeedbackRecordId;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 会员学习日期
     */
    @ExcelProperty(value = "会员学习日期",index = 0)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordDate;

    /**
     * 会员姓名
     */
    @ExcelProperty(value = "会员姓名",index = 1)
    private String studentName;

    /**
     * 反馈状态
     */
    @ExcelProperty(value = "反馈状态",index = 3,converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=未反馈,2=已反馈,3=超时反馈,4=超时未反馈")
    private Integer feedbackStatus;

    /**
     * 发布状态
     */
    @ExcelProperty(value = "发布状态" ,index = 4,converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=暂存,2=未发布")
    private Integer publishStatus;

    private RemoteStaffVo consultantInfo;

    @ExcelProperty(value = "会员顾问" ,index = 2)
    private String consultantName;
    private Long createBy;


    /**
     * 反馈记录ID
     */
    private Long feedbackRecordId;

    /**
     * 姓名(电话后四位)，电话在这里其实就是用户名
     */
    private String nameWithPhone;
    /**
     * 反馈内容
     */
    private String feedbackContent;
}
