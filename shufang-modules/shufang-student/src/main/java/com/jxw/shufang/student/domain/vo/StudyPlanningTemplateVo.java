package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudyPlanningTemplate;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 学习规划模板视图对象 study_planning_template
 *
 *
 * @date 2024-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanningTemplate.class)
public class StudyPlanningTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学习规划模板id
     */
    @ExcelProperty(value = "学习规划模板id")
    private Long studyPlanningTemplateId;

    /**
     * 模板名称
     */
    @ExcelProperty(value = "模板名称")
    private String templateName;

    /**
     * 模板描述
     */
    @ExcelProperty(value = "模板描述")
    private String templateInfo;

    /**
     * 模板说明
     */
    @ExcelProperty(value = "模板说明")
    private String templateDesc;

    /**
     * 课程学段（与课程共用一个字典）
     */
    @ExcelProperty(value = "课程学段", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "与=课程共用一个字典")
    private String courseStage;

    /**
     * 课程学年（与课程共用一个字典），这里允许存在多个，使用逗号分隔
     */
    @ExcelProperty(value = "课程学年", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "与=课程共用一个字典")
    private String courseGrades;

    /**
     * 模版上下架状态，0：下架，1：上架
     */
    @ExcelProperty(value = "模版上下架状态，0：下架，1：上架")
    private String templateStatus;

    /**
     * 模板来源，对应枚举类SourceEnum
     */
    @ExcelProperty(value = "模板来源，对应枚举类SourceEnum")
    private String sourceType;

    /**
     * 来源为门店的时候，这里关联门店id
     */
    @ExcelProperty(value = "来源为门店的时候，这里关联门店id")
    private Long branchId;


    List<StudyPlanningTemplateInfoVo> studyPlanningTemplateInfoVoList;

    /**
     * 科目数
     */
    private Long subjectCount;

    /**
     * 课程数
     */
    private Long courseCount;

    /**
     * 课次数
     */
    private Long chapterCount;

    private Date createTime;

    private Date updateTime;

    private String affiliationSubjects;

    private RemoteBranchVo branch;
}
