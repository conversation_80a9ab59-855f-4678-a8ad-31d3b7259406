package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiTestRecordBo;
import com.jxw.shufang.student.domain.vo.AiTestRecordVo;
import com.jxw.shufang.student.service.IAiTestRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ai测验记录
 * 前端访问路由地址为:/student/aiTestRecord
 *
 *
 * @date 2024-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/aiTestRecord")
public class AiTestRecordController extends BaseController {

    private final IAiTestRecordService aiTestRecordService;

    /**
     * 查询ai测验记录列表
     */
    @SaCheckPermission("student:aiTestRecord:list")
    @GetMapping("/list")
    public TableDataInfo<AiTestRecordVo> list(AiTestRecordBo bo, PageQuery pageQuery) {
        return aiTestRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ai测验记录列表
     */
    @SaCheckPermission("student:aiTestRecord:export")
    @Log(title = "ai测验记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiTestRecordBo bo, HttpServletResponse response) {
        List<AiTestRecordVo> list = aiTestRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "ai测验记录", AiTestRecordVo.class, response);
    }

    /**
     * 获取ai测验记录详细信息
     *
     * @param aiTestRecordId 主键
     */
    @SaCheckPermission("student:aiTestRecord:query")
    @GetMapping("/{aiTestRecordId}")
    public R<AiTestRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long aiTestRecordId) {
        return R.ok(aiTestRecordService.queryById(aiTestRecordId));
    }

    /**
     * 新增ai测验记录
     */
    @SaCheckPermission("student:aiTestRecord:add")
    @Log(title = "ai测验记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiTestRecordBo bo) {
        return toAjax(aiTestRecordService.insertByBo(bo));
    }

    /**
     * 修改ai测验记录
     */
    @SaCheckPermission("student:aiTestRecord:edit")
    @Log(title = "ai测验记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiTestRecordBo bo) {
        return toAjax(aiTestRecordService.updateByBo(bo));
    }

    /**
     * 删除ai测验记录
     *
     * @param aiTestRecordIds 主键串
     */
    @SaCheckPermission("student:aiTestRecord:remove")
    @Log(title = "ai测验记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{aiTestRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] aiTestRecordIds) {
        return toAjax(aiTestRecordService.deleteWithValidByIds(List.of(aiTestRecordIds), true));
    }
}
