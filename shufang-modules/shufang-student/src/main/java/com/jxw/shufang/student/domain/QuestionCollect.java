package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("question_collect")
public class QuestionCollect extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 错题id
     */
    @TableId(value = "question_collect_id")
    private Long questionCollectId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 科目ID（cds_subject）
     */
    private Integer subjectId;
}
