package com.jxw.shufang.student.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.jxw.shufang.common.core.constant.QueueTopicConstants;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.service.studyduration.DurationTimeFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 消费者
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = QueueTopicConstants.STUDY_VIDEO_RECORD_TOPIC,
    consumerGroup = QueueTopicConstants.STUDY_RECORD_CONSUMER_GROUP,
    messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.CONCURRENTLY
)
public class StudyVideoRecordConsumer implements RocketMQListener<String> {

    private final DurationTimeFactory durationTimeFactory;
    private final RedissonClient redissonClient;

    @Override
    public void onMessage(String data) {
        log.info("开始处理" + QueueTopicConstants.STUDY_VIDEO_RECORD_TOPIC + "消息:{}", data);
        StudyVideoRecordBo studyRecordBo = JSONObject.parseObject(data, StudyVideoRecordBo.class);
        if (null == studyRecordBo) {
            return;
        }

        String lockKey = "study_record_lock:" + studyRecordBo.getStudentId() + ":"
            + studyRecordBo.getCourseId() + ":" + studyRecordBo.getStudyPlanningRecordId();
        RLock clientLock = redissonClient.getLock(lockKey);
        try {
            boolean isLocked = clientLock.tryLock(600, 900, TimeUnit.MILLISECONDS);
            if (!isLocked) {
                log.error("获取锁失败，消息等待下次重试,计划ID：{}", studyRecordBo.getStudyPlanningRecordId());
                throw new RuntimeException("获取锁失败，消息等待下次重试");
            }
            StudyModuleAndGroupEnum studyModuleType = studyRecordBo.getStudyModuleType();
            List<StudyVideoRecordBo> studyVideoRecordBos = Collections.singletonList(studyRecordBo);
            durationTimeFactory.getStudyRecordService(studyModuleType).process(studyVideoRecordBos, studyModuleType);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("线程在等待锁时被中断", e);
            throw new RuntimeException("线程被中断", e);
        } catch (Exception e) {
            log.error("其他异常", e);
            throw new RuntimeException(e);
        } finally {
            if (clientLock.isHeldByCurrentThread()) {
                clientLock.unlock();
            }
        }

    }
}
