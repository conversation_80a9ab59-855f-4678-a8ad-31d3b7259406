package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.student.domain.StudyPlanningPending;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 需学习规划学生视图对象 study_planning_pending
 *
 * @date 2024-06-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudyPlanningPending.class)
public class StudyPlanningPendingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（不导出）
     */
    private Long id;

    /**
     * 学生ID（不导出）
     */
    private Long studentId;

    /**
     * 模式类型:1-春秋模式,2-寒暑模式（不导出）
     */
    private Integer modeType;

    /**
     * 计划开始日期
     */
    @ExcelProperty(value = "学习规划时间范围-开始日期")
    private Date planStartDate;

    /**
     * 计划结束日期
     */
    @ExcelProperty(value = "学习规划时间范围-结束日期")
    private Date planEndDate;

    /**
     * 反馈状态:0-待反馈,1-已反馈（不导出，用于计算）
     */
    private Integer feedbackStatus;

    /**
     * 是否超时反馈：0-否,1-是（不导出，用于计算）
     */
    private Integer overtimeFeedback;

    /**
     * 规划状态:0-待规划,1-已规划（不导出，用于计算）
     */
    private Integer planningStatus;

    /**
     * 是否超时规划（不导出，用于计算）
     */
    private Integer overtimePlanning;

    /**
     * 应规划时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedPlanningTime;

    /**
     * 应反馈时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectedFeedbackTime;

    /**
     * 实际规划时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualPlanningTime;

    /**
     * 实际反馈时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualFeedbackTime;

    /**
     * 创建时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间（不导出）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 会员姓名
     */
    @ExcelProperty(value = "会员姓名")
    private String studentName;

    /**
     * 会员顾问姓名
     */
    @ExcelProperty(value = "会员顾问")
    private String consultantName;

    /**
     * 会员最后登录时间
     */
    @ExcelProperty(value = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loginDate;

    /**
     * 学习规划状态（文字描述）
     */
    @ExcelProperty(value = "学习规划状态")
    private String planningStatusText;

    /**
     * 学习规划反馈状态（文字描述）
     */
    @ExcelProperty(value = "学习规划反馈状态")
    private String feedbackStatusText;

}
