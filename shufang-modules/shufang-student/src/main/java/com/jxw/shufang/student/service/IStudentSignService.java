package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudentSign;
import com.jxw.shufang.student.domain.bo.StudentSignBo;
import com.jxw.shufang.student.domain.vo.StudentSignVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员标签Service接口
 *
 *
 * @date 2024-03-11
 */
public interface IStudentSignService {

    /**
     * 查询会员标签
     */
    StudentSignVo queryById(Long studentSignId);

    /**
     * 查询会员标签列表
     */
    TableDataInfo<StudentSignVo> queryPageList(StudentSignBo bo, PageQuery pageQuery);

    /**
     * 查询会员标签列表
     */
    List<StudentSignVo> queryList(StudentSignBo bo);

    /**
     * 新增会员标签
     */
    Boolean insertByBo(StudentSignBo bo);

    /**
     * 批量新增会员标签
     */
    Boolean insertBatchByBo(List<StudentSignBo> studentSignList);


    /**
     * 修改会员标签
     */
    Boolean updateByBo(StudentSignBo bo);

    /**
     * 校验并批量删除会员标签信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    StudentSign queryStudentSignById(Long studentSignId);

    void cleanCache();
}
