package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableId;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 反馈记录剔除学习规划关联表对象 feedback_record_exclusion
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackRecordExclusionVo.class)
public class FeedbackRecordExclusionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "feedback_exclusion_id") // MyBatis-Plus 主键注解
    private Long feedbackExclusionId;

    /**
     * 关联的反馈记录id
     */
    private Long feedbackRecordId;

    /**
     * 需要剔除的学习规划id
     */
    private Long studyPlanningRecordId;

}
