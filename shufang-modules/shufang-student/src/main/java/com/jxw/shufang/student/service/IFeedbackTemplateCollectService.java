package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.FeedbackTemplateCollectBo;
import com.jxw.shufang.student.domain.vo.FeedbackTemplateCollectVo;

import java.util.Collection;
import java.util.List;

/**
 * 反馈模板收藏Service接口
 *
 *
 * @date 2024-05-24
 */
public interface IFeedbackTemplateCollectService {

    /**
     * 查询反馈模板收藏
     */
    FeedbackTemplateCollectVo queryById(Long feedbackTemplateCollectId);

    /**
     * 查询反馈模板收藏列表
     */
    TableDataInfo<FeedbackTemplateCollectVo> queryPageList(FeedbackTemplateCollectBo bo, PageQuery pageQuery);

    /**
     * 查询反馈模板收藏列表
     */
    List<FeedbackTemplateCollectVo> queryList(FeedbackTemplateCollectBo bo);

    /**
     * 新增反馈模板收藏
     */
    Boolean insertByBo(FeedbackTemplateCollectBo bo);

    /**
     * 修改反馈模板收藏
     */
    Boolean updateByBo(FeedbackTemplateCollectBo bo);

    /**
     * 校验并批量删除反馈模板收藏信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<Long> queryTemplateIdListByCreateUserId(Long userId);

    Boolean collect(Long feedbackTemplateId,Long userId);
}
