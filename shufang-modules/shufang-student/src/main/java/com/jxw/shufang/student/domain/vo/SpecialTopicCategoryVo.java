package com.jxw.shufang.student.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 学生专题分类VO对象
 *
 *
 * @date 2024-02-29
 */
@Data
public class SpecialTopicCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专题(对应字典course_special_topic)
     */
    private String specialTopic;

    /**
     * 课程列表
     */
    private List<SpecialTopicCategoryItemVo> specialTopicCategoryItemList;
}

