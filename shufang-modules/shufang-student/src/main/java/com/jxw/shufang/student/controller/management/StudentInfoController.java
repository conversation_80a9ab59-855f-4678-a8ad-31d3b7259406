package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentInfoBo;
import com.jxw.shufang.student.domain.vo.StudentInfoVo;
import com.jxw.shufang.student.service.IStudentInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
 * 前端访问路由地址为:/student/studentInfo
 *
 *
 * @date 2024-03-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/studentInfo")
public class StudentInfoController extends BaseController {

    private final IStudentInfoService studentInfoService;

    /**
     * 查询会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    @SaCheckPermission("student:studentInfo:list")
    @GetMapping("/list")
    public TableDataInfo<StudentInfoVo> list(StudentInfoBo bo, PageQuery pageQuery) {
        return studentInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）列表
     */
    @SaCheckPermission("student:studentInfo:export")
    @Log(title = "会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentInfoBo bo, HttpServletResponse response) {
        List<StudentInfoVo> list = studentInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）", StudentInfoVo.class, response);
    }

    /**
     * 获取会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）详细信息
     *
     * @param studentInfoId 主键
     */
    @SaCheckPermission("student:studentInfo:query")
    @GetMapping("/{studentInfoId}")
    public R<StudentInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentInfoId) {
        return R.ok(studentInfoService.queryById(studentInfoId));
    }

    /**
     * 新增会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @SaCheckPermission("student:studentInfo:add")
    @Log(title = "会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentInfoBo bo) {
        return toAjax(studentInfoService.insertByBo(bo));
    }

    /**
     * 修改会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     */
    @SaCheckPermission("student:studentInfo:edit")
    @Log(title = "会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentInfoBo bo) {
        return toAjax(studentInfoService.updateByBo(bo));
    }

    /**
     * 删除会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）
     *
     * @param studentInfoIds 主键串
     */
    @SaCheckPermission("student:studentInfo:remove")
    @Log(title = "会员信息（可以考虑用动态属性，便于不同分店的会员信息的不同）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentInfoIds) {
        return toAjax(studentInfoService.deleteWithValidByIds(List.of(studentInfoIds), true));
    }
}
