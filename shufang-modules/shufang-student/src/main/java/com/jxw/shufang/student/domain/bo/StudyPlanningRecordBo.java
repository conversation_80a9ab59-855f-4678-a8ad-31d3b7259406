package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudyPlanningRecord;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 学习规划记录业务对象 study_planning_record
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudyPlanningRecord.class, reverseConvertGenerate = false)
public class StudyPlanningRecordBo extends BaseEntity {

    /**
     * 学习规划记录id
     */
    @NotNull(message = "学习规划记录id不能为空", groups = { EditGroup.class })
    private Long studyPlanningRecordId;

    /**
     * 学习规划id
     */
    @NotNull(message = "学习规划id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyPlanningId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 学习开始时间
     */
    @NotNull(message = "学习开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyStartTime;

    /**
     * 学习结束时间
     */
    @NotNull(message = "学习结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyEndTime;

    /**
     * 学习时长（还没用到）
     */
    @NotNull(message = "学习时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyDuration;

    /**
     * 学习状态（0未开始 1进行中 2已完成）（还没用到）
     */
    @NotBlank(message = "学习状态（0未开始 1进行中 2已完成）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyStatus;

    /**
     * 学习记录状态（0正常 1覆盖 2删除 ）（还没用到）
     */
    @NotBlank(message = "学习记录状态（0正常 1覆盖 2删除 ）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyRecordStatus;

    /**
     * 学习规划日期(非数据库字段，仅用于查询条件)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studyPlanningDate;

    /**
     *  学习规划日期(非数据库字段，仅用于查询条件)（这个只包含年月）yyyy-MM
     */
    private String studyPlanningDateYearMonth;


    /**
     *  学习规划日期范围开始(非数据库字段，仅用于查询条件)yyyy-MM或yyyy-MM-dd
     */
    private String dateYearMonthLimitStart;

    /**
     *  学习规划日期范围结束(非数据库字段，仅用于查询条件)yyyy-MM或yyyy-MM-dd
     */
    private String dateYearMonthLimitEnd;


    /**
     * 是否带机位座位信息
     */
    private Boolean withBranchMachineSeatInfo;

    /**
     * 是否带考勤信息
     */
    private Boolean withAttendanceInfo;

    /**
     * 是否带学习记录
     */
    private Boolean withStudyRecord;

    /**
     * 携带反馈记录
     */
    private Boolean withFeedbackRecord;

    /**
     * 是否带测试批改记录信息
     */
    private Boolean withTestCorrectionRecord;

    /**
     * 是否携带练习批改记录信息
     */
    private Boolean withPracticeCorrectionRecord;
    private Boolean withPreviewCorrectionRecord;
    private Boolean withSpeakCorrectionRecord;

    /**
     * 是否携带课程重复状态
     */
    private Boolean withCourseRepeatStatus;

    /**
     * 是否携带最后一次学习视频记录
     */
    private Boolean withLastStudyVideoRecord;

    /**
     * 是否携带会员信息
     */
    private Boolean withStudentInfo;

    /**
     * 是否携带会员系统用户信息(withStudentInfo=true时才会生效)
     */
    private Boolean withStudentSysUserInfo;

    /**
     * 是否携带课程详情（属性+字典）
     */
    private Boolean withCourseDetail;

    /**
     * 机位座位信息
     */
    private RemoteBranchMachineSeatBo branchMachineSeat;


    private List<Long> studyPlanningRecordIdList;

    private List<Long> notInStudyPlanningRecordIdList;

    /**
     * 会员名称
     */
    private String studentName;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    private List<Long> studentIdList;

    private List<Long>  studyPlanningIdList;

    private String nameWithPhone;

    /**
     * 课程学科
     */
    private String affiliationSubject;

    /**
     * 存在学习记录，存在记录，学习时长大于0
     */
    private Boolean studyRecordHas;
    /**
     * 是否必须存在学习记录，studyRecordHas为true时检验，该字段设置为ture之后只返回有学习记录的相关数据
     */
    private Boolean studyRecordMustHas;

    private String studyPlanningStatus;

    /**
     * 学习计划日期开始时间
     */
    private String studyPlanningDateStart;

    /**
     * 学习计划日期结束时间
     */
    private String studyPlanningDateEnd;


}
