package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 会员每日打卡登录情况表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04 11:57:25
 */
@Mapper
public interface AttendanceDailyActivityMapper  extends BaseMapperPlus<AttendanceDailyActivity, AttendanceDailyActivityVo> {

    Page<AttendanceDailyActivityVo> selectNotFeedbackStudentList(@Param("page") Page<AttendanceDailyActivity> page, @Param(Constants.WRAPPER) QueryWrapper<AttendanceDailyActivity> wrapper);

    List<AttendanceDailyActivityVo> selectListByFeedbackStatus(@Param("feedbackStatus") Integer feedbackStatus);

}
