package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.IStudentParentRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员绑定家长记录--平板端
 * 前端访问路由地址为:/student/android/parentRecord
 * @date 2024-03-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/parentRecord")
public class AStudentParentRecordController extends BaseController {

    private final IStudentParentRecordService studentParentRecordService;

    /**
     * 获取会员绑定家长二维码，返回二维码的base64编码
     */
    @GetMapping("/getBoundQrCode")
    public R<String> getBoundQrCode() {
        if (!LoginHelper.isStudent()){
            return R.fail("当前用户不是会员");
        }
        return R.ok("操作成功",studentParentRecordService.getBoundQrCode(LoginHelper.getStudentId()));
    }


}
