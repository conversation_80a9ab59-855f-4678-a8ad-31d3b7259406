package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.StudentParentRecordBo;
import com.jxw.shufang.student.domain.vo.StudentParentRecordVo;
import com.jxw.shufang.student.service.IStudentParentRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会员绑定家长记录--管理端
 * 前端访问路由地址为:/student/parentRecord
 *
 *
 * @date 2024-03-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/parentRecord")
public class StudentParentRecordController extends BaseController {

    private final IStudentParentRecordService studentParentRecordService;

    /**
     * 查询会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:parentRecord:list")
    @GetMapping("/list")
    public TableDataInfo<StudentParentRecordVo> list(StudentParentRecordBo bo, PageQuery pageQuery) {
        return studentParentRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    @SaCheckPermission("student:parentRecord:export")
    @Log(title = "会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentParentRecordBo bo, HttpServletResponse response) {
        List<StudentParentRecordVo> list = studentParentRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）", StudentParentRecordVo.class, response);
    }

    /**
     * 获取会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）详细信息
     *
     * @param studentParentRecordId 主键
     */
    @SaCheckPermission("student:parentRecord:query")
    @GetMapping("/{studentParentRecordId}")
    public R<StudentParentRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long studentParentRecordId) {
        return R.ok(studentParentRecordService.queryById(studentParentRecordId));
    }

    /**
     * 新增会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:parentRecord:add")
    @Log(title = "会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentParentRecordBo bo) {
        return toAjax(studentParentRecordService.insertByBo(bo));
    }

    /**
     * 修改会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     */
    @SaCheckPermission("student:parentRecord:edit")
    @Log(title = "会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentParentRecordBo bo) {
        return toAjax(studentParentRecordService.updateByBo(bo));
    }

    /**
     * 删除会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）
     *
     * @param studentParentRecordIds 主键串
     */
    @SaCheckPermission("student:parentRecord:remove")
    @Log(title = "会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentParentRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] studentParentRecordIds) {
        return toAjax(studentParentRecordService.deleteWithValidByIds(List.of(studentParentRecordIds), true));
    }

    /**
     * 获取会员绑定家长二维码，返回二维码的base64编码 TODO
     */
    @GetMapping("/getBoundQrCode")
    public R<String> getBoundQrCode(@NotNull(message = "会员id不能为空") Long studentId) {
        return R.ok("操作成功",studentParentRecordService.getBoundQrCode(studentId));
    }

    /**
     * 获取学习反馈H5地址
     * @param feedbackId 学习反馈ID
     * @return 学习反馈H5地址
     */
    @GetMapping("/getFeedbackUrl")
    public R<String> getFeedbackUrl(@NotNull(message = "学习反馈ID不能为空") Long feedbackId) {
        return R.ok("操作成功",studentParentRecordService.getFeedbackUrl(feedbackId));
    }


}
