package com.jxw.shufang.student.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.metadata.Cell;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.ValidatorUtils;
import com.jxw.shufang.common.excel.core.DefaultExcelListener;
import com.jxw.shufang.common.excel.core.ExcelResult;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.student.domain.bo.CourseBo;
import com.jxw.shufang.student.domain.vo.CourseImportVo;
import com.jxw.shufang.student.domain.vo.CourseVo;
import com.jxw.shufang.student.service.ICourseService;
import com.jxw.shufang.system.api.RemoteAttributeService;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;
import com.jxw.shufang.system.api.domain.vo.RemoteAttributeVo;
import com.jxw.shufang.system.api.enums.AttrGroupTypeEnum;
import jakarta.validation.ConstraintViolation;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CourseImportNewListener extends DefaultExcelListener<CourseImportVo> {

    // 属性表头，key为表头所在列index，value为表头名称
    private final Map<Integer, String> attrHeaderMap = new HashMap<>();

    // 属性map，key为属性名称，value为属性信息
    private final Map<String, RemoteAttributeVo> attributeMap = new HashMap<>();

    // 章节表头，key为表头所在列index，value为表头名称
    private final Map<Integer, String> chapterHeaderMap = new HashMap<>();

    // 基础表头
    private static final String[] baseHeader = new String[]{"学段", "学科", "课程专题", "课程来源", "年级", "课程名称"};

    //“课程名称”表头所在列index
    private Integer courseNameHeaderIndex;

    private final RemoteAttributeService remoteAttributeService;

    //存放课程信息
    private final Map<String, List<CourseImportVo>> courseMap = new HashMap<>();

    //当前读取课程的一个信息
    private String currentCourseKey;

    private final ICourseService courseService;

    //当前读取的课程是否有错误
    private boolean currCourseErrSign = false;

    //错误信息
    private final StringBuilder errMsg = new StringBuilder();

    //mybatis-plus的雪花id生成器
    private final IdentifierGenerator identifierGenerator;

    //字典服务
    private final DictService dictService;

    private int courseSize = 0;


    public CourseImportNewListener(RemoteAttributeService remoteAttributeService) {
        // 显示使用构造函数，否则将导致空指针
        super(false);
        this.remoteAttributeService = remoteAttributeService;
        this.courseService = SpringUtils.getBean(ICourseService.class);
        this.identifierGenerator = SpringUtils.getBean(IdentifierGenerator.class);
        dictService = SpringUtils.getBean(DictService.class);
    }


    @Override
    public void invoke(CourseImportVo data, AnalysisContext context) {
        //如果第一行就课程名称为空，直接抛出异常
        if (context.readRowHolder().getRowIndex() == 1 && StringUtils.isBlank(data.getCourseName())) {
            throw new ExcelAnalysisException("第一行课程名称不能为空");
        }

        //其中某一项不为空，则定义为开始新的课程记录
        if (StringUtils.isNotBlank(data.getCourseName())
            || StringUtils.isNotBlank(data.getGrade())
            || StringUtils.isNotBlank(data.getStage())
            || StringUtils.isNotBlank(data.getAffiliationSubject())
            || StringUtils.isNotBlank(data.getSpecialTopic())
            || StringUtils.isNotBlank(data.getCourseSource())) {
            courseSize++;
            //直到遇到新的课程，才会重置标志
            currCourseErrSign = false;
            Set<ConstraintViolation<CourseImportVo>> constraintViolations = ValidatorUtils.validateReturnRes(data);
            if (!constraintViolations.isEmpty()) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据校验失败：");
                for (ConstraintViolation<CourseImportVo> constraintViolation : constraintViolations) {
                    errMsg.append(constraintViolation.getMessage()).append(";");
                }
                errMsg.append("<br/>");
                //标记当前课程有错误,下面的章节都不用处理了
                currCourseErrSign = true;
                return;
            }

            String gradeDictValue = dictService.getDictValue("course_grade", data.getGrade());
            if (StringUtils.isBlank(gradeDictValue)) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据有误，年级不存在;<br/>");
                currCourseErrSign = true;
                return;
            }
            String stageDictValue = dictService.getDictValue("course_stage", data.getStage());
            if (StringUtils.isBlank(stageDictValue)) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据有误，学段不存在;<br/>");
                currCourseErrSign = true;
                return;
            }
            String affiliationSubjectDictValue = dictService.getDictValue("course_affiliation_subject", data.getAffiliationSubject());
            if (StringUtils.isBlank(affiliationSubjectDictValue)) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据有误，学科不存在;<br/>");
                currCourseErrSign = true;
                return;
            }

            String courseSpecialTopic = dictService.getDictValue("course_special_topic", data.getSpecialTopic());
            if (StringUtils.isBlank(courseSpecialTopic)) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据有误，课程专题不存在;<br/>");
                currCourseErrSign = true;
                return;
            }
            String courseSource = dictService.getDictValue("course_source", data.getCourseSource());
            if (StringUtils.isBlank(courseSource)) {
                errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行数据有误，课程来源不存在;<br/>");
                currCourseErrSign = true;
                return;
            }
            String courseName = data.getCourseName();
            currentCourseKey = gradeDictValue + ":" + stageDictValue + ":" + courseName + ":" + affiliationSubjectDictValue;
            if (!courseMap.containsKey(currentCourseKey)) {
                boolean b = putCourseAttr(data, context);
                if (!b) {
                    currCourseErrSign = true;
                    return;
                }
                courseMap.put(currentCourseKey, CollUtil.newArrayList(data));
                data.setParentId(0L);
                data.setCourseType(UserConstants.TOP_COURSE_TYPE);
                data.setCurrentRowIndex(context.readRowHolder().getRowIndex() + 1);
                data.setGrade(gradeDictValue);
                data.setStage(stageDictValue);
                data.setAffiliationSubject(affiliationSubjectDictValue);
                data.setSpecialTopic(courseSpecialTopic);
                data.setCourseSource(courseSource);
            }
        }

        //主课程有错误，不处理子章节
        if (currCourseErrSign) {
            return;
        }

        //存子章节
        for (Map.Entry<Integer, String> chapterHeaderItem : chapterHeaderMap.entrySet()) {
            Integer key = chapterHeaderItem.getKey();
            Cell cell = context.readRowHolder().getCellMap().get(key);
            if (cell == null) {
                continue;
            }
            String chapterName = ((ReadCellData) cell).getStringValue();
            if (StringUtils.isBlank(chapterName)) {
                continue;
            }
            CourseImportVo chapter = new CourseImportVo();
            chapter.setCourseName(chapterName);
            //按照表名的排序规律，比课程的索引大几，那么是子章节的courseType,主课程本身的courseType为1
            chapter.setCourseType(chapterHeaderItem.getKey() - courseNameHeaderIndex + 1);
            chapter.setCurrentRowIndex(context.readRowHolder().getRowIndex() + 1);
            chapter.setSort(data.getSort());
//            chapter.setKnowledgeId(data.getKnowledgeId());
            courseMap.get(currentCourseKey).add(chapter);
        }
    }

    /**
     * 放入课程属性
     *
     * @param courseImportVo 课程导入vo
     * @param context        上下文
     * @boolean 是否全部成功，true成功，false失败，失败会往错误信息里面添加错误信息
     * @date 2024/04/01 05:39:30
     */
    private boolean putCourseAttr(CourseImportVo courseImportVo, AnalysisContext context) {
        boolean sign = true;
        if (courseImportVo.getAttrMap() == null) {
            courseImportVo.setAttrMap(new HashMap<>());
        }
        Map<Integer, Cell> cellMap = context.readRowHolder().getCellMap();
        for (Map.Entry<Integer, String> attrHeaderItem : attrHeaderMap.entrySet()) {
            Integer attrHeaderIndex = attrHeaderItem.getKey();
            String attrName = attrHeaderItem.getValue();
            RemoteAttributeVo remoteAttributeVo = attributeMap.get(attrName);
            String canNullStatus = remoteAttributeVo.getCanNullStatus();
            Cell cell = cellMap.get(attrHeaderIndex);
            if ((cell == null || StringUtils.isBlank(((ReadCellData) cell).getStringValue()))) {
                if (UserConstants.ATTR_STATUS_NO.equals(canNullStatus)){
                    errMsg.append("第").append(context.readRowHolder().getRowIndex()).append("行").append(attrName).append("不能为空;<br/>");
                    sign = false;
                    continue;
                }
                continue;
            }
            courseImportVo.getAttrMap().put(remoteAttributeVo.getAttributeId(), ((ReadCellData) cell).getStringValue());
        }
        return sign;
    }
    /**
     * 在所有分析完成后执行的处理逻辑。
     * 对于给定的分析上下文，此方法将处理父级和子级之间的关系，并保存课程信息。
     *
     * @param context 分析上下文，包含分析过程中收集的所有信息。
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        handleParentChildRelation(courseMap);
//        handleKnowledgeId(courseMap);
        saveCourse();
    }

    private void handleKnowledgeId(Map<String, List<CourseImportVo>> courseMap) {
        for (Map.Entry<String, List<CourseImportVo>> courseEntry : courseMap.entrySet()) {
            //非叶子节点去除知识点id
            List<CourseImportVo> list = courseEntry.getValue();
            list.stream().filter(e -> Boolean.TRUE.equals(e.getHasChild())).forEach(e -> e.setKnowledgeId(null));
        }
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

        int typeIndex = headMap.entrySet().stream().filter(entry -> entry.getValue().equals("教材版本")).map(Map.Entry::getKey).findFirst().get();
        Map<Integer, String> map = headMap.entrySet().stream().filter(entry -> entry.getKey() == typeIndex).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollUtil.isNotEmpty(map)) {
            attrHeaderMap.putAll(map);
        }

        //获取course的动态属性,课程没有分门店，所以传-1
        List<RemoteAttributeVo> attributeList = remoteAttributeService.getAttributeByGroupType(AttrGroupTypeEnum.COURSE.getType(), -1L, true);
        if (attributeList == null) {
            attributeList = CollUtil.newArrayList();
        }
        attributeMap.putAll(attributeList.stream().collect(Collectors.toMap(RemoteAttributeVo::getAttributeName, e -> e)));

        courseNameHeaderIndex = headMap.entrySet().stream().filter(entry -> entry.getValue().equals("课程名称")).map(Map.Entry::getKey).findFirst().get();

        //记录X级名称（章节）的表头，课程名称后面到知识点ID之间的都是
        Map<Integer, String> chapterHeader = headMap.entrySet().stream().filter(entry -> (entry.getKey() > courseNameHeaderIndex) && StringUtils.isNotBlank(entry.getValue())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollUtil.isNotEmpty(chapterHeader)) {
            chapterHeaderMap.putAll(chapterHeader);
        }

    }

    @Override
    public ExcelResult<CourseImportVo> getExcelResult() {
        //拿到所有courseType为1的课程
        List<CourseImportVo> courseImportVoList = courseMap.entrySet().stream().map(Map.Entry::getValue).flatMap(List::stream).filter(e -> e.getCourseType() == UserConstants.TOP_COURSE_TYPE).collect(Collectors.toList());
        int successNum = courseImportVoList.stream().filter(e -> !e.getHasError()).mapToInt(e -> 1).sum();
        int failureNum = courseImportVoList.size() - successNum;
        return new ExcelResult<CourseImportVo>() {

            @Override
            public List<CourseImportVo> getList() {
                return List.of();
            }

            @Override
            public List<String> getErrorList() {
                return List.of();
            }

            @Override
            public String getAnalysis() {
                if (failureNum > 0) {
                    if (failureNum == courseSize) {
                        return ("导入全部失败，，错误如下：" + errMsg.toString());
                    } else {
                        return ("部分导入失败,成功" + successNum + "门课程，失败" + failureNum + "门课程，错误如下：" + errMsg.toString());
                    }
                }
                return ("导入全部成功，共" + successNum + "门课程");
            }
        };
    }


    /**
     * 处理父子关系
     */
    private void handleParentChildRelation(Map<String, List<CourseImportVo>> courseMap) {
        //循环所有课程，处理父子关系
        for (Map.Entry<String, List<CourseImportVo>> courseEntry : courseMap.entrySet()) {
            //因为是按照从左往右，从上往下的方式添加的章节，所以这里是有序的
            List<CourseImportVo> courseList = courseEntry.getValue();
            //第一个为主课程
            CourseImportVo mainCourse = courseList.get(0);
            putCourseId(mainCourse, true);
            mainCourse.setHasError(false);

            //主课程的祖先默认是0
            mainCourse.setAncestors("0");
            //处理章节
            //先按照courseType分组，并且按照courseType升序排序，再按照currentRowIndex分组，并且按照currentRowIndex升序排序
            Map<Integer, Map<Integer, CourseImportVo>> chapterMap = courseList.stream().collect(Collectors.groupingBy(CourseImportVo::getCourseType, TreeMap::new, Collectors.toMap(CourseImportVo::getCurrentRowIndex, Function.identity(), (k1, k2) -> k1, TreeMap::new)));

            //courseType升序排序，保证找到的上一级一定是先处理过的
            for (Map.Entry<Integer, Map<Integer, CourseImportVo>> integerMapEntry : chapterMap.entrySet()) {
                Integer key = integerMapEntry.getKey();
                //最顶级跳过
                if (key == UserConstants.TOP_COURSE_TYPE) {
                    continue;
                }
                Integer parentTypeKey = key - 1;
                Map<Integer, CourseImportVo> parentTypeMap = chapterMap.get(parentTypeKey);
                if (parentTypeMap == null) {
                    mainCourse.setHasError(true);
                    errMsg.append("第").append(integerMapEntry.getValue().values().iterator().next().getCurrentRowIndex()).append("行数据有误，找不到父级章节;<br/>");
                    break;
                }
                //可能的父级行号,按照从小到大排序
                Set<Integer> maybeRowIndexSet = parentTypeMap.keySet();
                for (Map.Entry<Integer, CourseImportVo> entry : integerMapEntry.getValue().entrySet()) {
                    Integer rowIndex = entry.getKey();
                    //一行行的往上找（包含自己），并且保证在maybeRowIndexSet里面，找出接近自己的那一行
                    Integer parentRowIndex = maybeRowIndexSet.stream()
                        .filter(e -> e <= rowIndex)
                        .max(Integer::compareTo)
                        .orElse(null);
                    if (parentRowIndex == null) {
                        mainCourse.setHasError(true);
                        errMsg.append("第").append(rowIndex).append("行数据有误，找不到父级章节;<br/>");
                        break;
                    }
                    CourseImportVo parent = parentTypeMap.get(parentRowIndex);
                    parent.setHasChild(true);
                    entry.getValue().setParentId(parent.getCourseId());
                    putCourseId(entry.getValue(), parent.getExist());
                    entry.getValue().setAncestors(parent.getAncestors() + "," + parent.getCourseId());
                }
            }


            courseList.stream().filter(e -> !Boolean.TRUE.equals(e.getHasChild())).forEach(e -> e.setHasError(false));
        }
    }


    /**
     * put课程id
     *
     * @param courseImportVo 课程导入vo
     * @param queryExist     查询存在
     * @date 2024/04/01 10:03:18
     */
    public void putCourseId(CourseImportVo courseImportVo, boolean queryExist) {
        if (!queryExist) {
            courseImportVo.setExist(false);
            courseImportVo.setCourseId(identifierGenerator.nextId(courseImportVo).longValue());
            return;
        }
        CourseBo courseBo = new CourseBo();
        courseBo.setGrade(courseImportVo.getGrade());
        courseBo.setStage(courseImportVo.getStage());
        courseBo.setCourseName(courseImportVo.getCourseName());
        courseBo.setAffiliationSubject(courseImportVo.getAffiliationSubject());
        courseBo.setCourseParentId(courseImportVo.getParentId());
        courseBo.setDelFlag(UserConstants.DEL_FLAG_NO);
        CourseVo courseVo = courseService.getOnceByBo(courseBo);
        if (courseVo == null) {
            courseImportVo.setExist(false);
            courseImportVo.setCourseId(identifierGenerator.nextId(courseBo).longValue());
        } else {
            courseImportVo.setExist(true);
            courseImportVo.setCourseId(courseVo.getCourseId());
        }
    }


    public void saveCourse() {
        //拿出所有课程，排除有错误的主课程
        List<CourseImportVo> courseImportVoList = courseMap.entrySet().stream().filter(e -> !e.getValue().get(0).getHasError()).map(e -> e.getValue()).flatMap(List::stream).collect(Collectors.toList());
        //service需要的BO
        List<CourseBo> courseBoList = new ArrayList<>();
        for (CourseImportVo courseImportVo : courseImportVoList) {
            CourseBo courseBo = new CourseBo();
            courseBo.setCourseId(courseImportVo.getCourseId());
            courseBo.setGrade(courseImportVo.getGrade());
            courseBo.setCourseType(courseImportVo.getCourseType());
            courseBo.setStage(courseImportVo.getStage());
            courseBo.setCourseName(courseImportVo.getCourseName());
            courseBo.setAffiliationSubject(courseImportVo.getAffiliationSubject());
            courseBo.setCourseParentId(courseImportVo.getParentId());
            courseBo.setAncestors(courseImportVo.getAncestors());
            courseBo.setKnowledgeId(courseImportVo.getKnowledgeId());
            courseBo.setSpecialTopic(courseImportVo.getSpecialTopic());
            courseBo.setCourseSource(courseImportVo.getCourseSource());
            courseBo.setQuarterType(courseImportVo.getQuarterType());
            courseBo.setSort(courseImportVo.getSort());
            Map<Long, String> attrMap = courseImportVo.getAttrMap();
            if (CollUtil.isNotEmpty(attrMap)) {
                List<RemoteAttributeRelationBo> remoteAttributeRelationBos = new ArrayList<>();
                for (Map.Entry<Long, String> entry : attrMap.entrySet()) {
                    RemoteAttributeRelationBo remoteAttributeRelationBo = new RemoteAttributeRelationBo();
                    remoteAttributeRelationBo.setAttributeId(entry.getKey());
                    remoteAttributeRelationBo.setValue(entry.getValue());
                    remoteAttributeRelationBo.setTypeId(courseImportVo.getCourseId());
                    remoteAttributeRelationBo.setType(AttrGroupTypeEnum.COURSE.getType());
                    remoteAttributeRelationBos.add(remoteAttributeRelationBo);
                }
                courseBo.setAttributeRelationList(remoteAttributeRelationBos);
            }

            courseBoList.add(courseBo);
        }
        if (CollUtil.isNotEmpty(courseBoList)) {
            courseService.insertBatchByBoList(courseBoList);
        }
    }


}
