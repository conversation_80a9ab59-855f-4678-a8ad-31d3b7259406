package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AttendanceLogStudentEzkeco;

import java.util.Date;
import java.util.List;

/**
 * ezkeco学员考勤记录业务对象 attendance_log_student_ezkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AttendanceLogStudentEzkeco.class, reverseConvertGenerate = false)
public class AttendanceLogStudentEzkecoBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = { EditGroup.class })
    private Long attendanceLogStudentEzkecoId;

    /**
     * 流水号
     */
    private Long logId;

    /**
     * 验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)
     */
    @NotBlank(message = "验证方式 (详见获取考勤记录功能介绍附录：考勤记录验证方式表)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String verify;

    /**
     * 打卡时间
     */
    @NotNull(message = "打卡时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date checktime;

    /**
     * 设备序列号
     */
    @NotBlank(message = "设备序列号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sn;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String alias;

    /**
     * 人员编号
     */
    @NotBlank(message = "人员编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String pin;

    /**
     * 考勤状态说明
     */
    @NotBlank(message = "考勤状态说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String state;

    /**
     * 考勤关联用户_id
     */
    @NotNull(message = "考勤关联用户_id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long attendanceUserId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 学员id
     */
    @NotNull(message = "学员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 人员姓名
     */
    @NotBlank(message = "人员姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nickName;

    /**
     * 照片地址
     */
    @NotBlank(message = "照片地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String photograph;

    /**
     * 是否通知家长（数据字典：sys_yes_no）
     */
    @NotBlank(message = "是否通知家长（数据字典：sys_yes_no）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String notifyParents;

    /**
     * 是否绑定家长（数据字典：sys_yes_no）
     */
    @NotBlank(message = "是否绑定家长（数据字典：sys_yes_no）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bindParents;

    /**
     * 备注说明
     */
    @NotBlank(message = "备注说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    private List<Long> studentIdList;


    private String rangeStartDate;

    private String rangeEndDate;

    /**
     * 是否查询会员顾问信息
     */
    private Boolean withConsultantInfo;

    /**
     * 会员顾问姓名
     */
    private String consultantName;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 会员账号
     */
    private String studentAccount;

}
