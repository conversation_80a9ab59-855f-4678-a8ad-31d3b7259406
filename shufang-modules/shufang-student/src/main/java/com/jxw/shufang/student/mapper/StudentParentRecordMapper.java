package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.StudentParentRecord;
import com.jxw.shufang.student.domain.vo.StudentParentRecordVo;

import java.util.List;

/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）Mapper接口
 *
 *
 * @date 2024-03-11
 */
public interface StudentParentRecordMapper extends BaseMapperPlus<StudentParentRecord, StudentParentRecordVo> {

    List<StudentParentRecordVo> queryBindList(@Param(Constants.WRAPPER) QueryWrapper<StudentParentRecord> studentParentRecordQueryWrapper);
}
