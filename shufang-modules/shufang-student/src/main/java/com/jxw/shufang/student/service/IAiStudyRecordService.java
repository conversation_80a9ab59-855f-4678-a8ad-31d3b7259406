package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.AiStudyRecord;
import com.jxw.shufang.student.domain.bo.AiStudyRecordBo;
import com.jxw.shufang.student.domain.vo.AiStudyRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * ai学习记录Service接口
 *
 *
 * @date 2024-05-21
 */
public interface IAiStudyRecordService {

    /**
     * 查询ai学习记录
     */
    AiStudyRecordVo queryById(Long aiStudyRecordId);

    /**
     * 查询ai学习记录列表
     */
    TableDataInfo<AiStudyRecordVo> queryPageList(AiStudyRecordBo bo, PageQuery pageQuery);

    /**
     * 查询ai学习记录列表
     */
    List<AiStudyRecordVo> queryList(AiStudyRecordBo bo);

    /**
     * 新增ai学习记录
     */
    Boolean insertByBo(AiStudyRecordBo bo);

    /**
     * 修改ai学习记录
     */
    Boolean updateByBo(AiStudyRecordBo bo);

    /**
     * 校验并批量删除ai学习记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AiStudyRecordVo queryOnce(AiStudyRecordBo aiStudyRecordBo);

    AiStudyRecordVo queryByStudentIdAndCourseId(Long studentId, Long courseId);

    TableDataInfo<AiStudyRecordVo> queryStudyRecordPage(AiStudyRecordBo bo, PageQuery pageQuery);

    List<AiStudyRecord> batchQueryByStudentIdAndTestPaperId(List<Long> studentIds, List<Long> testPaperId);

    List<AiStudyRecord> batchQueryByStudentIdAndTestCourseId(List<Long> studentIds, List<Long> courseIds);

    void batchInsert(List<AiStudyRecord> studyProcessorInserts);

    void batchUpdate(List<AiStudyRecord> studyProcessorUpdates);

    AiStudyRecord selectStudyAitestRecordByStudentCourse(Long studentId, Long courseId);

    void updateBatchById(List<AiStudyRecord> updated);
}
