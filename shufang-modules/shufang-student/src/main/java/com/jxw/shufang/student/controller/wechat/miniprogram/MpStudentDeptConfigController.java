package com.jxw.shufang.student.controller.wechat.miniprogram;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.api.RemoteDeptConfigService;
import com.jxw.shufang.system.api.domain.vo.RemoteSysDeptConfigVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/deptConfig")
public class MpStudentDeptConfigController {

    @DubboReference
    private final RemoteDeptConfigService remoteDeptConfigService;

    /**
     * 获取部门配置
     */
    @GetMapping("/get")
    public R<RemoteSysDeptConfigVo> selectDeptById() {
        Long deptId = LoginHelper.getDeptId();
        if (ObjectUtils.isEmpty(deptId)){
            return R.fail("部门不存在");
        }
        RemoteSysDeptConfigVo remoteSysDeptConfigVo = remoteDeptConfigService.selectDeptById(deptId);
        return R.ok(remoteSysDeptConfigVo);
    }
}
