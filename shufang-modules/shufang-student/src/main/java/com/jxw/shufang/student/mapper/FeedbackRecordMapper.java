package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.FeedbackRecord;
import com.jxw.shufang.student.domain.vo.FeedbackRecordVo;

import java.util.List;

/**
 * 反馈记录Mapper接口
 *
 *
 * @date 2024-05-24
 */
public interface FeedbackRecordMapper extends BaseMapperPlus<FeedbackRecord, FeedbackRecordVo> {

    Page<FeedbackRecordVo> selectRecordPage(@Param("page") Page<Object> build,@Param(Constants.WRAPPER) QueryWrapper<FeedbackRecord> lqw);

    List<FeedbackRecordVo> selectRecordList(@Param(Constants.WRAPPER) QueryWrapper<FeedbackRecord> lqw);

    FeedbackRecordVo queryLatestFeedbackRecord(@Param("feedbackRecordId") Long feedbackRecordId,@Param("studentId") Long studentId);
}
