package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.Product;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 产品（会员卡）视图对象 product
 *
 *
 * @date 2024-02-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Product.class)
public class ProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    @ExcelProperty(value = "产品id")
    private Long productId;

    /**
     * 会员类型id
     */
    @ExcelProperty(value = "会员类型id")
    private Long studentTypeId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品有效天数
     */
    @ExcelProperty(value = "产品有效天数")
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    @ExcelProperty(value = "产品有效期")
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    @ExcelProperty(value = "产品价格")
    private BigDecimal productPrice;

    /**
     * 产品状态（0上架 1下架）
     */
    @ExcelProperty(value = "产品状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=上架,1=下架")
    private String productStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 会员类型
     */
    private StudentTypeVo studentType;



}
