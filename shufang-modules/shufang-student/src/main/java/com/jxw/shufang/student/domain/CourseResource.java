package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 课程资源（绑定到课程的资源）对象 course_resource
 *
 *
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("course_resource")
public class CourseResource extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程资源id
     */
    @TableId(value = "course_resource_id")
    private Long courseResourceId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 资源种类（1学习视频 2课程讲义 3会员练习 4练习解答视频 5会员测验 6测验解答视频 7试卷 8试卷解析 9试卷+解析）
     */
    private String courseResourceType;

    /**
     * 资源来源（1公共资源 2本地资源）
     */
    private String resourceSource;

    /**
     * 外部资料id（公共资源才有此项，可以为表加id与外部资源进行对应）
     */
    private String externalInformationId;

    /**
     * 资料id（本地资源才有此项）
     */
    private Long informationId;

    /**
     * 资源内容（资源地址）
     */
    private String resourceContent;

    /**
     * 资源内容（oss_id）
     */
    private Long resourceOssId;

    /**
     * 资源名称（可用于重命名，包含文件后缀）
     */
    private String resourceName;

    /**
     * 资源总大小（单位KB）
     */
    private Long resourceSize;

    /**
     * 资源总时长（单位秒 学习视频才有此项）
     */
    private Long resourceDuration;

    /**
     * 题目序号（题目解答视频才有此项）
     */
    private String questionNo;

    /**
     * 题目详情（对应试卷中有哪些题型，数量多少）
     */
    private String questionInfo;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
