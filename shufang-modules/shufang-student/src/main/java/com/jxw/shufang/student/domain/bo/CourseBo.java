package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.Course;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;

import java.util.List;

/**
 * 课程（课程包含章节）业务对象 course
 *
 *
 * @date 2024-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Course.class, reverseConvertGenerate = false)
public class CourseBo extends BaseEntity {

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空", groups = {EditGroup.class})
    private Long courseId;

    /**
     * 祖先节点（最顶层默认0）
     */
    //@NotBlank(message = "祖先节点（最顶层默认0）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ancestors;

    /**
     * 年级（对应字典值 course_grade）（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "年级不能为空", groups = {AddGroup.class, EditGroup.class})
    private String grade;

    /**
     * 学段（对应字典值 course_stage）（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "学段不能为空", groups = {AddGroup.class, EditGroup.class})
    private String stage;

    /**
     * 归属学科（对应字典值 course_affiliation_subject）（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "归属学科（对应字典值）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String affiliationSubject;


    /**
     * 与外部资源知识点id关联（只有叶子节点的课程才会存）
     */
    private Long knowledgeId;

    /**
     * 课程名称
     */
    @NotBlank(message = "课程名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String courseName;

    /**
     * 课程简介（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "课程简介不能为空", groups = {AddGroup.class, EditGroup.class})
    private String courseIntroduction;

    /**
     * 课程代码（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "课程代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String courseNo;

    /**
     * 课程缩略图（oss_id）（只有最顶层的课程才会存）
     */
    //@NotNull(message = "课程缩略图（oss_id）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long courseThumbnail;

    /**
     * 课程父节点id（顶级课程默认为0）
     */
    @NotNull(message = "课程父节点id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long courseParentId;

    /**
     * 类型(1课程 2章 3节 4小节 5小小节 以此类推)
     */
    @NotNull(message = "类型(1课程 2章 3节 4小节 5小小节 以此类推)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer courseType;

    /**
     * 课程来源（对应字典值 course_source）（只有最顶层的课程才会存）
     */
    //@NotBlank(message = "课程来源（对应字典值）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String courseSource;

    /**
     * 地区，省市县之间用空格分割，理应与试卷关联，但是试卷来自第三方，并且没有返回地区数据，这里只能先跟课程关联
     */
    private String region;

    /**
     * 课程专题，对应字典course_special_topic
     */
    private String specialTopic;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 排序字段
     */
    private Integer sort;

    private Boolean orderBy;

    /*
     *  填写的属性列表
     */
    private List<RemoteAttributeRelationBo> attributeRelationList;


    /**
     * 课程id列表
     */
    private List<Long> courseIdList;
    private List<Long> notIncourseIdList;

    private Boolean withAttr;

    /**
     * 是否需要子节点
     */
    private Boolean needChild = true;


    private Boolean knowledgeIdSetNull;

    /**
     * 0插入 开头  1 结尾插入  2 中间插入
     */
    private Integer insertSortType;
    /**
     * 当insertType 为2 时 中间插入 为插入的位置
     *
     */
    private Integer insertSortNum;
    /**
     * 0为向前 1为向后插入
     */
    private Integer insertSortPosition;

    /**
     * 季度类型code2（ 字典code）
     */
    private String quarterType;
    /**
     * 可使用课程
     */
    private List<String> applicableGrade;

    /**
     * 会员ID，如果会员ID为空，则查询全部课程，如果会员ID不为空，则只能查询当前会员所属门店已授权的课程集合
     * 可以传多个
     */
    private List<Long> studentId;

    /**
     * 课次名称
     */
    private String catalogName;

}
