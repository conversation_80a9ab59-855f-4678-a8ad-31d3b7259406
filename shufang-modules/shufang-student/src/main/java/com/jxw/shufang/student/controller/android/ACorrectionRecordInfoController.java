package com.jxw.shufang.student.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.ICorrectionRecordInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 批改记录详情--平板端
 * 前端访问路由地址为:/student/android/correctionRecordInfo
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/correctionRecordInfo")
public class ACorrectionRecordInfoController extends BaseController {

    private final ICorrectionRecordInfoService correctionRecordInfoService;



}
