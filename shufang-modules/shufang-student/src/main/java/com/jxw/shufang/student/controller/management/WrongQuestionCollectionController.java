package com.jxw.shufang.student.controller.management;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.WrongQuestionCollectionBo;
import com.jxw.shufang.student.service.IWrongQuestionCollectionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 错题合集
 * 前端访问路由地址为:/student/wrongQuestionCollection
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/wrongQuestionCollection")
public class WrongQuestionCollectionController extends BaseController {

    private final IWrongQuestionCollectionService wrongQuestionCollectionService;

    /**
     * 新增错题合集
     */
    @Log(title = "错题合集", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WrongQuestionCollectionBo bo) {
        return toAjax(wrongQuestionCollectionService.addCollection(bo));
    }

}
