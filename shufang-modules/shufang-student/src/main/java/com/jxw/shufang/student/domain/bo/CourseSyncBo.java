package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 同步课程BO
 */
@Data
public class CourseSyncBo{

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空")
    private Long courseId;
    /**
     * 同步课程资源id
     */
    @NotNull(message = "父级id不能为空")
    private Long parentId;

    @NotNull(message = "同步至课程管理的课程id不能为空")
    private Long resourceId;



}
