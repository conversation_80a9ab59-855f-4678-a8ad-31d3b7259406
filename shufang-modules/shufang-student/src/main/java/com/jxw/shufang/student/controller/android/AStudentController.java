package com.jxw.shufang.student.controller.android;

import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.encrypt.annotation.ApiEncrypt;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.api.RemoteSmsService;
import com.jxw.shufang.student.domain.vo.StuAccumulatedStudyData;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.service.IStudentService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 会员-平板端
 * 前端访问路由地址为:/student/android/student
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/student")
public class AStudentController extends BaseController {

    private final IStudentService studentService;

    @DubboReference
    private RemoteSmsService remoteSmsService;

    /**
     * 会员个人中心
     */
    @GetMapping("/getInfo")
    public R<StudentVo> getInfo() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentService.getInfo(LoginHelper.getStudentId()));
    }

    /**
     * 会员个人中心
     */
    @GetMapping("/getBranch")
    public R<RemoteBranchVo> getBranch() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentService.getBranch(LoginHelper.getStudentId()));
    }

    /**
     * 更改密码接口
     *
     * @param newPwd 新密码，不能为空（最少5位，起码包含数字、字母）
     * @param oldPwd 原密码，不能为空
     * @return 返回操作结果，如果操作成功，返回一个成功的标识；如果操作失败，返回失败的原因。
     */
    @PostMapping("/changePwd")
    //@RepeatSubmit()
    @Log(title = "平板端-修改密码", businessType = BusinessType.UPDATE)
    public R<Void> changePwd( @NotBlank(message = "新密码不能为空") String newPwd,
                              @NotBlank(message = "原密码不能为空") String oldPwd) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        studentService.changePwd(LoginHelper.getStudentId(),oldPwd, newPwd,true);
        return R.ok();
    }

    /**
     * 发送验证码接口
     * 这里就不加限流注解了，因为短信验证码发送频率（代码实现了）有限制，所以不用担心频繁发送验证码
     * @param phone 手机号
     */
    @PostMapping("/sendSmsCode")
    @Log(title = "平板端-发送验证码", businessType = BusinessType.INSERT)
    @ApiEncrypt
    public R<Void> sendCode(@NotBlank(message = "手机号不能为空") String phone) {
        StudentVo studentVo = studentService.queryByStudentPhone(phone,false);
        if (studentVo == null) {
            return R.fail("手机号不存在");
        }
        remoteSmsService.sendSmsCaptcha(phone);

        return R.ok();
    }

    /**
     * 忘记密码接口
     * @param phone 手机号
     * @param code 验证码
     * @param newPwd 新密码
     */
    @PostMapping("/forgetPwd")
    @RepeatSubmit()
    @Log(title = "平板端-忘记密码", businessType = BusinessType.UPDATE)
    @ApiEncrypt
    public R<Void> forgetPwd(@NotBlank(message = "手机号不能为空") String phone,
                             @NotBlank(message = "验证码不能为空") String code,
                             @NotBlank(message = "新密码不能为空") String newPwd) {
        remoteSmsService.checkSmsCaptcha(phone, code);
        StudentVo studentVo = studentService.queryStudentByPhone(phone);
        if (studentVo == null) {
            return R.fail("手机号不存在");
        }
        studentService.changePwd(studentVo.getStudentId(),null, newPwd,false);
        return R.ok();
    }

    /**
     * 获取学习信息
     * @date 2024/04/23 03:19:57
     */
    @GetMapping("/getStudyInfo")
    public R<StuAccumulatedStudyData> getStudyInfo() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        return R.ok(studentService.getStuAccumulatedStudyData(LoginHelper.getStudentId()));
    }
}
