package com.jxw.shufang.student.controller.management;

import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.service.IWrongQuestionCollectionDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 错题合集详情
 * 前端访问路由地址为:/student/wrongQuestionCollectionDetail
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/wrongQuestionCollectionDetail")
public class WrongQuestionCollectionDetailController extends BaseController {

    private final IWrongQuestionCollectionDetailService wrongQuestionCollectionDetailService;

}
