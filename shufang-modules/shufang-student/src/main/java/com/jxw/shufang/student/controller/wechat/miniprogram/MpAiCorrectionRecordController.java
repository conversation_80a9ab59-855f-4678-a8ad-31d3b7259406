package com.jxw.shufang.student.controller.wechat.miniprogram;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionQuestionRecordVo;
import com.jxw.shufang.student.service.IAiCorrectionRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * Ai批改记录---小程序端接口
 * 前端访问路由地址为:/student/miniProgram/aiCorrectionRecord
 *
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/aiCorrectionRecord")
public class MpAiCorrectionRecordController extends BaseController {

    private final IAiCorrectionRecordService aiCorrectionRecordService;



    /**
     * 提交批改记录
     */
    @PostMapping()
    @RepeatSubmit
    @Log(title = "AI提交批改记录--小程序端", businessType = BusinessType.INSERT)
    public R<BigDecimal> submitCorrectionRecord(@RequestBody @Validated(AddGroup.class) AiCorrectionRecordBo aiCorrectionRecordBo, Long courseId) {
        if (aiCorrectionRecordBo.getStudentId() == null) {
            return R.fail("会员ID不能为空");
        }
        aiCorrectionRecordBo.setCorrectionPersonType(UserConstants.CORRECTION_PERSON_TYPE_STAFF);
        aiCorrectionRecordBo.setCourseId(courseId);
        return R.ok(aiCorrectionRecordService.insertByBo(aiCorrectionRecordBo));
    }

    /**
     * 查询批改的题目列表列表
     *
     * @param courseId  课程Id
     * @param studentId 会员Id
     * @param correctionType        批改类型 1=测试,2=练习
     */
    @GetMapping("/queryQuestionRecord")
    public R<AiCorrectionQuestionRecordVo> queryQuestionRecord(@NotNull(message = "学习规划记录ID不能为空") Long courseId,
                                                               @NotNull(message = "会员ID不能为空") Long studentId,
                                                               @NotBlank(message = "批改类型不能为空") String correctionType) {
        return R.ok(aiCorrectionRecordService.queryQuestionRecord(courseId,studentId, correctionType));
    }

}
