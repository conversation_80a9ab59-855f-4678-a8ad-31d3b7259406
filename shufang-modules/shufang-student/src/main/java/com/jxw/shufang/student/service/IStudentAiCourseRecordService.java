package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudentAiCourseRecordBo;
import com.jxw.shufang.student.domain.vo.StudentAiCourseRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IStudentAiCourseRecordService {

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    StudentAiCourseRecordVo queryById(Long studentAiCourseRecordId);

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    TableDataInfo<StudentAiCourseRecordVo> queryPageList(StudentAiCourseRecordBo bo, PageQuery pageQuery);

    /**
     * 查询会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）列表
     */
    List<StudentAiCourseRecordVo> queryList(StudentAiCourseRecordBo bo);

    /**
     * 新增会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean insertByBo(StudentAiCourseRecordBo bo);

    /**
     * 修改会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）
     */
    Boolean updateByBo(StudentAiCourseRecordBo bo);

    /**
     * 校验并批量删除会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
