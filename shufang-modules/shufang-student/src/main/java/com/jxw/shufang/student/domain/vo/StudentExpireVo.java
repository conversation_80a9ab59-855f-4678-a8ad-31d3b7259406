package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.StudentExpire;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 会员过期（用于 会员过期列 的展示和数据操作）视图对象 student_expire
 *
 *
 * @date 2024-03-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentExpire.class)
public class StudentExpireVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员过期id
     */
    @ExcelProperty(value = "会员过期id")
    private Long studentExpireId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 是否显示（0是 1否 用于假删除）
     */
    @ExcelProperty(value = "是否显示", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=是,1=否,用=于假删除")
    private String isShow;

    private StudentVo student;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String productNames;

    /*
    * 会员过期天数
     */
    private Long expireDays;

    /**
     * 会员过期时间
     */
    private Date expireTime;


    private String lastOrderProductNames;

    private Long notFinishPlanCount;


}
