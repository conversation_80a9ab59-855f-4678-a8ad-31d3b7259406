package com.jxw.shufang.student.dubbo;

import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.student.api.RemoteStudentConsultantRecordService;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentConsultantRecordBo;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentConsultantRecordServiceImpl implements RemoteStudentConsultantRecordService {

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudentService studentService;

    @DubboReference
    private RemoteStaffService remoteStaffService;



    @Override
    public Map<Long,List<Long>> getStaffResponsibleStudentIdMap(List<Long> staffIdList,boolean ignoreDataPermission) {
        if (ignoreDataPermission){
            return DataPermissionHelper.ignore(() -> studentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIdList));
        }else {
            return studentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIdList);
        }
    }

    @Override
    public List<Long> getStaffResponsibleStudentIdList(Long staffId, boolean ignoreDataPermission) {
        if (ignoreDataPermission){
            return DataPermissionHelper.ignore(() -> studentConsultantRecordService.getStaffResponsibleStudentIdList(staffId));
        }else {
            return studentConsultantRecordService.getStaffResponsibleStudentIdList(staffId);
        }
    }

    @Override
    public Long getStudentResponsibleStaffId(Long studentId, boolean ignoreDataPermission) {
        Map<Long, Long> ignore = null;
        if (ignoreDataPermission){
            ignore =   DataPermissionHelper.ignore(() -> studentConsultantRecordService.getStudentConsultantIdMap(List.of(studentId)));
        }else {
            ignore = studentConsultantRecordService.getStudentConsultantIdMap(List.of(studentId));
        }
        if (ignore!=null){
            return ignore.get(studentId);
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handoverMember(List<Long> studentIds,Long staffId) {

        AtomicReference<Integer> count = new AtomicReference<>(0);
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffId(staffId);
        List<RemoteStaffVo> remoteStaffVos;
        Long branchId = CollectionUtils.isEmpty(remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo)) ? null : remoteStaffVos.get(0).getBranchId();

        studentIds.parallelStream().forEach(studentId ->{

            StudentConsultantRecordBo bo = new StudentConsultantRecordBo();
            bo.setStudentId(studentId);
            bo.setStudentConsultantId(staffId);
            studentConsultantRecordService.insertByBo(bo);

            StudentBo studentBo = new StudentBo();
            studentBo.setStudentId(studentId);
            studentBo.setStudentConsultantRecordId(bo.getStudentConsultantRecordId());
            studentBo.setBranchId(branchId);
            studentService.updateStudentConsultantRecordIdByBo(studentBo);

            count.getAndSet(count.get() + 1);
        });

        return count.get() == studentIds.size();
    }


}
