package com.jxw.shufang.student.dubbo;

import java.math.BigDecimal;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.alibaba.druid.support.json.JSONUtils;
import com.jxw.shufang.student.api.RemoteStudentPreferentialService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentPreferentialBo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentPreferentialRecordBo;
import com.jxw.shufang.student.enums.PreferentialInOrOutEnum;
import com.jxw.shufang.student.service.IStudentPreferentialRecordService;
import com.jxw.shufang.student.service.IStudentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentPreferentialServiceImpl implements RemoteStudentPreferentialService {

    private final IStudentService studentService;

    private final IStudentPreferentialRecordService studentPreferentialRecordService;

    @Override
    public Boolean modifyStudentPreferential(RemoteStudentPreferentialBo bo) {
        if (null == bo.getStudentId() || null == bo.getModifyPreferentialAmount() || null == bo.getModifyType()) {
            log.error("更新会员优惠额度异常，更新的数据异常" + JSONUtils.toJSONString(bo));
            return false;
        }
        String errLogPrefix = "更新会员优惠额度异常，更新类型: " + bo.getModifyType() + "，会员id：" + bo.getStudentId() + "，业务id："
            + bo.getBusinessId() + ",info:";
        if (bo.getModifyPreferentialAmount().compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }
        Student student = studentService.queryStudentById(bo.getStudentId());
        boolean reducePreferential;
        BigDecimal frozenAmount = BigDecimal.ZERO;
        if (null == student) {
            log.error(errLogPrefix + "无法查询到会员信息");
            return false;
        } else if (null == student.getPreferentialAmount()
            || ((reducePreferential = bo.getModifyPreferentialAmount().compareTo(BigDecimal.ZERO) < 0)
                && student.getPreferentialAmount()
                    .subtract((frozenAmount = studentPreferentialRecordService.getFrozenAmount(student.getStudentId())))
                    .add(bo.getModifyPreferentialAmount()).compareTo(BigDecimal.ZERO) < 0)) {
            log.error(errLogPrefix + "更新金额异常，会员优惠额度" + student.getPreferentialAmount() + "，冻结金额" + frozenAmount);
            return false;
        }
        // 新增优惠额度变动记录
        StudentPreferentialRecordBo saveRecordBo = new StudentPreferentialRecordBo();
        saveRecordBo.setOwnerStudentId(student.getStudentId());
        saveRecordBo.setBusinessId(bo.getBusinessId());
        saveRecordBo.setChangeType(
            (reducePreferential ? PreferentialInOrOutEnum.OUT.getType() : PreferentialInOrOutEnum.IN.getType()));
        saveRecordBo.setGainType(bo.getModifyType().getType());
        saveRecordBo.setChangePreferentialAmount(bo.getModifyPreferentialAmount().abs());
        if (!studentPreferentialRecordService.saveRecordByBo(saveRecordBo)) {
            log.error(errLogPrefix + "插入优惠额度变动记录异常");
            return false;
        }
        // 更新会员优惠额度
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentId(student.getStudentId());
        studentBo.setPreferentialAmount(student.getPreferentialAmount());
        studentBo.setPreferentialAmountVersion(student.getPreferentialAmountVersion());
        if (!studentService.updatePreferentialAmountByBo(studentBo, bo.getModifyPreferentialAmount())) {
            log.error(errLogPrefix + "更新介绍会员时会员优惠额度更新失败");
            return false;
        }
        return true;
    }
}
