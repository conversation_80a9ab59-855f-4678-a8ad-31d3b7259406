package com.jxw.shufang.student.domain.vo;

import com.jxw.shufang.common.core.enums.PreferentialModifyTypeEnum;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentPreferentialRecord;
import com.jxw.shufang.student.enums.PreferentialInOrOutEnum;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@AutoMapper(target = StudentPreferentialRecord.class)
public class StudentPreferentialRecordVo extends BaseEntity {
    /**
     * 会员优惠额度变动记录id
     */
    private Long recordId;
    /**
     * 会员ID
     */
    private Long ownerStudentId;
    /**
     * 来源会员ID
     */
    private Long fromStudentId;
    /**
     * 业务ID
     */
    private Long businessId;
    /**
     * 支出or入账 0-收入 1-支出
     */
    private Integer changeType;
    /**
     * 获取类别 1-转介绍 2-转赠
     */
    private Integer gainType;
    /**
     * 优惠额度变动金额
     */
    private BigDecimal changePreferentialAmount;
    /**
     * 优惠额度解冻时间
     */
    private Date unFrozenTime;

    /**
     * for student info
     */
    @Data
    @NoArgsConstructor
    public static class PreferentialRecordVo{
        /**
         * 1-系统发放 2-转赠 3-接收 4-消费
         */
        private int type;
        /**
         * 操作时间
         */
        private Date createTime;
        /**
         * 对象
         */
        private String sourceName;
        /**
         * 优惠额度
         */
        private BigDecimal preferentialAmount;

        public PreferentialRecordVo(PreferentialModifyTypeEnum typeEnum,
                                    PreferentialInOrOutEnum inOrOutEnum,
                                    BigDecimal preferentialAmount,
                                    String ownerStudentName,
                                    String fromStudentName,
                                    Date createTime) {
            if (PreferentialModifyTypeEnum.STUDENT_INTRODUCE.equals(typeEnum)) {
                type = 1;
                sourceName = ownerStudentName;
            } else if (PreferentialModifyTypeEnum.STUDENT_TRANSFER.equals(typeEnum)) {
                if(PreferentialInOrOutEnum.IN.equals(inOrOutEnum)){
                    type = 3;
                }else {
                    type = 2;
                }
                sourceName = fromStudentName;
            }else {
                type = 4;
                sourceName = ownerStudentName;
            }
            this.preferentialAmount =
                PreferentialInOrOutEnum.IN.equals(inOrOutEnum) ? preferentialAmount : preferentialAmount.negate();
            this.createTime = createTime;
        }
    }
}
