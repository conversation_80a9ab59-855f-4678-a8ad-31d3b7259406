package com.jxw.shufang.student.service.studyduration.aistudy;

import com.jxw.shufang.student.domain.bo.AiStudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.AiStudyDurationProcessingContextDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyRecordDTO;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateAiStudyVideoRecordDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.studyduration.AbstractAiStudyRecordTime;
import com.jxw.shufang.student.service.studyduration.StudyRecordTimeContentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/7 10:26
 * @Version 1
 * @Description AI伴学【预习】【自讲】 时长统计
 */
@Service
public class AiStudyDurationTimeServiceImpl extends AbstractAiStudyRecordTime<AiStudyVideoRecordBo> {
    @Resource
    private StudyRecordTimeContentService contextService;

    @Override
    public List<AiStudyVideoRecordBo> filterData(List<AiStudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return records;
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return List.of(StudyModelGroupEnum.AI_STUDY);
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return List.of(StudyModuleTypeEnum.PREVIEW, StudyModuleTypeEnum.SELF_SPEECH);
    }

    @Override
    public SaveOrUpdateAiStudyRecordDTO buildRecordProcessDTO(AiStudyVideoRecordBo recordBo) {
        SaveOrUpdateAiStudyRecordDTO recordDTO = new SaveOrUpdateAiStudyRecordDTO();
        recordDTO.setCourseId(recordBo.getCourseId());
        recordDTO.setStudentId(recordBo.getStudentId());
        recordDTO.setVideoId(recordBo.getVideoId());
        recordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        recordDTO.setCommitTime(recordBo.getCommitTime());
        return recordDTO;
    }

    @Override
    public SaveOrUpdateAiStudyVideoRecordDTO buildVideoRecordProcessDTO(AiStudyVideoRecordBo recordBo) {
        return null;
    }

    @Override
    public AiStudyDurationProcessingContextDTO contextData(List<AiStudyVideoRecordBo> records,
                                                           StudyModuleAndGroupEnum moduleAndGroupEnum) {
        return contextService.aiStudyContext(records, moduleAndGroupEnum);
    }
}
