package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.QuestionCollectBo;
import com.jxw.shufang.student.domain.vo.QuestionCollectVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IQuestionCollectService {

    TableDataInfo<QuestionCollectVo> pageQuestionCollects(QuestionCollectBo questionCollectBo, PageQuery pageQuery);

    List<QuestionCollectVo> listQuestionCollectsByQuestionIds(List<Long> questionIds);

    Boolean insertBatchByBo(List<QuestionCollectBo> bos);

    Boolean deleteBatchByBo(List<QuestionCollectBo> bos);
}
