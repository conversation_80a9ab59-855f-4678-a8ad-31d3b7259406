package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 数据统计-学习情况 studyProcess
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ExcelIgnoreUnannotated
public class StudyProcessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 今日累计学习时长（分钟）
     */
    private Long todayCumulativeStudyDur;

    /**
     * 本周累计学习时长（分钟）
     */
    private Long weekCumulativeStudyDur;

    /**
     * 本月累计学习时长（分钟）
     */
    private Long monthCumulativeStudyDur;

    /**
     * 总累计学习天数
     */
    private Long totalCumulativeStudyDays;

    /**
     * 总累计学习时长（单位：分钟，根据实际情况选择）
     */
    private Long totalCumulativeStudyDur;

    /**
     * 总累计学习课程数
     */
    private Long totalCumulativeCourseCount;

    /**
     * 近期学习时长列表
     */
    private XYVo xyVo;

    /**
     * 近期学习时长折线图实体 XYEntity
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class XYEntity {
        /**
         * x轴数据
         */
        private String x;
        /**
         * y轴数据
         */
        private String y;
    }

    /**
     * 近期学习时长折线图 XYVo
     */
    @Data
    public static class XYVo {

        /**
         * x轴名称
         */
        private String xName;

        /**
         * y轴名称
         */
        private String yName;

        /**
         * 近期学习时长列表
         */
        private List<XYEntity> list;
    }
}
