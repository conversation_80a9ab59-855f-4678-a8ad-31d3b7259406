package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyPlanningRecordBo;
import com.jxw.shufang.student.domain.vo.StudyPlanRecordGroupVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordDashboardVo;
import com.jxw.shufang.student.domain.vo.StudyPlanningRecordVo;
import com.jxw.shufang.student.domain.vo.StudyRecordStatisticsVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 学习规划记录Service接口
 * @date 2024-04-23
 */
public interface IStudyPlanningRecordService {

    /**
     * 查询学习规划记录
     */
    StudyPlanningRecordVo queryById(Long studyPlanningRecordId);

    /**
     * 查询学习规划记录列表
     */
    TableDataInfo<StudyPlanningRecordVo> queryPageList(StudyPlanningRecordBo bo, PageQuery pageQuery);

    /**
     * 查询学习规划记录列表
     */
    List<StudyPlanningRecordVo> queryList(StudyPlanningRecordBo bo);

    /**
     * 新增学习规划记录
     */
    Boolean insertByBo(StudyPlanningRecordBo bo);

    /**
     * 修改学习规划记录
     */
    Boolean updateByBo(StudyPlanningRecordBo bo);

    /**
     * 校验并批量删除学习规划记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 放置课程信息
     *
     * @param recordList 记录列表
     * @date 2024/04/29 04:42:16
     */
    void putCourseInfo(List<StudyPlanningRecordVo> recordList);

    /**
     * 通过记录id列表获取课程列表(再各自StudyPlanningRecordVo的course属性中)
     *
     * @param studyPlanningRecordIdList 研究计划记录id列表
     * @date 2024/05/10 03:52:30
     */
    List<StudyPlanningRecordVo> getCourseInfoByRecordIdList(List<Long> studyPlanningRecordIdList);

    TableDataInfo<StudyPlanningRecordVo> queryStudyPlanningRecordPage(StudyPlanningRecordBo bo, PageQuery pageQuery);

    /**
     * 学习规划列表V3，根据会员信息分组
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<StudyPlanningRecordDashboardVo> queryStudyPlanningRecordPageV3(StudyPlanningRecordBo bo,
        PageQuery pageQuery);

    List<StudyPlanningRecordVo> queryStudyPlanningRecordList(StudyPlanningRecordBo bo);




    /**
     * bo更新批次
     *
     * @param list
     * @date 2024/06/02 03:51:20
     */
    boolean updateBatchByBo(List<StudyPlanningRecordBo> list);

    boolean updateBatchByVo(List<StudyPlanningRecordVo> list);

    boolean insertBatchByBo(List<StudyPlanningRecordBo> addStudyPlanningRecordBoList);


    List<Long> queryStudyPlanningIdListByRecordIdList(List<Long> list);


    List<Long> queryStudyPlanningRecordIdListByPlanningIdList(List<Long> list);

    Map<Long,List<Long>> queryIdMapByStudyPlanningIdList(List<Long> studyPlanningIdList);

    /**
     * 根据学习规划ID列表查询学习规划记录ID和学生ID的映射关系
     * 只查询必要的字段以提高性能
     *
     * @param studyPlanningIdList 学习规划ID列表
     * @return 学生ID到学习规划记录ID列表的映射
     */
    Map<Long, List<Long>> queryRecordIdsByStudentIdMap(List<Long> studyPlanningIdList);

    StudyPlanningRecordVo queryFullInfoById(Long studyPlanningRecordId);

    TableDataInfo<StudyPlanRecordGroupVo> getStudyRecordByDate(StudyPlanningRecordBo bo, PageQuery pageQuery);

    /**
     * 查询学习记录统计列表
     */
    List<StudyRecordStatisticsVo> queryStudyRecordStatisticsList(StudyPlanningRecordBo bo);

    void putCorrectionRecordInfo(List<StudyPlanningRecordVo> records, Boolean withTestCorrectionRecord, Boolean withPracticeCorrectionRecord);

    Set<Long> checkCourseRepeat(Long courseId, List<Long> studentIdList);

    Map<Long, List<StudyPlanningRecordVo>> checkCourseRepeat(List<Long> courseIdList, Long studentId);

    Map<Long, List<StudyPlanningRecordVo>> checkCourseRepeatWithCorrection(List<Long> courseIdList, Long studentId);

    /**
     * 根据学习规划ID列表查询学习规划记录详细信息
     */
    List<StudyPlanningRecordVo> queryByPlanningIds(List<Long> planningIds);

    List<StudyPlanningRecordVo> queryStudyPlanningRecordListByIds(List<Long> studyPlanningRecordIds);
}
