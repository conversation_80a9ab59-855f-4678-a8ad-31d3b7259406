package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.CorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.CorrectionRecordInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 批改记录详情Service接口
 *
 *
 * @date 2024-05-09
 */
public interface ICorrectionRecordInfoService {

    /**
     * 查询批改记录详情
     */
    CorrectionRecordInfoVo queryById(Long correctionRecordInfoId);

    /**
     * 查询批改记录详情列表
     */
    TableDataInfo<CorrectionRecordInfoVo> queryPageList(CorrectionRecordInfoBo bo, PageQuery pageQuery);

    /**
     * 查询批改记录详情列表
     */
    List<CorrectionRecordInfoVo> queryList(CorrectionRecordInfoBo bo);

    /**
     * 新增批改记录详情
     */
    Boolean insertByBo(CorrectionRecordInfoBo bo);

    /**
     * 修改批改记录详情
     */
    Boolean updateByBo(CorrectionRecordInfoBo bo);

    /**
     * 校验并批量删除批改记录详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    Boolean insertBatchByBo(List<CorrectionRecordInfoBo> correctionRecordInfoBoList);

}
