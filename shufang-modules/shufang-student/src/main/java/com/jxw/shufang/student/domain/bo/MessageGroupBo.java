package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import com.jxw.shufang.common.core.validate.AddGroup;

import java.io.Serial;
import java.io.Serializable;

@Data
public class MessageGroupBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空", groups = { AddGroup.class })
    private String content;

    /**
     * 消息类型 1文本 2图片
     */
    @NotNull(message = "消息类型不能为空", groups = { AddGroup.class })
    private Integer contentType;

}
