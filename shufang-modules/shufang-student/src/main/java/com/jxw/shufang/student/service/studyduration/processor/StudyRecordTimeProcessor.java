package com.jxw.shufang.student.service.studyduration.processor;

import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.dto.StudyDurationProcessingContextDTO;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/4/3 16:14
 * @Version 1
 * @Description 学习记录保存时长处理
 */
public class StudyRecordTimeProcessor {
    private List<StudyRecord> updates = new ArrayList<>();
    private List<StudyRecord> inserts = new ArrayList<>();
    private StudyDurationProcessingContextDTO context;

    public StudyRecordTimeProcessor(StudyDurationProcessingContextDTO context) {
        this.context = context;
    }

    public List<StudyRecord> getUpdates() {
        return updates;
    }

    public List<StudyRecord> getInserts() {
        return inserts;
    }

    /**
     * 执行
     *
     * @param recordBo
     */
    public void studyProcess(SaveOrUpdateStudyRecordDTO recordBo) {
        StudyRecord existing = context.getExistStudyRecordMap().get(recordBo.getStudyPlanningRecordId());
        if (existing != null) {
            updates.add(this.buildUpdateRecord(existing, recordBo));
        } else {
            inserts.add(this.buildInsertStudyRecord(recordBo));
        }
    }

    public void studyProcessByQuestionRecord(SaveOrUpdateStudyRecordDTO recordDTO) {
        Long studyRecordMapKey = this.getExistStudyRecordMapKey(recordDTO);
        StudyRecord existRecord = context.getExistStudyRecordMap().get(studyRecordMapKey);
        if (existRecord != null) {
            Optional.of(this.buildUpdateRecordByQuestion(existRecord, recordDTO)).ifPresent(updates::add);
        } else {
            Optional.of(this.buildInsertRecordByQuestion(recordDTO)).ifPresent(inserts::add);
        }
    }

    private Long getExistStudyRecordMapKey(SaveOrUpdateStudyRecordDTO recordDTO) {
        Long key = null;
        // AI评测没有学习规划，因此key使用课程ID
        Boolean isAiStudy = context.getModuleAndGroupEnum().equals(StudyModuleAndGroupEnum.AI_STUDY_PRACTICE)
            || context.getModuleAndGroupEnum().equals(StudyModuleAndGroupEnum.AI_STUDY_TEST);
        if (isAiStudy) {
            key = recordDTO.getCourseId();
        } else {
            key = recordDTO.getStudyPlanningRecordId();
        }
        return key;
    }

    private StudyRecord buildInsertRecordByQuestion(SaveOrUpdateStudyRecordDTO recordBo) {
        Student student = context.getStudentMap().get(recordBo.getStudentId());

        StudyRecord studyVideoRecord = new StudyRecord();
        studyVideoRecord.setStudentId(recordBo.getStudentId());
        studyVideoRecord.setCourseId(recordBo.getCourseId());
        studyVideoRecord.setCreateBy(student.getCreateBy());
        studyVideoRecord.setCreateDept(student.getCreateDept());
        studyVideoRecord.setCreateTime(recordBo.getCommitTime());
        studyVideoRecord.setUpdateBy(student.getCreateBy());
        studyVideoRecord.setUpdateTime(recordBo.getCommitTime());
        studyVideoRecord.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        Long sliceSeconds = this.getSliceSeconds(recordBo.getStudyVideoDuration(), recordBo.getStudyVideoSlices());
        this.setTotalDurationByModule(sliceSeconds, studyVideoRecord, recordBo.getVideoId());
        return studyVideoRecord;
    }

    private StudyRecord buildUpdateRecordByQuestion(StudyRecord existRecord, SaveOrUpdateStudyRecordDTO recordBo) {
        Student student = context.getStudentMap().get(recordBo.getStudentId());
        Long sliceSeconds = this.getSliceSeconds(recordBo.getStudyVideoDuration(), recordBo.getStudyVideoSlices());
        long totalDurationTime = this.getTotalDurationTime(existRecord, sliceSeconds);

        StudyRecord updateRecord = new StudyRecord();
        updateRecord.setStudyRecordId(existRecord.getStudyRecordId());
        updateRecord.setUpdateBy(student.getCreateBy());
        updateRecord.setUpdateTime(recordBo.getCommitTime());
        this.setTotalDurationByModule(totalDurationTime, updateRecord, recordBo.getVideoId());
        return updateRecord;
    }

    private StudyRecord buildInsertStudyRecord(SaveOrUpdateStudyRecordDTO recordDTO) {
        Student student = context.getStudentMap().get(recordDTO.getStudentId());

        StudyRecord studyVideoRecord = new StudyRecord();
        studyVideoRecord.setStudentId(recordDTO.getStudentId());
        studyVideoRecord.setStudyPlanningRecordId(recordDTO.getStudyPlanningRecordId());
        studyVideoRecord.setCourseId(recordDTO.getCourseId());
        studyVideoRecord.setCreateBy(student.getCreateBy());
        studyVideoRecord.setCreateDept(student.getCreateDept());
        studyVideoRecord.setCreateTime(recordDTO.getCommitTime());
        studyVideoRecord.setUpdateBy(student.getCreateBy());
        studyVideoRecord.setUpdateTime(recordDTO.getCommitTime());
        Long sliceSeconds = this.getSliceSeconds(recordDTO.getStudyVideoDuration(), recordDTO.getStudyVideoSlices());
        this.setTotalDurationByModule(sliceSeconds, studyVideoRecord, recordDTO.getVideoId());
        return studyVideoRecord;
    }

    private StudyRecord buildUpdateRecord(StudyRecord existingRecord, SaveOrUpdateStudyRecordDTO recordDTO) {
        Student student = context.getStudentMap().get(recordDTO.getStudentId());

        StudyRecord updateRecord = new StudyRecord();
        updateRecord.setStudyRecordId(existingRecord.getStudyRecordId());
        updateRecord.setUpdateBy(student.getCreateBy());
        updateRecord.setUpdateTime(recordDTO.getCommitTime());

        Long sliceSeconds = this.getSliceSeconds(recordDTO.getStudyVideoDuration(), recordDTO.getStudyVideoSlices());
        long totalDurationTime = this.getTotalDurationTime(existingRecord, sliceSeconds);

        this.setTotalDurationByModule(totalDurationTime, updateRecord, recordDTO.getVideoId());
        return updateRecord;
    }

    private long getTotalDurationTime(StudyRecord existingRecord, Long sliceSeconds) {
        StudyModuleTypeEnum moduleEnum = context.getModuleAndGroupEnum().getModuleEnum();
        boolean noVideoSpliceModuleType = StudyModuleTypeEnum.PREVIEW.equals(moduleEnum) || StudyModuleTypeEnum.SELF_SPEECH.equals(moduleEnum);
        long totalDurationTime = 0;

        // 已经存在的视频时长
        Long existVideoTotalDuration = this.getExistVideoTotalDuration(existingRecord);
        // 预和自讲没有视频分片，无法判断视频分片进度，无法判断是否累加学习，使用取最大值的方式
        if (noVideoSpliceModuleType) {
            totalDurationTime = Math.max(existVideoTotalDuration, sliceSeconds);
        } else {
            totalDurationTime = existVideoTotalDuration + sliceSeconds;
        }
        return totalDurationTime;
    }

    /**
     * 获取已存在的视频时长
     *
     * @param existingRecord
     * @return
     */
    private Long getExistVideoTotalDuration(StudyRecord existingRecord) {
        StudyModuleTypeEnum moduleTypeEnum = context.getModuleAndGroupEnum().getModuleEnum();

        Long totalDurationTime;
        if (moduleTypeEnum.equals(StudyModuleTypeEnum.PREVIEW)) {
            totalDurationTime = existingRecord.getPreviewTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.SELF_SPEECH)) {
            totalDurationTime = existingRecord.getSelfSpeechTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.PRACTICE)) {
            totalDurationTime = existingRecord.getPracticeVideoTotalDuration();
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.TEST)) {
            totalDurationTime = existingRecord.getTestVideoTotalDuration();
        } else {
            totalDurationTime = existingRecord.getStudyVideoTotalDuration();
        }

        return Optional.ofNullable(totalDurationTime).orElse(0L);
    }

    /**
     * 设置总时长
     *
     * @param studyVideoDuration
     * @param studyRecord
     * @param videoId
     */
    private void setTotalDurationByModule(Long studyVideoDuration, StudyRecord studyRecord, Long videoId) {
        StudyModuleTypeEnum moduleTypeEnum = context.getModuleAndGroupEnum().getModuleEnum();
        Long courseDuration = Optional.ofNullable(context.getCourseDurationMap()).map(m->m.get(videoId)).orElse(0L);
        // 是否超过课程时长
        boolean overCourseMaxTime = studyVideoDuration >= courseDuration;
        long remainDuration = courseDuration - studyVideoDuration;
        boolean useCourseDuration = overCourseMaxTime || remainDuration < 10;

        if (moduleTypeEnum.equals(StudyModuleTypeEnum.PREVIEW)) {
            studyRecord.setPreviewTotalDuration(overCourseMaxTime ? courseDuration : studyVideoDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.SELF_SPEECH)) {
            Long selfSpeechMaxDurationTime = TimeUnit.MINUTES.toSeconds(15);
            boolean overMaxSelfSpeechDurationTime = studyVideoDuration >= selfSpeechMaxDurationTime;
            studyRecord.setSelfSpeechTotalDuration(overMaxSelfSpeechDurationTime ? selfSpeechMaxDurationTime : studyVideoDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.PRACTICE)) {
            studyRecord.setPracticeVideoTotalDuration(useCourseDuration ? courseDuration : studyVideoDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.TEST)) {
            studyRecord.setTestVideoTotalDuration(useCourseDuration ? courseDuration : studyVideoDuration);
        } else if (moduleTypeEnum.equals(StudyModuleTypeEnum.STUDY)) {
            studyRecord.setStudyVideoTotalDuration(useCourseDuration ? courseDuration : studyVideoDuration);
        } else {
        }
    }

    /**
     * 获取视频分片时长 没有视频的直接传递的是时长studyVideoDuration，没有时间分片
     *
     * @param studyVideoDuration
     * @param videoSlice
     * @return
     */
    private Long getSliceSeconds(Long studyVideoDuration, String videoSlice) {
        if (null != studyVideoDuration && studyVideoDuration > 0) {
            return studyVideoDuration;
        }
        return getSliceSecondsBySlice(videoSlice);
    }

    private static Long getSliceSecondsBySlice(String videoSlice) {
        return VideoSlicesUtils.calVideoSliceSeconds(videoSlice);
    }
}
