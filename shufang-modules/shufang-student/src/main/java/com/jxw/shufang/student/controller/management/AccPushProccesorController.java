package com.jxw.shufang.student.controller.management;

import cn.hutool.core.map.MapUtil;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStaffEzkecoBo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.listener.GenerateCmd;
import com.jxw.shufang.student.service.IAttendanceLogStaffEzkecoService;
import com.jxw.shufang.student.service.IAttendanceLogStudentEzkecoService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤机的官方demo，并且优化过了
 * 1.初始化
 * 2.注册
 * 3.push
 * 4.心跳
 * 5.心跳处理结果
 * 6.实时记录
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/management/iclock")
public class AccPushProccesorController {


    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStaffService remoteStaffService;


    private final IStudentService studentService;

    private final IAttendanceLogStaffEzkecoService attendanceLogStaffEzkecoService;

    private final IAttendanceLogStudentEzkecoService attendanceLogStudentEzkecoService;



    private static boolean test = true;
    private static int index = 0;

    private static List<String> CMDLIST = null;

    static {
        CMDLIST = GenerateCmd.cmd();
    }

    /**
     * 处理初始化请求的
     *
     * @return
     */
    @RequestMapping(value = "/cdata", method = RequestMethod.GET)
    public String init(String SN, String pushver, String options, HttpServletRequest req) {
//        System.out.println("#######请求的URL:"+req.getServletPath());
        Map<String, String> param = convertMap(req);
//        System.out.println("######请求的参数"+param.toString());
//        System.out.println("######请求完整URL:"+req.getServletPath()+"?"+param.toString().trim().replace(", ", "&").replace("{", "").replace("}", ""));
        log.info("SN...{}...pushver...{}...options...{}...{}", SN, pushver, options, new SimpleDateFormat("HH:mm:ss").format(new Date()));

        return "OK\n" + "PushProtVer=3.1.2";

    }

    /**
     * 处理注册请求的
     *
     * @return
     */
    @RequestMapping("/registry")
    public String registry(@RequestBody String deviceData, HttpServletRequest req, String SN) {

//        System.out.println("#######请求的URL:"+req.getServletPath());
        Map<String, String> param = convertMap(req);
//        System.out.println("######请求的参数"+param.toString());
//        System.out.println("######请求完整URL:"+req.getServletPath()+"?"+param.toString().trim().replace(", ", "&").replace("{", "").replace("}", ""));
        log.info(deviceData);
        return "RegistryCode=Uy47fxftP3";
    }

    /**
     * 处理push请求
     *
     * @return
     */
    @RequestMapping(value = "/push")
    public String push(HttpServletRequest req) {
        log.info("进入到push请求.....");
//        System.out.println("#######请求的URL:"+req.getServletPath());
        Map<String, String> param = convertMap(req);
//        System.out.println("######请求的参数"+param.toString());
//        System.out.println("######请求完整URL:"+req.getServletPath()+"?"+param.toString().trim().replace(", ", "&").replace("{", "").replace("}", ""));
        StringBuffer sb = new StringBuffer();
        sb.append("ServerVersion=3.0.1\n");
        sb.append("ServerName=ADMS\n");
        sb.append("PushVersion=3.0.1\n");
        sb.append("ErrorDelay=30\n");
        sb.append("RequestDelay=2\n");
        sb.append("TransTimes=00:0014:00\n");
        sb.append("TransInterval=1\n");
        sb.append("TransTables=User Transaction\n");
        sb.append("Realtime=1\n");
        sb.append("SessionID=30BFB04B2C8AECC72C01C03BFD549D15\n");
        sb.append("TimeoutSec=10\n");

        return sb.toString();
    }

    /*
     * 处理心跳请求
     *
     */
    @RequestMapping("/getrequest")
    public String heartbeat(String SN) {

//        System.out.println("#######请求的URL:"+req.getServletPath());
        //     Map<String,String> param = convertMap(req);
//        System.out.println("######请求的参数"+param.toString());
//        System.out.println("######请求完整URL:"+req.getServletPath()+"?"+param.toString().trim().replace(", ", "&").replace("{", "").replace("}", ""));
        log.info("进入到心跳请求....{} {}", SN, new Date());
        //cmd.txt 放在d盘
        BufferedReader br = null;
        File file = new File("d://cmd.txt");//elegent

        StringBuffer sb = new StringBuffer();

        if (file.exists()) {
            try {
                br = new BufferedReader(new FileReader(file));
                String cmd = "";
                while ((cmd = br.readLine()) != null) {
                    if (cmd.startsWith("C")) {
                        //正常的命令
                        sb.append(cmd + "\r\n\r\n");
                        //return sb.toString();
                    } else if (cmd.startsWith("D")) {
                    } else {
                        //进到这里目前看只能是E开头，则表示人员头像
                    }
                }
                br.close();
                file.delete();
                Thread.sleep(1000);
                return sb.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }

        } else if (test) {
            //test = false;
            if (index < CMDLIST.size()) {
                System.out.println("命令为...." + CMDLIST.get(index));
                return CMDLIST.get(index++);
            } else {
                test = false;
            }
            // return GenerateCmd.cmd();
            return "OK";
        } else {
            return "OK";
        }
        return "OK";


       /* if(userPic.length()>0){
            try {
                InputStream is = new FileInputStream(new File("d://lz.jpg"));//从磁盘读取照片
                int length = is.available();
                byte[] buffer = new byte[length];
                is.read(buffer);
                String base64 =  Base64.getEncoder().encodeToString(buffer);
                userPic = userPic+"\tsize="+base64.length()+"\tcontent="+base64+"\r\n\r\n";
            } catch (IOException e) {
                e.printStackTrace();
            }
            String cmd =userCmd;
            userPic = "";
            return cmd;
        }else{
            return "OK";
        }*/
    }

    public Map<String, String> convertMap(HttpServletRequest request) {
        Map<String, String> returnMap = new HashMap<>();
        // 转换为Entry
        Set<Map.Entry<String, String[]>> entries = request.getParameterMap().entrySet();
        for (Map.Entry<String, String[]> entry : entries) {
            String key = entry.getKey();
            StringBuffer value = new StringBuffer("");
            String[] val = entry.getValue();
            if (null != val && val.length > 0) {
                for (String v : val) {
                    value.append(v);
                }
            }
            returnMap.put(key, value.toString());
        }
        return returnMap;
    }

    @RequestMapping("/ping")
    public String ping(HttpServletRequest req) {
        log.info("设备上传时通过Ping请求维持心跳");
        log.info("#######请求的URL:{}", req.getServletPath());
//        Map<String,String> param = convertMap(req);
//        System.out.println("######请求的参数"+param.toString());
//        System.out.println("######请求完整URL:"+req.getServletPath()+"?"+param.toString().trim().replace(", ", "&").replace("{", "").replace("}", ""));
        return "OK";
    }

    @RequestMapping(value = "/cdata", method = RequestMethod.POST)
    public String handleForm(@RequestBody String data, String SN, HttpServletRequest req, HttpServletResponse response, String table, String AuthType) {

        String verification = "AUTH=SUCCESS\r\n";
        String cmd = "CONTROL DEVICE 1 1 1 5\r\n\r\n";
        if (("device").equals(AuthType)) {
            log.info("回复的数据是...\r\n{}{}\r\n{}", verification, data, cmd);
            return verification + data + "\r\n" + cmd;
        }

        log.info("上传的表名为：{}", table);
        log.info("设备上传的实时记录为：{}序列号：{}", data, SN);
        log.info("AuthType:{}", AuthType);
        Map<String, String> param = convertMap(req);
        log.info("请求的参数为：{}", param);

        //判断是考勤记录
        if(!Objects.equals(table,"ATTLOG")||StringUtils.isBlank(data)){
            return "OK";
        }
        try {
            List<Map<String, String>> dataMapList = new ArrayList<>();

            //按照换行分割
            String[] split = data.split("\n");
            List<String> recordList = Arrays.stream(split).filter(StringUtils::isNotBlank).toList();
            for (String item : recordList) {
                try {
                    String[] dataArray = item.split("\t");
                    List<String> dataList = Arrays.stream(dataArray).filter(StringUtils::isNotBlank).toList();
                    String mobile = dataList.get(0);
                    //第二条是日期和时间 yyyy-MM-dd HH:mm:ss
                    String time = dataList.get(1);
                    //校验时间格式对不对
                    try {
                        DateUtils.parseDate(time);
                    }catch (Exception e){
                        log.error("上传的考勤记录格式不正确，请检查上传数据！, time={}", time);
                        continue;
                    }



                    Map<String, String> build = MapUtil.builder("mobile", mobile).put("time", time).build();
                    dataMapList.add(build);
                }catch (Exception e){
                    log.error("上传的考勤记录格式不正确，请检查上传数据！", e);
                }
            }
            if (dataMapList.isEmpty()){
                return "OK";
            }

            //处理考勤记录
            //查询对应的人员信息
            List<String> phoneList = dataMapList.stream().map(item -> item.get("mobile")).toList();
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setUserNames(phoneList);
            List<RemoteUserVo> userVoList = remoteUserService.queryUserList(remoteUserBo, true);

            //按照手机号码转成map
            Map<String, RemoteUserVo> userMap = userVoList.stream().collect(Collectors.toMap(RemoteUserVo::getUserName, item -> item));

            Iterator<Map<String, String>> iterator = dataMapList.iterator();

            List<AttendanceLogStudentEzkecoBo> attendanceLogStudentEzkecoBoList = new ArrayList<>();
            List<AttendanceLogStaffEzkecoBo> attendanceLogStaffEzkecoBoList = new ArrayList<>();

            while (iterator.hasNext()) {
                Map<String, String> item = iterator.next();
                String mobile = item.get("mobile");
                RemoteUserVo userVo = userMap.get(mobile);
                if (userVo == null) {
                    log.error("未找到对应的人员信息，请检查上传数据！, mobile={}", mobile);
                    iterator.remove();
                    continue;
                }
                //组装日期和时间
                String datetime = item.get("time");
                //查询这个点是不是已经打卡了，如果是已经打卡了，则删除


                //先看看是会员还是员工
                String userTypeStr = userVo.getUserType();
                UserType userType = UserType.getUserType(userTypeStr);
                log.info("用户类型是：{}", userType);
                switch (userType){
                    case APP_STU_USER->{
                        AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo = new AttendanceLogStudentEzkecoBo();
                        attendanceLogStudentEzkecoBo.setUserId(userVo.getUserId());
                        attendanceLogStudentEzkecoBo.setChecktime(DateUtils.parseDate(datetime));
                        Boolean exist = attendanceLogStudentEzkecoService.exist(attendanceLogStudentEzkecoBo);
                        if (exist) {
                            iterator.remove();
                            continue;
                        }
                        attendanceLogStudentEzkecoBo.setSn(SN);
                        attendanceLogStudentEzkecoBoList.add(attendanceLogStudentEzkecoBo);
                    }
                    case STAFF_USER->{
                        AttendanceLogStaffEzkecoBo attendanceLogStaffEzkecoBo = new AttendanceLogStaffEzkecoBo();
                        attendanceLogStaffEzkecoBo.setUserId(userVo.getUserId());
                        attendanceLogStaffEzkecoBo.setChecktime(DateUtils.parseDate(datetime));
                        Boolean exist = attendanceLogStaffEzkecoService.exist(attendanceLogStaffEzkecoBo);
                        if (exist) {
                            iterator.remove();
                            continue;
                        }
                        attendanceLogStaffEzkecoBo.setSn(SN);
                        attendanceLogStaffEzkecoBoList.add(attendanceLogStaffEzkecoBo);
                    }
                }
            }

            if (!attendanceLogStaffEzkecoBoList.isEmpty()){
                List<Long> userIdList = attendanceLogStaffEzkecoBoList.stream().map(AttendanceLogStaffEzkecoBo::getUserId).distinct().toList();
                RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
                remoteStaffBo.setUserIdList(userIdList);
                List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
                Map<Long, RemoteStaffVo> staffMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getCreateBy, item -> item));

                attendanceLogStaffEzkecoBoList.forEach(item -> {
                    RemoteStaffVo remoteStaffVo = staffMap.get(item.getUserId());
                    item.setCreateBy(item.getUserId());
                    if (remoteStaffVo == null) {
                        return;
                    }
                    item.setCreateDept(remoteStaffVo.getCreateDept());
                });
                attendanceLogStaffEzkecoService.insertBatch(attendanceLogStaffEzkecoBoList);
            }
            if (!attendanceLogStudentEzkecoBoList.isEmpty()){
                List<Long> userIdList = attendanceLogStudentEzkecoBoList.stream().map(AttendanceLogStudentEzkecoBo::getUserId).distinct().toList();
                StudentBo studentBo = new StudentBo();
                studentBo.setUserIdList(userIdList);
                List<StudentVo> studentVos = studentService.queryList(studentBo);
                Map<Long, StudentVo> studentMap = studentVos.stream().collect(Collectors.toMap(StudentVo::getCreateBy, item -> item));
                attendanceLogStudentEzkecoBoList.forEach(item -> {
                    StudentVo studentVo = studentMap.get(item.getUserId());
                    item.setCreateBy(item.getUserId());
                    if (studentVo == null) {
                        return;
                    }
                    item.setCreateDept(studentVo.getCreateDept());
                    item.setStudentId(studentVo.getStudentId());
                });
                attendanceLogStudentEzkecoService.insertBatch(attendanceLogStudentEzkecoBoList);
            }
        }catch (Exception e){
            log.error("上传的考勤记录格式不正确，请检查上传数据！", e);
        }

        //必须返回OK，否则会一直重试,恶心的很
        return "OK";


    }





    @RequestMapping(value = "/querydata", method = RequestMethod.POST)
    public String query(@RequestBody String querrydata, HttpServletRequest req) {
        String[] dataArray = querrydata.split("\r\n");

        int count = dataArray.length;
        String tableName = dataArray[0].split(" ")[0];
        log.info("查询信息是。。。。。{}", querrydata);
        String returnValue = tableName + "=" + count;
        return returnValue;

    }

    @RequestMapping(value = "/rtdata", method = RequestMethod.GET)
    public String rtdata(String type) {
        return "DateTime=716116020" + "," + "ServerTZ=+0800";//格林威治时间减去八小时return

    }


    @RequestMapping(value = "/devicecmd")
    public String deviceCmd(@RequestBody String cmdResult, HttpServletRequest req) {
        log.info("命令返回的结果为...{}", cmdResult);
        return "OK";
    }

    @RequestMapping(value = "/file")
    public String File(@RequestBody String SN, String cmdid, String fileseq, String contenttype, String count) {
        return "OK";
    }

}

