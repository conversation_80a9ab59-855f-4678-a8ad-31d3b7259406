package com.jxw.shufang.student.controller.h5;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.vo.StuDailyShareVo;
import com.jxw.shufang.student.service.IStudyDailyShareService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 反馈记录
 * 前端访问路由地址为:/student/h5/feedbackRecord
 *
 *
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/h5/studyDailyShare")
public class MpFeedbackRecordController extends BaseController {

    private final IStudyDailyShareService studyDailyShareService;



    /**
     * 获取反馈记录详细信息
     *
     * @param feedbackRecordId 主键
     */
    @GetMapping("/queryById")
    public R<StuDailyShareVo> getInfo(@NotNull(message = "主键不能为空")
                                     Long feedbackRecordId) {
        StuDailyShareVo feedbackRecordVo = studyDailyShareService.queryByIdForH5(feedbackRecordId);
        return R.ok(feedbackRecordVo);
    }

    /**
     * 获取反馈记录详细信息
     *
     * @param feedbackRecordId 主键
     */
    @GetMapping("/read")
    public R<Void> read(@NotNull(message = "主键不能为空")
                                      Long feedbackRecordId) {
        studyDailyShareService.read(feedbackRecordId);
        return R.ok();
    }


    /**
     * 获取二维码
     * @return 二维码base64
     */
    @GetMapping("/getQrCode")
    public R<String> getQrCode() {
        return R.ok("操作成功",studyDailyShareService.getQrCode());
    }


}
