package com.jxw.shufang.student.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人员对接 - 获取人员信息对象 EKEcoEmployeeBo
 *
 * <AUTHOR>
 * @date 2024-05-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EZKEcoEmployeeBo {

    /**
     * 获取的人员编号。多个用逗号分隔。
     */
    private String pinlist;

    /**
     * 人员状态（默认为0，只返回在职人员）
     * 0 在职
     * 1 离职
     * 2 在职和离职
     * 允许值: 0, 1, 2
     */
    private Integer offduty;

    /**
     * 获取的部门编号。多个用逗号分隔。
     */
    private String deptnumberlist;

    /**
     * 1/0：是否递归获取子部门下面的成员，默认为否(0) 允许值: 0, 1
     */
    private Integer fetch_child;

}
