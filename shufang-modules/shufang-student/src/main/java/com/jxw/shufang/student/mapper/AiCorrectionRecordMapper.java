package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AiCorrectionRecord;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordVo;

import java.util.List;

/**
 * ai批改记录Mapper接口
 *
 *
 * @date 2024-05-23
 */
public interface AiCorrectionRecordMapper extends BaseMapperPlus<AiCorrectionRecord, AiCorrectionRecordVo> {

    List<AiCorrectionRecordVo> queryRecordAndRightWrongInfo(@Param(Constants.WRAPPER) QueryWrapper<AiCorrectionRecord> correctionRecordQueryWrapper);

}
