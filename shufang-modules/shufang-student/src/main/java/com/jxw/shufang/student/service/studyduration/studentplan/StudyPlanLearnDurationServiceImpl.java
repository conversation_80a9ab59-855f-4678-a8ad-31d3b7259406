package com.jxw.shufang.student.service.studyduration.studentplan;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.StudyVideoRecord;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyRecordDTO;
import com.jxw.shufang.student.domain.bo.StudyVideoRecordBo;
import com.jxw.shufang.student.domain.dto.SaveOrUpdateStudyVideoRecordDTO;
import com.jxw.shufang.student.domain.dto.StudyDurationProcessingContextDTO;
import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.service.IStudyRecordService;
import com.jxw.shufang.student.service.studyduration.AbstractStudyRecordTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2025/4/2 15:59
 * @Version 1
 * @Description 学习规划 【学】时长统计
 */
@Service
@Slf4j
public class StudyPlanLearnDurationServiceImpl extends AbstractStudyRecordTime<StudyVideoRecordBo> {
    @Resource
    private IStudyRecordService studyRecordService;

    @Override
    public List<StudyVideoRecordBo> filterData(List<StudyVideoRecordBo> records, StudyModuleAndGroupEnum moduleAndGroupEnum) {
        filterInvalidMessage(records);
        return records;
    }

    @Override
    public StudyDurationProcessingContextDTO contextData(List<StudyVideoRecordBo> records,
                                                         StudyModuleAndGroupEnum moduleAndGroupEnum) {
        List<Long> planningIds = records.stream().map(StudyVideoRecordBo::getStudyPlanningRecordId).toList();
        List<Long> studentIds = records.stream().map(StudyVideoRecordBo::getStudentId).toList();
        List<Long> videoIds = records.stream().map(StudyVideoRecordBo::getVideoId).filter(Objects::nonNull).toList();

        StudyDurationProcessingContextDTO contextData = new StudyDurationProcessingContextDTO();
        contextData.setExistVideoRecordMap(super.batchQueryVideoRecords(records));
        contextData.setExistStudyRecordMap(studyRecordService.batchQueryMapByPlanningIds(planningIds));
        contextData.setStudentMap(super.studentMap(studentIds));
        contextData.setCourseDurationMap(this.courseDurationMap(videoIds));
        contextData.setModuleAndGroupEnum(moduleAndGroupEnum);

        this.checkRepeatRecord(records, contextData);

        return contextData;
    }

    /**
     * 检查学习记录是否重复
     * @param records
     * @param contextData
     */
    private void checkRepeatRecord(List<StudyVideoRecordBo> records, StudyDurationProcessingContextDTO contextData) {
        String studyVideoSlices = records.get(0).getStudyVideoSlices();
        List<StudyVideoRecord> studyVideoRecords = contextData.getExistVideoRecordMap().values().stream().toList();
        List<StudyVideoRecord> repeatSlicesList = studyVideoRecords.stream()
            .filter(studyVideoRecord -> VideoSlicesUtils.ignoreRepeatSlice(studyVideoRecord.getStudyVideoSlices(), studyVideoSlices))
            .toList();

        if (CollectionUtil.isNotEmpty(repeatSlicesList)) {
            contextData.setIgnoreStudyVideoRecord(true);
        }
    }

    @Override
    public List<StudyModelGroupEnum> matchStudyModelGroup() {
        return Stream.of(StudyModelGroupEnum.STUDY_PLANNING).collect(Collectors.toList());
    }

    @Override
    public List<StudyModuleTypeEnum> matchStudyModuleType() {
        return Stream.of(StudyModuleTypeEnum.STUDY).collect(Collectors.toList());
    }

    @Override
    public SaveOrUpdateStudyVideoRecordDTO buildStudyVideoRecordProcessDTO(StudyVideoRecordBo recordBo) {
        SaveOrUpdateStudyVideoRecordDTO saveOrUpdateStudyVideoRecordDTO = new SaveOrUpdateStudyVideoRecordDTO();
        saveOrUpdateStudyVideoRecordDTO.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        saveOrUpdateStudyVideoRecordDTO.setStudentId(recordBo.getStudentId());
        saveOrUpdateStudyVideoRecordDTO.setCourseId(recordBo.getCourseId());
        saveOrUpdateStudyVideoRecordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        saveOrUpdateStudyVideoRecordDTO.setVideoId(recordBo.getVideoId());
        saveOrUpdateStudyVideoRecordDTO.setCommitTime(recordBo.getCommitTime());
        saveOrUpdateStudyVideoRecordDTO.setModuleType(recordBo.getStudyModuleType().getModuleEnum().getModuleCode());
        saveOrUpdateStudyVideoRecordDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        return saveOrUpdateStudyVideoRecordDTO;
    }

    @Override
    public SaveOrUpdateStudyRecordDTO buildStudyRecordProcessDTO(StudyVideoRecordBo recordBo) {
        SaveOrUpdateStudyRecordDTO saveOrUpdateStudyRecordDTO = new SaveOrUpdateStudyRecordDTO();
        saveOrUpdateStudyRecordDTO.setStudyPlanningRecordId(recordBo.getStudyPlanningRecordId());
        saveOrUpdateStudyRecordDTO.setCourseId(recordBo.getCourseId());
        saveOrUpdateStudyRecordDTO.setStudentId(recordBo.getStudentId());
        saveOrUpdateStudyRecordDTO.setStudyVideoDuration(recordBo.getStudyVideoDuration());
        saveOrUpdateStudyRecordDTO.setCommitTime(recordBo.getCommitTime());
        saveOrUpdateStudyRecordDTO.setStudyVideoSlices(recordBo.getStudyVideoSlices());
        saveOrUpdateStudyRecordDTO.setVideoId(recordBo.getVideoId());
        return saveOrUpdateStudyRecordDTO;
    }

    private void filterInvalidMessage(List<StudyVideoRecordBo> messageList) {
        Iterator<StudyVideoRecordBo> iterator = messageList.iterator();
        while (iterator.hasNext()) {
            StudyVideoRecordBo studyVideoRecordBo = iterator.next();
            if (null == studyVideoRecordBo) {
                log.error(MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG + "消息体为空");
                iterator.remove();
            }
            if (null == studyVideoRecordBo.getStudyPlanningRecordId()) {
                log.error(MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG + "studyPlanningRecordId为空,具体消息:{}", studyVideoRecordBo);
                iterator.remove();
            }
            if (studyVideoRecordBo.getCourseId() == null) {
                iterator.remove();
                log.error(MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG + "courseId为空,具体消息:{}", studyVideoRecordBo);
            }
        }
    }
}
