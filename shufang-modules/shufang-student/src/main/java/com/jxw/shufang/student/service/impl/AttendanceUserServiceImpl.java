package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.service.DictService;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.AttendanceUser;
import com.jxw.shufang.student.domain.bo.AttendanceUserBo;
import com.jxw.shufang.student.domain.bo.EZKEcoEmployeeBo;
import com.jxw.shufang.student.domain.vo.AttendanceUserVo;
import com.jxw.shufang.student.domain.vo.EZKEcoEmployeeVo;
import com.jxw.shufang.student.mapper.AttendanceUserMapper;
import com.jxw.shufang.student.service.IAttendanceUserService;
import com.jxw.shufang.student.service.IEZKEcoService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * 考勤关联用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class AttendanceUserServiceImpl implements IAttendanceUserService, BaseService {

    private final AttendanceUserMapper baseMapper;
    private final IEZKEcoService ezkEcoService;
    private final DictService dictService;

    @DubboReference
    private final RemoteUserService remoteUserService;
    /**
     * 查询考勤关联用户
     */
    @Override
    public AttendanceUserVo queryById(Long attendanceUserId){
        return baseMapper.selectVoById(attendanceUserId);
    }

    /**
     * 查询考勤关联用户列表
     */
    @Override
    public TableDataInfo<AttendanceUserVo> queryPageList(AttendanceUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AttendanceUser> lqw = buildQueryWrapper(bo);
        Page<AttendanceUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询考勤关联用户列表
     */
    @Override
    public TableDataInfo<AttendanceUser> queryDataPageList(AttendanceUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AttendanceUser> lqw = buildQueryWrapper(bo);
        Page<AttendanceUser> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询考勤关联用户列表
     */
    @Override
    public List<AttendanceUserVo> queryList(AttendanceUserBo bo) {
        LambdaQueryWrapper<AttendanceUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AttendanceUser> buildQueryWrapper(AttendanceUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AttendanceUser> lqw = Wrappers.lambdaQuery();
        lqw.in(!bo.getUserIds().isEmpty(), AttendanceUser::getUserId, bo.getUserIds());
        lqw.eq(StringUtils.isNotBlank(bo.getIp()), AttendanceUser::getIp, bo.getIp());
        lqw.eq(bo.getUserId() != null, AttendanceUser::getUserId, bo.getUserId());
        return lqw;
    }

    /**
     * 新增考勤关联用户
     */
    @Override
    public Boolean insertByBo(AttendanceUserBo bo) {
        AttendanceUser add = MapstructUtils.convert(bo, AttendanceUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAttendanceUserId(add.getAttendanceUserId());
        }
        return flag;
    }

    /**
     * 修改考勤关联用户
     */
    @Override
    public Boolean updateByBo(AttendanceUserBo bo) {
        AttendanceUser update = MapstructUtils.convert(bo, AttendanceUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AttendanceUser entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除考勤关联用户
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {

    }

    /**
     * 同步EzkEco考勤机
     */
    @Override
    public Integer syncEzkEcoAttendanceUser() {
        // 定义考勤机和系统的关联的字段，后期只需要改这个就行
        final Function<EZKEcoEmployeeVo, String> getRelaFieldFunc = EZKEcoEmployeeVo::getPin;
        // 用来存放考勤机的同步条数
        AtomicInteger count = new AtomicInteger(0);
        // 当前时间
        final Date nowDate = DateUtils.getNowDate();
        // 获取数据字典的考勤机配置信息
        Map<String, String> ezkecoIpKey = dictService.getAllDictByDictType("ezkeco_ip_key");
        // 取出所有的考勤机配置
        Set<String> ipKeys = ezkecoIpKey.keySet();
        // 循环遍历考勤机
        ipKeys.forEach(config -> {
            // 获取考勤机上的人员信息
            List<EZKEcoEmployeeVo> dataItems = ezkEcoService.getEmployee(config, new EZKEcoEmployeeBo()).getDataItems();
            // 用于接口返回的同步数
            count.addAndGet(dataItems.size());
            // 分区处理数据
            List<List<EZKEcoEmployeeVo>> partitionUser = ListUtil.partition(dataItems, 100);
            partitionUser.forEach(ecoEmployeeVos -> {
                // 获取到关联系统的字段值集合
                List<String> relaFieldList = StreamUtils.toList(ecoEmployeeVos, getRelaFieldFunc);
                // 远程通过用户名称（一般都是手机号）
                RemoteUserBo userBo = new RemoteUserBo();
                userBo.setUserNames(relaFieldList);
                List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(userBo,true);
                // 获取考勤机中最新的用户id
                List<Long> addUserIdList = StreamUtils.toList(remoteUserVos, RemoteUserVo::getUserId);
                // 获取已经存在表里的系统用户id
                AttendanceUserBo addBo = new AttendanceUserBo();
                addBo.setUserIds(addUserIdList);
                List<Long> existsUserIds = StreamUtils.toList(this.queryList(addBo), AttendanceUserVo::getUserId);
                // 获取单差集existsUserIds有的，userIdList没有的数据，用于插入到数据库中
                List<Long> deleteUserIds = CollectionUtil.subtractToList(existsUserIds, addUserIdList);
                // 获取单差集userIdList有的，existsUserIds没有的数据，用于插入到数据库中
                List<Long> addUserIds = CollectionUtil.subtractToList(addUserIdList, existsUserIds);
                // 求交集，这些需要更新update_time
                Collection<Long> updateUserIds = CollectionUtil.intersection(addUserIdList, existsUserIds);
                // 查询需要删除的勤关联的用户表的ID
                AttendanceUserBo updateBo = new AttendanceUserBo();
                updateBo.setUserIds((List<Long>) updateUserIds);
                List<Long> updateAttendanceUserId = StreamUtils.toList(this.queryList(updateBo), AttendanceUserVo::getAttendanceUserId);
                // 查询需要删除的勤关联的用户表的ID
                AttendanceUserBo deleteBo = new AttendanceUserBo();
                deleteBo.setUserIds(deleteUserIds);
                List<Long> attendanceUserId = StreamUtils.toList(this.queryList(deleteBo), AttendanceUserVo::getAttendanceUserId);
                // 先删除一些考勤关联的用户（考勤机中不存在的，但是本系统中还有的用户信息）
                if (!attendanceUserId.isEmpty()) {
                    this.deleteWithValidByIds(attendanceUserId, false);
                }
                // 生成插入数据库的实体类
                List<AttendanceUser> attendanceUserList = remoteUserVos
                    .stream().filter(item -> addUserIds.contains(item.getUserId()))
                    .map(remoteUserVo -> {
                        AttendanceUser bo = new AttendanceUser();
                        bo.setUserId(remoteUserVo.getUserId());
                        bo.setIp(config);
                        return bo;
                    }).toList();

                if (!attendanceUserList.isEmpty()) {
                    // 批量插入
                    baseMapper.insertBatch(attendanceUserList);
                }
                // 需要更新该考勤机的同步时间的记录值，用于下次同步时候，传递时间区间用
                if (!updateUserIds.isEmpty()) {
                    List<AttendanceUser> updateList = updateAttendanceUserId.stream().map(id -> {
                        AttendanceUser attendanceUser = new AttendanceUser();
                        attendanceUser.setAttendanceUserId(id);
                        attendanceUser.setUpdateTime(nowDate);
                        return attendanceUser;
                    }).toList();
                    baseMapper.updateBatchById(updateList);
                }
            });
        });
        return count.intValue();
    }
}
