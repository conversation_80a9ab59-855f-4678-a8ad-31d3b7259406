package com.jxw.shufang.student.domain.vo;

import lombok.Data;
import com.jxw.shufang.common.core.enums.SourceEnum;

import java.io.Serial;
import java.io.Serializable;


@Data
public class FeedbackTemplateSourceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 来源
     */
    private SourceEnum source;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 来源id
     */
    private Long sourceId;

}
