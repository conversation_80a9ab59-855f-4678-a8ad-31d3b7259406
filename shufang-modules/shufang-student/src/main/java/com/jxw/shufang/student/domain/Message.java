package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 消息对象 message
 *
 *
 * @date 2024-06-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("message")
public class Message extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 消息id
     */
    @TableId(value = "message_id")
    private Long messageId;

    /**
     * 消息发送者类型 1 员工 2会员
     */
    private String sendUserType;

    /**
     * 消息类型 1文本 2图片
     */
    private String contentType;

    /**
     * 消息内容,发送类型为文本时，这里有值
     */
    private String messageConcat;

    /**
     * 员工或者门店管理员的userid
     */
    private Long messageStaffId;

    /**
     * 会员id
     */
    private Long messageStudentId;


    /**
     * 发送的为图片资源时，里面放ossId
     */
    private Long messageResources;

    /**
     * 接受者读取消息状态（1已读 2未读）
     */
    private String readStatus;

    /**
     * 发送状态（1发送成功 2被过滤）
     */
    private String sendStatus;

    /**
     * 敏感词id（被过滤才有）
     */
    private Long sensitiveId;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
