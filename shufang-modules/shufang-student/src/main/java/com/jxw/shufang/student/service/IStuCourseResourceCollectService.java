package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StuCourseResourceCollectBo;
import com.jxw.shufang.student.domain.vo.StuCourseResourceCollectVo;

import java.util.Collection;
import java.util.List;

/**
 * 会员对课程资源的收藏Service接口
 *
 *
 * @date 2024-05-13
 */
public interface IStuCourseResourceCollectService {

    /**
     * 查询会员对课程资源的收藏
     */
    StuCourseResourceCollectVo queryById(Long resourceCollectId);

    /**
     * 查询会员对课程资源的收藏列表
     */
    TableDataInfo<StuCourseResourceCollectVo> queryPageList(StuCourseResourceCollectBo bo, PageQuery pageQuery);

    /**
     * 查询会员对课程资源的收藏列表
     */
    List<StuCourseResourceCollectVo> queryList(StuCourseResourceCollectBo bo);

    /**
     * 新增会员对课程资源的收藏
     */
    Boolean insertByBo(StuCourseResourceCollectBo bo);

    /**
     * 修改会员对课程资源的收藏
     */
    Boolean updateByBo(StuCourseResourceCollectBo bo);

    /**
     * 校验并批量删除会员对课程资源的收藏信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
