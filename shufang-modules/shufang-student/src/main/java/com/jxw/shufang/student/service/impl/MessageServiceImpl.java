package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.SensitiveGroup;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.Message;
import com.jxw.shufang.student.domain.bo.MessageBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.vo.MessageGroupVo;
import com.jxw.shufang.student.domain.vo.MessageVo;
import com.jxw.shufang.student.domain.vo.StudentVo;
import com.jxw.shufang.student.mapper.MessageMapper;
import com.jxw.shufang.student.service.IMessageService;
import com.jxw.shufang.student.service.ISensitiveService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudentService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 消息Service业务层处理
 *
 *
 * @date 2024-06-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MessageServiceImpl implements IMessageService, BaseService {

    private final MessageMapper baseMapper;

    private final IStudentService studentService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final ISensitiveService sensitiveService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteFileService remoteFileService;


    /**
     * 查询消息
     */
    @Override
    public MessageVo queryById(Long messageId) {
        return baseMapper.selectVoById(messageId);
    }

    /**
     * 查询消息列表
     */
    @Override
    public TableDataInfo<MessageVo> queryPageList(MessageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Message> lqw = buildLambdaQueryWrapper(bo);
        Page<MessageVo> result = baseMapper.selectMessagePage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(result.getRecords(), bo.getWithStudentSysUserInfo());
        }
        if (Boolean.TRUE.equals(bo.getWithMessageStaffInfo())) {
            putMessageStaffInfo(result.getRecords());
        }

        return TableDataInfo.build(result);
    }

    /**
     * 里面放的并非staff，因为门店管理员不是员工，但是又可以发信息，所以这里放sysUser的信息更合适
     *
     * @param records
     */
    private void putMessageStaffInfo(List<MessageVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> userIdList = records.stream().map(MessageVo::getMessageStaffId).distinct().toList();
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(userIdList);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        if (CollUtil.isEmpty(remoteUserVos)) {
            return;
        }
        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        for (MessageVo messageVo : records) {
            messageVo.setMessageStaffInfo(remoteUserVoMap.get(messageVo.getMessageStaffId()));
        }
    }

    /**
     * 查询消息列表
     */
    @Override
    public List<MessageVo> queryList(MessageBo bo) {
        LambdaQueryWrapper<Message> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Message> buildLambdaQueryWrapper(MessageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Message> lqw = Wrappers.lambdaQuery();
        lqw.lt(bo.getLtMessageId() != null, Message::getMessageId, bo.getLtMessageId());
        lqw.gt(bo.getGtMessageId() != null, Message::getMessageId, bo.getGtMessageId());
        lqw.eq(StringUtils.isNotBlank(bo.getSendUserType()), Message::getSendUserType, bo.getSendUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getContentType()), Message::getContentType, bo.getContentType());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageConcat()), Message::getMessageConcat, bo.getMessageConcat());
        lqw.eq(bo.getMessageStaffId() != null, Message::getMessageStaffId, bo.getMessageStaffId());
        lqw.eq(bo.getMessageStudentId() != null, Message::getMessageStudentId, bo.getMessageStudentId());
        lqw.eq(bo.getMessageResources() != null, Message::getMessageResources, bo.getMessageResources());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), Message::getReadStatus, bo.getReadStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSendStatus()), Message::getSendStatus, bo.getSendStatus());
        lqw.eq(bo.getSensitiveId() != null, Message::getSensitiveId, bo.getSensitiveId());
        lqw.in(CollUtil.isNotEmpty(bo.getSendStatusList()), Message::getSendStatus, bo.getSendStatusList());
        if (StringUtils.isBlank(bo.getSendStatus()) && CollUtil.isEmpty(bo.getSendStatusList())) {
            lqw.eq(Message::getSendStatus, UserConstants.MESSAGE_SEND_STATUS_SUCCESS);
        }
        lqw.in(CollUtil.isNotEmpty(bo.getMessageStudentIdList()), Message::getMessageStudentId, bo.getMessageStudentIdList());
        return lqw;
    }

    private QueryWrapper<Message> buildQueryWrapper(MessageBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<Message> lqw = Wrappers.query();
        lqw.gt(bo.getLtMessageId() != null, "t.message_id", bo.getLtMessageId());
        lqw.eq(StringUtils.isNotBlank(bo.getSendUserType()), "t.send_user_type", bo.getSendUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getContentType()), "t.content_type", bo.getContentType());
        lqw.eq(StringUtils.isNotBlank(bo.getMessageConcat()), "t.message_concat", bo.getMessageConcat());
        lqw.eq(bo.getMessageStaffId() != null, "t.message_staff_id", bo.getMessageStaffId());
        lqw.eq(bo.getMessageStudentId() != null, "t.message_student_id", bo.getMessageStudentId());
        lqw.eq(bo.getMessageResources() != null, "t.message_resources", bo.getMessageResources());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), "t.read_status", bo.getReadStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSendStatus()), "t.send_status", bo.getSendStatus());
        lqw.eq(bo.getSensitiveId() != null, "t.sensitive_id", bo.getSensitiveId());
        lqw.in(CollUtil.isNotEmpty(bo.getSendStatusList()), "t.send_status", bo.getSendStatusList());
        lqw.in(CollUtil.isNotEmpty(bo.getMessageStudentIdList()), "t.message_student_id", bo.getMessageStudentIdList());
        if (StringUtils.isNotBlank(bo.getStudentName())) {
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentName(bo.getStudentName());
            List<StudentVo> studentVos = studentService.queryList(studentBo);
            if (CollUtil.isEmpty(studentVos)) {
                lqw.in("t.message_student_id", List.of(-1L));
            } else {
                List<Long> studentIds = studentVos.stream().map(StudentVo::getStudentId).toList();
                lqw.in("t.message_student_id", studentIds);
            }
        }

        if (bo.getConsultantId() != null) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(bo.getConsultantId());
            if (CollUtil.isEmpty(staffResponsibleStudentIdList)) {
                lqw.in("t.message_student_id", List.of(-1L));
            } else {
                lqw.in("t.message_student_id", staffResponsibleStudentIdList);
            }
        }

        if (StringUtils.isBlank(bo.getSendStatus()) && CollUtil.isEmpty(bo.getSendStatusList())) {
            lqw.eq("t.send_status", UserConstants.MESSAGE_SEND_STATUS_SUCCESS);

        }


        return lqw;
    }

    /**
     * 新增消息，如果返回的结果字符串不为空，表示存在敏感词
     * 返回具体的错误
     */
    @Override
    public String insertByBo(MessageBo bo) {
        String errMsg = "";
        bo.setSendStatus(UserConstants.MESSAGE_SEND_STATUS_SUCCESS);
        String messageConcat = bo.getMessageConcat();
        if (StringUtils.isNotBlank(messageConcat)) {
            //敏感词检测
            Long sensitiveId = sensitiveService.checkHasSensitive(messageConcat, SensitiveGroup.MESSAGE);
            if (sensitiveId != null) {
                bo.setSensitiveId(sensitiveId);
                bo.setSendStatus(UserConstants.MESSAGE_SEND_STATUS_FILTER);
                errMsg = "发送失败，消息存在敏感词";
            }
        }

        if (UserConstants.MESSAGE_TYPE_IMAGE.equals(bo.getContentType())) {
            bo.setMessageConcat("【图片】");
        }

        bo.setReadStatus(UserConstants.MESSAGE_READ_STATUS_UNREAD);
        Message add = MapstructUtils.convert(bo, Message.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMessageId(add.getMessageId());
        } else {
            errMsg = "发送失败";
        }
        return errMsg;
    }

    /**
     * 修改消息
     */
    @Override
    public Boolean updateByBo(MessageBo bo) {
        Message update = MapstructUtils.convert(bo, Message.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Message entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<MessageVo> stuMessageInfoPage(MessageBo bo, PageQuery pageQuery) {
        String readStatus = bo.getReadStatus();
        bo.setReadStatus(null);
        handleQueryParam(bo);
        QueryWrapper<Message> messageQueryWrapper = buildQueryWrapper(bo);
        Page<MessageVo> result = baseMapper.stuMessageInfoPage(messageQueryWrapper, pageQuery.build(), readStatus);

        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(result.getRecords(), bo.getWithStudentSysUserInfo());
        }

        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    @Override
    public List<MessageVo> staffShowMessageList(MessageBo bo) {
        String readStatus = bo.getReadStatus();
        bo.setReadStatus(null);
        handleQueryParam(bo);
        QueryWrapper<Message> messageQueryWrapper = buildQueryWrapper(bo);
        List<MessageVo> result = baseMapper.staffShowMessageList(messageQueryWrapper, readStatus);

        if (Boolean.TRUE.equals(bo.getWithStudentInfo())) {
            putStudentInfo(result, bo.getWithStudentSysUserInfo());
        }

        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(result);
        }
        return result;
    }

    @Override
    public TableDataInfo<MessageGroupVo> messageGroupPage(MessageBo messageBo, PageQuery pageQuery) {
        messageBo.setWithStudentInfo(Boolean.TRUE);
        messageBo.setWithStudentSysUserInfo(Boolean.TRUE);
        messageBo.setWithMessageStaffInfo(Boolean.TRUE);
        TableDataInfo<MessageVo> messageVoTableDataInfo = queryPageList(messageBo, pageQuery);
        TableDataInfo<MessageGroupVo> build = TableDataInfo.build();
        build.setCode(messageVoTableDataInfo.getCode());
        build.setMsg(messageVoTableDataInfo.getMsg());
        build.setTotal(messageVoTableDataInfo.getTotal());
        if (CollUtil.isEmpty(messageVoTableDataInfo.getRows())) {
            return build;
        }
        List<MessageGroupVo> messageGroupVos = new ArrayList<>();
        for (MessageVo messageVo : messageVoTableDataInfo.getRows()) {
            MessageGroupVo messageGroupVo = new MessageGroupVo();
            if (StringUtils.isBlank(messageVo.getSendUserType())) {
                continue;
            }
            messageGroupVo.setSendUserType(messageVo.getSendUserType());
            messageGroupVo.setSendTime(messageVo.getCreateTime());
            if (UserConstants.MESSAGE_TYPE_IMAGE.equals(messageVo.getContentType())) {
                messageGroupVo.setContent(messageVo.getMessageResources() != null ? messageVo.getMessageResources() + "" : "");
            } else {
                messageGroupVo.setContent(messageVo.getMessageConcat());
            }
            messageGroupVo.setContentType(StringUtils.isNotBlank(messageVo.getContentType()) ? messageVo.getContentType() : "1");
            if (UserConstants.MESSAGE_SENDER_TYPE_STAFF.equals(messageVo.getSendUserType())) {
                RemoteUserVo messageStaffInfo = messageVo.getMessageStaffInfo();
                if (messageStaffInfo != null) {
                    messageGroupVo.setUserShowName(messageStaffInfo.getNickName());
                    messageGroupVo.setAvatarUrl(messageStaffInfo.getAvatar() != null ? messageStaffInfo.getAvatar() + "" : "");
                }
            } else {
                StudentVo student = messageVo.getStudent();
                if (student != null) {
                    messageGroupVo.setUserShowName(student.getStudentName());
                    if (student.getSysUser() != null) {
                        messageGroupVo.setAvatarUrl(student.getSysUser().getAvatar() != null ? student.getSysUser().getAvatar() + "" : "");
                    }
                }
            }
            messageGroupVo.setMessageId(messageVo.getMessageId());
            messageGroupVos.add(messageGroupVo);
        }


        build.setRows(messageGroupVos);
        //把所有ossid拿出啦，换成url
        List<String> ossIdList = messageGroupVos.stream().filter(m -> m.getContentType() != null && UserConstants.MESSAGE_TYPE_IMAGE.equals(m.getContentType() + "")).map(MessageGroupVo::getContent).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> ossIdList2 = messageGroupVos.stream().map(MessageGroupVo::getAvatarUrl).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        //合并去重
        ossIdList.addAll(ossIdList2);
        ossIdList = ossIdList.stream().distinct().toList();
        if (CollUtil.isEmpty(ossIdList)) {
            return build;
        }
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIds(ossIdList.stream().collect(Collectors.joining(",")));
        if (CollUtil.isEmpty(remoteFiles)) {
            return build;
        }
        Map<String, RemoteFile> remoteFileMap = remoteFiles.stream().collect(Collectors.toMap(e -> e.getOssId() + "", Function.identity()));
        for (MessageGroupVo messageGroupVo : messageGroupVos) {
            if (messageGroupVo.getContentType() != null && UserConstants.MESSAGE_TYPE_IMAGE.equals(messageGroupVo.getContentType() + "")) {
                String ossId = messageGroupVo.getContent();
                if (StringUtils.isNotBlank(ossId)) {
                    RemoteFile remoteFile = remoteFileMap.get(ossId);
                    if (remoteFile != null) {
                        messageGroupVo.setContent(remoteFile.getUrl());
                    }
                }
            }
            if (StringUtils.isNotBlank(messageGroupVo.getAvatarUrl())) {
                String ossId = messageGroupVo.getAvatarUrl();
                if (StringUtils.isNotBlank(ossId)) {
                    RemoteFile remoteFile = remoteFileMap.get(ossId);
                    if (remoteFile != null) {
                        messageGroupVo.setAvatarUrl(remoteFile.getUrl());
                    }
                }
            }
        }
        return build;

    }

    @Override
    public Long unreadCount(Long id, String sendUserType) {
        LambdaQueryWrapper<Message> queryWrapper = Wrappers.lambdaQuery();
        switch (sendUserType) {
            case UserConstants.MESSAGE_SENDER_TYPE_STUDENT:
                queryWrapper.eq(Message::getMessageStaffId, id);

                break;
            case UserConstants.MESSAGE_SENDER_TYPE_STAFF:
                queryWrapper.eq(Message::getMessageStudentId, id);

                break;
        }
        queryWrapper
            .eq(Message::getReadStatus, UserConstants.MESSAGE_READ_STATUS_UNREAD)
            .eq(Message::getSendStatus, UserConstants.MESSAGE_SEND_STATUS_SUCCESS)
            .eq(Message::getSendUserType, sendUserType);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public Boolean readMessages(List<Long> ids, String sendUserType) {
        if (CollUtil.isEmpty(ids)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<Message> updateWrapper = Wrappers.lambdaUpdate(Message.class);
        updateWrapper.in(Message::getMessageId, ids);
        updateWrapper.eq(Message::getSendUserType, sendUserType);
        updateWrapper.eq(Message::getReadStatus, UserConstants.MESSAGE_READ_STATUS_UNREAD);
        updateWrapper.set(Message::getReadStatus, UserConstants.MESSAGE_READ_STATUS_READ);
        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    public Boolean readMessagesOfStudent(Long messageStudentId, String sendUserType) {
        if (Objects.isNull(messageStudentId)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<Message> updateWrapper = Wrappers.lambdaUpdate(Message.class);
        updateWrapper.eq(Message::getMessageStudentId, messageStudentId);
        updateWrapper.eq(Message::getSendUserType, sendUserType);
        updateWrapper.eq(Message::getReadStatus, UserConstants.MESSAGE_READ_STATUS_UNREAD);
        updateWrapper.set(Message::getReadStatus, UserConstants.MESSAGE_READ_STATUS_READ);
        return baseMapper.update(updateWrapper) > 0;
    }

    private void putConsultantInfo(List<MessageVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIds = records.stream().map(MessageVo::getMessageStudentId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentIds)) {
            return;
        }
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(studentIds);
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return;
        }
        Collection<Long> staffIds = studentConsultantIdMap.values();

        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(new ArrayList<>(staffIds));
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> staffMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, Function.identity()));
        for (MessageVo messageVo : records) {
            if (messageVo.getMessageStudentId() != null) {
                Long consultantId = studentConsultantIdMap.get(messageVo.getMessageStudentId());
                if (consultantId != null) {
                    messageVo.setConsultant(staffMap.get(consultantId));
                }
            }
        }
    }

    private void putStudentInfo(List<MessageVo> records, Boolean withSysUserInfo) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(MessageVo::getMessageStudentId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(list);
        studentBo.setWithSysUserInfo(withSysUserInfo);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        Map<Long, StudentVo> map = studentVos.stream().collect(Collectors.toMap(StudentVo::getStudentId, Function.identity()));
        for (MessageVo messageVo : records) {
            if (messageVo.getMessageStudentId() != null) {
                messageVo.setStudent(map.get(messageVo.getMessageStudentId()));
            }
        }
    }

    private void handleQueryParam(MessageBo record) {
        if (record.getMessageStudentId()!=null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null ) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getMessageStudentIdList())) {
                record.setMessageStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setMessageStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getMessageStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getMessageStudentIdList())){
                    record.setMessageStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setMessageStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getMessageStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getMessageStudentIdList())){
                    record.setMessageStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setMessageStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getMessageStudentIdList(), studentIdList)));
                }
            }

        }
    }


    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
