package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.utils.VideoSlicesUtils;
import com.jxw.shufang.student.domain.bo.*;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;
import com.jxw.shufang.student.enums.StudyQuestionTypeEnum;
import com.jxw.shufang.student.service.MqTemplateSendMessageService;
import com.jxw.shufang.student.service.RecordVideoProcessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @Date 2025/5/8 15:07
 * @Version 1
 * @Description
 */
@Service
public class RecordVideoProcessServiceImpl implements RecordVideoProcessService {
    @Resource
    private MqTemplateSendMessageService sendMessageService;

    @Override
    public void batchRecordAiVideoProgress(BatchAiReportProcessBO reportProcess, Long studentId) {
        Optional.ofNullable(reportProcess.getRecords())
            .filter(CollectionUtil::isNotEmpty)
            .ifPresent(records -> this.processAiRecords(records, studentId));
    }

    @Override
    public void batchRecordPlanVideoProgress(BatchPlanReportProcessBO reportProcess, Long studentId) {
        Optional.ofNullable(reportProcess.getRecords())
            .filter(CollectionUtil::isNotEmpty)
            .ifPresent(records -> this.processPlanRecords(records, studentId));
    }

    private void processPlanRecords(List<BatchPlanReportProcessBO.RecordProcess> records, Long studentId) {
        Predicate<BatchPlanReportProcessBO.RecordProcess> predicate = getProcessPredicate();
        records.forEach(recordProcess -> {
            if (predicate.test(recordProcess)) {
                this.processPlan(studentId, recordProcess);
            }
        });
    }


    private void processAiRecords(List<BatchAiReportProcessBO.RecordProcess> records, Long studentId) {
        Predicate<BatchAiReportProcessBO.RecordProcess> predicate = getRecordProcessPredicate();
        records.forEach(record -> {
            if (predicate.test(record)) {
                this.processAi(studentId, record);
            }
        });
    }

    private void processAi(Long studentId, BatchAiReportProcessBO.RecordProcess record) {
        StudyModuleTypeEnum moduleType = StudyModuleTypeEnum.getByCode(record.getStudyType());
        switch (moduleType) {
            case STUDY -> this.processAiStudyRecord(record, studentId);
            case PRACTICE -> this.processAiQuestionVideoRecord(record, StudyQuestionTypeEnum.PRACTICE.getQuestionType(),
                StudyModuleAndGroupEnum.AI_STUDY_PRACTICE, studentId);
            case TEST -> this.processAiQuestionVideoRecord(record, StudyQuestionTypeEnum.TEST.getQuestionType(),
                StudyModuleAndGroupEnum.AI_STUDY_TEST, studentId);
        }
    }

    private void processPlan(Long studentId, BatchPlanReportProcessBO.RecordProcess record) {
        StudyModuleTypeEnum moduleType = StudyModuleTypeEnum.getByCode(record.getStudyType());
        switch (moduleType) {
            case STUDY -> this.processPlanStudyRecord(record, studentId);
            case PRACTICE -> this.processPlanQuestionRecord(record, StudyQuestionTypeEnum.PRACTICE.getQuestionType(),
                StudyModuleAndGroupEnum.STUDY_PLAN_PRACTICE, studentId);
            case TEST -> this.processPlanQuestionRecord(record, StudyQuestionTypeEnum.TEST.getQuestionType(),
                StudyModuleAndGroupEnum.STUDY_PLAN_TEST, studentId);
        }
    }

    private void processPlanQuestionRecord(BatchPlanReportProcessBO.RecordProcess record,
                                           String questionType,
                                           StudyModuleAndGroupEnum studyPlanPractice,
                                           Long studentId) {
        String topicAndTagFlag = String.format("%s:%s", MqTopicConstant.SHUFANG_STUDENT_TOPIC, MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG);
        this.sendAsyncMq(topicAndTagFlag, this.buildQuestionVideoRecordBo(record, questionType, studyPlanPractice, studentId));
    }

    private void processPlanStudyRecord(BatchPlanReportProcessBO.RecordProcess record, Long studentId) {
        String topicAndTagFlag = String.format("%s:%s", MqTopicConstant.SHUFANG_STUDENT_TOPIC, MqTagConstant.STUDY_VIDEO_DURATION_TIME_TAG);
        this.sendAsyncMq(topicAndTagFlag, this.buildStudyVideoRecordBo(record, studentId));
    }

    private void processAiQuestionVideoRecord(BatchAiReportProcessBO.RecordProcess recordProcess,
                                              String questionType,
                                              StudyModuleAndGroupEnum aiStudyPractice,
                                              Long studentId) {
        String topicAndTagFlag = String.format("%s:%s", MqTopicConstant.SHUFANG_STUDENT_TOPIC, MqTagConstant.QUESTION_VIDEO_DURATION_TIME_TAG);
        this.sendAsyncMq(topicAndTagFlag, this.buildQuestionVideoRecordBo(recordProcess, questionType, aiStudyPractice, studentId));
    }

    private void processAiStudyRecord(BatchAiReportProcessBO.RecordProcess recordProcess, Long studentId) {
        String topicAndTagFlag = String.format("%s:%s", MqTopicConstant.SHUFANG_STUDENT_TOPIC, MqTagConstant.AI_STUDY_VIDEO_DURATION_TIME_TAG);
        this.sendAsyncMq(topicAndTagFlag, this.buildProcessStudyRecord(recordProcess, studentId));
    }

    private QuestionVideoRecordBo buildQuestionVideoRecordBo(BatchPlanReportProcessBO.RecordProcess record,
                                                             String questionType,
                                                             StudyModuleAndGroupEnum studyPlanPractice,
                                                             Long studentId) {
        QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
        questionVideoRecordBo.setStudyPlanningRecordId(record.getStudyPlanningRecordId());
        questionVideoRecordBo.setCourseId(record.getCourseId());
        questionVideoRecordBo.setVideoId(record.getVideoId());
        String concatMultiple = this.getConcatMultiple(record.getSpliceItem(), record.getMultiple());
        questionVideoRecordBo.setStudyVideoSlices(concatMultiple);
        questionVideoRecordBo.setStudentId(studentId);
        questionVideoRecordBo.setCommitTime(new Date());
        questionVideoRecordBo.setQuestionId(record.getQuestionId());
        questionVideoRecordBo.setQuestionType(questionType);
        questionVideoRecordBo.setStudyModuleType(studyPlanPractice);
        return questionVideoRecordBo;
    }

    private StudyVideoRecordBo buildStudyVideoRecordBo(BatchPlanReportProcessBO.RecordProcess record, Long studentId) {
        StudyVideoRecordBo studyVideoRecordBo = new StudyVideoRecordBo();
        studyVideoRecordBo.setStudyPlanningRecordId(record.getStudyPlanningRecordId());
        studyVideoRecordBo.setCourseId(record.getCourseId());
        studyVideoRecordBo.setVideoId(record.getVideoId());
        String sliceConcatMultiple = this.getConcatMultiple(record.getSpliceItem(), record.getMultiple());
        studyVideoRecordBo.setStudyVideoSlices(sliceConcatMultiple);
        studyVideoRecordBo.setStudentId(studentId);
        studyVideoRecordBo.setCommitTime(new Date());
        studyVideoRecordBo.setStudyModuleType(StudyModuleAndGroupEnum.STUDY_PLAN_LEARN);
        return studyVideoRecordBo;
    }

    private AiStudyVideoRecordBo buildProcessStudyRecord(BatchAiReportProcessBO.RecordProcess recordProcess, Long studentId) {
        AiStudyVideoRecordBo aiStudyVideoRecordBo = new AiStudyVideoRecordBo();
        aiStudyVideoRecordBo.setCourseId(recordProcess.getCourseId());
        aiStudyVideoRecordBo.setVideoId(recordProcess.getVideoId());
        aiStudyVideoRecordBo.setStudentId(studentId);
        String sliceConcatMultiple = this.getConcatMultiple(recordProcess.getSpliceItem(), recordProcess.getMultiple());
        aiStudyVideoRecordBo.setStudyVideoSlices(sliceConcatMultiple);
        aiStudyVideoRecordBo.setCommitTime(new Date());
        aiStudyVideoRecordBo.setModuleAndGroupEnum(StudyModuleAndGroupEnum.AI_STUDY_LEARN);
        return aiStudyVideoRecordBo;
    }


    private QuestionVideoRecordBo buildQuestionVideoRecordBo(BatchAiReportProcessBO.RecordProcess recordProcess,
                                                             String questionType,
                                                             StudyModuleAndGroupEnum aiStudyPractice,
                                                             Long studentId) {
        QuestionVideoRecordBo questionVideoRecordBo = new QuestionVideoRecordBo();
        questionVideoRecordBo.setCourseId(recordProcess.getCourseId());
        questionVideoRecordBo.setVideoId(recordProcess.getVideoId());
        String concatMultiple = this.getConcatMultiple(recordProcess.getSpliceItem(), recordProcess.getMultiple());
        questionVideoRecordBo.setStudyVideoSlices(concatMultiple);
        questionVideoRecordBo.setStudentId(studentId);
        questionVideoRecordBo.setCommitTime(new Date());
        questionVideoRecordBo.setQuestionId(recordProcess.getQuestionId());
        questionVideoRecordBo.setQuestionType(questionType);
        questionVideoRecordBo.setStudyModuleType(aiStudyPractice);
        return questionVideoRecordBo;
    }

    private Predicate<BatchPlanReportProcessBO.RecordProcess> getProcessPredicate() {
        return record -> null != record.getStudyType() && null != StudyModuleTypeEnum.getByCode(record.getStudyType());
    }

    private static Predicate<BatchAiReportProcessBO.RecordProcess> getRecordProcessPredicate() {
        return record -> null != record.getStudyType() && null != StudyModuleTypeEnum.getByCode(record.getStudyType());
    }

    private String getConcatMultiple(String spliceItem, Double multiple) {
        return VideoSlicesUtils.videoSliceConcatMultiple(spliceItem, multiple);
    }

    private void sendAsyncMq(String topicAndTagFlag, Object data) {
        sendMessageService.sendAsyncMq(topicAndTagFlag, data);
    }
}
