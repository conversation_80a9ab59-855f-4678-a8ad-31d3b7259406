package com.jxw.shufang.student.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.jxw.shufang.student.config.StudentIntroduceConfig;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.branch.api.RemoteBranchConfigService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchConfigBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchConfigVo;
import com.jxw.shufang.common.core.enums.PreferentialModifyTypeEnum;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.domain.Student;
import com.jxw.shufang.student.domain.StudentIntroduceRecord;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudentIntroduceRecordBo;
import com.jxw.shufang.student.domain.bo.StudentPreferentialRecordBo;
import com.jxw.shufang.student.domain.vo.StudentIntroduceRecordVo;
import com.jxw.shufang.student.domain.vo.StudentPreferentialRecordVo;
import com.jxw.shufang.student.enums.PreferentialInOrOutEnum;
import com.jxw.shufang.student.enums.TimeUnitEnum;
import com.jxw.shufang.student.mapper.StudentIntroduceRecordMapper;
import com.jxw.shufang.student.service.IStudentIntroduceRecordService;
import com.jxw.shufang.student.service.IStudentPreferentialRecordService;
import com.jxw.shufang.student.service.IStudentService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StudentIntroduceRecordServiceImpl implements IStudentIntroduceRecordService, BaseService {

    private final StudentIntroduceRecordMapper baseMapper;

    private final IStudentService studentService;

    private final StudentIntroduceConfig studentIntroduceConfig;

    private final IStudentPreferentialRecordService studentPreferentialRecordService;

    @DubboReference
    private final RemoteBranchConfigService remoteBranchConfigService;

    @Override
    public TableDataInfo<StudentIntroduceRecordVo> queryPage(StudentIntroduceRecordBo bo, PageQuery pageQuery) {
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        IPage<StudentIntroduceRecordVo> studentIntroduceVoIPage = baseMapper.pageStudentIntroduceRecord(pageQuery.build(), buildQueryMapper(bo));
        return TableDataInfo.build(studentIntroduceVoIPage);
    }

    @Override
    public boolean insertByBo(StudentIntroduceRecordBo studentIntroduceRecordBo) {
        StudentIntroduceRecord convert = MapstructUtils.convert(studentIntroduceRecordBo, StudentIntroduceRecord.class);
        return baseMapper.insert(convert) > 0;
    }

    @Override
    public List<StudentIntroduceRecordVo> listAllRecord(StudentIntroduceRecordBo bo) {
        PageQuery query = new PageQuery();
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setOrderByColumn("t.create_time");
        query.setIsAsc("desc");
        IPage<StudentIntroduceRecordVo> studentIntroduceRecordVoIPage = baseMapper.pageStudentIntroduceRecord(query.build(), buildQueryMapper(bo));
        return studentIntroduceRecordVoIPage.getRecords();
    }

    @Override
    public Boolean remoteDealWithOrderSuccess(Long studentId, Long orderId, List<RemoteProductBo> productBoList,
        Long operator, Long operateDept) {
        StudentIntroduceRecordVo introduceRecord = getByStudentId(studentId);
        if (null == introduceRecord || Boolean.TRUE.equals(introduceRecord.getDoReferent())) {
            return true;
        }
        RemoteProductBo officialProductBo = null;
        for (RemoteProductBo remoteProductBo : productBoList) {
            if (!studentIntroduceConfig.getExperienceStudentTypeId().equals(remoteProductBo.getStudentTypeId())) {
                officialProductBo = remoteProductBo;
            }
        }
        if (null == officialProductBo) {
            return true;
        }
        Student student = studentService.queryStudentById(introduceRecord.getIntroduceStudentId());
        RemoteBranchConfigBo queryConfigBo = new RemoteBranchConfigBo();
        queryConfigBo.setBranchId(introduceRecord.getBranchId());
        RemoteBranchConfigVo remoteBranchConfigVo = remoteBranchConfigService.remoteGetPreferentialAmountConfig(queryConfigBo);
        RemoteBranchConfigVo.ProducePreferentialConfig specificProduceConfig = null;
        if (null != remoteBranchConfigVo && !CollectionUtils.isEmpty(remoteBranchConfigVo.getProducePreferentialConfigList())) {
            RemoteProductBo finalOfficialProductBo = officialProductBo;
            specificProduceConfig = remoteBranchConfigVo.getProducePreferentialConfigList().stream().filter(
                config -> finalOfficialProductBo.getProductId().equals(config.getProduceId())
            ).findFirst().orElse(null);
        }
        if (null == specificProduceConfig) {
            // 更新转介绍记录列表
            StudentIntroduceRecordBo updateBo = new StudentIntroduceRecordBo();
            updateBo.setIntroduceRecordId(introduceRecord.getIntroduceRecordId());
            updateBo.setRemark("未配置优惠额度");
            updateBo.setPreferentialAmount(BigDecimal.ZERO);
            updateBo.setBranchAuthTypeName(officialProductBo.getProductName());
            updateBo.setDoReferent(true);
            updateBo.setOrderId(orderId);
            updateBo.setUpdateBy(operator);
            if (!updateByBo(updateBo)) {
                log.error("转介绍首次购入正式卡更新介绍会员时更新转介绍记录列表异常，订单id：" + orderId);
                return false;
            }
        } else {
            BigDecimal productPreferentialAmount = specificProduceConfig.getPreferentialAmount();
            // 更新会员优惠额度
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentId(student.getStudentId());
            studentBo.setPreferentialAmount(student.getPreferentialAmount());
            studentBo.setPreferentialAmountVersion(student.getPreferentialAmountVersion());
            studentBo.setUpdateBy(operator);
            if (!studentService.updatePreferentialAmountByBo(studentBo, productPreferentialAmount)) {
                log.error("转介绍首次购入正式卡更新介绍会员时会员优惠额度更新失败，订单id：" + orderId);
                return false;
            }
            // 新增优惠额度变动记录
            StudentPreferentialRecordBo saveRecordBo = new StudentPreferentialRecordBo();
            saveRecordBo.setOwnerStudentId(introduceRecord.getIntroduceStudentId());
            saveRecordBo.setBusinessId(orderId);
            saveRecordBo.setChangeType(PreferentialInOrOutEnum.IN.getType());
            saveRecordBo.setGainType(PreferentialModifyTypeEnum.STUDENT_INTRODUCE.getType());
            saveRecordBo.setChangePreferentialAmount(productPreferentialAmount);
            saveRecordBo.setCreateBy(operator);
            saveRecordBo.setCreateDept(operateDept);
            Calendar calendar = Calendar.getInstance();
            calendar.add(TimeUnitEnum.fromString(studentIntroduceConfig.getFreezeTimeUnit())
                .getCalendarTimeUnit(), specificProduceConfig.getFrozenDay());
            saveRecordBo.setUnFrozenTime(calendar.getTime());
            if (!studentPreferentialRecordService.saveRecordByBo(saveRecordBo)) {
                log.error("转介绍首次购入正式卡更新介绍会员时插入优惠额度变动记录异常，订单id：" + orderId);
                return false;
            }
            // 更新转介绍记录列表
            StudentIntroduceRecordBo updateBo = new StudentIntroduceRecordBo();
            updateBo.setPreferentialAmount(productPreferentialAmount);
            updateBo.setBranchAuthTypeName(officialProductBo.getProductName());
            updateBo.setIntroduceRecordId(introduceRecord.getIntroduceRecordId());
            updateBo.setUnFrozenTime(saveRecordBo.getUnFrozenTime());
            updateBo.setDoReferent(true);
            updateBo.setOrderId(orderId);
            updateBo.setUpdateBy(operator);
            if (!updateByBo(updateBo)) {
                log.error("转介绍首次购入正式卡更新介绍会员时更新转介绍记录列表异常，订单id：" + orderId);
                return false;
            }
        }
        return true;
    }

    @Override
    public Boolean remoteDealWithOrderRefund(Long studentId, Long orderId) {
        StudentIntroduceRecordVo introduceRecordVo = getByStudentId(studentId);
        if (null == introduceRecordVo || null == introduceRecordVo.getIntroduceRecordId()) {
            return true;
        }
        StudentPreferentialRecordBo frozenQueryBo = new StudentPreferentialRecordBo();
        frozenQueryBo.setBusinessId(orderId);
        frozenQueryBo.setOwnerStudentId(introduceRecordVo.getIntroduceStudentId());
        StudentPreferentialRecordVo frozenRecord = studentPreferentialRecordService.getFrozenRecord(frozenQueryBo);
        if (null != frozenRecord) {
            Student student = studentService.queryStudentById(frozenRecord.getOwnerStudentId());
            // 更新会员优惠额度
            StudentBo studentBo = new StudentBo();
            studentBo.setStudentId(student.getStudentId());
            studentBo.setPreferentialAmount(student.getPreferentialAmount());
            studentBo.setPreferentialAmountVersion(student.getPreferentialAmountVersion());
            if (!studentService.updatePreferentialAmountByBo(studentBo, frozenRecord.getChangePreferentialAmount().negate())) {
                log.error("转介绍首次购入正式卡更新介绍会员时会员优惠额度更新失败，订单id：" + orderId);
                return false;
            }
            // 新增优惠额度变动记录
            StudentPreferentialRecordBo saveRecordBo = new StudentPreferentialRecordBo();
            saveRecordBo.setOwnerStudentId(frozenRecord.getOwnerStudentId());
            saveRecordBo.setBusinessId(orderId);
            saveRecordBo.setChangeType(PreferentialInOrOutEnum.OUT.getType());
            saveRecordBo.setGainType(PreferentialModifyTypeEnum.STUDENT_INTRODUCE.getType());
            saveRecordBo.setChangePreferentialAmount(frozenRecord.getChangePreferentialAmount());
            if (!studentPreferentialRecordService.saveRecordByBo(saveRecordBo)) {
                log.error("转介绍首次订单退款时，插入优惠额度变动记录失败，订单id：" + orderId);
                return false;
            }
            // 更新转介绍记录列表
            LambdaUpdateWrapper<StudentIntroduceRecord> updateWrapper =
                Wrappers.lambdaUpdate(StudentIntroduceRecord.class);
            updateWrapper.set(StudentIntroduceRecord::getPreferentialAmount, BigDecimal.ZERO)
                .set(StudentIntroduceRecord::getRemark, "介绍人退款").set(StudentIntroduceRecord::getUnFrozenTime, null)
                .eq(StudentIntroduceRecord::getIntroduceRecordId, introduceRecordVo.getIntroduceRecordId());
            if (baseMapper.update(updateWrapper) <= 0) {
                log.error("转介绍首次订单退款时更新转介绍记录列表失败，订单id：" + orderId);
                return false;
            }
            frozenQueryBo.setRecordId(frozenRecord.getRecordId());
            if (!studentPreferentialRecordService.removeFrozenTime(frozenQueryBo)) {
                log.error("转介绍首次订单退款时，更新原发放记录的解冻时间异常，订单id：" + orderId);
                return false;
            }
        }
        return true;
    }

    @Override
    public Map<Long, StudentIntroduceRecord> getIntroduceMapByStudentIdList(List<Long> studentIds) {
        LambdaQueryWrapper<StudentIntroduceRecord> wrapper =
            Wrappers.lambdaQuery(StudentIntroduceRecord.class).in(StudentIntroduceRecord::getStudentId, studentIds);
        List<StudentIntroduceRecord> studentIntroduceRecords = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(studentIntroduceRecords)) {
            return new HashMap<>(1);
        } else {
            return studentIntroduceRecords.stream()
                .collect(Collectors.toMap(StudentIntroduceRecord::getStudentId, Function.identity()));
        }
    }

    /**
     * 更新转介绍记录列表
     *
     * @param updateBo
     * @return
     */
    public boolean updateByBo(StudentIntroduceRecordBo updateBo) {
        if (null == updateBo.getIntroduceRecordId()) {
            return false;
        }
        StudentIntroduceRecord convert = MapstructUtils.convert(updateBo, StudentIntroduceRecord.class);
        return baseMapper.updateById(convert) > 0;
    }

    @Override
    public boolean deleteById(Long introduceRecordId) {
        if (null == introduceRecordId) {
            return false;
        }
        return baseMapper.deleteById(introduceRecordId) > 0;
    }

    @Override
    public StudentIntroduceRecordVo getByStudentId(Long studentId) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(StudentIntroduceRecord.class).eq(StudentIntroduceRecord::getStudentId, studentId));
    }

    private QueryWrapper<StudentIntroduceRecord> buildQueryMapper(StudentIntroduceRecordBo bo) {
        QueryWrapper<StudentIntroduceRecord> wrapper = Wrappers.query();
        wrapper.between(null != bo.getCheckInTimeStart() && null != bo.getCheckInTimeEnd(), "t.create_time", bo.getCheckInTimeStart(), bo.getCheckInTimeEnd())
            .eq(null != bo.getBranchId(), "t.branch_id", bo.getBranchId())
            .eq(null != bo.getIntroduceStudentId(), "t.introduce_student_id", bo.getIntroduceStudentId())
            .eq(StringUtils.isNotEmpty(bo.getStudentGrade()), "t.student_grade", bo.getStudentGrade());
        return wrapper;
    }
}
