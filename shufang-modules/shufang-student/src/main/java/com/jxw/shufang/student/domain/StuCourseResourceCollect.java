package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员对课程资源的收藏对象 stu_course_resource_collect
 *
 *
 * @date 2024-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stu_course_resource_collect")
public class StuCourseResourceCollect extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 资源收藏id
     */
    private Long resourceCollectId;

    /**
     * 课程资源ID
     */
    private Long courseResourceId;

    /**
     * 会员ID
     */
    private Long studentId;


}
