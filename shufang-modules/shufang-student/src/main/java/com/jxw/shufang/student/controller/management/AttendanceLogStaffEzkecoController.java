package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.AttendanceLogStaffEzkecoBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStaffEzkecoVo;
import com.jxw.shufang.student.service.IAttendanceLogStaffEzkecoService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ezkeco员工考勤记录
 * 前端访问路由地址为:/student/attendanceLogStaffEzkeco
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/attendanceLogStaffEzkeco")
public class AttendanceLogStaffEzkecoController extends BaseController {

    private final IAttendanceLogStaffEzkecoService attendanceLogStaffEzkecoService;

    /**
     * 查询ezkeco员工考勤记录列表
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:list")
    @GetMapping("/list")
    public TableDataInfo<AttendanceLogStaffEzkecoVo> list(AttendanceLogStaffEzkecoBo bo, PageQuery pageQuery) {
        return attendanceLogStaffEzkecoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出ezkeco员工考勤记录列表
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:export")
    @Log(title = "ezkeco员工考勤记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttendanceLogStaffEzkecoBo bo, HttpServletResponse response) {
        List<AttendanceLogStaffEzkecoVo> list = attendanceLogStaffEzkecoService.queryList(bo);
        ExcelUtil.exportExcel(list, "ezkeco员工考勤记录", AttendanceLogStaffEzkecoVo.class, response);
    }

    /**
     * 获取ezkeco员工考勤记录详细信息
     *
     * @param attendanceLogStaffEzkecoId 主键
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:query")
    @GetMapping("/{attendanceLogStaffEzkecoId}")
    public R<AttendanceLogStaffEzkecoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attendanceLogStaffEzkecoId) {
        return R.ok(attendanceLogStaffEzkecoService.queryById(attendanceLogStaffEzkecoId));
    }

    /**
     * 新增ezkeco员工考勤记录
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:add")
    @Log(title = "ezkeco员工考勤记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttendanceLogStaffEzkecoBo bo) {
        return toAjax(attendanceLogStaffEzkecoService.insertByBo(bo));
    }

    /**
     * 修改ezkeco员工考勤记录
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:edit")
    @Log(title = "ezkeco员工考勤记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttendanceLogStaffEzkecoBo bo) {
        return toAjax(attendanceLogStaffEzkecoService.updateByBo(bo));
    }

    /**
     * 删除ezkeco员工考勤记录
     *
     * @param attendanceLogStaffEzkecoIds 主键串
     */
    @SaCheckPermission("student:attendanceLogStaffEzkeco:remove")
    @Log(title = "ezkeco员工考勤记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attendanceLogStaffEzkecoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attendanceLogStaffEzkecoIds) {
        return toAjax(attendanceLogStaffEzkecoService.deleteWithValidByIds(List.of(attendanceLogStaffEzkecoIds), true));
    }
}
