package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentType;

import java.util.List;

/**
 * 会员类型（会员卡的类型，默认有一个 体验卡 类型）业务对象 student_type
 *
 *
 * @date 2024-03-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentType.class, reverseConvertGenerate = false)
public class StudentTypeBo extends BaseEntity {

    /**
     * 会员类型id
     */
    @NotNull(message = "会员类型id不能为空", groups = { EditGroup.class })
    private Long studentTypeId;

    /**
     * 会员类型名称
     */
    @NotBlank(message = "会员类型名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studentTypeName;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentTypeSort;

    /**
     * 会员类型IDs
     */
    private List<Long> studentTypeIds;

    /**
     * 是否携带产品
     */
    private Boolean withProduct;

}
