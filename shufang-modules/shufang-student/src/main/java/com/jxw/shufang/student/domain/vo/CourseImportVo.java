package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;


@Data
@NoArgsConstructor
@ExcelIgnoreUnannotated
public class CourseImportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学科
     */
    @ExcelProperty("学科")
    @NotBlank(message = "学科不能为空")
    private String affiliationSubject;

    /**
     * 课程名称
     */
    @ExcelProperty("课程名称")
    @NotBlank(message = "课程名称不能为空")
    private String courseName;

    /**
     * 课程专题
     */
    @ExcelProperty("课程专题")
    @NotBlank(message = "课程专题不能为空")
    private String specialTopic;

    /**
     * 课程来源
     */
    @ExcelProperty("课程来源")
    @NotBlank(message = "课程来源不能为空")
    private String courseSource;

    /**
     * 年级
     */
    @ExcelProperty("年级")
    @NotBlank(message = "年级不能为空")
    private String grade;

    /**
     * 学段（对应字典值）
     */
    @ExcelProperty("学段")
    @NotBlank(message = "学段不能为空")
    private String stage;

    @ExcelProperty("一级名称")
    @NotBlank(message = "一级名称不能为空")
    private String courseFirstName;

    @ExcelProperty("排序")
    private Integer sort;
    /**
     * 知识点ID
     */
//    @ExcelProperty("知识点ID")
    private Long knowledgeId;


    private Long courseId;

    private Boolean hasChild;

    private Long parentId;

    //类型(1课程 2章 3节 4小节 5小小节 以此类推)
    private Integer courseType;

    //当前行索引
    private Integer currentRowIndex;

    //属性<AttrId,value>
    private Map<Long,String> attrMap;

    //是否已经存在在数据库中
    private Boolean exist;

    private Boolean hasError;

    //祖先节点（最顶层默认0）
    private String ancestors;

    /**
     * 季度类型凑得（ 字典code）
     */
    private String quarterType;



}

