package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.TestRecordBo;
import com.jxw.shufang.student.domain.vo.TestRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 测验记录Service接口
 *
 *
 * @date 2024-05-09
 */
public interface ITestRecordService {

    /**
     * 查询测验记录
     */
    TestRecordVo queryById(Long testRecordId);

    /**
     * 查询测验记录列表
     */
    TableDataInfo<TestRecordVo> queryPageList(TestRecordBo bo, PageQuery pageQuery);

    /**
     * 查询测验记录列表
     */
    List<TestRecordVo> queryList(TestRecordBo bo);

    /**
     * 新增测验记录
     */
    Boolean insertByBo(TestRecordBo bo);

    /**
     * 修改测验记录
     */
    Boolean updateByBo(TestRecordBo bo);

    /**
     * 校验并批量删除测验记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<TestRecordBo> convert);
}
