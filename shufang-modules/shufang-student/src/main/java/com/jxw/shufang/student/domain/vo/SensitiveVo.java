package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.Sensitive;

import java.io.Serial;
import java.io.Serializable;


/**
 * 敏感词视图对象 sensitive
 *
 *
 * @date 2024-06-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Sensitive.class)
public class SensitiveVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 敏感词id
     */
    @ExcelProperty(value = "敏感词id")
    private Long sensitiveId;

    /**
     * 敏感词组名
     */
    @ExcelProperty(value = "敏感词组名")
    private String sensitiveGroupName;

    /**
     * 敏感词内容（多个，换行符隔开）
     */
    @ExcelProperty(value = "敏感词内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "多=个，换行符隔开")
    private String sensitiveContent;


}
