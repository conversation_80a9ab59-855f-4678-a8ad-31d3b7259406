package com.jxw.shufang.student.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.bo.AiPracticeRecordBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AiCorrectionRecordInfoBoConvertAiPracticeRecordBo extends BaseMapper<AiCorrectionRecordInfoBo, AiPracticeRecordBo> {

    AiPracticeRecordBo convert(AiCorrectionRecordInfoBo source);
}
