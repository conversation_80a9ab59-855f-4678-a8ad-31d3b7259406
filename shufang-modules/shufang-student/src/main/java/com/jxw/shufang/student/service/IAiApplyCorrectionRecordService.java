package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiApplyCorrectionRecordBo;
import com.jxw.shufang.student.domain.vo.AiApplyCorrectionRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * ai申请批改记录Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiApplyCorrectionRecordService {

    /**
     * 查询ai申请批改记录
     */
    AiApplyCorrectionRecordVo queryById(Long aiApplyCorrectionRecordId);

    /**
     * 查询ai申请批改记录列表
     */
    TableDataInfo<AiApplyCorrectionRecordVo> queryPageList(AiApplyCorrectionRecordBo bo, PageQuery pageQuery);

    /**
     * 查询ai申请批改记录列表
     */
    List<AiApplyCorrectionRecordVo> queryList(AiApplyCorrectionRecordBo bo);

    /**
     * 新增ai申请批改记录
     */
    Boolean insertByBo(AiApplyCorrectionRecordBo bo);

    /**
     * 修改ai申请批改记录
     */
    Boolean updateByBo(AiApplyCorrectionRecordBo bo);

    /**
     * 校验并批量删除ai申请批改记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询最后一条记录
     *
     * @param aiApplyCorrectionRecordBo 应用更正记录bo
     *
     * @date 2024/05/08 12:39:52
     */
    AiApplyCorrectionRecordVo queryLastRecord(AiApplyCorrectionRecordBo aiApplyCorrectionRecordBo);


    /**
     * 计数
     *
     * @param bo bo
     *
     * @date 2024/06/06 10:34:16
     */
    Long count(AiApplyCorrectionRecordBo bo);

    boolean updateApplyResult( Long aiApplyCorrectionRecordId, String applyResult);

    boolean updateBatchById(List<AiApplyCorrectionRecordBo> updateList);

    /**
     * 当前的申请记录是否可见（当前系统只查询当天凌晨三点之后的记录）
     *
     * @param aiApplyCorrectionRecordVo
     * @return
     */
    boolean wheatherApplyRecordVisiable(AiApplyCorrectionRecordVo aiApplyCorrectionRecordVo);
}
