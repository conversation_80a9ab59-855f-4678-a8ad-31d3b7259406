package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.StudyFeedbackReportBo;
import com.jxw.shufang.student.domain.vo.StudyFeedbackReportVo;

import java.util.Collection;
import java.util.List;

/**
 * 学习反馈报告Service接口
 *
 * @date 2024-06-14
 */
public interface IStudyFeedbackReportService {

    /**
     * 查询学习反馈报告
     */
    StudyFeedbackReportVo queryById(Long id);

    /**
     * 查询学习反馈报告列表
     */
    TableDataInfo<StudyFeedbackReportVo> queryPageList(StudyFeedbackReportBo bo, PageQuery pageQuery);

    /**
     * 查询学习反馈报告列表
     */
    List<StudyFeedbackReportVo> queryList(StudyFeedbackReportBo bo);

    /**
     * 新增学习反馈报告
     */
    Boolean insertByBo(StudyFeedbackReportBo bo);

    /**
     * 修改学习反馈报告
     */
    Boolean updateByBo(StudyFeedbackReportBo bo);

    /**
     * 校验并批量删除学习反馈报告信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);



    /**
     * 根据学生ID和时间范围查询报告
     */
    StudyFeedbackReportVo queryByStudentAndPeriod(Long studentId, String periodStart, String periodEnd);

    /**
     * 更新反馈状态
     *
     * <p>当学生或家长确认查看学习反馈报告后，更新反馈状态为已反馈。
     * 此方法会同时更新反馈报告本身的状态以及关联的学习规划待处理记录状态。</p>
     *
     * @param id 学习反馈报告ID
     * @param feedbackStatus 反馈状态：0-待反馈，1-已反馈
     * @return 更新是否成功
     */
    Boolean updateFeedbackStatus(Long id, Integer feedbackStatus);

    /**
     * H5页面查询学习反馈报告详细信息
     * 包含学习计划和学习资料信息
     */
    StudyFeedbackReportVo queryH5ById(Long id);

    /**
     * 获取学习规划H5地址
     */
    String getStudyPlanningUrl(Long studyPlanningId);

}
