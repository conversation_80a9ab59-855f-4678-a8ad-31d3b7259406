package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.QuestionCollect;
import com.jxw.shufang.student.domain.bo.QuestionCollectBo;
import com.jxw.shufang.student.domain.vo.QuestionCollectVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface QuestionCollectMapper extends BaseMapperPlus<QuestionCollect, QuestionCollectVo> {
    Page<QuestionCollectVo> selectQuestionCollects(@Param("page") Page<QuestionCollectVo> build, @Param(Constants.WRAPPER) LambdaQueryWrapper<QuestionCollect> lqw);

    int deleteBatchByBo(@Param("bos") List<QuestionCollectBo> bos);
}
