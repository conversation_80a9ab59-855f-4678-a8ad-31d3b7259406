package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.student.domain.PrintRecord;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 打印记录视图对象 print_record
 *
 *
 * @date 2024-05-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PrintRecord.class)
public class PrintRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 打印id
     */
    @ExcelProperty(value = "打印id")
    private Long printRecordId;

    /**
     * 打印类型（1单份 2合并）
     */
    @ExcelProperty(value = "打印类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=单份,2=合并")
    private String printType;

    /**
     * 来源课程id
     */
    @ExcelProperty(value = "来源课程id")
    private Long courseId;

    /**
     * 学习规划记录id
     */
    private Long studyPlanRecordId;

    /**
     * 打印内容,KnowledgeResourceType枚举，多个用数组分开
     */
    @ExcelProperty(value = "打印内容,KnowledgeResourceType枚举，多个用数组分开")
    private String printContent;
    /**
     * 会员ID
     */
    private Long studentId;

    /**
     * 试卷id,打印试卷时会用得上
     */
    private Long paperId;

    /**
     * 1代表原卷，2代表解析卷，3代表原卷+解析卷
     */
    private String paperType;

    private StudentVo student;

    private Long createDept;

    private Date createTime;

    private Long createBy;

    private Date updateTime;

    private Long updateBy;


    /**
     * 打印来源 1:学习规划 2:课程 3:试卷
     */
    public Integer getPrintSource(){
        if (paperId!= null){
            return 3;
        }
        if (studyPlanRecordId!= null){
            return 1;
        }
        if (courseId!= null){
            return 2;
        }
        return null;
    }

}
