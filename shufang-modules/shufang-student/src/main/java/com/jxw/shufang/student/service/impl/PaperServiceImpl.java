package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.student.domain.Paper;
import com.jxw.shufang.student.domain.bo.PaperBo;
import com.jxw.shufang.student.domain.bo.PaperDocBo;
import com.jxw.shufang.student.domain.vo.PaperDocVo;
import com.jxw.shufang.student.domain.vo.PaperTypeGroupVo;
import com.jxw.shufang.student.domain.vo.PaperVo;
import com.jxw.shufang.student.mapper.PaperMapper;
import com.jxw.shufang.student.service.IPaperCollectionService;
import com.jxw.shufang.student.service.IPaperService;
import com.jxw.shufang.system.api.RemoteDictService;
import com.jxw.shufang.system.api.domain.vo.RemoteDictDataVo;
import org.springframework.cloud.client.hypermedia.RemoteResource;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 试卷Service业务层处理
 *
 *
 * @date 2024-05-14
 */
@RequiredArgsConstructor
@Service
public class PaperServiceImpl implements IPaperService, BaseService {

    private final PaperMapper baseMapper;


    private final IPaperCollectionService paperCollectionService;

    @DubboReference
    private RemoteDictService remoteDictService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    /**
     * 查询试卷
     */
    @Override
    public PaperVo queryById(Long paperId) {
        return baseMapper.selectVoById(paperId);
    }

    /**
     * 查询试卷列表
     */
    @Override
    public TableDataInfo<PaperVo> queryPageList(PaperBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Paper> lqw = buildQueryWrapper(bo);
        Page<PaperVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void putBranchInfo(List<PaperVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> branchIdList = records.stream().map(PaperVo::getBranchId).filter(Objects::nonNull).distinct().toList();

        if (CollUtil.isEmpty(branchIdList)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(branchIdList);
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo);
        Map<Long, RemoteBranchVo> remoteBranchVoMap = StreamUtils.toIdentityMap(remoteBranchVos, RemoteBranchVo::getBranchId);

        for (PaperVo paperVo : records) {
            if (paperVo.getBranchId() == null) {
                continue;
            }
            RemoteBranchVo remoteBranchVo = remoteBranchVoMap.get(paperVo.getBranchId());
            paperVo.setBranch(remoteBranchVo);
        }

    }

    /**
     * 查询试卷列表
     */
    @Override
    public List<PaperVo> queryList(PaperBo bo) {
        LambdaQueryWrapper<Paper> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Paper> buildQueryWrapper(PaperBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Paper> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPaperStage()), Paper::getPaperStage, bo.getPaperStage());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperType()), Paper::getPaperType, bo.getPaperType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperGrade()), Paper::getPaperGrade, bo.getPaperGrade());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperAffiliationSubject()), Paper::getPaperAffiliationSubject, bo.getPaperAffiliationSubject());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperRegion()), Paper::getPaperRegion, bo.getPaperRegion());
        lqw.like(StringUtils.isNotBlank(bo.getPaperTitle()), Paper::getPaperTitle, bo.getPaperTitle());
        lqw.eq(bo.getOriginal() != null, Paper::getOriginal, bo.getOriginal());
        lqw.eq(bo.getAnalysis() != null, Paper::getAnalysis, bo.getAnalysis());
        lqw.eq(bo.getOriginalWithAnalysis() != null, Paper::getOriginalWithAnalysis, bo.getOriginalWithAnalysis());
        lqw.eq(StringUtils.isNotBlank(bo.getPaperSource()), Paper::getPaperSource, bo.getPaperSource());
        lqw.in(CollUtil.isNotEmpty(bo.getPaperIdList()), Paper::getPaperId, bo.getPaperIdList());

        if (Boolean.TRUE.equals(bo.getContainManagement())) {
            lqw.and(w -> {
                    w.eq(Paper::getPaperSource, "1")
                        .or(bo.getBranchId() != null || CollUtil.isNotEmpty(bo.getBranchIdList()), (e) -> {
                            e.eq(bo.getBranchId() != null, Paper::getBranchId, bo.getBranchId());
                            e.in(CollUtil.isNotEmpty(bo.getBranchIdList()), Paper::getBranchId, bo.getBranchIdList());
                        });

                }
            );
        } else {
            lqw.eq(bo.getBranchId() != null, Paper::getBranchId, bo.getBranchId());
            lqw.eq(CollUtil.isNotEmpty(bo.getPaperIdList()), Paper::getBranchId, bo.getPaperIdList());

        }

        lqw.eq(Paper::getDelFlag, StringUtils.isBlank(bo.getDelFlag()) ? UserConstants.DEL_FLAG_NO : bo.getDelFlag());

        if (Boolean.TRUE.equals(bo.getIsCollect())) {
            List<Long> collectPaperIdList = paperCollectionService.queryCollectionPaperIdList(LoginHelper.getStudentId());
            if (CollUtil.isEmpty(collectPaperIdList)) {
                lqw.in(Paper::getPaperId, List.of(-1L));
            } else {
                lqw.in(Paper::getPaperId, collectPaperIdList);
            }
        }

        if (StringUtils.isNotBlank(bo.getOrderBy())) {
            lqw.last(" ORDER BY " + bo.getOrderBy());
        }

        return lqw;
    }

    /**
     * 新增试卷
     */
    @Override
    public Boolean insertByBo(PaperBo bo) {
        Paper add = MapstructUtils.convert(bo, Paper.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPaperId(add.getPaperId());
        }
        return flag;
    }

    /**
     * 修改试卷
     */
    @Override
    public Boolean updateByBo(PaperBo bo) {
        Paper update = MapstructUtils.convert(bo, Paper.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Paper entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除试卷
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Long queryTotalPaperCount(PaperBo paperBo) {
        return baseMapper.selectCount(buildQueryWrapper(paperBo));
    }

    @Override
    public List<PaperTypeGroupVo> queryPaperTypeAndCount(PaperBo paperBo) {
        //先查询所有的试卷类型
        List<RemoteDictDataVo> paperTypeList = remoteDictService.selectDictDataByType("paper_type");
        if (CollUtil.isEmpty(paperTypeList)) {
            return List.of();
        }

        //按照条件查询试卷数量
        LambdaQueryWrapper<Paper> paperLambdaQueryWrapper = buildQueryWrapper(paperBo);
        List<PaperTypeGroupVo> paperTypeGroupList = baseMapper.selectPaperTypeGroupList(paperLambdaQueryWrapper);
        Map<String, Long> map = null;
        if (CollUtil.isNotEmpty(paperTypeGroupList)) {
            map = StreamUtils.toMap(paperTypeGroupList, PaperTypeGroupVo::getPaperType, PaperTypeGroupVo::getPaperCount);
        } else {
            map = new HashMap<>();
        }
        List<PaperTypeGroupVo> resList = new ArrayList<>();
        for (RemoteDictDataVo remoteDictDataVo : paperTypeList) {
            PaperTypeGroupVo paperTypeGroupVo = new PaperTypeGroupVo();
            paperTypeGroupVo.setPaperType(remoteDictDataVo.getDictValue());
            paperTypeGroupVo.setPaperCount(map.getOrDefault(remoteDictDataVo.getDictValue(), 0L));
            resList.add(paperTypeGroupVo);
        }
        return resList;
    }


    @Override
    public List<PaperDocVo> checkUploadFileNameList(List<String> fileNames) {
        if (CollUtil.isEmpty(fileNames)) {
            throw new ServiceException("文件名不能为空");
        }
        for (String fileName : fileNames) {
            if (StringUtils.isBlank(fileName)) {
                throw new ServiceException("文件名不能为空");
            }
        }
        if (fileNames.size() != fileNames.stream().distinct().count()) {
            throw new ServiceException("存在重复文件名");
        }

        final String originalFileNameEnd = "（原卷）";
        final String analysisFileNameEnd = "（解析）";

        List<PaperDocVo> resList = new ArrayList<>();
        //按照文件名分组
        Map<String, List<String>> fileNameMap = StreamUtils.groupByKey(fileNames, e -> {
            return e.replace(".pdf", "").replace(originalFileNameEnd, "").replace(analysisFileNameEnd, "");
        });

        for (Map.Entry<String, List<String>> entry : fileNameMap.entrySet()) {
            PaperDocVo paperDocVo = new PaperDocVo();
            paperDocVo.setDocName(entry.getKey());
            List<String> value = entry.getValue();
            boolean hasError = false;
            if (value.size() > 3) {
                throw new ServiceException("【" + value.get(0).replace(".pdf", "") + "】:文件存在多余文件");
            }
            for (String fileName : value) {
                String newFileName = fileName.replace(".pdf", "");
                if (!newFileName.endsWith(originalFileNameEnd) && !newFileName.endsWith(analysisFileNameEnd)) {
                    paperDocVo.setStatus("0");
                    paperDocVo.setReason("文件名格式错误");
                    hasError = true;
                    break;
                }
                if (newFileName.endsWith(originalFileNameEnd)) {
                    paperDocVo.setOriginalDoc(fileName);
                }
                if (newFileName.endsWith(analysisFileNameEnd)) {
                    paperDocVo.setAnalysisDoc(fileName);
                }
                //if (newFileName.endsWith(originalWithAnalysisFileNameEnd)) {
                //    paperDocVo.setOriginalWithAnalysisDoc(fileName);
                //}
            }
            resList.add(paperDocVo);
            if (hasError) {
                continue;
            }
            if (StringUtils.isBlank(paperDocVo.getOriginalDoc())) {
                paperDocVo.setStatus("0");
                paperDocVo.setReason("文件名称不匹配，缺少（原卷）");
                continue;
            }
            if (StringUtils.isBlank(paperDocVo.getAnalysisDoc())) {
                paperDocVo.setStatus("0");
                paperDocVo.setReason("文件名称不匹配，缺少（解析卷）");
                continue;
            }
            paperDocVo.setStatus("1");
        }
        return resList;
    }

    @Override
    public Boolean batchInsert(PaperBo bo) {
        List<PaperDocBo> paperDocList = bo.getPaperDocList();
        bo.setPaperDocList(null);
        List<Paper> insertList = new ArrayList<>();
        for (PaperDocBo paperDocBo : paperDocList) {
            Paper paper = MapstructUtils.convert(bo, Paper.class);
            paper.setPaperTitle(paperDocBo.getDocName());
            paper.setOriginal(paperDocBo.getOriginalDocOssId());
            paper.setAnalysis(paperDocBo.getAnalysisDocOssId());
            paper.setOriginalWithAnalysis(paperDocBo.getOriginalWithAnalysisDocOssId());
            insertList.add(paper);
        }
        return baseMapper.insertBatch(insertList);

    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {

    }

}
