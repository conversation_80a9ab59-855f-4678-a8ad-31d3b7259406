package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_preferential_record")
public class StudentPreferentialRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员优惠额度变动记录id
     */
    @TableId
    private Long recordId;
    /**
     * 会员ID
     */
    private Long ownerStudentId;
    /**
     * 来源会员ID
     */
    private Long fromStudentId;
    /**
     * 业务ID
     */
    private Long businessId;
    /**
     * 支出or入账 0-收入 1-支出
     */
    private Integer changeType;
    /**
     * 获取类别 1-转介绍 2-转赠 3-会员消费
     */
    private Integer gainType;
    /**
     * 优惠额度变动金额
     */
    private BigDecimal changePreferentialAmount;
    /**
     * 优惠额度解冻时间（对于ownerStudentId，只有收入的优惠额度才有冻结时间）
     */
    private Date unFrozenTime;
}
