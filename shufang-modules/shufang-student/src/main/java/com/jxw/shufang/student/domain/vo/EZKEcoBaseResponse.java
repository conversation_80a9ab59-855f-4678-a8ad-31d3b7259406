package com.jxw.shufang.student.domain.vo;


import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.util.List;

/**
 * 考勤机返回结果基础类封装EZKEcoBaseResponse
 *
 * <AUTHOR>
 * @date 2024-05-18
 */
@Data
@ExcelIgnoreUnannotated
public class EZKEcoBaseResponse<T> {

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 数据体
     */
    private Data<T> data;

    /**
     * 响应状态码
     */
    private int ret;

    /**
     * 请求成功
     */
    public boolean isOk() {
        return getRet() == 0;
    }

    /**
     * 获取DataItems集合
     * @return DataItems集合
     */
    public List<T> getDataItems() {
        Data<T> data1 = this.getData();
        if (data1 != null) {
            return ListUtil.toList(data1.getItems());
        }
       return List.of();
    }

    /**
     * 嵌套类 Data实体类
     * @param <T>
     */
    @lombok.Data
    public static class Data<T> {

        /**
         * 考勤记录总数
         */
        private int count;

        /**
         * 考勤记录列表
         */
        private List<T> items;
    }
}
