package com.jxw.shufang.student.service.studyduration;

import com.jxw.shufang.student.enums.StudyModelGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import com.jxw.shufang.student.enums.StudyModuleTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/8 17:39
 * @Version 1
 * @Description
 */
public interface ModuleGroupProvider<T> {
    /**
     * 过滤数据
     * @param records
     * @param moduleAndGroupEnum
     * @return
     */
     List<T> filterData(List<T> records, StudyModuleAndGroupEnum moduleAndGroupEnum);

    /**
     * 匹配模块类型
     * @return
     */
    List<StudyModuleTypeEnum> matchStudyModuleType();

    /**
     * 匹配组类型
     * @return
     */
    List<StudyModelGroupEnum> matchStudyModelGroup();
}
