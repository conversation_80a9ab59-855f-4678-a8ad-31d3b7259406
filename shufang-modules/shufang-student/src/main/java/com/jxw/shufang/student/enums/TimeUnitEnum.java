package com.jxw.shufang.student.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Calendar;

/**
 * @author: cyj
 * @date: 2025/3/26
 */
@AllArgsConstructor
@Getter
public enum TimeUnitEnum {
    DAYS(Calendar.DAY_OF_MONTH), HOURS(Calendar.HOUR_OF_DAY), MINUTES(Calendar.MINUTE);

    private final Integer calendarTimeUnit;

    public static TimeUnitEnum fromString(String unit) {
        try {
            return TimeUnitEnum.valueOf(unit.toUpperCase());
        } catch (IllegalArgumentException e) {
            return DAYS; // 默认值
        }
    }
}
