package com.jxw.shufang.student.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 反馈记录视图对象 feedback_record
 * @date 2024-05-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FeedbackRecordVo.class)
public class FeedbackRecordShareVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈记录id
     */
    //@ExcelProperty(value = "反馈记录id")
    private Long feedbackRecordId;

    /**
     * 会员id
     */
    //@ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 反馈内容
     */
    @ExcelProperty(value = "反馈内容")
    private String feedbackContent;

    /**
     * 反馈日期范围-开始
     */
    //@ExcelProperty(value = "反馈日期范围-开始")
    private Date feedbackStartDate;

    /**
     * 反馈日期范围-结束
     */
    //@ExcelProperty(value = "反馈日期范围-结束")
    private Date feedbackEndDate;


    /**
     * 表现分数（满分为5分，用星进行展示）
     */
    @ExcelProperty(value = "表现分数")
    private Long showScore;


    private Long createBy;

    @ExcelProperty(value = "反馈时间",order = 5)
    private Date createTime;


    /**
     * 最近的一次通知时间
     */
    private Date notificationTime;
    /**
     * 家长是否已读 1 未读 2已读
     */
    private String parentReadStatus;





}
