package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.Message;
import com.jxw.shufang.student.domain.vo.MessageVo;

import java.util.List;

/**
 * 消息Mapper接口
 *
 *
 * @date 2024-06-15
 */
public interface MessageMapper extends BaseMapperPlus<Message, MessageVo> {


    Page<MessageVo> stuMessageInfoPage(@Param(Constants.WRAPPER) QueryWrapper<Message> messageQueryWrapper,@Param("page") Page<MessageVo> build,@Param("readStatus") String readStatus);

    Page<MessageVo> selectMessagePage(@Param("page") Page<MessageVo> build,@Param(Constants.WRAPPER)  LambdaQueryWrapper<Message> lqw);

    List<MessageVo> staffShowMessageList(@Param(Constants.WRAPPER) QueryWrapper<Message> messageQueryWrapper,@Param("readStatus") String readStatus);
}
