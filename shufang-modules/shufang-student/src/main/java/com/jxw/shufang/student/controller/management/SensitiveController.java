package com.jxw.shufang.student.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.domain.bo.SensitiveBo;
import com.jxw.shufang.student.domain.vo.SensitiveVo;
import com.jxw.shufang.student.service.ISensitiveService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 敏感词
 * 前端访问路由地址为:/student/management/sensitive
 *
 *
 * @date 2024-06-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/sensitive/group")
public class SensitiveController extends BaseController {

    private final ISensitiveService sensitiveService;

    /**
     * 查询敏感词列表
     */
    @SaCheckPermission("student:sensitive:list")
    @GetMapping("/list")
    public TableDataInfo<SensitiveVo> list(SensitiveBo bo, PageQuery pageQuery) {
        return sensitiveService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出敏感词列表
     */
    @SaCheckPermission("student:sensitive:export")
    @Log(title = "敏感词", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SensitiveBo bo, HttpServletResponse response) {
        List<SensitiveVo> list = sensitiveService.queryList(bo);
        ExcelUtil.exportExcel(list, "敏感词", SensitiveVo.class, response);
    }

    /**
     * 获取敏感词详细信息
     *
     * @param sensitiveId 主键
     */
    @SaCheckPermission("student:sensitive:query")
    @GetMapping("/{sensitiveId}")
    public R<SensitiveVo> getInfo(@PathVariable Long sensitiveId) {
        if(Long.valueOf(0).equals(sensitiveId)){
            //查出来默认的那一条
            List<SensitiveVo>  sensitiveVos = sensitiveService.queryList(new SensitiveBo());
            return R.ok(sensitiveVos.get(0));
        }
        return R.ok(sensitiveService.queryById(sensitiveId));
    }

    /**
     * 新增敏感词
     */
    @SaCheckPermission("student:sensitive:add")
    @Log(title = "敏感词", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SensitiveBo bo) {
        return toAjax(sensitiveService.insertByBo(bo));
    }

    /**
     * 修改敏感词
     */
    @SaCheckPermission("student:sensitive:edit")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SensitiveBo bo) {
        return toAjax(sensitiveService.updateByBo(bo));
    }

    /**
     * 修改敏感词，通过分组更新
     */
    @SaCheckPermission("student:sensitive:edit")
    @Log(title = "敏感词", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/group")
    public R<Void> editByGroup(@Validated(EditGroup.class) @RequestBody SensitiveBo bo) {
        return toAjax(sensitiveService.updateByGroup(bo));
    }

    /**
     * 删除敏感词
     *
     * @param sensitiveIds 主键串
     */
    @SaCheckPermission("student:sensitive:remove")
    @Log(title = "敏感词", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sensitiveIds}")
    public R<Void> remove(Long[] sensitiveIds) {
        return toAjax(sensitiveService.deleteWithValidByIds(List.of(sensitiveIds), true));
    }
}
