package com.jxw.shufang.student.domain.convert;

import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AttendanceDailyActivityBoConvertAttendanceDailyActivity extends BaseMapper<AttendanceDailyActivityBo, AttendanceDailyActivity> {

}
