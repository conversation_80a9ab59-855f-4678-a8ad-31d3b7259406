package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AiCorrectionRecordInfoBo;
import com.jxw.shufang.student.domain.vo.AiCorrectionRecordInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * ai批改记录详情Service接口
 *
 *
 * @date 2024-05-23
 */
public interface IAiCorrectionRecordInfoService {

    /**
     * 查询ai批改记录详情
     */
    AiCorrectionRecordInfoVo queryById(Long aiCorrectionRecordInfoId);

    /**
     * 查询ai批改记录详情列表
     */
    TableDataInfo<AiCorrectionRecordInfoVo> queryPageList(AiCorrectionRecordInfoBo bo, PageQuery pageQuery);

    /**
     * 查询ai批改记录详情列表
     */
    List<AiCorrectionRecordInfoVo> queryList(AiCorrectionRecordInfoBo bo);

    /**
     * 新增ai批改记录详情
     */
    Boolean insertByBo(AiCorrectionRecordInfoBo bo);

    /**
     * 修改ai批改记录详情
     */
    Boolean updateByBo(AiCorrectionRecordInfoBo bo);

    /**
     * 校验并批量删除ai批改记录详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean insertBatchByBo(List<AiCorrectionRecordInfoBo> correctionRecordInfoBoList);
}
