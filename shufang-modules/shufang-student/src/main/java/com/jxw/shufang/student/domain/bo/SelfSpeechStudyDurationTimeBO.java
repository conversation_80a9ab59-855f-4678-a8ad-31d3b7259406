package com.jxw.shufang.student.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/2 9:53
 * @Version 1
 * @Description 学习规划自讲入参
 */
@Data
public class SelfSpeechStudyDurationTimeBO {
    /**
     * 学习规划ID
     */
    @NotNull(message = "学习规划ID不能为空")
    private Long studyPlanningRecordId;
    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    /**
     * 自讲video时长
     */
    @NotNull(message = "自讲视频学习视频录制时长不能为空")
    private Long selfSpeechTime;

    /**
     * 自讲videoID
     */
    @NotNull(message = "自讲视频ID不能为空")
    private Long selfSpeechVideoId;
}
