package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class WrongQuestionReviseBo {

    /**
     * 错题合集id
     */
    @NotNull(message = "错题合集id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long wrongQuestionCollectionId;

    @NotEmpty(message = "错题合集记录不能为空", groups = { AddGroup.class})
    private List<WrongQuestionCollectionDetailBo> collectionDetailBoList;

    /**
     * 订正截图（oss_id，多个，逗号隔开）
     */
    @NotNull(message = "作答照片不能为空", groups = { AddGroup.class })
    private String reviseScreenshots;

}
