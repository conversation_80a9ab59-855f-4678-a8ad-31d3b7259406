package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.StudentParentRecord;

/**
 * 会员绑定家长记录（时间逆序的最后一条记录和会员中的对应）业务对象 student_parent_record
 *
 *
 * @date 2024-03-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentParentRecord.class, reverseConvertGenerate = false)
public class StudentParentRecordBo extends BaseEntity {

    /**
     * 会员绑定家长记录id
     */
    @NotNull(message = "会员绑定家长记录id不能为空", groups = { EditGroup.class })
    private Long studentParentRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 家长姓名
     */
    @NotBlank(message = "家长姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentName;

    /**
     * 家长微信昵称
     */
    @NotBlank(message = "家长微信昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentWechatNickname;

    /**
     * 家长微信号
     */
    @NotBlank(message = "家长微信号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentWechatNo;

    /**
     * 家长微信头像
     */
    @NotBlank(message = "家长微信头像不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentWechatImg;

    /**
     * 家长微信openId
     */
    @NotBlank(message = "家长微信openId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentWechatOpenId;

    /**
     * 家长微信unionId
     */
    @NotBlank(message = "家长微信unionId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentWechatUnionId;

    /**
     * 是否关注公众号（0是 1否）
     */
    @NotBlank(message = "是否关注公众号（0是 1否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isFollow;

    private Boolean withStudentSysUserInfo;

    //前端取keyId用于换取公众号的openId 可能会用到
    private String keyId;


    //打卡日期，yyyy-MM-dd
    private String attendanceDate;

}
