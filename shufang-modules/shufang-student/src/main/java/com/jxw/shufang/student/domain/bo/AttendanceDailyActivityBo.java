package com.jxw.shufang.student.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import jakarta.validation.constraints.NotNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = AttendanceDailyActivity.class)
public class AttendanceDailyActivityBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceDailyActivityId;

    /**
     * 会员id
     */
    @NotNull(message = "会员ID不能为空", groups = AddGroup.class)
    private Long studentId;

    /**
     * 每日日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date recordDate;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date feedbackStartDate;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date feedbackEndDate;
    /**
     * 打卡/登录时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventTime;

    /**
     * 事件时间来源（1：考勤打卡。2：设备登录）
     */
    private Integer eventTimeSource;

    /**
     * 反馈状态 1未反馈 2已反馈 3超时反馈 4超时未反馈
     */
    private Integer feedbackStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    private List<Long> studentIdList;

    /**
     * 会员姓名+电话尾数
     */
    private String nameWithPhone;

    /**
     * 会员账号
     */
    private String studentAccount;

    /**
     * 会员顾问id
     */
    private Long consultantId;

    private Boolean withConsultantInfo;

    private Boolean withCreateStaffInfo;

    /**
     * 门店IDList
     */
    private List<Long> branchIdList;
    /**
     * 门店IDList
     */
    private Long branchId;
}
