package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 会员AI课程分配记录（时间逆序的最后一条记录和会员中的对应）对象 student_ai_course_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student_ai_course_record")
public class StudentAiCourseRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员AI课程分配记录表id
     */
    @TableId(value = "student_ai_course_record_id")
    private Long studentAiCourseRecordId;

    /**
     * 会员id
     */
    private Long studentId;


}
