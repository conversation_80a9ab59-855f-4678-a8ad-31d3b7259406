package com.jxw.shufang.student.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AiWrongQuestionRecord;

/**
 * Ai学习错题记录业务对象 ai_wrong_question_record
 *
 *
 * @date 2024-05-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiWrongQuestionRecord.class, reverseConvertGenerate = false)
public class AiWrongQuestionRecordBo extends BaseEntity {

    /**
     * ai学习错题id
     */
    @NotNull(message = "ai学习错题id不能为空", groups = { EditGroup.class })
    private Long aiWrongQuestionRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long questionId;

    /**
     * 题目序号
     */
    @NotBlank(message = "题目序号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionNo;

    /**
     * 来源类型（1测试 2练习）
     */
    @NotBlank(message = "来源类型（1测试 2练习）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sourceType;

    /**
     * 作答结果（对应字典值，如全错 半错）
     */
    @NotBlank(message = "作答结果（对应字典值，如全错 半错）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String answerResult;


}
