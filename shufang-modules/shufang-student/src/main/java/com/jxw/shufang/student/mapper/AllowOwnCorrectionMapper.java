package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.AllowOwnCorrection;
import com.jxw.shufang.student.domain.vo.AllowOwnCorrectionVo;

/**
 * 允许自主批改Mapper接口
 *
 *
 * @date 2024-05-07
 */
public interface AllowOwnCorrectionMapper extends BaseMapperPlus<AllowOwnCorrection, AllowOwnCorrectionVo> {

    Page<AllowOwnCorrectionVo> selectAllowOwnCorrectionVoPage(@Param("page") Page<AllowOwnCorrectionVo> build,@Param(Constants.WRAPPER) QueryWrapper<AllowOwnCorrection> lqw);
}
