package com.jxw.shufang.student.domain.bo;

import com.jxw.shufang.student.enums.StudyModuleAndGroupEnum;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.student.domain.AiStudyVideoRecord;

import java.util.Date;
import java.util.List;

/**
 * ai学习视频记录（视频观看记录）业务对象 ai_study_video_record
 *
 *
 * @date 2024-05-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiStudyVideoRecord.class, reverseConvertGenerate = false)
public class AiStudyVideoRecordBo extends BaseEntity {

    /**
     * 学习视频记录id
     */
    @NotNull(message = "学习视频记录id不能为空", groups = { EditGroup.class })
    private Long aiStudyVideoRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 对应外部资源的视频Id
     */
    @NotNull(message = "对应外部资源的视频Id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long videoId;

    /**
     * 章节ID
     */
    @NotNull(message = "章节ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long courseId;

    /**
     * 章节id列表
     */
    private List<Long> courseIdList;

    /**
     * 测评试卷ID
     */
    private Long testPaperId;

    /**
     * 播放时长（单位秒 按日累加）
     */
    @NotNull(message = "播放时长（单位秒 按日累加）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyVideoDuration;

    /**
     * 分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）
     */
    @NotBlank(message = "分片值（如 00:18-00:41(23’’),00:42-00:45(3’’) ，非需要展示情况下，尽可能不查此字段）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String studyVideoSlices;

    /**
     * 提交时间，与数据库无关
     */
    private Date commitTime;

    /**
     * 创建时间(日期)列表
     */
    private List<String> createDateList;

    private StudyModuleAndGroupEnum moduleAndGroupEnum;
}
