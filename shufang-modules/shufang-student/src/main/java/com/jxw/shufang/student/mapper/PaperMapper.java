package com.jxw.shufang.student.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.student.domain.Paper;
import com.jxw.shufang.student.domain.vo.PaperTypeGroupVo;
import com.jxw.shufang.student.domain.vo.PaperVo;

import java.util.List;

/**
 * 试卷Mapper接口
 *
 *
 * @date 2024-05-14
 */
public interface PaperMapper extends BaseMapperPlus<Paper, PaperVo> {

    List<PaperTypeGroupVo> selectPaperTypeGroupList(@Param(Constants.WRAPPER)LambdaQueryWrapper<Paper> paperLambdaQueryWrapper);
}
