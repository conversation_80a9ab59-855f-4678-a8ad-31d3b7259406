package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 学习反馈报告对象 study_feedback_report
 *
 * @date 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("study_feedback_report")
public class StudyFeedbackReport extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 顾问ID
     */
    private Long consultantId;

    /**
     * 报告周期开始日期
     */
    private Date periodStart;

    /**
     * 报告周期结束日期
     */
    private Date periodEnd;

    /**
     * 总结内容
     */
    private String summary;

    /**
     * 存在问题
     */
    private String issues;

    /**
     * 需关注重点
     */
    private String focusPoints;

    /**
     * 状态:1-草稿,2-已发布
     */
    private Integer status;

    /**
     * 反馈状态:0-待反馈,1-已反馈
     */
    private Integer feedbackStatus;

    /**
     * 删除标记:0-未删除,1-已删除
     */
    @TableLogic
    private Integer isDeleted;
}
