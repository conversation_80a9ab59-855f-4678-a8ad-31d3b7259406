package com.jxw.shufang.student.service;

import com.jxw.shufang.student.domain.bo.BatchAiReportProcessBO;
import com.jxw.shufang.student.domain.bo.BatchPlanReportProcessBO;

/**
 * <AUTHOR>
 * @Date 2025/5/8 14:36
 * @Version 1
 * @Description
 */
public interface RecordVideoProcessService {
    /**
     * 批量记录AI视频学习时长
     *
     * @param reportProcessBO
     */
    void batchRecordAiVideoProgress(BatchAiReportProcessBO reportProcessBO, Long studentId);

    /**
     * 批量记录学习规划视频学习时长
     *
     * @param batchPlanReportProcessBO
     */
    void batchRecordPlanVideoProgress(BatchPlanReportProcessBO batchPlanReportProcessBO, Long studentId);
}
