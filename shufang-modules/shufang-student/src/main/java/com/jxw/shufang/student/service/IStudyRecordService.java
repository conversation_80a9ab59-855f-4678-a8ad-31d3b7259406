package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.StudyRecord;
import com.jxw.shufang.student.domain.bo.StudyRecordBo;
import com.jxw.shufang.student.domain.vo.StudyRecordVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 学习记录Service接口
 *
 *
 * @date 2024-05-06
 */
public interface IStudyRecordService {

    /**
     * 查询学习记录
     */
    StudyRecordVo queryById(Long studyRecordId);

    /**
     * 查询学习记录
     */
    StudyRecordVo queryByStudyPlanningRecordId(Long studyPlanningRecordId);

    /**
     * 查询学习记录列表
     */
    TableDataInfo<StudyRecordVo> queryPageList(StudyRecordBo bo, PageQuery pageQuery);

    /**
     * 查询学习记录列表
     */
    List<StudyRecordVo> queryList(StudyRecordBo bo);

    List<Long> queryStudyPlanningRecordIdList(StudyRecordBo bo);

    /**
     * 新增学习记录
     */
    Boolean insertByBo(StudyRecordBo bo);

    /**
     * 修改学习记录
     */
    Boolean updateByBo(StudyRecordBo bo);

    /**
     * 校验并批量删除学习记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询学习记录(单个，如果条件查出来多个mybatis会报错)
     */
    StudyRecordVo queryOnce(StudyRecordBo studyRecordBo);

    /**
     * 查询批改状态
     * @param queryType 1练习  2测试
     * @param studyPlanningRecordId   学习计划记录id
     * @param studentId 学员id
     */
    String queryCorrectStatus(int queryType,Long studyPlanningRecordId,Long studentId);

    /**
     * 查询学习时长排名（自己）
     */
    Integer queryMyRank(StudyRecordBo bo,Long studentId);

    Long countStudentStudyTime(Long studentId, Date startDate, Date endDate);

    void updateBatchById(List<StudyRecord> updated);

    List<StudyRecordVo> batchQueryListByPlanningIds(List<Long> studyPlanningRecordIds);

    Map<Long, StudyRecord> batchQueryMapByPlanningIds(List<Long> planningIds);

    void batchUpdate(List<StudyRecord> updates);

    void batchInsert(List<StudyRecord> inserts);

    List<StudyRecord> batchQuerySameStudentAiRecord(List<Long> studentIds, List<Long> courseIds);
}
