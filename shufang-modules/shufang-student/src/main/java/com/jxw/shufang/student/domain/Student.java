package com.jxw.shufang.student.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员对象 student
 *
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("student")
public class Student extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会员id
     */
    @TableId(value = "student_id")
    private Long studentId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 登录账号（可以用对应的sys_user表中的数据）
     */
    private String studentAccount;

    /**
     * 登录密码（可以用对应的sys_user表中的数据）
     */
    private String studentPassword;

    /**
     * 姓名
     */
    private String studentName;

    /**
     * 性别（0男 1女）
     */
    private String studentSex;

    /**
     * 年级（对应字典值）
     */
    private String studentGrade;

    /**
     * 来源（对应字典值）
     */
    private String studentSource;

    /**
     * 家长电话
     */
    private String studentParentPhone;

    /**
     * 备用电话（家长电话2）
     */
    private String studentBackupPhone;

    /**
     * 会员顾问记录id
     */
    private Long studentConsultantRecordId;

    /**
     * 会员绑定家长记录id
     */
    private Long studentParentRecordId;

    /**
     * 会员AI课程分配记录表id
     */
    private Long studentAiCourseRecordId;

    /**
     * 会员账号类型(0正常  1演示)
     */
    private Integer studentAccountType;

    /**
     * 会员备注
     */
    private String studentRemark;

    /**
     * 最后登录时间（最近登录时间）
     */
    private Date lastLoginTime;

    /**
     * 会员所拥有的产品中，最大的那个过期时间（已经向下取整）
     */
    private Date expireTime;

    /**
     * 优惠额度
     */
    private BigDecimal preferentialAmount;

    /**
     * 优惠额度版本
     */
    private Integer preferentialAmountVersion;

    /**
     * 是否已经购买正式卡
     */
    private Integer purchasedCardFlag;

}
