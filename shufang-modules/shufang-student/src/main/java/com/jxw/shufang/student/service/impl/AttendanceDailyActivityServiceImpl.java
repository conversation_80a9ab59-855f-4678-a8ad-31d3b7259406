package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxw.shufang.common.core.constant.EventTimeSourceConstant;
import com.jxw.shufang.common.core.constant.FeedbackStatusConstant;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AttendanceDailyActivity;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityBo;
import com.jxw.shufang.student.domain.bo.AttendanceDailyActivityFeedbackRecordBo;
import com.jxw.shufang.student.domain.vo.AttendanceDailyActivityVo;
import com.jxw.shufang.student.enums.PublishStatusConstant;
import com.jxw.shufang.student.mapper.AttendanceDailyActivityMapper;
import com.jxw.shufang.student.service.AttendanceDailyActivityFeedbackRecordService;
import com.jxw.shufang.student.service.AttendanceDailyActivityService;
import com.jxw.shufang.student.service.IStudentConsultantRecordService;
import com.jxw.shufang.student.service.IStudentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员每日打卡登录情况表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04 11:57:25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AttendanceDailyActivityServiceImpl extends ServiceImpl<AttendanceDailyActivityMapper, AttendanceDailyActivity> implements AttendanceDailyActivityService {

    private final AttendanceDailyActivityMapper baseMapper;
    private final IStudentConsultantRecordService studentConsultantRecordService;
    private final IStudentService studentService;
    private final AttendanceDailyActivityFeedbackRecordService attendanceDailyActivityFeedbackRecordService;
    @DubboReference
    private RemoteStaffService remoteStaffService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAttendanceDailyActivity(AttendanceDailyActivityBo bo) {
        log.info("saveAttendanceDailyActivity 的请求参数：{}", bo);
        Long studentId = bo.getStudentId();
        Date recordDate = bo.getRecordDate();

        AttendanceDailyActivityVo attendanceDailyActivityVo = this.queryAttendanceDailyActivityByStudentIdAndDate(studentId, recordDate);
        if (attendanceDailyActivityVo != null) {
            log.info("saveAttendanceDailyActivity 会员已有到店记录，不再重复记录，{}", attendanceDailyActivityVo);
            return;
        }
        AttendanceDailyActivity convert = MapstructUtils.convert(bo, AttendanceDailyActivity.class);
        if (convert == null) {
            return;
        }
        // 当日到店记录已经存在了，那么就不再更新
        boolean save = this.save(convert);
        if (save) {
            AttendanceDailyActivityFeedbackRecordBo attendanceDailyActivityFeedbackRecordBo = new AttendanceDailyActivityFeedbackRecordBo();
            attendanceDailyActivityFeedbackRecordBo.setAttendanceDailyActivityId(convert.getAttendanceDailyActivityId());
            attendanceDailyActivityFeedbackRecordBo.setFeedbackStatus(FeedbackStatusConstant.NOT_FEEDBACK);
            attendanceDailyActivityFeedbackRecordBo.setPublishStatus(PublishStatusConstant.NOT_PUBLISHED);
            attendanceDailyActivityFeedbackRecordBo.setCreateBy(convert.getCreateBy());
            attendanceDailyActivityFeedbackRecordBo.setCreateTime(convert.getCreateTime());
            attendanceDailyActivityFeedbackRecordBo.setUpdateTime(convert.getUpdateTime());
            attendanceDailyActivityFeedbackRecordService.saveAttendanceDailyActivityFeedbackRecord(attendanceDailyActivityFeedbackRecordBo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAttendanceDailyActivity(List<AttendanceDailyActivityBo> boList) {
        if (CollUtil.isEmpty(boList)) {
            return;
        }
        log.info("saveBatchAttendanceDailyActivity 的请求参数：{}", boList);
        // 提取 studentId 和 recordDate 的键，用于一次性查询数据库中已有的记录
        Set<Long> studentIds = boList.stream()
            .map(AttendanceDailyActivityBo::getStudentId)
            .collect(Collectors.toSet());
        Set<Date> recordDates = boList.stream()
            .map(AttendanceDailyActivityBo::getRecordDate)
            .collect(Collectors.toSet());


        List<AttendanceDailyActivityVo> existingRecords = this.queryAttendanceDailyActivitiesByStudentIdsAndDateList(studentIds, recordDates);
        // 用 studentId + recordDate 拼接成 key。判断是否又已经存在的记录
        Set<String> existingKeys = existingRecords.stream().map(vo -> vo.getStudentId() + "_" + DateUtils.dateTime(vo.getRecordDate()))
            .collect(Collectors.toSet());

        List<AttendanceDailyActivity> attendanceDailyActivitySaveList = new ArrayList<>();
        List<AttendanceDailyActivityFeedbackRecordBo> feedbackList = new ArrayList<>();

        for (AttendanceDailyActivityBo bo : boList) {
            String key = bo.getStudentId() + "_" + DateUtils.dateTime(bo.getRecordDate());
            if (existingKeys.contains(key)) {
                log.info("会员已有到店记录，不再重复记录，key={}", key);
                continue;
            }
            AttendanceDailyActivity convert = MapstructUtils.convert(bo, AttendanceDailyActivity.class);
            if (convert == null) {
                continue;
            }
            // 添加会员每日打卡登录情况 对象
            attendanceDailyActivitySaveList.add(convert);

            // 添加会员每日打卡登录情况反馈记录对象
            AttendanceDailyActivityFeedbackRecordBo feedback = new AttendanceDailyActivityFeedbackRecordBo();
            feedback.setFeedbackStatus(FeedbackStatusConstant.NOT_FEEDBACK);
            feedback.setPublishStatus(PublishStatusConstant.NOT_PUBLISHED);
            feedback.setCreateBy(convert.getCreateBy());
            feedback.setCreateTime(convert.getCreateTime());
            feedback.setUpdateTime(convert.getUpdateTime());

            feedbackList.add(feedback);
        }

        // 批量保存主表记录
        if (CollUtil.isNotEmpty(attendanceDailyActivitySaveList)) {
            baseMapper.insertBatch(attendanceDailyActivitySaveList);
            // 批量保存反馈记录
            for (int i = 0; i < attendanceDailyActivitySaveList.size(); i++) {
                feedbackList.get(i).setAttendanceDailyActivityId(attendanceDailyActivitySaveList.get(i).getAttendanceDailyActivityId());
            }
            // 批量保存反馈记录
            attendanceDailyActivityFeedbackRecordService.saveBatchAttendanceDailyActivityFeedbackRecord(feedbackList);
        }
    }


    @Override
    public TableDataInfo<AttendanceDailyActivityVo> queryNotFeedbackStudentList(AttendanceDailyActivityBo bo, PageQuery pageQuery) {
        // 根据登陆人的不同查询展示不同的待反馈学员
        handleQueryParam(bo);
        // 构建查询条件
        QueryWrapper<AttendanceDailyActivity> wrapper = buildQueryWrapper(bo);
        wrapper.in("adafr.feedback_status", List.of(FeedbackStatusConstant.NOT_FEEDBACK, FeedbackStatusConstant.OVERTIME_NOT_FEEDBACK));
        Page<AttendanceDailyActivityVo> page = baseMapper.selectNotFeedbackStudentList(pageQuery.build(), wrapper);
        List<AttendanceDailyActivityVo> records = page.getRecords();
        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())) {
            putConsultantInfo(records);
        }

        return TableDataInfo.build(page);
    }

    @Override
    public AttendanceDailyActivityVo queryAttendanceDailyActivityByStudentIdAndDate(Long studentId, Date feedbackStartDate) {
        log.info("【queryAttendanceDailyActivityByStudentIdAndDate 的请求参数】 学生ID：{}, 反馈日期：{}", studentId, feedbackStartDate);

        return baseMapper.selectVoOne(new LambdaQueryWrapper<AttendanceDailyActivity>()
            .eq(AttendanceDailyActivity::getStudentId, studentId)
            .eq(AttendanceDailyActivity::getRecordDate, DateUtils.dateTime(feedbackStartDate)));
    }

    @Override
    public List<AttendanceDailyActivityVo> queryAttendanceDailyActivitiesByStudentIdsAndDateList(Set<Long> studentId, Set<Date> feedbackStartDateList) {
        Set<String> dateList = feedbackStartDateList.stream().map(DateUtils::dateTime).collect(Collectors.toSet());
        log.info("【queryAttendanceDailyActivitiesByStudentIdsAndDateList 的请求参数】 学生ID：{}, 反馈日期：{}", studentId, dateList);
        return baseMapper.selectVoList(new LambdaQueryWrapper<AttendanceDailyActivity>().in(AttendanceDailyActivity::getStudentId, studentId).in(AttendanceDailyActivity::getRecordDate, dateList));
    }

    @Override
    public List<AttendanceDailyActivityVo> queryListByFeedbackStatus(Integer feedbackStatus) {
        return baseMapper.selectListByFeedbackStatus(feedbackStatus);
    }

    private void handleQueryParam(AttendanceDailyActivityBo record) {
        if (null != record.getStudentId()) {
            return;
        }

        // 如果是超级管理员或租户管理员，不进行分支权限控制
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return;
        }

        //判断是不是门店
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            record.setBranchIdList(LoginHelper.getBranchIdList());
        } else if (null != LoginHelper.getBranchId()) {
            record.setBranchId(LoginHelper.getBranchId());
        } else {
            record.setStudentIdList(List.of(-1L));
        }
    }

    private QueryWrapper<AttendanceDailyActivity> buildQueryWrapper(AttendanceDailyActivityBo bo) {

        QueryWrapper<AttendanceDailyActivity> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "a.student_id", bo.getStudentId());
        lqw.eq(bo.getFeedbackStatus() != null, "adafr.feedback_status", bo.getFeedbackStatus());
        lqw.eq(bo.getRecordDate() != null, "a.record_date", bo.getRecordDate() != null ? DateUtils.dateTime(bo.getRecordDate()) : null);
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "a.student_id", bo.getStudentIdList());
        lqw.apply(StringUtils.isNotBlank(bo.getNameWithPhone()), "concat(s.student_name,'(',right(s.student_account,4),')') like concat('%',{0},'%')", bo.getNameWithPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getStudentAccount()), "s.student_account", bo.getStudentAccount());
        lqw.eq(bo.getCreateBy() != null, "a.create_by", bo.getCreateBy());
        if (bo.getFeedbackStartDate() != null && bo.getFeedbackEndDate() != null) {
            lqw.between("a.record_date", DateUtils.dateTime(bo.getFeedbackStartDate()), DateUtils.dateTime(bo.getFeedbackEndDate()));
        }
        // 门店集合不为空 查询门店下所有学生
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), "s.branch_id", bo.getBranchIdList());
        lqw.eq(bo.getBranchId() != null, "s.branch_id", bo.getBranchId());
        //查顾问
        if (bo.getConsultantId() != null) {
            RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
            remoteStaffBo.setBranchStaffId(bo.getConsultantId());
            List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
            if (CollUtil.isNotEmpty(remoteStaffVos)) {
                List<Long> consultantIds = remoteStaffVos.stream().map(RemoteStaffVo::getBranchStaffId).collect(Collectors.toList());
                Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(consultantIds);
                if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
                    lqw.in("a.student_id", staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList());
                } else {
                    lqw.in("a.student_id", -1);
                }
            } else {
                lqw.in("a.student_id", -1);
            }
        }
        lqw.orderByDesc("a.create_time");
        return lqw;
    }


    private void putConsultantInfo(List<AttendanceDailyActivityVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(AttendanceDailyActivityVo::getStudentId).filter(Objects::nonNull).distinct().toList();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Map<Long, Long> studentConsultantIdMap = studentConsultantRecordService.getStudentConsultantIdMap(list);
        if (CollUtil.isEmpty(studentConsultantIdMap)) {
            return;
        }
        List<Long> consultantIdList = studentConsultantIdMap.values().stream().distinct().toList();
        if (CollUtil.isEmpty(consultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(consultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        if (CollUtil.isEmpty(remoteStaffVos)) {
            return;
        }
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        records.forEach(item -> item.setConsultantInfo(remoteStaffVoMap.get(studentConsultantIdMap.get(item.getStudentId()))));
    }
}
