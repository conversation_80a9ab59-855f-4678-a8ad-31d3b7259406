package com.jxw.shufang.student.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.EventTimeSourceConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.domain.AttendanceLogStudentEzkeco;
import com.jxw.shufang.student.domain.StudentParentRecord;
import com.jxw.shufang.student.domain.bo.AttendanceLogStudentEzkecoBo;
import com.jxw.shufang.student.domain.bo.StudentBo;
import com.jxw.shufang.student.domain.bo.StudyPlanningBo;
import com.jxw.shufang.student.domain.vo.*;
import com.jxw.shufang.student.mapper.AttendanceLogStudentEzkecoMapper;
import com.jxw.shufang.student.service.*;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ezkeco学员考勤记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
@Slf4j
public class AttendanceLogStudentEzkecoServiceImpl implements IAttendanceLogStudentEzkecoService, BaseService {

    private final AttendanceLogStudentEzkecoMapper baseMapper;

    private final IStudentService studentService;

    private final IStudentParentRecordService studentParentRecordService;

    private final IStudentConsultantRecordService studentConsultantRecordService;

    private final IStudyPlanningService studyPlanningService;

    private final IStudyPlanningRecordService studyPlanningRecordService;

    private final IStudyVideoRecordService studyVideoRecordService;

    @DubboReference
    private RemoteWxService remoteWxService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    private final RocketMQTemplate rocketMQTemplate;

    /**
     * 查询ezkeco学员考勤记录
     */
    @Override
    public AttendanceLogStudentEzkecoVo queryById(Long attendanceLogStudentEzkecoId) {
        return baseMapper.selectVoById(attendanceLogStudentEzkecoId);
    }

    /**
     * 查询ezkeco学员考勤记录列表
     */
    @Override
    public TableDataInfo<AttendanceLogStudentEzkecoVo> queryPageList(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<AttendanceLogStudentEzkeco> lqw = buildQueryWrapper(bo);
        Page<AttendanceLogStudentEzkecoVo> result = baseMapper.queryPageList(pageQuery.build(), lqw);

        if (Boolean.TRUE.equals(bo.getWithConsultantInfo())){
            putConsultantInfo(result.getRecords());
        }

        return TableDataInfo.build(result);
    }

    @Override
    public TableDataInfo<AttendanceLogStudentEzkecoVo> queryPageRecord(AttendanceLogStudentEzkecoBo bo, PageQuery pageQuery) {
        QueryWrapper<AttendanceLogStudentEzkeco> lqw = buildQueryWrapper(bo);
        Page<AttendanceLogStudentEzkecoVo> result = baseMapper.queryPageList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }


    /**
     * 查询ezkeco学员考勤记录列表
     */
    @Override
    public List<AttendanceLogStudentEzkecoVo> queryList(AttendanceLogStudentEzkecoBo bo) {
        LambdaQueryWrapper<AttendanceLogStudentEzkeco> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AttendanceLogStudentEzkeco> buildLambdaQueryWrapper(AttendanceLogStudentEzkecoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AttendanceLogStudentEzkeco> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getVerify()), AttendanceLogStudentEzkeco::getVerify, bo.getVerify());
        lqw.eq(bo.getChecktime() != null, AttendanceLogStudentEzkeco::getChecktime, bo.getChecktime());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), AttendanceLogStudentEzkeco::getSn, bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getAlias()), AttendanceLogStudentEzkeco::getAlias, bo.getAlias());
        lqw.eq(StringUtils.isNotBlank(bo.getPin()), AttendanceLogStudentEzkeco::getPin, bo.getPin());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), AttendanceLogStudentEzkeco::getState, bo.getState());
        lqw.eq(bo.getAttendanceUserId() != null, AttendanceLogStudentEzkeco::getAttendanceUserId, bo.getAttendanceUserId());
        lqw.eq(bo.getUserId() != null, AttendanceLogStudentEzkeco::getUserId, bo.getUserId());
        lqw.eq(bo.getStudentId() != null, AttendanceLogStudentEzkeco::getStudentId, bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), AttendanceLogStudentEzkeco::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotograph()), AttendanceLogStudentEzkeco::getPhotograph, bo.getPhotograph());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyParents()), AttendanceLogStudentEzkeco::getNotifyParents, bo.getNotifyParents());
        lqw.eq(StringUtils.isNotBlank(bo.getBindParents()), AttendanceLogStudentEzkeco::getBindParents, bo.getBindParents());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), AttendanceLogStudentEzkeco::getStudentId, bo.getStudentIdList());

        if (StringUtils.isNotBlank(bo.getStudentAccount())) {
            lqw.apply("s.student_account like concat('%',{0},'%')", bo.getStudentAccount());
        }
        if (StringUtils.isNotBlank(bo.getStudentName())) {
            lqw.apply("s.student_name like concat('%',{0},'%')", bo.getStudentName());
        }

        if (StringUtils.isNotBlank(bo.getRangeStartDate()) && StringUtils.isNotBlank(bo.getRangeEndDate())){
            if (bo.getRangeStartDate().contains(":")){
                lqw.between(AttendanceLogStudentEzkeco::getChecktime, DateUtils.parseDate(bo.getRangeStartDate()), DateUtils.parseDate(bo.getRangeEndDate()));
            }else {
                lqw.between(AttendanceLogStudentEzkeco::getChecktime, DateUtils.parseDate(bo.getRangeStartDate() + " 00:00:00"), DateUtils.parseDate(bo.getRangeEndDate() + " 23:59:59"));
            }
        }
        //查顾问
        if (StringUtils.isNotBlank(bo.getConsultantName()) || bo.getConsultantId() != null) {
            RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
            remoteStaffBo.setName(bo.getConsultantName());
            remoteStaffBo.setBranchStaffId(bo.getConsultantId());
            List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
            if (CollUtil.isNotEmpty(remoteStaffVos)) {
                List<Long> consultantIds = remoteStaffVos.stream().map(RemoteStaffVo::getBranchStaffId).collect(Collectors.toList());
                Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(consultantIds);
                if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
                    lqw.in(AttendanceLogStudentEzkeco::getStudentId, staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList());
                } else {
                    lqw.in(AttendanceLogStudentEzkeco::getStudentId, -1);
                }
            } else {
                lqw.in(AttendanceLogStudentEzkeco::getStudentId, -1);
            }
        }

        return lqw;
    }


    private QueryWrapper<AttendanceLogStudentEzkeco> buildQueryWrapper(AttendanceLogStudentEzkecoBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<AttendanceLogStudentEzkeco> lqw = Wrappers.query();
        lqw.eq(StringUtils.isNotBlank(bo.getVerify()), "t.verify", bo.getVerify());
        lqw.eq(bo.getChecktime() != null, "t.checktime", bo.getChecktime());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), "t.sn", bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getAlias()), "t.alias", bo.getAlias());
        lqw.eq(StringUtils.isNotBlank(bo.getPin()), "t.pin", bo.getPin());
        lqw.eq(StringUtils.isNotBlank(bo.getState()), "t.state", bo.getState());
        lqw.eq(bo.getAttendanceUserId() != null, "t.attendance_user_id", bo.getAttendanceUserId());
        lqw.eq(bo.getUserId() != null, "t.user_id", bo.getUserId());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), "t.nick_name", bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getPhotograph()), "t.photograph", bo.getPhotograph());
        lqw.eq(StringUtils.isNotBlank(bo.getNotifyParents()), "t.notify_parents", bo.getNotifyParents());
        lqw.eq(StringUtils.isNotBlank(bo.getBindParents()), "t.bind_parents", bo.getBindParents());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.like(StringUtils.isNotBlank(bo.getStudentAccount()), "s.student_account", bo.getStudentAccount());
        lqw.like(StringUtils.isNotBlank(bo.getStudentName()), "s.student_name", bo.getStudentName());

        if (StringUtils.isNotBlank(bo.getRangeStartDate()) && StringUtils.isNotBlank(bo.getRangeEndDate())){
            if (bo.getRangeStartDate().contains(":")){
                lqw.between("t.checktime", DateUtils.parseDate(bo.getRangeStartDate()), DateUtils.parseDate(bo.getRangeEndDate()));
            }else {
                lqw.between("t.checktime", DateUtils.parseDate(bo.getRangeStartDate() + " 00:00:00"), DateUtils.parseDate(bo.getRangeEndDate() + " 23:59:59"));
            }
        }

        //查顾问
        if (StringUtils.isNotBlank(bo.getConsultantName()) || bo.getConsultantId() != null) {
            RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
            remoteStaffBo.setName(bo.getConsultantName());
            remoteStaffBo.setBranchStaffId(bo.getConsultantId());
            List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
            if (CollUtil.isNotEmpty(remoteStaffVos)) {
                List<Long> consultantIds = remoteStaffVos.stream().map(RemoteStaffVo::getBranchStaffId).collect(Collectors.toList());
                Map<Long, List<Long>> staffResponsibleStudentIdMap = studentConsultantRecordService.getStaffResponsibleStudentIdMap(consultantIds);
                if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
                    lqw.in("t.student_id", staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList());
                } else {
                    lqw.in("t.student_id", -1);
                }
            } else {
                lqw.in("t.student_id", -1);
            }
        }

        return lqw;
    }

    /**
     * 新增ezkeco学员考勤记录
     */
    @Override
    public Boolean insertByBo(AttendanceLogStudentEzkecoBo bo) {
        AttendanceLogStudentEzkeco add = MapstructUtils.convert(bo, AttendanceLogStudentEzkeco.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAttendanceLogStudentEzkecoId(add.getAttendanceLogStudentEzkecoId());
        }

        return flag;
    }

    /**
     * 修改ezkeco学员考勤记录
     */
    @Override
    public Boolean updateByBo(AttendanceLogStudentEzkecoBo bo) {
        AttendanceLogStudentEzkeco update = MapstructUtils.convert(bo, AttendanceLogStudentEzkeco.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AttendanceLogStudentEzkeco entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除ezkeco学员考勤记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量插入
     *
     * @param list
     */
    @Override
    public void insertBatch(Collection<AttendanceLogStudentEzkecoBo> list) {

        for (AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo : list) {
            if (StringUtils.isBlank(attendanceLogStudentEzkecoBo.getNotifyParents())) {
                attendanceLogStudentEzkecoBo.setNotifyParents(UserConstants.NO);
            }
        }
        List<AttendanceLogStudentEzkecoBo> insertList = ListUtil.toList(list);
        List<AttendanceLogStudentEzkeco> studentEzkecoList = MapstructUtils.convert(insertList, AttendanceLogStudentEzkeco.class);
        boolean b = baseMapper.insertBatch(studentEzkecoList);
        if (!b) {
            throw new ServiceException("批量插入失败");
        }
        try {
            notifyParent(studentEzkecoList);
        } catch (Exception e) {
            log.error("通知家长失败", e);
        }
        JSONArray array =new JSONArray();
        // 设备打卡时间 为实际到店时间
        list.forEach(bo -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("studentId", bo.getStudentId());
            jsonObject.put("recordDate", bo.getChecktime());
            jsonObject.put("eventTime", bo.getChecktime());
            jsonObject.put("eventTimeSource", EventTimeSourceConstant.DEVICE_LOGIN);
            jsonObject.put("createBy", bo.getStudentId());
            array.add(jsonObject);
        });
        rocketMQTemplate.convertAndSend(MqTopicConstant.ATTENDANCE_DAILY_ACTIVITY_TOPIC, array.toJSONString());
    }

    private void notifyParent(List<AttendanceLogStudentEzkeco> list) {
        List<AttendanceLogStudentEzkecoBo> updateList = new ArrayList<>();
        List<Long> studentIdList = list.stream().map(AttendanceLogStudentEzkeco::getStudentId).toList();
        StudentBo studentBo = new StudentBo();
        studentBo.setStudentIds(studentIdList);
        List<StudentVo> studentVos = studentService.queryList(studentBo);
        List<Long> studentParentRecordIdList = studentVos.stream().map(StudentVo::getStudentParentRecordId).filter(Objects::nonNull).toList();

        List<StudentParentRecord> studentParentRecordList = studentParentRecordService.queryStudentParentRecordByIdList(studentParentRecordIdList);

        Map<Long, StudentVo> studentVoMap = StreamUtils.toIdentityMap(studentVos, StudentVo::getStudentId);
        Map<Long, StudentParentRecord> studentParentRecordMap = StreamUtils.toIdentityMap(studentParentRecordList, StudentParentRecord::getStudentParentRecordId);

        for (AttendanceLogStudentEzkeco attendanceLogStudentEzkeco : list) {
            Long studentId = attendanceLogStudentEzkeco.getStudentId();
            StudentVo studentVo = studentVoMap.get(studentId);
            if (studentVo == null || studentVo.getStudentParentRecordId() == null) {
                continue;
            }
            StudentParentRecord studentParentRecord = studentParentRecordMap.get(studentVo.getStudentParentRecordId());
            if (studentParentRecord == null || StringUtils.isBlank(studentParentRecord.getParentWechatOpenId())) {
                continue;
            }
            AttendanceLogStudentEzkecoBo updateBo = new AttendanceLogStudentEzkecoBo();
            updateBo.setAttendanceLogStudentEzkecoId(attendanceLogStudentEzkeco.getAttendanceLogStudentEzkecoId());
            String parentWechatOpenId = studentParentRecord.getParentWechatOpenId();
            String message = "您好，您的孩子:" + studentVo.getStudentName() + "于 " + DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, attendanceLogStudentEzkeco.getChecktime()) + " 考勤成功\n点击订阅下一次考勤信息";
            try {
                Boolean b = remoteWxService.sendAttendanceMessage(parentWechatOpenId, "考勤通知", message, "#999999");
                if (b) {
                    updateBo.setNotifyParents(UserConstants.YES);
                } else {
                    updateBo.setRemark("通知家长失败");
                }
            } catch (Exception e) {
                log.error("通知家长失败", e);
                updateBo.setRemark(StringUtils.substring(e.getMessage(), 0, 500));
            }
            updateList.add(updateBo);
        }

        if (!updateList.isEmpty()) {
            baseMapper.updateBatchById(MapstructUtils.convert(updateList, AttendanceLogStudentEzkeco.class));
        }

    }

    @Override
    public Boolean exist(AttendanceLogStudentEzkecoBo attendanceLogStudentEzkecoBo) {
        LambdaQueryWrapper<AttendanceLogStudentEzkeco> queryWrapper = buildLambdaQueryWrapper(attendanceLogStudentEzkecoBo);
        return baseMapper.exists(queryWrapper);
    }

    @Override
    public StudentAttendanceStatisticsVo statistics(Long studentId) {
        StudentAttendanceStatisticsVo studentAttendanceStatisticsVo = new StudentAttendanceStatisticsVo();

        List<String> attendanceDateList = baseMapper.getAllAttendanceDate(studentId);
        studentAttendanceStatisticsVo.setTotalCheckTimes(attendanceDateList.size());

        //查所有学习规划主体
        StudyPlanningBo studyPlanningBo = new StudyPlanningBo();
        studyPlanningBo.setStudentId(studentId);
        studyPlanningBo.setLeStudyPlanningDate(DateUtils.dateTime(new Date()));
        studyPlanningBo.setWithStudyRecordInfo(Boolean.TRUE);
        List<StudyPlanningVo> studyPlanningVos = studyPlanningService.queryPlanAndRecordList(studyPlanningBo);

        if (CollUtil.isEmpty(studyPlanningVos)){
            return studentAttendanceStatisticsVo;
        }


        int  missingCheckTimes = 0;
        int  hasRecordButNoCheckTimes = 0;

        for (StudyPlanningVo studyPlanningVo : studyPlanningVos) {
            Date studyPlanningDate = studyPlanningVo.getStudyPlanningDate();
            String studyPlanningDateStr = DateUtils.dateTime(studyPlanningDate);
            if (!attendanceDateList.contains(studyPlanningDateStr)){
                missingCheckTimes++;
            }
            List<StudyPlanningRecordVo> studyPlanningRecordList = studyPlanningVo.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)){
                continue;
            }
            for (StudyPlanningRecordVo studyPlanningRecordVo : studyPlanningRecordList) {
                StudyRecordVo studyRecord = studyPlanningRecordVo.getStudyRecord();
                if (studyRecord!= null &&
                    (Objects.nonNull(studyRecord.getStudyVideoTotalDuration()) && studyRecord.getStudyVideoTotalDuration() > 0)&&
                    !attendanceDateList.contains(studyPlanningDateStr)){
                    hasRecordButNoCheckTimes++;
                    break;
                }
            }
        }
        studentAttendanceStatisticsVo.setMissingCheckTimes(missingCheckTimes);
        studentAttendanceStatisticsVo.setNoCheckButStudyTimes(hasRecordButNoCheckTimes);
         return studentAttendanceStatisticsVo;
    }

    @Override
    public List<Long> getExistAttendanceLogStuIdList(List<Long> studentIdList, String attendanceDate) {
        QueryWrapper<AttendanceLogStudentEzkeco> query = Wrappers.query();
        query.in("student_id", studentIdList);
        query.eq("date(checktime)", attendanceDate);
        query.select("student_id");
        List<AttendanceLogStudentEzkeco> attendanceLogStudentEzkecos = baseMapper.selectList(query);
        return attendanceLogStudentEzkecos.stream().map(AttendanceLogStudentEzkeco::getStudentId).distinct().toList();
    }

    @Override
    public Map<Long, Integer> getStudentAttendanceDayNumsMap(List<Long> studentIds, Date rangeStartTime,
        Date rangeEndTime) {
        List<StudentAttendanceCountVo> studentAttendanceCountVos =
            baseMapper.countCheckDaysByStudentIds(studentIds, rangeStartTime, rangeEndTime);
        return CollUtil.isEmpty(studentAttendanceCountVos) ? new HashMap<>()
            : studentAttendanceCountVos.stream().collect(
                Collectors.toMap(StudentAttendanceCountVo::getStudentId, record -> record.getCheckDays().intValue()));
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void handleQueryParam(AttendanceLogStudentEzkecoBo record) {
        if (record.getStudentId() != null) {
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null) {
            List<Long> staffResponsibleStudentIdList = studentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId());
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = studentService.getStudentIdListByBranchId(LoginHelper.getBranchId());
                if (CollUtil.isEmpty(record.getStudentIdList())) {
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                } else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }

        }
    }

    //会员顾问记录详情
    private void putConsultantInfo(List<AttendanceLogStudentEzkecoVo> attendanceLogStudentEzkecoVoList) {
        if (CollUtil.isEmpty(attendanceLogStudentEzkecoVoList)){
            return;
        }
        List<StudentVo> studentVos = attendanceLogStudentEzkecoVoList.stream().map(AttendanceLogStudentEzkecoVo::getStudent).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(studentVos)) {
            return;
        }
        List<Long> studentConsultantRecordId = studentVos.stream().map(StudentVo::getStudentConsultantRecordId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantRecordId)) {
            return;
        }
        List<StudentConsultantRecordVo> studentConsultantRecordVos = studentConsultantRecordService.queryByIdList(studentConsultantRecordId);
        if (CollUtil.isEmpty(studentConsultantRecordVos)) {
            return;
        }
        List<Long> studentConsultantIdList = studentConsultantRecordVos.stream().map(StudentConsultantRecordVo::getStudentConsultantId).collect(Collectors.toList());
        if (CollUtil.isEmpty(studentConsultantIdList)) {
            return;
        }
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setBranchStaffIds(studentConsultantIdList);
        remoteStaffBo.setWithSysUserInfo(Boolean.TRUE);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(remoteStaffBo);
        Map<Long, RemoteStaffVo> remoteStaffVoMap = remoteStaffVos.stream().collect(Collectors.toMap(RemoteStaffVo::getBranchStaffId, vo -> vo));
        studentVos.forEach(studentVo -> {
            StudentConsultantRecordVo studentConsultantRecordVo = studentConsultantRecordVos.stream().filter(vo -> vo.getStudentConsultantRecordId().equals(studentVo.getStudentConsultantRecordId())).findFirst().orElse(null);
            if (studentConsultantRecordVo != null) {
                RemoteStaffVo remoteStaffVo = remoteStaffVoMap.get(studentConsultantRecordVo.getStudentConsultantId());
                studentVo.setConsultant(remoteStaffVo);
            }
        });

    }

}
