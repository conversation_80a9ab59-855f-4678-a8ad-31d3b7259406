package com.jxw.shufang.student.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.student.domain.bo.AttendanceLogStaffEzkecoBo;
import com.jxw.shufang.student.domain.vo.AttendanceLogStaffEzkecoVo;

import java.util.Collection;
import java.util.List;

/**
 * ezkeco员工考勤记录Service接口
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
public interface IAttendanceLogStaffEzkecoService {

    /**
     * 查询ezkeco员工考勤记录
     */
    AttendanceLogStaffEzkecoVo queryById(Long attendanceLogStaffEzkecoId);

    /**
     * 查询ezkeco员工考勤记录列表
     */
    TableDataInfo<AttendanceLogStaffEzkecoVo> queryPageList(AttendanceLogStaffEzkecoBo bo, PageQuery pageQuery);

    /**
     * 查询ezkeco员工考勤记录列表
     */
    List<AttendanceLogStaffEzkecoVo> queryList(AttendanceLogStaffEzkecoBo bo);

    /**
     * 新增ezkeco员工考勤记录
     */
    Boolean insertByBo(AttendanceLogStaffEzkecoBo bo);

    /**
     * 修改ezkeco员工考勤记录
     */
    Boolean updateByBo(AttendanceLogStaffEzkecoBo bo);

    /**
     * 校验并批量删除ezkeco员工考勤记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入
     * @param list
     */
    void insertBatch(Collection<AttendanceLogStaffEzkecoBo> list);

    Boolean exist(AttendanceLogStaffEzkecoBo attendanceLogStaffEzkecoBo);
}
