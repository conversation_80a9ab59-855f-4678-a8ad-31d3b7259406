package com.jxw.shufang.branch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.IntegralOrderInfo;
import com.jxw.shufang.branch.domain.bo.IntegralOrderInfoBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderInfoVo;
import com.jxw.shufang.branch.mapper.IntegralOrderInfoMapper;
import com.jxw.shufang.branch.service.IIntegralOrderInfoService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 积分订单详情Service业务层处理
 *
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class IntegralOrderInfoServiceImpl implements IIntegralOrderInfoService {

    private final IntegralOrderInfoMapper baseMapper;

    /**
     * 查询积分订单详情
     */
    @Override
    public IntegralOrderInfoVo queryById(Long integralOrderInfoId){
        return baseMapper.selectVoById(integralOrderInfoId);
    }

    /**
     * 查询积分订单详情列表
     */
    @Override
    public TableDataInfo<IntegralOrderInfoVo> queryPageList(IntegralOrderInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IntegralOrderInfo> lqw = buildQueryWrapper(bo);
        Page<IntegralOrderInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分订单详情列表
     */
    @Override
    public List<IntegralOrderInfoVo> queryList(IntegralOrderInfoBo bo) {
        LambdaQueryWrapper<IntegralOrderInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IntegralOrderInfo> buildQueryWrapper(IntegralOrderInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IntegralOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getIntegralOrderId() != null, IntegralOrderInfo::getIntegralOrderId, bo.getIntegralOrderId());
        lqw.eq(bo.getIntegralGoodId() != null, IntegralOrderInfo::getIntegralGoodId, bo.getIntegralGoodId());
        lqw.eq(bo.getIntegralGoodImg() != null, IntegralOrderInfo::getIntegralGoodImg, bo.getIntegralGoodImg());
        lqw.like(StringUtils.isNotBlank(bo.getIntegralGoodName()), IntegralOrderInfo::getIntegralGoodName, bo.getIntegralGoodName());
        lqw.eq(bo.getIntegralGoodCost() != null, IntegralOrderInfo::getIntegralGoodCost, bo.getIntegralGoodCost());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralGoodType()), IntegralOrderInfo::getIntegralGoodType, bo.getIntegralGoodType());
        return lqw;
    }

    /**
     * 新增积分订单详情
     */
    @Override
    public Boolean insertByBo(IntegralOrderInfoBo bo) {
        IntegralOrderInfo add = MapstructUtils.convert(bo, IntegralOrderInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralOrderInfoId(add.getIntegralOrderInfoId());
        }
        return flag;
    }

    /**
     * 修改积分订单详情
     */
    @Override
    public Boolean updateByBo(IntegralOrderInfoBo bo) {
        IntegralOrderInfo update = MapstructUtils.convert(bo, IntegralOrderInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralOrderInfo entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除积分订单详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public IntegralOrderInfoVo queryByOrderId(Long integralOrderId) {
        LambdaQueryWrapper<IntegralOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(IntegralOrderInfo::getIntegralOrderId, integralOrderId);
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }
}
