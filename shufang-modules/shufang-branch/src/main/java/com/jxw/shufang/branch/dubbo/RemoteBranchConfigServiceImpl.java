package com.jxw.shufang.branch.dubbo;

import com.jxw.shufang.branch.api.RemoteBranchConfigService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchConfigBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchConfigVo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchPayModelConfigVo;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.handler.BranchConfigJsonHandler;
import com.jxw.shufang.branch.service.IBranchConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBranchConfigServiceImpl implements RemoteBranchConfigService {

    private final IBranchConfigService branchConfigService;

    @Override
    public RemoteBranchConfigVo remoteGetPreferentialAmountConfig(RemoteBranchConfigBo bo) {
        bo.setConfigType(BranchConfigTypeEnum.INTRODUCE_PREFERENTIAL_AMOUNT.getConfigType());
        BranchConfigVo voByBranchId = branchConfigService.getBranchConfigByBo(new BranchConfigBo(bo.getBranchId(), bo.getConfigType()));
        if (null == voByBranchId) {
            // 未配置
            return null;
        }
        RemoteBranchConfigVo remoteBranchBo = new RemoteBranchConfigVo(voByBranchId.getBranchConfigId(), voByBranchId.getBranchId(), voByBranchId.getConfigType());
        remoteBranchBo.setProducePreferentialConfigList(
            BranchConfigJsonHandler.parseConfigJsonList(voByBranchId.getConfigJson(), RemoteBranchConfigVo.ProducePreferentialConfig.class)
        );
        return remoteBranchBo;
    }

    @Override
    public RemoteBranchPayModelConfigVo remoteGetPayModelConfig(Long branchId) {
        Integer paymentFeeModeConfigType = BranchConfigTypeEnum.PAYMENT_FEE_MODE.getConfigType();
        BranchConfigVo branchConfigVo = branchConfigService.getBranchConfigByBo(new BranchConfigBo(branchId,paymentFeeModeConfigType));
        if (null == branchConfigVo) {
            return null;
        }
        RemoteBranchPayModelConfigVo result = new RemoteBranchPayModelConfigVo();
        result.setBranchConfigId(branchConfigVo.getBranchConfigId());
        result.setBranchId(branchConfigVo.getBranchId());
        result.setConfigType(branchConfigVo.getConfigType());
        result.setConfigJson(branchConfigVo.getConfigJson());
        return result;
    }
}
