package com.jxw.shufang.branch.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineRegionBo;
import com.jxw.shufang.branch.domain.bo.BranchMachineRegionBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteBranchMachineRegionBoConvertBranchMachineRegionBo extends BaseMapper<RemoteBranchMachineRegionBo, BranchMachineRegionBo> {

}
