package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.domain.IntegralOrder;
import com.jxw.shufang.branch.domain.bo.IntegralOrderBo;
import com.jxw.shufang.branch.domain.bo.IntegralOrderInfoBo;
import com.jxw.shufang.branch.domain.bo.IntegralOrderOperateBo;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderInfoVo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderVo;
import com.jxw.shufang.branch.mapper.IntegralOrderMapper;
import com.jxw.shufang.branch.service.*;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.IntegralChangeReasonEnum;
import com.jxw.shufang.common.core.enums.IntegralGoodRuleEnum;
import com.jxw.shufang.common.core.enums.IntegralOrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.student.api.RemoteStudentConsultantRecordService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 积分订单Service业务层处理
 *
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class IntegralOrderServiceImpl implements IIntegralOrderService {

    private final IntegralOrderMapper baseMapper;

    private final IIntegralGoodService integralGoodService;

    private final IIntegralRecordService integralRecordService;

    private final IIntegralOrderInfoService integralOrderInfoService;

    private final IIntegralOrderOperateService integralOrderOperateService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteUserService remoteUserService;


    /**
     * 查询积分订单
     */
    @Override
    public IntegralOrderVo queryById(Long integralOrderId) {
        IntegralOrderVo integralOrderVo = baseMapper.selectVoById(integralOrderId);
        if (integralOrderVo == null) {
            return null;
        }
        putExchangeStudentInfo(List.of(integralOrderVo));
        Long integralOrderOperateId = integralOrderVo.getIntegralOrderOperateId();
        if (integralOrderOperateId != null) {
            IntegralOrderOperateVo integralOrderOperateVo = integralOrderOperateService.queryById(integralOrderOperateId);
            integralOrderVo.setIntegralOrderOperate(integralOrderOperateVo);
            if (integralOrderOperateVo != null && IntegralOrderStatusEnum.EXCHANGED.getCode().equals(integralOrderOperateVo.getIntegralOrderOperateStatus())) {
                putVerifierInfo(List.of(integralOrderVo));
            }
        }
        IntegralOrderInfoVo integralOrderInfoVo = integralOrderInfoService.queryByOrderId(integralOrderId);
        integralOrderVo.setIntegralOrderInfo(integralOrderInfoVo);
        return integralOrderVo;
    }

    /**
     * 查询积分订单列表
     */
    @Override
    public TableDataInfo<IntegralOrderVo> queryPageList(IntegralOrderBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<IntegralOrder> wrapper = buildQueryWrapper(bo);
        Page<IntegralOrderVo> result = baseMapper.queryOrderInfoPage(pageQuery.build(), wrapper);

        if (Boolean.TRUE.equals(bo.getWithVerifierInfo())) {
            putVerifierInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithExchangeStudentInfo())) {
            putExchangeStudentInfo(result.getRecords());
        }


        return TableDataInfo.build(result);
    }

    private void putExchangeStudentInfo(List<IntegralOrderVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> studentIdList = records.stream().map(IntegralOrderVo::getStudentId).distinct().toList();
        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setStudentIds(studentIdList);
        List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
        Map<Long, RemoteStudentVo> studentMap = StreamUtils.toMap(remoteStudentVos, RemoteStudentVo::getStudentId, vo -> vo);
        for (IntegralOrderVo record : records) {
            Long studentId = record.getStudentId();
            if (studentId != null && studentMap.containsKey(studentId)) {
                record.setStudent(studentMap.get(studentId));
            }
        }

    }

    private void putVerifierInfo(List<IntegralOrderVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<IntegralOrderOperateVo> list = records.stream().map(IntegralOrderVo::getIntegralOrderOperate).filter(Objects::nonNull).toList();

        List<Long> userIdList = list.stream().map(IntegralOrderOperateVo::getCreateBy).distinct().toList();
        if (CollUtil.isEmpty(userIdList)) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(userIdList);
        remoteUserBo.setStatusList(Arrays.asList(UserConstants.USER_NORMAL, UserConstants.USER_DISABLE));
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> userMap = StreamUtils.toMap(remoteUserVos, RemoteUserVo::getUserId, vo -> vo);
        for (IntegralOrderVo record : records) {
            IntegralOrderOperateVo integralOrderOperate = record.getIntegralOrderOperate();
            if (integralOrderOperate != null) {
                Long createBy = integralOrderOperate.getCreateBy();
                if (createBy != null && userMap.containsKey(createBy)) {
                    record.setVerifier(userMap.get(createBy));
                }
            }
        }
    }

    /**
     * 查询积分订单列表
     */
    @Override
    public List<IntegralOrderVo> queryOrderList(IntegralOrderBo bo) {
        QueryWrapper<IntegralOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.queryOrderList(lqw);
    }


    private QueryWrapper<IntegralOrder> buildQueryWrapper(IntegralOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<IntegralOrder> lqw = Wrappers.query();
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "student_id", bo.getStudentIdList());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getConsultantId() != null, "t.consultant_id", bo.getConsultantId());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralOrderNo()), "t.integral_order_no", bo.getIntegralOrderNo());
        lqw.eq(bo.getIntegralOrderOperateId() != null, "t.integral_order_operate_id", bo.getIntegralOrderOperateId());
        lqw.eq(bo.getIntegralGoodId() != null, "ioi.integral_good_id", bo.getIntegralGoodId());
        lqw.in(CollUtil.isNotEmpty(bo.getIntegralOrderOperateStatusList()), "ioop.integral_order_operate_status", bo.getIntegralOrderOperateStatusList());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralOrderOperateStatus()), "ioop.integral_order_operate_status", bo.getIntegralOrderOperateStatus());
        lqw.last(StringUtils.isNotBlank(bo.getOrderBy()), "order by " + bo.getOrderBy());
        //    private String nameWithPhone;
        lqw.like(StringUtils.isNotBlank(bo.getIntegralGoodName()), "ioi.integral_good_name", bo.getIntegralGoodName());
        if (StringUtils.isNotBlank(bo.getNameWithPhone())) {
            RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
            remoteStudentBo.setNameWithPhone(bo.getNameWithPhone());
            List<Long> longs = remoteStudentService.queryStudentIdList(remoteStudentBo);
            if (CollUtil.isNotEmpty(longs)) {
                lqw.in("t.student_id", longs);
            }else {
                lqw.eq("t.student_id", List.of(-1L));
            }
        }

        return lqw;
    }

    private LambdaQueryWrapper<IntegralOrder> buildLambdaQueryWrapper(IntegralOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IntegralOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, IntegralOrder::getStudentId, bo.getStudentId());
        lqw.eq(bo.getConsultantId() != null, IntegralOrder::getConsultantId, bo.getConsultantId());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralOrderNo()), IntegralOrder::getIntegralOrderNo, bo.getIntegralOrderNo());
        lqw.eq(bo.getIntegralOrderOperateId() != null, IntegralOrder::getIntegralOrderOperateId, bo.getIntegralOrderOperateId());
        return lqw;
    }

    /**
     * 新增积分订单
     */
    @Override
    public Boolean insertByBo(IntegralOrderBo bo) {
        IntegralOrder add = MapstructUtils.convert(bo, IntegralOrder.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralOrderId(add.getIntegralOrderId());
        }
        return flag;
    }

    /**
     * 修改积分订单
     */
    @Override
    public Boolean updateByBo(IntegralOrderBo bo) {
        IntegralOrder update = MapstructUtils.convert(bo, IntegralOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralOrder entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除积分订单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void exchange(Long integralGoodId, Long studentId) {
        IntegralGoodVo integralGoodVo = integralGoodService.queryById(integralGoodId);
        if (integralGoodVo == null) {
            throw new ServiceException("商品不存在");
        }
        if (!UserConstants.INTEGRAL_GOOD_STATUS_UP.equals(integralGoodVo.getIntegralGoodStatus())) {
            throw new ServiceException("商品已下架");
        }
        //如果仅限兑换一次，判断是否已经兑换
        if (IntegralGoodRuleEnum.ONLY_ONCE.getType().equals(integralGoodVo.getIntegralGoodRule())) {
            IntegralOrderBo integralOrderBo = new IntegralOrderBo();
            integralOrderBo.setStudentId(studentId);
            integralOrderBo.setIntegralGoodId(integralGoodId);
            integralOrderBo.setIntegralOrderOperateStatusList(List.of(IntegralOrderStatusEnum.EXCHANGED.getCode(), IntegralOrderStatusEnum.WAIT_EXCHANGE.getCode()));
            List<IntegralOrderVo> integralOrderVos = queryOrderList(integralOrderBo);
            if (CollUtil.isNotEmpty(integralOrderVos)) {
                String integralOrderOperateStatus = integralOrderVos.get(0).getIntegralOrderOperate().getIntegralOrderOperateStatus();
                IntegralOrderStatusEnum byCode = IntegralOrderStatusEnum.getByCode(integralOrderOperateStatus);
                if (IntegralOrderStatusEnum.EXCHANGED.equals(byCode)) {
                    throw new ServiceException("该商品：" + IntegralGoodRuleEnum.ONLY_ONCE.getDesc() + ",当前状态：" + IntegralOrderStatusEnum.EXCHANGED.getInfo());
                } else if (IntegralOrderStatusEnum.WAIT_EXCHANGE.equals(byCode)) {
                    throw new ServiceException("该商品：" + IntegralGoodRuleEnum.ONLY_ONCE.getDesc() + ",当前状态：" + IntegralOrderStatusEnum.WAIT_EXCHANGE.getInfo());
                } else if (IntegralOrderStatusEnum.CANCELLED.equals(byCode)) {
                    //nothing
                } else {
                    throw new ServiceException("该商品：" + IntegralGoodRuleEnum.ONLY_ONCE.getDesc());
                }
            }
        }

        BigDecimal integralByStudentId = integralRecordService.getIntegralByStudentId(studentId);
        if (integralByStudentId.compareTo(integralGoodVo.getIntegralGoodCost()) < 0) {
            throw new ServiceException("积分不足");
        }
        SpringUtils.getAopProxy(this).createOrder(integralGoodId, studentId);
    }

    @Override
    public void verify(Long orderId) {
        IntegralOrderVo integralOrderVo = baseMapper.selectVoById(orderId);
        if (integralOrderVo == null) {
            throw new ServiceException("订单不存在");
        }
        Long integralOrderOperateId = integralOrderVo.getIntegralOrderOperateId();
        if (integralOrderOperateId == null) {
            throw new ServiceException("订单数据异常");
        }
        IntegralOrderOperateVo integralOrderOperateVo = integralOrderOperateService.queryById(integralOrderOperateId);
        if (integralOrderOperateVo == null) {
            throw new ServiceException("订单操作数据异常");
        }
        if (IntegralOrderStatusEnum.EXCHANGED.getCode().equals(integralOrderOperateVo.getIntegralOrderOperateStatus())) {
            throw new ServiceException("订单已核销");
        }
        if (IntegralOrderStatusEnum.CANCELLED.getCode().equals(integralOrderOperateVo.getIntegralOrderOperateStatus())) {
            throw new ServiceException("订单已取消");
        }
        //核销订单
        IntegralOrderOperateBo bo = new IntegralOrderOperateBo();
        bo.setIntegralOrderOperateStatus(IntegralOrderStatusEnum.EXCHANGED.getCode());
        bo.setIntegralOrderId(integralOrderVo.getIntegralOrderId());
        bo.setPaymentIntegral(integralOrderOperateVo.getPaymentIntegral());
        Boolean b = integralOrderOperateService.insertByBo(bo);
        if (!b || bo.getIntegralOrderOperateId() == null) {
            throw new ServiceException("核销订单失败");
        }
        //更新订单状态
        IntegralOrder updateBean = new IntegralOrder();
        updateBean.setIntegralOrderId(integralOrderVo.getIntegralOrderId());
        updateBean.setIntegralOrderOperateId(bo.getIntegralOrderOperateId());
        baseMapper.updateById(updateBean);
    }

    @Override
    public void cancel(Long orderId) {
        IntegralOrderVo integralOrderVo = baseMapper.selectVoById(orderId);
        if (integralOrderVo == null) {
            throw new ServiceException("订单不存在");
        }
        Long integralOrderOperateId = integralOrderVo.getIntegralOrderOperateId();
        if (integralOrderOperateId == null) {
            throw new ServiceException("订单数据异常");
        }
        IntegralOrderOperateVo integralOrderOperateVo = integralOrderOperateService.queryById(integralOrderOperateId);
        if (integralOrderOperateVo == null) {
            throw new ServiceException("订单操作数据异常");
        }
        if (IntegralOrderStatusEnum.EXCHANGED.getCode().equals(integralOrderOperateVo.getIntegralOrderOperateStatus())) {
            throw new ServiceException("订单已核销");
        }
        if (IntegralOrderStatusEnum.CANCELLED.getCode().equals(integralOrderOperateVo.getIntegralOrderOperateStatus())) {
            throw new ServiceException("订单已取消");
        }
        //订单详情
        IntegralOrderInfoVo integralOrderInfoVo = integralOrderInfoService.queryByOrderId(orderId);
        if (integralOrderInfoVo == null) {
            throw new ServiceException("订单详情数据异常");
        }
        //取消订单
        IntegralOrderOperateBo bo = new IntegralOrderOperateBo();
        bo.setIntegralOrderOperateStatus(IntegralOrderStatusEnum.CANCELLED.getCode());
        bo.setIntegralOrderId(integralOrderVo.getIntegralOrderId());
        bo.setPaymentIntegral(integralOrderOperateVo.getPaymentIntegral());
        Boolean b = integralOrderOperateService.insertByBo(bo);
        if (!b || bo.getIntegralOrderOperateId() == null) {
            throw new ServiceException("核销订单失败");
        }
        //更新订单状态
        IntegralOrder updateBean = new IntegralOrder();
        updateBean.setIntegralOrderId(integralOrderVo.getIntegralOrderId());
        updateBean.setIntegralOrderOperateId(bo.getIntegralOrderOperateId());
        baseMapper.updateById(updateBean);

        //返还会员积分，返还库存
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(integralOrderVo.getStudentId());
        integralRecordBo.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
        integralRecordBo.setChangeNum(integralOrderOperateVo.getPaymentIntegral());
        integralRecordBo.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
        integralRecordBo.setChangeReason("订单取消");
        integralRecordBo.setChangeRemark("兑换：" + integralOrderInfoVo.getIntegralGoodName() + "失败，订单已取消，积分已返还");
        integralRecordService.insertByBo(integralRecordBo);

        //扣减库存
        Boolean b2 = integralGoodService.updateStock(integralOrderInfoVo.getIntegralGoodId(), +1);
        if (!b2) {
            throw new ServiceException("库存返还失败");
        }

    }

    @Lock4j(keys = {"#integralGoodId"})
    @Transactional(rollbackFor = Exception.class)
    public void createOrder(Long integralGoodId, Long studentId) {
        IntegralGoodVo integralGoodVo = integralGoodService.queryById(integralGoodId);
        if (integralGoodVo == null) {
            throw new ServiceException("积分商品不存在");
        }
        if (integralGoodVo.getIntegralGoodStock() <= 0) {
            throw new ServiceException("库存不足");
        }
        IntegralOrder integralOrder = new IntegralOrder();
        integralOrder.setStudentId(studentId);
        integralOrder.setIntegralOrderNo(generateIntegralOrderNo());
        integralOrder.setConsultantId(remoteStudentConsultantRecordService.getStudentResponsibleStaffId(studentId, true));
        int insert = baseMapper.insert(integralOrder);
        if (insert <= 0) {
            throw new ServiceException("创建订单失败");
        }
        //创建订单信息
        IntegralOrderInfoBo integralOrderInfo = new IntegralOrderInfoBo();
        integralOrderInfo.setIntegralOrderId(integralOrder.getIntegralOrderId());
        integralOrderInfo.setIntegralGoodId(integralGoodId);
        integralOrderInfo.setIntegralGoodImg(integralGoodVo.getIntegralGoodImg());
        integralOrderInfo.setIntegralGoodCost(integralGoodVo.getIntegralGoodCost());
        integralOrderInfo.setIntegralGoodName(integralGoodVo.getIntegralGoodName());
        integralOrderInfo.setIntegralGoodType(integralGoodVo.getIntegralGoodType());
        Boolean b = integralOrderInfoService.insertByBo(integralOrderInfo);
        if (!b) {
            throw new ServiceException("创建订单详情失败");
        }
        //创建订单操作
        IntegralOrderOperateBo integralOrderOperateBo = new IntegralOrderOperateBo();
        integralOrderOperateBo.setIntegralOrderId(integralOrder.getIntegralOrderId());
        integralOrderOperateBo.setIntegralOrderOperateStatus(IntegralOrderStatusEnum.WAIT_EXCHANGE.getCode());
        integralOrderOperateBo.setPaymentIntegral(integralGoodVo.getIntegralGoodCost());
        Boolean b1 = integralOrderOperateService.insertByBo(integralOrderOperateBo);
        if (!b1 || integralOrderOperateBo.getIntegralOrderOperateId() == null) {
            throw new ServiceException("创建订单操作失败");
        }
        //赋值订单操作id，并更新
        IntegralOrder updateBean = new IntegralOrder();
        updateBean.setIntegralOrderId(integralOrder.getIntegralOrderId());
        updateBean.setIntegralOrderOperateId(integralOrderOperateBo.getIntegralOrderOperateId());
        baseMapper.updateById(updateBean);

        //新增积分扣减记录
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_REDUCE);
        integralRecordBo.setChangeNum(integralGoodVo.getIntegralGoodCost());
        integralRecordBo.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
        integralRecordBo.setChangeReason(IntegralChangeReasonEnum.EXCHANGE_GOODS.toString());
        integralRecordBo.setChangeRemark("兑换商品：" + integralGoodVo.getIntegralGoodName());
        integralRecordService.insertByBo(integralRecordBo);

        //扣减库存
        Boolean b2 = integralGoodService.updateStock(integralGoodId, -1);
        if (!b2) {
            throw new ServiceException("扣减库存失败");
        }

    }


    private String generateIntegralOrderNo() {
        //先做简单的
        return String.valueOf(System.currentTimeMillis());
    }


    private void handleQueryParam(IntegralOrderBo record) {
        if (record.getStudentId()!=null) {
            return;
        }
        //判断登录的是不是会员顾问，并且不是店长
        // &&!LoginHelper.isStoreOwner()
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null) {
            List<Long> staffResponsibleStudentIdList = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId(),true);
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
                List<Long> studentIdList = remoteStudentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList(),true);
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = remoteStudentService.getStudentIdListByBranchId(LoginHelper.getBranchId(),true);
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
        }
    }
}
