package com.jxw.shufang.branch.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.service.IIntegralTaskService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 积分任务-平板端
 * 前端访问路由地址为:/branch/integralTask
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/integralTask")
public class AIntegralTaskController extends BaseController {

    private final IIntegralTaskService integralTaskService;

    /**
     * 查询积分任务列表-平板端
     */
    @GetMapping("/list")
    public R<List<IntegralTaskVo>> list() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        IntegralTaskBo integralTaskBo = new IntegralTaskBo();
        integralTaskBo.setTaskStatus(UserConstants.INTEGRAL_TASK_STATUS_UP);
        integralTaskBo.setBranchId(LoginHelper.getBranchId());
        return R.ok(integralTaskService.queryStudentIntegralTaskList(LoginHelper.getStudentId(),integralTaskBo));
    }

    /**
     * 领取任务积分-平板端
     */
    @PostMapping("/receive")
    @Log(title = "平板端-领取任务积分", businessType = BusinessType.INSERT)
    public R<Void> receive(Long integralTaskId) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        integralTaskService.receiveTaskIntegral(LoginHelper.getStudentId(), integralTaskId);
        return R.ok();
    }


}
