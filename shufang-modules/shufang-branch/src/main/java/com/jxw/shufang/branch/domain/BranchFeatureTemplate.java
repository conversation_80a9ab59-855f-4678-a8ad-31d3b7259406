package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 管控功能配置模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("branch_feature_template")
public class BranchFeatureTemplate implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableId(value = "branch_feature_template_id")
    private Long branchFeatureTemplateId;

    /**
     * 功能名称（唯一标识）
     */
    private String featureName;
    /**
     * 管控类型
     */
    private String featureType;

    /**
     * 关联管控类型（1督学/2自学）
     */
    private Integer controlType;

    /**
     * 功能详细描述
     */
    private String remark;
    /**
     * 开关状态（0: 关闭, 1: 开启）
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
