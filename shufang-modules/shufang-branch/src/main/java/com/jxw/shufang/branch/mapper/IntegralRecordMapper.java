package com.jxw.shufang.branch.mapper;

import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分记录Mapper接口
 *
 *
 * @date 2024-04-23
 */
public interface IntegralRecordMapper extends BaseMapperPlus<IntegralRecord, IntegralRecordVo> {

    BigDecimal getIntegralByStudentId(Long studentId);

    List<IntegralRecordVo> getIntegralByStudentIdList(List<Long> studentIdList);

}
