package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.branch.domain.BranchMachineRegion;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.BranchMachineRegionBo;
import com.jxw.shufang.branch.domain.bo.BranchMachineSeatBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.mapper.BranchMachineRegionMapper;
import com.jxw.shufang.branch.service.IBranchMachineRegionService;
import com.jxw.shufang.branch.service.IBranchMachineSeatService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分店机位分区Service业务层处理
 *
 *
 * @date 2024-03-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BranchMachineRegionServiceImpl implements IBranchMachineRegionService, BaseService {

    private final BranchMachineRegionMapper baseMapper;

    private final IBranchMachineSeatService branchMachineSeatService;

    private final IBranchService branchService;

    /**
     * 查询分店机位分区
     */
    @Override
    public BranchMachineRegionVo queryById(Long branchMachineRegionId) {
        return baseMapper.selectVoById(branchMachineRegionId);
    }

    /**
     * 查询分店机位分区列表
     */
    @Override
    public TableDataInfo<BranchMachineRegionVo> queryPageList(BranchMachineRegionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchMachineRegion> lqw = buildQueryWrapper(bo);
        Page<BranchMachineRegionVo> result = baseMapper.selectMachineRegionPage(pageQuery.build(), lqw);
        putBranchInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询分店机位分区列表
     */
    @Override
    public List<BranchMachineRegionVo> queryList(BranchMachineRegionBo bo) {
        LambdaQueryWrapper<BranchMachineRegion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectRegionList(lqw);
    }

    private LambdaQueryWrapper<BranchMachineRegion> buildQueryWrapper(BranchMachineRegionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchMachineRegion> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, BranchMachineRegion::getBranchId, bo.getBranchId());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), BranchMachineRegion::getBranchId, bo.getBranchIdList());
        lqw.like(StringUtils.isNotBlank(bo.getRegionName()), BranchMachineRegion::getRegionName, bo.getRegionName());
        lqw.eq(bo.getRegionNum() != null, BranchMachineRegion::getRegionNum, bo.getRegionNum());
        lqw.eq(bo.getRegionImg() != null, BranchMachineRegion::getRegionImg, bo.getRegionImg());
        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq(BranchMachineRegion::getDelFlag, bo.getDelFlag());
        } else {
            lqw.eq(BranchMachineRegion::getDelFlag, UserConstants.DEL_FLAG_NO);
        }
        return lqw;
    }

    /**
     * 新增分店机位分区
     */
    @Override
    public Boolean insertByBo(BranchMachineRegionBo bo) {
        BranchMachineRegion add = MapstructUtils.convert(bo, BranchMachineRegion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchMachineRegionId(add.getBranchMachineRegionId());
        }
        return flag;
    }

    /**
     * 修改分店机位分区
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByBo(BranchMachineRegionBo bo) {
        BranchMachineRegion update = MapstructUtils.convert(bo, BranchMachineRegion.class);
        validEntityBeforeSave(update);
        baseMapper.updateById(update);

    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchMachineRegion entity) {
        Long branchMachineRegionId = entity.getBranchMachineRegionId();
        //更新时校验
        if (branchMachineRegionId != null) {
            BranchMachineRegionVo branchMachineRegionVo = queryById(branchMachineRegionId);
            Long regionNum = branchMachineRegionVo.getRegionNum();
            if (regionNum <= entity.getRegionNum()) {
                return;
            }
            //删除机位前需要校验是否有会员使用
            long delNum = regionNum - entity.getRegionNum();
            List<Long> delNumList = new ArrayList<>();
            for (long i = 1; i <= delNum; i++) {
                delNumList.add(regionNum + i);
            }

            BranchMachineSeatBo branchMachineSeatBo = new BranchMachineSeatBo();
            branchMachineSeatBo.setSeatNoList(delNumList);
            branchMachineSeatBo.setBranchMachineRegionId(entity.getBranchMachineRegionId());
            branchMachineSeatBo.setGreaterThanEndTime(new Date());
            List<BranchMachineSeatVo> branchMachineSeatVoList = branchMachineSeatService.queryList(branchMachineSeatBo);
            if (CollUtil.isEmpty(branchMachineSeatVoList)) {
                return;
            }
            StringBuilder errMsg = new StringBuilder();
            for (BranchMachineSeatVo branchMachineSeatVo : branchMachineSeatVoList) {
                errMsg.append("机位[")
                    .append(branchMachineSeatVo.getSeatNo())
                    .append("]已有会员使用,");
                if (branchMachineSeatVo.getUseEndTime() != null
                    && branchMachineSeatVo.getUseStartTime() != null) {
                    errMsg.append("使用时间段为[")
                        .append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, branchMachineSeatVo.getUseStartTime()))
                        .append("至")
                        .append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, branchMachineSeatVo.getUseEndTime()))
                        .append("]，");
                }
                errMsg.append("不能删除\n");
            }
            if (!errMsg.isEmpty()) {
                throw new ServiceException(errMsg.toString());
            }

        }
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分店机位分区
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public void putBranchInfo(List<BranchMachineRegionVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> branchIds = list.stream().map(BranchMachineRegionVo::getBranchId).collect(Collectors.toList());
        branchIds.remove(null);
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }

        BranchBo branchBo = new BranchBo();
        branchBo.setBranchIds(branchIds);
        List<BranchVo> branchVoList = branchService.selectSimpleBranInfo(branchBo);
        Map<Long, BranchVo> branchVoMap = branchVoList.stream().collect(Collectors.toMap(BranchVo::getBranchId, vo -> vo));
        list.forEach(item -> {
            BranchVo vo = branchVoMap.get(item.getBranchId());
            item.setBranch(vo);
        });

    }

    @Cacheable(value = "branchMachineRegion", key = "#branchMachineRegionId", condition = "#branchMachineRegionId != null")
    @Override
    public BranchMachineRegion queryBranchMachineRegionById(Long branchMachineRegionId) {
        return baseMapper.selectById(branchMachineRegionId);
    }

    @CacheEvict(value = "branchMachineRegion", allEntries = true)
    public void cleanCache() {
        log.info("===========branchMachineRegionService cleanCache===========");
    }

    @Override
    public List<BranchMachineSeatVo> listWithOccupied(Date startTime, Date endTime, Long branchMachineRegionId) {
        BranchMachineRegionVo branchMachineRegionVo = baseMapper.selectVoById(branchMachineRegionId);
        if (branchMachineRegionVo == null){
            return Collections.emptyList();
        }
        //查时间区间内在使用的机子
        BranchMachineSeatBo branchMachineSeatBo = new BranchMachineSeatBo();
        branchMachineSeatBo.setBranchMachineRegionId(branchMachineRegionId);
        branchMachineSeatBo.setRangeStartTime(startTime);
        branchMachineSeatBo.setRangeEndTime(endTime);
        List<BranchMachineSeatVo> branchMachineSeatVos = branchMachineSeatService.queryList(branchMachineSeatBo);

        //按照座位号分组
        Map<Long, List<BranchMachineSeatVo>> seatMap = branchMachineSeatVos.stream().collect(Collectors.groupingBy(BranchMachineSeatVo::getSeatNo));

        List<BranchMachineSeatVo> resList = new ArrayList<>();
        Long regionNum = branchMachineRegionVo.getRegionNum();
        //生成机位列表
        for (long i = 1; i <= regionNum; i++) {
            BranchMachineSeatVo branchMachineSeatVo = new BranchMachineSeatVo();
            branchMachineSeatVo.setSeatNo(i);
            branchMachineSeatVo.setBranchMachineRegionId(branchMachineRegionId);
            branchMachineSeatVo.setIsUseSeat(CollUtil.isNotEmpty(seatMap.get(i)));
            resList.add(branchMachineSeatVo);
        }
        return resList;
    }

    @Override
    public void init() {
        IBranchMachineRegionService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchMachineRegionService init===========");
        LambdaQueryWrapper<BranchMachineRegion> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchMachineRegion::getBranchMachineRegionId);
        List<BranchMachineRegion> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========branchMachineRegionService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchMachineRegionById(item.getBranchMachineRegionId());
        });
        log.info("===========branchMachineRegionService init end===========");
    }


}
