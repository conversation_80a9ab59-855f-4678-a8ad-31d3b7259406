package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.IntegralOrderBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 积分订单Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IIntegralOrderService {

    /**
     * 查询积分订单
     */
    IntegralOrderVo queryById(Long integralOrderId);

    /**
     * 查询积分订单列表
     */
    TableDataInfo<IntegralOrderVo> queryPageList(IntegralOrderBo bo, PageQuery pageQuery);

    /**
     * 查询积分订单列表
     */
    List<IntegralOrderVo> queryOrderList(IntegralOrderBo bo);


    /**
     * 新增积分订单
     */
    Boolean insertByBo(IntegralOrderBo bo);

    /**
     * 修改积分订单
     */
    Boolean updateByBo(IntegralOrderBo bo);

    /**
     * 校验并批量删除积分订单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 兑换
     *
     * @param integralGoodId 积分商品id
     * @param studentId      会员id
     *
     * @date 2024/04/24 12:54:38
     */
    void exchange(Long integralGoodId, Long studentId);

    void verify( Long orderId);

    void cancel(Long orderId);
}
