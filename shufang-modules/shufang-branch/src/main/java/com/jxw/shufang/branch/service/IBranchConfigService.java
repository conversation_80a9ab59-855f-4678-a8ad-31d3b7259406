package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

public interface IBranchConfigService {
    /**
     * 获取某个类型的门店配置
     *
     * @param bo
     * @return
     */
    BranchConfigVo getBranchConfigByBo(BranchConfigBo bo);

    /**
     * 获取所有的配置类型
     *
     * @param pageQuery
     * @return
     */
    TableDataInfo<BranchConfigVo.ConfigType> listAllConfigType(PageQuery pageQuery);

    /**
     * 获取门店某个类型的配置(for update)
     *
     * @param bo
     * @return
     */
    BranchConfigVo getConfig(BranchConfigBo bo);

    /**
     * 更新配置
     * @param bo
     * @return
     */
    boolean updateConfigBo(BranchConfigBo bo);

    /**
     * 同步配置
     * @param bo
     * @return
     */
    boolean duplicateConfig(BranchConfigBo bo);
}
