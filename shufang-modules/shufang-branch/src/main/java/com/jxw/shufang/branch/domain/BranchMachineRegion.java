package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 分店机位分区对象 branch_machine_region
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_machine_region")
public class BranchMachineRegion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店机位分区id
     */
    @TableId(value = "branch_machine_region_id")
    private Long branchMachineRegionId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分区名称
     */
    private String regionName;

    /**
     * 分区数量
     */
    private Long regionNum;

    /**
     * 机位图片（oss_id 每个分区的机位分布示意图）
     */
    private Long regionImg;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;





}
