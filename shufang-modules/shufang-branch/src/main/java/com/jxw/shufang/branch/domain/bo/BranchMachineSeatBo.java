package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.BranchMachineSeat;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 机位业务对象 branch_machine_seat
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchMachineSeat.class, reverseConvertGenerate = false)
public class BranchMachineSeatBo extends BaseEntity {

    /**
     * 机位id
     */
    @NotNull(message = "机位id不能为空", groups = { EditGroup.class })
    private Long branchMachineSeatId;

    /**
     * 分店机位分区id
     */
    @NotNull(message = "分店机位分区id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchMachineRegionId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 机位号（数字，如20号机位，则为20）
     */
    @NotNull(message = "机位号（数字，如20号机位，则为20）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long seatNo;


    /**
     * 使用开始时间（对应学习规划中某课程的学习开始时间）
     */
    @NotNull(message = "使用开始时间（对应学习规划中某课程的学习开始时间）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date useStartTime;

    /**
     * 使用结束时间（对应学习规划中某课程的学习结束时间）
     */
    @NotNull(message = "使用结束时间（对应学习规划中某课程的学习结束时间）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date useEndTime;

    /**
     * 大于等于使用结束时间
     */
    private Date greaterThanEndTime;

    private List<Long> seatNoList;

    private List<Long> studentIdList;

    /**
     * 使用时间范围查询开始时间
     */
    private Date rangeStartTime;

    /**
     * 使用时间范围查询结束时间
     */
    private Date rangeEndTime;
}
