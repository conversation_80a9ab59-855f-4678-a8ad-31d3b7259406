package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralOrderInfoBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderInfoVo;
import com.jxw.shufang.branch.service.IIntegralOrderInfoService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分订单详情
 * 前端访问路由地址为:/branch/integralOrderInfo
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralOrderInfo")
public class IntegralOrderInfoController extends BaseController {

    private final IIntegralOrderInfoService integralOrderInfoService;

    /**
     * 查询积分订单详情列表
     */
    @SaCheckPermission("branch:integralOrderInfo:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralOrderInfoVo> list(IntegralOrderInfoBo bo, PageQuery pageQuery) {
        return integralOrderInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分订单详情列表
     */
    @SaCheckPermission("branch:integralOrderInfo:export")
    @Log(title = "积分订单详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IntegralOrderInfoBo bo, HttpServletResponse response) {
        List<IntegralOrderInfoVo> list = integralOrderInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分订单详情", IntegralOrderInfoVo.class, response);
    }

    /**
     * 获取积分订单详情详细信息
     *
     * @param integralOrderInfoId 主键
     */
    @SaCheckPermission("branch:integralOrderInfo:query")
    @GetMapping("/{integralOrderInfoId}")
    public R<IntegralOrderInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralOrderInfoId) {
        return R.ok(integralOrderInfoService.queryById(integralOrderInfoId));
    }

    /**
     * 新增积分订单详情
     */
    @SaCheckPermission("branch:integralOrderInfo:add")
    @Log(title = "积分订单详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralOrderInfoBo bo) {
        return toAjax(integralOrderInfoService.insertByBo(bo));
    }

    /**
     * 修改积分订单详情
     */
    @SaCheckPermission("branch:integralOrderInfo:edit")
    @Log(title = "积分订单详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralOrderInfoBo bo) {
        return toAjax(integralOrderInfoService.updateByBo(bo));
    }

    /**
     * 删除积分订单详情
     *
     * @param integralOrderInfoIds 主键串
     */
    @SaCheckPermission("branch:integralOrderInfo:remove")
    @Log(title = "积分订单详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralOrderInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralOrderInfoIds) {
        return toAjax(integralOrderInfoService.deleteWithValidByIds(List.of(integralOrderInfoIds), true));
    }
}
