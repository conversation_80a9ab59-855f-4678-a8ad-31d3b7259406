package com.jxw.shufang.branch.domain.bo;

import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 管控功能配置模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@Data
@AutoMapper(target = BranchFeatureTemplate.class)
public class BranchFeatureTemplateBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @NotNull(message = "功能名称不能为空", groups = {EditGroup.class})
    private Long branchFeatureTemplateId;

    /**
     * 功能名称（唯一标识）
     */
    @NotNull(message = "功能名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String featureName;

    /**
     * 管控类型
     */
    private String featureType;

    /**
     * 管控类型（1督学/2自学）
     */
    @NotNull(message = "管控类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer controlType;

    /**
     * 功能详细描述
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 开关状态（0: 关闭, 1: 开启）
     */
    private Integer status;
}
