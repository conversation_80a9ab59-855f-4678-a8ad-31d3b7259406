package com.jxw.shufang.branch.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: cyj
 * @date: 2025/7/8
 */
@Getter
@AllArgsConstructor
public enum ScheduleIntegralEnum {

    START_OF_MONTH("月初", 1), MIDDLE_OF_MONTH("月中", 15), END_OF_MONTH("月底", 28);

    /**
     * 时间
     */
    private String scheduleName;
    /**
     * 每月多少号的零点执行定时任务
     */
    private Integer dayOfMonth;

    /**
     * 获取配置的日期
     *
     * @param scheduleName
     * @return
     */
    public static Integer getDayOfMonthByName(String scheduleName) {
        for (ScheduleIntegralEnum value : ScheduleIntegralEnum.values()) {
            if (value.scheduleName.equals(scheduleName)) {
                return value.dayOfMonth;
            }
        }
        return null;
    }

    /**
     * 获取配置的日期
     *
     * @param dayOfMonth
     * @return
     */
    public static String getScheduleName(Integer dayOfMonth) {
        for (ScheduleIntegralEnum value : ScheduleIntegralEnum.values()) {
            if (value.dayOfMonth.equals(dayOfMonth)) {
                return value.scheduleName;
            }
        }
        return null;
    }
}
