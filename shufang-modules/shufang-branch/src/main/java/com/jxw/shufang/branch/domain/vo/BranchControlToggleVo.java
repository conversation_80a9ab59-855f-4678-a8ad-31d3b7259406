package com.jxw.shufang.branch.domain.vo;

import com.jxw.shufang.branch.domain.BranchControlToggle;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = BranchControlToggle.class)
public class BranchControlToggleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long branchControlToggleId;

    /**
     * 门店编号
     */
    private Long branchId;

    /**
     * 管控功能模板Id
     */
    private Long branchFeatureTemplateId;

    /**
     * 开关状态（默认0: 关闭, 1: 开启）
     */
    private Integer status;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
