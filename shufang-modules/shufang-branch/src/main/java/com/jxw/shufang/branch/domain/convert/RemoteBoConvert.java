package com.jxw.shufang.branch.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteBoConvert extends BaseMapper<RemoteBranchBo, BranchBo> {


}
