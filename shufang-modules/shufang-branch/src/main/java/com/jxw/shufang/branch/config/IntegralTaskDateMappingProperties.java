package com.jxw.shufang.branch.config;

import com.jxw.shufang.branch.enums.ScheduleIntegralEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @author: cyj
 * @date: 2025/7/24
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "integral.date-mapping")
public class IntegralTaskDateMappingProperties {

    private DateMapping begin = new DateMapping(ScheduleIntegralEnum.START_OF_MONTH.name(),
        ScheduleIntegralEnum.START_OF_MONTH.getDayOfMonth());
    private DateMapping middle = new DateMapping(ScheduleIntegralEnum.MIDDLE_OF_MONTH.name(),
        ScheduleIntegralEnum.MIDDLE_OF_MONTH.getDayOfMonth());
    private DateMapping end =
        new DateMapping(ScheduleIntegralEnum.END_OF_MONTH.name(), ScheduleIntegralEnum.END_OF_MONTH.getDayOfMonth());

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DateMapping {
        private String enumName;
        private Integer dayOfMonth;
    }

}
