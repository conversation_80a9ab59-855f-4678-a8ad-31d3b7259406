package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.Material;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.resource.api.domain.RemoteFile;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 物料视图对象 material
 *
 *
 * @date 2024-02-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Material.class)
public class MaterialVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物料id
     */
    @ExcelProperty(value = "物料id")
    private Long materialId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 物料类型（启动培训、产品介绍、培训讲座、销售工具、宣传资料、运维工具）
     */
    @ExcelProperty(value = "物料类型", converter = ExcelDictConvert.class)
    private String materialType;

    /**
     * 是否外链（0是 1否）
     */
    @ExcelProperty(value = "是否外链", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=是,1=否")
    private String isUrl;

    /**
     * 物料外链地址
     */
    @ExcelProperty(value = "物料外链地址")
    private String materialUrl;

    /**
     * 物料文件ids
     */
    @ExcelProperty(value = "物料文件ids")
    private String materialFileIds;

    /**
     * 物料对应的门店
     */
    private BranchVo branch;

    /**
     * 物料文件列表
     */
    private List<RemoteFile> materialFileList;


}
