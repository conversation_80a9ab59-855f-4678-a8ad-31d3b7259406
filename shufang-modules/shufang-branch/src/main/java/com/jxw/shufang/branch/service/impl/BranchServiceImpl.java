package com.jxw.shufang.branch.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.branch.domain.BranchStaffBranchRelation;
import com.jxw.shufang.branch.domain.bo.BranchAuthRecordBo;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.mapper.BranchMapper;
import com.jxw.shufang.branch.mapper.BranchStaffBranchRelationMapper;
import com.jxw.shufang.branch.service.*;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderOperateVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.api.*;
import com.jxw.shufang.student.api.domain.bo.RemoteFeedbackRecordBo;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyVideoRecordBo;
import com.jxw.shufang.student.api.domain.vo.*;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.RemotePayMerchantConfigService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.api.domain.vo.RemotePayMerchantConfig;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分店Service业务层处理
 *
 * @date 2024-02-21
 */
@Service
//懒加载，允许循环依赖
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class BranchServiceImpl implements IBranchService, BaseService {
    private final BranchMapper baseMapper;

    private final BranchStaffBranchRelationMapper branchStaffBranchRelationMapper;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteUserService remoteUserService;
    private final IBranchAuthRecordService branchAuthRecordService;
    private final IBranchAuthTypeService branchAuthTypeService;
    private final IBranchService self;
    private final IMaterialService materialService;
    private final IIntegralTaskService integralTaskService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;

    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteStudyVideoRecordService remoteStudyVideoRecordService;

    @DubboReference
    private RemoteFeedbackRecordService remoteFeedbackRecordService;

    @DubboReference
    private RemotePayMerchantConfigService remotePayMerchantConfigService;

    /**
     * 查询分店
     */
    @Override
    public BranchVo queryById(Long branchId) {
        BranchVo branchVo = baseMapper.selectVoById(branchId);
        if (ObjectUtils.isEmpty(branchVo)){
            return branchVo;
        }
        RemoteDeptVo remoteDeptVo = remoteDeptService.selectDeptInfoById(branchVo.getCreateDept());
        if (!ObjectUtils.isEmpty(remoteDeptVo)){
            branchVo.setParentDeptId(remoteDeptVo.getParentId());
        }

        List<BranchStaffBranchRelation> branchStaffBranchRelations = getBranchStaffBranchRelations(Collections.singletonList(branchVo));

        branchVo.setBranchStaffIdList(branchStaffBranchRelations.stream().map(BranchStaffBranchRelation::getBranchStaffId).collect(Collectors.toList()));
        return branchVo;
    }

    /**
     * 通过门店ID集合 查询门店信息集合
     *
     * @param deptIds
     */
    @Override
    public List<BranchVo> selectDeptByIds(List<Long> deptIds) {
        if (ObjectUtil.isEmpty(deptIds)) return ListUtil.toList();

        LambdaQueryWrapper<Branch> objectLambdaQueryWrapper = Wrappers.lambdaQuery(Branch.class);
        objectLambdaQueryWrapper.in(Branch::getBranchId, deptIds);
        return baseMapper.selectVoList(objectLambdaQueryWrapper);
    }

    /**
     * 查询分店列表
     */
    @Override
    public TableDataInfo<BranchVo> queryPageList(BranchBo bo, PageQuery pageQuery) {
        QueryWrapper<Branch> lqw = buildQueryWrapper(bo);
        //baseMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<>());
        Page<BranchVo> result = baseMapper.selectPageBranchList(pageQuery.build(), lqw);
        putBranchAdminInfo(result.getRecords());
        putBranchParentDept(result.getRecords());
        return TableDataInfo.build(result);
    }

    private void putBranchParentDept(List<BranchVo> records) {
        if (org.springframework.util.CollectionUtils.isEmpty(records)) {
            return;
        }
        List<Long> list = records.stream().map(BranchVo::getCreateDept).distinct().toList();
        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setDeptIdList(list);
        Map<Long, RemoteDeptVo> deptVoMap = getDeptVO(remoteDeptBo);
        for (BranchVo record : records) {
            RemoteDeptVo remoteDeptVo = deptVoMap.get(record.getCreateDept());
            if (ObjectUtils.isEmpty(remoteDeptVo)) {
                continue;
            }
            record.setParentDeptId(remoteDeptVo.getParentId());
        }

        List<Long> parentList = records.stream().map(BranchVo::getParentDeptId).distinct().toList();
        RemoteDeptBo remoteParentDeptBo = new RemoteDeptBo();
        remoteParentDeptBo.setDeptIdList(parentList);
        Map<Long, RemoteDeptVo> parentDeptMaps = getDeptVO(remoteParentDeptBo);
        for (BranchVo record : records) {
            if (record.getParentDeptId() == null) {
                continue;
            }
            RemoteDeptVo remoteDeptVo = parentDeptMaps.get(record.getParentDeptId());
            if (ObjectUtils.isEmpty(remoteDeptVo)) {
                continue;
            }
            record.setParentDeptName(remoteDeptVo.getDeptName());
        }


    }

    private Map<Long, RemoteDeptVo> getDeptVO(RemoteDeptBo remoteDeptBo) {
        List<RemoteDeptVo> deptList = remoteDeptService.getDeptListIgnore(remoteDeptBo);
        if (org.springframework.util.CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyMap();
        }
        return deptList.stream().collect(Collectors.toMap(RemoteDeptVo::getDeptId, Function.identity()));
    }

    public void putExcelOtherInfo(List<BranchVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        for (BranchVo record : records) {
            BranchAuthRecordVo branchAuthRecord = record.getBranchAuthRecord();
            if (branchAuthRecord != null) {
                record.setAuthStatus(branchAuthRecord.getAuthStatus());
                record.setBranchAuthTypeName(branchAuthRecord.getBranchAuthTypeName());
                String authStartTime = branchAuthRecord.getAuthStartTime();
                String authEndTime = branchAuthRecord.getAuthEndTime();
                Long branchAuthTypeDays = branchAuthRecord.getBranchAuthTypeDays();
                String s = ObjectUtil.defaultIfNull(branchAuthTypeDays, 0L) + "天"
                    + "（"
                    + ObjectUtil.defaultIfBlank(authStartTime, "-")
                    + "至"
                    + ObjectUtil.defaultIfBlank(authEndTime, "-")
                    + "）";
                record.setAuthFullTime(s);
            }
        }
    }

    @Override
    public BranchVo selectBranchByDeptId(Long deptId) {
        LambdaQueryWrapper<Branch> eq = Wrappers.lambdaQuery(Branch.class)
            .eq(Branch::getCreateDept, deptId);
        return baseMapper.selectVoOne(eq);
    }

    @Override
    public Map<Long, Long> selectBranchIdMapByDeptIdList(List<Long> deptIdList, Boolean filterStopBranch) {
        LambdaQueryWrapper<Branch> eq = Wrappers.lambdaQuery(Branch.class)
            .in(Branch::getCreateDept, deptIdList);
        if (Boolean.TRUE.equals(filterStopBranch)) {
            eq.eq(Branch::getBranchStatus, UserConstants.NORMAL);
        }
        List<Branch> branchList = baseMapper.selectList(eq);
        return branchList.stream().collect(Collectors.toMap(Branch::getCreateDept, Branch::getBranchId));
    }

    @Override
    public TableDataInfo<BranchVo> statisticsPage(BranchBo bo, PageQuery pageQuery) {
        QueryWrapper<Branch> lqw = buildQueryWrapper(bo);
        //baseMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<>());
        Page<BranchVo> result = baseMapper.selectPageBranchList(pageQuery.build(), lqw);
        putStatisticsInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    @Override
    public List<BranchVo> statistics(BranchBo bo) {
        QueryWrapper<Branch> lqw = buildQueryWrapper(bo);
        //baseMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<>());
        List<BranchVo> result = baseMapper.selectBranchList(lqw);
        putStatisticsInfo(result);
        return result;
    }

    @Override
    @CacheEvict(value = "branch", key = "#branchBo.branchId", condition = "#branchBo.branchId != null")
    public Boolean updateBranchStatus(BranchBo branchBo) {
        Long branchId = branchBo.getBranchId();
        if (ObjectUtils.isEmpty(branchId)) {
            return false;
        }
        return baseMapper
            .update(null,
                Wrappers.<Branch>lambdaUpdate()
                    .set(branchBo.getSelfStudySystemStatus()!=null,Branch::getSelfStudySystemStatus, branchBo.getSelfStudySystemStatus())
                    .set(branchBo.getStudentSpeakingStatus()!=null,Branch::getStudentSpeakingStatus, branchBo.getStudentSpeakingStatus())
                    .set(branchBo.getSmartPrimarySecondarySchoolStatus()!=null,Branch::getSmartPrimarySecondarySchoolStatus, branchBo.getSmartPrimarySecondarySchoolStatus())
                    .set(branchBo.getSpeedControlStatus()!=null,Branch::getSpeedControlStatus, branchBo.getSpeedControlStatus())
                    .set(branchBo.getPhotoStatus()!=null,Branch::getPhotoStatus, branchBo.getPhotoStatus())
                    .eq(Branch::getBranchId, branchId)) > 0;
    }

    @Override
    public List<BranchVo> queryBriefInfoList(BranchBo branchBo) {
        LambdaQueryWrapper<Branch> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollectionUtils.isNotEmpty(branchBo.getCreateDeptIds()), BaseEntity::getCreateDept,branchBo.getCreateDeptIds());
        wrapper.select(Branch::getBranchId,BaseEntity::getCreateDept);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public String getCommonPayAppId(Long branchId) {
        Branch branch = queryBranchById(branchId);
        return branch == null ? null : remotePayMerchantConfigService.getCommonPayAppId(branch.getCreateDept());
    }

    @Override
    public Boolean transfer(Long branchId, Integer amount) {
        return baseMapper.updateMoney(branchId, amount) > 0;
    }

    private void putStatisticsInfo(List<BranchVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        //先初始化，保证前端有值
        for (BranchVo record : records) {
            record.setMemberCount(0L);
            record.setTotalMemberCount(0L);
            record.setFormalRenewCount(0L);
            record.setFormalConversionCount(0L);
            record.setFormalConversionRate(BigDecimal.ZERO);
            record.setOnlineLearnCount(0L);
            record.setFeedbackCount(0L);
            record.setOrderCount(0L);
            record.setRefundOrderCount(0L);
            record.setOrderRevenue(BigDecimal.ZERO);
        }

        List<Long> branchIdList = records.stream().map(BranchVo::getBranchId).toList();
        //查询会员顾问
        RemoteStaffBo remoteStaffBo = new RemoteStaffBo();
        remoteStaffBo.setRoleIds(Collections.singletonList(StaffRole.MEMBER_CONSULTANT.getRoleId()));
        remoteStaffBo.setBranchIdList(branchIdList);
        List<RemoteStaffVo> memberConsultantVoList = remoteStaffService.queryStaffList(remoteStaffBo);
        Map<Long, List<RemoteStaffVo>> memberConsultantMap = StreamUtils.groupByKey(CollUtil.isEmpty(memberConsultantVoList) ? List.of() : memberConsultantVoList, RemoteStaffVo::getBranchId);

        //查门店下会员
        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setBranchIdList(branchIdList);
        List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
        Map<Long, List<RemoteStudentVo>> studentMap = StreamUtils.groupByKey(CollUtil.isEmpty(remoteStudentVos) ? List.of() : remoteStudentVos, RemoteStudentVo::getBranchId);
        List<Long> studentIdList = remoteStudentVos.stream().map(RemoteStudentVo::getStudentId).toList();
        studentIdList = CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList;

        //查销售顾问
        RemoteStaffBo staffBo = new RemoteStaffBo();
        staffBo.setRoleIds(Collections.singletonList(StaffRole.SALES_CONSULTANT.getRoleId()));
        staffBo.setBranchIdList(branchIdList);
        List<RemoteStaffVo> salesConsultantVoList = remoteStaffService.queryStaffList(staffBo);
        Map<Long, List<RemoteStaffVo>> salesConsultantMap = StreamUtils.groupByKey(CollUtil.isEmpty(salesConsultantVoList) ? List.of() : salesConsultantVoList, RemoteStaffVo::getBranchId);

        List<Long> salesPersonIdList = CollUtil.isEmpty(salesConsultantMap) ? List.of(-1L) : salesConsultantVoList.stream().map(RemoteStaffVo::getBranchStaffId).toList();
        //查已支付的订单
        RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
        remoteOrderBo.setSalesPersonIdList(salesPersonIdList);
        remoteOrderBo.setOrderStatus(OrderStatusEnum.PAYED.getCode());
        List<RemoteOrderVo> payedOrderVoList = remoteOrderService.selectOrderListAndInfo(remoteOrderBo, true);
        payedOrderVoList = CollUtil.isEmpty(payedOrderVoList) ? List.of() : payedOrderVoList;

        //查退款订单
        RemoteOrderBo refundOrderBo = new RemoteOrderBo();
        refundOrderBo.setSalesPersonIdList(salesPersonIdList);
        refundOrderBo.setOrderStatus(OrderStatusEnum.REFUNDED.getCode());
        List<RemoteOrderVo> refundOrderVoList = remoteOrderService.selectOrderListAndInfo(refundOrderBo, true);
        refundOrderVoList = CollUtil.isEmpty(refundOrderVoList) ? List.of() : refundOrderVoList;

        //获取体验类型的产品
        RemoteStudentTypeVo experienceStudentType = remoteStudentTypeService.getExperienceStudentType(true);
        List<Long> experienceProductIdList = new ArrayList<>();
        if (experienceStudentType != null) {
            RemoteProductBo productBo = new RemoteProductBo();
            productBo.setStudentTypeId(experienceStudentType.getStudentTypeId());
            List<RemoteProductVo> list = remoteProductService.queryProductList(productBo, true);
            if (CollUtil.isNotEmpty(list)) {
                experienceProductIdList.addAll(list.stream().map(RemoteProductVo::getProductId).toList());
            }
        }

        //查学习记录
        RemoteStudyVideoRecordBo remoteStudyVideoRecordBo = new RemoteStudyVideoRecordBo();
        remoteStudyVideoRecordBo.setStudentIdList(studentIdList);
        remoteStudyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
        remoteStudyVideoRecordBo.setGeStudyVideoDuration(0L);
        List<RemoteStudyVideoRecordVo> remoteStudyVideoRecordVos = remoteStudyVideoRecordService.queryList(remoteStudyVideoRecordBo, true);
        remoteStudyVideoRecordVos = CollUtil.isEmpty(remoteStudyVideoRecordVos) ? List.of() : remoteStudyVideoRecordVos;

        //查反馈
        RemoteFeedbackRecordBo remoteFeedbackRecordBo = new RemoteFeedbackRecordBo();
        remoteFeedbackRecordBo.setStudentIdList(studentIdList);
        List<RemoteFeedbackRecordVo> remoteFeedbackRecordVos = remoteFeedbackRecordService.queryList(remoteFeedbackRecordBo, true);


        //开始统计数据

        for (BranchVo record : records) {
            //会员顾问数
            List<RemoteStaffVo> memberConsultantList = memberConsultantMap.get(record.getBranchId());
            record.setMemberCount((long) CollUtil.size(memberConsultantList));

            //会员总数
            List<RemoteStudentVo> studentList = studentMap.get(record.getBranchId());
            studentList = CollUtil.isEmpty(studentList) ? List.of() : studentList;
            record.setTotalMemberCount((long) CollUtil.size(studentList));
            List<Long> branchStudentIdList = studentList.stream().map(RemoteStudentVo::getStudentId).toList();

            //正式会员续费数
            //销售顾问
            List<RemoteStaffVo> salesConsultantList = salesConsultantMap.get(record.getBranchId());
            salesConsultantList = CollUtil.isEmpty(salesConsultantList) ? List.of() : salesConsultantList;
            List<Long> list = salesConsultantList.stream().map(RemoteStaffVo::getBranchStaffId).toList();


            //过滤出本门店订单
            List<RemoteOrderVo> branchOrderList = payedOrderVoList.stream().filter(e -> e.getSalesPerson() != null && list.contains(e.getSalesPerson())).toList();
            branchOrderList = CollUtil.isEmpty(branchOrderList) ? List.of() : branchOrderList;
            List<RemoteOrderVo> branchRefundOrderList = refundOrderVoList.stream().filter(e -> e.getSalesPerson() != null && list.contains(e.getSalesPerson())).toList();
            branchRefundOrderList = CollUtil.isEmpty(branchRefundOrderList) ? List.of() : branchRefundOrderList;

            //过滤出续费订单（存在多个以支付订单的）
            Map<Long, Long> studentIdCountMap = branchOrderList.stream().collect(Collectors.groupingBy(RemoteOrderVo::getStudentId, Collectors.counting()));
            long formalRenewCount = studentIdCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .count();
            record.setFormalRenewCount(formalRenewCount);

            //正式会员转化人数
            //转化人数就是有非体验类型的会员卡而且会员卡没有过期的人
            long formalConversionCount = branchOrderList.stream().filter(e -> {
                List<RemoteOrderProductInfoVo> orderProductInfoList = e.getOrderProductInfoList();
                if (CollUtil.isEmpty(orderProductInfoList)) {
                    return false;
                }
                return orderProductInfoList.stream().noneMatch(e1 -> experienceProductIdList.contains(e1.getProductId()));
            }).filter(order -> {
                //在有效期内
                Date calTime = new Date();
                for (RemoteOrderProductInfoVo orderProductInfoVo : order.getOrderProductInfoList()) {
                    String productValidTimeLimit = orderProductInfoVo.getProductValidTimeLimit();
                    Long productValidDays = orderProductInfoVo.getProductValidDays();
                    Date payTime = order.getOrderOperate().getCreateTime();
                    try {
                        if (StringUtils.isNotBlank(productValidTimeLimit)) {
                            String[] split = productValidTimeLimit.split(" 至 ");
                            Date startDate = DateUtils.parseDate(split[0]);
                            Date endDate = DateUtils.parseDate(split[1]);
                            if (startDate.getTime() <= calTime.getTime() && endDate.getTime() >= calTime.getTime()) {
                                return true;
                            }
                        } else if (productValidDays != null) {
                            long time = payTime.getTime();
                            Date endDate = new Date(time + productValidDays * 24 * 60 * 60 * 1000);
                            if (time <= calTime.getTime() && endDate.getTime() >= calTime.getTime()) {
                                return true;
                            }
                        } else {
                            throw new ServiceException("产品有效期限为空");
                        }
                    } catch (Exception e) {
                        throw new ServiceException("解析产品有效期限失败");
                    }
                }
                return false;
            }).map(RemoteOrderVo::getStudentId).distinct().count();

            record.setFormalConversionCount(formalConversionCount);


            //正式会员转化率
            //总会员数
            BigDecimal formalConversionRate = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(studentList)) {
                formalConversionRate = new BigDecimal(formalConversionCount).divide(new BigDecimal(studentList.size()), 2, RoundingMode.HALF_UP);
            }
            record.setFormalConversionRate(formalConversionRate);

            List<RemoteStudyVideoRecordVo> studyVideoRecordVoList = remoteStudyVideoRecordVos.stream().filter(e -> branchStudentIdList.contains(e.getStudentId())).toList();
            studyVideoRecordVoList = CollUtil.isEmpty(studyVideoRecordVoList) ? List.of() : studyVideoRecordVoList;
            //在线学习人数
            long onlineLearnCount = studyVideoRecordVoList.stream().map(RemoteStudyVideoRecordVo::getStudentId).distinct().count();
            record.setOnlineLearnCount(onlineLearnCount);

            //总学习天数
            long totalStudyDays = studyVideoRecordVoList.stream().map(e -> DateUtils.dateTime(e.getCreateTime())).distinct().count();
            //总学习时长
            long totalStudyDuration = studyVideoRecordVoList.stream().mapToLong(e -> ObjectUtil.defaultIfNull(e.getStudyVideoDuration(), 0L)).sum();
            //每日平均学习时长
            long dailyAverageStudyDuration = 0;
            if (totalStudyDays > 0) {
                dailyAverageStudyDuration = totalStudyDuration / totalStudyDays;
            }
            record.setDailyLearnTime(dailyAverageStudyDuration);

            //学习反馈次数
            long feedbackCount = remoteFeedbackRecordVos.stream().filter(e -> branchStudentIdList.contains(e.getStudentId())).count();
            record.setFeedbackCount(feedbackCount);

            //订单总数

            long orderCount = branchOrderList.size();
            long refundOrderCount = branchRefundOrderList.size();

            record.setOrderCount(orderCount + refundOrderCount);

            //退款订单总数
            record.setRefundOrderCount(refundOrderCount);

            //订单营收总额
            BigDecimal reduce = branchOrderList.stream().map(e -> {
                RemoteOrderOperateVo orderOperate = e.getOrderOperate();
                if (orderOperate == null || orderOperate.getPaymentAmount() == null) {
                    return BigDecimal.ZERO;
                }
                return orderOperate.getPaymentAmount();
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            record.setOrderRevenue(reduce);

        }


    }

    @Override
    public List<BranchVo> branchOptionList() {
        return baseMapper.selectBranchList(new LambdaQueryWrapper<>());
    }


    public void putBranchAdminInfo(List<BranchVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<BranchStaffBranchRelation> branchStaffBranchRelations = getBranchStaffBranchRelations(list);
        if (CollUtil.isEmpty(branchStaffBranchRelations)){
            return;
        }
        List<Long> userIds = branchStaffBranchRelations.stream().map(BranchStaffBranchRelation::getUserId).distinct().toList();
        Long roleId = StaffRole.EXECUTIVE_STORE_MANAGER.getRoleId();
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(userIds);
        remoteUserBo.setRoleIds(new Long[]{roleId});

        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);

        Map<Long, RemoteUserVo> remoteUserVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));

        Map<Long, List<BranchStaffBranchRelation>> branchIdToBranchStaffBranchRelationListMap =
            branchStaffBranchRelations.stream().collect(Collectors.groupingBy(BranchStaffBranchRelation::getBranchId));
        //获取只有执行店长的用户id
        for (BranchVo branchVo : list) {
            List<BranchStaffBranchRelation> branchStaffBranchRelationList = branchIdToBranchStaffBranchRelationListMap.get(branchVo.getBranchId());
            if (CollUtil.isEmpty(branchStaffBranchRelationList)) {
                continue;
            }
            List<RemoteUserVo> branchAdminList = branchStaffBranchRelationList.stream().map(branchStaffBranchRelation -> {
                Long userId = branchStaffBranchRelation.getUserId();
                return remoteUserVoMap.get(userId);
            }).filter(Objects::nonNull).toList();
            branchVo.setAdminUserName(branchAdminList.stream().map(RemoteUserVo::getUserName).collect(Collectors.joining(",")));
            branchVo.setAdminNickName(branchAdminList.stream().map(RemoteUserVo::getNickName).collect(Collectors.joining(",")));
        }
    }

    private @NotNull List<BranchStaffBranchRelation> getBranchStaffBranchRelations(List<BranchVo> list) {
        List<BranchStaffBranchRelation> branchStaffBranchRelations = branchStaffBranchRelationMapper
            .selectList(new LambdaQueryWrapper<BranchStaffBranchRelation>().
                in(BranchStaffBranchRelation::getBranchId, list.stream().map(BranchVo::getBranchId).collect(Collectors.toSet())));
        if (CollUtil.isEmpty(branchStaffBranchRelations)) {
            return Lists.newArrayList();
        }
        return branchStaffBranchRelations;
    }

    public Collection<?> buildBranchAdminSearchCondition(String adminNickName, String adminUserName) {
        if (StringUtils.isBlank(adminNickName) && StringUtils.isBlank(adminUserName)) {
            return null;
        }
        Set<Long> adminBranchDeptIds = remoteUserService.getAdminBranchDeptIds(adminUserName, adminNickName);
        return CollUtil.isEmpty(adminBranchDeptIds) ? Collections.singleton(-1) : adminBranchDeptIds;
    }

    /**
     * 查询分店列表
     */
    @Override
    public List<BranchVo> queryList(BranchBo bo) {
        QueryWrapper<Branch> lqw = buildQueryWrapper(bo);
        List<BranchVo> branchVos = baseMapper.selectBranchList(lqw);
        putBranchAdminInfo(branchVos);
        putBranchParentDept(branchVos);
        return branchVos;
    }

    private LambdaQueryWrapper<Branch> buildLambdaQueryWrapper(BranchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Branch> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getBranchName()), Branch::getBranchName, bo.getBranchName());
        lqw.eq(StringUtils.isNotBlank(bo.getBranchStatus()), Branch::getBranchStatus, bo.getBranchStatus());
        lqw.eq(bo.getBranchAuthRecordId() != null, Branch::getBranchAuthRecordId, bo.getBranchAuthRecordId());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIds()), Branch::getBranchId, bo.getBranchIds());
        return lqw;
    }

    private QueryWrapper<Branch> buildQueryWrapper(BranchBo bo) {
        QueryWrapper<Branch> lqw = Wrappers.query(Branch.class);
        lqw.eq(bo.getBranchId() != null, "b.branch_id", bo.getBranchId());
        lqw.like(StringUtils.isNotBlank(bo.getBranchName()), "b.branch_name", bo.getBranchName());
        lqw.eq(StringUtils.isNotBlank(bo.getBranchStatus()), "b.branch_status", bo.getBranchStatus());
        lqw.eq(bo.getBranchAuthRecordId() != null, "b.branch_auth_record_id", bo.getBranchAuthRecordId());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIds()), "b.branch_id", bo.getBranchIds());
        lqw.in(StringUtils.isNotBlank(bo.getAdminNickName()) || StringUtils.isNotBlank(bo.getAdminUserName()),
            "b.create_dept",
            buildBranchAdminSearchCondition(bo.getAdminNickName(), bo.getAdminUserName()));
        if (!ObjectUtils.isEmpty(bo.getDeptParentId())) {
            lqw.in("b.create_dept", buildBranchSearchCondition(bo.getDeptParentId()));
        }
        lqw.eq(bo.getBranchStatus() != null, "b.branch_status", bo.getBranchStatus());
        lqw.in(CollectionUtils.isNotEmpty(bo.getCreateDeptIds()), "b.create_dept", bo.getCreateDeptIds());
        lqw.orderByDesc("b.create_time");
        return lqw;
    }

    private Collection<?> buildBranchSearchCondition(Long deptParentId) {

        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setParentId(deptParentId);
        remoteDeptBo.setIsStore(true);
        List<RemoteDeptVo> selfAndChildShopList = remoteDeptService.getDeptList(remoteDeptBo);
        if (org.springframework.util.CollectionUtils.isEmpty(selfAndChildShopList)) {
            return Collections.singleton(-1);
        }
        List<Long> branchDeptIds = selfAndChildShopList.stream().map(RemoteDeptVo::getDeptId).toList();

        return CollUtil.isEmpty(branchDeptIds) ? Collections.singleton(-1) : branchDeptIds;
    }

    /**
     * 新增分店
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(BranchBo bo) {
        try {
            validEntityBeforeSave(bo);
            //新建分店对应的部门
            RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
            remoteDeptBo.setDeptName(bo.getBranchName());
            remoteDeptBo.setParentId(bo.getDeptParentId());
            remoteDeptBo.setIsStore(true);
            remoteDeptBo.setStatus(UserConstants.DEPT_NORMAL);
            remoteDeptBo.setDelFlag(UserConstants.DEL_FLAG_NO);
            Long shopDeptId = remoteDeptService.insertDept(remoteDeptBo);
            if (shopDeptId == null) {
                throw new ServiceException("新增分店失败");
            }

            //20250116 新增门店不强行绑定门店管理员
            //20250516 新增门店不再绑定门店管理员


            //新建门店
            Branch addBean = MapstructUtils.convert(bo, Branch.class);
            addBean.setBranchStatus(UserConstants.NORMAL);
            addBean.setCreateDept(shopDeptId);
            boolean flag = baseMapper.insert(addBean) > 0;
            if (flag) {
                bo.setBranchId(addBean.getBranchId());
            }
            //查看门店是否绑定执行店长

            List<Long> branchStaffIdList = bo.getBranchStaffIdList();
            if (CollUtil.isNotEmpty(branchStaffIdList)) {
                saveOrUpdateBranchStaffRelation(branchStaffIdList, addBean);
            }


            //2B2C合并，门店授权直接继承上级组织的授权，不再直接授权
//            //如果新增时选择了授权类型，那么创建记录和授权类型关联
//            if (null != bo.getBranchAuthTypeId()) {
//                self.branchAddAuthType(addBean.getBranchId(), bo.getBranchAuthTypeId());
//            }
            //初始化物料
            materialService.branchInitMaterial(addBean.getBranchId());
            //初始化门店积分任务
            //20250326废弃
//            integralTaskService.initIntegralTask(addBean.getBranchId());
            return flag;
        } catch (Exception e) {
            log.error("新增分店有误");
            throw new ServiceException(e.getMessage());
        }

    }

    private void saveOrUpdateBranchStaffRelation(List<Long> branchStaffIdList, Branch addBean) {
        RemoteStaffBo branchStaffBo = new RemoteStaffBo();
        branchStaffBo.setBranchStaffIds(branchStaffIdList);
        List<RemoteStaffVo> remoteStaffVos = remoteStaffService.queryStaffList(branchStaffBo);

        branchStaffBranchRelationMapper
            .delete(new LambdaQueryWrapper<BranchStaffBranchRelation>()
            .eq(BranchStaffBranchRelation::getBranchId, addBean.getBranchId()));
        branchStaffBranchRelationMapper.insertBatch(remoteStaffVos.stream().map(staffVo -> {
            BranchStaffBranchRelation branchStaffBranchRelation = new BranchStaffBranchRelation();
            branchStaffBranchRelation.setBranchStaffId(staffVo.getBranchStaffId());
            branchStaffBranchRelation.setBranchId(addBean.getBranchId());
            branchStaffBranchRelation.setUserId(staffVo.getCreateBy());
            return branchStaffBranchRelation;
        }).collect(Collectors.toList()));
    }


    /**
     * 分店添加授权类型
     *
     * @param branchId         分店id
     * @param branchAuthTypeId 授权类型id
     * @date 2024/02/23 02:24:27
     */
    public void branchAddAuthType(Long branchId, Long branchAuthTypeId) {
        // 修改授权类型，并添加记录
        BranchAuthTypeVo branchAuthTypeVo = branchAuthTypeService.queryById(branchAuthTypeId);
        if (branchAuthTypeVo == null) {
            throw new ServiceException("授权类型不存在");
        }
        BranchAuthRecordBo branchAuthRecordBo = new BranchAuthRecordBo();
        branchAuthRecordBo.setBranchId(branchId);
        branchAuthRecordBo.setBranchAuthTypeId(branchAuthTypeId);
        branchAuthRecordBo.setBranchAuthTypeName(branchAuthTypeVo.getBranchAuthTypeName());
        branchAuthRecordBo.setBranchAuthTypeInfo(branchAuthTypeVo.getBranchAuthTypeInfo());
        branchAuthRecordBo.setBranchAuthTypeDays(branchAuthTypeVo.getBranchAuthTypeDays());
        branchAuthRecordBo.setBranchAuthTypeCost(branchAuthTypeVo.getBranchAuthTypeCost());
        branchAuthRecordBo.setBranchAuthStartTime(new Date());
        branchAuthRecordService.insertByBo(branchAuthRecordBo);

        //更新门店的授权记录id
        Branch branch = new Branch();
        branch.setBranchId(branchId);
        branch.setBranchAuthRecordId(branchAuthRecordBo.getBranchAuthRecordId());
        baseMapper.updateById(branch);
    }

    @Override
    public void restPwd(List<Long> branchAdminIdList) {
        if (CollUtil.isNotEmpty(branchAdminIdList)) {
            branchAdminIdList.removeIf(Objects::isNull);
            if (CollUtil.isEmpty(branchAdminIdList)) {
                return;
            }
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setUserIds(branchAdminIdList);
            List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
            if (CollUtil.isEmpty(remoteUserVos)) {
                return;
            }
            List<RemoteUserBo> remoteUserBos = new ArrayList<>();
            for (RemoteUserVo remoteUserVo : remoteUserVos) {
                RemoteUserBo updateBean = new RemoteUserBo();
                updateBean.setUserId(remoteUserVo.getUserId());
                updateBean.setPassword(generatePassword(remoteUserVo.getUserName()));
                remoteUserBos.add(updateBean);
            }
            remoteUserService.batchUpdateUserInfo(remoteUserBos);
        }
    }


    /**
     * 修改分店
     */
    @Override
    public Boolean updateByBo(BranchBo bo) {

        Branch update = MapstructUtils.convert(bo, Branch.class);
        if (Objects.isNull(update)){
            return false;
        }
        validEntityBeforeSave(bo);

        Branch branch = baseMapper.selectById(bo.getBranchId());
        if (branch == null) {
            throw new ServiceException("分店不存在");
        }
        Long createDept = branch.getCreateDept();

        //更新分店对应的部门
        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setDeptName(bo.getBranchName());
        remoteDeptBo.setParentId(bo.getDeptParentId());
        remoteDeptBo.setDeptId(createDept);
        remoteDeptService.updateDept(remoteDeptBo);

        List<Long> branchStaffIdList = bo.getBranchStaffIdList();
        if (CollUtil.isNotEmpty(branchStaffIdList)) {
            saveOrUpdateBranchStaffRelation(branchStaffIdList, update);
        }

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchBo entity) {
        //做一些数据校验,如唯一约束

        //校验分店名称是否存在
        String branchName = entity.getBranchName();
        if (StringUtils.isNotBlank(branchName)) {
            //因为跟部门表有关联，所以这里需要校验部门表中是否存在相同的部门名称
            boolean b = remoteDeptService.checkDeptNameUnique(entity.getCreateDept(), branchName, entity.getDeptParentId());
            if (!b) {
                throw new ServiceException("分店名称在该父组织下已存在");
            }
        }

    }

    /**
     * 批量删除分店
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 按照门店授权ids，获取未过期的正常状态的门店列表
     *
     * @param authTypeIds 授权类型id
     * @date 2024/02/22 05:01:39
     */
    @Override
    public List<BranchVo> queryUnexpiredByAuthTypeIds(List<Long> authTypeIds) {
        return baseMapper.queryUnexpiredByAuthTypeIds(authTypeIds);
    }

    private String generatePassword(String username) {
        return BCrypt.hashpw(StringUtils.substring(username, username.length() - 6));
    }

    @Override
    public List<BranchVo> selectSimpleBranInfo(BranchBo bo) {
        QueryWrapper<Branch> branchQueryWrapper = buildQueryWrapper(bo);
        List<BranchVo> branchVos = baseMapper.selectBranchList(branchQueryWrapper);
        putBranchAdminInfo(branchVos);
        return branchVos;

    }


    @Cacheable(value = "branch", key = "#branchId", condition = "#branchId != null")
    @Override
    public Branch queryBranchById(Long branchId) {
        return baseMapper.selectById(branchId);
    }


    @CacheEvict(value = "branch", allEntries = true)
    public void cleanCache() {
        log.info("===========branchService cleanCache===========");
    }


    @Override
    public void init() {
        IBranchService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchService init===========");
        LambdaQueryWrapper<Branch> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Branch::getBranchId);
        List<Branch> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========branchService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchById(item.getBranchId());
        });
        log.info("===========branchService init end===========");
    }

    @Override
    public List<BranchVo> queryStaffBranchList(Long userId) {
        List<Long> branchIds = new ArrayList<>();
        RemoteStaffVo remoteStaffVo = remoteStaffService.queryStaffByUserId(userId);

        if (remoteStaffVo != null) {
            branchIds.add(remoteStaffVo.getBranchId());
        }

        List<BranchStaffBranchRelation> branchStaffBranchRelations =
            branchStaffBranchRelationMapper.selectList(new LambdaQueryWrapper<BranchStaffBranchRelation>()
                .eq(BranchStaffBranchRelation::getUserId, userId));
        if (!CollectionUtils.isEmpty(branchStaffBranchRelations)) {
            List<Long> branchStaffIds = branchStaffBranchRelations.stream().map(BranchStaffBranchRelation::getBranchId).distinct().toList();
            branchIds.addAll(branchStaffIds);
            branchIds = branchIds.stream().distinct().toList();
        }

        if (CollectionUtils.isEmpty(branchIds)){
            return new ArrayList<>();
        }
        return baseMapper.selectVoList(new LambdaQueryWrapper<Branch>()
            .in(Branch::getBranchId, branchIds)
            .eq(Branch::getBranchStatus, UserConstants.BRANCH_NORMAL));


    }

    @Override
    public BranchVo getBranchInfoByCreateDept(Long deptId) {
        return baseMapper.selectVoOne(Wrappers.lambdaQuery(Branch.class).eq(BaseEntity::getCreateDept, deptId)
            .select(Branch::getBranchId).last("limit 1 "));
    }

    @Override
    public RemotePayMerchantConfig getCommonPayConfig(Long branchId) {
        Branch branch = queryBranchById(branchId);
        return remotePayMerchantConfigService.getCommonPayConfig(branch.getCreateDept());
    }

    @Override
    public List<Long> listBranchId(BranchBo branchBo) {
        QueryWrapper<Branch> branchQueryWrapper = buildQueryWrapper(branchBo);
        List<BranchVo> branchVos = baseMapper.selectBranchIdList(branchQueryWrapper);
        return CollUtil.isEmpty(branchVos) ? new ArrayList<>()
            : branchVos.stream().map(BranchVo::getBranchId).collect(Collectors.toList());
    }

    @Override
    public List<BranchVo> queryByCreateDept(Long deptId) {
        return baseMapper.selectByCreateDept(deptId);
    }

}
