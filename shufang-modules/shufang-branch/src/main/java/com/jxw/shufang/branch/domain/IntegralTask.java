package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 积分任务对象 integral_task
 *
 *
 * @date 2024-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_task")
public class IntegralTask extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分任务id
     */
    @TableId(value = "integral_task_id")
    private Long integralTaskId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 积分任务名称 $为占位符，查询的时候需要把他换成字段param
     */
    private String integralTaskName;

    /**
     * 参数，触发器和任务名称会使用到他
     */
    private String param;

    /**
     * 积分数值（正整数）
     */
    private Integer integralNum;

    /**
     * 对应程序中的触发器
     */
    private String taskTrigger;

    /**
     * 任务上下架状态（1上架 2下架）
     */
    private String taskStatus;

}
