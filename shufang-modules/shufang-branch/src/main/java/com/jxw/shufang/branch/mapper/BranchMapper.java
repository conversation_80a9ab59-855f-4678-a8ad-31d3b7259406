package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 分店Mapper接口
 *
 *
 * @date 2024-02-21
 */
public interface BranchMapper extends BaseMapperPlus<Branch, BranchVo> {

    List<BranchVo> queryUnexpiredByAuthTypeIds(List<Long> authTypeIds);


    @DataPermission({
        @DataColumn(key = "deptName", value = "b.create_dept"),
        @DataColumn(key = "userName", value = "b.create_by")
    })
    @BranchColumn(key = "deptName", value = "b.create_dept")
    Page<BranchVo> selectPageBranchList(@Param("page") Page<Branch> page, @Param(Constants.WRAPPER) Wrapper<Branch> queryWrapper);

    @DataPermission({
        @DataColumn(key = "deptName", value = "b.create_dept"),
        @DataColumn(key = "userName", value = "b.create_by")
    })
    @BranchColumn(key = "deptName", value = "b.create_dept")
    List<BranchVo> selectBranchList( @Param(Constants.WRAPPER) Wrapper<Branch> queryWrapper);

    int updateMoney(@Param("branchId") Long branchId, @Param("amount") Integer amount);


    List<BranchVo> selectBranchIdList(@Param(Constants.WRAPPER) Wrapper<Branch> queryWrapper);

    List<BranchVo> selectByCreateDept(Long deptId);

}
