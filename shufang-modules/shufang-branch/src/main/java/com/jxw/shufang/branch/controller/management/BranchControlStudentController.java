package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.branch.domain.bo.BranchControlStudentBo;
import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.branch.service.BranchControlStudentService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@RestController
@RequestMapping("/management/branch-control-student")
@RequiredArgsConstructor
@Slf4j
public class BranchControlStudentController extends BaseController {

    private final BranchControlStudentService branchControlStudentService;


    /**
     * 添加管控学生白名单
     */
    @PostMapping("/add")
    @SaCheckPermission("branch:branchControlStudent:add")
    @Log(title = "添加管控学生白名单", businessType = BusinessType.INSERT)
    public R<Void> insertByBo(@RequestBody BranchControlStudentBo branchControlStudentBo) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("请选择对应的门店进行操作");
        }
        branchControlStudentBo.setBranchId(branchId);
        return toAjax(branchControlStudentService.insertByBo(branchControlStudentBo));
    }

    /**
     * 删除管控学生白名单
     */
    @PostMapping("/remove")
    @SaCheckPermission("branch:branchControlStudent:remove")
    @Log(title = "删除管控学生白名单", businessType = BusinessType.DELETE)
    public R<Void> remove(@RequestBody List<Long> branchControlStudentIds) {
        return toAjax(branchControlStudentService.deleteBatchIds(branchControlStudentIds));
    }

    /**
     * 分页查询管控学生白名单
     */
    @PostMapping("/pageList")
    public R<TableDataInfo<BranchControlStudentVo>> queryPageList(PageQuery pageQuery,@RequestBody BranchControlStudentBo branchControlStudentBo) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("请选择对应的门店进行操作");
        }
        branchControlStudentBo.setBranchId(branchId);
        return R.ok(branchControlStudentService.queryPageList(pageQuery,branchControlStudentBo));
    }


    /**
     *  根据管控模板检查学生是否存在白名单
     *  true:白名单，false:不在白名单
     */
    @PostMapping("/checkStudentInControlTemplate")
    public R<Boolean> checkStudentInControlTemplate(@RequestBody BranchControlStudentBo branchControlStudentBo) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("请选择对应的门店进行操作");
        }
        branchControlStudentBo.setBranchId(branchId);
        return R.ok(branchControlStudentService.checkStudentInControlTemplate(branchControlStudentBo));
    }

}
