package com.jxw.shufang.branch.wrapper;

import com.jxw.shufang.branch.config.IntegralTaskDateMappingProperties;
import com.jxw.shufang.branch.enums.ScheduleIntegralEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @author: cyj
 * @date: 2025/7/24
 */
@Component
@RequiredArgsConstructor
public class IntegralDateMappingWrapper {
    private final IntegralTaskDateMappingProperties properties;

    public ScheduleIntegralEnum getScheduleEnumByDate(Integer dayOfMonth) {
        String enumName = null;
        if (properties.getBegin().getDayOfMonth().equals(dayOfMonth)) {
            enumName = properties.getBegin().getEnumName();
        } else if (properties.getMiddle().getDayOfMonth().equals(dayOfMonth)) {
            enumName = properties.getMiddle().getEnumName();
        } else if (properties.getEnd().getDayOfMonth().equals(dayOfMonth)) {
            enumName = properties.getEnd().getEnumName();
        }
        return enumName != null ? ScheduleIntegralEnum.valueOf(enumName) : null;
    }
}
