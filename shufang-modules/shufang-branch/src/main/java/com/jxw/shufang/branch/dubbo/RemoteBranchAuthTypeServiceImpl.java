package com.jxw.shufang.branch.dubbo;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.domain.bo.BranchAuthTypeBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import com.jxw.shufang.branch.service.IBranchAuthRecordService;
import com.jxw.shufang.branch.service.IBranchAuthTypeService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBranchAuthTypeServiceImpl implements RemoteBranchAuthTypeService {

    private final IBranchAuthRecordService branchAuthRecordService;

    private final IBranchAuthTypeService branchAuthTypeService;



    @Override
    public RemoteBranchAuthTypeVo getAuthTypeByBranchId(Long branchId) throws ServiceException {
        BranchAuthRecordVo branchAuthRecordVo = branchAuthRecordService.queryLastAuthRecordByBranchId(branchId);
        if (ObjectUtil.isEmpty(branchAuthRecordVo)){
            return null;
        }
        //是否过期
        if(System.currentTimeMillis() > branchAuthRecordVo.getBranchAuthStartTime().getTime()+branchAuthRecordVo.getBranchAuthTypeDays()*86400000L) {
            return null;
        }

        BranchAuthTypeVo branchAuthTypeVo = branchAuthTypeService.queryById(branchAuthRecordVo.getBranchAuthTypeId());

        return  MapstructUtils.convert(branchAuthTypeVo, RemoteBranchAuthTypeVo.class);
    }

    @Override
    public List<RemoteBranchAuthTypeVo> getAuthTypeListByBranchIdList(List<Long> branchIdList) throws ServiceException {
        List<BranchAuthRecordVo> branchAuthRecordVoList = branchAuthRecordService.queryLastAuthRecordListByBranchIdList(branchIdList);
        if (ObjectUtil.isEmpty(branchAuthRecordVoList)){
            return null;
        }
        //是否过期
        List<Long> list = branchAuthRecordVoList.stream()
            .filter(branchAuthRecordVo -> !(System.currentTimeMillis() > branchAuthRecordVo.getBranchAuthStartTime().getTime() + branchAuthRecordVo.getBranchAuthTypeDays() * 86400000L))
            .map(BranchAuthRecordVo::getBranchAuthTypeId).filter(Objects::nonNull).distinct().toList();
        if (ObjectUtil.isEmpty(list)){
            return null;
        }

        BranchAuthTypeBo branchAuthTypeBo = new BranchAuthTypeBo();
        branchAuthTypeBo.setBranchAuthTypeIdList(list);
        List<BranchAuthTypeVo> branchAuthTypeVos = branchAuthTypeService.queryList(branchAuthTypeBo);
        if (ObjectUtil.isEmpty(branchAuthTypeVos)){
            return null;
        }

        return  MapstructUtils.convert(branchAuthTypeVos, RemoteBranchAuthTypeVo.class);
    }

}
