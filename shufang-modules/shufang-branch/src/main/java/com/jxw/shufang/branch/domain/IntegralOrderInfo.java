package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 积分订单详情对象 integral_order_info
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_order_info")
public class IntegralOrderInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单详情id
     */
    @TableId(value = "integral_order_info_id")
    private Long integralOrderInfoId;

    /**
     * 积分订单id
     */
    private Long integralOrderId;

    /**
     * 积分商品id
     */
    private Long integralGoodId;

    /**
     * 积分商品图片（oss_id）
     */
    private Long integralGoodImg;

    /**
     * 积分商品名称
     */
    private String integralGoodName;

    /**
     * 消耗积分
     */
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟）
     */
    private String integralGoodType;


}
