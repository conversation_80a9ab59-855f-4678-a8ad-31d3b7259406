package com.jxw.shufang.branch.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import com.jxw.shufang.common.core.utils.StringUtils;

/**
 * 物料类型枚举
 *
 *
 * @date 2024/02/23 05:52:12
 */
@Getter
@AllArgsConstructor
public enum MaterialTypeEnum {
    START_TRAINING("启动培训"),
    PRODUCT_INTRODUCTION("产品介绍"),
    TRAINING_LECTURE("培训讲座"),
    SALES_TOOL("销售工具"),
    PROMOTIONAL_MATERIAL("宣传资料"),
    MAINTENANCE_TOOL("运维工具");
    private final String type;

    public static MaterialTypeEnum find(String databaseProductName) {
        if (StringUtils.isBlank(databaseProductName)) {
            return null;
        }
        for (MaterialTypeEnum type : values()) {
            if (type.getType().equals(databaseProductName)) {
                return type;
            }
        }
        return null;
    }
}
