package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralOrder;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 积分订单业务对象 integral_order
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralOrder.class, reverseConvertGenerate = false)
public class IntegralOrderBo extends BaseEntity {

    /**
     * 积分订单id
     */
    @NotNull(message = "积分订单id不能为空", groups = { EditGroup.class })
    private Long integralOrderId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 顾问id
     */
    @NotNull(message = "顾问id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long consultantId;

    /**
     * 积分订单编号
     */
    @NotBlank(message = "积分订单编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralOrderNo;

    /**
     * 积分操作id
     */
    @NotNull(message = "积分操作id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralOrderOperateId;

    /**
     * 积分商品id
     */
    private Long integralGoodId;

    /**
     * 订单状态（1待兑换 2已兑换 3已取消）不能为空
     */
    private String integralOrderOperateStatus;

    private List<String> IntegralOrderOperateStatusList;

    private String orderBy;

    private List<Long> studentIdList;

    /**
     * 是否包含兑换者会员信息
     */
    private Boolean withExchangeStudentInfo;


    /**
     * 是否包含核销人信息
     */
    private Boolean withVerifierInfo;


    private String nameWithPhone;

    private String integralGoodName;

}
