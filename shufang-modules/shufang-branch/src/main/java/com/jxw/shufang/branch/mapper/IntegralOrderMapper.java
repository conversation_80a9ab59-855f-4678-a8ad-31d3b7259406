package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.branch.domain.IntegralOrder;
import com.jxw.shufang.branch.domain.vo.IntegralOrderVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 积分订单Mapper接口
 *
 *
 * @date 2024-04-23
 */
public interface IntegralOrderMapper extends BaseMapperPlus<IntegralOrder, IntegralOrderVo> {

    /**
     * 查询积分订单
     *
     * @param lqw 查询条件
     * @return 积分订单集合
     */
    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.create_by")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    List<IntegralOrderVo> queryOrderList(@Param(Constants.WRAPPER) QueryWrapper<IntegralOrder> lqw);

    Page<IntegralOrderVo> queryOrderInfoPage(@Param("page") Page<IntegralOrderVo> build, @Param(Constants.WRAPPER) QueryWrapper<IntegralOrder> lqw);
}
