package com.jxw.shufang.branch.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.domain.BranchControlStudent;
import com.jxw.shufang.branch.domain.bo.BranchControlStudentBo;
import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.branch.mapper.BranchControlStudentMapper;
import com.jxw.shufang.branch.service.BranchControlStudentService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@Service
@RequiredArgsConstructor
public class BranchControlStudentServiceImpl implements BranchControlStudentService {

    private final BranchControlStudentMapper branchControlStudentMapper;

    @Override
    public boolean insertByBo(BranchControlStudentBo branchControlStudentBo) {
        BranchControlStudent convert = MapstructUtils.convert(branchControlStudentBo, BranchControlStudent.class);
        if (convert != null) {
            return branchControlStudentMapper.insert(convert) > 0;
        }
        return false;
    }

    @Override
    public boolean deleteBatchIds(List<Long> branchControlStudentIds) {
        return branchControlStudentMapper.deleteBatchIds(branchControlStudentIds) > 0;
    }

    @Override
    public TableDataInfo<BranchControlStudentVo> queryPageList(PageQuery pageQuery, BranchControlStudentBo branchControlStudentBo) {
        // 模板ID
        Long branchFeatureTemplateId = branchControlStudentBo.getBranchFeatureTemplateId();
        // 门店ID
        Long branchId = branchControlStudentBo.getBranchId();
        if (branchFeatureTemplateId == null) {
            throw new ServiceException("模板ID不能为空");
        }
        if (branchId == null) {
            throw new RuntimeException("门店ID不能为空");
        }

        Page<BranchControlStudentVo> branchControlStudentPage = branchControlStudentMapper.selectQueryPageList(pageQuery.build(), branchFeatureTemplateId, branchId);

        return TableDataInfo.build(branchControlStudentPage);
    }

    @Override
    public boolean checkStudentInControlTemplate(BranchControlStudentBo branchControlStudentBo) {
        Long count = branchControlStudentMapper.selectCount(buildQueryWrapper(branchControlStudentBo));
        return count > 0;
    }

    @Override
    public List<BranchControlStudentVo> selectStudentListByBranchIdAndTemplateId(Long branchId, Long templateId) {
        BranchControlStudentBo branchControlStudentBo = new BranchControlStudentBo();
        branchControlStudentBo.setBranchId(branchId);
        branchControlStudentBo.setBranchFeatureTemplateId(templateId);
        List<BranchControlStudent> branchControlStudents = branchControlStudentMapper.selectList(buildQueryWrapper(branchControlStudentBo));
        return MapstructUtils.convert(branchControlStudents, BranchControlStudentVo.class);
    }

    @Override
    public List<BranchControlStudentVo> queryList(BranchControlStudentBo branchControlStudentBo) {
        List<BranchControlStudent> branchControlStudentList = branchControlStudentMapper.selectList(buildQueryWrapper(branchControlStudentBo));
        return MapstructUtils.convert(branchControlStudentList, BranchControlStudentVo.class);
    }

    public LambdaQueryWrapper<BranchControlStudent> buildQueryWrapper(BranchControlStudentBo branchControlStudentBo) {
        Long branchControlStudentId = branchControlStudentBo.getBranchControlStudentId();
        Long branchFeatureTemplateId = branchControlStudentBo.getBranchFeatureTemplateId();
        Long branchId = branchControlStudentBo.getBranchId();
        Long studentId = branchControlStudentBo.getStudentId();
        Long operateId = branchControlStudentBo.getOperateId();

        LambdaQueryWrapper<BranchControlStudent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(branchControlStudentId != null, BranchControlStudent::getBranchControlStudentId, branchControlStudentId);
        lambdaQueryWrapper.eq(branchFeatureTemplateId != null, BranchControlStudent::getBranchFeatureTemplateId, branchFeatureTemplateId);
        lambdaQueryWrapper.eq(branchId != null, BranchControlStudent::getBranchId, branchId);
        lambdaQueryWrapper.eq(studentId != null, BranchControlStudent::getStudentId, studentId);
        lambdaQueryWrapper.eq(operateId != null, BranchControlStudent::getOperateId, operateId);
        return lambdaQueryWrapper;
    }
}
