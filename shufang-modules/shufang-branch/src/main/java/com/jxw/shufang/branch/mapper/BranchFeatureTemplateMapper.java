package com.jxw.shufang.branch.mapper;

import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;


/**
 * <p>
 * 管控功能配置模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@Mapper
public interface BranchFeatureTemplateMapper extends BaseMapperPlus<BranchFeatureTemplate, BranchFeatureTemplateVo> {


}
