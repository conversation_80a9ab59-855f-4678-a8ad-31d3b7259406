package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("branch_control_student")
public class BranchControlStudent implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "branch_control_student_id")
    private Long branchControlStudentId;

    /**
     * 模板id
     */
    @TableField("branch_feature_template_id")
    private Long branchFeatureTemplateId;

    /**
     * 分店id
     */
    @TableField("branch_id")
    private Long branchId;

    /**
     * 学生id
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 操作顾问id
     */
    @TableField("operate_id")
    private Long operateId;

    /**
     * 操作顾问
     */
    @TableField("operate_name")
    private String operateName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
