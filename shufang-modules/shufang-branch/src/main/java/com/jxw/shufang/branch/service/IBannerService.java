package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.BannerBo;
import com.jxw.shufang.branch.domain.vo.BannerVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 轮播图Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IBannerService {

    /**
     * 查询轮播图
     */
    BannerVo queryById(Long bannerId);

    /**
     * 查询轮播图列表
     */
    TableDataInfo<BannerVo> queryPageList(BannerBo bo, PageQuery pageQuery);

    /**
     * 查询轮播图列表
     */
    List<BannerVo> queryList(BannerBo bo);

    /**
     * 新增轮播图
     */
    Boolean insertByBo(BannerBo bo);

    /**
     * 修改轮播图
     */
    Boolean updateByBo(BannerBo bo);

    /**
     * 校验并批量删除轮播图信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
