package com.jxw.shufang.branch.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.branch.api.RemoteBranchMachineSeatService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchMachineSeatBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;
import com.jxw.shufang.branch.domain.bo.BranchMachineSeatBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.branch.service.IBranchMachineSeatService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBranchMachineSeatServiceImpl implements RemoteBranchMachineSeatService {

    private final IBranchMachineSeatService branchMachineSeatService;

    @Override
    public List<RemoteBranchMachineSeatVo> queryList(RemoteBranchMachineSeatBo remoteBranchMachineSeatBo) {
        BranchMachineSeatBo convert = MapstructUtils.convert(remoteBranchMachineSeatBo, BranchMachineSeatBo.class);
        List<BranchMachineSeatVo> branchMachineSeatVos = branchMachineSeatService.queryList(convert);
        return MapstructUtils.convert(branchMachineSeatVos, RemoteBranchMachineSeatVo.class);
    }

    @Override
    public Boolean delBySeatIdList(List<Long> list) {
        return branchMachineSeatService.deleteWithValidByIds(list, false);
    }

    @Override
    public Boolean insertBatchByBo(List<RemoteBranchMachineSeatBo> seatInsertList) {
        List<BranchMachineSeatBo> convert = MapstructUtils.convert(seatInsertList, BranchMachineSeatBo.class);
        return branchMachineSeatService.insertBatchByBo(convert);
    }
}
