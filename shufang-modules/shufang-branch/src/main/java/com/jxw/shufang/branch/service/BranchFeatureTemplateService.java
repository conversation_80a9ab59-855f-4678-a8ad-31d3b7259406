package com.jxw.shufang.branch.service;


import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.branch.domain.bo.BranchFeatureTemplateBo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;

import java.util.List;

/**
 * <p>
 * 管控功能配置模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
public interface BranchFeatureTemplateService {

    boolean insertByBo(BranchFeatureTemplateBo branchFeatureTemplateBo);

    boolean updateByBo(BranchFeatureTemplateBo branchFeatureTemplateBo);

    boolean deleteByBranchFeatureTemplateIds(List<Long> branchFeatureTemplateIds);

    List<BranchFeatureTemplateVo> queryByControlTypeList(Integer controlType);

    List<BranchFeatureTemplateVo> queryList(BranchFeatureTemplateBo branchFeatureTemplateBo);

}
