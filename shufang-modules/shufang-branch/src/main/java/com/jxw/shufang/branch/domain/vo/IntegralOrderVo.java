package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.IntegralOrder;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 积分订单视图对象 integral_order
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralOrder.class)
public class IntegralOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单id
     */
    @ExcelProperty(value = "积分订单id")
    private Long integralOrderId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 顾问id
     */
    @ExcelProperty(value = "顾问id")
    private Long consultantId;

    /**
     * 积分订单编号
     */
    @ExcelProperty(value = "积分订单编号")
    private String integralOrderNo;

    /**
     * 积分操作id
     */
    @ExcelProperty(value = "积分操作id")
    private Long integralOrderOperateId;

    /**
     * 创建时间
     */
    private Date createTime;


    //private List<IntegralOrderInfoVo> integralOrderInfoList;

    private IntegralOrderInfoVo integralOrderInfo;

    private IntegralOrderOperateVo integralOrderOperate;

    private RemoteStudentVo student;

    private RemoteUserVo verifier;

}
