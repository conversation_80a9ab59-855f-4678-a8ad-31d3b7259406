package com.jxw.shufang.branch.mapper;

import com.jxw.shufang.branch.domain.IntegralOrderOperate;
import com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）Mapper接口
 *
 *
 * @date 2024-04-23
 */
public interface IntegralOrderOperateMapper extends BaseMapperPlus<IntegralOrderOperate, IntegralOrderOperateVo> {

}
