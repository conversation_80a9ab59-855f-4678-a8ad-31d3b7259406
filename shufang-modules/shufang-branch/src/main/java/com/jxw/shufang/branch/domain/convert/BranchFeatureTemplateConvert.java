package com.jxw.shufang.branch.domain.convert;

import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchFeatureTemplateConvert extends BaseMapper<BranchFeatureTemplate, BranchFeatureTemplateVo> {
}
