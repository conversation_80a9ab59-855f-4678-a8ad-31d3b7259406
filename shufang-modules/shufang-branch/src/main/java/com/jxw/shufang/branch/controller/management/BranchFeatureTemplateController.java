package com.jxw.shufang.branch.controller.management;

import com.jxw.shufang.branch.domain.bo.BranchFeatureTemplateBo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.branch.service.BranchFeatureTemplateService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 管控功能配置模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@RestController
@RequestMapping("/management/feature-template")
@Validated
@RequiredArgsConstructor
@Slf4j
public class BranchFeatureTemplateController extends BaseController {

    private final BranchFeatureTemplateService branchFeatureTemplateService;

    /**
     * 新增管控功能模板
     */
    @PostMapping("/add")
    @RepeatSubmit()
    @Log(title = "管控功能模板", businessType = BusinessType.INSERT)
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchFeatureTemplateBo branchFeatureTemplateBo) {
        boolean result = branchFeatureTemplateService.insertByBo(branchFeatureTemplateBo);
        return toAjax(result);
    }

    /**
     * 修改管控功能模板
     */
    @PostMapping("/edit")
    @Log(title = "管控功能模板", businessType = BusinessType.UPDATE)
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchFeatureTemplateBo branchFeatureTemplateBo) {
        boolean result = branchFeatureTemplateService.updateByBo(branchFeatureTemplateBo);
        return toAjax(result);
    }


    /**
     * 删除管控功能模板
     */
    @PostMapping("/remove")
    @Log(title = "管控功能模板", businessType = BusinessType.DELETE)
    public R<Void> remove(@RequestBody List<Long> branchFeatureTemplateIds) {
        boolean result = branchFeatureTemplateService.deleteByBranchFeatureTemplateIds(branchFeatureTemplateIds);
        return toAjax(result);
    }

    /**
     * 根据管控类型查询管控模板信息列表
     */
    @PostMapping("/queryByControlTypeList")
    public R<List<BranchFeatureTemplateVo>> queryByControlTypeList(@RequestParam Integer controlType) {
        List<BranchFeatureTemplateVo> list = branchFeatureTemplateService.queryByControlTypeList(controlType);
        return R.ok(list);
    }


}
