package com.jxw.shufang.branch.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.domain.BranchConfig;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.factory.BranchConfigStrategyFactory;
import com.jxw.shufang.branch.mapper.BranchConfigMapper;
import com.jxw.shufang.branch.service.BranchConfigStrategy;
import com.jxw.shufang.branch.service.IBranchConfigService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.api.RemoteDeptService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BranchConfigServiceImpl implements IBranchConfigService, BaseService {

    private final BranchConfigMapper baseMapper;

    private final IBranchService branchService;

    private final BranchConfigStrategyFactory branchConfigStrategyFactory;

    @DubboReference
    private final RemoteDeptService remoteDeptService;

    @Override
    public BranchConfigVo getBranchConfigByBo(BranchConfigBo bo) {
        if (null == bo.getBranchId() || null == bo.getConfigType()) {
            return null;
        }
        return baseMapper.selectVoOne(buildWrapper(bo));
    }

    @Override
    public TableDataInfo<BranchConfigVo.ConfigType> listAllConfigType(PageQuery pageQuery) {
        boolean noPage = null == pageQuery || (pageQuery.getPageSize() == null && pageQuery.getPageNum() == null);
        Page<BranchConfigVo.ConfigType> build = noPage ? new Page<>() : pageQuery.build();
        int index = 0;
        int startIndex = noPage ? 0 : (pageQuery.getPageNum() - 1) * pageQuery.getPageSize();
        int endIndex = noPage ? Integer.MAX_VALUE : startIndex + pageQuery.getPageSize() - 1;
        build.setRecords(new ArrayList<>());
        for (BranchConfigTypeEnum value : BranchConfigTypeEnum.values()) {
            if (startIndex <= index && index <= endIndex) {
                build.getRecords().add(new BranchConfigVo.ConfigType(value.getConfigType(), value.getConfigName()));
            }
            index++;
        }
        build.setTotal(BranchConfigTypeEnum.values().length);
        return TableDataInfo.build(build);
    }

    @Override
    public BranchConfigVo getConfig(BranchConfigBo bo) {
        BranchConfigVo voByBranchId = getBranchConfigByBo(bo);
        BranchConfigTypeEnum configTypeEnum = BranchConfigTypeEnum.getByType(bo.getConfigType());
        if (voByBranchId == null) {
            voByBranchId = new BranchConfigVo();
            voByBranchId.setBranchId(bo.getBranchId());
            voByBranchId.setConfigType(bo.getConfigType());
            voByBranchId.setConfigJson("[]");
        }
        BranchConfigStrategy configStrategy =
            branchConfigStrategyFactory.getConfigStrategy(configTypeEnum.getConfigType());
        configStrategy.buildConfigItems(voByBranchId);
        voByBranchId.setConfigJson(null);
        return voByBranchId;
    }

    @Override
    public boolean updateConfigBo(BranchConfigBo bo) {
        validateBranchConfigData(bo);
        BranchConfigTypeEnum configTypeEnum = BranchConfigTypeEnum.getByType(bo.getConfigType());
        BranchConfigStrategy configStrategy = branchConfigStrategyFactory.getConfigStrategy(configTypeEnum.getConfigType());
        String configJson = configStrategy.buildConfigJson(bo);
        if (null == configJson) {
            log.error("更新门店参数配置时转换配置参数异常");
            return false;
        }
        BranchConfigVo configByBo = getBranchConfigByBo(bo);
        BranchConfig saveOrUpdateEntity = new BranchConfig();
        saveOrUpdateEntity.setBranchId(bo.getBranchId());
        saveOrUpdateEntity.setConfigType(bo.getConfigType());
        saveOrUpdateEntity.setConfigJson(configJson);
        if (null != configByBo) {
            bo.setBranchId(null);
            bo.setConfigType(null);
            saveOrUpdateEntity.setBranchConfigId(configByBo.getBranchConfigId());
        }
        return baseMapper.insertOrUpdate(saveOrUpdateEntity);
    }

    @Override
    public boolean duplicateConfig(BranchConfigBo bo) {
        validateBranchConfigData(bo);
        List<BranchConfigVo> baseBranchConfigList = listBranchConfigByBo(bo);
        if (CollectionUtils.isEmpty(baseBranchConfigList)
            || bo.getConfigTypeList().size() != baseBranchConfigList.size()) {
            throw new ServiceException("同步配置时基准门店的门店配置信息不全");
        }
        boolean allShop = remoteDeptService.deptListIsAllShop(new ArrayList<>(bo.getDeptIdList()));
        if (!allShop) {
            throw new ServiceException("请选择门店进行同步");
        }
        BranchBo queryBranchBo = new BranchBo();
        queryBranchBo.setCreateDeptIds(bo.getDeptIdList());
        List<BranchVo> branchVos = branchService.queryBriefInfoList(queryBranchBo);
        if (CollectionUtils.isEmpty(branchVos) || branchVos.size() != bo.getDeptIdList().size()) {
            throw new ServiceException("同步门店配置时查询门店信息异常");
        }
        bo.setBranchId(null);
        bo.setBranchIds(branchVos.stream().map(BranchVo::getBranchId).collect(Collectors.toSet()));
        List<BranchConfigVo> branchConfigVos = listBranchConfigByBo(bo);
        Map<Integer, Map<Long, BranchConfigVo>> configTypeToConfigMapMap = branchConfigVos.stream().collect(Collectors
            .groupingBy(BranchConfigVo::getConfigType, Collectors.toMap(BranchConfigVo::getBranchId, vo -> vo)));
        List<BranchConfig> saveOrUpdateList = new ArrayList<>();
        for (BranchConfigVo baseConfig : baseBranchConfigList) {
            BranchConfigStrategy configStrategy =
                branchConfigStrategyFactory.getConfigStrategy(baseConfig.getConfigType());
            Map<Long, BranchConfigVo> branchIdToConfigMap = configTypeToConfigMapMap.get(baseConfig.getConfigType());
            if (null == branchIdToConfigMap) {
                branchIdToConfigMap = new HashMap<>(1);
            }
            Map<Long, String> duplicateConfigJson =
                configStrategy.getDuplicateConfigJson(baseConfig.getConfigJson(), branchVos, branchIdToConfigMap);
            for (BranchVo branchVo : branchVos) {
                BranchConfig saveOrUpdateEntity = new BranchConfig();
                saveOrUpdateEntity.setBranchId(branchVo.getBranchId());
                saveOrUpdateEntity.setConfigType(baseConfig.getConfigType());
                saveOrUpdateEntity.setConfigJson(duplicateConfigJson.get(branchVo.getBranchId()) == null ? "[]"
                    : duplicateConfigJson.get(branchVo.getBranchId()));
                if (null != branchIdToConfigMap.get(branchVo.getBranchId())) {
                    bo.setBranchId(null);
                    bo.setConfigType(null);
                    saveOrUpdateEntity
                        .setBranchConfigId(branchIdToConfigMap.get(branchVo.getBranchId()).getBranchConfigId());
                }
                saveOrUpdateList.add(saveOrUpdateEntity);
            }
        }
        return baseMapper.insertOrUpdateBatch(saveOrUpdateList);
    }

    /**
     * 获取门店配置类别，考虑bo中的门店id集合以及
     *
     * @param bo
     * @return
     */
    private List<BranchConfigVo> listBranchConfigByBo(BranchConfigBo bo) {
        return baseMapper.selectVoList(buildWrapper(bo));
    }

    /**
     * 检验入参
     *
     * @param bo
     */
    private void validateBranchConfigData(BranchConfigBo bo) {
        if (null != bo.getBranchId() && null == branchService.queryById(bo.getBranchId())) {
            throw new ServiceException("门店信息异常");
        }
        Set<Integer> typeSet = Arrays.stream(BranchConfigTypeEnum.values()).map(BranchConfigTypeEnum::getConfigType)
            .collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(bo.getConfigTypeList()) && !typeSet.containsAll(bo.getConfigTypeList())) {
            throw new ServiceException("配置类型异常");
        } else if (null != bo.getConfigType() && !typeSet.contains(bo.getConfigType())) {
            throw new ServiceException("配置类型异常");
        }
    }

    /**
     * 封装wrapper
     *
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<BranchConfig> buildWrapper(BranchConfigBo bo) {
        LambdaQueryWrapper<BranchConfig> lambdaQuery = Wrappers.lambdaQuery(BranchConfig.class);
        lambdaQuery.eq(null != bo.getBranchId(), BranchConfig::getBranchId, bo.getBranchId())
            .eq(null != bo.getConfigType(), BranchConfig::getConfigType, bo.getConfigType())
            .in(!CollectionUtils.isEmpty(bo.getConfigTypeList()), BranchConfig::getConfigType, bo.getConfigTypeList())
            .in(!CollectionUtils.isEmpty(bo.getBranchIds()), BranchConfig::getBranchId, bo.getBranchIds());
        return lambdaQuery;
    }
}
