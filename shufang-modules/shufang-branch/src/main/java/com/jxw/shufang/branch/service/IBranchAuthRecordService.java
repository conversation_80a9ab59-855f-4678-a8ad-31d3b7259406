package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.BranchAuthRecord;
import com.jxw.shufang.branch.domain.bo.BranchAuthRecordBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 分店授权记录
（时间逆序的最后一条记录和分店中的对应）Service接口
 *
 *
 * @date 2024-02-22
 */
public interface IBranchAuthRecordService {

    /**
     * 查询分店授权记录（时间逆序的最后一条记录和分店中的对应）
     */
    BranchAuthRecordVo queryById(Long branchAuthRecordId);

    /**
     * 查询分店授权记录（时间逆序的最后一条记录和分店中的对应）列表
     */
    TableDataInfo<BranchAuthRecordVo> queryPageList(BranchAuthRecordBo bo, PageQuery pageQuery);

    /**
     * 查询分店授权记录（时间逆序的最后一条记录和分店中的对应）列表
     */
    List<BranchAuthRecordVo> queryList(BranchAuthRecordBo bo);

    /**
     * 新增分店授权记录（时间逆序的最后一条记录和分店中的对应）
     */
    Boolean insertByBo(BranchAuthRecordBo bo);

    /**
     * 修改分店授权记录（时间逆序的最后一条记录和分店中的对应）
     */
    Boolean updateByBo(BranchAuthRecordBo bo);

    /**
     * 校验并批量删除分店授权记录（时间逆序的最后一条记录和分店中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    BranchAuthRecord queryBranchAuthRecordById(Long branchAuthRecordId);

    void cleanCache();

    /**
     * 根据分店id查询最后一条授权记录
     * @param branchId
     * @return
     */
    BranchAuthRecordVo queryLastAuthRecordByBranchId(Long branchId);

    List<BranchAuthRecordVo> queryLastAuthRecordListByBranchIdList(List<Long> branchIdList);
}
