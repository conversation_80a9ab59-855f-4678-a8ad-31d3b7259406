package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.ListUtils;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.bo.OptionalTaskBo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.enums.TaskEnum;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.mapper.IntegralTaskMapper;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.branch.service.IIntegralTaskService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
/**
 * 积分任务Service业务层处理
 *
 * @date 2024-04-25
 */
@RequiredArgsConstructor
@Service
public class IntegralTaskServiceImpl implements IIntegralTaskService {

    private final IntegralTaskMapper baseMapper;

    private final IBranchService branchService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询积分任务
     */
    @Override
    public IntegralTaskVo queryById(Long integralTaskId) {
        IntegralTaskVo integralTaskVo = baseMapper.selectVoById(integralTaskId);
        if (ObjectUtil.isNull(integralTaskVo)) {
            return null;
        }
        fillIntegralTaskName(Lists.newArrayList(integralTaskVo));
        return integralTaskVo;
    }

    /**
     * 查询积分任务列表
     */
    @Override
    public TableDataInfo<IntegralTaskVo> queryPageList(IntegralTaskBo bo, PageQuery pageQuery) {
        // 可以查询当前门店所属组织所有的积分任务
        Long deptId = LoginHelper.getDeptId();
        if (deptId == null) {
            return TableDataInfo.build();
        }
        //有门店的话以当前门店为准
        if (!ObjectUtils.isEmpty(bo.getBranchId())){
            deptId = getDeptId(bo.getBranchId());
        }
        List<Long> allParentDeptId = remoteDeptService.getAllParentDeptId(deptId);
        allParentDeptId.add(deptId);
        bo.setDeptIdList(allParentDeptId);
        LambdaQueryWrapper<IntegralTask> lqw = buildQueryWrapper(bo);
        Page<IntegralTaskVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        fillIntegralTaskName(result.getRecords());
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }

        putCreateDeptInfo(result.getRecords());

        return TableDataInfo.build(result);
    }

    /**
     * 查询积分任务列表
     */
    @Override
    public List<IntegralTaskVo> queryList(IntegralTaskBo bo) {
        LambdaQueryWrapper<IntegralTask> lqw = buildQueryWrapper(bo);
        List<IntegralTaskVo> integralTaskVos = baseMapper.selectVoList(lqw);
        fillIntegralTaskName(integralTaskVos);
        return integralTaskVos;
    }

    private LambdaQueryWrapper<IntegralTask> buildQueryWrapper(IntegralTaskBo bo) {
        LambdaQueryWrapper<IntegralTask> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, IntegralTask::getBranchId, bo.getBranchId());
        lqw.like(isNotBlank(bo.getIntegralTaskName()), IntegralTask::getIntegralTaskName, bo.getIntegralTaskName());
        lqw.eq(isNotBlank(bo.getParam()), IntegralTask::getParam, bo.getParam());
        lqw.eq(bo.getIntegralNum() != null, IntegralTask::getIntegralNum, bo.getIntegralNum());
        lqw.eq(isNotBlank(bo.getTaskTrigger()), IntegralTask::getTaskTrigger, bo.getTaskTrigger());
        lqw.eq(bo.getTaskStatus() != null, IntegralTask::getTaskStatus, bo.getTaskStatus());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), IntegralTask::getBranchId, bo.getBranchIdList());
        if (CollUtil.isNotEmpty(bo.getDeptIdList())) {
            lqw.or(v -> {
                v.in(IntegralTask::getCreateDept, bo.getDeptIdList());
                v.eq(isNotBlank(bo.getTaskTrigger()), IntegralTask::getTaskTrigger, bo.getTaskTrigger());
                v.eq(bo.getTaskStatus() != null, IntegralTask::getTaskStatus, bo.getTaskStatus());
                v.like(isNotBlank(bo.getIntegralTaskName()), IntegralTask::getIntegralTaskName, bo.getIntegralTaskName());
            });
        }
        lqw.orderByAsc(IntegralTask::getCreateTime);
        return lqw;
    }


    /**
     * 新增积分任务
     */
    @Override
    public Boolean insertByBo(IntegralTaskBo bo) {
        if (ObjectUtil.isNull(bo)) {
            return true;
        }
        IntegralTask add = MapstructUtils.convert(bo, IntegralTask.class);
        validEntityBeforeSave(add);

        Long branchId = bo.getBranchId();
        if (ObjectUtil.isNotNull(branchId)) {
            BranchVo branchVo = branchService.queryById(branchId);
            if (ObjectUtil.isNull(branchVo)) {
                throw new ServiceException("分店不存在");
            }
            add.setCreateDept(branchVo.getCreateDept());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralTaskId(add.getIntegralTaskId());
        }
        return flag;
    }

    /**
     * 修改积分任务
     */
    @Override
    public Boolean updateByBo(IntegralTaskBo bo) {
        IntegralTask update = MapstructUtils.convert(bo, IntegralTask.class);
        if (ObjectUtils.isEmpty(update)){
            return true;
        }
        validEntityBeforeSave(update);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralTask entity) {
        //做一些数据校验,如唯一约束
        Integer integralNum = entity.getIntegralNum();
        checkIntegralNum(integralNum);
        //判断是否上架
        if (org.apache.commons.lang3.StringUtils.equals(entity.getTaskStatus(), "1")) {
            checkTask(entity);
        }
        String taskTrigger = entity.getTaskTrigger();
        // 获取对应的枚举
        TaskEnum taskEnum = EnumUtil.fromStringQuietly(TaskEnum.class, taskTrigger);
        if (taskEnum == null) {
            throw new ServiceException("任务枚举不存在");
        }
        TaskHandler taskHandler = TaskHandler.getTaskHandler(taskEnum);
        if (taskHandler == null) {
            throw new ServiceException("任务触发器不存在");
        }
        taskHandler.validTaskRecord(entity);
    }

    private void checkTask(IntegralTask entity) {
        //上架
        //判断积分任务是否存在
        IntegralTask integralTask = baseMapper.selectById(entity.getIntegralTaskId());
        if (ObjectUtil.isNull(integralTask)){
            throw new ServiceException("积分任务不存在");
        }
        checkIsUp(entity, integralTask);
    }

    private void checkIsUp(IntegralTask entity, IntegralTask integralTask) {
        if (ObjectUtils.isEmpty(entity)){
            return;
        }
        //判断更新时是否需要上架，不需要则直接返回
        if (isNotNeedCheckIsUp(entity, integralTask)){
            return;
        }

        //判断该积分任务类型是否唯一
        String taskTrigger = integralTask.getTaskTrigger();
        TaskEnum taskEnum = EnumUtil.fromStringQuietly(TaskEnum.class, taskTrigger);


        if (taskEnum != null && taskEnum.isSingleCreate()) {
            //首先检查上级是否有上架的同类型任务
            Long deptId = getDeptId(integralTask);
            List<Long> allParentDeptId = remoteDeptService.getAllParentDeptId(deptId);
            allParentDeptId.add(deptId);
            List<IntegralTask> existTasks = getIntegralTasksIsUp(entity.getBranchId(), allParentDeptId, entity.getTaskTrigger());
            if (CollUtil.isNotEmpty(existTasks)) {
                throw new ServiceException("积分任务类型为" + taskEnum.getDesc() + "，只能上架一个");
            }

            //查看是否全部应用
            if (ObjectUtil.isNull(entity.getBranchId())) {
                List<RemoteDeptVo> selfAndChildShopList = remoteDeptService.getDeptChildrenList(integralTask.getCreateDept());
                if (CollUtil.isNotEmpty(selfAndChildShopList)) {
                    List<Long> deptIdList = new ArrayList<>(selfAndChildShopList.stream().map(RemoteDeptVo::getDeptId).toList());
                    IntegralTaskBo integralTaskBo = new IntegralTaskBo();
                    integralTaskBo.setDeptIdList(deptIdList);
                    integralTaskBo.setTaskStatus("1");
                    integralTaskBo.setTaskTrigger(taskTrigger);
                    List<IntegralTask> existChildrenTasks = getIntegralTasksIsUp(integralTaskBo);
                    if (CollUtil.isNotEmpty(existChildrenTasks)) {
                        //下架所有同类型的积分任务
                        List<IntegralTask> integralTasks = existChildrenTasks.stream().map(v -> {
                            v.setTaskStatus("2");
                            return v;
                        }).toList();
                        baseMapper.updateBatchById(integralTasks);
                    }
                }
            }



        }
    }

    private static boolean isNotNeedCheckIsUp(IntegralTask entity, IntegralTask integralTask) {
        //判断更新任务是否需要上架
        if (org.apache.commons.lang3.StringUtils.equals(entity.getTaskStatus(), "1")){
            //判断原先是否上架
            return org.apache.commons.lang3.StringUtils.equals(integralTask.getTaskStatus(), "1");
        }

       return true;
    }

    private static void checkIntegralNum(Integer integralNum) {
        if (integralNum != null && integralNum < 0) {
            throw new ServiceException("积分数不能小于0");
        }
    }

    private Long getDeptId(IntegralTask entity) {
        Long branchId = entity.getBranchId();
        if (ObjectUtil.isNotNull(branchId)) {
            BranchVo branchVo = branchService.queryById(branchId);
            if (ObjectUtil.isNotNull(branchVo)) {
                return branchVo.getCreateDept();
            }
        }else {
            return entity.getCreateDept();
        }
        return null;
    }


    private Long getDeptId( Long branchId) {
        if (ObjectUtil.isNotNull(branchId)) {
            BranchVo branchVo = branchService.queryById(branchId);
            if (ObjectUtil.isNotNull(branchVo)) {
                return branchVo.getCreateDept();
            }
        }
        return null;
    }

    /**
     * 批量删除积分任务
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {

        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<IntegralTaskVo> queryStudentIntegralTaskList(Long studentId, IntegralTaskBo bo) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return ListUtils.newArrayList();
        }
        BranchVo branchVo = branchService.queryById(branchId);
        if (ObjectUtil.isNull(branchVo)) {
            return ListUtils.newArrayList();
        }
        Long deptId = branchVo.getCreateDept();
        List<Long> allParentDeptId = remoteDeptService.getAllParentDeptId(deptId);
        bo.setDeptIdList(allParentDeptId);
        LambdaQueryWrapper<IntegralTask> lqw = buildQueryWrapper(bo);
        List<IntegralTaskVo> integralTaskVos = baseMapper.selectVoList(lqw);
        integralTaskVos = integralTaskVos.stream().filter(taskVo -> {
            TaskEnum taskEnum;
            if (taskVo.getTaskTrigger() != null && (taskEnum = TaskEnum.getEnum(taskVo.getTaskTrigger())) != null
                && !taskEnum.isStudentVisible()) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        fillIntegralTaskName(integralTaskVos);
        putStudentTaskStatus(studentId, integralTaskVos);
        return integralTaskVos;
    }

    @Override
    @Lock4j(keys = {"'integral_task_receive:' + #studentId + '-' + #integralTaskId"})
    public void receiveTaskIntegral(Long studentId, Long integralTaskId) {
        IntegralTaskVo integralTaskVo = queryById(integralTaskId);
        if (integralTaskVo != null) {
            fillIntegralTaskName(CollUtil.newArrayList(integralTaskVo));
        }
        if (integralTaskVo == null) {
            throw new ServiceException("任务不存在");
        }

        String taskTrigger = integralTaskVo.getTaskTrigger();
        //获取对应的枚举
        TaskEnum taskEnum = EnumUtil.fromStringQuietly(TaskEnum.class, taskTrigger);
        if (taskEnum == null) {
            throw new ServiceException("任务枚举不存在");
        }
        TaskHandler taskHandler = TaskHandler.getTaskHandler(taskEnum);
        if (taskHandler == null) {
            throw new ServiceException("任务触发器不存在");
        }

        taskHandler.finishTask(integralTaskVo, studentId);

    }

    @Override
    public void initIntegralTask(Long branchId) {
        //20250326废弃
        TaskEnum[] values = TaskEnum.values();
        if (ArrayUtil.isEmpty(values)) {
            return;
        }
        List<IntegralTask> insertList = new ArrayList<>();
        for (TaskEnum taskEnum : values) {
            IntegralTask integralTask = new IntegralTask();
            integralTask.setBranchId(branchId);
            integralTask.setTaskTrigger(taskEnum.toString());
            integralTask.setIntegralNum(taskEnum.getDefaultIntegralNum());
            integralTask.setTaskStatus(UserConstants.INTEGRAL_TASK_STATUS_DOWN);
            integralTask.setIntegralTaskName(taskEnum.getIntegralTaskName());
            integralTask.setParam(taskEnum.getDefaultParam());
            insertList.add(integralTask);
        }
        baseMapper.insertBatch(insertList);
    }

    @Override
    public List<IntegralTaskVo> optional(OptionalTaskBo bo) {
        // 可以查询当前门店所属组织所有的积分任务
        Long deptId = LoginHelper.getDeptId();
        if (deptId == null) {
            return Lists.newArrayList();
        }
        //有门店的话以当前门店为准
        if (!ObjectUtils.isEmpty(bo.getBranchId())){
            deptId = getDeptId(bo.getBranchId());
        }
        List<Long> allParentDeptId = remoteDeptService.getAllParentDeptId(deptId);
        allParentDeptId.add(deptId);
        IntegralTaskBo integralTaskBo = new IntegralTaskBo();
        integralTaskBo.setDeptIdList(allParentDeptId);
        integralTaskBo.setBranchId(bo.getBranchId());

        LambdaQueryWrapper<IntegralTask> lqw = buildQueryWrapper(integralTaskBo);
        List<IntegralTaskVo> integralTaskVos = baseMapper.selectVoList(lqw);


        // 获取所有已存在的任务类型(去重)
        Set<String> existTaskTriggers = integralTaskVos.stream()
            .map(IntegralTaskVo::getTaskTrigger)
            .collect(Collectors.toSet());

        // 返回所有任务类型(去重)
        return Arrays.stream(TaskEnum.values())
            .filter(taskEnum -> !taskEnum.isStopped())
            .filter(taskEnum -> {
                if (taskEnum.isSingleCreate()) {
                    return !existTaskTriggers.contains(taskEnum.toString());
                }
                return true;
            })
            .distinct()
            .map(IntegralTaskVo::buildIntegralTaskVo)
            .toList();
    }

    private List<IntegralTask> getIntegralTasks(Long branchId, List<Long> allParentDeptId) {
        return getIntegralTasksIsUp(branchId,allParentDeptId, null);
    }

    private List<IntegralTask> getIntegralTasksIsUp(Long branchId, List<Long> allParentDeptId, String taskTrigger) {
        IntegralTaskBo integralTaskBo = new IntegralTaskBo();
        integralTaskBo.setBranchId(branchId);
        integralTaskBo.setDeptIdList(allParentDeptId);
        integralTaskBo.setTaskTrigger(taskTrigger);
        integralTaskBo.setTaskStatus("1");
        return getIntegralTasksIsUp(integralTaskBo);
    }

    private List<IntegralTask> getIntegralTasksIsUp(IntegralTaskBo bo) {
        LambdaQueryWrapper<IntegralTask> lqw = Wrappers.lambdaQuery();
        lqw.select(IntegralTask::getTaskTrigger, IntegralTask::getTaskStatus, IntegralTask::getCreateDept,IntegralTask::getIntegralTaskId);
        LambdaQueryWrapper<IntegralTask> lqw1 = getLqw(bo, lqw);
        if (CollUtil.isNotEmpty(bo.getDeptIdList())) {
            lqw1.or(v -> {
                v.in(IntegralTask::getCreateDept, bo.getDeptIdList());
                v.eq(bo.getTaskTrigger() != null, IntegralTask::getTaskTrigger, bo.getTaskTrigger());
                v.eq(bo.getTaskStatus() != null, IntegralTask::getTaskStatus, bo.getTaskStatus());
            });
        }

        return baseMapper.selectList(lqw);
    }

    private static LambdaQueryWrapper<IntegralTask>  getLqw(IntegralTaskBo bo, LambdaQueryWrapper<IntegralTask> lqw) {
        if (bo.getBranchId()== null){
            return lqw;
        }
        lqw.eq( IntegralTask::getBranchId, bo.getBranchId());
        lqw.eq(bo.getTaskTrigger() != null, IntegralTask::getTaskTrigger, bo.getTaskTrigger());
        lqw.eq(bo.getTaskStatus() != null, IntegralTask::getTaskStatus, bo.getTaskStatus());
        return lqw;
    }

    //店铺信息
    public void putBranchInfo(List<IntegralTaskVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> branchIds = list.stream().map(IntegralTaskVo::getBranchId).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }
        BranchBo branchBo = new BranchBo();
        branchBo.setBranchIds(branchIds);
        List<BranchVo> remoteBranchVos = branchService.selectSimpleBranInfo(branchBo);
        Map<Long, BranchVo> remoteBranchVoMap = remoteBranchVos.stream().collect(Collectors.toMap(BranchVo::getBranchId, vo -> vo));
        list.forEach(item -> {
            BranchVo branchVo = remoteBranchVoMap.get(item.getBranchId());
            item.setBranch(branchVo);
        });

    }


    private void fillIntegralTaskName(List<IntegralTaskVo> integralTaskVos) {
        if (CollUtil.isEmpty(integralTaskVos)) {
            return;
        }
        integralTaskVos.forEach(item -> {
            if (isNotBlank(item.getIntegralTaskName())
            ) {
                item.setIntegralTaskNameFill(item.getIntegralTaskName().replace("$", cn.hutool.core.text.CharSequenceUtil.blankToDefault(item.getParam(), "")));
            }
        });
    }

    private void putStudentTaskStatus(Long studentId, List<IntegralTaskVo> integralTaskVos) {
        if (CollUtil.isEmpty(integralTaskVos)) {
            return;
        }
        for (IntegralTaskVo integralTaskVo : integralTaskVos) {
            String taskTrigger = integralTaskVo.getTaskTrigger();
            //获取对应的枚举
            TaskHandler taskHandler = getTaskHandler(taskTrigger);
            if (taskHandler == null) {
                continue;
            }
            TaskFinishedStatusEnum status = taskHandler.getStatus(integralTaskVo, studentId);
            integralTaskVo.setTaskFinishedStatusEnum(status);
        }
    }

    private static @Nullable TaskHandler getTaskHandler(String taskTrigger) {
        if (isBlank(taskTrigger)){
            return null;
        }
        TaskEnum taskEnum = EnumUtil.fromStringQuietly(TaskEnum.class, taskTrigger);
        if (taskEnum == null) {
            return null;
        }
        TaskHandler taskHandler = TaskHandler.getTaskHandler(taskEnum);
        if (taskHandler == null) {
            return null;
        }
        return taskHandler;
    }

    public void putCreateDeptInfo(List<IntegralTaskVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> createDeptList =
            records.stream().filter(record -> ObjectUtils.isEmpty(record.getBranchId())).map(IntegralTaskVo::getCreateDept).toList();
        if (CollUtil.isEmpty(createDeptList)) {
            return;
        }
        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setDeptIdList(createDeptList);

        List<RemoteDeptVo> remoteDeptVos = remoteDeptService.getDeptListIgnore(remoteDeptBo);

        if (CollUtil.isEmpty(remoteDeptVos)) {
            return;
        }
        Map<Long, RemoteDeptVo> remoteDeptVoMap = remoteDeptVos.stream().collect(Collectors.toMap(RemoteDeptVo::getDeptId, item -> item));
        for (IntegralTaskVo record : records) {
            RemoteDeptVo remoteDeptVo = remoteDeptVoMap.get(record.getCreateDept());
            if (remoteDeptVo != null) {
                record.setCreateDeptName(remoteDeptVo.getDeptName());
            }
        }
    }

}
