package com.jxw.shufang.branch.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Slf4j
public class BranchConfigJsonHandler {
    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 解析 JSON 为单个对象
     */
    public static <T> T parseConfigJson(String configJson, Class<T> clazz) {
        if (configJson == null || configJson.isBlank()) {
            return null;
        }
        try {
            return objectMapper.readValue(configJson, clazz);
        } catch (Exception e) {
            log.error("转换门店配置json异常",e);
            throw new ServiceException("配置参数异常");
        }
    }

    /**
     * 解析 JSON 为对象列表
     */
    public static <T> List<T> parseConfigJsonList(String configJson, Class<T> clazz) {
        if (configJson == null || configJson.isBlank()) {
            return Collections.emptyList();
        }
        try {
            return objectMapper.readValue(configJson, objectMapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            log.error("门店配置json转换为list异常",e);
            throw new ServiceException("配置参数异常");
        }
    }

    /**
     * 将对象转为 JSON
     */
    public static <T> String toJson(T config) {
        try {
            return objectMapper.writeValueAsString(config);
        } catch (Exception e) {
            log.error("门店配置json转换为obj异常",e);
            throw new ServiceException("配置参数异常");
        }
    }

    /**
     * 将对象列表转为 JSON
     */
    public static <T> String toJson(List<T> configList) {
        try {
            return objectMapper.writeValueAsString(configList);
        } catch (Exception e) {
            log.error("list转jsonString异常",e);
            throw new ServiceException("配置参数异常");
        }
    }
}
