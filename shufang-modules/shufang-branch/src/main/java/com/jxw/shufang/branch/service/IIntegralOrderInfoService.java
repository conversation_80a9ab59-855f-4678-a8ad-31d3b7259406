package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.IntegralOrderInfoBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderInfoVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 积分订单详情Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IIntegralOrderInfoService {

    /**
     * 查询积分订单详情
     */
    IntegralOrderInfoVo queryById(Long integralOrderInfoId);

    /**
     * 查询积分订单详情列表
     */
    TableDataInfo<IntegralOrderInfoVo> queryPageList(IntegralOrderInfoBo bo, PageQuery pageQuery);

    /**
     * 查询积分订单详情列表
     */
    List<IntegralOrderInfoVo> queryList(IntegralOrderInfoBo bo);

    /**
     * 新增积分订单详情
     */
    Boolean insertByBo(IntegralOrderInfoBo bo);

    /**
     * 修改积分订单详情
     */
    Boolean updateByBo(IntegralOrderInfoBo bo);

    /**
     * 校验并批量删除积分订单详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    IntegralOrderInfoVo queryByOrderId(Long integralOrderId);
}
