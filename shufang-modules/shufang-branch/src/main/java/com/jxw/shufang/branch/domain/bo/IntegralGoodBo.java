package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralGood;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分商品业务对象 integral_good
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralGood.class, reverseConvertGenerate = false)
public class IntegralGoodBo extends BaseEntity {

    /**
     * 积分商品id
     */
    @NotNull(message = "积分商品id不能为空", groups = { EditGroup.class })
    private Long integralGoodId;

    /**
     * 分店id
     */
//    @NotNull(message = "分店id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 积分商品图片（oss_id）
     */
    @NotNull(message = "积分商品图片（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralGoodImg;

    /**
     * 积分商品名称
     */
    @NotBlank(message = "积分商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodName;

    /**
     * 商品描述
     */
    @NotBlank(message = "商品描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodDesc;

    /**
     * 消耗积分
     */
    @NotNull(message = "消耗积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟）
     */
    @NotBlank(message = "商品类型（1实物 2虚拟）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodType;

    /**
     * 兑换规则（1仅可兑换一次 2不限次数）
     */
    @NotBlank(message = "兑换规则（1仅可兑换一次 2不限次数）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodRule;

    /**
     * 商品库存
     */
    @NotNull(message = "商品库存不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralGoodStock;

    /**
     * 商品状态（1上架 2下架）
     */
//    @NotBlank(message = "商品状态（1上架 2下架）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodStatus;

    /**
     * 商品排序（数字越小越靠前）
     */
    @NotNull(message = "商品排序（数字越小越靠前）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralGoodSort;


    private List<Long> branchIdList;

}
