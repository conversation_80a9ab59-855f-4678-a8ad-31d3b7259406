package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.branch.domain.BranchMachineSeat;
import com.jxw.shufang.branch.domain.bo.BranchMachineSeatBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.branch.mapper.BranchMachineSeatMapper;
import com.jxw.shufang.branch.service.IBranchMachineSeatService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 机位Service业务层处理
 *
 *
 * @date 2024-03-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BranchMachineSeatServiceImpl implements IBranchMachineSeatService, BaseService {

    private final BranchMachineSeatMapper baseMapper;

    /**
     * 查询机位
     */
    @Override
    public BranchMachineSeatVo queryById(Long branchMachineSeatId) {
        return baseMapper.selectVoById(branchMachineSeatId);
    }

    /**
     * 查询机位列表
     */
    @Override
    public TableDataInfo<BranchMachineSeatVo> queryPageList(BranchMachineSeatBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchMachineSeat> lqw = buildLambdaQueryWrapper(bo);
        Page<BranchMachineSeatVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询机位列表
     */
    @Override
    public List<BranchMachineSeatVo> queryList(BranchMachineSeatBo bo) {
        QueryWrapper<BranchMachineSeat> lqw = buildQueryWrapper(bo);
        return baseMapper.selectSeatList(lqw);
    }

    private LambdaQueryWrapper<BranchMachineSeat> buildLambdaQueryWrapper(BranchMachineSeatBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchMachineSeat> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchMachineRegionId() != null, BranchMachineSeat::getBranchMachineRegionId, bo.getBranchMachineRegionId());
        lqw.eq(bo.getStudentId() != null, BranchMachineSeat::getStudentId, bo.getStudentId());
        lqw.eq(bo.getSeatNo() != null, BranchMachineSeat::getSeatNo, bo.getSeatNo());
        lqw.eq(bo.getUseStartTime() != null, BranchMachineSeat::getUseStartTime, bo.getUseStartTime());
        lqw.eq(bo.getUseEndTime() != null, BranchMachineSeat::getUseEndTime, bo.getUseEndTime());
        return lqw;
    }


    private QueryWrapper<BranchMachineSeat> buildQueryWrapper(BranchMachineSeatBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<BranchMachineSeat> lqw = Wrappers.query();
        lqw.eq(bo.getBranchMachineRegionId() != null, "t.branch_machine_region_id", bo.getBranchMachineRegionId());
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getSeatNo() != null, "t.seat_no", bo.getSeatNo());
        lqw.eq(bo.getUseStartTime() != null, "t.use_start_time", bo.getUseStartTime());
        lqw.eq(bo.getUseEndTime() != null, "t.use_end_time", bo.getUseEndTime());
        lqw.ge(bo.getGreaterThanEndTime() != null, "t.use_end_time", bo.getGreaterThanEndTime());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        if (bo.getRangeEndTime() != null && bo.getRangeStartTime() != null) {
            lqw.le("t.use_end_time", bo.getRangeEndTime());
            lqw.ge("t.use_start_time", bo.getRangeStartTime());
        }
        return lqw;
    }

    /**
     * 新增机位
     */
    @Override
    public Boolean insertByBo(BranchMachineSeatBo bo) {
        BranchMachineSeat add = MapstructUtils.convert(bo, BranchMachineSeat.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchMachineSeatId(add.getBranchMachineSeatId());
        }
        return flag;
    }

    @Override
    public Boolean insertBatchByBo(List<BranchMachineSeatBo> boList) {
        List<BranchMachineSeat> convert = MapstructUtils.convert(boList, BranchMachineSeat.class);
        for (BranchMachineSeat branchMachineSeat : convert) {
            validEntityBeforeSave(branchMachineSeat);
        }
        return baseMapper.insertBatch(convert);
    }

    /**
     * 修改机位
     */
    @Override
    public Boolean updateByBo(BranchMachineSeatBo bo) {
        BranchMachineSeat update = MapstructUtils.convert(bo, BranchMachineSeat.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchMachineSeat entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除机位
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean deleteBySeatNo(Long branchMachineRegionId, List<Long> delNoList) {
        int delete = baseMapper.delete(Wrappers.<BranchMachineSeat>lambdaQuery()
            .eq(BranchMachineSeat::getBranchMachineRegionId, branchMachineRegionId)
            .in(BranchMachineSeat::getSeatNo, delNoList)
        );
        return delete > 0;
    }


    @Cacheable(value = "branchMachineSeat", key = "#branchMachineSeatId", condition = "#branchMachineSeatId != null")
    @Override
    public BranchMachineSeat queryBranchMachineSeatById(Long branchMachineSeatId) {
        return baseMapper.selectById(branchMachineSeatId);
    }

    @CacheEvict(value = "branchMachineSeat", allEntries = true)
    public void cleanCache() {
        log.info("===========branchMachineSeatService cleanCache===========");
    }



    @Override
    public void init() {
        IBranchMachineSeatService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchMachineSeatService init===========");
        LambdaQueryWrapper<BranchMachineSeat> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchMachineSeat::getBranchMachineSeatId);
        List<BranchMachineSeat> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========branchMachineSeatService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchMachineSeatById(item.getBranchMachineSeatId());
        });
        log.info("===========branchMachineSeatService init end===========");
    }


}
