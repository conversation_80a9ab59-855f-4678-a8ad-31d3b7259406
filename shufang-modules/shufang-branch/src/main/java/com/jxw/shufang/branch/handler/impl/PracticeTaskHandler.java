package com.jxw.shufang.branch.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.student.api.RemoteCorrectionRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteCorrectionRecordBo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.jxw.shufang.branch.handler.TaskHandler.BASE_NAME;


@RequiredArgsConstructor
@Service("PRACTICE_TASK" + BASE_NAME)
public class PracticeTaskHandler implements TaskHandler {

    private final IIntegralRecordService integralRecordService;

    @DubboReference
    private RemoteCorrectionRecordService remoteCorrectionRecordService;

    @Override
    public TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId) {
        Long integralTaskId = integralTaskVo.getIntegralTaskId();
        if (integralTaskId == null || studentId == null) {
            return TaskFinishedStatusEnum.RECEIVED;
        }
        String param = integralTaskVo.getParam();
        long num = 0L;
        if (StringUtils.isBlank(param)) {
            throw new ServiceException("请管理员配置任务参数");
        }
        try {
            num = Long.parseLong(param.trim());
        } catch (Exception e) {
            throw new ServiceException("请管理员配置正确的任务参数");
        }

        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setTaskId(integralTaskId);
        integralRecordBo.setCreateDateStr(DateUtils.getDate());
        List<IntegralRecordVo> integralRecordVos = integralRecordService.queryList(integralRecordBo);
        //今天已经领取过了，直接返回
        if (CollUtil.isNotEmpty(integralRecordVos)) {
            return TaskFinishedStatusEnum.RECEIVED;
        }
        //没完成，查询相关业务逻辑
        //查询有没有这个学生今天的批改记录


        //查业务
        Date now = new Date();
        DateTime startDate = DateUtil.beginOfDay(now);
        DateTime endDate = DateUtil.endOfDay(now);

        RemoteCorrectionRecordBo remoteCorrectionRecordBo = new RemoteCorrectionRecordBo();
        remoteCorrectionRecordBo.setStudentId(studentId);
        remoteCorrectionRecordBo.setCorrectionType(UserConstants.CORRECTION_TYPE_PRACTICE);
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeStart(startDate.toJdkDate());
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeEnd(endDate.toJdkDate());
        Long count = remoteCorrectionRecordService.count(remoteCorrectionRecordBo, true);
        if (ObjectUtil.defaultIfNull(count, 0L) >= num) {
            return TaskFinishedStatusEnum.UNRECEIVED;
        }
        return TaskFinishedStatusEnum.UNFINISHED;

    }

    @Override
    public void finishTask(IntegralTaskVo integralTaskVo, Long studentId) {
        TaskFinishedStatusEnum status = getStatus(integralTaskVo, studentId);
        if (!TaskFinishedStatusEnum.UNRECEIVED.equals(status)) {
            throw new ServiceException("错误状态，不可领取");
        }
        //添加记录
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
        integralRecordBo.setChangeNum(new BigDecimal(integralTaskVo.getIntegralNum() == null ? 0 : integralTaskVo.getIntegralNum()));
        integralRecordBo.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setChangeReason(StrUtil.blankToDefault(integralTaskVo.getIntegralTaskNameFill(), integralTaskVo.getIntegralTaskName()));
        integralRecordBo.setTaskId(integralTaskVo.getIntegralTaskId());
        Boolean b = integralRecordService.insertByBo(integralRecordBo);
        if (!b) {
            throw new ServiceException("领取失败");
        }
    }
}
