package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.IntegralOrderOperateBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IIntegralOrderOperateService {

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    IntegralOrderOperateVo queryById(Long integralOrderOperateId);

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    TableDataInfo<IntegralOrderOperateVo> queryPageList(IntegralOrderOperateBo bo, PageQuery pageQuery);

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    List<IntegralOrderOperateVo> queryList(IntegralOrderOperateBo bo);

    /**
     * 新增积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    Boolean insertByBo(IntegralOrderOperateBo bo);

    /**
     * 修改积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    Boolean updateByBo(IntegralOrderOperateBo bo);

    /**
     * 校验并批量删除积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
