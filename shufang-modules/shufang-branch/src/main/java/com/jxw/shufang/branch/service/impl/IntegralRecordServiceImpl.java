package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.mapper.IntegralRecordMapper;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 积分记录Service业务层处理
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class IntegralRecordServiceImpl implements IIntegralRecordService {

    private final IntegralRecordMapper baseMapper;

    /**
     * 查询积分记录
     */
    @Override
    public IntegralRecordVo queryById(Long integralRecordId){
        return baseMapper.selectVoById(integralRecordId);
    }

    /**
     * 查询积分记录列表
     */
    @Override
    public TableDataInfo<IntegralRecordVo> queryPageList(IntegralRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IntegralRecord> lqw = buildQueryWrapper(bo);
        Page<IntegralRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分记录列表
     */
    @Override
    public List<IntegralRecordVo> queryList(IntegralRecordBo bo) {
        LambdaQueryWrapper<IntegralRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IntegralRecord> buildQueryWrapper(IntegralRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IntegralRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, IntegralRecord::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeType()), IntegralRecord::getChangeType, bo.getChangeType());
        lqw.eq(bo.getChangeNum() != null, IntegralRecord::getChangeNum, bo.getChangeNum());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeState()), IntegralRecord::getChangeState, bo.getChangeState());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeReason()), IntegralRecord::getChangeReason, bo.getChangeReason());
        lqw.eq(StringUtils.isNotBlank(bo.getChangeRemark()), IntegralRecord::getChangeRemark, bo.getChangeRemark());
        lqw.apply(StringUtils.isNotBlank(bo.getCreateDateStr()), "date_format(create_time,'%Y-%m-%d') = {0}" , bo.getCreateDateStr());
        lqw.ge(bo.getCreateTimeStart() != null, IntegralRecord::getCreateTime, bo.getCreateTimeStart());
        lqw.last(StringUtils.isNotBlank(bo.getOrderBy()), "order by " + bo.getOrderBy());
        lqw.eq(bo.getTaskId() != null, IntegralRecord::getTaskId, bo.getTaskId());
        lqw.ge(bo.getCreateTimeStart() != null, IntegralRecord::getCreateTime, bo.getCreateTimeStart());
        lqw.le(bo.getCreateTimeEnd() != null, IntegralRecord::getCreateTime, bo.getCreateTimeEnd());
        return lqw;
    }

    /**
     * 新增积分记录
     */
    @Override
    public Boolean insertByBo(IntegralRecordBo bo) {
        IntegralRecord add = MapstructUtils.convert(bo, IntegralRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralRecordId(add.getIntegralRecordId());
        }
        return flag;
    }

    /**
     * 修改积分记录
     */
    @Override
    public Boolean updateByBo(IntegralRecordBo bo) {
        IntegralRecord update = MapstructUtils.convert(bo, IntegralRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralRecord entity){
        //做一些数据校验,如唯一约束
        if (entity.getChangeNum()!= null && entity.getChangeNum().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("积分数量必须大于0");
        }
        if (entity.getStudentId()!=null&& UserConstants.INTEGRAL_CHANGE_TYPE_REDUCE.equals(entity.getChangeType())){
            //减少之前先判断余额是否足够
            BigDecimal totalIntegral = baseMapper.getIntegralByStudentId(entity.getStudentId());
            if (totalIntegral == null || totalIntegral.compareTo(entity.getChangeNum()) < 0) {
                throw new ServiceException("会员剩余积分不足以扣除");
            }
        }
    }

    /**
     * 批量删除积分记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public BigDecimal getIntegralByStudentId(Long studentId) {
        BigDecimal totalIntegral = baseMapper.getIntegralByStudentId(studentId);
        return totalIntegral == null ? BigDecimal.ZERO : totalIntegral;
    }

    @Override
    public Map<Long, BigDecimal> getIntegralByStudentIdList(List<Long> studentIdList) {
        List<IntegralRecordVo> integralByStudentIdList = baseMapper.getIntegralByStudentIdList(studentIdList);
        if (CollUtil.isEmpty(integralByStudentIdList)){
            return Map.of();
        }
        return StreamUtils.toMap(integralByStudentIdList, IntegralRecordVo::getStudentId, e->e.getStudentIntegral()==null?BigDecimal.ZERO:e.getStudentIntegral());
    }

    @Override
    public boolean batchSaveRecord(List<IntegralRecord> saveList) {
        return baseMapper.insertBatch(saveList);
    }

}
