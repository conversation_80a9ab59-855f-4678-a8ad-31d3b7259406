package com.jxw.shufang.branch.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.dto.BranchAuthTypeRelationDto;
import com.jxw.shufang.branch.domain.dto.BranchConfigDto;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.handler.BranchConfigJsonHandler;
import com.jxw.shufang.branch.service.BranchConfigStrategy;
import com.jxw.shufang.branch.service.IBranchAuthTypeService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * @author: cyj
 * @date: 2025/3/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BranchProductPreferentialStrategy implements BranchConfigStrategy {
    @DubboReference
    private final RemoteProductService remoteProductService;

    @DubboReference
    private final RemoteBranchAuthTypeService remoteBranchAuthTypeService;

    private final IBranchAuthTypeService branchAuthTypeService;

    @Override
    public void buildConfigItems(BranchConfigVo branchConfigVo) {
        List<BranchConfigDto.PreferentialAmountConfig> preferentialAmountConfigs =
            BranchConfigJsonHandler.parseConfigJsonList(branchConfigVo.getConfigJson(), BranchConfigDto.PreferentialAmountConfig.class);
        Map<Long, BranchConfigDto.PreferentialAmountConfig> idToConfigMap =
            preferentialAmountConfigs.stream().collect(Collectors.toMap(BranchConfigDto.PreferentialAmountConfig::getProduceId, config -> config));
        // 获取所有正价产品列表
        RemoteProductBo productBo = new RemoteProductBo();
        RemoteBranchAuthTypeVo authTypeByBranchId =
            remoteBranchAuthTypeService.getAuthTypeByBranchId(branchConfigVo.getBranchId());
        List<Long> authProductIdList;
        if (null == authTypeByBranchId || StringUtils.isEmpty(authTypeByBranchId.getProductIds())
            || CollectionUtils.isEmpty(authProductIdList =
                Arrays.stream(authTypeByBranchId.getProductIds().split(",")).map(Long::parseLong).toList())) {
            branchConfigVo.setConfigData(new ArrayList<>(1));
            return;
        }
        productBo.setIsOfficialCard(true);
        productBo.setProductIdList(authProductIdList);
        List<RemoteProductVo> list = remoteProductService.queryProductList(productBo, true);
        List<BranchConfigDto.PreferentialAmountConfig> configDataList = new ArrayList<>();
        for (RemoteProductVo remoteProductVo : list) {
            if (null != idToConfigMap.get(remoteProductVo.getProductId())) {
                configDataList.add(idToConfigMap.get(remoteProductVo.getProductId()));
            } else {
                BranchConfigDto.PreferentialAmountConfig preferentialAmountConfig = new BranchConfigDto.PreferentialAmountConfig();
                preferentialAmountConfig.setProduceId(remoteProductVo.getProductId());
                preferentialAmountConfig.setProduceName(remoteProductVo.getProductName());
                preferentialAmountConfig.setPreferentialAmount(null);
                preferentialAmountConfig.setFrozenDay(null);
                configDataList.add(preferentialAmountConfig);
            }
        }
        branchConfigVo.setConfigData(configDataList);
    }

    @Override
    public String buildConfigJson(BranchConfigBo bo) {
        List<BranchConfigDto.PreferentialAmountConfig> preferentialAmountConfigs
            = BranchConfigJsonHandler.parseConfigJsonList(BranchConfigJsonHandler.toJson(bo.getConfigData()), BranchConfigDto.PreferentialAmountConfig.class);
        ListIterator<BranchConfigDto.PreferentialAmountConfig> preferentialAmountConfigListIterator = preferentialAmountConfigs.listIterator();
        while (preferentialAmountConfigListIterator.hasNext()) {
            BranchConfigDto.PreferentialAmountConfig config = preferentialAmountConfigListIterator.next();
            if (null == config.getProduceId() || StringUtils.isEmpty(config.getProduceName()) || (null == config.getPreferentialAmount() && null == config.getFrozenDay())) {
                preferentialAmountConfigListIterator.remove();
                continue;
            }
            if (null == config.getPreferentialAmount()) {
                throw new ServiceException("配置保存失败，产品：" + config.getProduceName() + "配置了冻结日期，但未配置优惠额度。");
            }
            if (config.getPreferentialAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("配置保存失败，产品：" + config.getProduceName() + "优惠额度须大于零");
            }
            if (null == config.getFrozenDay()) {
                throw new ServiceException("配置保存失败，产品：" + config.getProduceName() + "配置了优惠额度，但未配置释放天数。");
            }
            if (config.getFrozenDay() < 7) {
                throw new ServiceException("配置保存失败，产品：" + config.getProduceName() + "释放天数最小为7");
            }
        }
        if (preferentialAmountConfigs.isEmpty()) {
            throw new ServiceException("配置保存失败，未检测到有效的门店配置");
        }
        return BranchConfigJsonHandler.toJson(preferentialAmountConfigs);
    }

    @Override
    public Map<Long, String> getDuplicateConfigJson(String configJson, List<BranchVo> branchVos,
        Map<Long, BranchConfigVo> branchIdToConfigMap) {
        List<BranchConfigDto.PreferentialAmountConfig> preferentialAmountConfigs =
            BranchConfigJsonHandler.parseConfigJsonList(configJson, BranchConfigDto.PreferentialAmountConfig.class);
        Map<Long, BranchConfigDto.PreferentialAmountConfig> productIdToConfigMap = preferentialAmountConfigs.stream()
            .collect(Collectors.toMap(BranchConfigDto.PreferentialAmountConfig::getProduceId, Function.identity()));
        BranchAuthTypeRelationDto relation = branchAuthTypeService
            .getAuthTypeMapByBranchId(branchVos.stream().map(BranchVo::getBranchId).collect(Collectors.toList()));
        if (relation == null) {
            return new HashMap<>();
        }
        Map<Long, Long> studentIdToAuthTypeIdMap = relation.getStudentIdToAuthTypeIdMap();
        Map<Long, List<Long>> authTypeVoMap = relation.getRemoteBranchAuthTypeVos().stream()
            .collect(Collectors.toMap(RemoteBranchAuthTypeVo::getBranchAuthTypeId, authType -> {
                List<Long> authProductIdList;
                if (!StringUtils.isEmpty(authType.getProductIds()) && !CollectionUtils.isEmpty(authProductIdList =
                    Arrays.stream(authType.getProductIds().split(",")).map(Long::parseLong).toList())) {
                    return authProductIdList;
                } else {
                    return new ArrayList<>(1);
                }
            }));
        Map<Long, String> branchIdToConfigJsonStrMap = new HashMap<>();
        for (BranchVo branchVo : branchVos) {
            List<Long> productIdList = authTypeVoMap.get(studentIdToAuthTypeIdMap.get(branchVo.getBranchId()));
            if (CollectionUtils.isEmpty(productIdList)) {
                throw new ServiceException("配置保存失败，门店：" + branchVo.getBranchName() + "未配置授权产品");
            }
            BranchConfigVo branchConfigVo = branchIdToConfigMap.get(branchVo.getBranchId());
            List<BranchConfigDto.PreferentialAmountConfig> branchConfigList = new ArrayList<>();
            if (branchConfigVo == null) {
                for (Long productId : productIdList) {
                    if (null != productIdToConfigMap.get(productId)) {
                        branchConfigList.add(productIdToConfigMap.get(productId));
                    }
                }
            } else {
                Map<Long, BranchConfigDto.PreferentialAmountConfig> branchProductIdToConfigMap = BranchConfigJsonHandler
                    .parseConfigJsonList(branchConfigVo.getConfigJson(), BranchConfigDto.PreferentialAmountConfig.class)
                    .stream().collect(
                        Collectors.toMap(BranchConfigDto.PreferentialAmountConfig::getProduceId, Function.identity()));
                for (Long productId : productIdList) {
                    if (productIdToConfigMap.get(productId) != null) {
                        branchConfigList.add(productIdToConfigMap.get(productId));
                    } else if (branchProductIdToConfigMap.get(productId) != null) {
                        branchConfigList.add(branchProductIdToConfigMap.get(productId));
                    }
                }
            }
            branchIdToConfigJsonStrMap.put(branchVo.getBranchId(), BranchConfigJsonHandler.toJson(branchConfigList));
        }
        return branchIdToConfigJsonStrMap;
    }

    @Override
    public BranchConfigTypeEnum getConfigType() {
        return BranchConfigTypeEnum.INTRODUCE_PREFERENTIAL_AMOUNT;
    }
}
