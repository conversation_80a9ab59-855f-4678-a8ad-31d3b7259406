package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.List;
import java.util.Set;

/**
 * 分店业务对象 branch
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Branch.class, reverseConvertGenerate = false)
public class BranchBo extends BaseEntity {

    /**
     * 分店id
     */
    @NotNull(message = "分店id不能为空", groups = { EditGroup.class })
    private Long branchId;

    /**
     * 分店名称
     */
    @NotBlank(message = "分店名称不能为空", groups = { AddGroup.class})
    private String branchName;

    /**
     * 分店状态（0正常 1停用）
     */
    @NotBlank(message = "分店状态（0正常 1停用）不能为空", groups = { AddGroup.class})
    private String branchStatus;

    /**
     * 分店授权记录id
     */
    //@NotNull(message = "分店授权记录id", groups = { AddGroup.class})
    private Long branchAuthRecordId;

    @NotNull(message = "归属部门id不能为空", groups = { AddGroup.class})
    private Long deptParentId;

//    @NotBlank(message = "管理员账号不能为空", groups = { AddGroup.class})
    private String adminUserName;

//    @NotBlank(message = "管理员姓名不能为空", groups = { AddGroup.class})
    private String adminNickName;

    /**
     * 创建部门IDs
     */
    private Set<Long> createDeptIds;

    /**
     * 门店授权类型id
     */
    private Long branchAuthTypeId;

    /**
     * 门店管理员用户id列表
     */
    private List<Long> branchAdminUserIdList;

    /**
     * 执行店长列表
     */
    private List<Long> branchStaffIdList;

    /**
     * 分店ids
     */
    private List<Long> branchIds;

    /**
     * 平板端 自学系统状态（0关闭 1开启）
     */
    private Integer selfStudySystemStatus;
    /**
     * 平板端 学生自讲状态(0关闭 1开启)
     */
    private Integer studentSpeakingStatus;
    /**
     * 平板端 智慧中小学状态(0关闭  1开启)
     */
    private Integer smartPrimarySecondarySchoolStatus;

    /**
     * 平板端 倍速管控(0关闭 1开启)
     */
    private Integer speedControlStatus;
    /**
     * 平板端 拍照管控(0关闭 1开启)
     */
    private Integer photoStatus;
}
