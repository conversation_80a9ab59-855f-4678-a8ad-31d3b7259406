package com.jxw.shufang.branch.controller.management;

import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.branch.domain.bo.BranchControlToggleBo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.branch.service.BranchControlToggleService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 门店管控开关表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 11:38:30
 */
@RestController
@RequestMapping("/management/branch-control-toggle")
@Validated
@RequiredArgsConstructor
@Slf4j
public class BranchControlToggleController extends BaseController {

    private final BranchControlToggleService branchControlToggleService;

    /**
     * 根据管控类型和门店id查询管控列表
     */
    @PostMapping("/list")
    public R<List<BranchFeatureTemplateVo>> queryBranchControlToggle(@RequestParam String controlType) {
        if (StrUtil.isEmpty(controlType)) {
            return R.fail("管控类型不能为空");
        }
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("请选择对应的门店进行操作");
        }
        List<BranchFeatureTemplateVo> list = branchControlToggleService.queryBranchControlToggleByControlTypeAndBranchId(controlType, branchId);
        return R.ok(list);
    }

    /**
     * 修改管控开关状态
     */
    @PutMapping("/update")
    @Log(title = "管控开关", businessType = BusinessType.UPDATE)
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchControlToggleBo branchControlToggleBo) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("请选择对应的门店进行操作");
        }
        branchControlToggleBo.setBranchId(branchId);
        Boolean result = branchControlToggleService.updateByBo(branchControlToggleBo);
        return toAjax(result);
    }

}
