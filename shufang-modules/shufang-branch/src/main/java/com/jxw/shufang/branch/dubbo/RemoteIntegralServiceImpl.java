package com.jxw.shufang.branch.dubbo;


import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.branch.api.RemoteIntegralService;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteIntegralServiceImpl implements RemoteIntegralService {

    private final IIntegralRecordService integralRecordService;

    @Override
    public BigDecimal getIntegralByStudentId(Long studentId) {
        return integralRecordService.getIntegralByStudentId(studentId);
    }

    @Override
    public Map<Long, BigDecimal> getIntegralByStudentIdList(List<Long> studentIdList) {
        return integralRecordService.getIntegralByStudentIdList(studentIdList);
    }
}
