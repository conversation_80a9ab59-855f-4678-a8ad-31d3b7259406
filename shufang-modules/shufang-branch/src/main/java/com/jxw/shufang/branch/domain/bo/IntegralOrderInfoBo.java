package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralOrderInfo;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 积分订单详情业务对象 integral_order_info
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralOrderInfo.class, reverseConvertGenerate = false)
public class IntegralOrderInfoBo extends BaseEntity {

    /**
     * 积分订单详情id
     */
    @NotNull(message = "积分订单详情id不能为空", groups = { EditGroup.class })
    private Long integralOrderInfoId;

    /**
     * 积分订单id
     */
    @NotNull(message = "积分订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralOrderId;

    /**
     * 积分商品id
     */
    @NotNull(message = "积分商品id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralGoodId;

    /**
     * 积分商品图片（oss_id）
     */
    @NotNull(message = "积分商品图片（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralGoodImg;

    /**
     * 积分商品名称
     */
    @NotBlank(message = "积分商品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodName;

    /**
     * 消耗积分
     */
    @NotNull(message = "消耗积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟）
     */
    @NotBlank(message = "商品类型（1实物 2虚拟）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralGoodType;


}
