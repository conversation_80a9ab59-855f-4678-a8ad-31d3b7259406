package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.Banner;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 轮播图业务对象 banner
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Banner.class, reverseConvertGenerate = false)
public class BannerBo extends BaseEntity {

    /**
     * 轮播图id
     */
    @NotNull(message = "轮播图id不能为空", groups = { EditGroup.class })
    private Long bannerId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 轮播图状态（1上架 2下架）
     */
    @NotBlank(message = "轮播图状态（1上架 2下架）不能为空", groups = { EditGroup.class })
    private String bannerStatus;

    /**
     * 轮播图名称
     */
    @NotBlank(message = "轮播图名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bannerName;

    /**
     * 轮播图（oss_id）
     */
    @NotNull(message = "轮播图（oss_id）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bannerImg;

    /**
     * 轮播图类型（对应字典值）
     */
    //@NotBlank(message = "轮播图类型（对应字典值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bannerType;

    private Boolean withBranchInfo;

    private List<Long> branchIdList;

}
