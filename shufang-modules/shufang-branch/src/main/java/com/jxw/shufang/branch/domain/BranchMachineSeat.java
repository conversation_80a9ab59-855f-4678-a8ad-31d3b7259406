package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 机位对象 branch_machine_seat
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_machine_seat")
public class BranchMachineSeat extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机位id
     */
    @TableId(value = "branch_machine_seat_id")
    private Long branchMachineSeatId;

    /**
     * 分店机位分区id
     */
    private Long branchMachineRegionId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 机位号（数字，如20号机位，则为20）
     */
    private Long seatNo;

    /**
     * 使用开始时间（对应学习规划中某课程的学习开始时间）
     */
    private Date useStartTime;

    /**
     * 使用结束时间（对应学习规划中某课程的学习结束时间）
     */
    private Date useEndTime;


}
