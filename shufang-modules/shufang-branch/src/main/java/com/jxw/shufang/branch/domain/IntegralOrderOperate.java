package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）对象 integral_order_operate
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_order_operate")
public class IntegralOrderOperate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单操作id
     */
    @TableId(value = "integral_order_operate_id")
    private Long integralOrderOperateId;

    /**
     * 积分订单id
     */
    private Long integralOrderId;

    /**
     * 订单状态（1待兑换 2已兑换 3已取消）
     */
    private String integralOrderOperateStatus;

    /**
     * 消耗积分
     */
    private BigDecimal paymentIntegral;

    /**
     * 备注
     */
    private String orderOperateRemark;


}
