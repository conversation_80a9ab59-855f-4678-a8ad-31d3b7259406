package com.jxw.shufang.branch.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/11
 */
@Data
@AllArgsConstructor
public class BranchConfigItemDto {
    /**
     * 配置项的显示名称
     */
    private String label;
    /**
     * 数据类型（string、number、boolean、date等），指示前端如何渲染该字段。
     */
    private String type;
    /**
     * 字段名，用于映射后端保存的配置字段。
     */
    private String propertiesName;
    /**
     * 当前值，前端根据这个值展示和编辑配置。
     */
    private Object value;
    /**
     * 是否为必填项。
     */
    private Boolean required;
    /**
     * 是否展示
     */
    private Boolean show;
    /**
     * 是否可以编辑
     */
    private Boolean editable;
    /**
     * 针对数值类型，定义最小值和最大值。
     */
    private Number min;
    /**
     * 针对数值类型，定义最小值和最大值。
     */
    private Number max;
    /**
     * 输入框的提示信息。
     */
    private String placeholder;
    /**
     * 下拉框或多选框的选项（对于选择类型配置）。
     */
    private List<String> options;
}
