package com.jxw.shufang.branch.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchAuthTypeVoConvert extends BaseMapper<BranchAuthTypeVo, RemoteBranchAuthTypeVo> {
}
