package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.BranchAuthRecord;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分店授权记录
（时间逆序的最后一条记录和分店中的对应）业务对象 branch_auth_record
 *
 *
 * @date 2024-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchAuthRecord.class, reverseConvertGenerate = false)
public class BranchAuthRecordBo extends BaseEntity {

    /**
     * 分店授权记录id
     */
    @NotNull(message = "分店授权记录id不能为空", groups = { EditGroup.class })
    private Long branchAuthRecordId;

    /**
     * 分店id
     */
    @NotNull(message = "分店id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 分店授权类型id
     */
    @NotNull(message = "分店授权类型id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    @NotBlank(message = "分店授权名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    @NotBlank(message = "分店授权描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    @NotNull(message = "分店授权天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    @NotNull(message = "分店授权费用不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal branchAuthTypeCost;

    /**
     * 分店授权开始时间
     */
    private Date branchAuthStartTime;


}
