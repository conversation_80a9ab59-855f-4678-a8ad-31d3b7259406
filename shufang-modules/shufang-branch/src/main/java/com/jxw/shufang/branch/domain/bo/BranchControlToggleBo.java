package com.jxw.shufang.branch.domain.bo;

import com.jxw.shufang.branch.domain.BranchControlToggle;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AutoMapper(target = BranchControlToggle.class)
public class BranchControlToggleBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long branchControlToggleId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 管控功能模板Id
     */
    @NotNull(message = "管控功能模板Id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long branchFeatureTemplateId;

    /**
     * 开关状态（默认0: 关闭, 1: 开启）
     */
    @NotNull(message = "管控功能模板Id不能为空", groups = {EditGroup.class})
    private Integer status;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
