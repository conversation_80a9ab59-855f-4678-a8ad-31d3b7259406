package com.jxw.shufang.branch.dubbo;


import com.jxw.shufang.branch.api.RemoteBranchControlStudentService;

import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.branch.service.BranchControlStudentService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBranchControlStudentServiceImpl implements RemoteBranchControlStudentService {

    private final BranchControlStudentService branchControlStudentService;

    @Override
    public List<Long> selectStudentListByBranchIdAndTemplateId(Long branchId, Long branchFeatureTemplateId) {
        List<BranchControlStudentVo> branchControlStudentVos = branchControlStudentService.selectStudentListByBranchIdAndTemplateId(branchId, branchFeatureTemplateId);

        return branchControlStudentVos.stream().map(BranchControlStudentVo::getStudentId).toList();
    }
}
