package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.BranchAuthType;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 分店授权类型视图对象 branch_auth_type
 *
 *
 * @date 2024-02-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchAuthType.class)
public class BranchAuthTypeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权类型id
     */
    @ExcelProperty(value = "分店授权类型id")
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    @ExcelProperty(value = "分店授权名称")
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    @ExcelProperty(value = "分店授权描述")
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    @ExcelProperty(value = "分店授权天数")
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    @ExcelProperty(value = "分店授权费用")
    private BigDecimal branchAuthTypeCost;

    /**
     * 课程ids
     */
    @ExcelProperty(value = "课程ids")
    private String courseIds;

    /**
     * 产品ids
     */
    @ExcelProperty(value = "产品ids")
    private String productIds;

    /**
     * 创建时间
     */
    private Date createTime;


}
