package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.IntegralGoodBo;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 积分商品Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IIntegralGoodService {

    /**
     * 查询积分商品
     */
    IntegralGoodVo queryById(Long integralGoodId);

    /**
     * 查询积分商品列表
     */
    TableDataInfo<IntegralGoodVo> queryPageList(IntegralGoodBo bo, PageQuery pageQuery);

    /**
     * 查询积分商品列表
     */
    List<IntegralGoodVo> queryList(IntegralGoodBo bo);

    /**
     * 新增积分商品
     */
    Boolean insertByBo(IntegralGoodBo bo);

    /**
     * 修改积分商品
     */
    Boolean updateByBo(IntegralGoodBo bo);

    /**
     * 校验并批量删除积分商品信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新库存
     *
     * @param integralGoodId 积分良id
     * @param i              我
     *
     * @date 2024/04/25 03:16:09
     */
    Boolean updateStock(Long integralGoodId, int i);

    /**
     * 放入积分商品图片地址url
     * @param list
     */
    void putIntegralGoodImgUrl(List<IntegralGoodVo> list);
}
