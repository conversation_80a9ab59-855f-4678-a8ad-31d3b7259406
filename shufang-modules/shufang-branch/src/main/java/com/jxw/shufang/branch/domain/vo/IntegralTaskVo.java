package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.branch.enums.TaskEnum;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 积分任务视图对象 integral_task
 *
 *
 * @date 2024-04-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralTask.class)
public class IntegralTaskVo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分任务id
     */
    @ExcelProperty(value = "积分任务id")
    private Long integralTaskId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 积分任务名称 $为占位符，查询的时候需要把他换成字段param
     */
    @ExcelProperty(value = "积分任务名称 $为占位符，查询的时候需要把他换成字段param")
    private String integralTaskName;

    /**
     * 积分任务填充以后的名称
     */
    @ExcelProperty(value = "积分任务填充以后的名称")
    private String integralTaskNameFill;

    /**
     * 参数，触发器和任务名称会使用到他
     */
    @ExcelProperty(value = "参数，触发器和任务名称会使用到他")
    private String param;

    /**
     * 积分数值（正整数）
     */
    @ExcelProperty(value = "积分数值")
    private Integer integralNum;

    /**
     * 对应程序中的触发器
     */
    @ExcelProperty(value = "对应程序中的触发器")
    private String taskTrigger;

    /**
     * 任务上下架状态（1上架 2下架）
     */
    @ExcelProperty(value = "任务上下架状态（1上架 2下架）")
    private String taskStatus;

    /**
     * 任务完成状态
     */
    private TaskFinishedStatusEnum taskFinishedStatusEnum;

    private BranchVo branch;

    private Date createTime;

    private String createDeptName;

    public static IntegralTaskVo buildIntegralTaskVo(TaskEnum taskEnum) {
        IntegralTaskVo integralTaskVo = new IntegralTaskVo();
        integralTaskVo.setTaskTrigger(taskEnum.toString());
        integralTaskVo.setIntegralNum(taskEnum.getDefaultIntegralNum());
        integralTaskVo.setTaskStatus(UserConstants.INTEGRAL_TASK_STATUS_DOWN);
        integralTaskVo.setIntegralTaskName(taskEnum.getIntegralTaskName());
        integralTaskVo.setParam(taskEnum.getDefaultParam());
        return integralTaskVo;
    }

}
