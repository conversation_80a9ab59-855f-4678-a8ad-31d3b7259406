package com.jxw.shufang.branch.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.student.api.RemoteStudyVideoRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyVideoRecordBo;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.jxw.shufang.branch.handler.TaskHandler.BASE_NAME;

@RequiredArgsConstructor
@Service("STUDY_TASK"+BASE_NAME)
public class StudyTaskHandler implements TaskHandler {
    private final IIntegralRecordService integralRecordService;

    @DubboReference
    private RemoteStudyVideoRecordService remoteStudyVideoRecordService;

    @Override
    public TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId) {
        Long integralTaskId = integralTaskVo.getIntegralTaskId();
        if (integralTaskId == null||studentId == null){
            return TaskFinishedStatusEnum.RECEIVED;
        }

        String param = integralTaskVo.getParam();
        long seconds = 0;
        if (StringUtils.isBlank(param)) {
            throw new ServiceException("请管理员配置任务参数");
        }
        try {
            long minutes = Long.parseLong(param.trim());
            //分钟转为秒
            seconds = minutes * 60L;
        } catch (Exception e) {
            throw new ServiceException("请管理员配置正确的任务参数");
        }
        //查记录
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setTaskId(integralTaskId);
        integralRecordBo.setCreateDateStr(DateUtils.getDate());
        List<IntegralRecordVo> integralRecordVos = integralRecordService.queryList(integralRecordBo);
        if (CollUtil.isNotEmpty(integralRecordVos)){
            return TaskFinishedStatusEnum.RECEIVED;
        }


        //查业务
        Date now = new Date();
        DateTime startDate = DateUtil.beginOfDay(now);
        DateTime endDate = DateUtil.endOfDay(now);
        RemoteStudyVideoRecordBo remoteStudyVideoRecordBo = new RemoteStudyVideoRecordBo();
        remoteStudyVideoRecordBo.setStudentId(studentId);
        remoteStudyVideoRecordBo.setVideoRecordCreateDateStart(startDate);
        remoteStudyVideoRecordBo.setVideoRecordCreateDateEnd(endDate);
        remoteStudyVideoRecordBo.setGeStudyVideoDuration(seconds);
        Long count = remoteStudyVideoRecordService.count(remoteStudyVideoRecordBo,true);
        if (count!= null && count > 0){
            return TaskFinishedStatusEnum.UNRECEIVED;
        }
        return TaskFinishedStatusEnum.UNFINISHED;
    }

    @Override
    public void finishTask(IntegralTaskVo integralTaskVo,Long studentId){
        TaskFinishedStatusEnum status = getStatus(integralTaskVo, studentId);
        if (!TaskFinishedStatusEnum.UNRECEIVED.equals(status)) {
            throw new ServiceException("错误状态，不可领取");
        }
        //添加记录
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
        integralRecordBo.setChangeNum(new BigDecimal(integralTaskVo.getIntegralNum()==null?0:integralTaskVo.getIntegralNum()));
        integralRecordBo.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setChangeReason(StrUtil.blankToDefault(integralTaskVo.getIntegralTaskNameFill(),integralTaskVo.getIntegralTaskName()));
        integralRecordBo.setTaskId(integralTaskVo.getIntegralTaskId());

        Boolean b = integralRecordService.insertByBo(integralRecordBo);
        if (!b) {
            throw new ServiceException("领取失败");
        }
    }
}
