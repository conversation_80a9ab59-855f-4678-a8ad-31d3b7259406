package com.jxw.shufang.branch.controller.android;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.TOTPGenerator;
import com.jxw.shufang.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;


@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/branch")
@Slf4j
public class ABranchController extends BaseController {

    /**
     * 门店平板端管控
     */
    @GetMapping("/getCode")
    public  R<Boolean>  generateTOTP(String code) {
        try {
            String s = TOTPGenerator.generateTOTP();
            return R.ok(s.equals(code));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("门店平板端管控获取动态验证码失败:",e);
            throw  new ServiceException("服务繁忙请稍后再试");
        }
    }
}
