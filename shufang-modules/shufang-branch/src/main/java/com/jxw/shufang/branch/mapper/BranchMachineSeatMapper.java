package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.branch.domain.BranchMachineSeat;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 机位Mapper接口
 *
 *
 * @date 2024-03-18
 */
public interface BranchMachineSeatMapper extends BaseMapperPlus<BranchMachineSeat, BranchMachineSeatVo> {

    //@DataPermission(
    //    @DataColumn(key = "deptName",value = "t.create_dept")
    //)
    List<BranchMachineSeatVo> selectSeatList(@Param(Constants.WRAPPER) QueryWrapper<BranchMachineSeat> lqw);
}
