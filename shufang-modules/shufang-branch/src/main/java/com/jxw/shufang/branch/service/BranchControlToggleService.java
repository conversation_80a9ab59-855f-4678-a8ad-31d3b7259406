package com.jxw.shufang.branch.service;


import com.jxw.shufang.branch.domain.bo.BranchControlToggleBo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;

import java.util.List;

/**
 * <p>
 * 门店管控开关表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 11:38:30
 */
public interface BranchControlToggleService {


    List<BranchFeatureTemplateVo> queryBranchControlToggleByControlTypeAndBranchId(String controlType, Long branchId);

    Boolean updateByBo(BranchControlToggleBo branchControlToggleBo);

    List<BranchFeatureTemplateVo> queryBranchControlToggleByStudentIdAndBranchId(Long studentId, Long branchId);

}
