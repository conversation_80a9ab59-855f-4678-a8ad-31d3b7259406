package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分记录
 * 前端访问路由地址为:/branch/integralRecord
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralRecord")
public class IntegralRecordController extends BaseController {

    private final IIntegralRecordService integralRecordService;

    /**
     * 查询积分记录列表
     */
    @SaCheckPermission("branch:integralRecord:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralRecordVo> list(IntegralRecordBo bo, PageQuery pageQuery) {
        return integralRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分记录列表
     */
    @SaCheckPermission("branch:integralRecord:export")
    @Log(title = "积分记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IntegralRecordBo bo, HttpServletResponse response) {
        List<IntegralRecordVo> list = integralRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分记录", IntegralRecordVo.class, response);
    }

    /**
     * 获取积分记录详细信息
     *
     * @param integralRecordId 主键
     */
    @SaCheckPermission("branch:integralRecord:query")
    @GetMapping("/{integralRecordId}")
    public R<IntegralRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralRecordId) {
        return R.ok(integralRecordService.queryById(integralRecordId));
    }

    /**
     * 新增积分记录
     */
    @SaCheckPermission("branch:integralRecord:add")
    @Log(title = "积分记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralRecordBo bo) {
        return toAjax(integralRecordService.insertByBo(bo));
    }

    /**
     * 修改积分记录
     */
    @SaCheckPermission("branch:integralRecord:edit")
    @Log(title = "积分记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralRecordBo bo) {
        return toAjax(integralRecordService.updateByBo(bo));
    }

    /**
     * 删除积分记录
     *
     * @param integralRecordIds 主键串
     */
    @SaCheckPermission("branch:integralRecord:remove")
    @Log(title = "积分记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralRecordIds) {
        return toAjax(integralRecordService.deleteWithValidByIds(List.of(integralRecordIds), true));
    }

    /**
     * 查询会员积分余额
     */
    @SaCheckPermission("branch:integralRecord:query")
    @GetMapping("/queryStudentIntegral")
    public R<BigDecimal> queryMemberIntegral(@NotNull(message = "会员ID不能为空") @RequestParam Long studentId) {
        return R.ok(integralRecordService.getIntegralByStudentId(studentId));
    }
}
