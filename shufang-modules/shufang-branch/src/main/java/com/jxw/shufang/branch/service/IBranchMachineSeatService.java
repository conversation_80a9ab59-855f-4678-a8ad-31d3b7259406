package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.BranchMachineSeat;
import com.jxw.shufang.branch.domain.bo.BranchMachineSeatBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 机位Service接口
 *
 *
 * @date 2024-03-18
 */
public interface IBranchMachineSeatService {

    /**
     * 查询机位
     */
    BranchMachineSeatVo queryById(Long branchMachineSeatId);

    /**
     * 查询机位列表
     */
    TableDataInfo<BranchMachineSeatVo> queryPageList(BranchMachineSeatBo bo, PageQuery pageQuery);

    /**
     * 查询机位列表
     */
    List<BranchMachineSeatVo> queryList(BranchMachineSeatBo bo);

    /**
     * 新增机位
     */
    Boolean insertByBo(BranchMachineSeatBo bo);

    /**
     * 批量新增机位
     *
     * @param boList 机位信息
     *
     * @date 2024/03/19 04:47:22
     */
    Boolean insertBatchByBo(List<BranchMachineSeatBo> boList);

    /**
     * 修改机位
     */
    Boolean updateByBo(BranchMachineSeatBo bo);

    /**
     * 校验并批量删除机位信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 按座位号删除
     *
     * @param branchMachineRegionId 分支机区域id
     * @param delNumList            零件编号列表
     *
     * @date 2024/03/19 06:50:18
     */
    Boolean deleteBySeatNo(Long branchMachineRegionId, List<Long> delNumList);

     BranchMachineSeat queryBranchMachineSeatById(Long branchMachineSeatId);

    void cleanCache();

}
