package com.jxw.shufang.branch.controller.android;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralOrderBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderVo;
import com.jxw.shufang.branch.service.IIntegralOrderService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.IntegralOrderStatusEnum;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 积分订单-平板端
 * 前端访问路由地址为:/branch/integralOrder
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/integralOrder")
public class AIntegralOrderController extends BaseController {

    private final IIntegralOrderService integralOrderService;

    /**
     * 兑换商品-平板端
     */
    @PostMapping("/exchange")
    @Log(title = "平板端-兑换商品", businessType = BusinessType.INSERT)
    public R<Void> exchange(@NotNull(message = "主键不能为空")
                            Long integralGoodId) {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        integralOrderService.exchange(integralGoodId, LoginHelper.getStudentId());
        return R.ok();
    }

    /**
     * 积分兑换记录，学生端的，一般不会有很多记录，这里直接查询全部列表-平板端
     */
    @GetMapping("/exchangeList")
    public R<List<IntegralOrderVo>> list() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        IntegralOrderBo integralOrderBo = new IntegralOrderBo();
        integralOrderBo.setStudentId(LoginHelper.getStudentId());
        integralOrderBo.setIntegralOrderOperateStatusList(List.of(IntegralOrderStatusEnum.EXCHANGED.getCode(), IntegralOrderStatusEnum.WAIT_EXCHANGE.getCode()));
        integralOrderBo.setOrderBy("t.create_time desc");
        return R.ok(integralOrderService.queryOrderList(integralOrderBo));
    }

}
