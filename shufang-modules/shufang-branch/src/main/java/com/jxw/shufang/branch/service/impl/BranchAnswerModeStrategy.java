package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.dto.BranchAnswerModeDto;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.enums.AnswerModeEnum;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.service.BranchConfigStrategy;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.json.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 作答模式 门店配置策略
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BranchAnswerModeStrategy implements BranchConfigStrategy {
    /**
     * 获取门店配置项
     *
     * @param branchConfigVo
     */
    @Override
    public void buildConfigItems(BranchConfigVo branchConfigVo) {
        //获取json配置
        String configJson = branchConfigVo.getConfigJson();
        JSONArray jsonArray = JSON.parseArray(configJson);

        //默认线上作答
        AnswerModeEnum answerModeEnum = Optional.ofNullable(jsonArray)
            .filter(arr -> !arr.isEmpty())
            .map(arr -> arr.getJSONObject(0))
            .filter(obj -> ObjectUtil.isNotEmpty(obj) && obj.containsKey("modeCode"))
            .map(obj -> obj.getInteger("modeCode"))
            .map(AnswerModeEnum::getByCode)
            .orElse(AnswerModeEnum.ONLINE_ANSWER);

        //获取所有模式JSON
        AnswerModeEnum[] answerModes = AnswerModeEnum.values();
        List<BranchAnswerModeDto> modeDtos = Arrays.stream(answerModes)
            .map(mode -> {
                BranchAnswerModeDto modeDto = new BranchAnswerModeDto();
                modeDto.setModeCode(mode.getModeCode());
                modeDto.setModeName(mode.getModeName());
                modeDto.setChecked(answerModeEnum.getModeCode().equals(mode.getModeCode()));
                return modeDto;
            }).collect(Collectors.toList());

        //设置响应对象
        branchConfigVo.setConfigData(modeDtos);
    }

    /**
     * 更新配置时处理验证，过滤，返回配置的json
     *
     * @param bo
     * @return
     */
    @Override
    public String buildConfigJson(BranchConfigBo bo) {
        String configData = JsonUtils.toJsonString(bo.getConfigData());
        if (StringUtils.isEmpty(configData)) {
            throw new ServiceException("请输入配置");
        }

        JSONArray jsonArray = JSON.parseArray(configData);
        AnswerModeEnum answerModeEnum = Optional.ofNullable(jsonArray)
            .filter(arr -> !arr.isEmpty())
            .map(arr -> arr.getJSONObject(0))
            .filter(obj -> ObjectUtil.isNotEmpty(obj) && obj.containsKey("modeCode"))
            .map(obj -> obj.getInteger("modeCode"))
            .map(AnswerModeEnum::getByCode)
            .orElseThrow(() -> new ServiceException("请输入正确配置"));
        return configData;
    }

    /**
     * 湖区
     *
     * @param configJson          选择的门店配置
     * @param branchVos           被同步的门店信息
     * @param branchIdToConfigMap 被同步的门店id与配置的json
     * @return
     */
    @Override
    public Map<Long, String> getDuplicateConfigJson(String configJson, List<BranchVo> branchVos, Map<Long, BranchConfigVo> branchIdToConfigMap) {
        Map<Long, String> result = branchVos.stream().collect(Collectors.toMap(BranchVo::getBranchId, branchVo -> configJson));
        return result;
    }

    @Override
    public BranchConfigTypeEnum getConfigType() {
        return BranchConfigTypeEnum.ANSWER_MODE;
    }

}
