package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 分店对象 branch
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch")
public class Branch extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店id
     */
    @TableId(value = "branch_id")
    private Long branchId;

    /**
     * 分店名称
     */
    private String branchName;

    /**
     * 分店状态（0正常 1停用）
     */
    private String branchStatus;

    /**
     * 分店授权记录id
     */
    private Long branchAuthRecordId;

    /**
     * 平板端 自学系统状态（0关闭 1开启）
     */
    private Integer selfStudySystemStatus;
    /**
     * 平板端 学生自讲状态(0关闭 1开启)
     */
    private Integer studentSpeakingStatus;
    /**
     * 平板端 智慧中小学状态(0关闭  1开启)
     */
    private Integer smartPrimarySecondarySchoolStatus;
    /**
     * 平板端 倍速管控(0关闭 1开启)
     */
    private Integer speedControlStatus;
    /**
     * 平板端 拍照管控(0关闭 1开启)
     */
    private Integer photoStatus;

    /**
     * 剩余天数时长（最小单位为天）
     */
    private Integer remainTime;
}
