package com.jxw.shufang.branch.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: cyj
 * @date: 2025/3/10
 */
public class BranchConfigDto {
    /**
     * 产品优惠配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreferentialAmountConfig {
        /**
         * 产品id
         */
        private Long produceId;
        /**
         * 优惠金额
         */
        private BigDecimal preferentialAmount;
        /**
         * 冻结日期
         */
        private Integer frozenDay;
        /**
         * 产品名称
         */
        private String produceName;
    }
}
