package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 积分订单对象 integral_order
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_order")
public class IntegralOrder extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单id
     */
    @TableId(value = "integral_order_id")
    private Long integralOrderId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 顾问id
     */
    private Long consultantId;

    /**
     * 积分订单编号
     */
    private String integralOrderNo;

    /**
     * 积分操作id
     */
    private Long integralOrderOperateId;


}
