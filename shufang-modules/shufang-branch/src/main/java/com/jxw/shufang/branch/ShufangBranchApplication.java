package com.jxw.shufang.branch;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 门店模块
 *
 *
 */
@EnableDubbo
@SpringBootApplication
public class ShufangBranchApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangBranchApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  门店模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
