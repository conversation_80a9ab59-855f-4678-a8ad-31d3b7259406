package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 积分商品对象 integral_good
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_good")
public class IntegralGood extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分商品id
     */
    @TableId(value = "integral_good_id")
    private Long integralGoodId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 积分商品图片（oss_id）
     */
    private Long integralGoodImg;

    /**
     * 积分商品名称
     */
    private String integralGoodName;

    /**
     * 商品简介
     */
    private String integralGoodIntro;

    /**
     * 商品描述
     */
    private String integralGoodDesc;

    /**
     * 消耗积分
     */
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟）
     */
    private String integralGoodType;

    /**
     * 兑换规则（1仅可兑换一次 2不限次数）
     */
    private String integralGoodRule;

    /**
     * 商品库存
     */
    private Long integralGoodStock;

    /**
     * 商品状态（1上架 2下架）
     */
    private String integralGoodStatus;

    /**
     * 商品排序（数字越小越靠前）
     */
    private Long integralGoodSort;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
