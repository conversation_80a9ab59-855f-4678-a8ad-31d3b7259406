package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.domain.Banner;
import com.jxw.shufang.branch.domain.bo.BannerBo;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.vo.BannerVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.mapper.BannerMapper;
import com.jxw.shufang.branch.service.IBannerService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.student.api.RemoteStudentConsultantRecordService;
import com.jxw.shufang.student.api.RemoteStudentService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 轮播图Service业务层处理
 *
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class BannerServiceImpl implements IBannerService {

    private final BannerMapper baseMapper;

    private final IBranchService branchService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;

    @DubboReference
    private RemoteStudentService remoteStudentService;


    /**
     * 查询轮播图
     */
    @Override
    public BannerVo queryById(Long bannerId) {
        return baseMapper.selectVoById(bannerId);
    }

    /**
     * 查询轮播图列表
     */
    @Override
    public TableDataInfo<BannerVo> queryPageList(BannerBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        LambdaQueryWrapper<Banner> lqw = buildQueryWrapper(bo);
        Page<BannerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void handleQueryParam(BannerBo bo) {
        if (bo.getBannerId() != null) {
            return;
        }
        if (!LoginHelper.isSuperAdmin()) {
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }
    }

    /**
     * 查询轮播图列表
     */
    @Override
    public List<BannerVo> queryList(BannerBo bo) {
        LambdaQueryWrapper<Banner> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Banner> buildQueryWrapper(BannerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Banner> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, Banner::getBranchId, bo.getBranchId());
        lqw.eq(StringUtils.isNotBlank(bo.getBannerStatus()), Banner::getBannerStatus, bo.getBannerStatus());
        lqw.like(StringUtils.isNotBlank(bo.getBannerName()), Banner::getBannerName, bo.getBannerName());
        lqw.eq(bo.getBannerImg() != null, Banner::getBannerImg, bo.getBannerImg());
        lqw.eq(StringUtils.isNotBlank(bo.getBannerType()), Banner::getBannerType, bo.getBannerType());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), Banner::getBranchId, bo.getBranchIdList());
        return lqw;
    }

    /**
     * 新增轮播图
     */
    @Override
    public Boolean insertByBo(BannerBo bo) {

        if (CollUtil.isNotEmpty(bo.getBranchIdList())){
            List<Banner> insertList = new ArrayList<>();
            for (Long branchId : bo.getBranchIdList()) {

                Banner banner = new Banner();
                banner.setBannerName(bo.getBannerName());
                banner.setBannerImg(bo.getBannerImg());
                banner.setBannerType(bo.getBannerType());
                banner.setBannerStatus(bo.getBannerStatus());
                banner.setBranchId(branchId);
                banner.setBannerType(bo.getBannerType());
                insertList.add(banner);
            }
            boolean flag = baseMapper.insertBatch(insertList);
            return flag;
        }else {
            Banner add = MapstructUtils.convert(bo, Banner.class);
            validEntityBeforeSave(add);
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setBannerId(add.getBannerId());
            }
            return flag;
        }
    }

    /**
     * 修改轮播图
     */
    @Override
    public Boolean updateByBo(BannerBo bo) {
        Banner update = MapstructUtils.convert(bo, Banner.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Banner entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除轮播图
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    public void putBranchInfo(List<BannerVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> branchIds = list.stream().map(BannerVo::getBranchId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }

        BranchBo branchBo = new BranchBo();
        branchBo.setBranchIds(branchIds);
        List<BranchVo> branchVoList = branchService.selectSimpleBranInfo(branchBo);
        Map<Long, BranchVo> branchVoMap = branchVoList.stream().collect(Collectors.toMap(BranchVo::getBranchId, vo -> vo));
        list.forEach(item -> {
            BranchVo vo = branchVoMap.get(item.getBranchId());
            item.setBranch(vo);
        });

    }

}
