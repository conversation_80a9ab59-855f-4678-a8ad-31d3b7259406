package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.BranchMachineRegion;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 分店机位分区视图对象 branch_machine_region
 *
 *
 * @date 2024-03-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchMachineRegion.class)
public class BranchMachineRegionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店机位分区id
     */
    @ExcelProperty(value = "分店机位分区id")
    private Long branchMachineRegionId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 分区名称
     */
    @ExcelProperty(value = "分区名称")
    private String regionName;

    /**
     * 分区数量
     */
    @ExcelProperty(value = "分区数量")
    private Long regionNum;

    /**
     * 机位图片（oss_id 每个分区的机位分布示意图）
     */
    @ExcelProperty(value = "机位图片", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "o=ss_id,每=个分区的机位分布示意图")
    private Long regionImg;


    private Date createTime;

    private BranchVo branch;


}
