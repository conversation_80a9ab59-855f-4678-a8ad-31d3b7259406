package com.jxw.shufang.branch.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 管控功能配置模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@Data
@AutoMapper(target = BranchFeatureTemplate.class)
public class BranchFeatureTemplateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    private Long branchFeatureTemplateId;

    /**
     * 功能名称（唯一标识）
     */
    private String featureName;

    /**
     * 管控类型
     */
    private String featureType;

    /**
     * 关联管控类型（1督学/2自学）
     */
    private Integer controlType;

    /**
     * 功能详细描述
     */
    @JsonIgnore
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 开关状态（0: 关闭, 1: 开启）
     */
    private Integer status;


    /**
     * 学生管控状态
     */
    private Integer studentControlStatus;

}
