package com.jxw.shufang.branch.controller.android;

import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.branch.service.BranchControlToggleService;
import com.jxw.shufang.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/branch-control-toggle")
public class ABranchControlToggleController {

    private final BranchControlToggleService branchControlToggleService;

    /**
     * 查询门店的管控列表(包含学生的白名单情况)
     */
    @PostMapping("/list")
    public R<List<BranchFeatureTemplateVo>> queryBranchControlToggleByStudentIdAndBranchId(@RequestParam Long branchId,
                                                                                           @RequestParam(required = false) Long studentId) {

        List<BranchFeatureTemplateVo> list = branchControlToggleService.queryBranchControlToggleByStudentIdAndBranchId(studentId, branchId);
        return R.ok(list);
    }
}
