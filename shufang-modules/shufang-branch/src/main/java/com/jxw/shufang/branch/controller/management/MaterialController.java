package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.MaterialBo;
import com.jxw.shufang.branch.domain.vo.MaterialVo;
import com.jxw.shufang.branch.service.IMaterialService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 物料
 * 前端访问路由地址为:/branch/material
 *
 *
 * @date 2024-02-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/material")
public class MaterialController extends BaseController {

    private final IMaterialService materialService;

    /**
     * 查询物料列表
     */
    @SaCheckPermission("branch:material:list")
    @GetMapping("/list")
    public TableDataInfo<MaterialVo> list(MaterialBo bo, PageQuery pageQuery) {
        return materialService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出物料列表
     */
    @SaCheckPermission("branch:material:export")
    @Log(title = "物料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MaterialBo bo, HttpServletResponse response) {
        List<MaterialVo> list = materialService.queryList(bo);
        ExcelUtil.exportExcel(list, "物料", MaterialVo.class, response);
    }

    /**
     * 获取物料详细信息
     *
     * @param materialId 主键
     */
    @SaCheckPermission("branch:material:query")
    @GetMapping("/{materialId}")
    public R<MaterialVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long materialId) {
        return R.ok(materialService.queryById(materialId));
    }

    /**
     * 新增物料
     */
    @SaCheckPermission("branch:material:add")
    @Log(title = "物料", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MaterialBo bo) {
        return toAjax(materialService.insertByBo(bo));
    }

    /**
     * 修改物料
     */
    @SaCheckPermission("branch:material:edit")
    @Log(title = "物料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MaterialBo bo) {
        return toAjax(materialService.updateByBo(bo));
    }


    /**
     * 批量修改物料
     */
    @SaCheckPermission("branch:material:edit")
    @Log(title = "物料", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/batch")
    public R<Void> batchEdit(@Validated(EditGroup.class) @RequestBody List<MaterialBo> boList) {
        for (MaterialBo materialBo : boList) {
            materialService.updateByBo(materialBo);
        }
        return R.ok();
    }

    /**
     * 删除物料
     *
     * @param materialIds 主键串
     */
    @SaCheckPermission("branch:material:remove")
    @Log(title = "物料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{materialIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] materialIds) {
        return toAjax(materialService.deleteWithValidByIds(List.of(materialIds), true));
    }

    /**
     * 清空物料
     *
     * @param materialIds 主键串
     */
    @SaCheckPermission("branch:material:edit")
    @Log(title = "物料", businessType = BusinessType.UPDATE)
    @PutMapping("/clean/{materialIds}")
    public R<Void> clean(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] materialIds) {
        return toAjax(materialService.clean(List.of(materialIds)));
    }


    @SaCheckPermission("branch:material:query")
    @GetMapping("/getMaterialMenuInfo")
    public R<MaterialVo> getMaterialMenuInfo(@NotBlank(message = "物料类型不能为空") String materialType) {
        Long branchId = LoginHelper.getBranchId();
        if (branchId == null) {
            return R.fail("获取门店信息失败");
        }
        return R.ok(materialService.getMaterialMenuInfo(branchId,materialType));
    }

}
