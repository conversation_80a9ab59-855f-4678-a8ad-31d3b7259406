package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.BranchMachineSeat;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 机位视图对象 branch_machine_seat
 *
 *
 * @date 2024-03-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchMachineSeat.class)
public class BranchMachineSeatVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机位id
     */
    @ExcelProperty(value = "机位id")
    private Long branchMachineSeatId;

    /**
     * 分店机位分区id
     */
    @ExcelProperty(value = "分店机位分区id")
    private Long branchMachineRegionId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 机位号（数字，如20号机位，则为20）
     */
    @ExcelProperty(value = "机位号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=字，如20号机位，则为20")
    private Long seatNo;

    /**
     * 使用开始时间（对应学习规划中某课程的学习开始时间）
     */
    @ExcelProperty(value = "使用开始时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应学习规划中某课程的学习开始时间")
    private Date useStartTime;

    /**
     * 使用结束时间（对应学习规划中某课程的学习结束时间）
     */
    @ExcelProperty(value = "使用结束时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应学习规划中某课程的学习结束时间")
    private Date useEndTime;

    /**
     * 分区
     */
    private BranchMachineRegionVo branchMachineRegion;

    /**
     * 座位是否被使用
     */
    private Boolean isUseSeat;


}
