package com.jxw.shufang.branch.domain.vo;

import com.jxw.shufang.branch.domain.BranchConfig;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@AutoMapper(target = BranchConfig.class)
public class BranchConfigVo extends BaseEntity {

    /**
     * 门店参数配置ID
     */
    private Long branchConfigId;
    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 参数配置类型
     */
    private Integer configType;
    /**
     * 配置参数json
     */
    private String configJson;

    /**
     * 配置参数
     */
    private Object configData;

    /**
     * configData 是否是列表
     */
    private Boolean isList;


    @Data
    @AllArgsConstructor
    public static class ConfigType {
        private Integer configType;
        private String configName;
    }
}
