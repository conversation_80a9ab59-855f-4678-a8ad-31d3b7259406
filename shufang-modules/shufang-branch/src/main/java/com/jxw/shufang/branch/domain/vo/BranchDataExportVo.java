package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchVo.class)
public class BranchDataExportVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分店名称
     */
    @ExcelProperty(value = "分店名称", order = 1)
    private String branchName;


    /**
     * 门店会员顾问总数
     */
    @ExcelProperty(value = "门店会员顾问总数", order = 2)
    private Long memberCount;

    /**
     * 会员总数
     */
    @ExcelProperty(value = "会员总数", order = 3)
    private Long totalMemberCount;

    /**
     * 正式会员续费人数
     */
    @ExcelProperty(value = "正式会员续费人数", order = 4)
    private Long formalRenewCount;

    /**
     * 正式会员转化人数
     */
    @ExcelProperty(value = "正式会员转化人数", order = 5)
    private Long formalConversionCount;

    /**
     * 正式会员转化率
     */
    @ExcelProperty(value = "正式会员转化率", order = 6)
    private BigDecimal formalConversionRate;

    /**
     * 在线学习人数
     */
    @ExcelProperty(value = "在线学习人数", order = 7)
    private Long onlineLearnCount;

    /**
     * 每日平均学习时长（秒）
     */
    @ExcelProperty(value = "每日平均学习时长（秒）", order = 8)
    private Long dailyLearnTime;

    /**
     * 学习反馈次数
     */
    @ExcelProperty(value = "学习反馈次数", order = 9)
    private Long feedbackCount;

    /**
     * 订单总数
     */
    @ExcelProperty(value = "订单总数", order = 10)
    private Long orderCount;

    /**
     * 退费订单数
     */
    @ExcelProperty(value = "退费订单数", order = 11)
    private Long refundOrderCount;

    /**
     * 订单营收金额
     */
    @ExcelProperty(value = "订单营收金额", order = 12)
    private BigDecimal orderRevenue;


}
