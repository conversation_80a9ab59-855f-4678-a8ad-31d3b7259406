package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 分店授权类型对象 branch_auth_type
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_auth_type")
public class BranchAuthType extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权类型id
     */
    @TableId(value = "branch_auth_type_id")
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    private BigDecimal branchAuthTypeCost;

    /**
     * 课程ids
     */
    private String courseIds;

    /**
     * 产品ids
     */
    private String productIds;


}
