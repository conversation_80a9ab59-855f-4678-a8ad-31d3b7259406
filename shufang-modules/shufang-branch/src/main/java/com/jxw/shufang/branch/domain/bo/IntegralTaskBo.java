package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 积分任务业务对象 integral_task
 *
 *
 * @date 2024-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralTask.class, reverseConvertGenerate = false)
public class IntegralTaskBo extends BaseEntity {

    /**
     * 积分任务id
     */
    @NotNull(message = "积分任务id不能为空", groups = { EditGroup.class })
    private Long integralTaskId;

    /**
     * 分店id
     */
//    @NotNull(message = "分店id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 积分任务名称 $为占位符，查询的时候需要把他换成字段param
     */
//    @NotBlank(message = "积分任务名称 $为占位符，查询的时候需要把他换成字段param不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralTaskName;

    /**
     * 参数，触发器和任务名称会使用到他
     */
//    @NotBlank(message = "参数，触发器和任务名称会使用到他不能为空", groups = { AddGroup.class, EditGroup.class })
    private String param;

    /**
     * 积分数值（正整数）
     */
//    @NotNull(message = "积分数值（正整数）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer integralNum;

    /**
     * 对应程序中的触发器
     */
//    @NotBlank(message = "对应程序中的触发器不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskTrigger;

    /**
     * 任务上下架状态（1上架 2下架）
     */
//    @NotBlank(message = "任务上下架状态（1上架 2下架）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskStatus;

    private Boolean withBranchInfo;


    private List<Long> branchIdList;

    private List<Long> deptIdList;

}
