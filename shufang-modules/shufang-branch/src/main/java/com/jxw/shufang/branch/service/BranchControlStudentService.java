package com.jxw.shufang.branch.service;


import com.jxw.shufang.branch.domain.bo.BranchControlStudentBo;
import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
public interface BranchControlStudentService {

    boolean insertByBo(BranchControlStudentBo branchControlStudentBo);

    boolean deleteBatchIds(List<Long> branchControlStudentIds );

    TableDataInfo<BranchControlStudentVo> queryPageList(PageQuery pageQuery, BranchControlStudentBo branchControlStudentBo);

    boolean checkStudentInControlTemplate(BranchControlStudentBo branchControlStudentBo);

    List<BranchControlStudentVo> selectStudentListByBranchIdAndTemplateId(Long branchId, Long templateId);

    List<BranchControlStudentVo> queryList(BranchControlStudentBo branchControlStudentBo);
}
