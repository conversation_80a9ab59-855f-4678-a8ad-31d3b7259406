package com.jxw.shufang.branch.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineSeatVo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchMachineSeatVoConvertRemoteBranchMachineSeatVo extends BaseMapper<BranchMachineSeatVo, RemoteBranchMachineSeatVo> {


}
