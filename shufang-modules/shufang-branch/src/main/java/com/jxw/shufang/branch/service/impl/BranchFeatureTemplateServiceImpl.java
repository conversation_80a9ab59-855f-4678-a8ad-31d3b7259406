package com.jxw.shufang.branch.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxw.shufang.branch.domain.BranchControlStudent;
import com.jxw.shufang.branch.domain.BranchControlToggle;
import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.branch.domain.bo.BranchControlStudentBo;
import com.jxw.shufang.branch.domain.bo.BranchFeatureTemplateBo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.branch.mapper.BranchControlToggleMapper;
import com.jxw.shufang.branch.mapper.BranchFeatureTemplateMapper;

import com.jxw.shufang.branch.service.BranchFeatureTemplateService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.web.core.BaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 管控功能配置模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 09:01:07
 */
@Service
@RequiredArgsConstructor
public class BranchFeatureTemplateServiceImpl implements BranchFeatureTemplateService, BaseService {

    private final BranchFeatureTemplateMapper branchFeatureTemplateMapper;
    private final BranchControlToggleMapper branchControlToggleMapper;

    @Override
    public boolean insertByBo(BranchFeatureTemplateBo branchFeatureTemplateBo) {
        BranchFeatureTemplate convert = MapstructUtils.convert(branchFeatureTemplateBo, BranchFeatureTemplate.class);
        if (convert == null) {
            return false;
        }
        return branchFeatureTemplateMapper.insert(convert) > 0;
    }

    @Override
    public boolean updateByBo(BranchFeatureTemplateBo branchFeatureTemplateBo) {
        BranchFeatureTemplate convert = BeanUtil.toBean(branchFeatureTemplateBo, BranchFeatureTemplate.class);
        int update = branchFeatureTemplateMapper.updateById(convert);
        return update > 0;
    }

    @Override
    public boolean deleteByBranchFeatureTemplateIds(List<Long> branchFeatureTemplateIds) {
        // 模板删除了，所有门店都获取不到这个模板
        if (CollUtil.isNotEmpty(branchFeatureTemplateIds)) {
            int deleted = branchFeatureTemplateMapper.deleteBatchIds(branchFeatureTemplateIds);
            if (deleted > 0) {
                // 删除分店和模板的关联表信息
                branchControlToggleMapper.delete(new LambdaQueryWrapper<BranchControlToggle>().in(BranchControlToggle::getBranchFeatureTemplateId, branchFeatureTemplateIds));
            }
            return true;
        }
        return false;
    }

    @Override
    public List<BranchFeatureTemplateVo> queryByControlTypeList(Integer controlType) {
        List<BranchFeatureTemplate> branchFeatureTemplates = branchFeatureTemplateMapper.selectList(new LambdaQueryWrapper<BranchFeatureTemplate>().eq(BranchFeatureTemplate::getControlType, controlType));

        return MapstructUtils.convert(branchFeatureTemplates, BranchFeatureTemplateVo.class);

    }

    @Override
    public List<BranchFeatureTemplateVo> queryList(BranchFeatureTemplateBo branchFeatureTemplateBo) {
        List<BranchFeatureTemplate> branchFeatureTemplates = branchFeatureTemplateMapper.selectList(buildQueryWrapper(branchFeatureTemplateBo));
        return MapstructUtils.convert(branchFeatureTemplates, BranchFeatureTemplateVo.class);
    }

    public LambdaQueryWrapper<BranchFeatureTemplate> buildQueryWrapper(BranchFeatureTemplateBo branchFeatureTemplateBo) {
        if (branchFeatureTemplateBo == null) {
            return new LambdaQueryWrapper<>();
        }
        Long branchFeatureTemplateId = branchFeatureTemplateBo.getBranchFeatureTemplateId();
        Integer status = branchFeatureTemplateBo.getStatus();
        Integer controlType = branchFeatureTemplateBo.getControlType();

        LambdaQueryWrapper<BranchFeatureTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(branchFeatureTemplateId != null, BranchFeatureTemplate::getBranchFeatureTemplateId, branchFeatureTemplateId);
        lambdaQueryWrapper.eq(status != null, BranchFeatureTemplate::getStatus, status);
        lambdaQueryWrapper.eq(controlType != null, BranchFeatureTemplate::getControlType, controlType);
        return lambdaQueryWrapper;
    }
}
