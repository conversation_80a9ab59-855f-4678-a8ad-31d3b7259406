package com.jxw.shufang.branch.handler.impl;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.enums.ScheduleIntegralEnum;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.jxw.shufang.branch.handler.TaskHandler.BASE_NAME;

/**
 * @author: cyj
 * @date: 2025/7/7
 */
@RequiredArgsConstructor
@Service("SCHEDULE_TASK" + BASE_NAME)
public class ScheduleTaskHandler implements TaskHandler {

    private final IIntegralRecordService integralRecordService;

    @Override
    public TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId) {
        Long integralTaskId = integralTaskVo.getIntegralTaskId();
        if (integralTaskId == null || studentId == null) {
            return TaskFinishedStatusEnum.RECEIVED;
        }
        String param = integralTaskVo.getParam();
        Integer dayOfMonthByName = ScheduleIntegralEnum.getDayOfMonthByName(param);
        if (dayOfMonthByName == null) {
            throw new ServiceException("请管理员配置正确的任务参数");
        }
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setTaskId(integralTaskId);
        integralRecordBo.setCreateTimeStart(DateUtils.getStartOfThisMonth());
        // 本类查询任务的都是当前月份固定发放，本月有记录则为已领取，否则为未完成
        List<IntegralRecordVo> integralRecordVos = integralRecordService.queryList(integralRecordBo);
        return CollUtil.isNotEmpty(integralRecordVos) ? TaskFinishedStatusEnum.RECEIVED
            : TaskFinishedStatusEnum.UNFINISHED;
    }

    @Override
    public void finishTask(IntegralTaskVo integralTaskVo, Long studentId) {
        // 该类任务不可以手动领取，等待定时任务自动发放
        throw new ServiceException("领取异常");
    }

    @Override
    public void validTaskRecord(IntegralTask task) {
        Integer dayOfMonthByName = ScheduleIntegralEnum.getDayOfMonthByName(task.getParam());
        if (null == dayOfMonthByName) {
            throw new ServiceException("参数异常");
        }
    }
}
