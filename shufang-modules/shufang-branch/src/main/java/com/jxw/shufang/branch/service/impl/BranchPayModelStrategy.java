package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.jxw.shufang.branch.api.RemoteBranchAuthTypeService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.dto.BranchAuthTypeRelationDto;
import com.jxw.shufang.branch.domain.dto.BranchPayModelConfigDTO;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.service.BranchConfigStrategy;
import com.jxw.shufang.branch.service.IBranchAuthTypeService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/10 20:22
 * @Version 1
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BranchPayModelStrategy implements BranchConfigStrategy {
    @DubboReference
    private final RemoteBranchAuthTypeService remoteBranchAuthTypeService;
    @DubboReference
    private final RemoteProductService remoteProductService;
    private final IBranchAuthTypeService branchAuthTypeService;

    @Value("${branch.pay.model.deadline.days:35}")
    private Integer defaultDeadlineDays;

    @Override
    public void buildConfigItems(BranchConfigVo branchConfigVo) {
        List<RemoteProductVo> productInfoList = this.getProductInfoList(branchConfigVo.getBranchId());
        if (CollectionUtil.isEmpty(productInfoList)) {
            return;
        }

        // 门店产品的配置信息
        Map<Long, BranchPayModelConfigDTO> branchProductConfigMap = this.parseToProductPayModelConfigMap(branchConfigVo.getConfigJson());
        List<BranchPayModelConfigDTO> payModelConfigList = productInfoList.stream()
            .filter(Objects::nonNull)
            .map(product -> this.buildBranchPayModelConfigDTO(branchProductConfigMap, product))
            .collect(Collectors.toList());
        branchConfigVo.setConfigData(payModelConfigList);
    }

    @Override
    public String buildConfigJson(BranchConfigBo bo) {
        String configData = JsonUtils.toJsonString(bo.getConfigData());
        if (StringUtils.isEmpty(configData)) {
            throw new ServiceException("请输入配置");
        }
        List<BranchPayModelConfigDTO> branchPayModelConfigList = JsonUtils.parseArray(configData, BranchPayModelConfigDTO.class);
        if (CollectionUtil.isEmpty(branchPayModelConfigList)) {
            throw new ServiceException("请输入正确配置");
        }

        List<BranchPayModelConfigDTO> existInstallmentProductList = branchPayModelConfigList.stream()
            .filter(f -> null != f.getInstallmentFlag())
            .filter(BranchPayModelConfigDTO::getInstallmentFlag)
            .toList();
        if (CollectionUtil.isEmpty(existInstallmentProductList)) {
            throw new ServiceException("请选择分期产品");
        }

        existInstallmentProductList.forEach(this::validateProductConfig);

        return JsonUtils.toJsonString(existInstallmentProductList);
    }

    @Override
    public Map<Long, String> getDuplicateConfigJson(String configJson,
                                                    List<BranchVo> branchVos,
                                                    Map<Long, BranchConfigVo> branchIdToConfigMap) {
        // 获取原始配置信息
        Map<Long, BranchPayModelConfigDTO> originProductPayModelConfigMap = this.parseToProductPayModelConfigMap(configJson);

        // 获取被同步的门店数据
        Map<Long, List<Long>> syncedBranchProductsMap = this.syncedBranchProductsMap(branchVos);

        Set<Long> validProductIds = new HashSet<>(originProductPayModelConfigMap.keySet());

        Map<Long, String> result = new HashMap<>(branchVos.size());
        syncedBranchProductsMap.entrySet().forEach(entry ->
            this.setBranchConfigJson(originProductPayModelConfigMap, validProductIds, result, entry)
        );
        return result;
    }

    /**
     * 设置门店配置信息
     *
     * @param originProductPayModelConfigMap
     * @param validProductIds
     * @param result
     * @param entry
     */
    private void setBranchConfigJson(Map<Long, BranchPayModelConfigDTO> originProductPayModelConfigMap,
                                     Set<Long> validProductIds,
                                     Map<Long, String> result,
                                     Map.Entry<Long, List<Long>> entry) {
        Long merchantId = entry.getKey();
        List<Long> productIdList = entry.getValue();

        // 获取当前merchant的有效productId（求交集）
        List<Long> validProductIdLists = productIdList.stream()
            .filter(validProductIds::contains)
            .toList();

        if (!validProductIdLists.isEmpty()) {
            List<BranchPayModelConfigDTO> validConfigs = validProductIdLists.stream()
                .map(originProductPayModelConfigMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            result.put(merchantId, JsonUtils.toJsonString(validConfigs));
        }
    }

    /**
     * 获取被同步的门店产品数据
     *
     * @param branchVos
     * @return
     */
    private Map<Long, List<Long>> syncedBranchProductsMap(List<BranchVo> branchVos) {
        // 获取门店ID列表
        List<Long> branchIds = branchVos.stream().map(BranchVo::getBranchId).toList();

        // 获取授权关系数据
        BranchAuthTypeRelationDto relation = branchAuthTypeService.getAuthTypeMapByBranchId(branchIds);
        return relation.getBranchProductsMap();
    }

    /**
     * 通过门店ID获取门店产品信息
     *
     * @param branchId
     * @return
     */
    private List<RemoteProductVo> getProductInfoList(Long branchId) {
        List<Long> branchProductIdList = this.getProductIdByBranchId(branchId);
        if (CollectionUtils.isEmpty(branchProductIdList)) {
            return Collections.emptyList();
        }

        List<RemoteProductVo> productInfoList = this.queryRemoteProductInfoList(branchProductIdList);
        if (CollectionUtil.isEmpty(productInfoList)) {
            return Collections.emptyList();
        }
        return productInfoList;
    }

    private List<RemoteProductVo> queryRemoteProductInfoList(List<Long> branchProductIdList) {
        RemoteProductBo productBo = new RemoteProductBo();
        productBo.setIsOfficialCard(true);
        productBo.setProductIdList(branchProductIdList);
        return remoteProductService.queryProductList(productBo, true);
    }

    private List<Long> getProductIdByBranchId(Long branchId) {
        RemoteBranchAuthTypeVo authTypeByBranchId = remoteBranchAuthTypeService.getAuthTypeByBranchId(branchId);
        return Optional.ofNullable(authTypeByBranchId)
            .map(RemoteBranchAuthTypeVo::getProductIds)
            .map(BranchPayModelStrategy::splitProductIds)
            .orElse(Collections.emptyList());
    }

    private static List<Long> splitProductIds(String ids) {
        return Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
    }

    /**
     * 解析门店产品配置信息
     *
     * @param configJson
     * @return
     */
    private Map<Long, BranchPayModelConfigDTO> parseToProductPayModelConfigMap(String configJson) {
        List<BranchPayModelConfigDTO> existModelConfigDTO = Optional.ofNullable(configJson)
            .map(json -> JsonUtils.parseArray(json, BranchPayModelConfigDTO.class))
            .orElse(Collections.emptyList());

        return existModelConfigDTO.stream()
            .collect(Collectors.toMap(
                BranchPayModelConfigDTO::getProduceId,
                Function.identity(),
                (existing, replacement) -> existing));
    }

    /**
     * 构建门店产品配置信息
     *
     * @param branchModelConfigMap
     * @param product
     * @return
     */
    private BranchPayModelConfigDTO buildBranchPayModelConfigDTO(Map<Long, BranchPayModelConfigDTO> branchModelConfigMap,
                                                                 RemoteProductVo product) {
        BranchPayModelConfigDTO branchPayModelConfigDTO = branchModelConfigMap.get(product.getProductId());
        boolean existBranchPayModelConfig = branchPayModelConfigDTO != null;
        BranchPayModelConfigDTO branchPayModelConfig = new BranchPayModelConfigDTO();
        branchPayModelConfig.setProduceId(product.getProductId());
        branchPayModelConfig.setProduceName(product.getProductName());
        branchPayModelConfig.setInstallmentFlag(existBranchPayModelConfig ? branchPayModelConfigDTO.getInstallmentFlag() : false);
        branchPayModelConfig.setInstallmentDeadlineDays(existBranchPayModelConfig ? branchPayModelConfigDTO.getInstallmentDeadlineDays() : 0);
        return branchPayModelConfig;
    }

    private void validateProductConfig(BranchPayModelConfigDTO branchPayModelConfig) {
        boolean installmentFlag = branchPayModelConfig.getInstallmentFlag();
        Long deadlineDays = branchPayModelConfig.getInstallmentDeadlineDays();
        boolean existDays = null != deadlineDays && deadlineDays > 0;
        if (installmentFlag && !existDays) {
            String errorMessage = "配置保存失败，存在产品未选择缴费截止天数";
            throw new ServiceException(errorMessage);
        }
        if (installmentFlag && deadlineDays > defaultDeadlineDays) {
            String errorMessage = "配置保存失败，产品最大缴费截止天数为：".concat(defaultDeadlineDays.toString());
            throw new ServiceException(errorMessage);
        }

        if (!installmentFlag && existDays) {
            String errorMessage = "配置保存失败，未选择分期时不能选择缴费截止天数";
            throw new ServiceException(errorMessage);
        }
    }

    @Override
    public BranchConfigTypeEnum getConfigType() {
        return BranchConfigTypeEnum.PAYMENT_FEE_MODE;
    }
}
