package com.jxw.shufang.branch.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/10 20:35
 * @Version 1
 * @Description 门店支付模式配置DTO
 */
@Data
public class BranchPayModelConfigDTO {
    /**
     * 产品id
     */
    private Long produceId;
    /**
     * 产品名称
     */
    private String produceName;
    /**
     * 是否分期
     */
    private Boolean installmentFlag;
    /**
     * 分期期限天数
     */
    private Long installmentDeadlineDays;
}
