package com.jxw.shufang.branch.handler;

import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.enums.TaskEnum;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.SpringUtils;

/**
 * 任務处理程序
 *
 *
 * @date 2024/04/25 09:51:16
 */
public interface TaskHandler {
    final static String BASE_NAME = "TaskHandler";

    static TaskHandler getTaskHandler(TaskEnum taskEnum) {
        String beanName = taskEnum.toString()+BASE_NAME;
        if (!SpringUtils.containsBean(beanName)) {
            throw new ServiceException(taskEnum.getDesc()+"任务处理器未找到！");
        }
        return SpringUtils.getBean(beanName);
    }


    /**
     * 获取完成状态（一般用于显示是否已经完成了任务）
     *
     * @param studentId 学生id
     *
     * @date 2024/04/25 09:54:58
     */
    TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId);

    /**
     * 完成任务，领取奖励
     *
     * @param integralTaskVo    任务
     * @param studentId 学生id
     *
     * @date 2024/04/25 09:55:10
     */
    void finishTask(IntegralTaskVo integralTaskVo,Long studentId);

    /**
     * 检查记录是否符合当前类型任务标准
     *
     * @param task
     */
    default void validTaskRecord(IntegralTask task) {
        // do nothing
    }
}
