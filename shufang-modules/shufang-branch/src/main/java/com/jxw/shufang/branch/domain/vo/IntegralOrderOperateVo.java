package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.IntegralOrderOperate;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）视图对象 integral_order_operate
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralOrderOperate.class)
public class IntegralOrderOperateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单操作id
     */
    @ExcelProperty(value = "积分订单操作id")
    private Long integralOrderOperateId;

    /**
     * 积分订单id
     */
    @ExcelProperty(value = "积分订单id")
    private Long integralOrderId;

    /**
     * 订单状态（1待兑换 2已兑换 3已取消）
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=待兑换,2=已兑换,3=已取消")
    private String integralOrderOperateStatus;

    /**
     * 消耗积分
     */
    @ExcelProperty(value = "消耗积分")
    private BigDecimal paymentIntegral;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String orderOperateRemark;

    private Long createBy;

    private Date createTime;


}
