package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.domain.BranchControlStudent;
import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@Mapper
public interface BranchControlStudentMapper extends BaseMapperPlus<BranchControlStudent, BranchControlStudentVo> {

    Page<BranchControlStudentVo> selectQueryPageList(@Param("page") Page<BranchControlStudent> build,
                                                     @Param("branchFeatureTemplateId") Long branchFeatureTemplateId,
                                                     @Param("branchId") Long branchId);

}
