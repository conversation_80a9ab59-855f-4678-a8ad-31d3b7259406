package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.bo.OptionalTaskBo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.service.IIntegralTaskService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分任务
 * 前端访问路由地址为:/branch/integralTask
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralTask")
public class IntegralTaskController extends BaseController {

    private final IIntegralTaskService integralTaskService;


    /**
     * 查询积分任务列表
     */
    @SaCheckPermission("branch:integralTask:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralTaskVo> list(IntegralTaskBo bo, PageQuery pageQuery) {

        return integralTaskService.queryPageList(bo, pageQuery);
    }

//    /**
//     * 导出积分任务列表
//     */
//    @SaCheckPermission("branch:integralTask:export")
//    @Log(title = "积分任务", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(IntegralTaskBo bo, HttpServletResponse response) {
//        if (LoginHelper.isBranchUser()&&LoginHelper.getBranchId()!=null){
//            bo.setBranchId(LoginHelper.getBranchId());
//        }
//        if (LoginHelper.isBranchAdmin()){
//            bo.setBranchId(LoginHelper.getBranchId());
//            bo.setBranchIdList(LoginHelper.getBranchIdList());
//        }
//        List<IntegralTaskVo> list = integralTaskService.queryList(bo);
//        ExcelUtil.exportExcel(list, "积分任务", IntegralTaskVo.class, response);
//    }

    /**
     * 获取积分任务详细信息
     *
     * @param integralTaskId 主键
     */
    @SaCheckPermission("branch:integralTask:query")
    @GetMapping("/{integralTaskId}")
    public R<IntegralTaskVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralTaskId) {
        return R.ok(integralTaskService.queryById(integralTaskId));
    }

    /**
     * 新增积分任务
     */
    @SaCheckPermission("branch:integralTask:add")
    @Log(title = "积分任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralTaskBo bo) {
        return toAjax(integralTaskService.insertByBo(bo));
    }
    @SaCheckPermission("branch:integralTask:add")
    @GetMapping("/optional")
    public R<List<IntegralTaskVo>> optional(OptionalTaskBo bo) {
        return R.ok(integralTaskService.optional(bo));
    }
    /**
     * 修改积分任务
     */
    @SaCheckPermission("branch:integralTask:edit")
    @Log(title = "积分任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralTaskBo bo) {
        return toAjax(integralTaskService.updateByBo(bo));
    }

    /**
     * 删除积分任务
     *
     * @param integralTaskIds 主键串
     */
    @SaCheckPermission("branch:integralTask:remove")
    @Log(title = "积分任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralTaskIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralTaskIds) {
        return toAjax(integralTaskService.deleteWithValidByIds(List.of(integralTaskIds), true));
    }
}
