package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 分店员工与门店关系对象 branch_staff_branch_relation
 *
 * @date 2024-05-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_staff_branch_relation")
public class BranchStaffBranchRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分店员工id
     */
    private Long branchStaffId;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 分店id
     */
    private Long branchId;

}
