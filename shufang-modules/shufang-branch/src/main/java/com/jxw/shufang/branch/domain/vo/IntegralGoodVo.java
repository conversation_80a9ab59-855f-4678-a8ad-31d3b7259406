package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.IntegralGood;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 积分商品视图对象 integral_good
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralGood.class)
public class IntegralGoodVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分商品id
     */
    @ExcelProperty(value = "积分商品id")
    private Long integralGoodId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 积分商品图片（oss_id）
     */
    @ExcelProperty(value = "积分商品图片ossId", converter = ExcelDictConvert.class)
    private Long integralGoodImg;

    //这个框架自带的注解太慢了
    //@Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "integralGoodImg")
    private String integralGoodImgUrl;

    /**
     * 积分商品名称
     */
    @ExcelProperty(value = "积分商品名称")
    private String integralGoodName;

    /**
     * 商品描述
     */
    @ExcelProperty(value = "商品描述")
    private String integralGoodDesc;

    /**
     * 消耗积分
     */
    @ExcelProperty(value = "消耗积分")
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟） 字典：good_type
     */
    @ExcelProperty(value = "商品类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=实物,2=虚拟")
    private String integralGoodType;

    /**
     * 兑换规则（1仅可兑换一次 2不限次数） 字典：good_rule
     */
    @ExcelProperty(value = "兑换规则", converter = ExcelDictConvert.class)
    //@ExcelDictFormat(readConverterExp = "1=仅可兑换一次,2=不限次数")
    private String integralGoodRule;

    /**
     * 商品库存
     */
    @ExcelProperty(value = "商品库存")
    private Long integralGoodStock;

    /**
     * 商品状态（1上架 2下架）  字典：good_status
     */
    @ExcelProperty(value = "商品状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=上架,2=下架")
    private String integralGoodStatus;

    /**
     * 商品排序（数字越小越靠前）
     */
    @ExcelProperty(value = "商品排序", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "数=字越小越靠前")
    private Long integralGoodSort;


}
