package com.jxw.shufang.branch.domain.convert;


import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchVoConvert extends BaseMapper<BranchVo, RemoteBranchVo> {


}
