package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.BranchAuthType;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 分店授权类型业务对象 branch_auth_type
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchAuthType.class, reverseConvertGenerate = false)
public class BranchAuthTypeBo extends BaseEntity {

    /**
     * 分店授权类型id
     */
    @NotNull(message = "分店授权类型id不能为空", groups = { EditGroup.class })
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    @NotBlank(message = "分店授权名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    @NotBlank(message = "分店授权描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    @NotNull(message = "分店授权天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    @NotNull(message = "分店授权费用不能为空", groups = { AddGroup.class, EditGroup.class })
    @DecimalMin(value = "0.01", inclusive = true, message = "金额必须大于等于0.01",groups = { AddGroup.class, EditGroup.class })
    @DecimalMax(value = "999999999.99", inclusive = true, message = "金额必须小于等于999999999.99",groups = { AddGroup.class, EditGroup.class })
    @Digits(integer = 9, fraction = 2, message = "金额格式不正确，最多10位整数，2位小数",groups = { AddGroup.class, EditGroup.class })
    private BigDecimal branchAuthTypeCost;

    /**
     * 课程ids
     */
    @NotBlank(message = "课程ids不能为空", groups = { AddGroup.class, EditGroup.class })
    private String courseIds;

    /**
     * 产品ids
     */
    @NotBlank(message = "产品ids不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productIds;

    private List<Long> branchAuthTypeIdList;

}
