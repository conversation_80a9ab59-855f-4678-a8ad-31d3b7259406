package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralOrderOperateBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo;
import com.jxw.shufang.branch.service.IIntegralOrderOperateService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
 * 前端访问路由地址为:/branch/integralOrderOperate
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralOrderOperate")
public class IntegralOrderOperateController extends BaseController {

    private final IIntegralOrderOperateService integralOrderOperateService;

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    @SaCheckPermission("branch:integralOrderOperate:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralOrderOperateVo> list(IntegralOrderOperateBo bo, PageQuery pageQuery) {
        return integralOrderOperateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    @SaCheckPermission("branch:integralOrderOperate:export")
    @Log(title = "积分订单操作记录（时间逆序取最后一条和积分订单中的对应）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IntegralOrderOperateBo bo, HttpServletResponse response) {
        List<IntegralOrderOperateVo> list = integralOrderOperateService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分订单操作记录（时间逆序取最后一条和积分订单中的对应）", IntegralOrderOperateVo.class, response);
    }

    /**
     * 获取积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）详细信息
     *
     * @param integralOrderOperateId 主键
     */
    @SaCheckPermission("branch:integralOrderOperate:query")
    @GetMapping("/{integralOrderOperateId}")
    public R<IntegralOrderOperateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralOrderOperateId) {
        return R.ok(integralOrderOperateService.queryById(integralOrderOperateId));
    }

    /**
     * 新增积分订单操作记（时间逆序取最后一条和积分订单中的对应）
     */
    @SaCheckPermission("branch:integralOrderOperate:add")
    @Log(title = "积分订单操作记录（时间逆序取最后一条和积分订单中的对应）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralOrderOperateBo bo) {
        return toAjax(integralOrderOperateService.insertByBo(bo));
    }

    /**
     * 修改积分订单操作记录（时间逆序取最后一条和积分订单中的对应）
     */
    @SaCheckPermission("branch:integralOrderOperate:edit")
    @Log(title = "积分订单操作记录（时间逆序取最后一条和积分订单中的对应）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralOrderOperateBo bo) {
        return toAjax(integralOrderOperateService.updateByBo(bo));
    }

    /**
     * 删除积分订单操作记录（时间逆序取最后一条和积分订单中的对应）
     *
     * @param integralOrderOperateIds 主键串
     */
    @SaCheckPermission("branch:integralOrderOperate:remove")
    @Log(title = "积分订单操作记录                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            （时间逆序取最后一条和积分订单中的对应）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralOrderOperateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralOrderOperateIds) {
        return toAjax(integralOrderOperateService.deleteWithValidByIds(List.of(integralOrderOperateIds), true));
    }


}
