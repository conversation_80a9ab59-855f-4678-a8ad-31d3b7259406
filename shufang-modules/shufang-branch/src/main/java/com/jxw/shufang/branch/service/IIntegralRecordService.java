package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 积分记录Service接口
 *
 *
 * @date 2024-04-23
 */
public interface IIntegralRecordService {

    /**
     * 查询积分记录
     */
    IntegralRecordVo queryById(Long integralRecordId);

    /**
     * 查询积分记录列表
     */
    TableDataInfo<IntegralRecordVo> queryPageList(IntegralRecordBo bo, PageQuery pageQuery);

    /**
     * 查询积分记录列表
     */
    List<IntegralRecordVo> queryList(IntegralRecordBo bo);

    /**
     * 新增积分记录
     */
    Boolean insertByBo(IntegralRecordBo bo);

    /**
     * 修改积分记录
     */
    Boolean updateByBo(IntegralRecordBo bo);

    /**
     * 校验并批量删除积分记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 通过会员id获取会员积分剩余量
     *
     * @param studentId 会员id
     *
     * @date 2024/04/24 01:17:37
     */
    BigDecimal getIntegralByStudentId(Long studentId);

    Map<Long, BigDecimal> getIntegralByStudentIdList(List<Long> studentIdList);

    /**
     * 批量保存发放记录
     *
     * @param saveList
     * @return
     */
    boolean batchSaveRecord(List<IntegralRecord> saveList);
}
