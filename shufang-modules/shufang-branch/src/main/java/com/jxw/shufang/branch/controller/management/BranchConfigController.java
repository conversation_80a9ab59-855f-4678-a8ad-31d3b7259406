package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.service.IBranchConfigService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.core.validate.QueryGroup;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @author: cyj
 * @date: 2025/3/10
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/branch/config")
public class BranchConfigController {

    private final IBranchConfigService branchConfigService;

    private final IBranchService branchService;

    /**
     * 查询门店
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("branch:config:list")
    @GetMapping("list")
    public TableDataInfo<BranchVo> list(BranchBo bo, PageQuery pageQuery) {
        return branchService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取所有配置类型
     *
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("branch:config:list")
    @GetMapping("option")
    public TableDataInfo<BranchConfigVo.ConfigType> listAllType(PageQuery pageQuery) {
        return branchConfigService.listAllConfigType(pageQuery);
    }


    /**
     * 获取门店优惠额度配置
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("branch:config:list")
    @GetMapping("getConfig")
    public R<BranchConfigVo> list(@Validated(QueryGroup.class) BranchConfigBo bo) {
        return R.ok(branchConfigService.getConfig(bo));
    }

    /**
     * 更新门店配置
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("branch:config:update")
    @PutMapping
    public R<Void> updateConfig(@Validated(EditGroup.class) @RequestBody BranchConfigBo bo) {
        return branchConfigService.updateConfigBo(bo) ? R.ok() : R.fail("配置更新失败");
    }

    /**
     * 批量同步门店配置
     */
    @SaCheckPermission("branch:config:update")
    @PostMapping("duplicateConfig")
    public R<Void> duplicateConfig(@Validated(QueryGroup.class) @RequestBody BranchConfigBo bo){
        if(CollectionUtils.isEmpty(bo.getDeptIdList())){
            return R.fail("请选择同步的门店信息");
        }
        if (CollectionUtils.isEmpty(bo.getConfigTypeList())) {
            return R.fail("请选择同步的类型");
        }
        return branchConfigService.duplicateConfig(bo) ? R.ok() : R.fail("同步配置失败");
    }
}
