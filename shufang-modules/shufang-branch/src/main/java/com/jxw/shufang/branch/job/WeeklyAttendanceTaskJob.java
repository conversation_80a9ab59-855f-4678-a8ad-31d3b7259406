package com.jxw.shufang.branch.job;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.Pair;
import com.jxw.shufang.common.core.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.enums.TaskEnum;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.branch.service.IIntegralTaskService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.student.api.RemoteStudentAttendanceLogService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/7/16
 * @Description 每周固定时间查询定时任务，查找以及范围内的学生，检查本周是否已经发放过积分，没有的话检查是否达到发放条件，进行发放积分。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WeeklyAttendanceTaskJob {

    private final IIntegralTaskService integralTaskService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteStudentService remoteStudentService;
    private final IBranchService branchService;
    private final IIntegralRecordService integralRecordService;
    @DubboReference
    private RemoteStudentAttendanceLogService studentAttendanceLogService;

    @XxlJob("weeklyAttendanceTaskJob")
    public ReturnT<String> execute(String param) {
        try {
            Date excuteDate = StringUtils.isEmpty(param) ? new Date() : DateUtils.parseDate(param);
            grantAllWeeklyAttendanceIntegral(excuteDate);
        } catch (ServiceException e) {
            log.error(e.getMessage());
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 发放积分
     */
    private void grantAllWeeklyAttendanceIntegral(Date excuteDate) {
        // 获取积分定时任务
        List<IntegralTaskVo> integralTaskVos = listExecuteIntegralTask();
        // 对每个设置的任务进行积分发放
        List<IntegralRecord> saveRecordList = new ArrayList<>();
        for (IntegralTaskVo integralTaskVo : integralTaskVos) {
            grantIntegralByTaskRecord(integralTaskVo, saveRecordList, excuteDate);
        }
        processSaveList(saveRecordList, true);
    }

    /**
     * 获取执行的定时任务，所有的每周考勤任务
     *
     * @return
     */
    private List<IntegralTaskVo> listExecuteIntegralTask() {
        IntegralTaskBo queryTaskBo = new IntegralTaskBo();
        queryTaskBo.setTaskTrigger(TaskEnum.WEEKLY_ATTENDANCE_TASK.toString());
        queryTaskBo.setTaskStatus("1");
        List<IntegralTaskVo> integralTaskVos = integralTaskService.queryList(queryTaskBo);
        if (CollUtil.isEmpty(integralTaskVos)) {
            log.info("定时任务结束，今日没有可以执行的定时发放任务");
        }
        return integralTaskVos;
    }

    private void grantIntegralByTaskRecord(IntegralTaskVo integralTaskVo, List<IntegralRecord> saveList,
        Date excuteDate) {
        if (integralTaskVo.getIntegralNum() == null || integralTaskVo.getIntegralNum() == 0) {
            log.info("每周考勤发放积分任务异常，发放的积分为0，自动跳过该积分任务，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        Integer attendanceTimeOfWeek;
        try {
            attendanceTimeOfWeek = Integer.valueOf(integralTaskVo.getParam());
        } catch (Exception e) {
            log.error("每周考勤发放积分任务异常，参数转换异常，自动跳过该积分任务，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        // 积分任务部门
        List<RemoteDeptVo> selfAndChildShopList =
            remoteDeptService.getSelfAndChildShopList(integralTaskVo.getCreateDept());
        if (CollUtil.isEmpty(selfAndChildShopList)) {
            log.info("每周考勤发放积分任务异常，任务没有适用的门店，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        // 查询门店id
        BranchBo branchBo = new BranchBo();
        branchBo
            .setCreateDeptIds(selfAndChildShopList.stream().map(RemoteDeptVo::getDeptId).collect(Collectors.toSet()));
        List<Long> branchIdList = branchService.listBranchId(branchBo);
        if (CollUtil.isEmpty(branchIdList)) {
            log.info("每周考勤发放积分任务异常，任务没有适用的门店，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setBranchIdList(branchIdList);
        List<Long> studentIdList = remoteStudentService.queryStudentIdList(remoteStudentBo);
        if (CollUtil.isEmpty(studentIdList)) {
            log.info("每周考勤发放积分任务异常，门店下没有匹配的会员，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        Pair<Date, Date> attendanceTimeRange = getAttendanceTimeRange(excuteDate);
        Map<Long, Integer> studentAttendanceDayNumsMap = studentAttendanceLogService
            .getStudentAttendanceDayNumsMap(studentIdList, attendanceTimeRange.getKey(),
                attendanceTimeRange.getValue());
        if (CollUtil.isEmpty(studentAttendanceDayNumsMap)) {
            log.info("每周考勤发放积分任务异常，获取门店考勤记录为空，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
            return;
        }
        for (Long studentId : studentIdList) {
            if (studentAttendanceDayNumsMap.get(studentId) != null
                && studentAttendanceDayNumsMap.get(studentId) >= attendanceTimeOfWeek) {
                IntegralRecord integralRecord = new IntegralRecord();
                integralRecord.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
                integralRecord.setChangeNum(
                    new BigDecimal(integralTaskVo.getIntegralNum() == null ? 0 : integralTaskVo.getIntegralNum()));
                integralRecord.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
                integralRecord.setStudentId(studentId);
                integralRecord.setChangeReason(StrUtil.blankToDefault(integralTaskVo.getIntegralTaskNameFill(),
                    integralTaskVo.getIntegralTaskName()));
                integralRecord.setTaskId(integralTaskVo.getIntegralTaskId());
                integralRecord.setCreateDept(integralTaskVo.getCreateDept());
                saveList.add(integralRecord);
                processSaveList(saveList, false);
            }
        }
    }

    /**
     * 返回执行时间所在周的开始结束时间
     *
     * @param executeDate
     * @return
     */
    private Pair<Date, Date> getAttendanceTimeRange(Date executeDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(executeDate);

        // 让一周从周一开始，避免
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int offset = (dayOfWeek == Calendar.SUNDAY) ? -6 : (Calendar.MONDAY - dayOfWeek);
        calendar.add(Calendar.DAY_OF_MONTH, offset);

        // 设置到当天0点
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date startOfWeek = calendar.getTime();

        // 加7天减1毫秒 得到周末最后时刻
        calendar.add(Calendar.DAY_OF_MONTH, 7);
        calendar.add(Calendar.MILLISECOND, -1);

        Date endOfWeek = calendar.getTime();

        return Pair.of(startOfWeek, endOfWeek);
    }

    /**
     * 处理积分任务
     *
     * @param saveList
     * @param addRecordFinish
     */
    private void processSaveList(List<IntegralRecord> saveList, boolean addRecordFinish) {
        if (saveList.size() >= 1000 || addRecordFinish) {
            saveBatch(saveList);
            saveList.clear();
        }
    }

    /**
     * 保存发放记录
     *
     * @param saveBoList
     */
    private void saveBatch(List<IntegralRecord> saveBoList) {
        if (CollUtil.isEmpty(saveBoList)) {
            return;
        }
        boolean saveRecord = integralRecordService.batchSaveRecord(saveBoList);
        if (!saveRecord) {
            log.error("发放记录保存失败，发放会员id:{}",
                saveBoList.stream().map(IntegralRecord::getStudentId).collect(Collectors.toList()));
        }
    }

}
