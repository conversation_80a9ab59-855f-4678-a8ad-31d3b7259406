package com.jxw.shufang.branch.domain.convert;

import com.jxw.shufang.branch.domain.BranchControlToggle;
import com.jxw.shufang.branch.domain.bo.BranchControlToggleBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchControlToggleBoConvert extends BaseMapper<BranchControlToggleBo, BranchControlToggle> {
}
