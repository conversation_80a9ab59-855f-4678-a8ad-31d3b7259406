package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.api.domain.vo.RemotePayMerchantConfig;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 分店Service接口
 *
 *
 * @date 2024-02-21
 */
public interface IBranchService {

    /**
     * 查询分店
     */
    BranchVo queryById(Long branchId);

    /**
     * 通过门店ID集合 查询门店信息集合
     */
    List<BranchVo> selectDeptByIds(List<Long> deptIds);

    /**
     * 查询分店列表
     */
    TableDataInfo<BranchVo> queryPageList(BranchBo bo, PageQuery pageQuery);

    /**
     * 查询分店列表
     */
    List<BranchVo> queryList(BranchBo bo);

    /**
     * 新增分店
     */
    Boolean insertByBo(BranchBo bo);

    /**
     * 修改分店
     */
    Boolean updateByBo(BranchBo bo);

    /**
     * 校验并批量删除分店信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<BranchVo> queryUnexpiredByAuthTypeIds(List<Long> authTypeIds);


    /**
     * 分店添加授权类型
     *
     * @param branchId         分店id
     * @param branchAuthTypeId 授权类型id
     *
     * @date 2024/02/23 02:24:27
     */
    void branchAddAuthType(Long branchId, Long branchAuthTypeId);


    /**
     * 重置密码
     *
     * @param branchAdminIdList 分支管理员id列表
     *
     * @date 2024/02/23 03:29:45
     */
    void restPwd(List<Long> branchAdminIdList);

    /**
     * 放入excel其他信息
     *
     * @param list 列表
     *
     * @date 2024/02/26 02:05:21
     */
    void putExcelOtherInfo(List<BranchVo> list);

    /**
     * 按部门id查询门店
     *
     * @param deptId 部门id
     *
     * @date 2024/02/26 02:35:29
     */
    BranchVo selectBranchByDeptId(Long deptId);

    /**
     * 门店选项列表,常用于下拉框类的查询
     *
     *
     * @date 2024/02/27 01:26:47
     */
    List<BranchVo> branchOptionList();

    /**
     * 查询简单的门店信息
     *
     * @param bo 查询条件
     *
     * @date 2024/02/28 01:57:11
     */
    List<BranchVo> selectSimpleBranInfo(BranchBo bo);

    Branch queryBranchById(Long branchId);

    void cleanCache();

    /**
     *通过部门Id列表获取门店Id map
     * key: 部门Id, value: 门店Id
     * @param deptIdList
     * @param filterStopBranch 是否过滤停用的门店
     * @return
     */
    Map<Long, Long> selectBranchIdMapByDeptIdList(List<Long> deptIdList,Boolean filterStopBranch);

    TableDataInfo<BranchVo> statisticsPage(BranchBo bo, PageQuery pageQuery);

    List<BranchVo> statistics(BranchBo bo);

    Boolean updateBranchStatus(BranchBo branchBo);

    /**
     * 查询简要门店信息
     *
     * @param branchBo
     * @return
     */
    List<BranchVo> queryBriefInfoList(BranchBo branchBo);

    /**
     * 获取门店的支付配置
     *
     * @param branchId
     * @return
     */
    String getCommonPayAppId(Long branchId);


    List<BranchVo> queryStaffBranchList(Long userId);

    /**
     * 根据部门id获取门店信息
     *
     * @param deptId
     * @return
     */
    BranchVo getBranchInfoByCreateDept(Long deptId);

    RemotePayMerchantConfig getCommonPayConfig(Long branchId);

    Boolean transfer(Long branchId, Integer amount);


    /**
     * 查询门店id列表
     *
     * @param branchBo
     * @return
     */
    List<Long> listBranchId(BranchBo branchBo);

    List<BranchVo> queryByCreateDept(Long deptId);

}
