package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.Material;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

/**
 * 物料业务对象 material
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Material.class, reverseConvertGenerate = false)
public class MaterialBo extends BaseEntity {

    /**
     * 物料id
     */
    @NotNull(message = "物料id不能为空", groups = { EditGroup.class })
    private Long materialId;

    /**
     * 分店id
     */
    @NotNull(message = "分店id不能为空", groups = { AddGroup.class})
    private Long branchId;

    /**
     * 物料类型（启动培训、产品介绍、培训讲座、销售工具、宣传资料、运维工具）
     */
    @NotBlank(message = "物料类型（启动培训、产品介绍、培训讲座、销售工具、宣传资料、运维工具）不能为空", groups = { AddGroup.class})
    private String materialType;

    /**
     * 是否外链（0是 1否）
     */
    @NotBlank(message = "是否外链（0是 1否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String isUrl;

    /**
     * 物料外链地址
     */
    @NotBlank(message = "物料外链地址不能为空", groups = { AddGroup.class})
    private String materialUrl;

    /**
     * 物料文件ids
     */
    @NotBlank(message = "物料文件ids不能为空", groups = { AddGroup.class })
    private String materialFileIds;

    private String branchName;


}
