package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.branch.domain.BranchAuthRecord;
import com.jxw.shufang.branch.domain.bo.BranchAuthRecordBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo;
import com.jxw.shufang.branch.mapper.BranchAuthRecordMapper;
import com.jxw.shufang.branch.service.IBranchAuthRecordService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 分店授权记录
（时间逆序的最后一条记录和分店中的对应）Service业务层处理
 *
 *
 * @date 2024-02-22
 */
@Service
@RequiredArgsConstructor()
@Slf4j
public class BranchAuthRecordServiceImpl implements IBranchAuthRecordService, BaseService {

    private final BranchAuthRecordMapper baseMapper;

    /**
     * 查询分店授权记录
（时间逆序的最后一条记录和分店中的对应）
     */
    @Override
    public BranchAuthRecordVo queryById(Long branchAuthRecordId){
        return baseMapper.selectVoById(branchAuthRecordId);
    }

    /**
     * 查询分店授权记录
（时间逆序的最后一条记录和分店中的对应）列表
     */
    @Override
    public TableDataInfo<BranchAuthRecordVo> queryPageList(BranchAuthRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchAuthRecord> lqw = buildQueryWrapper(bo);
        Page<BranchAuthRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        putAuthStatus(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 查询分店授权记录
（时间逆序的最后一条记录和分店中的对应）列表
     */
    @Override
    public List<BranchAuthRecordVo> queryList(BranchAuthRecordBo bo) {
        LambdaQueryWrapper<BranchAuthRecord> lqw = buildQueryWrapper(bo);
        List<BranchAuthRecordVo> branchAuthRecordVos = baseMapper.selectVoList(lqw);
        putAuthStatus(branchAuthRecordVos);
        return branchAuthRecordVos;
    }

    private LambdaQueryWrapper<BranchAuthRecord> buildQueryWrapper(BranchAuthRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchAuthRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchAuthRecordId() != null, BranchAuthRecord::getBranchAuthRecordId, bo.getBranchAuthRecordId());
        lqw.eq(bo.getBranchId()!=null, BranchAuthRecord::getBranchId, bo.getBranchId());
        lqw.eq(bo.getBranchAuthTypeId() != null, BranchAuthRecord::getBranchAuthTypeId, bo.getBranchAuthTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getBranchAuthTypeName()), BranchAuthRecord::getBranchAuthTypeName, bo.getBranchAuthTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getBranchAuthTypeInfo()), BranchAuthRecord::getBranchAuthTypeInfo, bo.getBranchAuthTypeInfo());
        lqw.eq(bo.getBranchAuthTypeDays() != null, BranchAuthRecord::getBranchAuthTypeDays, bo.getBranchAuthTypeDays());
        lqw.eq(bo.getBranchAuthTypeCost() != null, BranchAuthRecord::getBranchAuthTypeCost, bo.getBranchAuthTypeCost());
        lqw.eq(bo.getBranchAuthStartTime()!=null, BranchAuthRecord::getBranchAuthStartTime, bo.getBranchAuthStartTime());
        lqw.eq(bo.getCreateDept() != null, BranchAuthRecord::getCreateDept, bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, BranchAuthRecord::getCreateBy, bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, BranchAuthRecord::getCreateTime, bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, BranchAuthRecord::getUpdateBy, bo.getUpdateBy());
        return lqw;
    }

    /**
     * 新增分店授权记录
（时间逆序的最后一条记录和分店中的对应）
     */
    @Override
    public Boolean insertByBo(BranchAuthRecordBo bo) {
        BranchAuthRecord add = MapstructUtils.convert(bo, BranchAuthRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchAuthRecordId(add.getBranchAuthRecordId());
        }
        return flag;
    }

    /**
     * 修改分店授权记录
（时间逆序的最后一条记录和分店中的对应）
     */
    @Override
    public Boolean updateByBo(BranchAuthRecordBo bo) {
        BranchAuthRecord update = MapstructUtils.convert(bo, BranchAuthRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchAuthRecord entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分店授权记录
（时间逆序的最后一条记录和分店中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Cacheable(value = "branchAuthRecord", key = "#branchAuthRecordId",condition = "#branchAuthRecordId != null")
    @Override
    public BranchAuthRecord queryBranchAuthRecordById(Long branchAuthRecordId) {
        return baseMapper.selectById(branchAuthRecordId);
    }

    @CacheEvict(value = "branchAuthRecord",allEntries= true)
    public void cleanCache(){
        log.info("===========branchAuthRecordService cleanCache===========");
    }

    @Override
    public BranchAuthRecordVo queryLastAuthRecordByBranchId(Long branchId) {
        LambdaQueryWrapper<BranchAuthRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BranchAuthRecord::getBranchId, branchId);
        wrapper.orderByDesc(BranchAuthRecord::getCreateTime);
        wrapper.last("limit 1");

        BranchAuthRecordVo branchAuthRecordVo = baseMapper.selectVoOne(wrapper);
        putAuthStatus(branchAuthRecordVo);
        return branchAuthRecordVo;
    }

    @Override
    public List<BranchAuthRecordVo> queryLastAuthRecordListByBranchIdList(List<Long> branchIdList) {
        LambdaQueryWrapper<BranchAuthRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BranchAuthRecord::getBranchId, branchIdList);
        List<BranchAuthRecordVo> branchAuthRecordVos = baseMapper.selectVoList(wrapper);
        if (CollUtil.isEmpty(branchAuthRecordVos)){
            return List.of();
        }
        //每个门店只拿到最后一条授权记录
        Map<Long, List<BranchAuthRecordVo>> map = StreamUtils.groupByKey(branchAuthRecordVos, BranchAuthRecordVo::getBranchId);
        List<BranchAuthRecordVo> resList = new ArrayList<>();
        for (Long branchId : branchIdList) {
            List<BranchAuthRecordVo> branchAuthRecordVoList = map.get(branchId);
            if (CollUtil.isEmpty(branchAuthRecordVoList)){
                continue;
            }
            //拿到创建时间最大的那一个
            BranchAuthRecordVo branchAuthRecordVo = Collections.max(branchAuthRecordVoList, Comparator.comparing(BranchAuthRecordVo::getCreateTime));
            putAuthStatus(branchAuthRecordVo);
            resList.add(branchAuthRecordVo);
        }


        return resList;
    }

    private void putAuthStatus(List<BranchAuthRecordVo> list){
        if (CollUtil.isEmpty(list)){
            return;
        }
        for (BranchAuthRecordVo branchAuthRecordVo : list) {
            putAuthStatus(branchAuthRecordVo);
        }
    }

    private void putAuthStatus(BranchAuthRecordVo branchAuthRecordVo){
    //                   IF(DATE(now()) &lt;= DATE_ADD(DATE(bar.branch_auth_start_time), INTERVAL bar.branch_auth_type_days DAY), 0, 1) AS auth_status,
        if (branchAuthRecordVo==null){
            return;
        }
        Date branchAuthStartTime = branchAuthRecordVo.getBranchAuthStartTime();
        Long branchAuthTypeDays = branchAuthRecordVo.getBranchAuthTypeDays();

        if (branchAuthStartTime==null || branchAuthTypeDays==null){
            return;
        }
        long now = System.currentTimeMillis();
        long authEndTime = branchAuthStartTime.getTime() + branchAuthTypeDays * 24 * 60 * 60 * 1000;
        if (now > authEndTime){
            branchAuthRecordVo.setAuthStatus(1);
        }else{
            branchAuthRecordVo.setAuthStatus(0);
        }
    }

    @Override
    public void init() {
        IBranchAuthRecordService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchAuthRecordService init===========");
        LambdaQueryWrapper<BranchAuthRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchAuthRecord::getBranchAuthRecordId);
        List<BranchAuthRecord> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========branchAuthRecordService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchAuthRecordById(item.getBranchAuthRecordId());
        });
        log.info("===========branchAuthRecordService init end===========");
    }


}
