package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分店授权记录
（时间逆序的最后一条记录和分店中的对应）对象 branch_auth_record
 *
 *
 * @date 2024-02-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_auth_record")
public class BranchAuthRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权记录id
     */
    @TableId(value = "branch_auth_record_id")
    private Long branchAuthRecordId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分店授权类型id
     */
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    private BigDecimal branchAuthTypeCost;

    /**
     * 分店授权开始时间
     */
    private Date branchAuthStartTime;


}
