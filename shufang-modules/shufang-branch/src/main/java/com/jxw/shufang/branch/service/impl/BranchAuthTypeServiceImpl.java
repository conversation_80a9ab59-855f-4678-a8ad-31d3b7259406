package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import com.jxw.shufang.branch.domain.dto.BranchAuthTypeRelationDto;
import com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo;
import com.jxw.shufang.branch.service.IBranchAuthRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.branch.domain.BranchAuthType;
import com.jxw.shufang.branch.domain.bo.BranchAuthTypeBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.mapper.BranchAuthTypeMapper;
import com.jxw.shufang.branch.service.IBranchAuthTypeService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分店授权类型Service业务层处理
 *
 *
 * @date 2024-02-21
 */
@Service
@RequiredArgsConstructor()
@Slf4j
public class BranchAuthTypeServiceImpl implements IBranchAuthTypeService, BaseService {
    private final BranchAuthTypeMapper baseMapper;
    private final IBranchService branchService;

    private final IBranchAuthRecordService branchAuthRecordService;

    /**
     * 查询分店授权类型
     */
    @Override
    public BranchAuthTypeVo queryById(Long branchAuthTypeId){
        return baseMapper.selectVoById(branchAuthTypeId);
    }

    /**
     * 查询分店授权类型列表
     */
    @Override
    public TableDataInfo<BranchAuthTypeVo> queryPageList(BranchAuthTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchAuthType> lqw = buildQueryWrapper(bo);
        Page<BranchAuthTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询分店授权类型列表
     */
    @Override
    public List<BranchAuthTypeVo> queryList(BranchAuthTypeBo bo) {
        LambdaQueryWrapper<BranchAuthType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BranchAuthType> buildQueryWrapper(BranchAuthTypeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchAuthType> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getBranchAuthTypeName()), BranchAuthType::getBranchAuthTypeName, bo.getBranchAuthTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getBranchAuthTypeInfo()), BranchAuthType::getBranchAuthTypeInfo, bo.getBranchAuthTypeInfo());
        lqw.eq(bo.getBranchAuthTypeDays() != null, BranchAuthType::getBranchAuthTypeDays, bo.getBranchAuthTypeDays());
        lqw.eq(bo.getBranchAuthTypeCost() != null, BranchAuthType::getBranchAuthTypeCost, bo.getBranchAuthTypeCost());
        lqw.eq(StringUtils.isNotBlank(bo.getCourseIds()), BranchAuthType::getCourseIds, bo.getCourseIds());
        lqw.eq(StringUtils.isNotBlank(bo.getProductIds()), BranchAuthType::getProductIds, bo.getProductIds());
        lqw.in(CollectionUtils.isNotEmpty(bo.getBranchAuthTypeIdList()), BranchAuthType::getBranchAuthTypeId,
            bo.getBranchAuthTypeIdList());
        return lqw;
    }

    /**
     * 新增分店授权类型
     */
    @Override
    public Boolean insertByBo(BranchAuthTypeBo bo) {

        if(StringUtils.isNotBlank(bo.getCourseIds())){
            List<String> courseIdList = new ArrayList<>(Arrays.stream(bo.getCourseIds().split(",")).toList());
            courseIdList.removeAll(List.of(""));
            String courseIds = courseIdList.stream().collect(Collectors.joining(","));
            bo.setCourseIds(courseIds);
        }

        if(StringUtils.isNotBlank(bo.getProductIds())){
            List<String> productIdList = new ArrayList<>(Arrays.stream(bo.getProductIds().split(",")).toList());
            productIdList.removeAll(List.of(""));
            String productIds = productIdList.stream().collect(Collectors.joining(","));
            bo.setProductIds(productIds);
        }

        BranchAuthType add = MapstructUtils.convert(bo, BranchAuthType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchAuthTypeId(add.getBranchAuthTypeId());
        }
        return flag;
    }

    /**
     * 修改分店授权类型
     */
    @Override
    public Boolean updateByBo(BranchAuthTypeBo bo) {

        if(StringUtils.isNotBlank(bo.getCourseIds())){
            List<String> courseIdList = new ArrayList<>(Arrays.stream(bo.getCourseIds().split(",")).toList());
            courseIdList.removeAll(List.of(""));
            String courseIds = courseIdList.stream().collect(Collectors.joining(","));
            bo.setCourseIds(courseIds);
        }

        if(StringUtils.isNotBlank(bo.getProductIds())){
            List<String> productIdList = new ArrayList<>(Arrays.stream(bo.getProductIds().split(",")).toList());
            productIdList.removeAll(List.of(""));
            String productIds = productIdList.stream().collect(Collectors.joining(","));
            bo.setProductIds(productIds);
        }

        BranchAuthType update = MapstructUtils.convert(bo, BranchAuthType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchAuthType entity){
        //做一些数据校验,如唯一约束
    }


    /**
     * 批量删除分店授权类型
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
            //删除的时候要判断是否有已经授权的未过期的状态正常的分店，如果有就需要提示
            //1，查询未过期的分店
            List<BranchVo> branchVoList = branchService.queryUnexpiredByAuthTypeIds(ids);
            //分组
            Map<Long, List<BranchVo>> map = StreamUtils.groupByKey(branchVoList, t->t.getBranchAuthType().getBranchAuthTypeId() );
            if (CollUtil.isNotEmpty(map)){
                StringBuilder errMsg = new StringBuilder();
                map.forEach((k,v) -> {
                    errMsg.append("授权类型【").append(v.get(0).getBranchAuthType().getBranchAuthTypeName()).append("】下有未过期的分店【");
                    v.forEach(branchVo -> {
                        errMsg.append(branchVo.getBranchName()).append("、");
                    });
                    errMsg.deleteCharAt(errMsg.length() - 1).append("】;");
                });
                throw new ServiceException(errMsg.toString());
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Cacheable(value = "branchAuthType", key = "#branchAuthTypeId",condition = "#branchAuthTypeId != null")
    @Override
    public BranchAuthType queryBranchAuthTypeById(Long branchAuthTypeId) {
        return baseMapper.selectById(branchAuthTypeId);
    }

    @CacheEvict(value = "branchAuthType",allEntries= true)
    public void cleanCache(){
        log.info("===========branchAuthTypeService cleanCache===========");
    }

    @Override
    public void init() {
        IBranchAuthTypeService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchAuthTypeService init===========");
        LambdaQueryWrapper<BranchAuthType> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchAuthType::getBranchAuthTypeId);
        List<BranchAuthType> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========branchAuthTypeService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchAuthTypeById(item.getBranchAuthTypeId());
        });
        log.info("===========branchAuthTypeService init end===========");
    }

    @Override
    public BranchAuthTypeRelationDto getAuthTypeMapByBranchId(List<Long> branchIdList) throws ServiceException {
        List<BranchAuthRecordVo> branchAuthRecordVoList =
            branchAuthRecordService.queryLastAuthRecordListByBranchIdList(branchIdList);
        if (ObjectUtil.isEmpty(branchAuthRecordVoList)) {
            return null;
        }
        // 是否过期
        List<Long> list = branchAuthRecordVoList.stream()
            .filter(branchAuthRecordVo -> !(System.currentTimeMillis() > branchAuthRecordVo.getBranchAuthStartTime()
                .getTime() + branchAuthRecordVo.getBranchAuthTypeDays() * 86400000L))
            .map(BranchAuthRecordVo::getBranchAuthTypeId).filter(Objects::nonNull).distinct().toList();
        if (ObjectUtil.isEmpty(list)) {
            return null;
        }

        BranchAuthTypeBo branchAuthTypeBo = new BranchAuthTypeBo();
        branchAuthTypeBo.setBranchAuthTypeIdList(list);
        List<BranchAuthTypeVo> branchAuthTypeVos = queryList(branchAuthTypeBo);
        if (ObjectUtil.isEmpty(branchAuthTypeVos)) {
            return null;
        }
        BranchAuthTypeRelationDto relation = new BranchAuthTypeRelationDto();
        relation.setStudentIdToAuthTypeIdMap(branchAuthRecordVoList.stream()
            .collect(Collectors.toMap(BranchAuthRecordVo::getBranchId, BranchAuthRecordVo::getBranchAuthTypeId)));
        relation.setRemoteBranchAuthTypeVos(MapstructUtils.convert(branchAuthTypeVos, RemoteBranchAuthTypeVo.class));
        return relation;
    }


}
