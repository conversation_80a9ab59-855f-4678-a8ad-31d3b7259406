package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 分店视图对象 branch
 *
 *
 * @date 2024-02-21
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Branch.class)
public class BranchVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 分店名称
     */
    @ExcelProperty(value = "分店名称",order = 1)
    private String branchName;



    /**
     * 分店状态（0正常 1停用）
     */
    @ExcelProperty(value = "分店状态", converter = ExcelDictConvert.class,order = 7)
    @ExcelDictFormat(readConverterExp = "0=正常,1=停用")
    private String branchStatus;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",order = 8)
    private Date createTime;

    /**
     * 分店授权记录id
     */
    private Long branchAuthRecordId;

    /**
     * 创建部门(同时也是门店对应的部门)
     */
    private Long createDept;


    /**
     * 门店授权类型
     */
    private BranchAuthTypeVo branchAuthType;

    /**
     * 门店授权记录，最新的那一条
     */
    private BranchAuthRecordVo branchAuthRecord;


    /**
     * 执行店长列表
     */
    private List<Long> branchStaffIdList;

    /**
     * 门店授权名称
     */
    @ExcelProperty(value = "分店授权名称",order = 4)
    private String branchAuthTypeName;

    /**
     * 门店管理员名称
     */
    @ExcelProperty(value = "管理员姓名",order = 2)
    private String adminNickName;

    /**
     * 门店管理员账号
     */
    @ExcelProperty(value = "管理员名称",order = 3)
    private String adminUserName;

    /**
     * 授权有效期（拼接好的时间字符串）
     */
    @ExcelProperty(value = "授权有效期",order = 5)
    private String authFullTime;

    /**
     * 授权状态,对应authStatus字典(非数据库字段，需查询授权记录)
     */
    @ExcelProperty(value = "授权状态", converter = ExcelDictConvert.class,order = 6)
    @ExcelDictFormat(dictType = "branch_auth_status")
    private Integer authStatus;

    /**
     * 门店管理员id
     */
    private Long adminUserId;


    /**
     * 门店会员顾问总数
     */
    private Long memberCount;

    /**
     * 会员总数
     */
    private Long totalMemberCount;

    /**
     * 正式会员续费人数
     */
    private Long formalRenewCount;

    /**
     * 正式会员转化人数
     */
    private Long formalConversionCount;

    /**
     * 正式会员转化率
     */
    private BigDecimal formalConversionRate;

    /**
     * 在线学习人数
     */
    private Long onlineLearnCount;

    /**
     * 每日平均学习时长（秒）
     */
    private Long dailyLearnTime;

    /**
     * 学习反馈次数
     */
    private Long feedbackCount;

    /**
     * 订单总数
     */
    private Long orderCount;

    /**
     * 退费订单数
     */
    private Long refundOrderCount;

    /**
     * 订单营收金额
     */
    private BigDecimal orderRevenue;

    /**
     * 平板端 自学系统状态（0关闭 1开启）
     */
    private Integer selfStudySystemStatus;
    /**
     * 平板端 学生自讲状态(0关闭 1开启)
     */
    private Integer studentSpeakingStatus;
    /**
     * 平板端 智慧中小学状态(0关闭  1开启)
     */
    private Integer smartPrimarySecondarySchoolStatus;

    /**
     * 平板端 倍速管控(0关闭 1开启)
     */
    private Integer speedControlStatus;
    /**
     * 平板端 拍照管控(0关闭 1开启)
     */
    private Integer photoStatus;

    /**
     * 剩余天数时长（最小单位为天）
     */
    private Integer remainTime;
    /**
     * 父级机构id
     */
    private Long parentDeptId;

    /**
     * 所属上级组织
     */
    @ExcelProperty(value = "所属上级组织",order = 0)
    private String parentDeptName;
}
