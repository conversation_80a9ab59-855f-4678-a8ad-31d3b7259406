package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.Banner;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.common.translation.annotation.Translation;
import com.jxw.shufang.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 轮播图视图对象 banner
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Banner.class)
public class BannerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图id
     */
    @ExcelProperty(value = "轮播图id")
    private Long bannerId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 轮播图状态（1上架 2下架）
     */
    @ExcelProperty(value = "轮播图状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=上架,2=下架")
    private String bannerStatus;

    /**
     * 轮播图名称
     */
    @ExcelProperty(value = "轮播图名称")
    private String bannerName;

    /**
     * 轮播图（oss_id）
     */
    @ExcelProperty(value = "轮播图", converter = ExcelDictConvert.class)
    private Long bannerImg;

    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "bannerImg")
    private String bannerImgUrl;

    /**
     * 轮播图类型（对应字典值）
     */
    @ExcelProperty(value = "轮播图类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值")
    private String bannerType;

    /**
     * 门店信息
     */
    private BranchVo branch;

    private Date updateTime;


}
