package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * @author: cyj
 * @date: 2025/3/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_config")
public class BranchConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 门店参数配置ID
     */
    @TableId
    private Long branchConfigId;
    /**
     * 门店ID
     */
    private Long branchId;
    /**
     * 参数配置类型
     */
    private Integer configType;
    /**
     * 配置参数json
     */
    private String configJson;
}
