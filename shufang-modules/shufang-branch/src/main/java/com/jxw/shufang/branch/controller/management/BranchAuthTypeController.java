package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.BranchAuthTypeBo;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import com.jxw.shufang.branch.service.IBranchAuthTypeService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分店授权类型
 * 前端访问路由地址为:/branch/management/authType
 *
 *
 * @date 2024-02-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/authType")
public class BranchAuthTypeController extends BaseController {

    private final IBranchAuthTypeService branchAuthTypeService;

    /**
     * 查询分店授权类型列表
     */
    @SaCheckPermission("branch:authType:list")
    @GetMapping("/list")
    public TableDataInfo<BranchAuthTypeVo> list(BranchAuthTypeBo bo, PageQuery pageQuery) {
        return branchAuthTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出分店授权类型列表
     */
    @SaCheckPermission("branch:authType:export")
    @Log(title = "分店授权类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchAuthTypeBo bo, HttpServletResponse response) {
        List<BranchAuthTypeVo> list = branchAuthTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "分店授权类型", BranchAuthTypeVo.class, response);
    }

    /**
     * 获取分店授权类型详细信息
     *
     * @param branchAuthTypeId 主键
     */
    @SaCheckPermission("branch:authType:query")
    @GetMapping("/{branchAuthTypeId}")
    public R<BranchAuthTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long branchAuthTypeId) {
        return R.ok(branchAuthTypeService.queryById(branchAuthTypeId));
    }

    /**
     * 新增分店授权类型
     */
    @SaCheckPermission("branch:authType:add")
    @Log(title = "分店授权类型", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchAuthTypeBo bo) {
        return toAjax(branchAuthTypeService.insertByBo(bo));
    }

    /**
     * 修改分店授权类型
     */
    @SaCheckPermission("branch:authType:edit")
    @Log(title = "分店授权类型", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchAuthTypeBo bo) {
        return toAjax(branchAuthTypeService.updateByBo(bo));
    }

    /**
     * 删除分店授权类型
     *
     * @param branchAuthTypeIds 主键串
     */
    @SaCheckPermission("branch:authType:remove")
    @Log(title = "分店授权类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchAuthTypeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] branchAuthTypeIds) {
        return toAjax(branchAuthTypeService.deleteWithValidByIds(List.of(branchAuthTypeIds), true));
    }




}
