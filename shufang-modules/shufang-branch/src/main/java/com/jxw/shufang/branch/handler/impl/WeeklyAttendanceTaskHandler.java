package com.jxw.shufang.branch.handler.impl;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.branch.domain.IntegralTask;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import io.seata.common.util.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.jxw.shufang.branch.handler.TaskHandler.BASE_NAME;

/**
 * @author: cyj
 * @date: 2025/7/7
 * @Description 每周打卡次数达到设置值时发放积分
 */
@RequiredArgsConstructor
@Service("WEEKLY_ATTENDANCE_TASK" + BASE_NAME)
public class WeeklyAttendanceTaskHandler implements TaskHandler {

    private final IIntegralRecordService integralRecordService;

    @Override
    public TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId) {
        Long integralTaskId = integralTaskVo.getIntegralTaskId();
        if (integralTaskId == null || studentId == null) {
            return TaskFinishedStatusEnum.RECEIVED;
        }
        String param = integralTaskVo.getParam();
        Integer num;
        if (StringUtils.isBlank(param)) {
            throw new ServiceException("请管理员配置任务参数");
        }
        try {
            num = Integer.parseInt(param.trim());
            // 每周出勤天数，任务设置范围为 0 - 7
            if (num < 0 || num > 7) {
                throw new ServiceException("参数设置异常");
            }
        } catch (Exception e) {
            throw new ServiceException("请管理员配置正确的任务参数");
        }
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setTaskId(integralTaskId);
        integralRecordBo.setCreateTimeStart(DateUtils.getStartOfThisWeek());
        // 本类查询任务的都是当前月份固定发放，本月有记录则为已领取，否则为未完成
        List<IntegralRecordVo> integralRecordVos = integralRecordService.queryList(integralRecordBo);
        return CollUtil.isNotEmpty(integralRecordVos) ? TaskFinishedStatusEnum.RECEIVED
            : TaskFinishedStatusEnum.UNFINISHED;
    }

    @Override
    public void finishTask(IntegralTaskVo integralTaskVo, Long studentId) {
        // 该类任务不可以手动领取，等待定时任务自动发放
        throw new ServiceException("领取异常");
    }

    @Override
    public void validTaskRecord(IntegralTask task) {
        Integer attendanceDayNum;
        try {
            attendanceDayNum = Integer.valueOf(task.getParam());
        } catch (Exception e) {
            throw new ServiceException("参数设置异常");
        }
        if (attendanceDayNum < 0 || attendanceDayNum > 7) {
            throw new ServiceException("天数设置异常");
        }
    }
}
