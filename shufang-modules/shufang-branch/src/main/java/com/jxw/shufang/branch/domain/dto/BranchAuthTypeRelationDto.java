package com.jxw.shufang.branch.domain.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchAuthTypeVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: cyj
 * @date: 2025/3/27
 */
@Data
public class BranchAuthTypeRelationDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 门店id ->门店的授权类型id
     */
    private Map<Long, Long> studentIdToAuthTypeIdMap;
    /**
     * 门店授权类型数据
     */
    private List<RemoteBranchAuthTypeVo> remoteBranchAuthTypeVos;

    public Map<Long, List<Long>> getBranchProductsMap() {
        if (CollectionUtil.isEmpty(studentIdToAuthTypeIdMap) || CollectionUtil.isEmpty(remoteBranchAuthTypeVos)) {
            return new HashMap<>();
        }

        Map<Long, List<Long>> authTypeProductsMap = remoteBranchAuthTypeVos.stream()
            .collect(Collectors.toMap(
                RemoteBranchAuthTypeVo::getBranchAuthTypeId,
                vo -> parseProducts(vo.getProductIds())
            ));

        Map<Long, List<Long>> result = new HashMap<>();
        for (Map.Entry<Long, Long> entry : studentIdToAuthTypeIdMap.entrySet()) {
            Long merchantId = entry.getKey();
            Long authTypeId = entry.getValue();

            List<Long> products = authTypeProductsMap.getOrDefault(authTypeId, new ArrayList<>());
            result.put(merchantId, products);
        }
        return result;
    }

    private List<Long> parseProducts(String productStr) {
        List<Long> products = new ArrayList<>();
        if (productStr == null || productStr.trim().isEmpty()) {
            return products;
        }
        return Arrays.stream(productStr.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .map(Long::valueOf)
            .collect(Collectors.toList());
    }
}
