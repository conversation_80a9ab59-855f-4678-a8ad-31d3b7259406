package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 门店管控开关表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 11:38:30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("branch_control_toggle")
public class BranchControlToggle implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "branch_control_toggle_id")
    private Long branchControlToggleId;


    /**
     * 门店编号
     */
    private Long branchId;

    /**
     * 管控功能模板Id
     */
    private Long branchFeatureTemplateId;

    /**
     * 开关状态（0: 关闭, 1: 开启）
     */
    private Integer status;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
