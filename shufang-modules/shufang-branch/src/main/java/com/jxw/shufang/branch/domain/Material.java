package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 物料对象 material
 *
 *
 * @date 2024-02-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("material")
public class Material extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 物料id
     */
    @TableId(value = "material_id")
    private Long materialId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 物料类型（启动培训、产品介绍、培训讲座、销售工具、宣传资料、运维工具）
     */
    private String materialType;

    /**
     * 是否外链（0是 1否）
     */
    private String isUrl;

    /**
     * 物料外链地址
     */
    private String materialUrl;

    /**
     * 物料文件ids
     */
    private String materialFileIds;


}
