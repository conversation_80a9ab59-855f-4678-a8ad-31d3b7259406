package com.jxw.shufang.branch.controller.android;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralGoodBo;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.branch.service.IIntegralGoodService;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 积分商品-平板端
 * 前端访问路由地址为:/branch/android/integralGood
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/integralGood")
public class AIntegralGoodController extends BaseController {

    private final IIntegralGoodService integralGoodService;

    /**
     * 查询积分商品列表(不查总数)-平板端
     */
    @GetMapping("/list")
    public TableDataInfo<IntegralGoodVo> list(PageQuery pageQuery) {
        if (!LoginHelper.isStudent()) {
            return TableDataInfo.build(List.of());
        }
        IntegralGoodBo bo = new IntegralGoodBo();
        bo.setBranchId(LoginHelper.getBranchId());
        bo.setIntegralGoodStatus("1");//上架
        //不查总页数
        pageQuery.setSearchCount(false);
        TableDataInfo<IntegralGoodVo> tableDataInfo = integralGoodService.queryPageList(bo, pageQuery);
        List<IntegralGoodVo> rows = tableDataInfo.getRows();
        if (!CollUtil.isEmpty(rows)){
            integralGoodService.putIntegralGoodImgUrl(rows);
        }
        return tableDataInfo;
    }

}
