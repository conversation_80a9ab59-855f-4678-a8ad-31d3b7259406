package com.jxw.shufang.branch.mapper;

import com.jxw.shufang.branch.domain.IntegralGood;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 积分商品Mapper接口
 *
 *
 * @date 2024-04-23
 */
public interface IntegralGoodMapper extends BaseMapperPlus<IntegralGood, IntegralGoodVo> {

    int updateStock(Long integralGoodId, int i);

}
