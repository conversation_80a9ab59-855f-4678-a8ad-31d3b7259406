package com.jxw.shufang.branch.domain.vo;

import com.jxw.shufang.branch.domain.BranchControlStudent;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@Data
@AutoMapper(target = BranchControlStudent.class)
public class BranchControlStudentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long branchControlStudentId;

    /**
     * 模板id
     */
    private Long branchFeatureTemplateId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 操作顾问id
     */
    private Long operateId;

    /**
     * 操作顾问
     */
    private String operateName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    private String studentName;


}
