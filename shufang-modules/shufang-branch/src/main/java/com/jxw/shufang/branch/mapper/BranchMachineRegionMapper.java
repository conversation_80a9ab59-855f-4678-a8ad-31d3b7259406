package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.branch.domain.BranchMachineRegion;
import com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 分店机位分区Mapper接口
 *
 *
 * @date 2024-03-18
 */
public interface BranchMachineRegionMapper extends BaseMapperPlus<BranchMachineRegion, BranchMachineRegionVo> {

    //@DataPermission(
    //    @DataColumn(key = "deptName",value = "t.create_dept")
    //)
    List<BranchMachineRegionVo> selectRegionList(@Param(Constants.WRAPPER) LambdaQueryWrapper<BranchMachineRegion> lqw);

    //@DataPermission(
    //    @DataColumn(key = "deptName",value = "t.create_dept")
    //)
    Page<BranchMachineRegionVo> selectMachineRegionPage(@Param("page")Page<BranchMachineRegion> build, @Param(Constants.WRAPPER) LambdaQueryWrapper<BranchMachineRegion> lqw);
}
