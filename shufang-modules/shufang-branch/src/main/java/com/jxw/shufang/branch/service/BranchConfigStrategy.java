package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.bo.BranchConfigBo;
import com.jxw.shufang.branch.domain.vo.BranchConfigVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * @author: cyj
 * @date: 2025/3/11
 */
public interface BranchConfigStrategy {
    /**
     * 获取门店配置项
     */
    void buildConfigItems(BranchConfigVo branchConfigVo);

    /**
     * 更新配置时处理验证，过滤，返回配置的json
     *
     * @return
     */
    String buildConfigJson(BranchConfigBo bo);

    /**
     * 湖区
     *
     * @param configJson 选择的门店配置
     * @param branchVos 被同步的门店信息
     * @param branchIdToConfigMap 被同步的门店id与配置的json
     * @return
     */
    Map<Long, String> getDuplicateConfigJson(String configJson, List<BranchVo> branchVos,
        Map<Long, BranchConfigVo> branchIdToConfigMap);

    BranchConfigTypeEnum getConfigType();
}
