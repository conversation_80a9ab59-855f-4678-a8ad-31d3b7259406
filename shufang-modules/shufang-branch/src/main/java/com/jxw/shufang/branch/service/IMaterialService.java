package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.Material;
import com.jxw.shufang.branch.domain.bo.MaterialBo;
import com.jxw.shufang.branch.domain.vo.MaterialVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 物料Service接口
 *
 *
 * @date 2024-02-21
 */
public interface IMaterialService {

    /**
     * 查询物料
     */
    MaterialVo queryById(Long materialId);

    /**
     * 查询物料列表
     */
    TableDataInfo<MaterialVo> queryPageList(MaterialBo bo, PageQuery pageQuery);

    /**
     * 查询物料列表
     */
    List<MaterialVo> queryList(MaterialBo bo);

    /**
     * 新增物料
     */
    Boolean insertByBo(MaterialBo bo);

    /**
     * 修改物料
     */
    Boolean updateByBo(MaterialBo bo);

    /**
     * 校验并批量删除物料信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 门店初始化物料数据
     *
     * @param branchId 分支id
     *
     * @date 2024/02/25 11:34:26
     */
    void branchInitMaterial(Long branchId);

    /**
     * 清空物料数据
     *
     * @param materialIds 材料ID
     *
     * @date 2024/02/25 11:34:21
     */
    int clean(List<Long> materialIds);

    /**
     * 获取物料菜单信息
     *
     * @param branchId  门店Id
     * @param materialType 物料类型
     *
     * @date 2024/02/26 02:50:01
     */
    MaterialVo getMaterialMenuInfo(Long branchId,String materialType);

    Material queryMaterialById(Long materialId);

    void cleanCache();
}
