package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.BannerBo;
import com.jxw.shufang.branch.domain.vo.BannerVo;
import com.jxw.shufang.branch.service.IBannerService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 轮播图
 * 前端访问路由地址为:/branch/management/banner
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/banner")
public class BannerController extends BaseController {

    private final IBannerService bannerService;

    /**
     * 查询轮播图列表
     */
    //@SaCheckPermission("branch:banner:list")
    @GetMapping("/list")
    public TableDataInfo<BannerVo> list(BannerBo bo, PageQuery pageQuery) {

        return bannerService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出轮播图列表
     */
    @SaCheckPermission("branch:banner:export")
    @Log(title = "轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BannerBo bo, HttpServletResponse response) {
        List<BannerVo> list = bannerService.queryList(bo);
        ExcelUtil.exportExcel(list, "轮播图", BannerVo.class, response);
    }

    /**
     * 获取轮播图详细信息
     *
     * @param bannerId 主键
     */
    @SaCheckPermission("branch:banner:query")
    @GetMapping("/{bannerId}")
    public R<BannerVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long bannerId) {
        return R.ok(bannerService.queryById(bannerId));
    }

    /**
     * 新增轮播图
     */
    @SaCheckPermission("branch:banner:add")
    @Log(title = "轮播图", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BannerBo bo) {
        if(null == bo.getBranchId()&& CollUtil.isEmpty(bo.getBranchIdList())){
            if(null != LoginHelper.getBranchId()){
                bo.setBranchId(LoginHelper.getBranchId());
            }else {
                throw new ServiceException("店铺id为空");
            }
        }
        return toAjax(bannerService.insertByBo(bo));
    }

    /**
     * 修改轮播图
     */
    @SaCheckPermission("branch:banner:edit")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BannerBo bo) {
        if(null == bo.getBranchId()){
            if(null != LoginHelper.getBranchId()){
                bo.setBranchId(LoginHelper.getBranchId());
            }else {
                throw new ServiceException("店铺id为空");
            }
        }
        return toAjax(bannerService.updateByBo(bo));
    }


    /**
     * 修改轮播图状态
     */
    @SaCheckPermission("branch:banner:edit")
    @Log(title = "轮播图", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody BannerBo bo) {
        Long bannerId = bo.getBannerId();
        String bannerStatus = bo.getBannerStatus();
        if (bannerId == null || StringUtils.isBlank(bannerStatus)) {
            return R.fail("参数不完整");
        }
        return toAjax(bannerService.updateByBo(bo));
    }

    /**
     * 删除轮播图
     *
     * @param bannerIds 主键串
     */
    @SaCheckPermission("branch:banner:remove")
    @Log(title = "轮播图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bannerIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] bannerIds) {
        return toAjax(bannerService.deleteWithValidByIds(List.of(bannerIds), true));
    }
}
