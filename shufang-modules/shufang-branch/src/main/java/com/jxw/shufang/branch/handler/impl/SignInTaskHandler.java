package com.jxw.shufang.branch.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.handler.TaskHandler;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.TaskFinishedStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.jxw.shufang.branch.handler.TaskHandler.BASE_NAME;

@RequiredArgsConstructor
@Service("SIGN_IN_TASK"+BASE_NAME)
public class SignInTaskHandler implements TaskHandler {

    private final IIntegralRecordService integralRecordService;

    @Override
    public TaskFinishedStatusEnum getStatus(IntegralTaskVo integralTaskVo, Long studentId) {
        Long integralTaskId = integralTaskVo.getIntegralTaskId();
        if (integralTaskId == null||studentId == null){
            return TaskFinishedStatusEnum.RECEIVED;
        }
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setTaskId(integralTaskId);
        integralRecordBo.setCreateDateStr(DateUtils.getDate());
        List<IntegralRecordVo> integralRecordVos = integralRecordService.queryList(integralRecordBo);
        if (CollUtil.isEmpty(integralRecordVos)){
            return TaskFinishedStatusEnum.UNRECEIVED;
        }
        return TaskFinishedStatusEnum.RECEIVED;
    }

    @Override
    public void finishTask(IntegralTaskVo integralTaskVo,Long studentId){
        TaskFinishedStatusEnum status = getStatus(integralTaskVo, studentId);
        if (!TaskFinishedStatusEnum.UNRECEIVED.equals(status)) {
            throw new ServiceException("错误状态，不可领取");
        }
        //添加记录
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
        integralRecordBo.setChangeNum(new BigDecimal(integralTaskVo.getIntegralNum()==null?0:integralTaskVo.getIntegralNum()));
        integralRecordBo.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
        integralRecordBo.setStudentId(studentId);
        integralRecordBo.setChangeReason(StrUtil.blankToDefault(integralTaskVo.getIntegralTaskNameFill(),integralTaskVo.getIntegralTaskName()));
        integralRecordBo.setTaskId(integralTaskVo.getIntegralTaskId());
        integralRecordBo.setCreateTime(new Date());
        Boolean b = integralRecordService.insertByBo(integralRecordBo);
        if (!b) {
            throw new ServiceException("领取失败");
        }
    }
}
