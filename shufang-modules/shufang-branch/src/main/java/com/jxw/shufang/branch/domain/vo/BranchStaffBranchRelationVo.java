package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jxw.shufang.branch.domain.BranchStaffBranchRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchStaffBranchRelation.class)
public class BranchStaffBranchRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分店员工id
     */
    private Long branchStaffId;

    /**
     * 分店id
     */
    private Long branchId;
    /**
     * 用户id
     */
    private Long userId;
}
