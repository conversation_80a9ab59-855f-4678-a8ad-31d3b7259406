package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.BranchMachineRegion;
import com.jxw.shufang.branch.domain.bo.BranchMachineRegionBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 分店机位分区Service接口
 *
 *
 * @date 2024-03-18
 */
public interface IBranchMachineRegionService {

    /**
     * 查询分店机位分区
     */
    BranchMachineRegionVo queryById(Long branchMachineRegionId);

    /**
     * 查询分店机位分区列表
     */
    TableDataInfo<BranchMachineRegionVo> queryPageList(BranchMachineRegionBo bo, PageQuery pageQuery);

    /**
     * 查询分店机位分区列表
     */
    List<BranchMachineRegionVo> queryList(BranchMachineRegionBo bo);

    /**
     * 新增分店机位分区
     */
    Boolean insertByBo(BranchMachineRegionBo bo);

    /**
     * 修改分店机位分区
     */
    void updateByBo(BranchMachineRegionBo bo);

    /**
     * 校验并批量删除分店机位分区信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    BranchMachineRegion queryBranchMachineRegionById(Long branchMachineRegionId);

    void cleanCache();

    List<BranchMachineSeatVo> listWithOccupied(Date startTime, Date endTime, Long branchMachineRegionId);
}
