package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.BranchMachineSeatBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.branch.service.IBranchMachineSeatService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机位
 * 前端访问路由地址为:/branch/machineSeat
 *
 *
 * @date 2024-03-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/machineSeat")
public class BranchMachineSeatController extends BaseController {

    private final IBranchMachineSeatService branchMachineSeatService;

    /**
     * 查询机位列表
     */
    @SaCheckPermission("branch:machineSeat:list")
    @GetMapping("/list")
    public TableDataInfo<BranchMachineSeatVo> list(BranchMachineSeatBo bo, PageQuery pageQuery) {
        return branchMachineSeatService.queryPageList(bo, pageQuery);
    }



    /**
     * 导出机位列表
     */
    @SaCheckPermission("branch:machineSeat:export")
    @Log(title = "机位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchMachineSeatBo bo, HttpServletResponse response) {
        List<BranchMachineSeatVo> list = branchMachineSeatService.queryList(bo);
        ExcelUtil.exportExcel(list, "机位", BranchMachineSeatVo.class, response);
    }

    /**
     * 获取机位详细信息
     *
     * @param branchMachineSeatId 主键
     */
    @SaCheckPermission("branch:machineSeat:query")
    @GetMapping("/{branchMachineSeatId}")
    public R<BranchMachineSeatVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long branchMachineSeatId) {
        return R.ok(branchMachineSeatService.queryById(branchMachineSeatId));
    }

    /**
     * 新增机位
     */
    @SaCheckPermission("branch:machineSeat:add")
    @Log(title = "机位", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchMachineSeatBo bo) {
        return toAjax(branchMachineSeatService.insertByBo(bo));
    }

    /**
     * 修改机位
     */
    @SaCheckPermission("branch:machineSeat:edit")
    @Log(title = "机位", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchMachineSeatBo bo) {
        return toAjax(branchMachineSeatService.updateByBo(bo));
    }

    /**
     * 删除机位
     *
     * @param branchMachineSeatIds 主键串
     */
    @SaCheckPermission("branch:machineSeat:remove")
    @Log(title = "机位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchMachineSeatIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] branchMachineSeatIds) {
        return toAjax(branchMachineSeatService.deleteWithValidByIds(List.of(branchMachineSeatIds), true));
    }

}
