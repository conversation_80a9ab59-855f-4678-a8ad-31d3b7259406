package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分记录业务对象 integral_record
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralRecord.class, reverseConvertGenerate = false)
public class IntegralRecordBo extends BaseEntity {

    /**
     * 积分记录id
     */
    @NotNull(message = "积分记录id不能为空", groups = { EditGroup.class })
    private Long integralRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 变更类型（0增加 1减少）
     */
    @NotBlank(message = "变更类型（0增加 1减少）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeType;

    /**
     * 变更值（都为正数，展示时根据变更类型进行正负显示）
     */
    @NotNull(message = "变更值（都为正数，展示时根据变更类型进行正负显示）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal changeNum;

    /**
     * 变更方式（0自动 1手动）
     */
    @NotBlank(message = "变更方式（0自动 1手动）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeState;

    /**
     * 变更原因（对应字典值，如课间奖励、季度奖励、课间惩罚等） 字典type:change_reason
     */
    @NotBlank(message = "变更原因（对应字典值，如课间奖励、季度奖励、课间惩罚等）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeReason;

    /**
     * 变更备注
     */
    @NotBlank(message = "变更备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String changeRemark;

    /**
     * 如果记录来自于任务，需记录此id
     */
    private Long taskId;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 创建时间的日期部分，格式yyyy-MM-dd
     */
    private String createDateStr;

    private Date createTimeStart;
    /**
     * 创建结束时间
     */
    private Date createTimeEnd;
}
