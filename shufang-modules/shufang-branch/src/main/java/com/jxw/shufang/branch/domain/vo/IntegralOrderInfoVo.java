package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.IntegralOrderInfo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.common.translation.annotation.Translation;
import com.jxw.shufang.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 积分订单详情视图对象 integral_order_info
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralOrderInfo.class)
public class IntegralOrderInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分订单详情id
     */
    @ExcelProperty(value = "积分订单详情id")
    private Long integralOrderInfoId;

    /**
     * 积分订单id
     */
    @ExcelProperty(value = "积分订单id")
    private Long integralOrderId;

    /**
     * 积分商品id
     */
    @ExcelProperty(value = "积分商品id")
    private Long integralGoodId;

    /**
     * 积分商品图片（oss_id）
     */
    @ExcelProperty(value = "积分商品图片")
    private Long integralGoodImg;

    @Translation(type = TransConstant.OSS_ID_TO_URL,mapper = "integralGoodImg")
    private String integralGoodImgUrl;

    /**
     * 积分商品名称
     */
    @ExcelProperty(value = "积分商品名称")
    private String integralGoodName;

    /**
     * 消耗积分
     */
    @ExcelProperty(value = "消耗积分")
    private BigDecimal integralGoodCost;

    /**
     * 商品类型（1实物 2虚拟）
     */
    @ExcelProperty(value = "商品类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=实物,2=虚拟")
    private String integralGoodType;


}
