package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.vo.BranchDataExportVo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.TOTPGenerator;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.encrypt.annotation.ApiEncrypt;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 分店
 * 前端访问路由地址为:/branch/branch
 *
 * @date 2024-02-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/branch")
@Slf4j
public class BranchController extends BaseController {

    private final IBranchService branchService;

    /**
     * 查询分店列表
     */
    @SaCheckPermission("branch:branch:list")
    @GetMapping("/list")
    public TableDataInfo<BranchVo> list(BranchBo bo, PageQuery pageQuery) {
        return branchService.queryPageList(bo, pageQuery);
    }


    /**
     * 查询所有分店列表,常用于下拉框类的查询，数据范围由数据权限控制
     */
    @SaCheckPermission("branch:branch:optionList")
    @GetMapping("/optionList")
    public R<List<BranchVo>> branchOptionList() {
        return R.ok(branchService.branchOptionList());
    }

    /**
     * 导出分店列表
     */
    @SaCheckPermission("branch:branch:export")
    @Log(title = "分店", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchBo bo, HttpServletResponse response) {
        List<BranchVo> list = branchService.queryList(bo);
        branchService.putExcelOtherInfo(list);
        ExcelUtil.exportExcel(list, "分店", BranchVo.class, response);
    }


    /**
     * 导出门店数据
     */
    @SaCheckPermission("branch:branch:exportStatistics")
    @Log(title = "分店数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBranchData")
    public void exportBranchData(BranchBo bo, HttpServletResponse response) {
        bo.setBranchStatus(UserConstants.BRANCH_NORMAL);
        List<BranchVo> list = branchService.statistics(bo);
        ExcelUtil.exportExcel(MapstructUtils.convert(list, BranchDataExportVo.class), "门店数据", BranchDataExportVo.class, response);
    }


    /**
     * 获取分店详细信息
     *
     * @param branchId 主键
     */
    @SaCheckPermission("branch:branch:query")
    @GetMapping("/{branchId}")
    public R<BranchVo> getInfo(@NotNull(message = "主键不能为空")
                               @PathVariable Long branchId) {
        return R.ok(branchService.queryById(branchId));
    }

    /**
     * 获取分店详细信息
     *
     * @param deptId 主键
     */
    @GetMapping("/getBranchInfoByDept")
    public R<BranchVo> getBranchInfoByCreateDept(@NotNull(message = "请输入部门id") Long deptId) {
        return R.ok(branchService.getBranchInfoByCreateDept(deptId));
    }

    /**
     * 新增分店
     */
    @SaCheckPermission("branch:branch:add")
    @Log(title = "分店", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchBo bo) {
        return toAjax(branchService.insertByBo(bo));
    }

    /**
     * 修改分店
     */
    @SaCheckPermission("branch:branch:edit")
    @Log(title = "分店", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    @GlobalTransactional(rollbackFor = Exception.class)
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchBo bo) {
        branchService.updateByBo(bo);
//        if (!ObjectUtils.isEmpty(bo.getBranchAuthTypeId())) {
//            branchService.branchAddAuthType(bo.getBranchId(), bo.getBranchAuthTypeId());
//        }
        return R.ok();
    }

    @SaCheckPermission("branch:branch:edit")
    @Log(title = "分店", businessType = BusinessType.UPDATE)
    @PutMapping("/branchAddAuthType")
    public R<Void> branchAddAuthType(@NotNull(message = "门店id不能为空") Long branchId,
                                     @NotNull(message = "授权类型id不能为空") Long branchAuthTypeId) {
        branchService.branchAddAuthType(branchId,branchAuthTypeId);
        return R.ok();
    }

    // 重置密码
    @ApiEncrypt
    @SaCheckPermission("branch:branch:resetPwd")
    @Log(title = "分店", businessType = BusinessType.UPDATE)
    @PutMapping("/restPwd")
    public R<Void> restPwd(@RequestBody BranchBo bo) {
        branchService.restPwd(bo.getBranchAdminUserIdList());
        return R.ok();
    }

    /**
     * 删除分店
     *
     * @param branchIds 主键串
     */
    @SaCheckPermission("branch:branch:remove")
    @Log(title = "分店", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] branchIds) {
        return toAjax(branchService.deleteWithValidByIds(List.of(branchIds), true));
    }


    /**
     * 获取当前登录用户是否是门店管理员
     */
    @GetMapping("/isBranchAdmin")
    public R<Boolean> isBranchAdmin() {
        return R.ok(LoginHelper.isBranchAdmin());
    }


    /**
     * 门店数据统计
     */
    @SaCheckPermission("branch:branch:statistics")
    @GetMapping("/statisticsPage")
    public TableDataInfo<BranchVo> statisticsPage(BranchBo bo, PageQuery pageQuery) {
        bo.setBranchStatus(UserConstants.BRANCH_NORMAL);
        return branchService.statisticsPage(bo, pageQuery);
    }


    /**
     * 门店平板端管控
     */
    @SaCheckPermission("branch:status:query")
    @GetMapping("/selectBranchStatus")
    public R<BranchVo> selectBranch() {
        Long selectDeptId = LoginHelper.getSelectDeptId();
        if (!ObjectUtils.isEmpty(selectDeptId)) {
            BranchVo branchVo = branchService.selectBranchByDeptId(selectDeptId);
            if (!ObjectUtils.isEmpty(branchVo)) {
                return R.ok(branchVo);
            }
        }
        Long branchId = LoginHelper.getBranchId();
        if (ObjectUtils.isEmpty(branchId)) {
            throw new ServiceException("请选择对应的门店进行操作");
        }
        return R.ok(branchService.queryById(branchId));
    }

    /**
     * 门店平板端管控
     */
    @SaCheckPermission("branch:status:update")
    @PostMapping("/updateBranchStatus")
    public  R<Boolean>  updateBranchStatus(@RequestBody BranchBo branchBo) {
        return R.ok( branchService.updateBranchStatus(branchBo));
    }

    /**
     * 门店平板端管控
     */
    @SaCheckPermission("branch:status:query")
    @GetMapping("/getCode")
    public  R<String>  generateTOTP() {
        try {
            return R.ok("",TOTPGenerator.generateTOTP());
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("门店平板端管控获取动态验证码失败:",e);
            throw  new ServiceException("服务繁忙请稍后再试");
        }
    }

    @GetMapping("/getByDept")
    public R<List<BranchVo>> queryByCreateDept(@RequestParam Long deptId) {
        return R.ok(branchService.queryByCreateDept(deptId));
    }

}
