package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 轮播图对象 banner
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("banner")
public class Banner extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图id
     */
    @TableId(value = "banner_id")
    private Long bannerId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 轮播图状态（1上架 2下架）
     */
    private String bannerStatus;

    /**
     * 轮播图名称
     */
    private String bannerName;

    /**
     * 轮播图（oss_id）
     */
    private Long bannerImg;

    /**
     * 轮播图类型（对应字典值）
     */
    private String bannerType;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
