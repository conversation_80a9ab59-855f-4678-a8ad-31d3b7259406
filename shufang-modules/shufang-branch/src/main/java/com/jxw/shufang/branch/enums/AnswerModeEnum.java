package com.jxw.shufang.branch.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 作答模式枚举
 */
@Getter
@AllArgsConstructor
public enum AnswerModeEnum {

    /**
     * 线上作答
     */
    ONLINE_ANSWER(1, "线上作答"),
    /**
     * 线下作答
     */
    OFFLINE_ANSWER(2, "线下作答");

    /**
     * 模式code
     */
    private Integer modeCode;

    /**
     * 模式名称
     */
    private String modeName;

    /**
     * 通过code获取枚举
     * @param code
     * @return
     */
    public static AnswerModeEnum getByCode(int code) {
        for (AnswerModeEnum value : values()) {
            if (value.getModeCode() == code) {
                return value;
            }
        }
        return null;
    }
}
