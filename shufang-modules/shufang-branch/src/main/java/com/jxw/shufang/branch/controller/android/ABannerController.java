package com.jxw.shufang.branch.controller.android;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.BannerBo;
import com.jxw.shufang.branch.domain.vo.BannerVo;
import com.jxw.shufang.branch.service.IBannerService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 轮播图-平板端
 * 前端访问路由地址为:/branch/android/banner
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/banner")
public class ABannerController extends BaseController {

    private final IBannerService bannerService;

    /**
     * 首页轮播图列表，只查询当前会员门店下的轮播图
     */
    @GetMapping("/indexList")
    public R<List<BannerVo>> indexList() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        BannerBo bannerBo = new BannerBo();
        bannerBo.setBranchId(LoginHelper.getBranchId());
        bannerBo.setBannerStatus(UserConstants.BANNER_STATUS_UP);
        return R.ok(bannerService.queryList(bannerBo));
    }

}
