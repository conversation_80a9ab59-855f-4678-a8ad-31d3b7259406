package com.jxw.shufang.branch.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.core.validate.QueryGroup;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BranchConfigBo {
    /**
     * 门店ID
     */
    @NotNull(message = "请选择门店信息", groups = { QueryGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 参数配置类型
     */
    @NotNull(message = "请选择配置类型", groups = {EditGroup.class})
    private Integer configType;

    private List<Integer> configTypeList;

    @NotNull(message = "请输入配置", groups = { EditGroup.class })
    private Object configData;

    /**
     * 批量同步的部门id
     */
    private Set<Long> deptIdList;
    /**
     * 门店id集合
     */
    private Collection<Long> branchIds;

    public BranchConfigBo(Long branchId,Integer configType){
        this.branchId = branchId;
        this.configType = configType;
    }
}
