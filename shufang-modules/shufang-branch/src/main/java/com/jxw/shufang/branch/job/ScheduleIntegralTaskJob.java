package com.jxw.shufang.branch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.branch.enums.ScheduleIntegralEnum;
import com.jxw.shufang.branch.enums.TaskEnum;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.branch.service.IIntegralTaskService;
import com.jxw.shufang.branch.wrapper.IntegralDateMappingWrapper;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: cyj
 * @date: 2025/7/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScheduleIntegralTaskJob {

    private final IIntegralTaskService integralTaskService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteStudentService remoteStudentService;
    private final IBranchService branchService;
    private final IIntegralRecordService integralRecordService;
    private final IntegralDateMappingWrapper integralDateMappingWrapper;

    @XxlJob("scheduleIntegralTaskJob")
    public ReturnT<String> execute(String param) {
        log.info("定时发放积分任务开始");
        ScheduleIntegralEnum scheduleEnumByDate =
            integralDateMappingWrapper.getScheduleEnumByDate(DateUtil.dayOfMonth(new Date()));
        if (null == scheduleEnumByDate) {
            log.info("定时任务结束，今日没有可以执行的定时发放任务");
            return ReturnT.SUCCESS;
        }
        IntegralTaskBo queryTaskBo = new IntegralTaskBo();
        queryTaskBo.setTaskTrigger(TaskEnum.SCHEDULE_TASK.toString());
        queryTaskBo.setParam(scheduleEnumByDate.getScheduleName());
        queryTaskBo.setTaskStatus("1");
        List<IntegralTaskVo> integralTaskVos = integralTaskService.queryList(queryTaskBo);
        if (CollUtil.isEmpty(integralTaskVos)) {
            log.info("定时任务结束，今日没有可以执行的定时发放任务");
            return ReturnT.SUCCESS;
        }
        for (IntegralTaskVo integralTaskVo : integralTaskVos) {
            if (integralTaskVo.getIntegralNum() == null || integralTaskVo.getIntegralNum() == 0) {
                log.info("定时发放积分任务异常，发放的积分为0，自动跳过该积分任务，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
                continue;
            }
            // 积分任务部门
            List<RemoteDeptVo> selfAndChildShopList =
                remoteDeptService.getSelfAndChildShopList(integralTaskVo.getCreateDept());
            if (CollUtil.isEmpty(selfAndChildShopList)) {
                log.info("定时发放积分任务异常，任务没有适用的门店，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
                continue;
            }
            // 查询门店id
            BranchBo branchBo = new BranchBo();
            branchBo.setCreateDeptIds(
                selfAndChildShopList.stream().map(RemoteDeptVo::getDeptId).collect(Collectors.toSet()));
            List<Long> branchIdList = branchService.listBranchId(branchBo);
            if (CollUtil.isEmpty(branchIdList)) {
                log.info("定时发放积分任务异常，任务没有适用的门店，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
                continue;
            }
            RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
            remoteStudentBo.setBranchIdList(branchIdList);
            List<Long> studentIdList = remoteStudentService.queryStudentIdList(remoteStudentBo);
            if (CollUtil.isEmpty(studentIdList)) {
                log.info("定时发放积分任务异常，门店下没有匹配的会员，积分任务ID：{}", integralTaskVo.getIntegralTaskId());
                continue;
            }
            List<IntegralRecord> saveBoList = new ArrayList<>();
            for (Long studentId : studentIdList) {
                IntegralRecord integralRecord = new IntegralRecord();
                integralRecord.setChangeType(UserConstants.INTEGRAL_CHANGE_TYPE_ADD);
                integralRecord.setChangeNum(
                    new BigDecimal(integralTaskVo.getIntegralNum() == null ? 0 : integralTaskVo.getIntegralNum()));
                integralRecord.setChangeState(UserConstants.INTEGRAL_CHANGE_STATE_AUTO);
                integralRecord.setStudentId(studentId);
                integralRecord.setChangeReason(StrUtil.blankToDefault(integralTaskVo.getIntegralTaskNameFill(),
                    integralTaskVo.getIntegralTaskName()));
                integralRecord.setTaskId(integralTaskVo.getIntegralTaskId());
                integralRecord.setCreateDept(integralTaskVo.getCreateDept());
                saveBoList.add(integralRecord);
                if (saveBoList.size() >= 1000) {
                    saveBatch(integralTaskVo, saveBoList);
                    saveBoList = new ArrayList<>();
                }
            }
            if (CollUtil.isNotEmpty(saveBoList)) {
                saveBatch(integralTaskVo, saveBoList);
            }
        }
        log.info("定时发放积分任务结束，发放的任务ID：{}",
            integralTaskVos.stream().map(IntegralTaskVo::getIntegralTaskId).collect(Collectors.toList()));
        return ReturnT.SUCCESS;
    }

    /**
     * 保存发放记录
     *
     * @param integralTaskVo
     * @param saveBoList
     */
    private void saveBatch(IntegralTaskVo integralTaskVo, List<IntegralRecord> saveBoList) {
        boolean saveRecord = integralRecordService.batchSaveRecord(saveBoList);
        if (!saveRecord) {
            log.error("发放记录保存失败，任务id：{}，发放会员id:{}", integralTaskVo.getIntegralTaskId(),
                saveBoList.stream().map(IntegralRecord::getStudentId).collect(Collectors.toList()));
        }
    }

}
