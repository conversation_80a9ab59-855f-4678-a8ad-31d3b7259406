package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.BranchMachineRegionBo;
import com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo;
import com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo;
import com.jxw.shufang.branch.service.IBranchMachineRegionService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 分店机位分区
 * 前端访问路由地址为:/branch/machineRegion
 *
 *
 * @date 2024-03-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/machineRegion")
public class BranchMachineRegionController extends BaseController {

    private final IBranchMachineRegionService branchMachineRegionService;

    /**
     * 查询分店机位分区列表
     */
    @SaCheckPermission("branch:machineRegion:list")
    @GetMapping("/list")
    public TableDataInfo<BranchMachineRegionVo> list(BranchMachineRegionBo bo, PageQuery pageQuery) {
        return branchMachineRegionService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询分店机位分区列表
     */
    @SaCheckPermission("branch:machineRegion:list")
    @GetMapping("/queryList")
    public R<List<BranchMachineRegionVo>> list(BranchMachineRegionBo bo) {
        if (LoginHelper.isBranchStaff()&&LoginHelper.getBranchId()!=null){
            bo.setBranchId(LoginHelper.getBranchId());
        }
        if (LoginHelper.isBranchAdmin()){
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }
        return R.ok(branchMachineRegionService.queryList(bo));
    }

    /**
     * 导出分店机位分区列表
     */
    @SaCheckPermission("branch:machineRegion:export")
    @Log(title = "分店机位分区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchMachineRegionBo bo, HttpServletResponse response) {
        List<BranchMachineRegionVo> list = branchMachineRegionService.queryList(bo);
        ExcelUtil.exportExcel(list, "分店机位分区", BranchMachineRegionVo.class, response);
    }

    /**
     * 获取分店机位分区详细信息
     *
     * @param branchMachineRegionId 主键
     */
    @SaCheckPermission("branch:machineRegion:query")
    @GetMapping("/{branchMachineRegionId}")
    public R<BranchMachineRegionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long branchMachineRegionId) {
        return R.ok(branchMachineRegionService.queryById(branchMachineRegionId));
    }

    /**
     * 新增分店机位分区
     */
    @SaCheckPermission("branch:machineRegion:add")
    @Log(title = "分店机位分区", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchMachineRegionBo bo) {

        return toAjax(branchMachineRegionService.insertByBo(bo));
    }

    /**
     * 修改分店机位分区
     */
    @SaCheckPermission("branch:machineRegion:edit")
    @Log(title = "分店机位分区", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchMachineRegionBo bo) {
        branchMachineRegionService.updateByBo(bo);
        return R.ok();
    }

    /**
     * 删除分店机位分区
     *
     * @param branchMachineRegionIds 主键串
     */
    @SaCheckPermission("branch:machineRegion:remove")
    @Log(title = "分店机位分区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchMachineRegionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] branchMachineRegionIds) {
        return toAjax(branchMachineRegionService.deleteWithValidByIds(List.of(branchMachineRegionIds), true));
    }

    /**
     *  获取分店机位分区下拉列表
     */
    @GetMapping("/options")
    public R<List<BranchMachineRegionVo>> options(BranchMachineRegionBo bo) {
        return R.ok(branchMachineRegionService.queryList(bo));
    }

    /**
     * 查询机位列表，带是否占用，必须传使用时间
     */
    @SaCheckPermission("branch:machineSeat:list")
    @GetMapping("/listWithOccupied")
    public R<List<BranchMachineSeatVo>> listWithOccupied(@NotNull(message = "开始使用时间不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                         @NotNull(message = "结束使用时间不能为空") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                         @NotNull(message = "分区Id不能为空") Long branchMachineRegionId) {
        return R.ok(branchMachineRegionService.listWithOccupied(startTime, endTime, branchMachineRegionId));
    }

}
