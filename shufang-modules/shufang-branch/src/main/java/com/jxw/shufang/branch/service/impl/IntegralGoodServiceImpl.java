package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.domain.IntegralGood;
import com.jxw.shufang.branch.domain.bo.IntegralGoodBo;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.branch.mapper.IntegralGoodMapper;
import com.jxw.shufang.branch.service.IIntegralGoodService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 积分商品Service业务层处理
 *
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class IntegralGoodServiceImpl implements IIntegralGoodService {

    private final IntegralGoodMapper baseMapper;

    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 查询积分商品
     */
    @Override
    public IntegralGoodVo queryById(Long integralGoodId){
        return baseMapper.selectVoById(integralGoodId);
    }

    /**
     * 查询积分商品列表
     */
    @Override
    public TableDataInfo<IntegralGoodVo> queryPageList(IntegralGoodBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IntegralGood> lqw = buildQueryWrapper(bo);
        Page<IntegralGoodVo> result = baseMapper.selectVoPage( pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分商品列表
     */
    @Override
    public List<IntegralGoodVo> queryList(IntegralGoodBo bo) {
        LambdaQueryWrapper<IntegralGood> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IntegralGood> buildQueryWrapper(IntegralGoodBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IntegralGood> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, IntegralGood::getBranchId, bo.getBranchId());
        lqw.eq(bo.getIntegralGoodImg() != null, IntegralGood::getIntegralGoodImg, bo.getIntegralGoodImg());
        lqw.like(StringUtils.isNotBlank(bo.getIntegralGoodName()), IntegralGood::getIntegralGoodName, bo.getIntegralGoodName());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralGoodDesc()), IntegralGood::getIntegralGoodDesc, bo.getIntegralGoodDesc());
        lqw.eq(bo.getIntegralGoodCost() != null, IntegralGood::getIntegralGoodCost, bo.getIntegralGoodCost());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralGoodType()), IntegralGood::getIntegralGoodType, bo.getIntegralGoodType());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralGoodRule()), IntegralGood::getIntegralGoodRule, bo.getIntegralGoodRule());
        lqw.eq(bo.getIntegralGoodStock() != null, IntegralGood::getIntegralGoodStock, bo.getIntegralGoodStock());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralGoodStatus()), IntegralGood::getIntegralGoodStatus, bo.getIntegralGoodStatus());
        lqw.eq(bo.getIntegralGoodSort() != null, IntegralGood::getIntegralGoodSort, bo.getIntegralGoodSort());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), IntegralGood::getBranchId, bo.getBranchIdList());
        lqw.orderByAsc(IntegralGood::getIntegralGoodSort);
        return lqw;
    }

    /**
     * 新增积分商品
     */
    @Override
    public Boolean insertByBo(IntegralGoodBo bo) {
        IntegralGood add = MapstructUtils.convert(bo, IntegralGood.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralGoodId(add.getIntegralGoodId());
        }
        return flag;
    }

    /**
     * 修改积分商品
     */
    @Override
    public Boolean updateByBo(IntegralGoodBo bo) {
        IntegralGood update = MapstructUtils.convert(bo, IntegralGood.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralGood entity){
        //做一些数据校验,如唯一约束
        if (entity.getIntegralGoodCost()!= null && BigDecimal.ZERO.compareTo(entity.getIntegralGoodCost()) >= 0){
            throw new ServiceException("积分商品消耗积分不能小于等于0");
        }
        if (entity.getIntegralGoodStock() != null && entity.getIntegralGoodStock() < 0){
            throw new ServiceException("积分商品库存不能小于0");
        }
    }

    /**
     * 批量删除积分商品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean updateStock(Long integralGoodId, int i) {
        return baseMapper.updateStock(integralGoodId, i)>0;
    }

    public void putIntegralGoodImgUrl(List<IntegralGoodVo> list){
        if (CollUtil.isEmpty(list)){
            return;
        }

        List<Long> fileIdList = list.stream().map(IntegralGoodVo::getIntegralGoodImg).filter(Objects::nonNull).distinct().toList();
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIdList(fileIdList);
        if (CollUtil.isEmpty(remoteFiles)){
            return;
        }
        Map<Long, String> fileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, RemoteFile::getUrl));

        list.forEach(item -> item.setIntegralGoodImgUrl(fileMap.get(item.getIntegralGoodImg())));
    }
}
