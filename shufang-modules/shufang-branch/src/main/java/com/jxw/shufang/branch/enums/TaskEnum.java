package com.jxw.shufang.branch.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TaskEnum {
    PRACTICE_TASK("练习任务", "每天完成$次练习", 0, "1", true, false, true), SIGN_IN_TASK("签到任务", "签到", 0, "0", false, true, true),
    STUDY_TASK("每日学习时长", "学习$分钟", 0, "30", false, false, true),
    STUDY_TODAY_TASK("完成当日学习任务", "完成当日学习任务", 0, "0", false, true, true),
    TEST_TASK("小测任务", "每天完成$次小测", 0, "10", true, false, true),
    STUDY_MONTH_TASK("每月学习天数", "本月累计学习$天", 0, "10", false, false, true),
    SCHEDULE_TASK("每月固定发放积分", "每月$发放积分", 0, "月初", false, false, false),
    WEEKLY_ATTENDANCE_TASK("每周出勤任务", "本周出勤累计$天", 0, "5", false, false, false);
    private final String desc;
    private final String integralTaskName;
    private final Integer defaultIntegralNum;
    private final String defaultParam;
    private final boolean isStopped;
    private final boolean isSingleCreate; // 是否只能创建一次
    /**
     * 是否在学生端展示
     */
    private final boolean studentVisible;

    public static TaskEnum getEnum(String integralTaskName) {
        for (TaskEnum taskEnum : values()) {
            if (taskEnum.name().equals(integralTaskName)) {
                return taskEnum;
            }
        }
        return null;
    }
}
