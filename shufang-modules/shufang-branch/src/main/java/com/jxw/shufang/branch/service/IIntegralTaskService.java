package com.jxw.shufang.branch.service;

import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.branch.domain.bo.IntegralTaskBo;
import com.jxw.shufang.branch.domain.bo.OptionalTaskBo;
import com.jxw.shufang.branch.domain.vo.IntegralTaskVo;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 积分任务Service接口
 *
 *
 * @date 2024-04-25
 */
public interface IIntegralTaskService {

    /**
     * 查询积分任务
     */
    IntegralTaskVo queryById(Long integralTaskId);

    /**
     * 查询积分任务列表
     */
    TableDataInfo<IntegralTaskVo> queryPageList(IntegralTaskBo bo, PageQuery pageQuery);

    /**
     * 查询积分任务列表
     */
    List<IntegralTaskVo> queryList(IntegralTaskBo bo);

    /**
     * 新增积分任务
     */
    Boolean insertByBo(IntegralTaskBo bo);

    /**
     * 修改积分任务
     */
    Boolean updateByBo(IntegralTaskBo bo);

    /**
     * 校验并批量删除积分任务信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询学生综合任务列表
     *
     * @param bo bo
     *
     * @date 2024/04/26 02:41:17
     */
    List<IntegralTaskVo> queryStudentIntegralTaskList(Long studentId,IntegralTaskBo bo);

    /**
     * 接收任务积分
     *
     * @param studentId      学生id
     * @param integralTaskId 积分任务id
     *
     * @date 2024/04/27 01:02:48
     */
    void receiveTaskIntegral(Long studentId, Long integralTaskId);

    /**
     * 初始化积分任务
     * @param branchId
     */
    void initIntegralTask(Long branchId);

    List<IntegralTaskVo> optional(OptionalTaskBo bo);
}
