package com.jxw.shufang.branch.service;

import java.util.List;

import com.jxw.shufang.branch.domain.BranchAuthType;
import com.jxw.shufang.branch.domain.bo.BranchAuthTypeBo;
import com.jxw.shufang.branch.domain.dto.BranchAuthTypeRelationDto;
import com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;

/**
 * 分店授权类型Service接口
 *
 *
 * @date 2024-02-21
 */
public interface IBranchAuthTypeService {

    /**
     * 查询分店授权类型
     */
    BranchAuthTypeVo queryById(Long branchAuthTypeId);

    /**
     * 查询分店授权类型列表
     */
    TableDataInfo<BranchAuthTypeVo> queryPageList(BranchAuthTypeBo bo, PageQuery pageQuery);

    /**
     * 查询分店授权类型列表
     */
    List<BranchAuthTypeVo> queryList(BranchAuthTypeBo bo);

    /**
     * 新增分店授权类型
     */
    Boolean insertByBo(BranchAuthTypeBo bo);

    /**
     * 修改分店授权类型
     */
    Boolean updateByBo(BranchAuthTypeBo bo);

    /**
     * 校验并批量删除分店授权类型信息
     */
    Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid);

    BranchAuthType queryBranchAuthTypeById(Long branchAuthTypeId);

    void cleanCache();

    BranchAuthTypeRelationDto getAuthTypeMapByBranchId(List<Long> branchIdList) throws ServiceException;

}
