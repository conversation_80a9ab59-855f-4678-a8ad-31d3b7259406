package com.jxw.shufang.branch.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.branch.domain.Material;
import com.jxw.shufang.branch.domain.vo.MaterialVo;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 物料Mapper接口
 *
 *
 * @date 2024-02-21
 */
public interface MaterialMapper extends BaseMapperPlus<Material, MaterialVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "b.create_dept"),
        @DataColumn(key = "userName", value = "b.create_by")
    })
    @BranchColumn(key = "deptName", value = "b.create_dept")
    Page<MaterialVo> selectPageList(@Param("page") Page<Material> page, @Param(Constants.WRAPPER) QueryWrapper<Material> lqw);
}
