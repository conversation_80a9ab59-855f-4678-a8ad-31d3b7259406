package com.jxw.shufang.branch.controller.android;

import com.jxw.shufang.branch.domain.bo.IntegralRecordBo;
import com.jxw.shufang.branch.domain.vo.IntegralRecordVo;
import com.jxw.shufang.branch.service.IIntegralRecordService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.api.RemoteDictService;
import com.jxw.shufang.system.api.domain.vo.RemoteDictDataVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 积分记录-平板端
 * 前端访问路由地址为:/branch/android/integralRecord
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/android/integralRecord")
public class AIntegralRecordController extends BaseController {

    private final IIntegralRecordService integralRecordService;

    @DubboReference
    private RemoteDictService remoteDictService;


    /**
     * 查询积分-平板端
     *
     *
     * @date 2024/04/24 02:23:24
     */
    @GetMapping("/queryStudentIntegral")
    public R<BigDecimal> queryIntegral() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        BigDecimal integralByStudentId = integralRecordService.getIntegralByStudentId(LoginHelper.getStudentId());
        return R.ok(integralByStudentId);
    }

    /**
     * 查询积分记录-平板端
     *
     */
    @GetMapping("/queryIntegralRecord")
    public R<List<IntegralRecordVo>> queryIntegralRecord() {
        if (!LoginHelper.isStudent()) {
            return R.fail("当前用户不是会员");
        }
        IntegralRecordBo integralRecordBo = new IntegralRecordBo();
        integralRecordBo.setStudentId(LoginHelper.getStudentId());
        integralRecordBo.setOrderBy("create_time desc");
        List<IntegralRecordVo> data = integralRecordService.queryList(integralRecordBo);
        final String changeReasonType = "change_reason";
        final String exchangeGoods = "EXCHANGE_GOODS";
        List<RemoteDictDataVo> remoteDictDataVos = remoteDictService.selectDictDataByType(changeReasonType);
        List<String> dictValues = remoteDictDataVos.stream().map(RemoteDictDataVo::getDictValue).filter(s -> !exchangeGoods.equals(s)).toList();
        data.forEach(i -> {
            if (dictValues.contains(i.getChangeReason())) {
                String dictLabel = remoteDictDataVos.stream()
                    .filter(d -> d.getDictValue().equals(i.getChangeReason()))
                    .findFirst()
                    .orElse(new RemoteDictDataVo())
                    .getDictLabel();
                i.setDisplayStr(dictLabel);
            } else if (exchangeGoods.equals(i.getChangeReason())) {
                i.setDisplayStr(i.getChangeRemark());
            } else {
                i.setDisplayStr(i.getChangeReason());
            }
        });
        return R.ok(data);
    }


}
