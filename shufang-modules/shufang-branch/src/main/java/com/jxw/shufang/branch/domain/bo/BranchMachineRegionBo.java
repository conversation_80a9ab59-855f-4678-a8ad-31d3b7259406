package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.BranchMachineRegion;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 分店机位分区业务对象 branch_machine_region
 *
 *
 * @date 2024-03-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchMachineRegion.class, reverseConvertGenerate = false)
public class BranchMachineRegionBo extends BaseEntity {

    /**
     * 分店机位分区id
     */
    @NotNull(message = "分店机位分区id不能为空", groups = { EditGroup.class })
    private Long branchMachineRegionId;

    /**
     * 分店id
     */
    @NotNull(message = "分店id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 分区名称
     */
    @NotBlank(message = "分区名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String regionName;

    /**
     * 分区数量
     */
    @NotNull(message = "分区数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionNum;

    /**
     * 机位图片（oss_id 每个分区的机位分布示意图）
     */
    //@NotNull(message = "机位图片（oss_id 每个分区的机位分布示意图）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long regionImg;

    private String delFlag;

    private List<Long> branchIdList;

}
