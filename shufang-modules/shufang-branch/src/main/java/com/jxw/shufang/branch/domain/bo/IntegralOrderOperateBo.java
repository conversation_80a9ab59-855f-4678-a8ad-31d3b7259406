package com.jxw.shufang.branch.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.branch.domain.IntegralOrderOperate;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）业务对象 integral_order_operate
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = IntegralOrderOperate.class, reverseConvertGenerate = false)
public class IntegralOrderOperateBo extends BaseEntity {

    /**
     * 积分订单操作id
     */
    @NotNull(message = "积分订单操作id不能为空", groups = { EditGroup.class })
    private Long integralOrderOperateId;

    /**
     * 积分订单id
     */
    @NotNull(message = "积分订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long integralOrderId;

    /**
     * 订单状态（1待兑换 2已兑换 3已取消）
     */
    @NotBlank(message = "订单状态（1待兑换 2已兑换 3已取消）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String integralOrderOperateStatus;

    /**
     * 消耗积分
     */
    @NotNull(message = "消耗积分不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal paymentIntegral;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderOperateRemark;


}
