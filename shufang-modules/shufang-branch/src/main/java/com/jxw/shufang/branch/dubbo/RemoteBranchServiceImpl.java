package com.jxw.shufang.branch.dubbo;


import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.branch.api.domain.vo.RemotePayMerchantConfigVO;
import com.jxw.shufang.branch.domain.Branch;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.service.IBranchAuthRecordService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.system.api.domain.vo.RemotePayMerchantConfig;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteBranchServiceImpl implements RemoteBranchService {
    private final IBranchService branchService;

    private final IBranchAuthRecordService branchAuthRecordService;

    @Override
    public RemoteBranchVo selectBranchByDeptId(Long deptId) throws ServiceException {
        BranchVo branchVo = branchService.selectBranchByDeptId(deptId);
        return MapstructUtils.convert(branchVo, RemoteBranchVo.class);
    }

    @Override
    public RemoteBranchVo selectBranchById(Long branchId) throws ServiceException {
        BranchVo branchVo = branchService.queryById(branchId);
        return MapstructUtils.convert(branchVo, RemoteBranchVo.class);
    }

    /**
     * 通过门店ID集合 查询门店信息列表
     *
     * @param branchIds
     * @return
     */
    @Override
    public List<RemoteBranchVo> selectBranchList(List<Long> branchIds) {
        List<BranchVo> branchVos = branchService.selectDeptByIds(branchIds);
        return branchVos.stream().map(branchVo -> MapstructUtils.convert(branchVo, RemoteBranchVo.class)).toList();
    }

    @Override
    public Boolean selectAuthStatusByDeptId(Long deptId) throws ServiceException {
        BranchVo branchVo = branchService.selectBranchByDeptId(deptId);

        //2B2C合并，新版授权没有过期限制
//        if (branchVo != null) {
//            Long branchId = branchVo.getBranchId();
//            BranchAuthRecordVo branchAuthRecordVo = branchAuthRecordService.queryLastAuthRecordByBranchId(branchId);
//            if (branchAuthRecordVo!=null){
//                branchVo.setAuthStatus(branchAuthRecordVo.getAuthStatus());
//            }
//        }
        //授权状态（0正常 1过期）authStatus
        //分店状态（0正常 1停用）branchStatus
        //非门店直接通过
//        return null == branchVo || ((Integer) 0).equals(branchVo.getAuthStatus()) && "0".equals(branchVo.getBranchStatus());
        return null == branchVo || "0".equals(branchVo.getBranchStatus());
    }


    @Override
    public Long selectDeptIdByBranchId(Long branchId) throws ServiceException {
        BranchVo branchVo = branchService.queryById(branchId);
        if (branchVo == null) {
            throw new ServiceException("查询不到门店信息。");
        }
        return branchVo.getCreateDept();
    }

    @Override
    public List<RemoteBranchVo> selectBranchList(RemoteBranchBo remoteBranchBo) {
        BranchBo branchBo = MapstructUtils.convert(remoteBranchBo, BranchBo.class);
        List<BranchVo> branchVos = branchService.selectSimpleBranInfo(branchBo);
        List<RemoteBranchVo> remoteBranchVoList = new ArrayList<>();
        branchVos.forEach(branchVo -> {
            RemoteBranchVo convert = MapstructUtils.convert(branchVo, RemoteBranchVo.class);
            convert.setBranchAuthTypeName(
                null != branchVo.getBranchAuthType() ? branchVo.getBranchAuthType().getBranchAuthTypeName() : null);
            remoteBranchVoList.add(convert);
        });
        return remoteBranchVoList;
    }

    @Override
    public List<RemoteBranchVo> selectBranchList(RemoteBranchBo remoteBranchBo, boolean ignoreDataPermission) {
        if (ignoreDataPermission) {
            return DataPermissionHelper.ignore(() -> selectBranchList(remoteBranchBo));
        }
        return selectBranchList(remoteBranchBo);
    }

    @Override
    public Map<Long, Long> selectBranchIdMapByDeptIdList(List<Long> deptIdList,Boolean filterStopBranch) {
        return branchService.selectBranchIdMapByDeptIdList(deptIdList,filterStopBranch);
    }

    @Override
    public List<RemoteBranchVo> queryStaffBranchList(Long userId){
        List<BranchVo> branchVos = branchService.queryStaffBranchList(userId);
        return MapstructUtils.convert(branchVos, RemoteBranchVo.class);
    }

    @Override
    public RemotePayMerchantConfigVO getCommonPayConfig(Long branchId) {
        RemotePayMerchantConfig commonPayConfig = branchService.getCommonPayConfig(branchId);
        if(null == commonPayConfig){
            return null;
        }
        RemotePayMerchantConfigVO remotePayMerchantConfigVO = new RemotePayMerchantConfigVO();
        remotePayMerchantConfigVO.setMerchantName(commonPayConfig.getMerchantName());
        remotePayMerchantConfigVO.setAppId(commonPayConfig.getAppId());
        remotePayMerchantConfigVO.setAppName(commonPayConfig.getAppName());
        remotePayMerchantConfigVO.setPayType(commonPayConfig.getPayType());
        remotePayMerchantConfigVO.setPayCode(commonPayConfig.getPayCode());
        remotePayMerchantConfigVO.setWayCode(commonPayConfig.getWayCode());
        remotePayMerchantConfigVO.setDefaultMerchant(commonPayConfig.getDefaultMerchant());
        remotePayMerchantConfigVO.setConfigParamJson(commonPayConfig.getConfigParamJson());
        return remotePayMerchantConfigVO;
    }

    @Override
    public String getCommonPayAppId(Long branchId) {
        return branchService.getCommonPayAppId(branchId);
    }

    @Override
    public Boolean transfer(Long branchId, Integer amount) {
        //todo 分布式锁处理
        Branch branch = branchService.queryBranchById(branchId);
        if (ObjectUtils.isEmpty(branch)) {
            return false;
        }

        return branchService.transfer(branchId, amount);
    }
}
