package com.jxw.shufang.branch.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 积分记录对象 integral_record
 *
 *
 * @date 2024-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("integral_record")
public class IntegralRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分记录id
     */
    @TableId(value = "integral_record_id")
    private Long integralRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 变更类型（0增加 1减少）
     */
    private String changeType;

    /**
     * 变更值（都为正数，展示时根据变更类型进行正负显示）
     */
    private BigDecimal changeNum;

    /**
     * 变更方式（0自动 1手动）
     */
    private String changeState;

    /**
     * 变更原因（对应字典值，如课间奖励、季度奖励、课间惩罚等）
     */
    private String changeReason;

    /**
     * 变更备注
     */
    private String changeRemark;


    /**
     * 如果记录来自于任务，需记录此id
     */
    private Long taskId;


}
