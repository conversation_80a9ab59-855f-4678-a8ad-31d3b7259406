package com.jxw.shufang.branch.domain.bo;

import com.jxw.shufang.branch.domain.BranchControlStudent;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-17 06:06:11
 */
@Data
@AutoMapper(target = BranchControlStudent.class)
public class BranchControlStudentBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键Id不能为空", groups = {EditGroup.class})
    private Long branchControlStudentId;

    /**
     * 模板id
     */
    @NotNull(message = "管控功能模板Id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long branchFeatureTemplateId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 学生id
     */
    @NotNull(message = "学生id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long studentId;

    /**
     * 操作顾问id
     */
    @NotNull(message = "操作人Id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long operateId;

    /**
     * 操作顾问
     */
    @NotNull(message = "操作人名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String operateName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
