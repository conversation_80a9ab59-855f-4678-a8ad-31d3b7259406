package com.jxw.shufang.branch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.IntegralOrderOperate;
import com.jxw.shufang.branch.domain.bo.IntegralOrderOperateBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo;
import com.jxw.shufang.branch.mapper.IntegralOrderOperateMapper;
import com.jxw.shufang.branch.service.IIntegralOrderOperateService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）Service业务层处理
 *
 *
 * @date 2024-04-23
 */
@RequiredArgsConstructor
@Service
public class IntegralOrderOperateServiceImpl implements IIntegralOrderOperateService {

    private final IntegralOrderOperateMapper baseMapper;

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    @Override
    public IntegralOrderOperateVo queryById(Long integralOrderOperateId){
        return baseMapper.selectVoById(integralOrderOperateId);
    }

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    @Override
    public TableDataInfo<IntegralOrderOperateVo> queryPageList(IntegralOrderOperateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IntegralOrderOperate> lqw = buildQueryWrapper(bo);
        Page<IntegralOrderOperateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）列表
     */
    @Override
    public List<IntegralOrderOperateVo> queryList(IntegralOrderOperateBo bo) {
        LambdaQueryWrapper<IntegralOrderOperate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<IntegralOrderOperate> buildQueryWrapper(IntegralOrderOperateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IntegralOrderOperate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getIntegralOrderId() != null, IntegralOrderOperate::getIntegralOrderId, bo.getIntegralOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getIntegralOrderOperateStatus()), IntegralOrderOperate::getIntegralOrderOperateStatus, bo.getIntegralOrderOperateStatus());
        lqw.eq(bo.getPaymentIntegral() != null, IntegralOrderOperate::getPaymentIntegral, bo.getPaymentIntegral());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderOperateRemark()), IntegralOrderOperate::getOrderOperateRemark, bo.getOrderOperateRemark());
        return lqw;
    }

    /**
     * 新增积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    @Override
    public Boolean insertByBo(IntegralOrderOperateBo bo) {
        IntegralOrderOperate add = MapstructUtils.convert(bo, IntegralOrderOperate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setIntegralOrderOperateId(add.getIntegralOrderOperateId());
        }
        return flag;
    }

    /**
     * 修改积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    @Override
    public Boolean updateByBo(IntegralOrderOperateBo bo) {
        IntegralOrderOperate update = MapstructUtils.convert(bo, IntegralOrderOperate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IntegralOrderOperate entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除积分订单操作记录
（时间逆序取最后一条和积分订单中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
