package com.jxw.shufang.branch.factory;

import com.jxw.shufang.branch.enums.BranchConfigTypeEnum;
import com.jxw.shufang.branch.service.BranchConfigStrategy;
import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: cyj
 * @date: 2025/3/11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BranchConfigStrategyFactory {
    private final List<BranchConfigStrategy> strategyMap;


    public BranchConfigStrategy getConfigStrategy(Integer type) {
        return strategyMap.stream()
            .filter(item -> type.equals(item.getConfigType().getConfigType()))
            .findFirst()
            .orElseThrow(() -> new ServiceException("无匹配策略类型: " + type));
    }
}
