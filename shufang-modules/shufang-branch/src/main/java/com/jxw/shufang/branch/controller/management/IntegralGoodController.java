package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralGoodBo;
import com.jxw.shufang.branch.domain.vo.IntegralGoodVo;
import com.jxw.shufang.branch.service.IIntegralGoodService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分商品
 * 前端访问路由地址为:/branch/integralGood
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralGood")
public class IntegralGoodController extends BaseController {

    private final IIntegralGoodService integralGoodService;

    /**
     * 查询积分商品列表
     */
    @SaCheckPermission("branch:integralGood:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralGoodVo> list(IntegralGoodBo bo, PageQuery pageQuery) {
        if (LoginHelper.getBranchId()!=null){
            bo.setBranchId(LoginHelper.getBranchId());
        }
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }
        return integralGoodService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分商品列表
     */
    @SaCheckPermission("branch:integralGood:export")
    @Log(title = "积分商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IntegralGoodBo bo, HttpServletResponse response) {
        List<IntegralGoodVo> list = integralGoodService.queryList(bo);
        ExcelUtil.exportExcel(list, "积分商品", IntegralGoodVo.class, response);
    }

    /**
     * 获取积分商品详细信息
     *
     * @param integralGoodId 主键
     */
    @SaCheckPermission("branch:integralGood:query")
    @GetMapping("/{integralGoodId}")
    public R<IntegralGoodVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralGoodId) {
        return R.ok(integralGoodService.queryById(integralGoodId));
    }

    /**
     * 新增积分商品
     */
    @SaCheckPermission("branch:integralGood:add")
    @Log(title = "积分商品", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralGoodBo bo) {
        if (LoginHelper.getBranchId() == null) {
            return R.fail("非门店用户，无法新增积分商品");
        }
        bo.setBranchId(LoginHelper.getBranchId());
        if (LoginHelper.isBranchAdmin() && LoginHelper.getBranchId() == null) {
            return R.fail("门店管理员必须指定门店登录才能新增积分商品");
        }
        bo.setIntegralGoodStatus(UserConstants.INTEGRAL_GOOD_STATUS_DOWN); //商品状态（1上架 2下架）
        return toAjax(integralGoodService.insertByBo(bo));
    }

    /**
     * 修改积分商品
     */
    @SaCheckPermission("branch:integralGood:edit")
    @Log(title = "积分商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralGoodBo bo) {
        if (!LoginHelper.isBranchUser()) {
            return R.fail("非门店用户，无法修改积分商品");
        }
        if (bo.getBranchId() == null) {
            bo.setBranchId(LoginHelper.getBranchId());
        }
        if (LoginHelper.isBranchAdmin() && LoginHelper.getBranchId() == null) {
            return R.fail("门店管理员必须指定门店登录才能新增积分商品");
        }
        return toAjax(integralGoodService.updateByBo(bo));
    }

    /**
     * 删除积分商品
     *
     * @param integralGoodIds 主键串
     */
    @SaCheckPermission("branch:integralGood:remove")
    @Log(title = "积分商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralGoodIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralGoodIds) {
        if (!LoginHelper.isBranchUser()) {
            return R.fail("非门店用户，无法删除积分商品");
        }
        return toAjax(integralGoodService.deleteWithValidByIds(List.of(integralGoodIds), true));
    }
}
