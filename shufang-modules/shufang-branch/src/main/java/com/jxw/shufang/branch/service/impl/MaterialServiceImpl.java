package com.jxw.shufang.branch.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.domain.Material;
import com.jxw.shufang.branch.domain.bo.MaterialBo;
import com.jxw.shufang.branch.domain.vo.BranchVo;
import com.jxw.shufang.branch.domain.vo.MaterialVo;
import com.jxw.shufang.branch.enums.MaterialTypeEnum;
import com.jxw.shufang.branch.mapper.MaterialMapper;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.branch.service.IMaterialService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 物料Service业务层处理
 *
 *
 * @date 2024-02-21
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MaterialServiceImpl implements IMaterialService, BaseService {

    private final MaterialMapper baseMapper;


    private final IBranchService branchService;

    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 查询物料
     */
    @Override
    public MaterialVo queryById(Long materialId) {
        return baseMapper.selectVoById(materialId);
    }

    /**
     * 查询物料列表
     */
    @Override
    public TableDataInfo<MaterialVo> queryPageList(MaterialBo bo, PageQuery pageQuery) {
        QueryWrapper<Material> lqw = buildQueryWrapper(bo);
        Page<MaterialVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
        putMaterialFile(result.getRecords());
        return TableDataInfo.build(result);
    }


    public void putMaterialFile(List<MaterialVo> list){
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<MaterialVo> voList = list.stream().filter(item -> Objects.equals(item.getIsUrl(), "1") && StringUtils.isNotBlank(item.getMaterialFileIds())).toList();
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        String combinedMaterialFileIds = voList.stream()
            .map(MaterialVo::getMaterialFileIds)
            .flatMap(ids -> Arrays.stream(ids.split(",")))
            .distinct()
            .collect(Collectors.joining(","));
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIds(combinedMaterialFileIds);
        Map<Long, RemoteFile> remoteFileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, item -> item));
        for (MaterialVo vo : voList) {
            String[] ids = vo.getMaterialFileIds().split(",");
            List<RemoteFile> files = new ArrayList<>();
            for (String id : ids) {
                RemoteFile file = remoteFileMap.get(Long.valueOf(id));
                if (file != null) {
                    files.add(file);
                }
            }
            vo.setMaterialFileList(files);
        }

    }


    /**
     * 查询物料列表
     */
    @Override
    public List<MaterialVo> queryList(MaterialBo bo) {
        LambdaQueryWrapper<Material> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Material> buildLambdaQueryWrapper(MaterialBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Material> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchId() != null, Material::getBranchId, bo.getBranchId());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialType()), Material::getMaterialType, bo.getMaterialType());
        lqw.eq(StringUtils.isNotBlank(bo.getIsUrl()), Material::getIsUrl, bo.getIsUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialUrl()), Material::getMaterialUrl, bo.getMaterialUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialFileIds()), Material::getMaterialFileIds, bo.getMaterialFileIds());
        return lqw;
    }

    private QueryWrapper<Material> buildQueryWrapper(MaterialBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<Material> lqw = Wrappers.query(Material.class);
        lqw.eq(bo.getBranchId() != null, "m.branch_id", bo.getBranchId());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialType()), "m.material_type", bo.getMaterialType());
        lqw.eq(StringUtils.isNotBlank(bo.getIsUrl()), "m.is_url", bo.getIsUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialUrl()), "m.material_url", bo.getMaterialUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialFileIds()), "m.material_file_ids", bo.getMaterialFileIds());
        lqw.like(StringUtils.isNotBlank(bo.getBranchName()), "b.branch_name", bo.getBranchName());
        return lqw;
    }

    /**
     * 新增物料
     */
    @Override
    public Boolean insertByBo(MaterialBo bo) {
        Material add = MapstructUtils.convert(bo, Material.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMaterialId(add.getMaterialId());
        }
        return flag;
    }

    /**
     * 修改物料
     */
    @Override
    public Boolean updateByBo(MaterialBo bo) {
        Material update = MapstructUtils.convert(bo, Material.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Material entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除物料
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 店铺初始化物料
     */
    @Override
    public void branchInitMaterial(Long branchId) {
        //先判断有没有初始化过
        LambdaQueryWrapper<Material> lqw = Wrappers.lambdaQuery();
        lqw.eq(Material::getBranchId, branchId);
        boolean exists = baseMapper.exists(lqw);
        if (exists) {
            throw new ServiceException("店铺已经初始化过物料");
        }

        //获取店铺信息
        BranchVo branchVo = branchService.queryById(branchId);
        if (branchVo == null) {
            throw new ServiceException("店铺不存在");
        }


        //初始化物料
        for (MaterialTypeEnum item : MaterialTypeEnum.values()) {
            Material material = new Material();
            material.setBranchId(branchId);
            material.setMaterialType(item.getType());
            material.setCreateDept(branchVo.getCreateDept());
            baseMapper.insert(material);
        }
    }

    @Override
    public int clean(List<Long> materialIds) {
        return baseMapper.update(null,
            Wrappers.<Material>lambdaUpdate()
                .set(Material::getMaterialFileIds, null)
                .set(Material::getMaterialUrl, null)
                .set(Material::getIsUrl, null)
                .in(Material::getMaterialId, materialIds)
        );
    }

    @Override
    public MaterialVo getMaterialMenuInfo(Long branchId, String materialType) {
        LambdaQueryWrapper<Material> eq = Wrappers.lambdaQuery(Material.class)
            .eq(Material::getBranchId, branchId)
            .eq(Material::getMaterialType, materialType);
        return baseMapper.selectVoOne(eq);
    }

    @Cacheable(value = "material", key = "#materialId",condition = "#materialId != null")
    @Override
    public Material queryMaterialById(Long materialId) {
        return baseMapper.selectById(materialId);
    }

    @CacheEvict(value = "material",allEntries= true)
    public void cleanCache(){
        log.info("===========materialService cleanCache===========");
    }

    @Override
    public void init() {
        IMaterialService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========materialService init===========");
        LambdaQueryWrapper<Material> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Material::getMaterialId);
        List<Material> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========materialService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryMaterialById(item.getMaterialId());
        });
        log.info("===========materialService init end===========");
    }



}
