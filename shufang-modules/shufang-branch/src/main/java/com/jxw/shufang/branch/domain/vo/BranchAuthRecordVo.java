package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.BranchAuthRecord;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 分店授权记录
（时间逆序的最后一条记录和分店中的对应）视图对象 branch_auth_record
 *
 *
 * @date 2024-02-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchAuthRecord.class)
public class BranchAuthRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店授权记录id
     */
    @ExcelProperty(value = "分店授权记录id")
    private Long branchAuthRecordId;

    /**
     * 分店id
     */
    @ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 分店授权类型id
     */
    @ExcelProperty(value = "分店授权类型id")
    private Long branchAuthTypeId;

    /**
     * 分店授权名称
     */
    @ExcelProperty(value = "分店授权名称")
    private String branchAuthTypeName;

    /**
     * 分店授权描述
     */
    @ExcelProperty(value = "分店授权描述")
    private String branchAuthTypeInfo;

    /**
     * 分店授权天数
     */
    @ExcelProperty(value = "分店授权天数")
    private Long branchAuthTypeDays;

    /**
     * 分店授权费用
     */
    @ExcelProperty(value = "分店授权费用")
    private BigDecimal branchAuthTypeCost;

    /**
     * 分店授权开始时间
     */
    @ExcelProperty(value = "分店授权开始时间")
    private Date branchAuthStartTime;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 授权开始日期（无需理会，非数据库字段）
     */
    private String authStartTime;

    /**
     * 授权结束时间
     */
    private String authEndTime;

    /**
     * 授权状态,对应authStatus字典
     */
    private Integer authStatus;
}
