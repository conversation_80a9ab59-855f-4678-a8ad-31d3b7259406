package com.jxw.shufang.branch.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchMachineRegionVo;
import com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchMachineRegionVoConvertRemoteBranchMachineRegionVo extends BaseMapper<BranchMachineRegionVo, RemoteBranchMachineRegionVo> {


}
