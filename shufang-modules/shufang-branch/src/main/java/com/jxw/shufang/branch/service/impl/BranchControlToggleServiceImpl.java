package com.jxw.shufang.branch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxw.shufang.branch.domain.BranchControlToggle;
import com.jxw.shufang.branch.domain.BranchFeatureTemplate;
import com.jxw.shufang.branch.domain.bo.BranchBo;
import com.jxw.shufang.branch.domain.bo.BranchControlStudentBo;
import com.jxw.shufang.branch.domain.bo.BranchControlToggleBo;
import com.jxw.shufang.branch.domain.bo.BranchFeatureTemplateBo;
import com.jxw.shufang.branch.domain.vo.BranchControlStudentVo;
import com.jxw.shufang.branch.domain.vo.BranchFeatureTemplateVo;
import com.jxw.shufang.branch.mapper.BranchControlToggleMapper;
import com.jxw.shufang.branch.mapper.BranchFeatureTemplateMapper;
import com.jxw.shufang.branch.service.BranchControlStudentService;
import com.jxw.shufang.branch.service.BranchControlToggleService;
import com.jxw.shufang.branch.service.BranchFeatureTemplateService;
import com.jxw.shufang.branch.service.IBranchService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.web.core.BaseService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 门店管控开关表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16 11:38:30
 */
@Service
@RequiredArgsConstructor
public class BranchControlToggleServiceImpl implements BranchControlToggleService, BaseService {

    private final BranchControlToggleMapper branchControlToggleMapper;

    private final BranchFeatureTemplateMapper branchFeatureTemplateMapper;

    private final BranchFeatureTemplateService branchFeatureTemplateService;

    private final BranchControlStudentService branchControlStudentService;

    private final IBranchService branchService;

    @Override
    public List<BranchFeatureTemplateVo> queryBranchControlToggleByControlTypeAndBranchId(String controlType, Long branchId) {
        // 默认展示模板中的全部数据
        List<BranchFeatureTemplate> branchFeatureTemplateList = branchFeatureTemplateMapper.selectList(new LambdaQueryWrapper<BranchFeatureTemplate>()
            .eq(BranchFeatureTemplate::getControlType,controlType));
        // 查询出关联表的记录
        List<BranchControlToggle> branchControlToggles = branchControlToggleMapper.selectList(new LambdaQueryWrapper<BranchControlToggle>().eq(BranchControlToggle::getBranchId, branchId));
        // 比较如果分店管控关联表有记录，则覆盖默认状态
        Map<Long, BranchControlToggle> branchControlToggleMap = branchControlToggles.stream().collect(Collectors.toMap(BranchControlToggle::getBranchFeatureTemplateId, Function.identity()));

        branchFeatureTemplateList.forEach(branchFeatureTemplate -> {
            if (branchControlToggleMap.containsKey(branchFeatureTemplate.getBranchFeatureTemplateId())) {
                branchFeatureTemplate.setStatus(branchControlToggleMap.get(branchFeatureTemplate.getBranchFeatureTemplateId()).getStatus());
            }
        });
        return MapstructUtils.convert(branchFeatureTemplateList, BranchFeatureTemplateVo.class);
    }

    @Override
    public Boolean updateByBo(BranchControlToggleBo branchControlToggleBo) {
        BranchControlToggle branchControlToggle = MapstructUtils.convert(branchControlToggleBo, BranchControlToggle.class);
        if (branchControlToggle == null) {
            return false;
        }
        List<BranchFeatureTemplateVo> branchFeatureTemplateVos = branchFeatureTemplateService.queryList(new BranchFeatureTemplateBo());
        Map<Long, String> branchFeatureTemplateMap = branchFeatureTemplateVos.stream().collect(Collectors.toMap(BranchFeatureTemplateVo::getBranchFeatureTemplateId, BranchFeatureTemplateVo::getFeatureType));

        String featureType = branchFeatureTemplateMap.get(branchControlToggle.getBranchFeatureTemplateId());

        List<String> speedControl = List.of("SPEED_CONTROL", "PHOTO", "SELF_STUDY_SYSTEM", "STUDENT_SPEAKING", "SMART_PRIMARY_SECONDARY_SCHOOL");
        // 针对speedControl中的管控项做兼容旧版本管控
        if (speedControl.contains(featureType)){
            BranchBo branchBo = getBranchBo(branchControlToggleBo, featureType, branchControlToggle);
            branchService.updateBranchStatus(branchBo);
        }


        // 查询有没有这个记录，如果没有那么就新增一条
        BranchControlToggle branchControlToggleDo = branchControlToggleMapper.selectOne(new LambdaQueryWrapper<BranchControlToggle>()
            .eq(BranchControlToggle::getBranchId, branchControlToggle.getBranchId())
            .eq(BranchControlToggle::getBranchFeatureTemplateId, branchControlToggle.getBranchFeatureTemplateId()));
        if (branchControlToggleDo == null) {
            return branchControlToggleMapper.insert(branchControlToggle) > 0;
        } else {
            branchControlToggleDo.setStatus(branchControlToggle.getStatus());
            return branchControlToggleMapper.updateById(branchControlToggleDo) > 0;
        }

    }

    private static BranchBo getBranchBo(BranchControlToggleBo branchControlToggleBo, String featureType, BranchControlToggle branchControlToggle) {
        BranchBo branchBo = new BranchBo();
        branchBo.setBranchId(branchControlToggleBo.getBranchId());
        // 自学系统
        if ("SELF_STUDY_SYSTEM".equals(featureType)) {
            branchBo.setSelfStudySystemStatus(branchControlToggle.getStatus());
        }
        // 学生发言
        if ("STUDENT_SPEAKING".equals(featureType)) {
            branchBo.setStudentSpeakingStatus(branchControlToggle.getStatus());
        }
        // 智慧中小学
        if ("SMART_PRIMARY_SECONDARY_SCHOOL".equals(featureType)) {
            branchBo.setSmartPrimarySecondarySchoolStatus(branchControlToggle.getStatus());
        }

        // 播放倍速
        if ("SPEED_CONTROL".equals(featureType)) {
            branchBo.setSpeedControlStatus(branchControlToggle.getStatus());
        }
        // 拍照答疑
        if ("PHOTO".equals(featureType)) {
            branchBo.setPhotoStatus(branchControlToggle.getStatus());
        }
        return branchBo;
    }

    @Override
    public List<BranchFeatureTemplateVo> queryBranchControlToggleByStudentIdAndBranchId(Long studentId, Long branchId) {
        List<BranchFeatureTemplateVo> branchFeatureTemplateList = branchFeatureTemplateService.queryList(null);

        List<BranchControlToggle> branchControlToggles = branchControlToggleMapper.selectList(new LambdaQueryWrapper<BranchControlToggle>().eq(BranchControlToggle::getBranchId, branchId));

        // 学生拥有白名单的管控列表
        List<Long> branchFeatureTemplateIds;
        if (studentId != null) {
            BranchControlStudentBo branchControlStudentBo = new BranchControlStudentBo();
            branchControlStudentBo.setStudentId(studentId);
            branchControlStudentBo.setBranchId(branchId);
            List<BranchControlStudentVo> branchControlStudentVoList = branchControlStudentService.queryList(branchControlStudentBo);
            branchFeatureTemplateIds = branchControlStudentVoList.stream().map(BranchControlStudentVo::getBranchFeatureTemplateId).toList();
        } else {
            branchFeatureTemplateIds = Collections.emptyList();
        }
        // 比较如果分店管控关联表有记录，则覆盖默认状态
        Map<Long, BranchControlToggle> branchControlToggleMap = branchControlToggles.stream().collect(Collectors.toMap(BranchControlToggle::getBranchFeatureTemplateId, Function.identity()));

        branchFeatureTemplateList.forEach(branchFeatureTemplate -> {
            if (branchControlToggleMap.containsKey(branchFeatureTemplate.getBranchFeatureTemplateId())) {
                branchFeatureTemplate.setStatus(branchControlToggleMap.get(branchFeatureTemplate.getBranchFeatureTemplateId()).getStatus());
            }
            // 设置学生管控的状态，如果存在这个模板ID，说明这个模板添加了学生白名单
            branchFeatureTemplate.setStudentControlStatus(branchFeatureTemplateIds.contains(branchFeatureTemplate.getBranchFeatureTemplateId()) ? 1 : 0);
        });

        return branchFeatureTemplateList;
    }
}
