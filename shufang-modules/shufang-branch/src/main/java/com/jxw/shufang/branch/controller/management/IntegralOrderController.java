package com.jxw.shufang.branch.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.branch.domain.bo.IntegralOrderBo;
import com.jxw.shufang.branch.domain.vo.IntegralOrderVo;
import com.jxw.shufang.branch.service.IIntegralOrderService;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分订单
 * 前端访问路由地址为:/branch/integralOrder
 *
 *
 * @date 2024-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/integralOrder")
public class IntegralOrderController extends BaseController {

    private final IIntegralOrderService integralOrderService;

    /**
     * 查询积分订单列表
     */
    @SaCheckPermission("branch:integralOrder:list")
    @GetMapping("/list")
    public TableDataInfo<IntegralOrderVo> list(IntegralOrderBo bo, PageQuery pageQuery) {
        //integralOrderBo.setStudentId(LoginHelper.getStudentId());
        //integralOrderBo.setIntegralOrderOperateStatusList(List.of(IntegralOrderStatusEnum.EXCHANGED.getCode(), IntegralOrderStatusEnum.WAIT_EXCHANGE.getCode()));
        bo.setOrderBy("t.create_time desc");
        return integralOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出积分订单列表
     */
    @SaCheckPermission("branch:integralOrder:export")
    @Log(title = "积分订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IntegralOrderBo bo, HttpServletResponse response) {
        List<IntegralOrderVo> list = integralOrderService.queryOrderList(bo);
        ExcelUtil.exportExcel(list, "积分订单", IntegralOrderVo.class, response);
    }

    /**
     * 获取积分订单详细信息
     *
     * @param integralOrderId 主键
     */
    @SaCheckPermission("branch:integralOrder:query")
    @GetMapping("/{integralOrderId}")
    public R<IntegralOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long integralOrderId) {
        return R.ok(integralOrderService.queryById(integralOrderId));
    }

    /**
     * 新增积分订单
     */
    @SaCheckPermission("branch:integralOrder:add")
    @Log(title = "积分订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IntegralOrderBo bo) {
        return toAjax(integralOrderService.insertByBo(bo));
    }

    /**
     * 修改积分订单
     */
    @SaCheckPermission("branch:integralOrder:edit")
    @Log(title = "积分订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IntegralOrderBo bo) {
        return toAjax(integralOrderService.updateByBo(bo));
    }

    /**
     * 删除积分订单
     *
     * @param integralOrderIds 主键串
     */
    @SaCheckPermission("branch:integralOrder:remove")
    @Log(title = "积分订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{integralOrderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] integralOrderIds) {
        return toAjax(integralOrderService.deleteWithValidByIds(List.of(integralOrderIds), true));
    }


    /**
     * 核销
     */
    @SaCheckPermission("branch:integralOrderOperate:edit")
    @Log(title = "积分订单操作，核销", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/verify")
    public R<Void> verify(@NotNull(message = "主键不能为空") Long integralOrderId) {
        integralOrderService.verify(integralOrderId);
        return R.ok();
    }

    /**
     * 取消订单
     */
    @SaCheckPermission("branch:integralOrderOperate:edit")
    @Log(title = "积分订单操作，取消订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancel")
    public R<Void> cancel(@NotNull(message = "主键不能为空") Long integralOrderId) {
        integralOrderService.cancel(integralOrderId);
        return R.ok();
    }

}
