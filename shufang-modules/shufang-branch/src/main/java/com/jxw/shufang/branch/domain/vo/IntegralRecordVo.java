package com.jxw.shufang.branch.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.domain.IntegralRecord;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 积分记录视图对象 integral_record
 *
 *
 * @date 2024-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = IntegralRecord.class)
public class IntegralRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 积分记录id
     */
    @ExcelProperty(value = "积分记录id")
    private Long integralRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 变更类型（0增加 1减少）
     */
    @ExcelProperty(value = "变更类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=增加,1=减少")
    private String changeType;

    /**
     * 变更值（都为正数，展示时根据变更类型进行正负显示）
     */
    @ExcelProperty(value = "变更值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "都=为正数，展示时根据变更类型进行正负显示")
    private BigDecimal changeNum;

    /**
     * 变更方式（0自动 1手动）
     */
    @ExcelProperty(value = "变更方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=自动,1=手动")
    private String changeState;

    /**
     * 变更原因（对应字典值，如课间奖励、季度奖励、课间惩罚等）
     */
    @ExcelProperty(value = "变更原因", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如课间奖励、季度奖励、课间惩罚等")
    private String changeReason;

    /**
     * 变更备注
     */
    @ExcelProperty(value = "变更备注")
    private String changeRemark;

    /**
     * 如果记录来自于任务，需记录此id
     */
    private Long taskId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 学生积分余额
     */
    private BigDecimal studentIntegral;

    private String displayStr;



}
