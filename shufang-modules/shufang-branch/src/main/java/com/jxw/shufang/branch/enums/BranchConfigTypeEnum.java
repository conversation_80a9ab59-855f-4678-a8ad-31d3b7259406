package com.jxw.shufang.branch.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Getter
@AllArgsConstructor
public enum BranchConfigTypeEnum {

    INTRODUCE_PREFERENTIAL_AMOUNT(1, "转介绍优惠额"),
    PAYMENT_FEE_MODE(2,"缴费模式"),
    //作答模式
    ANSWER_MODE(3, "作答模式");

    private final Integer configType;
    private final String configName;

    public static BranchConfigTypeEnum getByType(int type) {
        return Arrays.stream(values())
            .filter(e -> e.configType == type)
            .findFirst()
            .orElse(null);
    }
}
