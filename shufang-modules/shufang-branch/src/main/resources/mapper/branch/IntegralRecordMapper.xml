<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.IntegralRecordMapper">

    <select id="getIntegralByStudentId" resultType="java.math.BigDecimal">
        SELECT
            SUM(IF(change_type = 0,IFNULL(change_num,0),-IFNULL(change_num,0)))
        FROM
            integral_record
        where student_id = #{studentId}
        group by student_id
    </select>
    <select id="getIntegralByStudentIdList" resultType="com.jxw.shufang.branch.domain.vo.IntegralRecordVo">
        SELECT student_id,
               SUM(IF(change_type = 0, IFNULL(change_num, 0), -IFNULL(change_num, 0))) as `student_integral`
        FROM integral_record
        where student_id in
        <foreach collection="studentIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by student_id
    </select>
</mapper>
