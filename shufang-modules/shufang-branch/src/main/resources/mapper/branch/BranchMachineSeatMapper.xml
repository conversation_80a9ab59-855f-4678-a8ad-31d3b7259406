<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.BranchMachineSeatMapper">

    <resultMap id="branchMachineSeatResult" type="com.jxw.shufang.branch.domain.vo.BranchMachineSeatVo">
        <id property="branchMachineSeatId" column="branch_machine_seat_id"/>
        <association property="branchMachineRegion" resultMap="branchMachineRegionResult" column="branch_machine_region_id"/>
    </resultMap>

    <resultMap id="branchMachineRegionResult" type="com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo">
        <id property="branchMachineRegionId" column="branch_machine_region_id"/>
    </resultMap>

    <select id="selectSeatList" resultMap="branchMachineSeatResult">
        select t.branch_machine_seat_id,
               t.branch_machine_region_id,
               t.student_id,
               t.seat_no,
               t.use_start_time,
               t.use_end_time,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               r.branch_machine_region_id,
               r.branch_id,
               r.region_name,
               r.region_num,
               r.region_img,
               r.del_flag
        from branch_machine_seat t
                 LEFT JOIN branch_machine_region r ON t.branch_machine_region_id = r.branch_machine_region_id
            ${ew.getCustomSqlSegment}
    </select>
</mapper>
