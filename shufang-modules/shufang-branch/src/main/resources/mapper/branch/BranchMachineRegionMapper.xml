<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.BranchMachineRegionMapper">

    <resultMap id="BranchMachineRegionResult" type="com.jxw.shufang.branch.domain.vo.BranchMachineRegionVo">

    </resultMap>

    <select id="selectRegionList" resultMap="BranchMachineRegionResult">
        select t.branch_machine_region_id,
               t.branch_id,
               t.region_name,
               t.region_num,
               t.region_img,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag
        from branch_machine_region t
            ${ew.getCustomSqlSegment}
    </select>
    <select id="selectMachineRegionPage" resultMap="BranchMachineRegionResult">
        select t.branch_machine_region_id,
               t.branch_id,
               t.region_name,
               t.region_num,
               t.region_img,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag
        from branch_machine_region t
            ${ew.getCustomSqlSegment}
    </select>
</mapper>
