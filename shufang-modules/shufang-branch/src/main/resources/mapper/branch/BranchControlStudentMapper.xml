<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.BranchControlStudentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.branch.domain.BranchControlStudent">
        <id column="branch_control_student_id" property="branchControlStudentId" />
        <result column="branch_feature_template_id" property="branchFeatureTemplateId" />
        <result column="branch_id" property="branchId" />
        <result column="student_id" property="studentId" />
        <result column="operate_id" property="operateId" />
        <result column="operate_name" property="operateName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        branch_control_student_id, branch_feature_template_id, branch_id, student_id, operate_id, operate_name, create_time, update_time
    </sql>
    <select id="selectQueryPageList" resultType="com.jxw.shufang.branch.domain.vo.BranchControlStudentVo">
        select bct.*, s.student_name
        from branch_control_student bct
        left join student s on bct.student_id = s.student_id
        <where>
            <if test="branchFeatureTemplateId != null">
                and bct.branch_feature_template_id = #{branchFeatureTemplateId}
            </if>
            <if test="branchId != null">
                and bct.branch_id = #{branchId}
            </if>
        </where>
    </select>

</mapper>
