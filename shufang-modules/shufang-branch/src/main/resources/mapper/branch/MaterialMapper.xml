<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.MaterialMapper">
    <resultMap id="MaterialResult" type="com.jxw.shufang.branch.domain.vo.MaterialVo">
        <id property="materialId" column="material_id"/>
        <result property="branchId" column="branch_id"/>
        <association property="branch" column="branch_id" resultMap="BranchResult"/>
    </resultMap>

    <resultMap id="BranchResult" type="com.jxw.shufang.branch.domain.vo.BranchVo">

    </resultMap>

    <select id="selectPageList" resultMap="MaterialResult">
        SELECT m.*, b.branch_name
        FROM material m
        LEFT JOIN branch b ON m.branch_id = b.branch_id
        ${ew.getCustomSqlSegment}
    </select>
</mapper>
