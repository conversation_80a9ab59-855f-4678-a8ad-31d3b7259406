<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.IntegralOrderMapper">
    <!-- 多结构嵌套自动映射需带上每个实体的主键id 否则映射会失败 -->
    <resultMap type="com.jxw.shufang.branch.domain.vo.IntegralOrderVo" id="orderResult">
        <id property="integralOrderId" column="integral_order_id"/>
        <!--要加上这一行，不然下面的branchAuthRecordResult会把id吞掉-->
        <result property="integralOrderOperateId" column="integral_order_operate_id"/>
        <association property="integralOrderOperate" column="integral_order_operate_id" resultMap="orderOperateResult"/>
        <association property="integralOrderInfo" column="integral_order_id" resultMap="orderInfoResult"/>
    </resultMap>

    <!-- 多结构嵌套自动映射需带上每个实体的主键id 否则映射会失败 -->
    <resultMap type="com.jxw.shufang.branch.domain.vo.IntegralOrderOperateVo" id="orderOperateResult">
        <id property="integralOrderOperateId" column="integral_order_operate_id"/>
        <result property="integralOrderId" column="integral_order_id"/>
        <result property="createBy" column="operate_create_by"/>
        <result property="createTime" column="operate_create_time"/>
    </resultMap>

    <resultMap type="com.jxw.shufang.branch.domain.vo.IntegralOrderInfoVo" id="orderInfoResult">
        <id property="integralOrderInfoId" column="integral_order_info_id"/>
        <result property="integralOrderId" column="integral_order_id"/>
    </resultMap>


    <select id="queryOrderList" resultMap="orderResult">
        select t.integral_order_id,
               t.student_id,
               t.consultant_id,
               t.integral_order_no,
               t.integral_order_operate_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               ioi.integral_order_info_id,
               ioi.integral_good_id,
               ioi.integral_good_img,
               ioi.integral_good_name,
               ioi.integral_good_cost,
               ioi.integral_good_type,
               ioi.integral_good_desc,
               ioi.create_by as info_create_by,
               ioi.create_time as info_create_time,
               ioi.create_dept as info_create_dept,
               ioi.update_by as info_update_by,
               ioi.update_time as info_update_time,
               ioop.integral_order_operate_id,
               ioop.integral_order_operate_status,
               ioop.payment_integral,
               ioop.order_operate_remark,
               ioop.create_by as operate_create_by,
               ioop.create_time as operate_create_time,
               ioop.create_dept as operate_create_dept,
               ioop.update_by as operate_update_by,
               ioop.update_time as operate_update_time
        from integral_order t
                 left join integral_order_info ioi on ioi.integral_order_id = t.integral_order_id
                 left join integral_order_operate ioop on ioop.integral_order_operate_id = t.integral_order_operate_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="queryOrderInfoPage" resultMap="orderResult">
        select t.integral_order_id,
               t.student_id,
               t.consultant_id,
               t.integral_order_no,
               t.integral_order_operate_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               ioi.integral_order_info_id,
               ioi.integral_good_id,
               ioi.integral_good_img,
               ioi.integral_good_name,
               ioi.integral_good_cost,
               ioi.integral_good_type,
               ioi.integral_good_desc,
               ioi.create_by as info_create_by,
               ioi.create_time as info_create_time,
               ioi.create_dept as info_create_dept,
               ioi.update_by as info_update_by,
               ioi.update_time as info_update_time,
               ioop.integral_order_operate_id,
               ioop.integral_order_operate_status,
               ioop.payment_integral,
               ioop.order_operate_remark,
               ioop.create_by as operate_create_by,
               ioop.create_time as operate_create_time,
               ioop.create_dept as operate_create_dept,
               ioop.update_by as operate_update_by,
               ioop.update_time as operate_update_time
        from integral_order t
                 left join integral_order_info ioi on ioi.integral_order_id = t.integral_order_id
                 left join integral_order_operate ioop on ioop.integral_order_operate_id = t.integral_order_operate_id
            ${ew.getCustomSqlSegment}
    </select>
</mapper>
