<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.branch.mapper.BranchMapper">

    <!-- 多结构嵌套自动映射需带上每个实体的主键id 否则映射会失败 -->
    <resultMap type="com.jxw.shufang.branch.domain.vo.BranchVo" id="branchResult">
        <id property="branchId" column="branch_id"/>
        <!--要加上这一行，不然下面的branchAuthRecordResult会把id吞掉-->
        <result property="branchAuthRecordId" column="branch_auth_record_id"/>
        <association property="branchAuthType" column="branch_auth_type_id" resultMap="branchAuthTypeResult"/>
        <association property="branchAuthRecord" column="branch_auth_record_id" resultMap="branchAuthRecordResult"/>
    </resultMap>

    <resultMap id="branchAuthTypeResult" type="com.jxw.shufang.branch.domain.vo.BranchAuthTypeVo">
    </resultMap>

    <resultMap id="branchAuthRecordResult" type="com.jxw.shufang.branch.domain.vo.BranchAuthRecordVo">
    </resultMap>


    <select id="queryUnexpiredByAuthTypeIds" resultMap="branchResult">
        SELECT b.*, bar.branch_auth_type_name,bar.branch_auth_type_id
        FROM branch b
        JOIN branch_auth_record bar ON b.branch_auth_record_id = bar.branch_auth_record_id
        WHERE now() &lt; DATE_ADD(bar.branch_auth_start_time, INTERVAL bar.branch_auth_type_days DAY)
        AND branch_status = 0
        AND bar.branch_auth_type_id IN
        <foreach collection="authTypeIds" item="authTypeId" open="(" separator="," close=")">
            #{authTypeId}
        </foreach>
    </select>

    <select id="selectPageBranchList" resultMap="branchResult">
        SELECT b.*,
        DATE(bar.branch_auth_start_time) auth_start_time,
        DATE_ADD(DATE(bar.branch_auth_start_time), INTERVAL bar.branch_auth_type_days DAY) auth_end_time,
        IF(DATE(now()) &lt;= DATE_ADD(DATE(bar.branch_auth_start_time), INTERVAL bar.branch_auth_type_days DAY), 0, 1) AS auth_status,
        bar.branch_auth_type_name,
        bar.branch_auth_type_id,
        bar.branch_auth_type_days
        FROM branch b
        LEFT JOIN branch_auth_record bar ON b.branch_auth_record_id = bar.branch_auth_record_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectBranchList" resultMap="branchResult">
        SELECT b.*,
               DATE(bar.branch_auth_start_time) auth_start_time,
               DATE_ADD(DATE(bar.branch_auth_start_time), INTERVAL bar.branch_auth_type_days DAY) auth_end_time,
               IF(DATE(now()) &lt;= DATE_ADD(DATE(bar.branch_auth_start_time), INTERVAL bar.branch_auth_type_days DAY), 0, 1) AS auth_status,
               bar.branch_auth_type_name,
               bar.branch_auth_type_id,
               bar.branch_auth_type_days
        FROM branch b
                 LEFT JOIN branch_auth_record bar ON b.branch_auth_record_id = bar.branch_auth_record_id
            ${ew.getCustomSqlSegment}
    </select>
    <update id="updateMoney" >
        UPDATE branch SET remain_time = remain_time +  #{amount} WHERE `branch_id` = #{branchId}
        <if test="amount &lt; 0">
            AND remain_time &gt;= ABS(#{amount})
        </if>
    </update>
    <select id="selectBranchIdList" resultType="com.jxw.shufang.branch.domain.vo.BranchVo">
        select b.branch_id
        from branch b ${ew.getCustomSqlSegment}
    </select>

    <select id="selectByCreateDept" resultType="com.jxw.shufang.branch.domain.vo.BranchVo">
        select * from branch
            where create_dept in (
                select dept_id from sys_dept where FIND_IN_SET(#{deptId}, ancestors) and is_store = 1
        )
    </select>

</mapper>
