#FROM findepi/graalvm:java17-native
FROM registry.cn-shanghai.aliyuncs.com/jxw-midsoftware/jxwopenjdk:17.0.6

MAINTAINER Lion Li

RUN mkdir -p /jxw/gen/logs

WORKDIR /jxw/gen

ENV SERVER_PORT=9202 LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Ddubbo.network.interface.preferred=eth0"

EXPOSE ${SERVER_PORT}

ADD ./target/shufang-gen.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -Dserver.port=${SERVER_PORT} -jar app.jar ${JAVA_OPTS}
