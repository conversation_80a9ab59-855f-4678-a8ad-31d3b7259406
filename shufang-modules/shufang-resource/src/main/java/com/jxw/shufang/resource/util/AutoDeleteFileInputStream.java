package com.jxw.shufang.resource.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;

/**
 * <AUTHOR>
 * @Date 2025/3/12 16:09
 * @Version 1
 * @Description 文件流关闭自动删除文件
 */
public class AutoDeleteFileInputStream extends FileInputStream {
    private final File file;

    public AutoDeleteFileInputStream(File file) throws FileNotFoundException {
        super(file);
        this.file = file;
    }

    @Override
    public void close() throws IOException {
        try {
            super.close();
        } finally {
            Files.deleteIfExists(file.toPath());
        }
    }
}
