package com.jxw.shufang.resource.service;

import com.jxw.shufang.common.core.enums.SmsCaptchaResultEnum;
import com.jxw.shufang.resource.domain.vo.SysSmsVo;

public interface ISysSmsService {

    /**
     * 发送短信captcha
     *
     * @param phone 电话
     * @param code  密码
     * @date 2024/04/18 06:42:00
     */
    SysSmsVo sendSmsCaptcha(String phone, String code);


    /**
     * 发送短信captcha
     *
     * @param phone 电话
     * @date 2024/04/20 02:08:25
     */
    SysSmsVo sendSmsCaptcha(String phone);


    /**
     * 校验验证码
     *
     * @param phone 电话
     * @param code  验证码
     * @return 结果
     */

    SmsCaptchaResultEnum checkSmsCaptcha(String phone, String code,boolean needDelSmsCaptcha);


    void deleteSmsCaptcha(String phone);
}
