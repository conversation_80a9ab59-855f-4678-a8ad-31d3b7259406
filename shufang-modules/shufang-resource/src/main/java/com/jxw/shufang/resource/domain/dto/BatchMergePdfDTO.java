package com.jxw.shufang.resource.domain.dto;

import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/2/21 14:00
 * @Version 1
 * @Description
 */
@Data
public class BatchMergePdfDTO {
    /**
     * 批次ID，相同的id会放一起合并处理
     */
    private String batchId;

    /**
     * 文件类型
     */
    private KnowledgeResourceType resourceType;

    /**
     * 文件下载路径
     */
    private String filePath;

    /**
     * 水印
     */
    private String watermark;

    /**
     * 首行添加的文字
     */
    private String firstLineText;

    /**
     * 二维码base64字符串
     */
    private String qrcodeBase64Str;

    /**
     * 合并的水印字号
     */
    private Integer fontSize;

    /**
     * 水平间距
     */
    private Float horizontalSpacing;

    /**
     * 垂直间距
     */
    private Float verticalSpacing;
}
