package com.jxw.shufang.resource.dubbo;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.oss.core.OssClient;
import com.jxw.shufang.common.oss.entity.UploadResult;
import com.jxw.shufang.common.oss.factory.OssFactory;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.resource.domain.bo.SysOssBo;
import com.jxw.shufang.resource.domain.vo.SysOssVo;
import com.jxw.shufang.resource.service.ISysOssService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 文件请求处理
 *

 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteFileServiceImpl implements RemoteFileService {

    private final ISysOssService sysOssService;

    /**
     * 文件上传请求
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public RemoteFile upload(String name, String originalFilename, String contentType, byte[] file) throws ServiceException {
        try {
            String suffix = StringUtils.substring(originalFilename, originalFilename.lastIndexOf("."), originalFilename.length());
            OssClient storage = OssFactory.instance();
            UploadResult uploadResult = storage.uploadSuffix(file, suffix, contentType);
            // 保存文件信息
            SysOssBo oss = new SysOssBo();
            oss.setUrl(uploadResult.getUrl());
            oss.setFileSuffix(suffix);
            oss.setFileName(uploadResult.getFilename());
            oss.setOriginalName(originalFilename);
            oss.setService(storage.getConfigKey());
            sysOssService.insertByBo(oss);
            RemoteFile sysFile = new RemoteFile();
            sysFile.setOssId(oss.getOssId());
            sysFile.setName(uploadResult.getFilename());
            sysFile.setUrl(uploadResult.getUrl());
            return sysFile;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new ServiceException("上传文件失败");
        }
    }

    /**
     * 通过ossId查询对应的url
     *
     * @param ossIds ossId串逗号分隔
     * @return url串逗号分隔
     */
    @Override
    public String selectUrlByIds(String ossIds) {
        return sysOssService.selectUrlByIds(ossIds);
    }

    @Override
    public String selectNameById(String ossId) {
        return sysOssService.selectNameById(ossId);
    }
    @Override
    public String selectUrlByIds(String ossIds,Integer second) {
        return sysOssService.selectUrlByIdsLimitTime(ossIds,second);
    }


    @Override
    public List<RemoteFile> selectFileByIds(String ossIds) {
        if (StringUtils.isEmpty(ossIds)) {
            return List.of();
        }
        List<Long> ids = Arrays.stream(ossIds.split(",")).map(Long::valueOf).toList();
        List<SysOssVo> sysOssVos = sysOssService.listByIds(ids);
        return MapstructUtils.convert(sysOssVos, RemoteFile.class);
    }

    @Override
    public List<RemoteFile> selectFileByIdList(List<Long> ossIdList) {
        if (CollUtil.isEmpty(ossIdList)){
            return List.of();
        }
        List<SysOssVo> sysOssVos = sysOssService.listByIds(ossIdList);
        return MapstructUtils.convert(sysOssVos, RemoteFile.class);
    }

}
