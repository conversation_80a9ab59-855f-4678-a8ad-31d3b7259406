package com.jxw.shufang.resource.service.impl;

import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import com.jxw.shufang.resource.domain.dto.MergeCourseResourceDTO;
import com.jxw.shufang.resource.service.CourseResourceService;
import com.jxw.shufang.student.api.RemoteStudentCourseResourceService;
import com.jxw.shufang.student.api.RemoteStudyPlanningService;
import com.jxw.shufang.student.api.domain.bo.DownloadMergeResourceRequest;
import com.jxw.shufang.student.api.domain.dto.RemoteCourseWithTopCourseDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteMergeCourseResourceDTO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudyPlanningRecordVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/26 19:56
 * @Version 1
 * @Description
 */
@Service
@RequiredArgsConstructor
public class CourseResourceServiceImpl implements CourseResourceService, BaseService {

    @DubboReference
    private RemoteStudentCourseResourceService remoteStudentCourseResourceService;
    @DubboReference
    private RemoteExtResourceService resourceService;
    @DubboReference
    private RemoteStudyPlanningService studyPlanningService;

    @Override
    public List<RemoteMergeCourseResourceDTO> getMergeCourseResource(DownloadMergeResourceRequest mergePdfBO) throws ServiceException {
        Long studentId = mergePdfBO.getStudentId();
        List<Long> courseIds = mergePdfBO.getCourseIds();
        List<String> resourceTypes = mergePdfBO.getResourceTypes();
        List<Long> studyPlanningRecordId = mergePdfBO.getStudyPlanningRecordIds();

        // 获取課程和知识点资源
        Map<Long, RemoteCourseWithTopCourseDTO> courseMap = remoteStudentCourseResourceService.getMergeCourseResource(studentId,
            courseIds, resourceTypes);
        Map<Long, RemoteStudyPlanningRecordVo> planningRecordMap = getCoursePlanningRecordMap(studyPlanningRecordId);
        Map<Long, List<RemoteGroupResourceVo>> knowledgeResourceOssUrl = this.getKnowledgeResourceOssUrl(resourceTypes, courseMap);
        List<MergeCourseResourceDTO> courseResource = courseMap.values().stream()
            .map(course -> this.buildMergeCourseResourceDTO(mergePdfBO, knowledgeResourceOssUrl, course, planningRecordMap.get(course.getCourseId())))
            .toList();

        return courseResource.stream()
            .map(this::convertToMergeCourseResourceDTO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    private Map<Long, RemoteStudyPlanningRecordVo> getCoursePlanningRecordMap(List<Long> studyPlanningRecordId) {
        List<RemoteStudyPlanningRecordVo> remoteStudyRecordVoList = studyPlanningService.queryPlanRecordListByIds(studyPlanningRecordId);
        if (CollectionUtils.isEmpty(remoteStudyRecordVoList)) {
            return Collections.emptyMap();
        }
        return remoteStudyRecordVoList.stream()
            .collect(Collectors.toMap(RemoteStudyPlanningRecordVo::getCourseId, Function.identity(), (v1, v2) -> v2));
    }

    private Map<Long, List<RemoteGroupResourceVo>> getKnowledgeResourceOssUrl(List<String> resourceTypes,
                                                                              Map<Long, RemoteCourseWithTopCourseDTO> courseMap) {
        List<Long> knowledgeIds = courseMap.values().stream()
            .map(RemoteCourseWithTopCourseDTO::getKnowledgeId)
            .collect(Collectors.toList());

        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeIdList(knowledgeIds);
        remoteKnowledgeResourceBo.setTypeList(resourceTypes);
        List<RemoteGroupResourceVo> knowledgeResourceList = resourceService.getKnowledgeResourceList(remoteKnowledgeResourceBo);
        return this.filterInvalidateResourceUrl(knowledgeResourceList);
    }

    private MergeCourseResourceDTO buildMergeCourseResourceDTO(DownloadMergeResourceRequest mergePdfBO,
                                                               Map<Long, List<RemoteGroupResourceVo>> knowledgeResourceOssUrl,
                                                               RemoteCourseWithTopCourseDTO course,
                                                               RemoteStudyPlanningRecordVo studyRecordVo) {
        Long knowledgeId = course.getKnowledgeId();
        Long courseId = course.getCourseId();
        MergeCourseResourceDTO mergeCourseResourceDTO = new MergeCourseResourceDTO();
        mergeCourseResourceDTO.setStudentId(mergePdfBO.getStudentId());
        mergeCourseResourceDTO.setCourseId(courseId);
        mergeCourseResourceDTO.setKnowledgeId(knowledgeId);
        mergeCourseResourceDTO.setCourseName(course.getStudyContent());
        mergeCourseResourceDTO.setSubject(course.getSubject());
        mergeCourseResourceDTO.setCourseResourceOssUrl(knowledgeResourceOssUrl.get(knowledgeId));
        mergeCourseResourceDTO.setStudyPlanningRecordId(null == studyRecordVo.getStudyPlanningRecordId() ? null : studyRecordVo.getStudyPlanningRecordId());
        return mergeCourseResourceDTO;
    }

    private Map<Long, List<RemoteGroupResourceVo>> filterInvalidateResourceUrl(List<RemoteGroupResourceVo> knowledgeResourceList) {
        if (CollectionUtils.isEmpty(knowledgeResourceList)) {
            throw new ServiceException("当前课程无有效资源可下载");
        }
        String fileType = "pdf";
        Map<Long, List<RemoteGroupResourceVo>> result = knowledgeResourceList.stream()
            .filter(f -> null != f.getKnowledgeResource())
            .filter(f -> fileType.equals(f.getKnowledgeResource().getFormat()))
            .collect(Collectors.groupingBy(
                RemoteGroupResourceVo::getKnowledgeId));

        if (result.isEmpty()) {
            throw new ServiceException("课程无有效资源可下载");
        }
        return result;
    }

    private RemoteMergeCourseResourceDTO convertToMergeCourseResourceDTO(MergeCourseResourceDTO resourceDTO) {
        RemoteMergeCourseResourceDTO remoteMergeCourseResourceDTO = new RemoteMergeCourseResourceDTO();
        remoteMergeCourseResourceDTO.setStudentId(resourceDTO.getStudentId());
        remoteMergeCourseResourceDTO.setCourseId(resourceDTO.getCourseId());
        remoteMergeCourseResourceDTO.setCourseName(resourceDTO.getCourseName());
        List<RemoteGroupResourceVo> resourceOssUrl = resourceDTO.getCourseResourceOssUrl();
        if(CollectionUtils.isEmpty(resourceOssUrl)){
            return null;
        }
        Map<KnowledgeResourceType, String> courseResourceOssUrlMap = new HashMap<>();
        for (RemoteGroupResourceVo resourceVo : resourceOssUrl) {
            courseResourceOssUrlMap.put(resourceVo.getType(), resourceVo.getKnowledgeResource().getUrl());
        }
        remoteMergeCourseResourceDTO.setCourseResourceOssUrlMap(courseResourceOssUrlMap);
        remoteMergeCourseResourceDTO.setKnowledgeId(resourceDTO.getKnowledgeId());
        remoteMergeCourseResourceDTO.setSubject(resourceDTO.getSubject());
        remoteMergeCourseResourceDTO.setStudyPlanningRecordId(resourceDTO.getStudyPlanningRecordId());
        return remoteMergeCourseResourceDTO;
    }
}
