package com.jxw.shufang.resource.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.resource.domain.dto.StudyReviewQrCodeParamDTO;
import com.jxw.shufang.resource.service.MergeCourseResourceService;
import com.jxw.shufang.resource.service.MergePdfService;
import com.jxw.shufang.resource.service.PdfMergeProcess;
import com.jxw.shufang.resource.util.AutoDeleteFileInputStream;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.dto.RemoteMergeCourseResourceDTO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentSimpleVO;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/20 23:18
 * @Version 1
 * @Description
 */
@Service
@Slf4j
public class MergeCourseResourceServiceImpl implements MergeCourseResourceService, BaseService {
    @DubboReference
    private RemoteStudentService remoteStudentService;

    @Resource
    private MergePdfService mergePdfService;
    @Value("${resource.mergePdf.watermark.prefix:学王书房}")
    private String waterMarkPrefix;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteWxService remoteWxService;

    @Value("${resource.qrcode.size:50}")
    private Integer qrcodeSize;

    @Override
    public InputStreamResource mergeCourseResource(List<BatchMergePdfDTO> mergePdfList) {
        if (CollectionUtil.isEmpty(mergePdfList)) {
            throw new ServiceException("请选择需要合并的资源");
        }
        // 合并
        try {
            File mergePdfFile = mergePdfService.batchMergePdf(mergePdfList);
            return new InputStreamResource(new AutoDeleteFileInputStream(mergePdfFile));
        } catch (IOException e) {
            log.error("合并pdf失败:{}", e.getMessage(), e);
            throw new ServiceException("合并pdf失败:".concat(e.getMessage()));
        }
    }

    @Override
    public List<BatchMergePdfDTO> createCourseMergePdfList(List<RemoteMergeCourseResourceDTO> resourceDTOList) {
        // 查找学生信息
        Map<Long, List<RemoteMergeCourseResourceDTO>> studentCourseMap = resourceDTOList.stream()
            .collect(Collectors.groupingBy(
                RemoteMergeCourseResourceDTO::getStudentId,
                LinkedHashMap::new,
                Collectors.toList()
            ));
        List<RemoteStudentSimpleVO> remoteStudentList = remoteStudentService.batchQueryStudentById(new ArrayList<>(studentCourseMap.keySet()));
        if (CollectionUtil.isEmpty(remoteStudentList)) {
            throw new ServiceException("无效的学生ID");
        }

        // 构建二维码
        Map<KnowledgeResourceType, String> resourceTypeQrcodeStrMap = this.buildResourceTypeQecodeStrMap(resourceDTOList);

        List<List<BatchMergePdfDTO>> mergePdfParamList = remoteStudentList.stream()
            .map(student -> {
                List<RemoteMergeCourseResourceDTO> courseResourceList = studentCourseMap.get(student.getStudentId());
                return this.buildMergePdfDTOStream(student, courseResourceList, resourceTypeQrcodeStrMap);
            }).filter(Objects::nonNull).toList();
        return mergePdfParamList.stream()
            .filter(Objects::nonNull)
            .flatMap(Collection::stream)
            .collect(Collectors.toList());
    }

    private Map<KnowledgeResourceType, String> buildResourceTypeQecodeStrMap(List<RemoteMergeCourseResourceDTO> resourceDTOList) {
        if (CollectionUtils.isEmpty(resourceDTOList)) {
            return Collections.emptyMap();
        }
        return resourceDTOList.stream().flatMap(resourceDTO -> {
            Map<KnowledgeResourceType, String> courseResourceOssUrlMap = resourceDTO.getCourseResourceOssUrlMap();
            return courseResourceOssUrlMap.keySet().stream()
                .map(resourceType -> new AbstractMap.SimpleEntry<>(resourceType, this.getQrcodeStr(resourceDTO, resourceType)));
        }).collect(Collectors.toMap(
            Map.Entry::getKey,
            Map.Entry::getValue,
            (oldValue, newValue) -> newValue
        ));
    }

    private String getQrcodeStr(RemoteMergeCourseResourceDTO resourceDTO, KnowledgeResourceType resourceType) {
        String qrcodeStr = this.buildQrcodeParamStr(resourceDTO, resourceType);
        QrConfig qrConfig = this.buildQrConfig();
        return QrCodeUtil.generateAsBase64(remoteWxService.generateMiniProgramUrl(qrcodeStr), qrConfig, ImgUtil.IMAGE_TYPE_PNG);
    }

    private QrConfig buildQrConfig() {
        QrConfig qrConfig = new QrConfig(qrcodeSize, qrcodeSize);
        qrConfig.setMargin(1);
        return qrConfig;
    }

    private String buildQrcodeParamStr(RemoteMergeCourseResourceDTO resourceDTO, KnowledgeResourceType resourceType) {
        StudyReviewQrCodeParamDTO qrCodeParam = new StudyReviewQrCodeParamDTO();
        qrCodeParam.setPrintSource(1L);
        qrCodeParam.setStudentId(resourceDTO.getStudentId().toString());
        qrCodeParam.setCourseId(resourceDTO.getCourseId().toString());
        qrCodeParam.setResourceType(resourceType.name());
        qrCodeParam.setStudyPlanRecordId(resourceDTO.getStudyPlanningRecordId().toString());
        return JSONUtil.toJsonStr(qrCodeParam).replace("\n", "");
    }

    private List<BatchMergePdfDTO> buildMergePdfDTOStream(RemoteStudentSimpleVO student,
                                                            List<RemoteMergeCourseResourceDTO> resources,
                                                            Map<KnowledgeResourceType, String> resourceTypeQrcodeStrMap) {
        String markPrefix = this.getWaterMarkPrefix(student);
        return resources.stream()
            .flatMap(mergeCourseResourceDTO -> {
                String lineTextModel = getLineTextModel(student, mergeCourseResourceDTO);
                Long courseId = mergeCourseResourceDTO.getCourseId();
                return mergeCourseResourceDTO.getCourseResourceOssUrlMap().entrySet().stream()
                    .map(ossUrl -> convertToBatchMergePdfDTO(student, lineTextModel, ossUrl.getKey(), ossUrl.getValue(),
                                    courseId, markPrefix,resourceTypeQrcodeStrMap.get(ossUrl.getKey())));
            }).collect(Collectors.toList());
    }

    private String getWaterMarkPrefix(RemoteStudentSimpleVO student) {
        if (ObjectUtils.isEmpty(student)) {
            return waterMarkPrefix;
        }
        Long branchId = student.getBranchId();
        Long deptId = remoteBranchService.selectDeptIdByBranchId(branchId);
        if (ObjectUtils.isEmpty(deptId)) {
            return waterMarkPrefix;
        }

        RemoteDeptVo deptSystemConfig = remoteDeptService.getDeptSystemConfig(deptId);

        if (StringUtils.isBlank(deptSystemConfig.getBrandName())) {
            return waterMarkPrefix;
        } else {
            return deptSystemConfig.getBrandName();
        }
    }

    private static BatchMergePdfDTO convertToBatchMergePdfDTO(RemoteStudentSimpleVO student, String lineTextModel,
                                                              KnowledgeResourceType fileType, String ossUrl, Long courseId,
                                                              String waterMarkPrefix,String qrCodeBase64Str) {
        BatchMergePdfDTO mergedPdfDTO = new BatchMergePdfDTO();
        mergedPdfDTO.setFilePath(ossUrl);
        String waterMark = waterMarkPrefix.concat(" ").concat(student.getStudentName());
        mergedPdfDTO.setWatermark(waterMark);
        mergedPdfDTO.setFirstLineText(lineTextModel);
        mergedPdfDTO.setFontSize(20);
        mergedPdfDTO.setVerticalSpacing(8.1f);
        mergedPdfDTO.setHorizontalSpacing(2f);
        mergedPdfDTO.setResourceType(KnowledgeResourceType.getByType(fileType.getType()));
        mergedPdfDTO.setBatchId(courseId.toString());
        mergedPdfDTO.setQrcodeBase64Str(qrCodeBase64Str);
        return mergedPdfDTO;
    }

    private static String getLineTextModel(RemoteStudentSimpleVO student, RemoteMergeCourseResourceDTO mergeCourseResourceDTO) {
        final String split = PdfMergeProcess.splitSign;
        return "姓名: " + student.getStudentName() + split
            + "年级: " + student.getStudentGrade() + split
            + "科目: " + mergeCourseResourceDTO.getSubject() + split
            + "课次: " + mergeCourseResourceDTO.getCourseName();
    }
}
