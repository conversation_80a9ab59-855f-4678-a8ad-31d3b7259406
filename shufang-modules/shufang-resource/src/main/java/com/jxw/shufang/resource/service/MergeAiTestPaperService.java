package com.jxw.shufang.resource.service;

import com.jxw.shufang.student.api.domain.dto.RemoteAiPaperOssUrlDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiRecordOssUrlDTO;
import org.springframework.core.io.InputStreamResource;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 11:28
 * @Version 1
 * @Description
 */
public interface MergeAiTestPaperService {
    /**
     * 合并AI评测报告
     *
     * @param aiRecordStudents
     * @return
     */
    InputStreamResource mergeAiReport(List<RemoteAiRecordOssUrlDTO> aiRecordStudents);

    /**
     * 合并AI评测试卷
     *
     * @param aiPaperInfos
     * @return
     */
    InputStreamResource mergeAiTestPaper(List<RemoteAiPaperOssUrlDTO> aiPaperInfos);
}
