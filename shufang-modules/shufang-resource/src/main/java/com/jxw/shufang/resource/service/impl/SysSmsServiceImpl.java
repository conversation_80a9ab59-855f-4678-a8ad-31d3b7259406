package com.jxw.shufang.resource.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.dromara.sms4j.provider.enumerate.SupplierType;
import com.jxw.shufang.common.core.constant.CacheConstants;
import com.jxw.shufang.common.core.enums.SmsCaptchaResultEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.resource.domain.vo.SysSmsVo;
import com.jxw.shufang.resource.service.ISysSmsService;
import com.jxw.shufang.system.api.RemoteConfigService;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.LinkedHashMap;

@RequiredArgsConstructor
@Service
@Slf4j
public class SysSmsServiceImpl implements ISysSmsService {

    @DubboReference
    private RemoteConfigService remoteConfigService;


    @Override
    public SysSmsVo sendSmsCaptcha(String phone, String code) {
        Assert.isTrue(PhoneUtil.isMobile(phone), () -> new ServiceException("手机号格式错误"));
        Assert.notBlank(code, () -> new ServiceException("验证码不能为空"));
        String templateId = remoteConfigService.selectSmsCaptchaTemplateId();
        Assert.notNull(templateId, () -> new ServiceException("短信验证码模板Id未配置"));

        Integer smsCaptchaMaxCount = remoteConfigService.select24hSmsCaptchaMaxCount(); // 24小时内验证码发送次数

        //检查手机号在24小时内获取了多少次，是否超过限制
        long timeToLive = RedisUtils.getTimeToLive(CacheConstants.SMS_CAPTCHA_COUNT_KEY + phone);
        Integer smsCaptchaCount = RedisUtils.getCacheObject(CacheConstants.SMS_CAPTCHA_COUNT_KEY + phone);
        smsCaptchaCount = smsCaptchaCount == null ? 0 : smsCaptchaCount;
        if (smsCaptchaCount > smsCaptchaMaxCount) {
            throw new ServiceException("您24小时内发送验证码次数已达上限，请" + getTimeTips(timeToLive) + "后重试");
        }

        //检查手机号在24小时内连续错误次数，是否超过限制
        Integer smsCaptchaErrorMaxCount = remoteConfigService.select24hSmsCaptchaErrorMaxCount(); // 24小时内验证码错误次数
        Integer smsCaptchaErrCount = RedisUtils.getCacheObject(CacheConstants.SMS_CAPTCHA_ERR_COUNT_KEY + phone);
        smsCaptchaErrCount = smsCaptchaErrCount == null ? 0 : smsCaptchaErrCount;
        if (smsCaptchaErrCount >= smsCaptchaErrorMaxCount) {
            throw new ServiceException("您24小时内连续错误次数已达上限，请" + getTimeTips(timeToLive) + "后重试");
        }

        //验证码有效期
        Long smsCaptchaExpireTime = remoteConfigService.selectSmsCaptchaExpireTime();


        //开始发送验证码
        SysSmsVo sysSmsVo = new SysSmsVo();
        LinkedHashMap<String, String> map = new LinkedHashMap<>(1);
        map.put("code", code);
        SmsBlend smsBlend = SmsFactory.createSmsBlend(SupplierType.ALIBABA);
        SmsResponse smsResponse = smsBlend.sendMessage(phone, templateId, map);
        boolean isSuccess = "OK".equals(smsResponse.getCode());
        sysSmsVo.setIsSuccess(isSuccess);
        if (isSuccess) {
            //缓存发送次数
            RedisUtils.setCacheObject(CacheConstants.SMS_CAPTCHA_COUNT_KEY + phone, ++smsCaptchaCount, timeToLive <= 0 ? Duration.ofHours(24) : Duration.ofMillis(timeToLive));
            //记录验证码
            RedisUtils.setCacheObject(CacheConstants.SMS_CAPTCHA_KEY + phone, code, Duration.ofSeconds(smsCaptchaExpireTime));
        } else {
            log.error("验证码短信发送异常 => {}", smsResponse);
        }
        sysSmsVo.setResponse(JsonUtils.toJsonString(smsResponse));
        sysSmsVo.setMessage(smsResponse.getMessage());
        return sysSmsVo;
    }

    @Override
    public SysSmsVo sendSmsCaptcha(String phone) {
        return sendSmsCaptcha(phone, RandomUtil.randomNumbers(4));
    }

    @Override
    public SmsCaptchaResultEnum checkSmsCaptcha(String phone, String code,boolean needDelSmsCaptcha) {
        Assert.isTrue(PhoneUtil.isMobile(phone), () -> new ServiceException("手机号格式错误"));
        Assert.notBlank(code, () -> new ServiceException("验证码不能为空"));

        //有超时的Redisson对象的剩余生存时间
        //时间（以毫秒为单位）-如果密钥不存在，则为2-如果密钥存在但没有关联的过期，则为1。
        long smsTimeToLive = RedisUtils.getTimeToLive(CacheConstants.SMS_CAPTCHA_KEY + phone);

        if (smsTimeToLive == -2) {
            return SmsCaptchaResultEnum.EXPIRED;
        }

        String rightCode = RedisUtils.getCacheObject(CacheConstants.SMS_CAPTCHA_KEY + phone);
        if (code.equals(rightCode)) {
            if (needDelSmsCaptcha){
                //验证成功，删除验证码
                RedisUtils.deleteObject(CacheConstants.SMS_CAPTCHA_KEY + phone);
            }
            //删除错误次数
            RedisUtils.deleteObject(CacheConstants.SMS_CAPTCHA_ERR_COUNT_KEY + phone);
            return SmsCaptchaResultEnum.SUCCESS;
        } else {
            //错误次数+1
            Integer smsCaptchaErrCount = RedisUtils.getCacheObject(CacheConstants.SMS_CAPTCHA_ERR_COUNT_KEY + phone);
            long errCountTimeToLive = RedisUtils.getTimeToLive(CacheConstants.SMS_CAPTCHA_ERR_COUNT_KEY + phone);
            smsCaptchaErrCount = smsCaptchaErrCount == null ? 1 : smsCaptchaErrCount + 1;
            RedisUtils.setCacheObject(CacheConstants.SMS_CAPTCHA_ERR_COUNT_KEY + phone, smsCaptchaErrCount, errCountTimeToLive <= 0 ? Duration.ofHours(24) : Duration.ofMillis(errCountTimeToLive));
            return SmsCaptchaResultEnum.FAIL;
        }
    }

    @Override
    public void deleteSmsCaptcha(String phone) {
        RedisUtils.deleteObject(CacheConstants.SMS_CAPTCHA_KEY + phone);
    }


    /**
     * 获取时间提示
     *
     * @param timeToLive 生存时间 毫秒
     * @date 2024/04/18 07:09:13
     */
    private String getTimeTips(long timeToLive) {
        //大于一小时，单位用小时，小于则用分钟
        if (timeToLive > 1000 * 60 * 60) {
            return timeToLive / 1000 / 60 / 60 + "小时";
        } else {
            return timeToLive / 1000 / 60 + "分钟";
        }
    }
}
