package com.jxw.shufang.resource.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.resource.domain.dto.BeforeMergeHandlerResultDTO;
import com.jxw.shufang.resource.domain.dto.BeforeMergeHandlerResultDTOComparator;
import com.jxw.shufang.resource.domain.dto.ProcessingMergeContext;
import com.jxw.shufang.resource.service.MergePdfService;
import com.jxw.shufang.resource.service.PdfMergeProcess;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/19 9:38
 * @Version 1
 * @Description 合并pdfservice
 */
@Service
@Slf4j
public class MergePdfServiceImpl implements MergePdfService {
    @Value("${resource.mergePdf.maxSize: 30}")
    private static final Integer MAXSIZE = 30;



    @Override
    public File batchMergePdf(List<BatchMergePdfDTO> batchMergePdfs){
        this.validateRequest(batchMergePdfs);

        Map<String, List<BatchMergePdfDTO>> mergePdfCourseMap = this.getMergePdfCourseMap(batchMergePdfs);
        List<BeforeMergeHandlerResultDTO> result = this.asyncProcessFile(mergePdfCourseMap);

        // 合并文件
        Map<String, List<String>> batchFileMap = result.stream()
            .collect(Collectors.groupingBy(
                BeforeMergeHandlerResultDTO::getBatchId,
                Collectors.mapping(BeforeMergeHandlerResultDTO::getFilePath, Collectors.toList())
            ));
        List<File> mergedFiles = batchFileMap.values().stream()
            .map(filePaths -> PdfMergeProcess.merge(new ArrayList<>(filePaths)))
            .toList();

        // 合并前添加空白页
        List<String> list = mergedFiles.stream().map(item -> {
            File file = PdfMergeProcess.addWhitePage(item);
            return file.getAbsolutePath();
        }).toList();

        // 循环合并
        return PdfMergeProcess.merge(list);
    }

    private Map<String, List<BatchMergePdfDTO>> getMergePdfCourseMap(List<BatchMergePdfDTO> batchMergePdfs) {
        return batchMergePdfs.stream()
            .collect(Collectors.groupingBy(BatchMergePdfDTO::getBatchId, LinkedHashMap::new, Collectors.toList()));
    }

    private List<BeforeMergeHandlerResultDTO> asyncProcessFile(Map<String, List<BatchMergePdfDTO>> mergePdfCourseMap) {
        List<CompletableFuture<List<BeforeMergeHandlerResultDTO>>> futures = mergePdfCourseMap.values()
            .stream()
            .map(this::processFile)
            .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return futures.stream()
            .flatMap(future -> {
                try {
                    return future.get().stream();
                } catch (Exception e) {
                    throw new RuntimeException("分组处理失败", e);
                }
            }).toList();
    }

    private CompletableFuture<List<BeforeMergeHandlerResultDTO>> processFile(List<BatchMergePdfDTO> batchMergePdfs) {
        return CompletableFuture.supplyAsync(()
            -> batchMergePdfs.stream()
            .map(ProcessingMergeContext::new)
            .map(this::downloadFile)
            .filter(v->{
                BatchMergePdfDTO batchMergePdfDTO = v.getBatchMergePdfDTO();
                return !(v.getSkip() || StrUtil.isEmpty(batchMergePdfDTO.getWatermark()));
            })
            .map(this::processWatermark)
            .map(this::processFirstLineText)
            .map(this::addImg)
            .map(this::buildResultDTO)
            .sorted(BeforeMergeHandlerResultDTOComparator.getComparator())
            .collect(Collectors.toList()));
    }

    private ProcessingMergeContext addImg(ProcessingMergeContext context) {
        BatchMergePdfDTO batchMergePdfDTO = context.getBatchMergePdfDTO();
        boolean addImgFlag = KnowledgeResourceType.TEST.getType().equals(batchMergePdfDTO.getResourceType().getType())
            || KnowledgeResourceType.PRACTICE.getType().equals(batchMergePdfDTO.getResourceType().getType());

        boolean skip = context.getSkip()
            || StrUtil.isEmpty(batchMergePdfDTO.getQrcodeBase64Str())
            || !addImgFlag;
        if (skip) {
            return context;
        }
        try {
            File imageFile = PdfMergeProcess.addImg(context.getTempFile(), batchMergePdfDTO.getQrcodeBase64Str());
            context.setTempFile(imageFile);
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            context.setSkip(true);
        }
        return context;
    }

    private ProcessingMergeContext downloadFile(ProcessingMergeContext context) {
        try {
            File tempFile = PdfMergeProcess.downloadFileToTemp(context.getBatchMergePdfDTO().getFilePath());
            if (null == tempFile) {
                context.setSkip(true);
            }
            context.setTempFile(tempFile);
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            context.setSkip(true);
        }
        return context;
    }

    private ProcessingMergeContext processWatermark(ProcessingMergeContext context) {
        BatchMergePdfDTO batchMergePdfDTO = context.getBatchMergePdfDTO();
        boolean skip = context.getSkip() || StrUtil.isEmpty(batchMergePdfDTO.getWatermark());
        if (skip) {
            return context;
        }

        try {
            File waterFile = PdfMergeProcess.addWatermark(context.getTempFile(), batchMergePdfDTO);
            context.setTempFile(waterFile);
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            context.setSkip(true);
        }
        return context;
    }

    private ProcessingMergeContext processFirstLineText(ProcessingMergeContext context) {
        BatchMergePdfDTO batchMergePdfDTO = context.getBatchMergePdfDTO();
        boolean addText = StrUtil.isNotEmpty(batchMergePdfDTO.getFirstLineText())
            && !KnowledgeResourceType.HANDOUT.equals(batchMergePdfDTO.getResourceType());
        boolean skip = context.getSkip() || !addText;
        if (skip) {
            return context;
        }

        try {
            File file = PdfMergeProcess.addText(context.getTempFile(), batchMergePdfDTO.getFirstLineText());
            context.setTempFile(file);
        } catch (Exception e) {
            log.error("下载文件失败: {}", e.getMessage(), e);
            context.setSkip(true);
        }
        return context;
    }

    private BeforeMergeHandlerResultDTO buildResultDTO(ProcessingMergeContext processingMergeContext) {
        BatchMergePdfDTO batchMergePdfDTO = processingMergeContext.getBatchMergePdfDTO();

        BeforeMergeHandlerResultDTO result = new BeforeMergeHandlerResultDTO();
        result.setBatchId(batchMergePdfDTO.getBatchId());
        result.setFilePath(processingMergeContext.getTempFile().getAbsolutePath());
        String fileType = Optional.ofNullable(batchMergePdfDTO.getResourceType()).map(KnowledgeResourceType::getType)
            .orElse(null);
        result.setFileType(fileType);
        return result;
    }

    /**
     * 参数验证
     *
     * @param batchMergePdfs
     */
    private void validateRequest(List<BatchMergePdfDTO> batchMergePdfs) {
        if (CollectionUtil.isEmpty(batchMergePdfs)) {
            throw new ServiceException("需合并的文件列表为空");
        }
        if (batchMergePdfs.size() > MAXSIZE) {
            throw new ServiceException("会员:".concat(batchMergePdfs.get(0).getWatermark()).concat(",需合并的文件列表不能超过10个"));
        }
    }

    /**
     * 合并后的处理（清除缓存文件，释放内存）
     */
    private void afterMergeHandler(List<String> tempFilePaths) {
        if (CollectionUtil.isEmpty(tempFilePaths)) {
            return;
        }

        tempFilePaths.forEach(f -> {
            Path filePath = Paths.get(f);
            try {
                Files.deleteIfExists(filePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
