package com.jxw.shufang.resource.domain.dto;

import lombok.Data;

import java.io.File;

/**
 * <AUTHOR>
 * @Date 2025/3/12 15:17
 * @Version 1
 * @Description
 */
@Data
public class ProcessingMergeContext {
    private BatchMergePdfDTO batchMergePdfDTO;
    private File tempFile;
    private Boolean skip;

    public ProcessingMergeContext(BatchMergePdfDTO batchMergePdfDTO) {
        this.batchMergePdfDTO = batchMergePdfDTO;
        this.skip = false;
    }
}
