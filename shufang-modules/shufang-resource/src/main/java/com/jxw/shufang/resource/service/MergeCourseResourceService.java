package com.jxw.shufang.resource.service;

import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteMergeCourseResourceDTO;
import org.springframework.core.io.InputStreamResource;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 23:18
 * @Version 1
 * @Description
 */
public interface MergeCourseResourceService {
    /**
     * 合并课程资源
     *
     * @param batchMergePdfDTOList
     * @return
     */
    InputStreamResource mergeCourseResource(List<BatchMergePdfDTO> batchMergePdfDTOList);

    /**
     * 创建课程资源合并列表
     *
     * @param resourceDTO
     * @return
     */
    List<BatchMergePdfDTO> createCourseMergePdfList(List<RemoteMergeCourseResourceDTO> resourceDTO);
}
