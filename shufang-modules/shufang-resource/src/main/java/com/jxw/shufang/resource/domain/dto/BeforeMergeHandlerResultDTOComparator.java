package com.jxw.shufang.resource.domain.dto;

import com.jxw.shufang.common.core.enums.KnowledgeResourceType;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/2/27 20:24
 * @Version 1
 * @Description 定义排序规则映射表
 */
public class BeforeMergeHandlerResultDTOComparator {
    private static final Map<String, Integer> FILE_TYPE_ORDER = new HashMap<>();

    static {
        FILE_TYPE_ORDER.put(KnowledgeResourceType.PRACTICE.getType(), KnowledgeResourceType.PRACTICE.getSortNum());
        FILE_TYPE_ORDER.put(KnowledgeResourceType.HANDOUT.getType(), KnowledgeResourceType.HANDOUT.getSortNum());
        FILE_TYPE_ORDER.put(KnowledgeResourceType.TEST.getType(), KnowledgeResourceType.TEST.getSortNum());
        FILE_TYPE_ORDER.put(KnowledgeResourceType.TEST_ANALYSIS.getType(), KnowledgeResourceType.TEST_ANALYSIS.getSortNum());
        FILE_TYPE_ORDER.put(KnowledgeResourceType.PRACTICE_ANALYSIS.getType(), KnowledgeResourceType.PRACTICE_ANALYSIS.getSortNum());
    }

    /**
     * 创建自定义排序器
     *
     * @return
     */
    public static Comparator<BeforeMergeHandlerResultDTO> getComparator() {
        return (o1, o2) -> {
            // 处理null值（排到末尾）
            String type1 = o1.getFileType();
            String type2 = o2.getFileType();

            int order1 = FILE_TYPE_ORDER.getOrDefault(type1, Integer.MAX_VALUE);
            int order2 = FILE_TYPE_ORDER.getOrDefault(type2, Integer.MAX_VALUE);

            return Integer.compare(order1, order2);
        };
    }
}
