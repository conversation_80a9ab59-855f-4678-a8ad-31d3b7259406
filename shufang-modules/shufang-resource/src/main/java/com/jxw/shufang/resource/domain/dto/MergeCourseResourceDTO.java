package com.jxw.shufang.resource.domain.dto;

import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupResourceVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 23:23
 * @Version 1
 * @Description
 */
@Data
public class MergeCourseResourceDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7074032518491133925L;
    private Long studentId;
    private Long courseId;
    private Long knowledgeId;
    /**
     * 课次
     */
    private String courseName;
    private String subject;
    private List<RemoteGroupResourceVo> courseResourceOssUrl;
    private Long studyPlanningRecordId;
}
