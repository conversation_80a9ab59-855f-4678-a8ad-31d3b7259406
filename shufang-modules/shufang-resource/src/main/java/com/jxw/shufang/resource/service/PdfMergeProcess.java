package com.jxw.shufang.resource.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.resource.domain.dto.WaterMarkToDocumentDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.text.BreakIterator;
import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2025/2/19 20:40
 * @Version 1
 * @Description pdf文件合并处理
 */
@Slf4j
public class PdfMergeProcess {

    private static String tempFilePrefix = "temp_pdf";
    private static String tempFileSuffix = "_merged.pdf";
    /**
     * 字节缓冲区大小
     */
    private static Integer streamBufferByteSize = 4096;
    /**
     * 字体类型路径
     */
    private static String fontTypePath = "fonts/Bold.ttf";
    private static String noBoldFontTypePath = "fonts/noBold.ttf";

    /**
     * 水印默认文字大小
     */
    private static Integer defaultWaterMarkFontSize = 35;

    /**
     * 横板文字默认大小
     */
    private static Integer defaultCommonFontSize = 13;

    /**
     * 默认水印横间距
     */
    private static float defaultHorizontalSpacing = 1.2f;

    /**
     * 默认垂直间距
     */
    private static float defaultVerticalSpacing = 4f;

    /**
     * 文本分隔符，用户分割多行文本
     */
    public static String splitSign = "&&";

    /**
     * 获取pdf临时文件地址
     *
     * @param httpUrlPath http原地址
     * @return 原文件地址的local缓存地址
     */
    public static File downloadFileToTemp(String httpUrlPath) {
        String http = "http";
        // URL验证
        if (StrUtil.isEmpty(httpUrlPath) || !httpUrlPath.startsWith(http)) {
            throw new IllegalArgumentException("Invalid URL: " + httpUrlPath);
        }
        try {
            File tempPath = createTempFile(tempFilePrefix, tempFileSuffix);
            URL url = new URL(httpUrlPath);
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("GET");
            int responseCode = con.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP请求失败，状态码： " + responseCode);
            }

            try (InputStream inputStream = con.getInputStream();
                 OutputStream outputStream = new BufferedOutputStream(Files.newOutputStream(tempPath.toPath()))) {
                byte[] buffer = new byte[streamBufferByteSize];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } finally {
                con.disconnect();
            }
            return tempPath;
        } catch (IOException e) {
            log.error("Failed to download file from URL:{} " + httpUrlPath, e);
        }
        return null;
    }

    /**
     * 合并多个 PDF 文件
     *
     * @param localFilePaths 源文件路径列表(非http请求地址)
     * @return 合并后的 PDF 文件临时地址
     */
    public static File merge(List<String> localFilePaths) {
        List<File> fileList = localFilePaths.stream().map(File::new).toList();

        try {
            PDFMergerUtility merger = new PDFMergerUtility();
            // 循环合并
            for (File file : fileList) {
                merger.addSource(file);
            }

            // 合并后输出流的位置
            File tempFile = createTempFile(tempFilePrefix, tempFileSuffix);
            merger.setDestinationStream(new FileOutputStream(tempFile));
            merger.mergeDocuments(null);

            return tempFile;
        } catch (Exception e) {
            log.error("Failed to merge PDF files: {}", e.getMessage(), e);
            throw new ServiceException("Failed to merge PDF files");
        } finally {
            deleteFile(fileList);
        }

    }


    /**
     * 添加水印和文字到 PDF 文件
     *
     * @param localFilePaths 输入文件
     * @return File 处理后的文件地址
     */
    public static File addWatermark(File localFilePaths, BatchMergePdfDTO batchMergePdfDTO) {
        try (PDDocument document = PDDocument.load(localFilePaths)) {
            InputStream inputStream = PdfMergeProcess.class.getClassLoader()
                .getResourceAsStream(fontTypePath);
            PDType0Font font = PDType0Font.load(document, inputStream, true);
            pageAddWaterMark(batchMergePdfDTO, document, font);

            File processedFile = createTempFile(tempFilePrefix, tempFileSuffix);
            document.save(processedFile);
            return processedFile;
        } catch (Exception e) {
            log.error("Failed to add watermark to PDF: {}", e.getMessage(), e);
            return localFilePaths;
        } finally {
            deleteFile(localFilePaths);
        }
    }

    private static void pageAddWaterMark(BatchMergePdfDTO batchMergePdfDTO, PDDocument document, PDType0Font font) throws IOException {
        for (int i = 0; i < document.getNumberOfPages(); i++) {
            PDPage page = document.getPage(i);
            // 组装document对象
            WaterMarkToDocumentDTO waterMarkToDocumentDTO = instanceWaterMarkDTO(batchMergePdfDTO, document, font, page);
            // add 水印
            addWaterMarkToDocument(waterMarkToDocumentDTO);
        }
    }

    public static File addText(File localFilePaths, String text) {
        try (PDDocument document = PDDocument.load(localFilePaths)) {
            InputStream noboldInputStream = PdfMergeProcess.class.getClassLoader()
                .getResourceAsStream(noBoldFontTypePath);
            PDType0Font noBoldFont = PDType0Font.load(document, noboldInputStream, true);
            firstPageAddText(document, text, noBoldFont);

            File processedFile = createTempFile(tempFilePrefix, tempFileSuffix);
            document.save(processedFile);
            return processedFile;
        } catch (Exception e) {
            log.error("Failed to add text to PDF: {}", e.getMessage(), e);
            return localFilePaths;
        } finally {
            deleteFile(localFilePaths);
        }
    }

    /**
     * 添加图片
     *
     * @param localFilePaths
     * @param base64Image
     * @return
     */
    public static File addImg(File localFilePaths, String base64Image) {
        try (PDDocument document = PDDocument.load(localFilePaths)) {
            // 处理图像
            processImg(base64Image, document);

            File processedFile = createTempFile("temp_", ".pdf");
            document.save(processedFile);
            return processedFile;
        } catch (Exception e) {
            log.error("Failed to add image to PDF: {}", e.getMessage(), e);
            return localFilePaths;
        } finally {
            deleteFile(localFilePaths);
        }
    }

    private static void processImg(String base64Image, PDDocument document) throws IOException {
        String cleanBase64 = base64Image.replaceAll("^data:image/[^;]+;base64,", "");
        byte[] imageBytes = Base64.getDecoder().decode(cleanBase64);
        PDPage firstPage = document.getPage(0);
        PDImageXObject pdImage = PDImageXObject.createFromByteArray(
            document,
            imageBytes,
            "png"
        );

        // 页面尺寸
        PDRectangle pageSize = firstPage.getMediaBox();
        double pageWidth = pageSize.getWidth();
        double pageHeight = pageSize.getHeight();

        // 边距设置（可根据需求调整）
        double margin = 15.0;
        // 限制图片最大显示尺寸（关键修改：设置为小尺寸，如100x100点）
        double maxDisplayWidth = 80.0;
        double maxDisplayHeight = 80.0;

        // 获取图片原始尺寸（点）
        int imageWidth = pdImage.getWidth();
        int imageHeight = pdImage.getHeight();

        // 计算缩放比例（保持宽高比，不超过最大显示尺寸）
        double scaleX = maxDisplayWidth / imageWidth;
        double scaleY = maxDisplayHeight / imageHeight;
        double scale = Math.min(scaleX, scaleY);

        // 最终显示尺寸（若原始尺寸小于最大尺寸，则保持原始大小）
        double displayWidth = imageWidth * scale;
        double displayHeight = imageHeight * scale;

        float xPos = (float) (pageWidth - margin - displayWidth);
        float yPos = (float) (pageHeight - margin - displayHeight);

        // 绘制图片到页面（使用APPEND模式避免覆盖原有内容）
        try (PDPageContentStream contentStream = new PDPageContentStream(document, firstPage,
            PDPageContentStream.AppendMode.APPEND, false, true)) {
            contentStream.drawImage(pdImage, xPos, yPos, (float) displayWidth, (float) displayHeight);
        }
    }

    private static void firstPageAddText(PDDocument document, String text, PDType0Font font) throws IOException {
        if (StrUtil.isEmpty(text)) {
            return;
        }
        PDPage firstPage = document.getPage(0);
        addTextToDocument(text, font, document, firstPage);
    }

    private static void deleteFile(File localFilePaths) {
        if (null == localFilePaths) {
            return;
        }
        deleteFile(Collections.singletonList(localFilePaths));
    }

    private static void deleteFile(List<File> localFilePaths) {
        if (CollectionUtil.isEmpty(localFilePaths)) {
            return;
        }
        localFilePaths.forEach(file -> {
            try {
                Files.deleteIfExists(file.toPath());
            } catch (IOException e) {
                log.error("Failed to delete file: {}", e.getMessage(), e);
            }
        });
    }


    public static File addWhitePage(File localFilePaths) {
        File processedFile;
        try (PDDocument document = PDDocument.load(localFilePaths)) {
            int numberOfPages = document.getNumberOfPages();
            //如果双面打印，需要添加一页空白页
            if (numberOfPages % 2 == 1) {
                document.addPage(new PDPage());
            }
            processedFile = createTempFile(tempFilePrefix, tempFileSuffix);
            document.save(processedFile);
            return processedFile;
        } catch (Exception e) {
            log.error("Failed to add nullPage to PDF: {}", e.getMessage(), e);
            throw new ServiceException("Failed to add  nullPage to PDF");
        } finally {
            deleteFile(localFilePaths);
        }

    }

    private static WaterMarkToDocumentDTO instanceWaterMarkDTO(BatchMergePdfDTO batchMergePdfDTO,
                                                               PDDocument document, PDType0Font font,
                                                               PDPage page) {
        WaterMarkToDocumentDTO waterMarkToDocumentDTO = new WaterMarkToDocumentDTO();
        waterMarkToDocumentDTO.setDocument(document);
        waterMarkToDocumentDTO.setWaterMark(batchMergePdfDTO.getWatermark());
        waterMarkToDocumentDTO.setPage(page);
        waterMarkToDocumentDTO.setFont(font);
        waterMarkToDocumentDTO.setFontSize(batchMergePdfDTO.getFontSize());
        waterMarkToDocumentDTO.setHorizontalSpacing(batchMergePdfDTO.getHorizontalSpacing());
        waterMarkToDocumentDTO.setVerticalSpacing(batchMergePdfDTO.getVerticalSpacing());
        return waterMarkToDocumentDTO;
    }

    /**
     * 添加水印
     *
     * @throws IOException
     */
    private static void addWaterMarkToDocument(WaterMarkToDocumentDTO waterMarkToDocumentDTO) throws IOException {
        String waterMark = waterMarkToDocumentDTO.getWaterMark();
        PDType0Font font = waterMarkToDocumentDTO.getFont();
        Integer fontSize = null == waterMarkToDocumentDTO.getFontSize() ?
            defaultWaterMarkFontSize : waterMarkToDocumentDTO.getFontSize();
        PDPage page = waterMarkToDocumentDTO.getPage();
        float horizontalSpacing = null == waterMarkToDocumentDTO.getHorizontalSpacing()
            ? defaultHorizontalSpacing : waterMarkToDocumentDTO.getHorizontalSpacing();
        float verticalSpacing = null == waterMarkToDocumentDTO.getVerticalSpacing()
            ? defaultVerticalSpacing : waterMarkToDocumentDTO.getVerticalSpacing();

        // 创建内容流（追加模式）
        try (PDPageContentStream contentStream = new PDPageContentStream(waterMarkToDocumentDTO.getDocument(), page,
            PDPageContentStream.AppendMode.APPEND, false, true)) {

            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(0.1f);
            contentStream.setGraphicsStateParameters(graphicsState);

            // 水印文本和字体设置
            contentStream.setFont(font, fontSize);
            PDRectangle mediaBox = page.getMediaBox();
            float pageWidth = mediaBox.getWidth();
            float pageHeight = mediaBox.getHeight();

            // 计算水印平铺参数
            float textWidth = font.getStringWidth(waterMark) / 1000 * fontSize;
            float textHeight = font.getFontDescriptor().getFontBoundingBox().getHeight() / 1000 * fontSize;

            // 水平间距
            float spacingX = textWidth * horizontalSpacing;
            // 垂直间距
            float spacingY = textHeight * verticalSpacing;

            // 倾斜平铺水印
            for (float x = 0; x < pageWidth; x += spacingX) {
                for (float y = 0; y < pageHeight; y += spacingY) {
                    contentStream.saveGraphicsState();
                    // 先旋转后平移
                    contentStream.beginText();
                    contentStream.transform(Matrix.getTranslateInstance(x, y));
                    contentStream.transform(Matrix.getRotateInstance(Math.toRadians(30), textWidth / 2, textHeight / 2));
                    contentStream.newLineAtOffset(0, 0);
                    contentStream.showText(waterMark);
                    contentStream.endText();
                    contentStream.restoreGraphicsState();
                }
            }
        }
    }

    /**
     * 添加文字
     *
     * @param text
     * @param font
     * @param document
     * @param page
     * @throws IOException
     */

    private static void addTextToDocument(String text, PDType0Font font, PDDocument document, PDPage page)
        throws IOException {
        final float marginRatio = 0.05f;
        PDRectangle mediaBox = page.getMediaBox();
        try (PDPageContentStream contentStream = new PDPageContentStream(
            document, page, PDPageContentStream.AppendMode.APPEND, true, true)) {
            contentStream.setFont(font, defaultCommonFontSize);
            // 1. 动态计算字体参数
            float fontSize = defaultCommonFontSize;
            // 2. 边距计算
            float availableWidth = mediaBox.getWidth() * (1 - 2 * marginRatio);
            // 删除pdf中多余的制表符
            text = replaceBlockStr(text);
            // 3. 文本分割（支持中英文混合）
            List<String> lines = splitTestWithSign(text, font, fontSize, availableWidth);

            // 4. 动态起始Y坐标计算
            contentStream.beginText();
            contentStream.setFont(font, defaultCommonFontSize);
            // 设置起始位置（左对齐，距顶部30点）
            contentStream.newLineAtOffset(30, page.getMediaBox().getHeight() - 30);
            contentStream.setLeading(defaultCommonFontSize * 1.2);
            for (String line : lines) {
                contentStream.showText(line);
                contentStream.newLine();
            }
            contentStream.saveGraphicsState();
        }
    }

    private static String replaceBlockStr(String text) {
        return text.replace('\u00A0', ' ');
    }

    private static List<String> splitTestWithSign(String text, PDType0Font font, float fontSize, float maxWidth) {
        List<String> split = Arrays.asList(text.split(splitSign));
        List<String> result = new ArrayList<>(split.subList(0, Math.max(split.size() - 1, 0)));

        String lastText = split.get(split.size() - 1);
        List<String> lastElement = Stream.of(lastText).flatMap(m -> {
            try {
                return splitTextIntoLines(m, font, fontSize, maxWidth).stream();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).toList();
        result.addAll(lastElement);
        return result;
    }

    private static List<String> splitTextIntoLines(String text, PDType0Font font,
                                                   float fontSize, float maxWidth) throws IOException {
        List<String> lines = new ArrayList<>();
        BreakIterator lineBreakIterator = BreakIterator.getLineInstance(Locale.getDefault());
        lineBreakIterator.setText(text);

        int start = lineBreakIterator.first();
        int end = lineBreakIterator.next();
        StringBuilder currentLine = new StringBuilder();

        while (end != BreakIterator.DONE) {
            String word = text.substring(start, end);
            float wordWidth = font.getStringWidth(word) * fontSize / 1000f;

            // 处理超长单词（如无空格长字符串）
            if (wordWidth > maxWidth) {
                if (!currentLine.toString().isEmpty()) {
                    lines.add(currentLine.toString());
                    currentLine.setLength(0);
                }
                lines.addAll(splitLongWord(word, font, fontSize, maxWidth));
                start = end;
                end = lineBreakIterator.next();
                continue;
            }

            // 检查当前行宽度
            float currentWidth = font.getStringWidth(currentLine.toString()) * fontSize / 1000f;
            if (currentWidth + wordWidth > maxWidth) {
                lines.add(currentLine.toString());
                currentLine.setLength(0);
            }

            currentLine.append(word);
            start = end;
            end = lineBreakIterator.next();
        }

        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }
        return lines;
    }

    // 处理超长单词分割
    private static List<String> splitLongWord(String word, PDType0Font font,
                                              float fontSize, float maxWidth) throws IOException {
        List<String> parts = new ArrayList<>();
        StringBuilder currentPart = new StringBuilder();
        float currentWidth = 0f;

        for (char c : word.toCharArray()) {
            float charWidth = font.getStringWidth(String.valueOf(c)) * fontSize / 1000f;
            if (currentWidth + charWidth > maxWidth) {
                parts.add(currentPart.toString());
                currentPart.setLength(0);
                currentWidth = 0f;
            }
            currentPart.append(c);
            currentWidth += charWidth;
        }

        if (currentPart.length() > 0) {
            parts.add(currentPart.toString());
        }
        return parts;
    }

    /**
     * 创建临时缓存文件
     *
     * @param prefix
     * @param suffix
     * @return
     * @throws IOException
     */
    private static File createTempFile(String prefix, String suffix) throws IOException {
        if (StrUtil.isEmpty(prefix)) {
            prefix = UUID.fastUUID().toString().concat(tempFilePrefix);
        } else {
            prefix = UUID.fastUUID().toString().concat(prefix);
        }
        return File.createTempFile(prefix, suffix);
    }
}
