package com.jxw.shufang.resource.controller;


import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.ratelimiter.annotation.RateLimiter;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.resource.domain.vo.SysSmsVo;
import com.jxw.shufang.resource.service.ISysSmsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信功能
 *

 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sms")
public class SysSmsController extends BaseController {

    private final ISysSmsService sysSmsService;

    /**
     * 短信验证码
     *
     * @param phonenumber 用户手机号
     */
    @RateLimiter(key = "#phonenumber", time = 60, count = 1)
    @GetMapping("/code")
    public R<Void> smsCaptcha(@NotBlank(message = "{user.phonenumber.not.blank}") String phonenumber) {
        SysSmsVo sysSmsVo = sysSmsService.sendSmsCaptcha(phonenumber);
        if (sysSmsVo.getIsSuccess()) {
            return R.ok();
        } else {
            return R.fail(sysSmsVo.getMessage());
        }
    }


}
