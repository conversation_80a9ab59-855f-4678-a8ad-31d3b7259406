package com.jxw.shufang.resource.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.resource.domain.vo.SysOssVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


/**
 * 系统角色数据转换器
 *
 * @date 2024/02/18 11:30:43
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysOssVoConvert extends BaseMapper<SysOssVo, RemoteFile> {
    //originalName转成name
    @Override
    @Mapping(target = "name", source = "originalName")
    RemoteFile convert(SysOssVo source);
}




