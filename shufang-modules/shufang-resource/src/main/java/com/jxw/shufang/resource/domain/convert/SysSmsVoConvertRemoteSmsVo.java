package com.jxw.shufang.resource.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.resource.api.domain.RemoteSmsVo;
import com.jxw.shufang.resource.domain.vo.SysSmsVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysSmsVoConvertRemoteSmsVo extends BaseMapper<SysSmsVo, RemoteSmsVo> {

}
