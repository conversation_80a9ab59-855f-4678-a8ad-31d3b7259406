package com.jxw.shufang.resource.service;

import com.jxw.shufang.common.oss.entity.UploadResult;
import com.jxw.shufang.resource.domain.bo.SysUploadOssBo;
import jakarta.servlet.http.HttpServletResponse;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.resource.domain.bo.SysOssBo;
import com.jxw.shufang.resource.domain.bo.SysOssMultipartBo;
import com.jxw.shufang.resource.domain.vo.SysOssMultipartVo;
import com.jxw.shufang.resource.domain.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Collection;
import java.util.List;

/**
 * 文件上传 服务层
 *

 */
public interface ISysOssService {

    TableDataInfo<SysOssVo> queryPageList(SysOssBo sysOss, PageQuery pageQuery);

    List<SysOssVo> listByIds(Collection<Long> ossIds);

    String selectUrlByIds(String ossIds);

    String selectNameById(String ossId);

    String selectUrlByIdsLimitTime(String ossIds, Integer second);

    SysOssVo getById(Long ossId);

    SysOssVo upload(MultipartFile file);

    SysOssVo uploadPublic(MultipartFile file, String configKey);

    SysOssVo upload(File file);

    SysOssVo buildResultEntity(String originalfileName, String suffix, String configKey, UploadResult uploadResult);

    Boolean insertByBo(SysOssBo bo);

    void download(Long ossId, HttpServletResponse response) throws IOException;

    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 初始化分片上传任务
     *
     * @param originalFileName 文件原名
     * @return 分片上传对象信息
     */
    SysOssMultipartVo initiateMultipart(String originalFileName);

    /**
     * 上传分段
     *
     * @param multipartBo 分片上传对象信息
     * @return 分片上传对象信息
     */
    SysOssMultipartVo uploadPart(MultipartFile file, SysOssMultipartBo multipartBo);

    /**
     * 获取上传分段进度
     *
     * @param multipartBo 分片上传对象信息
     * @return 分片上传对象信息
     */
    SysOssMultipartVo uploadPartList(SysOssMultipartBo multipartBo);

    /**
     * 中止分段上传任务
     *
     * @param multipartBo 分片上传对象信息
     */
    void abortMultipartUpload(SysOssMultipartBo multipartBo);

    /**
     * 合并分段
     *
     * @param multipartBo 分片上传对象信息
     * @return OSS对象存储视图对象
     */
    SysOssVo completeMultipartUpload(SysOssMultipartBo multipartBo);


    SysOssVo buildResultEntity(SysUploadOssBo uploadOssBo);
}
