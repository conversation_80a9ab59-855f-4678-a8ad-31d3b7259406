package com.jxw.shufang.resource.service.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.resource.service.MergeAiTestPaperService;
import com.jxw.shufang.resource.service.MergePdfService;
import com.jxw.shufang.resource.util.AutoDeleteFileInputStream;
import com.jxw.shufang.student.api.domain.dto.RemoteAiPaperOssUrlDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiRecordOssUrlDTO;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/2/20 11:28
 * @Version 1
 * @Description
 */
@Service
@Slf4j
public class MergeAiTestPaperServiceImpl implements MergeAiTestPaperService, BaseService {
    @Resource
    private MergePdfService mergePdfService;
    @Value("${resource.mergePdf.watermark.prefix:学王书房}")
    private String waterMarkPrefix;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    @Override
    public InputStreamResource mergeAiReport(List<RemoteAiRecordOssUrlDTO> aiRecordStudents) {
        Integer fontSize = 65;
        Float horizontalSpacing = 2f;
        Float verticalSpacing = 12f;
        List<BatchMergePdfDTO> mergePdfList = aiRecordStudents.stream()
            .map(m -> buildBatchMergePdfDTOParam(m.getStudentName(), m.getReportOssUrl(), fontSize, horizontalSpacing, verticalSpacing, getWaterMarkPrefix()))
            .collect(Collectors.toList());

        try {
            File mergePdfFile = mergePdfService.batchMergePdf(mergePdfList);
            return new InputStreamResource(new AutoDeleteFileInputStream(mergePdfFile));
        } catch (IOException e) {
            log.error("合并评测报告失败:{}", e.getMessage(), e);
            throw new ServiceException("合并评测报告失败：".concat(e.getMessage()));
        }
    }

    @Override
    public InputStreamResource mergeAiTestPaper(List<RemoteAiPaperOssUrlDTO> aiPaperInfos) {
        Integer fontSize = 20;
        Float horizontalSpacing = 2.5f;
        Float verticalSpacing = 8.5f;
        List<BatchMergePdfDTO> mergePdfList = aiPaperInfos.stream()
            .map(m -> buildBatchMergePdfDTOParam(m.getStudentName(), m.getPaperPdfOssUrl(), fontSize, horizontalSpacing, verticalSpacing,getWaterMarkPrefix()))
            .toList();

        try {
            File mergePdfFile = mergePdfService.batchMergePdf(mergePdfList);
            return new InputStreamResource(new AutoDeleteFileInputStream(mergePdfFile));
        } catch (IOException e) {
            log.error("合并评测试卷失败:{}", e.getMessage(), e);
            throw new ServiceException("合并评测报告失败：".concat(e.getMessage()));
        }
    }

    private String getWaterMarkPrefix() {

        Long deptId = getDeptId();
        if (ObjectUtils.isEmpty(deptId)) {
            return waterMarkPrefix;
        }
        RemoteDeptVo deptSystemConfig = remoteDeptService.getDeptSystemConfig(deptId);
        if (StringUtils.isBlank(deptSystemConfig.getBrandName())) {
            return waterMarkPrefix;
        } else {
            return deptSystemConfig.getBrandName();
        }
    }

    private static @Nullable Long getDeptId() {
        Long selectDeptId = LoginHelper.getSelectDeptId();
        if (ObjectUtils.isEmpty(selectDeptId)) {
            return  LoginHelper.getDeptId();
        }
        return selectDeptId;
    }

    private  BatchMergePdfDTO buildBatchMergePdfDTOParam(String studentName, String ossFilePath, Integer fontSize,
                                                               Float horizontalSpacing, Float verticalSpacing,String waterMarkPrefix) {
        BatchMergePdfDTO batchMergePdfDTO = new BatchMergePdfDTO();
        batchMergePdfDTO.setFilePath(ossFilePath);
        String waterMark = waterMarkPrefix.concat(" ").concat(studentName);

        batchMergePdfDTO.setWatermark(waterMark);
        batchMergePdfDTO.setFirstLineText(null);
        batchMergePdfDTO.setFontSize(fontSize);
        batchMergePdfDTO.setHorizontalSpacing(horizontalSpacing);
        batchMergePdfDTO.setVerticalSpacing(verticalSpacing);
        batchMergePdfDTO.setBatchId(UuidUtils.generateUuid());
        return batchMergePdfDTO;
    }
}
