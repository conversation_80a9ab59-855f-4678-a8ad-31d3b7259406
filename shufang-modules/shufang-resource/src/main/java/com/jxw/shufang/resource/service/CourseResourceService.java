package com.jxw.shufang.resource.service;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.student.api.domain.bo.DownloadMergeResourceRequest;
import com.jxw.shufang.student.api.domain.dto.RemoteMergeCourseResourceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/26 19:55
 * @Version 1
 * @Description
 */
public interface CourseResourceService {
    List<RemoteMergeCourseResourceDTO> getMergeCourseResource(DownloadMergeResourceRequest mergePdfBO) throws ServiceException;
}
