package com.jxw.shufang.resource.service;

import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/19 9:34
 * @Version 1
 * @Description pdf 相关操作
 */
public interface MergePdfService {
    /**
     * 根据pdf文件路径合并pdf并添加水印,适合多个文件添加不同的水印和文字
     * @param batchMergePdfDTO
     * @return
     */
    File batchMergePdf(List<BatchMergePdfDTO> batchMergePdfDTO) throws IOException;
}
