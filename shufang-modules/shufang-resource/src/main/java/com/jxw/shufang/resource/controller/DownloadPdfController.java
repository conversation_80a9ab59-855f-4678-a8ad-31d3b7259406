package com.jxw.shufang.resource.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.resource.domain.bo.DownloadMergePaperBO;
import com.jxw.shufang.resource.domain.dto.BatchMergePdfDTO;
import com.jxw.shufang.resource.service.MergeAiTestPaperService;
import com.jxw.shufang.resource.service.MergeCourseResourceService;
import com.jxw.shufang.resource.service.CourseResourceService;
import com.jxw.shufang.student.api.RemoteStudentAiRecordService;
import com.jxw.shufang.student.api.RemoteStudyPlanningService;
import com.jxw.shufang.student.api.domain.bo.DownloadMergeResourceRequest;
import com.jxw.shufang.student.api.domain.bo.RemoteDownLoadMergeRecordBO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiPaperOssUrlDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteAiRecordOssUrlDTO;
import com.jxw.shufang.student.api.domain.dto.RemoteMergeCourseResourceDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/26 15:18
 * @Version 1
 * @Description 下载pdf
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/mergeFile")
@Slf4j
public class DownloadPdfController extends BaseController {
    @DubboReference
    private RemoteStudentAiRecordService remoteStudentAiRecordService;
    @Resource
    private MergeCourseResourceService mergeCourseResourceService;
    @Resource
    private MergeAiTestPaperService mergeAiTestPaperService;
    @Resource
    private CourseResourceService resourceServices;

    @DubboReference
    private RemoteStudyPlanningService remoteStudyPlanningService;

    @Value("${mergePdf.aireport.maxSize:10}")
    private  Integer maxSize;

    /**
     * 合并下载课程资源
     *
     * @param mergePdfBO
     * @return
     */
    @PostMapping("/downloadMergeResource")
    @SaCheckPermission("resource:coursePlan:merge:download")
    public ResponseEntity<?> downloadMergeResource(@RequestBody @Validated DownloadMergeResourceRequest mergePdfBO) {
        // 获取资源信息
        List<RemoteMergeCourseResourceDTO> mergeCourseResource = resourceServices.getMergeCourseResource(mergePdfBO);

        if (CollectionUtils.isEmpty(mergeCourseResource)) {
            throw new ServiceException("资源信息不存在，请检查");
        }

        // 执行合并
        List<BatchMergePdfDTO> batchMergePdfDTOList = mergeCourseResourceService.createCourseMergePdfList(mergeCourseResource);
        InputStreamResource inputStreamResource = mergeCourseResourceService.mergeCourseResource(batchMergePdfDTOList);

        // 更新下载记录状态
        remoteStudyPlanningService.batchUpdateDownloadStatus(mergePdfBO.getStudyPlanningRecordIds());

        // 设置 HTTP 响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", "merged.pdf");
        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.APPLICATION_PDF)
            .body(inputStreamResource);
    }

    /**
     * 合并下载评测报告
     *
     * @param recordBO
     * @return
     */
    @PostMapping(value = "/downloadMergeRecord")
    @SaCheckPermission("resource:paperRecord:merge:download")
    public ResponseEntity<InputStreamResource> downloadMergeRecord(@RequestBody @Validated RemoteDownLoadMergeRecordBO recordBO) {
        if(recordBO.getStudentPaperRecordIds().size() > maxSize){
            throw new ServiceException("评测报告数量超出限制");
        }
        // 获取会员ID和评测报告内容
        List<RemoteAiRecordOssUrlDTO> aiRecordStudents = remoteStudentAiRecordService.getStudentRecordInfo(recordBO);
        if (CollectionUtils.isEmpty(aiRecordStudents)) {
            throw new ServiceException("无效的评测报告");
        }

        // 合并评测报告
        InputStreamResource inputStreamResource = mergeAiTestPaperService.mergeAiReport(aiRecordStudents);

        // 设置 HTTP 响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", "merged.pdf");
        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.APPLICATION_PDF)
            .body(inputStreamResource);
    }


    /**
     * 合并下载评测试卷
     */
    @PostMapping(value = "/downloadMergePaper")
    @SaCheckPermission("resource:paperRecord:paper:merge:download")
    public ResponseEntity<InputStreamResource> downloadMergePaper(@RequestBody @Validated DownloadMergePaperBO recordBO) {
        if(recordBO.getStudentPaperRecordIds().size() > maxSize){
            throw new ServiceException("评测试卷数量超出限制");
        }
        List<RemoteAiPaperOssUrlDTO> aiPaperInfos = remoteStudentAiRecordService.getAiPaperInfo(recordBO.getStudentPaperRecordIds());
        if (CollectionUtils.isEmpty(aiPaperInfos)) {
            throw new ServiceException("找不到对应的试卷");
        }

        InputStreamResource inputStreamResource = mergeAiTestPaperService.mergeAiTestPaper(aiPaperInfos);

        // 设置 HTTP 响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", "merged.pdf");
        return ResponseEntity.ok()
            .headers(headers)
            .contentType(MediaType.APPLICATION_PDF)
            .body(inputStreamResource);
    }
}
