package com.jxw.shufang.resource.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 对象存储配置对象 sys_oss_config
 */
@ConfigurationProperties(prefix = "oss")
@SuppressWarnings("ConfigurationProperties")
@Configuration
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SysOssConfigProperties {

    private List<SysOssConfig> configs;

}
