package com.jxw.shufang.resource.domain.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

/**
 * <AUTHOR>
 * @Date 2025/2/24 10:11
 * @Version 1
 * @Description 组装到document的参数
 */
@Data
@NoArgsConstructor
public class WaterMarkToDocumentDTO {
    /**
     * 水印
     */
    private String waterMark;
    /**
     * 字体
     */
    private PDType0Font font;
    /**
     * doc节点
     */
    private PDDocument document;
    /**
     * 页面
     */
    private PDPage page;
    /**
     * 字体号
     */
    private Integer fontSize;
    /**
     * 水平间距
     */
    private Float horizontalSpacing;

    /**
     * 垂直间距
     */
    private Float verticalSpacing;
}
