//package com.jxw.shufang.resource.dubbo;
//
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.DubboService;
//import com.jxw.shufang.common.core.enums.SmsCaptchaResultEnum;
//import com.jxw.shufang.common.core.exception.ServiceException;
//import com.jxw.shufang.common.core.utils.MapstructUtils;
//import com.jxw.shufang.resource.api.RemoteSmsService;
//import com.jxw.shufang.resource.api.domain.RemoteSmsVo;
//import com.jxw.shufang.resource.domain.vo.SysSmsVo;
//import com.jxw.shufang.resource.service.ISysSmsService;
//import org.springframework.stereotype.Service;
//
///**
// * 短信服务
// *
//
// */
//@Slf4j
//@RequiredArgsConstructor
//@Service
//@DubboService
//public class RemoteSmsServiceImpl implements RemoteSmsService {
//
//    private final ISysSmsService sysSmsService;
//
//    ///**
//    // * 发送短信
//    // *
//    // * @param phones     电话号(多个逗号分割)
//    // * @param templateId 模板id
//    // * @param param      模板对应参数
//    // */
//    //public RemoteSmsVo send(String phones, String templateId, LinkedHashMap<String, String> param) throws ServiceException {
//    //    SmsBlend smsBlend = SmsFactory.createSmsBlend(SupplierType.ALIBABA);
//    //    SmsResponse smsResponse = smsBlend.sendMessage(phones, templateId, param);
//    //    RemoteSmsVo sysSms = new RemoteSmsVo();
//    //    sysSms.setIsSuccess(smsResponse.isSuccess());
//    //    sysSms.setMessage(smsResponse.getMessage());
//    //    sysSms.setResponse(JsonUtils.toJsonString(smsResponse));
//    //    return sysSms;
//    //}
//
//    @Override
//    public RemoteSmsVo sendSmsCaptcha(String phones, String code) throws ServiceException {
//        SysSmsVo sysSmsVo = sysSmsService.sendSmsCaptcha(phones, code);
//        return MapstructUtils.convert(sysSmsVo, RemoteSmsVo.class);
//    }
//
//    @Override
//    public SmsCaptchaResultEnum checkSmsCaptcha(String phone, String code,boolean needDelSmsCaptcha) {
//        return sysSmsService.checkSmsCaptcha(phone, code,needDelSmsCaptcha);
//    }
//
//    @Override
//    public void deleteSmsCaptcha(String phone) {
//        sysSmsService.deleteSmsCaptcha(phone);
//    }
//
//
//}
