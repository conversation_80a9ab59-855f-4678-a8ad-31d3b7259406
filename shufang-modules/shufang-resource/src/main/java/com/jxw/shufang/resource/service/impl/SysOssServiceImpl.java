package com.jxw.shufang.resource.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.resource.domain.bo.SysUploadOssBo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.CacheNames;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.file.FileUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.oss.core.OssClient;
import com.jxw.shufang.common.oss.entity.MultipartUploadResult;
import com.jxw.shufang.common.oss.entity.PartUploadResult;
import com.jxw.shufang.common.oss.entity.UploadResult;
import com.jxw.shufang.common.oss.enumd.AccessPolicyType;
import com.jxw.shufang.common.oss.factory.OssFactory;
import com.jxw.shufang.resource.domain.SysOss;
import com.jxw.shufang.resource.domain.bo.SysOssBo;
import com.jxw.shufang.resource.domain.bo.SysOssMultipartBo;
import com.jxw.shufang.resource.domain.vo.SysOssMultipartVo;
import com.jxw.shufang.resource.domain.vo.SysOssVo;
import com.jxw.shufang.resource.mapper.SysOssMapper;
import com.jxw.shufang.resource.service.ISysOssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文件上传 服务层实现
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysOssServiceImpl implements ISysOssService {

    private final SysOssMapper baseMapper;

    @Value("${oss.expire:7200}")
    private String ossExpireTime;

    @Override
    public TableDataInfo<SysOssVo> queryPageList(SysOssBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOss> lqw = buildQueryWrapper(bo);
        Page<SysOssVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<SysOssVo> filterResult = result.getRecords().stream().map(this::matchingUrl).collect(Collectors.toList());
        result.setRecords(filterResult);
        return TableDataInfo.build(result);
    }

    @Override
    public List<SysOssVo> listByIds(Collection<Long> ossIds) {
        List<SysOssVo> list = new ArrayList<>();
        for (Long id : ossIds) {
            SysOssVo vo = SpringUtils.getBean(ISysOssService.class).getById(id);
            if (ObjectUtil.isNotNull(vo)) {
                try {
                    list.add(this.matchingUrl(vo));
                } catch (Exception ignored) {
                    // 如果oss异常无法连接则将数据直接返回
                    list.add(vo);
                }
            }
        }
        return list;
    }

    @Override
    public String selectUrlByIds(String ossIds) {
        List<String> list = new ArrayList<>();
        String[] split = ossIds.split(",");
        for (String idStr : split) {
            Long id = Convert.toLong(idStr);
            SysOssVo vo = SpringUtils.getAopProxy(this).getById(id);
            if (ObjectUtil.isNotNull(vo)) {
                try {
                    list.add(this.matchingUrl(vo).getUrl());
                } catch (Exception ignored) {
                    // 如果oss异常无法连接则将数据直接返回
                    list.add(vo.getUrl());
                }
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    @Override
    public String selectNameById(String ossId) {
        Long id = Convert.toLong(ossId);
        SysOssVo vo = SpringUtils.getAopProxy(this).getById(id);
        if (ObjectUtil.isNotNull(vo)) {
            return vo.getOriginalName();
        }
        return "";
    }

    @Override
    public String selectUrlByIdsLimitTime(String ossIds, Integer second) {
        List<String> list = new ArrayList<>();
        String[] split = ossIds.split(",");
        for (String idStr : split) {
            Long id = Convert.toLong(idStr);
            SysOssVo vo = SpringUtils.getAopProxy(this).getById(id);
            if (ObjectUtil.isNotNull(vo)) {
                try {
                    list.add(this.matchingPrivateUrl(vo, second).getUrl());
                } catch (Exception ignored) {
                    // 如果oss异常无法连接则将数据直接返回
                    list.add(vo.getUrl());
                }
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }


    private LambdaQueryWrapper<SysOss> buildQueryWrapper(SysOssBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOss> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), SysOss::getFileName, bo.getFileName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), SysOss::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), SysOss::getFileSuffix, bo.getFileSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), SysOss::getUrl, bo.getUrl());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            SysOss::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.eq(ObjectUtil.isNotNull(bo.getCreateBy()), SysOss::getCreateBy, bo.getCreateBy());
        lqw.eq(StringUtils.isNotBlank(bo.getService()), SysOss::getService, bo.getService());
        lqw.in(CollUtil.isNotEmpty(bo.getOssIds()), SysOss::getOssId, bo.getOssIds());
        lqw.orderByAsc(SysOss::getOssId);
        return lqw;
    }

    @Cacheable(cacheNames = CacheNames.SYS_OSS, key = "#ossId")
    @Override
    public SysOssVo getById(Long ossId) {
        return baseMapper.selectVoById(ossId);
    }

    @Override
    public void download(Long ossId, HttpServletResponse response) throws IOException {
        SysOssVo sysOss = SpringUtils.getAopProxy(this).getById(ossId);
        if (ObjectUtil.isNull(sysOss)) {
            throw new ServiceException("文件数据不存在!");
        }
        FileUtils.setAttachmentResponseHeader(response, sysOss.getOriginalName());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        OssClient storage = OssFactory.instance(sysOss.getService());
        try (InputStream inputStream = storage.getObjectContent(sysOss.getUrl())) {
            int available = inputStream.available();
            IoUtil.copy(inputStream, response.getOutputStream(), available);
            response.setContentLength(available);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SysOssVo upload(MultipartFile file) {
        return uploadPublic(file, null);
    }

    @Override
    public SysOssVo uploadPublic(MultipartFile file, String configKey) {
        String originalfileName = file.getOriginalFilename();
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage;
        if (StringUtils.isBlank(configKey)) {
            storage = OssFactory.instance();
        } else {
            storage = OssFactory.instance(configKey);
        }
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        return buildResultEntity(originalfileName, suffix, storage.getConfigKey(), uploadResult);
    }

    @Override
    public SysOssVo upload(File file) {
        String originalfileName = file.getName();
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult = storage.uploadSuffix(file, suffix);
        // 保存文件信息
        return buildResultEntity(originalfileName, suffix, storage.getConfigKey(), uploadResult);
    }

    @Override
    public SysOssVo buildResultEntity(String originalfileName, String suffix, String configKey, UploadResult uploadResult) {
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(originalfileName);
        oss.setService(configKey);
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        return this.matchingUrl(sysOssVo);
    }

    @Override
    public Boolean insertByBo(SysOssBo bo) {
        SysOss oss = BeanUtil.toBean(bo, SysOss.class);
        boolean flag = baseMapper.insert(oss) > 0;
        if (flag) {
            bo.setOssId(oss.getOssId());
        }
        return flag;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        List<SysOss> list = baseMapper.selectBatchIds(ids);
        for (SysOss sysOss : list) {
            OssClient storage = OssFactory.instance(sysOss.getService());
            storage.delete(sysOss.getUrl());
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 匹配Url
     *
     * @param oss OSS对象
     * @return oss 匹配Url的OSS对象
     */
    private SysOssVo matchingUrl(SysOssVo oss) {
        OssClient storage = OssFactory.instance(oss.getService());
        // 仅修改桶类型为 private 的URL，临时URL时长为2小时
        if (AccessPolicyType.PRIVATE == storage.getAccessPolicy()) {
            log.info("缓存失效时间配置为{}秒", ossExpireTime);
            oss.setUrl(storage.getPrivateUrl(oss.getFileName(), Integer.parseInt(ossExpireTime)));
        }
        return oss;
    }

    private SysOssVo matchingPrivateUrl(SysOssVo oss, Integer second) {
        OssClient storage = OssFactory.instance(oss.getService());
        if (AccessPolicyType.PRIVATE == storage.getAccessPolicy()) {
            oss.setUrl(storage.getPrivateUrl(oss.getFileName(), second));
        }
        return oss;
    }


    /**
     * 匹配Url
     * 初始化分片上传任务
     *
     * @param originalFileName 文件原名
     * @return 分片上传对象信息
     */
    @Override
    public SysOssMultipartVo initiateMultipart(String originalFileName) {
        //获取文件后缀名
        String suffix = StringUtils.substring(originalFileName, originalFileName.lastIndexOf("."), originalFileName.length());
        //获取oss实例
        OssClient storage = OssFactory.instance();
        //创建上传任务
        MultipartUploadResult multipart = storage.initiateMultipart(suffix);
        SysOssMultipartVo multipartVo = new SysOssMultipartVo();
        multipartVo.setUploadId(multipart.getUploadId());
        multipartVo.setFilename(multipart.getFilename());
        return multipartVo;
    }

    /**
     * 上传分段
     */
    @Override
    public SysOssMultipartVo uploadPart(MultipartFile file, SysOssMultipartBo multipartBo) {
        //获取文件原名
        String uploadId = multipartBo.getUploadId();
        String filename = multipartBo.getFilename();
        OssClient storage = OssFactory.instance();
        PartUploadResult uploadPart;
        try {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(file.getBytes());
            uploadPart = storage.uploadPart(byteArrayInputStream, uploadId, filename, multipartBo.getPartNumber(), multipartBo.getPartSize(), multipartBo.getMd5Digest());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        SysOssMultipartVo multipartVo = new SysOssMultipartVo();
        multipartVo.setUploadId(uploadId);
        multipartVo.setFilename(filename);
        multipartVo.setPartNumber(uploadPart.getPartNumber());
        multipartVo.setETag(uploadPart.getETag());
        return multipartVo;
    }

    /**
     * 获取上传分段进度
     *
     * @param multipartBo 分片上传对象信息
     * @return 分片上传对象信息
     */
    @Override
    public SysOssMultipartVo uploadPartList(SysOssMultipartBo multipartBo) {
        OssClient storage = OssFactory.instance();
        List<PartUploadResult> partList = storage.uploadPartList(multipartBo.getUploadId(), multipartBo.getFilename());
        SysOssMultipartVo multipartVo = new SysOssMultipartVo();
        if (CollUtil.isNotEmpty(partList)) {
            multipartVo.setPartUploadList(
                partList.stream().map(
                        x -> new PartUploadResult(x.getPartNumber(), x.getETag()))
                    .collect(Collectors.toList()));
        }
        return multipartVo;
    }


    /**
     * 中止分段上传任务
     *
     * @param multipartBo 分片上传对象信息
     */
    @Override
    public void abortMultipartUpload(SysOssMultipartBo multipartBo) {
        OssClient storage = OssFactory.instance();
        storage.abortMultipartUpload(multipartBo.getUploadId(), multipartBo.getFilename());
    }


    /**
     * 合并分段
     *
     * @param multipartBo 分片上传对象信息
     * @return OSS对象存储视图对象
     */
    @Override
    public SysOssVo completeMultipartUpload(SysOssMultipartBo multipartBo) {
        //获取文件原名
        String originalfileName = multipartBo.getOriginalfileName();
        //获取文件后缀名
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        //获取默认实例
        OssClient storage = OssFactory.instance();
        //合并分段
        UploadResult uploadResult = storage.completeMultipartUpload(multipartBo.getUploadId(), multipartBo.getFilename());
        // 保存文件信息
        return buildResultEntity(originalfileName, suffix, storage.getConfigKey(), uploadResult);
    }

    @Override
    public SysOssVo buildResultEntity(SysUploadOssBo uploadOssBo) {
        SysOss oss = new SysOss();
        oss.setUrl(uploadOssBo.getUrl());
        oss.setFileSuffix(uploadOssBo.getSuffix());
        oss.setFileName(uploadOssBo.getFilename());
        oss.setOriginalName(uploadOssBo.getOriginalFileName());
        oss.setService(uploadOssBo.getService());
        baseMapper.insert(oss);
        SysOssVo sysOssVo = MapstructUtils.convert(oss, SysOssVo.class);
        return this.matchingUrl(sysOssVo);
    }
}
