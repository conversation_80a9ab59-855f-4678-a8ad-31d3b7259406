<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-resource</artifactId>

    <description>
        shufang-resource资源服务
    </description>

    <dependencies>
        <!-- pdf-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.33</version>
        </dependency>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-sentinel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-sms</artifactId>
        </dependency>

        <!-- 短信 用哪个导入哪个依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java-sms</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-websocket</artifactId>
        </dependency>

        <!-- jxw Api System -->
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-resource</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-student</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-extresource</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-wxmp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
