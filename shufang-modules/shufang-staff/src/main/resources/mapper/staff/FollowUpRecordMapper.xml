<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.staff.mapper.FollowUpRecordMapper">

    <resultMap id="followUpRecordResult" type="com.jxw.shufang.staff.domain.vo.FollowUpRecordVo">
        <id property="followUpRecordId" column="follow_up_record_id"/>
        <association property="branchStaff" column="branch_staff_id" resultMap="branchStaffResult"/>
    </resultMap>

    <resultMap type="com.jxw.shufang.staff.domain.vo.BranchStaffVo" id="branchStaffResult">
        <id property="branchStaffId" column="branch_staff_id"/>
        <result property="createDept" column="staff_create_dept"/>
        <result property="createBy" column="staff_create_by"/>
        <result property="createTime" column="staff_create_time"/>
        <result property="updateBy" column="staff_update_by"/>
        <result property="updateTime" column="staff_update_time"/>
    </resultMap>

    <select id="selectPageList" resultMap="followUpRecordResult">
        select t.follow_up_record_id,
               t.student_id,
               t.follow_up_type,
               t.follow_up_content,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               b.branch_staff_id,
               b.check_work_attendance_id,
               b.branch_id,
               b.real_avatar,
               b.wechat_qr_code,
               b.create_dept as staff_create_dept,
               b.create_by as staff_create_by,
               b.create_time as staff_create_time,
               b.update_by as staff_update_by,
               b.update_time as staff_update_time
        from follow_up_record t
                 left join branch_staff b on b.create_by = t.create_by
        ${ew.customSqlSegment}
    </select>
</mapper>
