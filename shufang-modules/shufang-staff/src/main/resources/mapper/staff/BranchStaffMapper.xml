<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.staff.mapper.BranchStaffMapper">

    <resultMap type="com.jxw.shufang.staff.domain.vo.BranchStaffVo" id="branchStaffResult">
        <id property="branchStaffId" column="branch_staff_id"/>
        <association property="branchStaffStatistic" column="branch_staff_id" resultMap="BranchStaffStatisticResult"/>
    </resultMap>

    <resultMap id="BranchStaffStatisticResult" type="com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo">
    </resultMap>

    <select id="selectPageList" resultMap="branchStaffResult">
        select t.*
        from branch_staff t
        ${ew.getCustomSqlSegment}
    </select>
    <select id="queryStaffIdList" resultType="Long">
        select t.branch_staff_id
        from branch_staff t
            ${ew.getCustomSqlSegment}
    </select>
    <select id="selectStaffList" resultMap="branchStaffResult">
        select t.*
        from branch_staff t
            ${ew.getCustomSqlSegment}
    </select>

    <select id="staffStatisticPageList" resultMap="branchStaffResult">
        select t.*,
            bss.branch_staff_statistic_id,
            bss.total_student_num,
            bss.experience_num,
            bss.new_sign_num,
            bss.renew_num,
            bss.sign_amount,
            bss.study_video_num,
            bss.marking_num,
            bss.marking_rate,
            bss.feedback_num,
            bss.feedback_rate,
            bss.follow_up_num,
            bss.follow_up_rate,
            bss.carry_forward_amount,
            bss.day_study_num,
            bss.start_time,
            bss.end_time
        from branch_staff t
        LEFT JOIN branch_staff_statistic bss ON t.branch_staff_id = bss.branch_staff_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="staffStatisticGroupPageList" resultMap="branchStaffResult">
        SELECT t.*,
               IFNULL(bss.total_student_num, 0) as total_student_num,
               IFNULL(bss.experience_num, 0) as experience_num,
               IFNULL(bss.new_sign_num, 0) as new_sign_num,
               IFNULL(bss.renew_num, 0) as renew_num,
               IFNULL(bss.sign_amount, 0) as sign_amount,
               IFNULL(bss.study_video_num, 0) as study_video_num,
               IFNULL(bss.marking_num, 0) as marking_num,
               IFNULL(bss.marking_rate, 0) as marking_rate,
               IFNULL(bss.feedback_num, 0) as feedback_num,
               IFNULL(bss.feedback_rate, 0) as feedback_rate,
               IFNULL(bss.follow_up_num, 0) as follow_up_num,
               IFNULL(bss.follow_up_rate, 0) as follow_up_rate,
               IFNULL(bss.carry_forward_amount, 0) as carry_forward_amount,
               IFNULL(bss.day_study_num, 0) as day_study_num
        FROM branch_staff t
                 LEFT JOIN (select branch_staff_id,
                                   #拿最新的值
                                   substring(group_concat(total_student_num order by start_time desc), 1)     as total_student_num,
                                   sum(experience_num)                                                        as experience_num,
                                   sum(new_sign_num)                                                          as new_sign_num,
                                   sum(renew_num)                                                             as renew_num,
                                   sum(sign_amount)                                                           as sign_amount,
                                   sum(study_video_num)                                                       as study_video_num,
                                   sum(marking_num)                                                           as marking_num,
                                   ROUND((sum(marking_num) / IF(sum(study_video_num) = 0, 1, sum(study_video_num)))*100,0) as marking_rate,
                                   sum(feedback_num)                                                          as feedback_num,
                                   ROUND((sum(feedback_rate) / IF(sum(day_study_num) = 0, 1, sum(day_study_num)))*100,0)   as feedback_rate,
                                   sum(follow_up_num)                                                         as follow_up_num,
                                   ROUND((sum(follow_up_num) / IF(sum(day_study_num) = 0, 1, sum(day_study_num)))*100,0)   as follow_up_rate,
                                   sum(carry_forward_amount)                                                  as carry_forward_amount,
                                   sum(day_study_num)                                                         as day_study_num
                            from branch_staff_statistic
                            where start_time between #{staffStatisticStartTime} and #{staffStatisticEndTime}
                            group by branch_staff_id) bss
                           ON t.branch_staff_id = bss.branch_staff_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="staffStatisticList" resultMap="branchStaffResult">
        select t.*,
               bss.branch_staff_statistic_id,
               bss.total_student_num,
               bss.experience_num,
               bss.new_sign_num,
               bss.renew_num,
               bss.sign_amount,
               bss.study_video_num,
               bss.marking_num,
               bss.marking_rate,
               bss.feedback_num,
               bss.feedback_rate,
               bss.follow_up_num,
               bss.follow_up_rate,
               bss.carry_forward_amount,
               bss.day_study_num,
               bss.start_time,
               bss.end_time
        from branch_staff t
                 LEFT JOIN branch_staff_statistic bss ON t.branch_staff_id = bss.branch_staff_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="staffStatisticGroupList" resultMap="branchStaffResult">
        SELECT t.*,
               IFNULL(bss.total_student_num, 0) as total_student_num,
               IFNULL(bss.experience_num, 0) as experience_num,
               IFNULL(bss.new_sign_num, 0) as new_sign_num,
               IFNULL(bss.renew_num, 0) as renew_num,
               IFNULL(bss.sign_amount, 0) as sign_amount,
               IFNULL(bss.study_video_num, 0) as study_video_num,
               IFNULL(bss.marking_num, 0) as marking_num,
               IFNULL(bss.marking_rate, 0) as marking_rate,
               IFNULL(bss.feedback_num, 0) as feedback_num,
               IFNULL(bss.feedback_rate, 0) as feedback_rate,
               IFNULL(bss.follow_up_num, 0) as follow_up_num,
               IFNULL(bss.follow_up_rate, 0) as follow_up_rate,
               IFNULL(bss.carry_forward_amount, 0) as carry_forward_amount,
               IFNULL(bss.day_study_num, 0) as day_study_num
        FROM branch_staff t
                 LEFT JOIN (select branch_staff_id,
                                   #拿最新的值
                                   substring(group_concat(total_student_num order by start_time desc), 1)     as total_student_num,
                                   sum(experience_num)                                                        as experience_num,
                                   sum(new_sign_num)                                                          as new_sign_num,
                                   sum(renew_num)                                                             as renew_num,
                                   sum(sign_amount)                                                           as sign_amount,
                                   sum(study_video_num)                                                       as study_video_num,
                                   sum(marking_num)                                                           as marking_num,
                                   ROUND((sum(marking_num) / IF(sum(study_video_num) = 0, 1, sum(study_video_num)))*100,0) as marking_rate,
                                   sum(feedback_num)                                                          as feedback_num,
                                   ROUND((sum(feedback_rate) / IF(sum(day_study_num) = 0, 1, sum(day_study_num)))*100,0)   as feedback_rate,
                                   sum(follow_up_num)                                                         as follow_up_num,
                                   ROUND((sum(follow_up_num) / IF(sum(day_study_num) = 0, 1, sum(day_study_num)))*100,0)   as follow_up_rate,
                                   sum(carry_forward_amount)                                                  as carry_forward_amount,
                                   sum(day_study_num)                                                         as day_study_num
                            from branch_staff_statistic
                            where start_time between #{staffStatisticStartTime} and #{staffStatisticEndTime}
                            group by branch_staff_id) bss
                           ON t.branch_staff_id = bss.branch_staff_id
            ${ew.getCustomSqlSegment}

    </select>

    <select id="getConsultantByName" resultType="com.jxw.shufang.staff.domain.vo.ConsultantInfoVO">
        select sys_user.nick_name,
               branch_staff.branch_staff_id
        from branch_staff
            left join sys_user on sys_user.user_id = branch_staff.create_by
            left join sys_user_role on sys_user_role.user_id = sys_user.user_id
        <where>
            <if test="consultantNameDTO.branchIds !=null and consultantNameDTO.branchIds.size() >0">
                 and  branch_staff.branch_id in
                <foreach collection="consultantNameDTO.branchIds" item="branchId" separator="," open="(" close=")">
                    #{branchId}
                </foreach>
                />
            </if>
            <if test="consultantNameDTO.roles !=null and consultantNameDTO.roles.size() >0">
                and  sys_user_role.role_id in
                <foreach collection="consultantNameDTO.roles" item="roleId" separator="," open="(" close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="consultantNameDTO.consultantName !=null and consultantNameDTO.consultantName !=''">
                and sys_user.nick_name like concat(#{consultantNameDTO.consultantName},'%')
            </if>
        </where>
        order by sys_user.user_id asc
    </select>
</mapper>
