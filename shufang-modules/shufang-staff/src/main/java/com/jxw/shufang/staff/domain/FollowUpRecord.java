package com.jxw.shufang.staff.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 跟进记录对象 follow_up_record
 *
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("follow_up_record")
public class FollowUpRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录id
     */
    @TableId(value = "follow_up_record_id")
    private Long followUpRecordId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 跟进类型（对应字典值，如微信沟通、电话访问、登门拜访）
     */
    private String followUpType;

    /**
     * 跟进内容（包含文字、图片、音频）
     */
    private String followUpContent;


}
