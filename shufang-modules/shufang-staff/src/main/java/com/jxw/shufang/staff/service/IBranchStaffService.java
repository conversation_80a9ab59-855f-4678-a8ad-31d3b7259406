package com.jxw.shufang.staff.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.staff.domain.BranchStaff;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.dto.QueryConsultantNameDTO;
import com.jxw.shufang.staff.domain.vo.BranchStaffQrCodeVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.domain.vo.ConsultantInfoVO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 分店员工
（其他属性采用sys_user对应数据）Service接口
 *
 * @date 2024-02-27
 */
public interface IBranchStaffService {

    /**
     * 查询分店员工（其他属性采用sys_user对应数据）
     */
    BranchStaffVo queryById(Long branchStaffId);

    /**
     * 查询分店员工（其他属性采用sys_user对应数据）列表
     */
    TableDataInfo<BranchStaffVo> queryPageList(BranchStaffBo bo, PageQuery pageQuery);

    /**
     * 查询分店员工（其他属性采用sys_user对应数据）列表
     */
    List<BranchStaffVo> queryList(BranchStaffBo bo);

    /**
     * 新增分店员工（其他属性采用sys_user对应数据）
     */
    Boolean insertByBo(BranchStaffBo bo);

    /**
     * 修改分店员工（其他属性采用sys_user对应数据）
     */
    Boolean updateByBo(BranchStaffBo bo);

    /**
     * 校验并批量删除分店员工（其他属性采用sys_user对应数据）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void changUserStatus(Long userId,String userStatus);

    /**
     * 查询员工id列表
     *
     * @param branchStaffBo 分行员工bo
     * @date 2024/03/04 03:16:27
     */
    List<Long> queryStaffIdList(BranchStaffBo branchStaffBo);

    /**
     * 查询人员列表
     *
     *
     * @date 2024/03/04 03:45:39
     */
    List<BranchStaffVo> queryStaffOptionList(BranchStaffBo bo);

    /**
     * 查询会员顾问下的所有会员
     *
     *
     * @date 2024/04/05 03:45:39
     */
    List<RemoteStudentVo> getStudentListByStaffId(Long branchStaffId,String nameWithPhone);

    /**
     * 交接会员
     *
     *
     * @date 2024/04/05 03:45:39
     */
    Boolean handoverMember(List<Long> studentIds,Long staffId);

    TableDataInfo<BranchStaffVo> staffStatisticPageList(BranchStaffBo bo, PageQuery pageQuery);

    List<BranchStaffVo> staffStatisticList(BranchStaffBo bo);

    BranchStaff queryBranchStaffById(Long branchStaffId);

    void cleanCache();

    /**
     * 根据用户id查询分店员工信息
     * @param userId
     * @return
     */
    BranchStaffVo queryByUserId(Long userId);

    /**
     * 查询分店员工信息
     *
     * @param branchStaffId 分支机构员工id
     *
     * @date 2024/06/06 10:04:02
     */
    BranchStaffVo queryBranchStaffInfo(Long branchStaffId);

    BranchStaffStatisticVo getTimelyStatistics(Long branchStaffId, Date startDate, Date endDate);

    /**
     * 根据会员顾问名称查询会员顾问信息
     *
     * @param consultantNameDTO
     * @return
     */
    List<ConsultantInfoVO> getConsultantByName(QueryConsultantNameDTO consultantNameDTO);

    /**
     * 获取员工设置的二维码
     *
     * @param userId
     * @param codeType
     * @return
     */
    BranchStaffQrCodeVo getStaffQrCode(Long userId, String codeType);

    /**
     * 返回登录账号的员工id
     *
     * @return
     */
    BranchStaffVo getPresentStaffId();
}
