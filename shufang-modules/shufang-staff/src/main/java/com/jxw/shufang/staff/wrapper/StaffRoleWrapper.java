package com.jxw.shufang.staff.wrapper;

import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.staff.config.StaffRoleProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: cyj
 * @date: 2025/7/18
 * @Description: staffRole包装类
 */
@Component
@RequiredArgsConstructor
public class StaffRoleWrapper {

    private final StaffRoleProperties staffRoleProperties;

    private Set<Long> disabledRoleIds() {
        return staffRoleProperties.getDisabledIds();
    }

    public boolean isRoleDisabled(StaffRole role) {
        return disabledRoleIds().contains(role.getRoleId());
    }

    public List<StaffRole> getEnabledRoles() {
        return Arrays.stream(StaffRole.values()).filter(role -> !isRoleDisabled(role)).collect(Collectors.toList());
    }

    public List<Map<String, Object>> getStaffRoleInfo() {
        return StaffRole.getStaffRoleInfo(getEnabledRoles());
    }
}
