package com.jxw.shufang.staff.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.staff.domain.dto.QueryConsultantNameDTO;
import com.jxw.shufang.staff.domain.vo.ConsultantInfoVO;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.staff.domain.BranchStaff;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;

import java.util.Date;
import java.util.List;

/**
 * 分店员工
 * （其他属性采用sys_user对应数据）Mapper接口
 *
 * @date 2024-02-27
 */
public interface BranchStaffMapper extends BaseMapperPlus<BranchStaff, BranchStaffVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    Page<BranchStaffVo> selectPageList(@Param("page") Page<BranchStaff> build, @Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw);

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    List<BranchStaffVo> selectStaffList(@Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw);


    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    List<Long> queryStaffIdList(@Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw);

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    Page<BranchStaffVo> staffStatisticPageList(@Param("page") Page<BranchStaff> build, @Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw);

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    Page<BranchStaffVo> staffStatisticGroupPageList(@Param("page") Page<BranchStaff> build, @Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw, @Param("staffStatisticStartTime") Date staffStatisticStartTime, @Param("staffStatisticEndTime") Date staffStatisticEndTime);

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    List<BranchStaffVo> staffStatisticList(@Param(Constants.WRAPPER) QueryWrapper<BranchStaff> queryWrapper);


    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @BranchColumn(key = "deptName", value = "t.create_dept")
    List<BranchStaffVo> staffStatisticGroupList(@Param(Constants.WRAPPER) QueryWrapper<BranchStaff> lqw, @Param("staffStatisticStartTime") Date staffStatisticStartTime, @Param("staffStatisticEndTime") Date staffStatisticEndTime);

    List<ConsultantInfoVO> getConsultantByName(@Param("consultantNameDTO") QueryConsultantNameDTO consultantNameDTO);
}
