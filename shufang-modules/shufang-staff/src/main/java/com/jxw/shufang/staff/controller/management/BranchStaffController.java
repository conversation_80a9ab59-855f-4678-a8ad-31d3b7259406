package com.jxw.shufang.staff.controller.management;

import java.util.*;
import java.util.stream.Collectors;

import com.jxw.shufang.staff.wrapper.StaffRoleWrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.StaffPost;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.dto.QueryConsultantNameDTO;
import com.jxw.shufang.staff.domain.excel.BranchStaffExcel;
import com.jxw.shufang.staff.domain.vo.BranchStaffQrCodeVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.domain.vo.ConsultantInfoVO;
import com.jxw.shufang.staff.service.IBranchStaffService;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 分店员工

 * 前端访问路由地址为:/staff/branch_staff
 *
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/branchStaff")
public class BranchStaffController extends BaseController {

    private final IBranchStaffService branchStaffService;

    private final StaffRoleWrapper staffRoleWrapper;

    /**
     * 查询分店员工列表
     */
    @SaCheckPermission("staff:branchStaff:list")
    @GetMapping("/list")
    public TableDataInfo<BranchStaffVo> list(BranchStaffBo bo, PageQuery pageQuery) {
        bo.setWithBranchInfo(Boolean.TRUE);
        bo.setWithStudentCountInfo(Boolean.TRUE);
        bo.setWithSysUserInfo(Boolean.TRUE);
        return branchStaffService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出分店员工列表
     */
    @SaCheckPermission("staff:branchStaff:export")
    @Log(title = "分店员工", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchStaffBo bo, HttpServletResponse response) {
        bo.setWithBranchInfo(Boolean.TRUE);
        bo.setWithStudentCountInfo(Boolean.TRUE);
        bo.setWithSysUserInfo(Boolean.TRUE);
        List<BranchStaffVo> list = branchStaffService.queryList(bo);
        List<BranchStaffExcel> branchStaffExcels = new ArrayList<>();
        for (BranchStaffVo branchStaffVo : list) {
            BranchStaffExcel branchStaffExcel = new BranchStaffExcel();
            branchStaffExcel.setStaffName(branchStaffVo.getUser().getNickName());
            branchStaffExcel.setStaffAccount(branchStaffVo.getUser().getUserName());
            branchStaffExcel.setStaffSex(branchStaffVo.getUser().getSex());
            List<String> roleNameList = branchStaffVo.getRoleNameList();
            if (roleNameList != null && !roleNameList.isEmpty()) {
                branchStaffExcel.setRoleNames(String.join(",", roleNameList));
            }
            branchStaffExcel.setAllStudentCount(branchStaffVo.getTotalStudentCount());
            branchStaffExcel.setEffectiveStudentCount(branchStaffVo.getChargeStudentCount());
            branchStaffExcel.setCheckWorkAttendanceId(branchStaffVo.getCheckWorkAttendanceId());
            branchStaffExcel.setStatus(branchStaffVo.getUser().getStatus());
            branchStaffExcel.setCreateTime(branchStaffVo.getCreateTime());
            branchStaffExcel.setBranchName(branchStaffVo.getBranch().getBranchName());
            branchStaffExcels.add(branchStaffExcel);
        }
        ExcelUtil.exportExcel(branchStaffExcels, "分店员工", BranchStaffExcel.class, response);
    }

    /**
     * 获取分店员工详细信息
     *
     * @param branchStaffId 主键
     */
    @SaCheckPermission("staff:branchStaff:query")
    @GetMapping("/{branchStaffId}")
    public R<BranchStaffVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long branchStaffId) {
        return R.ok(branchStaffService.queryById(branchStaffId));
    }

    /**
     * 新增分店员工
     */
    @SaCheckPermission("staff:branchStaff:add")
    @Log(title = "分店员工", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchStaffBo bo) {
        if (LoginHelper.isBranchUser()&&LoginHelper.getBranchId()!=null){
            bo.setBranchId(LoginHelper.getBranchId());
        }else if (bo.getBranchId() == null){
            return R.fail("分店员工所属分店不能为空");
        }
        return toAjax(branchStaffService.insertByBo(bo));
    }

    /**
     * 修改分店员工
     */
    @SaCheckPermission("staff:branchStaff:edit")
    @Log(title = "分店员工", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchStaffBo bo) {
        return toAjax(branchStaffService.updateByBo(bo));
    }

    /**
     * 禁用分店员工
     * @param userId 用户Id
     * @param userStatus 用户状态
     */
    @SaCheckPermission("staff:branchStaff:edit")
    @Log(title = "分店员工", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/changUserStatus")
    public R<Void> changUserStatus(@NotNull(message = "用户Id不能为空")
                                Long userId,@NotBlank(message = "用户状态不能为空")String userStatus) {
        branchStaffService.changUserStatus(userId,userStatus);
        return R.ok();
    }

    /**
     * 查询分店员工可选的身份（岗位）
     */
    @GetMapping("/staffPostList")
    public R<List<Map<String,Object>>> staffPostList()  {
        return R.ok(StaffPost.getStaffPostInfo());
    }

    /**
     * 查询分店员工可选的角色
     */
    @GetMapping("/staffRoleList")
    public R<List<Map<String,Object>>> staffRoleList() {
        return R.ok(staffRoleWrapper.getStaffRoleInfo());
    }


    /**
     * 查询销售顾问
     */
    @GetMapping("/salesPersonList")
    public R<List<BranchStaffVo>> salesPersonList(BranchStaffBo bo) {
        return getRoleStaffList(bo, StaffRole.SALES_CONSULTANT);
    }

    /**
     * 查询销售顾问
     */
    @GetMapping("/executiveStoreManagerList")
    public R<List<BranchStaffVo>> executiveStoreManagerList(BranchStaffBo bo) {
        return getRoleStaffList(bo, StaffRole.EXECUTIVE_STORE_MANAGER);
    }

    /**
     * 查询会员顾问
     */
    @GetMapping("/memberConsultantList")
    public R<List<BranchStaffVo>> memberConsultantList(BranchStaffBo bo) {
        return getRoleStaffList(bo, StaffRole.MEMBER_CONSULTANT);
    }

    /**
     * 查询会员顾问
     */
    @GetMapping("/allStaff")
    public R<List<BranchStaffVo>> allStaffList(BranchStaffBo bo) {
        return getRoleStaffList(bo, staffRoleWrapper.getEnabledRoles().toArray(new StaffRole[0]));
    }

    /**
     * 获取crm的市场人员，对应系统角色：crm市场、会员顾问、执行店长。用于在公所有池的会员分配市场人员，以及转入上述池（公海、意向）中选择的负责人接口
     */
    @GetMapping("/crm/marketerList")
    public R<List<BranchStaffVo>> crmMarketerList(BranchStaffBo bo) {
        return getRoleStaffList(bo, StaffRole.CRM_MARKETER, StaffRole.MEMBER_CONSULTANT,
            StaffRole.EXECUTIVE_STORE_MANAGER);
    }

    /**
     * 获取crm的伴学人员，对应系统角色：会员顾问、执行店长。用于在答应到店池、体验池、在籍池的会员分配办学人员，以及转入上述池中选择的负责人接口
     */
    @GetMapping("/crm/consultantList")
    public R<List<BranchStaffVo>> listCrmResponsibleStaff(BranchStaffBo bo) {
        return getRoleStaffList(bo, StaffRole.MEMBER_CONSULTANT, StaffRole.EXECUTIVE_STORE_MANAGER);
    }

    /**
     * 获取角色员工列表
     *
     * @param bo
     * @param staffRoles
     * @return
     */
    private R<List<BranchStaffVo>> getRoleStaffList(BranchStaffBo bo, StaffRole... staffRoles) {
        if (LoginHelper.getBranchId() != null) {
            bo.setBranchId(LoginHelper.getBranchId());
        }
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }
        if (null != staffRoles && staffRoles.length > 0) {
            bo.setRoleIds(Arrays.stream(staffRoles).map(StaffRole::getRoleId).collect(Collectors.toList()));
        }
        return R.ok(branchStaffService.queryStaffOptionList(bo));
    }

    /**
     * 查门店下的员工列表
     */
    @GetMapping("/staffListByBranchId")
    public R<List<BranchStaffVo>> staffListByBranchId(Long branchId) {
        BranchStaffBo bo = new BranchStaffBo();
        if (branchId == null){
            bo.setBranchId(LoginHelper.getBranchId());
            bo.setBranchIdList(LoginHelper.getBranchIdList());
        }else{
            bo.setBranchId(branchId);
        }
        return R.ok(branchStaffService.queryStaffOptionList(bo));
    }

    /**
     * 查询会员顾问下的所有会员
     */
    @GetMapping("/getStudentListByStaffId")
    public R<List<RemoteStudentVo>> getStudentListByStaffId(Long branchStaffId,String nameWithPhone) {
        return R.ok( branchStaffService.getStudentListByStaffId(branchStaffId,nameWithPhone));
    }

    /**
     * 交接会员
     */
    @SaCheckPermission("student:consultantRecord:edit")
    @Log(title = "会员交接", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/handoverMember")
    public R<Void> handoverMember(@RequestBody BranchStaffBo branchStaffBo) {
        return toAjax(( branchStaffService.handoverMember(branchStaffBo.getStudentIds(),branchStaffBo.getBranchStaffId())));
    }


    /**
     * 根据顾问姓名查询会员顾问(模糊搜索)
     *
     * @param consultantName consultantName
     */
    @GetMapping("/getConsultant")
    public R<List<ConsultantInfoVO>> getConsultant(String consultantName) {
        QueryConsultantNameDTO consultantNameDTO = new QueryConsultantNameDTO();
        consultantNameDTO.setConsultantName(consultantName);
        consultantNameDTO.setBranchIds(this.getBranchId());
        consultantNameDTO.setRoles(Collections.singletonList(StaffRole.MEMBER_CONSULTANT.getRoleId()));
        List<ConsultantInfoVO> consultantInfoVOList = branchStaffService.getConsultantByName(consultantNameDTO);
        return R.ok(consultantInfoVOList);
    }

    private List<Long> getBranchId() {
        Set<Long> result = new HashSet<>();
        if (LoginHelper.getBranchId() != null) {
            result.add(LoginHelper.getBranchId());
        }
        if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())) {
            result.addAll(LoginHelper.getBranchIdList());
        }
        return new ArrayList<>(result);
    }

    /**
     * 通过用户id获取用户的微信二维码
     *
     * @param userId 用户id
     * @param codeType 二维码类型 WX、PROMOTION
     * @return
     */
    @GetMapping("/getUserQrcode")
    public R<List<BranchStaffQrCodeVo>> getUserQrcode(Long userId, String codeType) {
        return R.ok(Collections.singletonList(branchStaffService.getStaffQrCode(userId, codeType)));
    }

    /**
     * 获取branchStaffId （如果当前的账号是店员，返回店员的id）
     *
     * @return
     */
    @GetMapping("/getStaffId")
    public R<BranchStaffVo> getStaffId() {
        return R.ok(branchStaffService.getPresentStaffId());
    }
}
