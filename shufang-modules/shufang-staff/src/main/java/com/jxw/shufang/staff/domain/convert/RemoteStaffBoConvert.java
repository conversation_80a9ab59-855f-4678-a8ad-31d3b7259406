package com.jxw.shufang.staff.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteStaffBoConvert extends BaseMapper<RemoteStaffBo, BranchStaffBo> {
}




