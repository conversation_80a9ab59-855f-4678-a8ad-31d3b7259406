package com.jxw.shufang.staff.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.staff.domain.FollowUpRecord;
import com.jxw.shufang.staff.domain.vo.FollowUpRecordVo;

/**
 * 跟进记录Mapper接口
 *
 * @date 2024-05-24
 */
public interface FollowUpRecordMapper extends BaseMapperPlus<FollowUpRecord, FollowUpRecordVo> {


    Page<FollowUpRecordVo> selectPageList(@Param("page") Page<FollowUpRecord> build,@Param(Constants.WRAPPER) QueryWrapper<FollowUpRecord> lqw);
}
