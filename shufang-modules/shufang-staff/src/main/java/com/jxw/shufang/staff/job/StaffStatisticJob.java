package com.jxw.shufang.staff.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.bo.BranchStaffStatisticBo;
import com.jxw.shufang.staff.domain.bo.FollowUpRecordBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.domain.vo.FollowUpRecordVo;
import com.jxw.shufang.staff.service.IBranchStaffService;
import com.jxw.shufang.staff.service.IBranchStaffStatisticService;
import com.jxw.shufang.staff.service.IFollowUpRecordService;
import com.jxw.shufang.student.api.*;
import com.jxw.shufang.student.api.domain.bo.*;
import com.jxw.shufang.student.api.domain.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 只统计前一天的数据，而且只执行一次
 */
@Slf4j
//顾问统计数据方案变更，不再通过定时任务统计，所以去掉该类的扫描注解
//@Component
@RequiredArgsConstructor
public class StaffStatisticJob implements BasicProcessor {


    private final IBranchStaffService branchStaffService;

    private final IBranchStaffStatisticService branchStaffStatisticService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;


    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteStudyPlanningService remoteStudyPlanningService;

    @DubboReference
    private RemoteStudyVideoRecordService remoteStudyVideoRecordService;

    @DubboReference
    private RemoteStudyRecordService remoteStudyRecordService;

    @DubboReference
    private RemoteCorrectionRecordService remoteCorrectionRecordService;

    @DubboReference
    private RemoteFeedbackRecordService remoteFeedbackRecordService;

    private final IFollowUpRecordService followUpRecordService;

    //全部用户数 就是所有和这个顾问关联的会员人数
    //负责会员数 就是所有和这个顾问关联的会员中下单付款并且产品没过期的人数
    //注：以下当日可能是一天或一段时间
    //当前负责会员 和 负责会员数 一样 但是只是当日的
    //体验 已经下单付款 体验类型 的产品有效时间范围包括当日的
    //新签 当日下单付款 体验类型 的产品 的人数
    //续费 以前有下单付款任何产品的，当日又下单的人数
    //签约金额 当日下单付款 的产品 的总金额
    //签约 当日下单付款 非体验类型 的产品 的人数
    //小测批改 当日批改小测数量
    //批改率 当日批改小测数量/当日学习人数
    //课后反馈 当日课后反馈数量（有一个反馈记录表）
    //反馈率 当日课后反馈数量/当日学习人数
    //跟进会员 当日跟进会员数量（有一个跟进记录表）
    //结转 暂时没有业务逻辑，默认为0


    /**
     * 定时统计顾问相关数据
     *
     * 新需求统计规则：
     * 1、当前负责会员：时间段内 所负责的在籍会员
     * 2、体验：时间段内 负责过的体验会员-含当前时间段内过期体验
     * 3、新签：时间段内 产生购买正式卡的新生数量-第一次购买正式卡
     * 4、续费：时间段内 续费的老生数量 - 有购卡历史，新增正式卡
     * 5、签约：时间段内 新签+续费总数
     * 7、学习：时间段内 学习记录的在籍会员总数
     * 8、作答批改：时间段内 批改记录的在籍会员总数
     * 9、批改率：时间段内 自主批改申请通过数 / 当前在籍会员当前时间段内学习课程总数
     * 10、课后反馈：时间段内 学习反馈总数（会员去重 正式+体验）
     * 11、反馈率：时间段内 反馈率平均数
     * 12、跟进会员：时间段内 学习反馈总数（会员去重 仅正式）
     *
     * @param context
     * @return
     */
    @Override
    public ProcessResult process(TaskContext context) {
        long startTime = System.currentTimeMillis();
        log.info("【顾问数据统计】【Begin】开始统计...");

        OmsLogger omsLogger = context.getOmsLogger();
        //获取某一天前
        Date yesterday = null;
        //获取定时任务参数
        String jobParams = context.getJobParams();

        //根据参数获取指定的日期，默认获取昨天
        if (StringUtils.isNotBlank(jobParams)) {
            yesterday = DateUtils.addDays(new Date(), -Integer.parseInt(jobParams));
        } else {
            //默认获取昨天的数据
            yesterday = DateUtils.addDays(new Date(), -1);
        }


        //获取yesterday的开始时间
        Date beginOfYesterday = DateUtil.beginOfDay(yesterday);
        //获取yesterday的结束时间
        Date endOfYesterday = DateUtil.endOfDay(yesterday);

        //获取会员顾问，只查会员顾问角色
        BranchStaffBo bo = new BranchStaffBo();
        bo.setRoleIds(Collections.singletonList(StaffRole.MEMBER_CONSULTANT.getRoleId()));
        //查询顾问列表
        List<BranchStaffVo> branchStaffVos = DataPermissionHelper.ignore(() -> branchStaffService.queryStaffOptionList(new BranchStaffBo()));
        if (CollUtil.isEmpty(branchStaffVos)) {
            return new ProcessResult(true, "会员顾问数量为0");
        }

        //获取顾问ID集合
        List<Long> staffIds = branchStaffVos.stream().map(BranchStaffVo::getBranchStaffId).toList();
        //远程调用 - 查顾问关联的会员ID集合，返回Map集合
        //key：顾问ID value：会员ID集合
        Map<Long, List<Long>> staffResponsibleStudentIdMap = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIds, true);
        if (CollUtil.isEmpty(staffResponsibleStudentIdMap)) {
            return new ProcessResult(true, "所有顾问没有关联会员");
        }

        //获取所有会员ID
        List<Long> allStudentIds = staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).distinct().toList();

        //查询所有已支付的订单 - 根据会员ID（order表）
        List<RemoteOrderVo> payedOrderList = getPayedOrderList(allStudentIds);

        //获取类型为“体验卡”的产品列表 （student_type表 product表）
        List<RemoteProductVo> experienceCardProductList = queryExperienceCardProductList(omsLogger);

        //获取类型为“非体验卡”的产品列表（student_type表 product表）
        List<RemoteProductVo> nonExperienceCardProductList = queryNonExperienceCardProductList(omsLogger);

        //获取学生时间区间内的学习视频记录 - 根据会员ID （study_video_record表）
        List<RemoteStudyVideoRecordVo> remoteStudyVideoRecordVos = queryVideoRecordList(beginOfYesterday, endOfYesterday, allStudentIds);

        //获取学生时间区间内的学习规划 - 根据会员ID（study_planning表）
        List<RemoteStudyPlanningVo> remoteStudyPlanningVos = queryStudyPlanningList(beginOfYesterday, endOfYesterday, allStudentIds);

        //获取学生时间区间内的学习规划详情列表 - 根据学习规划列表
        List<RemoteStudyPlanningRecordVo> studyPlanningRecordList = queryStudyPlanningRecordList(remoteStudyPlanningVos);

        //获取学习规划对应的学习记录 - 根据学习规划列表 （study_record表）
        List<RemoteStudyRecordVo> remoteStudyRecordVos = queryStudyRecordList(remoteStudyPlanningVos);

        //查询批改记录 - 根据会员ID（correction_record表）
        List<RemoteCorrectionRecordVo> remoteCorrectionRecordVos = queryCorrectionRecordList(beginOfYesterday, endOfYesterday, allStudentIds);

        //反馈记录 - 根据会员ID（feedback_record表）
        List<RemoteFeedbackRecordVo> todayAllFeedbackRecordList = queryFeedbackRecordList(beginOfYesterday, endOfYesterday, allStudentIds);

        //跟进记录
        List<FollowUpRecordVo> todayAllFollowUpRecordList = queryFollowUpRecordList(beginOfYesterday, endOfYesterday, allStudentIds);

        List<BranchStaffStatisticBo> insertList = new ArrayList<>();

        //循环各个顾员ID，进行数据统计
        for (Map.Entry<Long, List<Long>> entry : staffResponsibleStudentIdMap.entrySet()) {
            BranchStaffStatisticBo branchStaffStatisticBo = new BranchStaffStatisticBo();

            //获取顾问ID
            Long staffId = entry.getKey();
            //获取该顾问ID对应的会员ID集合
            List<Long> studentIds = entry.getValue();
            branchStaffStatisticBo.setBranchStaffId(staffId);

            //顾问负责的会员所产生的已支付订单
            List<RemoteOrderVo> filterResponsibleOrderList = filterResponsibleOrderList(payedOrderList, studentIds);

            //过滤出顾问负责会员的 相关视频观看记录
            List<RemoteStudyVideoRecordVo> filterResponsibleVideoRecordList = filterResponsibleRecordList(remoteStudyVideoRecordVos, studentIds);

            //过滤出顾问负责会员的 学习规划相关记录
            List<RemoteStudyPlanningVo> filterResponsibleStudyPlanning = filterResponsibleStudyPlanning(remoteStudyPlanningVos, studentIds);

            //过滤出顾问负责会员的 学习记录
            List<RemoteStudyRecordVo> filterResponsibleStudyRecordList = filterResponsibleStudyRecordList(remoteStudyRecordVos, studentIds);

            //过滤出顾问负责会员的 批改记录
            List<RemoteCorrectionRecordVo> filterResponsibleCorrectionRecordList = filterResponsibleCorrectionRecordList(remoteCorrectionRecordVos, studentIds);

            //过滤出顾问负责会员的 反馈记录
            List<RemoteFeedbackRecordVo> filterResponsibleFeedbackRecordList = filterResponsibleFeedbackRecordList(todayAllFeedbackRecordList, studentIds);

            //过滤出顾问负责会员的 跟进记录
            List<FollowUpRecordVo> filterResponsibleFollowUpRecordList = filterResponsibleFollowUpRecordList(todayAllFollowUpRecordList, studentIds);

            //过滤出顾问负责会员的 学习规划详情列表
            List<RemoteStudyPlanningRecordVo> filterResponsibleStudyPlanningRecordList = filterResponsibleStudyPlanningRecordList(studyPlanningRecordList, studentIds);

            //-----------计算当前顾问当日的统计数据----------------
            // 当前负责会员：时间段内 所负责的在籍会员
            branchStaffStatisticBo.setTotalStudentNum(Convert.toLong(studentIds.size()));

            //体验：时间段内 负责过的体验会员-含当前时间段内过期体验
            Long experienceCardEffectiveNum = getExperienceCardEffectiveNum(beginOfYesterday, filterResponsibleOrderList, experienceCardProductList, omsLogger);
            branchStaffStatisticBo.setExperienceNum(experienceCardEffectiveNum);

            //新签 当日下单付款 体验类型 的产品 的人数
            Long newSignNum = getNewSignNum(beginOfYesterday, endOfYesterday, filterResponsibleOrderList, experienceCardProductList);
            branchStaffStatisticBo.setNewSignNum(newSignNum);

            //续费 以前有下单付款任何产品的，在时间区间内又下单的人数
            Long renewNum = getRenewNum(beginOfYesterday, endOfYesterday, filterResponsibleOrderList);
            branchStaffStatisticBo.setRenewNum(renewNum);

            //签约金额 当日下单付款 的产品 的总金额
            BigDecimal signAmount = getSignAmount(beginOfYesterday, endOfYesterday, filterResponsibleOrderList);
            branchStaffStatisticBo.setSignAmount(signAmount);

            Long studyNum = getStudyNum(filterResponsibleVideoRecordList);
            branchStaffStatisticBo.setStudyVideoNum(studyNum);

            Long testCorrectNum = getTestCorrectNum(filterResponsibleCorrectionRecordList);
            branchStaffStatisticBo.setMarkingNum(testCorrectNum);

            //小测批改率
            Long testCorrectRate = getMarkingRate(testCorrectNum, Convert.toLong(filterResponsibleStudyPlanningRecordList.size()));
            branchStaffStatisticBo.setMarkingRate(testCorrectRate);

            //课后反馈 当日课后反馈数量（有一个反馈记录表）
            branchStaffStatisticBo.setFeedbackNum(Convert.toLong(filterResponsibleFeedbackRecordList.size()));
            //反馈率
            Long feedbackRate = getFeedbackRate(Convert.toLong(filterResponsibleFeedbackRecordList.size()), Convert.toLong(filterResponsibleStudyPlanning.size()));
            branchStaffStatisticBo.setFeedbackRate(feedbackRate);

            //跟进人数
            branchStaffStatisticBo.setFollowUpNum(Convert.toLong(filterResponsibleFollowUpRecordList.size()));
            Long followUpRate = getFollowUpRate(Convert.toLong(filterResponsibleFollowUpRecordList.size()), Convert.toLong(filterResponsibleStudyPlanning.size()));
            branchStaffStatisticBo.setFollowUpRate(followUpRate);
            branchStaffStatisticBo.setCarryForwardAmount(getTransferNum());
            branchStaffStatisticBo.setDayStudyNum(Convert.toLong(filterResponsibleStudyPlanning.size()));
            insertList.add(branchStaffStatisticBo);
        }

        //先查时间区间内有没有已近存在的统计数据，如果有，则更新，如果没有，则插入
        boolean b = branchStaffStatisticService.batchSaveOrUpdateStatistic(insertList, yesterday);

        long endTime = System.currentTimeMillis();
        log.info("【顾问数据统计】【end】结束统计，结果：{}，耗时：{}", b, endTime - startTime);

        if (b) {
            return new ProcessResult(true, "统计成功");
        }
        return new ProcessResult(false, "统计失败");
    }

    private List<RemoteStudyPlanningVo> filterResponsibleStudyPlanning(List<RemoteStudyPlanningVo> remoteStudyPlanningVos, List<Long> studentIds) {
        if (CollUtil.isEmpty(remoteStudyPlanningVos) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return remoteStudyPlanningVos.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    private Long getFollowUpRate(Long followUpNum, Long studyNum) {
        if (studyNum == null || studyNum == 0 || followUpNum == null || followUpNum == 0) {
            return 0L;
        }
        return Convert.toLong(new BigDecimal(followUpNum).divide(new BigDecimal(studyNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue());

    }

    private List<RemoteStudyPlanningRecordVo> filterResponsibleStudyPlanningRecordList(List<RemoteStudyPlanningRecordVo> studyPlanningRecordList, List<Long> studentIds) {
        if (CollUtil.isEmpty(studyPlanningRecordList) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return studyPlanningRecordList.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    private List<RemoteStudyPlanningRecordVo> queryStudyPlanningRecordList(List<RemoteStudyPlanningVo> remoteStudyPlanningVos) {
        if (CollUtil.isEmpty(remoteStudyPlanningVos)) {
            return List.of();
        }
        List<RemoteStudyPlanningRecordVo> list = new ArrayList<>();

        for (RemoteStudyPlanningVo remoteStudyPlanningVo : remoteStudyPlanningVos) {
            List<RemoteStudyPlanningRecordVo> studyPlanningRecordList = remoteStudyPlanningVo.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)) {
                continue;
            }
            list.addAll(studyPlanningRecordList);
        }
        return list;
    }

    private List<FollowUpRecordVo> filterResponsibleFollowUpRecordList(List<FollowUpRecordVo> todayAllFollowUpRecordList, List<Long> studentIds) {
        if (CollUtil.isEmpty(todayAllFollowUpRecordList) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return todayAllFollowUpRecordList.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    private List<FollowUpRecordVo> queryFollowUpRecordList(Date beginOfYesterday, Date endOfYesterday, List<Long> allStudentIds) {
        if (CollUtil.isEmpty(allStudentIds)) {
            return List.of();
        }
        FollowUpRecordBo followUpRecordBo = new FollowUpRecordBo();
        followUpRecordBo.setFollowUpRecordCreateTimeStart(beginOfYesterday);
        followUpRecordBo.setFollowUpRecordCreateTimeEnd(endOfYesterday);
        followUpRecordBo.setStudentIdList(allStudentIds);
        return DataPermissionHelper.ignore(() -> followUpRecordService.queryList(followUpRecordBo));
    }


    private List<RemoteFeedbackRecordVo> queryFeedbackRecordList(Date beginOfYesterday, Date endOfYesterday, List<Long> allStudentIds) {
        if (CollUtil.isEmpty(allStudentIds)) {
            return List.of();
        }
        RemoteFeedbackRecordBo remoteFeedbackRecordBo = new RemoteFeedbackRecordBo();
        remoteFeedbackRecordBo.setFeedbackRecordCreateTimeStart(beginOfYesterday);
        remoteFeedbackRecordBo.setFeedbackRecordCreateTimeEnd(endOfYesterday);
        remoteFeedbackRecordBo.setStudentIdList(allStudentIds);
        return remoteFeedbackRecordService.queryList(remoteFeedbackRecordBo, true);

    }


    private List<RemoteCorrectionRecordVo> filterResponsibleCorrectionRecordList(List<RemoteCorrectionRecordVo> remoteCorrectionRecordVos, List<Long> studentIds) {
        if (CollUtil.isEmpty(remoteCorrectionRecordVos) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return remoteCorrectionRecordVos.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    private List<RemoteFeedbackRecordVo> filterResponsibleFeedbackRecordList(List<RemoteFeedbackRecordVo> todayAllFeedbackRecordList, List<Long> studentIds) {
        if (CollUtil.isEmpty(todayAllFeedbackRecordList) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return todayAllFeedbackRecordList.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    //时间区间内的批改记录
    private List<RemoteCorrectionRecordVo> queryCorrectionRecordList(Date beginOfYesterday, Date endOfYesterday, List<Long> allStudentIds) {
        if (CollUtil.isEmpty(allStudentIds)) {
            return List.of();
        }
        RemoteCorrectionRecordBo remoteCorrectionRecordBo = new RemoteCorrectionRecordBo();
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeStart(beginOfYesterday);
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeEnd(endOfYesterday);
        remoteCorrectionRecordBo.setStudentIdList(allStudentIds);
        return remoteCorrectionRecordService.queryList(remoteCorrectionRecordBo, true);
    }


    private List<RemoteStudyRecordVo> filterResponsibleStudyRecordList(List<RemoteStudyRecordVo> remoteStudyRecordVos, List<Long> studentIds) {
        if (CollUtil.isEmpty(remoteStudyRecordVos) || CollUtil.isEmpty(studentIds)) {
            return List.of();
        }
        return remoteStudyRecordVos.stream().filter(record ->
            studentIds.contains(record.getStudentId())
        ).toList();
    }

    private List<RemoteStudyRecordVo> queryStudyRecordList(List<RemoteStudyPlanningVo> remoteStudyPlanningVos) {
        if (CollUtil.isEmpty(remoteStudyPlanningVos)) {
            return List.of();
        }
        List<Long> studyPlanningRecordIdList = new ArrayList<>();
        for (RemoteStudyPlanningVo remoteStudyPlanningVo : remoteStudyPlanningVos) {
            List<RemoteStudyPlanningRecordVo> studyPlanningRecordList = remoteStudyPlanningVo.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)) {
                continue;
            }
            studyPlanningRecordList.stream().map(RemoteStudyPlanningRecordVo::getStudyPlanningRecordId).map(studyPlanningRecordIdList::add);
        }
        if (CollUtil.isEmpty(studyPlanningRecordIdList)) {
            return List.of();
        }
        RemoteStudyRecordBo remoteStudyRecordBo = new RemoteStudyRecordBo();
        remoteStudyRecordBo.setStudyPlanningRecordIdList(studyPlanningRecordIdList);
        return remoteStudyRecordService.queryList(remoteStudyRecordBo, true);
    }

    //查询学生时间区间内的学习规划
    private List<RemoteStudyPlanningVo> queryStudyPlanningList(Date startTime, Date endTime, List<Long> studentIdList) {
        RemoteStudyPlanningBo remoteStudyPlanningBo = new RemoteStudyPlanningBo();
        remoteStudyPlanningBo.setStudyPlanningDateStart(startTime);
        remoteStudyPlanningBo.setStudyPlanningDateEnd(endTime);
        remoteStudyPlanningBo.setStudentIdList(studentIdList);
        return remoteStudyPlanningService.queryPlanAndRecordList(remoteStudyPlanningBo, true);
    }


    //查询学生时间区间内的视频学习记录
    private List<RemoteStudyVideoRecordVo> queryVideoRecordList(Date startTime, Date endTime, List<Long> studentIdList) {
        RemoteStudyVideoRecordBo remoteStudyVideoRecordBo = new RemoteStudyVideoRecordBo();
        remoteStudyVideoRecordBo.setVideoRecordCreateDateStart(startTime);
        remoteStudyVideoRecordBo.setVideoRecordCreateDateEnd(endTime);
        remoteStudyVideoRecordBo.setStudentIdList(studentIdList);
        remoteStudyVideoRecordBo.setNonSelectStudyVideoSlicesField(true);
        return remoteStudyVideoRecordService.queryList(remoteStudyVideoRecordBo, true);
    }

    //体验卡产品
    private List<RemoteProductVo> queryExperienceCardProductList(OmsLogger omsLogger) {
        //先获取体验类型的产品
        RemoteStudentTypeVo experienceStudentType = remoteStudentTypeService.getExperienceStudentType(true);
        if (experienceStudentType == null) {
            omsLogger.error("体验类型不存在");
            throw new ServiceException("体验类型不存在");
        }
        RemoteProductBo productBo = new RemoteProductBo();
        productBo.setStudentTypeId(experienceStudentType.getStudentTypeId());
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(productBo, true);
        if (CollUtil.isEmpty(remoteProductVos)) {
            omsLogger.warn("体验类型下没有产品");
            return List.of();
        }
        return remoteProductVos;
    }

    //非体验卡产品
    private List<RemoteProductVo> queryNonExperienceCardProductList(OmsLogger omsLogger) {
        //先获取体验类型的产品
        RemoteStudentTypeVo experienceStudentType = remoteStudentTypeService.getExperienceStudentType(true);
        if (experienceStudentType == null) {
            omsLogger.error("体验类型不存在");
            throw new ServiceException("体验类型不存在");
        }
        RemoteProductBo productBo = new RemoteProductBo();
        productBo.setNeStudentTypeId(experienceStudentType.getStudentTypeId());
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(productBo, true);
        if (CollUtil.isEmpty(remoteProductVos)) {
            omsLogger.warn("非体验卡类型下没有产品");
            return List.of();
        }
        return remoteProductVos;
    }

    private List<RemoteOrderVo> getPayedOrderList(List<Long> studentIdList) {
        RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
        remoteOrderBo.setStudentIdList(studentIdList);
        remoteOrderBo.setOrderStatus(OrderStatusEnum.PAYED.getCode());
        return remoteOrderService.selectOrderListAndInfo(remoteOrderBo, true);
    }

    //过滤出顾问负责会员的相关订单
    private List<RemoteOrderVo> filterResponsibleOrderList(List<RemoteOrderVo> orderList, List<Long> responsibleStudentIdList) {
        if (CollUtil.isEmpty(orderList) || CollUtil.isEmpty(responsibleStudentIdList)) {
            return List.of();
        }
        return orderList.stream().filter(order ->
            responsibleStudentIdList.contains(order.getStudentId())
        ).toList();
    }

    //过滤出顾问负责会员的相关学习记录

    private List<RemoteStudyVideoRecordVo> filterResponsibleRecordList(List<RemoteStudyVideoRecordVo> recordList, List<Long> responsibleStudentIdList) {
        if (CollUtil.isEmpty(recordList) || CollUtil.isEmpty(responsibleStudentIdList)) {
            return List.of();
        }
        return recordList.stream().filter(record ->
            responsibleStudentIdList.contains(record.getStudentId())
        ).toList();

    }

    /**
     * “体验”数据统计：时间段内 负责过的体验会员-含当前时间段内过期体验
     * @param calTime 统计日期
     * @param payedOrderList 订单列表
     * @param experienceCardProductList 体验卡产品列表
     * @param omsLogger 日志记录对象
     * @return
     */
    private Long getExperienceCardEffectiveNum(Date calTime, List<RemoteOrderVo> payedOrderList, List<RemoteProductVo> experienceCardProductList, OmsLogger omsLogger) {
        if (CollUtil.isEmpty(payedOrderList) || CollUtil.isEmpty(experienceCardProductList)) {
            return 0L;
        }
        //获得体验卡产品的产品ID集合
        List<Long> productIdList = experienceCardProductList.stream().map(RemoteProductVo::getProductId).toList();
        //获取有购买这个体验卡产品的订单,并在有效期内
        List<RemoteOrderVo> orderList = payedOrderList.stream().filter(order -> {
            //获取订单产品信息列表
            List<RemoteOrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollUtil.isEmpty(orderProductInfoList)) {
                return false;
            }
            //筛选出体验卡产品信息列表
            List<RemoteOrderProductInfoVo> list = orderProductInfoList.stream().filter(item -> productIdList.contains(item.getProductId())).toList();
            if (CollUtil.isEmpty(list)) {
                return false;
            }
            //根据新需求，统计时间段内体验会员数量，包含当前时间段内过期的体验，所以需要将过期s
//            //筛选出在有效期内产品
//            for (RemoteOrderProductInfoVo orderProductInfoVo : list) {
//                //获取产品有效期（时间段）
//                String productValidTimeLimit = orderProductInfoVo.getProductValidTimeLimit();
//                //获取产品有效天数
//                Long productValidDays = orderProductInfoVo.getProductValidDays();
//                //获取订单创建时间
//                Date payTime = order.getOrderOperate().getCreateTime();
//                try {
//                    if (StringUtils.isNotBlank(productValidTimeLimit)) {
//                        //如果产品是有效时间段
//                        String[] split = productValidTimeLimit.split(" 至 ");
//                        //获取有效时间段的开始时间和结束时间
//                        Date startDate = DateUtils.parseDate(split[0]);
//                        Date endDate = DateUtils.parseDate(split[1]);
//                        //判断当前统计时间，是否在有效时间段内
//                        if (startDate.getTime() <= calTime.getTime() && endDate.getTime() >= calTime.getTime()) {
//                            return true;
//                        }
//                    } else if (productValidDays != null) {
//                        //如果产品是有效天数
//                        //计算有效天数的结束时间
//                        long time = payTime.getTime();
//                        Date endDate = new Date(time + productValidDays * 24 * 60 * 60 * 1000);
//                        //判断当前统计时间，是否在有效时间段内
//                        if (time <= calTime.getTime() && endDate.getTime() >= calTime.getTime()) {
//                            return true;
//                        }
//                    } else {
//                        omsLogger.error("产品有效期限为空");
//                    }
//                } catch (Exception e) {
//                    omsLogger.error("解析产品有效期限失败", e);
//                }
//            }
            return true;
        }).toList();

        return orderList.stream().map(RemoteOrderVo::getStudentId).distinct().count();
    }

    //新签 当日下单付款 体验类型 的产品 的人数
    private Long getNewSignNum(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList, List<RemoteProductVo> experienceCardProductList) {
        if (CollUtil.isEmpty(payedOrderList) || CollUtil.isEmpty(experienceCardProductList)) {
            return 0L;
        }
        List<Long> productIdList = experienceCardProductList.stream().map(RemoteProductVo::getProductId).toList();
        //获取有购买这个体验卡产品的订单,并在有效期内
        List<RemoteOrderVo> orderList = payedOrderList.stream().filter(order -> {
            List<RemoteOrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollUtil.isEmpty(orderProductInfoList)) {
                return false;
            }
            //过滤体验卡
            List<RemoteOrderProductInfoVo> list = orderProductInfoList.stream().filter(item -> productIdList.contains(item.getProductId())).toList();
            if (CollUtil.isEmpty(list)) {
                return false;
            }
            //时间范围内下单的
            Date createTime = order.getOrderOperate().getCreateTime();
            return createTime != null && createTime.getTime() >= startTime.getTime() && createTime.getTime() <= endTime.getTime();
        }).toList();
        return orderList.stream().map(RemoteOrderVo::getStudentId).distinct().count();
    }


    //续费 以前有下单付款任何产品的，在时间区间内又下单的人数
    private Long getRenewNum(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList) {
        if (CollUtil.isEmpty(payedOrderList)) {
            return 0L;
        }
        //先按照studentId分组
        Map<Long, List<RemoteOrderVo>> orderMap = payedOrderList.stream().collect(Collectors.groupingBy(RemoteOrderVo::getStudentId));
        Long count = 0L;
        for (Map.Entry<Long, List<RemoteOrderVo>> entry : orderMap.entrySet()) {
            List<RemoteOrderVo> value = entry.getValue();
            //先判断在startTime之前有没有下过单
            List<RemoteOrderVo> beforeList = value.stream().filter(order -> order.getOrderOperate().getCreateTime().getTime() <= startTime.getTime()).toList();
            if (CollUtil.isEmpty(beforeList)) {
                continue;
            }
            //再判断在startTime和endTime之间有没有下过单
            List<RemoteOrderVo> betweenList = value.stream().filter(order -> order.getOrderOperate().getCreateTime().getTime() >= startTime.getTime()
                && order.getOrderOperate().getCreateTime().getTime() <= endTime.getTime()).toList();
            if (CollUtil.isNotEmpty(betweenList)) {
                count++;
            }
        }
        return count;

    }

    //签约金额 当日下单付款 的产品 的总金额
    private BigDecimal getSignAmount(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList) {
        if (CollUtil.isEmpty(payedOrderList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal reduce = payedOrderList.stream().filter(order -> {
            Date createTime = order.getOrderOperate().getCreateTime();
            return createTime != null && createTime.getTime() >= startTime.getTime() && createTime.getTime() <= endTime.getTime();
        }).map(order -> {
            List<RemoteOrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollUtil.isEmpty(orderProductInfoList)) {
                return BigDecimal.ZERO;
            }
            return orderProductInfoList.stream().map(RemoteOrderProductInfoVo::getProductPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        reduce = reduce.setScale(2, RoundingMode.HALF_UP);
        return reduce;
    }

    //签约 当日下单付款 非体验类型 的产品 的人数
    private Long getSignNum(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList, List<RemoteProductVo> nonExperienceCardProductList) {
        if (CollUtil.isEmpty(payedOrderList) || CollUtil.isEmpty(nonExperienceCardProductList)) {
            return 0L;
        }
        List<Long> productIdList = nonExperienceCardProductList.stream().map(RemoteProductVo::getProductId).toList();
        //获取有购买这个非体验卡产品的订单,并在有效期内
        List<RemoteOrderVo> orderList = payedOrderList.stream().filter(order -> {
            List<RemoteOrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollUtil.isEmpty(orderProductInfoList)) {
                return false;
            }
            //过滤非体验卡
            List<RemoteOrderProductInfoVo> list = orderProductInfoList.stream().filter(item -> productIdList.contains(item.getProductId())).toList();
            if (CollUtil.isEmpty(list)) {
                return false;
            }
            //时间范围内下单的
            Date createTime = order.getOrderOperate().getCreateTime();
            return createTime != null && createTime.getTime() >= startTime.getTime() && createTime.getTime() <= endTime.getTime();
        }).toList();
        return orderList.stream().map(RemoteOrderVo::getStudentId).distinct().count();
    }

    //小测批改 当日批改小测数量
    private Long getTestCorrectNum(List<RemoteCorrectionRecordVo> list) {
        if (CollUtil.isEmpty(list)) {
            return 0L;
        }

        return list.stream().filter(item ->
            UserConstants.CORRECTION_PERSON_TYPE_STAFF.equals(item.getCorrectionPersonType()) && UserConstants.CORRECTION_TYPE_TEST.equals(item.getCorrectionType())
        ).map(item -> item.getStudentId() + "_" + item.getStudyPlanningRecordId()).distinct().count();
    }

    //批改率 当日批改小测数量/当日学习人数
    private BigDecimal getTestCorrectRate(Long testCorrectNum, Long responsibleStudentNum) {
        if (testCorrectNum == null || responsibleStudentNum == null || testCorrectNum == 0 || responsibleStudentNum == 0) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(testCorrectNum).divide(new BigDecimal(responsibleStudentNum), 4, RoundingMode.HALF_UP);
    }


    //反馈率 当日课后反馈数量/当日学习人数
    private Long getFeedbackRate(Long feedbackNum, Long responsibleStudentNum) {
        if (feedbackNum == null || responsibleStudentNum == null || feedbackNum == 0 || responsibleStudentNum == 0) {
            return 0L;
        }
        return Convert.toLong(new BigDecimal(feedbackNum).divide(new BigDecimal(responsibleStudentNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue());
    }

    //跟进会员 当日跟进会员数量（有一个跟进记录表）
    private Long getFollowNum(List<FollowUpRecordVo> todayAllFollowRecordList, Long staffId) {
        if (CollUtil.isEmpty(todayAllFollowRecordList)) {
            return 0L;
        }
        List<FollowUpRecordVo> list = todayAllFollowRecordList.stream().filter(item -> staffId.equals(item.getCreateBy())).toList();
        return list.stream().map(FollowUpRecordVo::getStudentId).distinct().count();
    }

    //结转 暂时没有业务逻辑，默认为0
    private BigDecimal getTransferNum() {
        return BigDecimal.ZERO;
    }

    //学习次数（视频观看次数）
    private Long getStudyNum(List<RemoteStudyVideoRecordVo> todayAllStudyVideoRecordList) {
        if (CollUtil.isEmpty(todayAllStudyVideoRecordList)) {
            return 0L;
        }
        return todayAllStudyVideoRecordList.stream().map(RemoteStudyVideoRecordVo::getStudyVideoRecordId).distinct().count();
    }

    //小测批改率 当日批改小测数量/当日学习规划数量
    private Long getMarkingRate(Long testCorrectNum, Long studyPlanNum) {
        if (testCorrectNum == null || studyPlanNum == null || testCorrectNum == 0 || studyPlanNum == 0) {
            return 0L;
        }
        return Convert.toLong(new BigDecimal(testCorrectNum).divide(new BigDecimal(studyPlanNum), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue());
    }


}
