package com.jxw.shufang.staff.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.domain.FollowUpRecord;
import com.jxw.shufang.staff.domain.bo.FollowUpRecordBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.domain.vo.FollowUpRecordVo;
import com.jxw.shufang.staff.mapper.FollowUpRecordMapper;
import com.jxw.shufang.staff.service.IFollowUpRecordService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 跟进记录Service业务层处理
 *
 * @date 2024-05-24
 */
@RequiredArgsConstructor
@Service
public class FollowUpRecordServiceImpl implements IFollowUpRecordService, BaseService {

    private final FollowUpRecordMapper baseMapper;

    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询跟进记录
     */
    @Override
    public FollowUpRecordVo queryById(Long followUpRecordId) {
        return baseMapper.selectVoById(followUpRecordId);
    }

    /**
     * 查询跟进记录列表
     */
    @Override
    public TableDataInfo<FollowUpRecordVo> queryPageList(FollowUpRecordBo bo, PageQuery pageQuery) {
        QueryWrapper<FollowUpRecord> lqw = buildQueryWrapper(bo);
        Page<FollowUpRecordVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
        putStaffUserInfo(result.getRecords());
        return TableDataInfo.build(result);
    }


    /**
     * 查询跟进记录列表
     */
    @Override
    public List<FollowUpRecordVo> queryList(FollowUpRecordBo bo) {
        LambdaQueryWrapper<FollowUpRecord> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FollowUpRecord> buildLambdaQueryWrapper(FollowUpRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FollowUpRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, FollowUpRecord::getStudentId, bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowUpType()), FollowUpRecord::getFollowUpType, bo.getFollowUpType());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowUpContent()), FollowUpRecord::getFollowUpContent, bo.getFollowUpContent());
        lqw.between(bo.getFollowUpRecordCreateTimeStart() != null && bo.getFollowUpRecordCreateTimeEnd() != null, FollowUpRecord::getCreateTime, bo.getFollowUpRecordCreateTimeStart(), bo.getFollowUpRecordCreateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), FollowUpRecord::getStudentId, bo.getStudentIdList());
        return lqw;
    }

    private QueryWrapper<FollowUpRecord> buildQueryWrapper(FollowUpRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<FollowUpRecord> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowUpType()), "t.follow_up_type", bo.getFollowUpType());
        lqw.eq(StringUtils.isNotBlank(bo.getFollowUpContent()), "t.follow_up_content", bo.getFollowUpContent());
        lqw.between(bo.getFollowUpRecordCreateTimeStart() != null && bo.getFollowUpRecordCreateTimeEnd() != null, "t.create_time", bo.getFollowUpRecordCreateTimeStart(), bo.getFollowUpRecordCreateTimeEnd());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        return lqw;
    }

    /**
     * 新增跟进记录
     */
    @Override
    public Boolean insertByBo(FollowUpRecordBo bo) {
        FollowUpRecord add = MapstructUtils.convert(bo, FollowUpRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFollowUpRecordId(add.getFollowUpRecordId());
        }
        return flag;
    }

    /**
     * 修改跟进记录
     */
    @Override
    public Boolean updateByBo(FollowUpRecordBo bo) {
        FollowUpRecord update = MapstructUtils.convert(bo, FollowUpRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FollowUpRecord entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除跟进记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    private void putStaffUserInfo(List<FollowUpRecordVo> records) {
        if (records.isEmpty()) {
            return;
        }
        List<Long> userIds = records.stream().map(FollowUpRecordVo::getBranchStaff).filter(Objects::nonNull).map(BranchStaffVo::getCreateBy).filter(Objects::nonNull).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(userIds);
        remoteUserBo.setGetAvatarUrl(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> userVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        records.forEach(record -> {
            BranchStaffVo branchStaff = record.getBranchStaff();
            if (branchStaff == null) {
                return;
            }
            RemoteUserVo remoteUserVo = userVoMap.get(branchStaff.getCreateBy());
            if (remoteUserVo != null) {
                branchStaff.setUser(remoteUserVo);
                Long[] roleIds = remoteUserVo.getRoleIds();
                List<String> roleNameList = new ArrayList<>();
                for (Long roleId : roleIds) {
                    roleNameList.add(StaffRole.getStaffPost(roleId, StaffRole::getRoleId).getRoleName());
                }
                branchStaff.setRoleNameList(roleNameList);
            }
        });
    }


}
