package com.jxw.shufang.staff.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 分店员工
（其他属性采用sys_user对应数据）对象 branch_staff
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_staff")
public class BranchStaff extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工id
     */
    @TableId(value = "branch_staff_id")
    private Long branchStaffId;

    /**
     * 考勤id
     */
    private String checkWorkAttendanceId;

    /**
     * 分店id
     */
    private Long branchId;

    /**
     * 真实头像(图片ossid)
     */
    private Long realAvatar;

    /**
     * 微信二维码(图片ossid)
     */
    private Long wechatQrCode;


}
