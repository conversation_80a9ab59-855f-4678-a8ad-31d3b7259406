package com.jxw.shufang.staff.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.bo.BranchStaffStatisticBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.service.IBranchStaffService;
import com.jxw.shufang.staff.service.IBranchStaffStatisticService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据
 * 前端访问路由地址为:/staff/staffStatistic
 *
 *
 * @date 2024-02-29
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/staffStatistic")
@Slf4j
public class BranchStaffStatisticController extends BaseController {

    private final IBranchStaffStatisticService branchStaffStatisticService;

    private final IBranchStaffService branchStaffService;


    /**
     * 查询分店员工统计数据列表
     */
    @SaCheckPermission("staff:staffStatistic:list")
    @GetMapping("/staffStatisticList")
    public TableDataInfo<BranchStaffVo> staffStatisticList(BranchStaffBo bo, PageQuery pageQuery) {
        return branchStaffService.staffStatisticPageList(bo, pageQuery);
    }

    /**
     * 导出分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据列表
     */
    @SaCheckPermission("staff:staffStatistic:export")
    @Log(title = "分店员工数据统计（顾问数据） ", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BranchStaffBo bo, HttpServletResponse response) {
        List<BranchStaffVo> result = branchStaffService.staffStatisticList(bo);
        List<BranchStaffStatisticVo> list = new ArrayList<>();
        for (BranchStaffVo branchStaffVo : result) {
            BranchStaffStatisticVo branchStaffStatistic = branchStaffVo.getBranchStaffStatistic();
            if (branchStaffStatistic == null) {
                branchStaffStatistic = new BranchStaffStatisticVo();
            }
            branchStaffStatistic.setBranchName(branchStaffVo.getBranch().getBranchName());
            branchStaffStatistic.setStaffName(branchStaffVo.getUser().getNickName());
            list.add(branchStaffStatistic);
            log.info("【顾员统计导出】统计的数据对象：{}, 导出的对象：{}", branchStaffVo, branchStaffStatistic);
        }
        ExcelUtil.exportExcel(list, "分店员工数据统计", BranchStaffStatisticVo.class, response);
    }

    /**
     * 获取分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据详细信息
     *
     * @param branchStaffStatisticId 主键
     */
    @SaCheckPermission("staff:staffStatistic:query")
    @GetMapping("/{branchStaffStatisticId}")
    public R<BranchStaffStatisticVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long branchStaffStatisticId) {
        return R.ok(branchStaffStatisticService.queryById(branchStaffStatisticId));
    }

    /**
     * 新增分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @SaCheckPermission("staff:staffStatistic:add")
    @Log(title = "分店员工数据统计（顾问数据） ", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BranchStaffStatisticBo bo) {
        return toAjax(branchStaffStatisticService.insertByBo(bo));
    }

    /**
     * 修改分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @SaCheckPermission("staff:staffStatistic:edit")
    @Log(title = "分店员工数据统计（顾问数据）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BranchStaffStatisticBo bo) {
        return toAjax(branchStaffStatisticService.updateByBo(bo));
    }

    /**
     * 删除分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     *
     * @param branchStaffStatisticIds 主键串
     */
    @SaCheckPermission("staff:staffStatistic:remove")
    @Log(title = "分店员工数据统计（顾问数据） ", businessType = BusinessType.DELETE)
    @DeleteMapping("/{branchStaffStatisticIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] branchStaffStatisticIds) {
        return toAjax(branchStaffStatisticService.deleteWithValidByIds(List.of(branchStaffStatisticIds), true));
    }
}
