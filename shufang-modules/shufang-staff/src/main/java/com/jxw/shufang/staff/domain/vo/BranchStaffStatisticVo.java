package com.jxw.shufang.staff.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.staff.domain.BranchStaffStatistic;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据视图对象 branch_staff_statistic
 *
 * @date 2024-02-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchStaffStatistic.class)
public class BranchStaffStatisticVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工数据id
     */
    private Long branchStaffStatisticId;

    /**
     * 分店员工id
     */
    private Long branchStaffId;

    /**
     * 负责总人数
     */
    @ExcelProperty(value = "当前负责会员")
    private Long totalStudentNum;

    /**
     * 体验人数
     */
    @ExcelProperty(value = "体验")
    private Long experienceNum;

    /**
     * 新签人数
     */
    @ExcelProperty(value = "新签")
    private Long newSignNum;

    /**
     * 续费次数
     */
    @ExcelProperty(value = "续费")
    private Long renewNum;

    /**
     * 签约人数
     */
    @ExcelProperty(value = "签约")
    private Long signAmountNum;

    /**
     * 签约金额
     */
//    @ExcelProperty(value = "签约金额")
    private BigDecimal signAmount;

    /**
     * 学习人数
     */
    @ExcelProperty(value = "学习")
    private Long studyVideoNum;

    /**
     * 作答批改人数
     */
    @ExcelProperty(value = "作答批改")
    private Long markingNum;

    /**
     * 批改率（前端展示需加%）
     */
    @ExcelProperty(value = "批改率(%)")
    private Long markingRate;

    /**
     * 反馈次数
     */
    @ExcelProperty(value = "课后反馈")
    private Long feedbackNum;

    /**
     * 反馈率（前端展示需加%）
     */
    @ExcelProperty(value = "反馈率(%)")
    private Long feedbackRate;

    /**
     * 跟进次数
     */
    @ExcelProperty(value = "跟进会员")
    private Long followUpNum;

    /**
     * 跟进会员比率（前端展示需加%）
     */
//    @ExcelProperty(value = "跟进会员比率(%)")
    private Long followUpRate;

    /**
     * 结转费用
     */
//    @ExcelProperty(value = "结转费用")
    private BigDecimal carryForwardAmount;

    /**
     * 当日学习人数（按照学习规划，当天的学习人数）
     */
//    @ExcelProperty(value = "当日学习人数")
    private Long dayStudyNum;

    /**
     * 开始时间（如果用本表，此处只精确到日）
     */
    private Date startTime;

    /**
     * 结束时间（如果用本表，此处只精确到日）
     */
    private Date endTime;

    /**
     * 门店名称
     */
    @ExcelProperty(value = "所属门店",order = 2)
    private String branchName;

    /**
     * 员工姓名
     */
    @ExcelProperty(value = "会员顾问",order = 1)
    private String staffName;

    /**
     * 明日预计学习人数，非数据库字段
     */
    private Long tomorrowPlanStudyNum;

    /**
     * 返回空对象
     * @return
     */
    public static BranchStaffStatisticVo empty() {
        BranchStaffStatisticVo emptyVo = new BranchStaffStatisticVo();
        emptyVo.setTotalStudentNum(0L);
        emptyVo.setExperienceNum(0L);
        emptyVo.setNewSignNum(0L);
        emptyVo.setRenewNum(0L);
        emptyVo.setSignAmountNum(0L);
        emptyVo.setStudyVideoNum(0L);
        emptyVo.setMarkingNum(0L);
        emptyVo.setMarkingRate(0L);
        emptyVo.setFeedbackNum(0L);
        emptyVo.setFeedbackRate(0L);
        emptyVo.setFollowUpNum(0L);
        emptyVo.setFollowUpRate(0L);
        emptyVo.setCarryForwardAmount(BigDecimal.ZERO);
        emptyVo.setDayStudyNum(0L);
        return emptyVo;
    }

}
