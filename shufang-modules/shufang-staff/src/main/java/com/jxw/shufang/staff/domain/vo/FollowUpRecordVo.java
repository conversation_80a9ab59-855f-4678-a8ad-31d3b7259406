package com.jxw.shufang.staff.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.staff.domain.FollowUpRecord;

import java.io.Serial;
import java.io.Serializable;


/**
 * 跟进记录视图对象 follow_up_record
 *
 * @date 2024-05-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FollowUpRecord.class)
public class FollowUpRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 跟进记录id
     */
    @ExcelProperty(value = "跟进记录id")
    private Long followUpRecordId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 跟进类型（对应字典值，如微信沟通、电话访问、登门拜访）
     */
    @ExcelProperty(value = "跟进类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应字典值，如微信沟通、电话访问、登门拜访")
    private String followUpType;

    /**
     * 跟进内容（包含文字、图片、音频）
     */
    @ExcelProperty(value = "跟进内容", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "包=含文字、图片、音频")
    private String followUpContent;

    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 创建人（员工信息）
     */
    private BranchStaffVo branchStaff;

    private String createTime;
}
