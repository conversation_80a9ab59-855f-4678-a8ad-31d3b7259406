package com.jxw.shufang.staff.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.staff.domain.BranchStaffStatistic;
import com.jxw.shufang.staff.domain.bo.BranchStaffStatisticBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据Service接口
 *
 * @date 2024-02-29
 */
public interface IBranchStaffStatisticService {

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    BranchStaffStatisticVo queryById(Long branchStaffStatisticId);

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据列表
     */
    TableDataInfo<BranchStaffStatisticVo> queryPageList(BranchStaffStatisticBo bo, PageQuery pageQuery);

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据列表
     */
    List<BranchStaffStatisticVo> queryList(BranchStaffStatisticBo bo);

    /**
     * 新增分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    Boolean insertByBo(BranchStaffStatisticBo bo);

    /**
     * 修改分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    Boolean updateByBo(BranchStaffStatisticBo bo);

    /**
     * 校验并批量删除分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 获取最新时间
     *
     *
     * @date 2024/03/06 02:57:32
     */
    Date getLatestTime();

    BranchStaffStatistic queryBranchStaffStatisticById(Long branchStaffStatisticId);

    boolean batchSaveOrUpdateStatistic(List<BranchStaffStatisticBo> insertList, Date date);

    void cleanCache();
}
