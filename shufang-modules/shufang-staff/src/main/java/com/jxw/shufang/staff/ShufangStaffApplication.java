package com.jxw.shufang.staff;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 员工模块
 *
 */
@EnableDubbo
@SpringBootApplication
public class ShufangStaffApplication    {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangStaffApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  员工模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }

}
