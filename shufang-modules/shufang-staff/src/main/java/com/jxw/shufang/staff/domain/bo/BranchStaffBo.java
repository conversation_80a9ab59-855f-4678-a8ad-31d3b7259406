package com.jxw.shufang.staff.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.staff.domain.BranchStaff;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 分店员工
（其他属性采用sys_user对应数据）业务对象 branch_staff
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchStaff.class, reverseConvertGenerate = false)
public class BranchStaffBo extends BaseEntity {

    /**
     * 分店员工id
     */
    @NotNull(message = "分店员工id不能为空", groups = { EditGroup.class })
    private Long branchStaffId;

    /**
     * 分店员工idList
     */
    private List<Long> branchStaffIds;

    /**
     * 考勤id
     */
    //@NotBlank(message = "考勤id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String checkWorkAttendanceId;

    /**
     * 分店授权类型id
     */
    private Long branchId;

    /**
     * 真实头像(图片ossid)
     */
    //@NotNull(message = "真实头像(图片ossid)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long realAvatar;

    /**
     * 微信二维码(图片ossid)
     */
    //@NotNull(message = "微信二维码(图片ossid)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long wechatQrCode;

    /**
     * 分店员工姓名
     */
    @NotBlank(message = "手机号不能为空", groups = { AddGroup.class })
    @Pattern(regexp="^[1][3456789]\\d{9}$", message = "手机号格式错误", groups = { AddGroup.class })
    private String phone;

    /**
     * 分店员工姓名
     */
    @NotBlank(message = "分店员工姓名不能为空", groups = { AddGroup.class })
    private String name;

    /**
     * 分店员工邮箱
     */
    private String email;

    /**
     * 分店员工性别,对应字典
     */
    @NotBlank(message = "分店员工性别", groups = { AddGroup.class })
    private String sex;

    /**
     * 分店员工身份
     */
    @NotNull(message = "分店员工身份不能为空", groups = { AddGroup.class })
    private Long postId;

    /**
     * 分店员工角色
     */
    @NotEmpty(message = "分店员工角色不能为空", groups = { AddGroup.class })
    private List<Long> roleIds;


    /**
     * 用户状态
     */
    private String userStatus;


    /**
     * 带有系统用户信息
     */
    private Boolean withSysUserInfo;

    /**
     * 带有分店信息
     */
    private Boolean withBranchInfo;

    /**
     * 带有员工统计信息
     */
    private Boolean withStudentCountInfo;

    /**
     * 员工用户id
     */
    private Long userId;

    /**
     * 员工统计开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date staffStatisticStartTime;

    /**
     * 员工统计结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date staffStatisticEndTime;

    private Date selectTime;

    private List<Long> studentIds;


    private List<Long> branchIdList;

    /**
     * 门店员工对应sysUser表的idList
     */
    private List<Long> userIdList;

}
