package com.jxw.shufang.staff.dubbo;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.bo.RemoteStaffBo;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.service.IBranchStaffService;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStaffServiceImpl implements RemoteStaffService {

    private final IBranchStaffService branchStaffService;

    @Override
    public List<RemoteStaffVo> queryStaffList(RemoteStaffBo bo) {
        BranchStaffBo branchStaffBo = MapstructUtils.convert(bo, BranchStaffBo.class);
        List<BranchStaffVo> studentVos = branchStaffService.queryList(branchStaffBo);
        return MapstructUtils.convert(studentVos, RemoteStaffVo.class);
    }

    @Override
    public RemoteStaffVo queryStaffByUserId(Long userId) {
        BranchStaffVo studentVo = branchStaffService.queryByUserId(userId);
        return MapstructUtils.convert(studentVo, RemoteStaffVo.class);
    }


    @Override
    public List<Long> queryStaffIdList(RemoteStaffBo bo) {
        BranchStaffBo branchStaffBo = MapstructUtils.convert(bo, BranchStaffBo.class);
        return branchStaffService.queryStaffIdList(branchStaffBo);
    }
}
