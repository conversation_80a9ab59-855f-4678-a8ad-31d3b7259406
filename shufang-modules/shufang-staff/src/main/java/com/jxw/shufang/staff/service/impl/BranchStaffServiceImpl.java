package com.jxw.shufang.staff.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.report.api.RemotePaidMemberService;
import com.jxw.shufang.report.api.domain.bo.RemotePaidMemberStaffsBo;
import com.jxw.shufang.report.api.domain.vo.RemotePaidMemberStaffsVo;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.staff.domain.BranchStaff;
import com.jxw.shufang.staff.domain.bo.BranchStaffBo;
import com.jxw.shufang.staff.domain.bo.BranchStaffStatisticBo;
import com.jxw.shufang.staff.domain.dto.QueryConsultantNameDTO;
import com.jxw.shufang.staff.domain.vo.BranchStaffQrCodeVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.domain.vo.ConsultantInfoVO;
import com.jxw.shufang.staff.mapper.BranchStaffMapper;
import com.jxw.shufang.staff.service.IBranchStaffService;
import com.jxw.shufang.staff.service.IBranchStaffStatisticService;
import com.jxw.shufang.student.api.*;
import com.jxw.shufang.student.api.domain.bo.RemoteCorrectionRecordBo;
import com.jxw.shufang.student.api.domain.bo.RemoteFeedbackRecordBo;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudyPlanningBo;
import com.jxw.shufang.student.api.domain.vo.*;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分店员工
 * （其他属性采用sys_user对应数据）Service业务层处理
 *
 * @date 2024-02-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BranchStaffServiceImpl implements IBranchStaffService, BaseService {

    private final BranchStaffMapper baseMapper;

    private final IBranchStaffStatisticService branchStaffStatisticService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;

    @DubboReference
    private RemoteOrderService remoteOrderService;

    @DubboReference
    private RemoteFileService remoteFileService;

    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;


    @DubboReference
    private RemoteStudyPlanningService remoteStudyPlanningService;

    @DubboReference
    private RemoteStudyVideoRecordService remoteStudyVideoRecordService;

    @DubboReference
    private RemoteStudyRecordService remoteStudyRecordService;

    @DubboReference
    private RemoteCorrectionRecordService remoteCorrectionRecordService;

    @DubboReference
    private RemoteFeedbackRecordService remoteFeedbackRecordService;

    @DubboReference
    private RemotePaidMemberService remotePaidMemberService;


    /**
     * 查询分店员工
     * （其他属性采用sys_user对应数据）
     */
    @Override
    public BranchStaffVo queryById(Long branchStaffId) {
        BranchStaffVo branchStaffVo = baseMapper.selectVoById(branchStaffId);
        putStaffUserInfo(List.of(branchStaffVo));
        putBranchInfo(List.of(branchStaffVo));
        return branchStaffVo;
    }

    /**
     * 查询分店员工
     * （其他属性采用sys_user对应数据）列表
     */
    @Override
    public TableDataInfo<BranchStaffVo> queryPageList(BranchStaffBo bo, PageQuery pageQuery) {
        QueryWrapper<BranchStaff> lqw = buildQueryWrapper(bo);
        Page<BranchStaffVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putStaffUserInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(result.getRecords());
        }
        if (Boolean.TRUE.equals(bo.getWithStudentCountInfo())) {
            putStudentCountInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    private void putStudentCountInfo(List<BranchStaffVo> records) {
        if (records.isEmpty()) {
            return;
        }
        List<Long> staffIds = records.stream().map(BranchStaffVo::getBranchStaffId).collect(Collectors.toList());
        //获取员工负责的会员idMap
        Map<Long, List<Long>> staffResponsibleStudentIdMap = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIds, true);


        //获取会员中存在有效订单的会员ids
        List<Long> effectiveOrderStudentIds = List.of();
        if (CollUtil.isNotEmpty(staffResponsibleStudentIdMap)) {
            List<Long> studentIdList = staffResponsibleStudentIdMap.values().stream().flatMap(Collection::stream).toList();
            effectiveOrderStudentIds = remoteOrderService.getEffectiveOrderStudentIds(studentIdList, null);
        }

        for (BranchStaffVo record : records) {
            List<Long> studentIds = staffResponsibleStudentIdMap.get(record.getBranchStaffId());
            //总会员数量
            int totalStudentCount = 0;
            //负责会员数量
            int chargeStudentCount = 0;
            if (CollUtil.isNotEmpty(studentIds)) {
                totalStudentCount = studentIds.size();
                chargeStudentCount = (int) studentIds.stream().filter(effectiveOrderStudentIds::contains).count();
            }
            record.setTotalStudentCount(totalStudentCount);
            record.setChargeStudentCount(chargeStudentCount);
        }
    }

    private void putStaffUserInfo(List<BranchStaffVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> userIds = records.stream().map(BranchStaffVo::getCreateBy).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return;
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserIds(userIds);
        remoteUserBo.setGetAvatarUrl(true);
        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
        Map<Long, RemoteUserVo> userVoMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, Function.identity()));
        records.forEach(record -> {
            RemoteUserVo remoteUserVo = userVoMap.get(record.getCreateBy());
            if (remoteUserVo != null) {
                record.setUser(remoteUserVo);
                Long[] roleIds = remoteUserVo.getRoleIds();
                if (roleIds == null) {
                    return;
                }
                List<String> roleNameList = new ArrayList<>();
                for (Long roleId : roleIds) {
                    roleNameList.add(StaffRole.getStaffPost(roleId, StaffRole::getRoleId).getRoleName());
                }
                record.setRoleNameList(roleNameList);
            }
        });
    }

    private void putBranchInfo(List<BranchStaffVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> branchIds = records.stream().map(BranchStaffVo::getBranchId).collect(Collectors.toList());
        branchIds.remove(null);
        if (CollUtil.isEmpty(branchIds)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(branchIds);
        List<RemoteBranchVo> branchList = remoteBranchService.selectBranchList(remoteBranchBo);
        Map<Long, RemoteBranchVo> branchMap = branchList.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, Function.identity()));
        records.forEach(record -> {
            RemoteBranchVo remoteBranchVo = branchMap.get(record.getBranchId());
            if (remoteBranchVo != null) {
                record.setBranch(remoteBranchVo);
            }
        });

    }

    /**
     * 查询分店员工
     * （其他属性采用sys_user对应数据）列表
     */
    @Override
    public List<BranchStaffVo> queryList(BranchStaffBo bo) {
        LambdaQueryWrapper<BranchStaff> lqw = buildLambdaQueryWrapper(bo);
        List<BranchStaffVo> branchStaffVos = baseMapper.selectVoList(lqw);
        if (Boolean.TRUE.equals(bo.getWithSysUserInfo())) {
            putStaffUserInfo(branchStaffVos);
        }
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())) {
            putBranchInfo(branchStaffVos);
        }
        if (Boolean.TRUE.equals(bo.getWithStudentCountInfo())) {
            putStudentCountInfo(branchStaffVos);
        }
        return branchStaffVos;
    }


    private LambdaQueryWrapper<BranchStaff> buildLambdaQueryWrapper(BranchStaffBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchStaff> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchStaffId() != null, BranchStaff::getBranchStaffId, bo.getBranchStaffId());
        lqw.eq(StringUtils.isNotBlank(bo.getCheckWorkAttendanceId()), BranchStaff::getCheckWorkAttendanceId, bo.getCheckWorkAttendanceId());
        lqw.eq(bo.getBranchId() != null, BranchStaff::getBranchId, bo.getBranchId());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), BranchStaff::getBranchId, bo.getBranchIdList());
        lqw.eq(bo.getRealAvatar() != null, BranchStaff::getRealAvatar, bo.getRealAvatar());
        lqw.eq(bo.getWechatQrCode() != null, BranchStaff::getWechatQrCode, bo.getWechatQrCode());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            BranchStaff::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.between(params.get("beginUpdateTime") != null && params.get("endUpdateTime") != null,
            BranchStaff::getUpdateTime, params.get("beginUpdateTime"), params.get("endUpdateTime"));
        lqw.in(CollUtil.isNotEmpty(bo.getBranchStaffIds()), BranchStaff::getBranchStaffId, bo.getBranchStaffIds());
        if (StringUtils.isNotBlank(bo.getName()) || StringUtils.isNotBlank(bo.getPhone()) || StringUtils.isNotBlank(bo.getUserStatus()) || bo.getRoleIds() != null) {
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setNickName(bo.getName());
            remoteUserBo.setUserName(bo.getPhone());
            remoteUserBo.setStatus(bo.getUserStatus());
            remoteUserBo.setRoleIds(CollUtil.isEmpty(bo.getRoleIds()) ? null : bo.getRoleIds().toArray(Long[]::new));
            List<Long> userIds = remoteUserService.queryUserIds(remoteUserBo, true);
            if (userIds.isEmpty()) {
                lqw.in(BranchStaff::getCreateBy, -1L);
            } else {
                lqw.in(BranchStaff::getCreateBy, userIds);
            }
        }
        lqw.in(CollUtil.isNotEmpty(bo.getUserIdList()), BranchStaff::getCreateBy, bo.getUserIdList());
        return lqw;
    }

    private QueryWrapper<BranchStaff> buildQueryWrapper(BranchStaffBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<BranchStaff> lqw = Wrappers.query();
        lqw.eq(StringUtils.isNotBlank(bo.getCheckWorkAttendanceId()), "t.check_work_attendance_id", bo.getCheckWorkAttendanceId());
        lqw.eq(bo.getBranchId() != null, "t.branch_id", bo.getBranchId());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIdList()), "t.branch_id", bo.getBranchIdList());
        lqw.eq(bo.getRealAvatar() != null, "t.real_avatar", bo.getRealAvatar());
        lqw.eq(bo.getWechatQrCode() != null, "t.wechat_qr_code", bo.getWechatQrCode());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            "t.create_time", params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.between(params.get("beginUpdateTime") != null && params.get("endUpdateTime") != null,
            "t.update_time", params.get("beginUpdateTime"), params.get("endUpdateTime"));
        lqw.in(CollUtil.isNotEmpty(bo.getBranchStaffIds()), "t.branch_staff_id", bo.getBranchStaffIds());
        if (StringUtils.isNotBlank(bo.getName()) || StringUtils.isNotBlank(bo.getPhone()) || StringUtils.isNotBlank(bo.getUserStatus()) || bo.getRoleIds() != null) {
            RemoteUserBo remoteUserBo = new RemoteUserBo();
            remoteUserBo.setNickName(bo.getName());
            remoteUserBo.setUserName(bo.getPhone());
            remoteUserBo.setStatus(bo.getUserStatus());
            remoteUserBo.setRoleIds(CollUtil.isEmpty(bo.getRoleIds()) ? null : bo.getRoleIds().toArray(Long[]::new));
            List<Long> userIds = remoteUserService.queryUserIds(remoteUserBo, true);
            if (userIds.isEmpty()) {
                lqw.in("t.create_by", -1L);
            } else {
                lqw.in("t.create_by", userIds);
            }
        }
        //if (bo.getSelectTime() != null) {
        //    DateTime startDateTime = DateUtil.beginOfDay(bo.getSelectTime());
        //    DateTime endDateTime = DateUtil.endOfDay(bo.getSelectTime());
        //    lqw.ge("bss.start_time", startDateTime);
        //    lqw.le("bss.end_time", endDateTime);
        //}

        return lqw;
    }


    /**
     * 新增分店员工
     * （其他属性采用sys_user对应数据）
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(BranchStaffBo bo) {
        BranchStaff add = MapstructUtils.convert(bo, BranchStaff.class);
        validEntityBeforeSave(add);

        //获取门店对应的deptId
        Long deptIdByBranchId = remoteBranchService.selectDeptIdByBranchId(bo.getBranchId());
        if (deptIdByBranchId == null) {
            throw new ServiceException("门店对应部门不存在");
        }
        Long userId = getUserId(bo, deptIdByBranchId);

        add.setCreateDept(deptIdByBranchId);
        add.setCreateBy(userId);

        //TODO 二期才用得上，先填写123456
        add.setCheckWorkAttendanceId("123456");

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchStaffId(add.getBranchStaffId());
        }
        return flag;
    }

    private Long getUserId(BranchStaffBo bo, Long deptIdByBranchId) {
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserName(bo.getPhone());

        List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);

        if (CollUtil.isNotEmpty(remoteUserVos)) {
            RemoteUserVo remoteUserVo = remoteUserVos.get(0);
            String userType = remoteUserVo.getUserType();
            //如果是学生,修改用户类型为员工
            if (UserType.APP_STU_USER.getUserType().equals(userType)) {
                RemoteUserBo updateUser =  new RemoteUserBo();
                updateUser.setUserId(remoteUserVo.getUserId());
                extracted(bo, deptIdByBranchId, updateUser);
                remoteUserService.updateUserInfo(updateUser);
                return updateUser.getUserId();
            }else {
                throw new ServiceException("该账号已存在");
            }
        }else {
            RemoteUserBo insertUser =  new RemoteUserBo();
            extracted(bo, deptIdByBranchId, insertUser);
            //尝试添加对应的sysUser
            return remoteUserService.insertUser(insertUser);
        }

    }

    private void extracted(BranchStaffBo bo, Long deptIdByBranchId, RemoteUserBo remoteUserBo) {
        remoteUserBo.setSex(bo.getSex());
        remoteUserBo.setNickName(bo.getName());
        remoteUserBo.setUserName(bo.getPhone());
        remoteUserBo.setUserType(UserType.STAFF_USER.getUserType());
        remoteUserBo.setEmail(bo.getEmail());
        remoteUserBo.setDeptId(deptIdByBranchId);
        remoteUserBo.setPassword(generatePassword(bo.getPhone()));
        remoteUserBo.setPostIds(new Long[]{bo.getPostId()});
        remoteUserBo.setRoleIds(bo.getRoleIds().toArray(Long[]::new));
    }

    /**
     * 修改分店员工
     * （其他属性采用sys_user对应数据）
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean updateByBo(BranchStaffBo bo) {
        BranchStaff branchStaff = MapstructUtils.convert(bo, BranchStaff.class);
        //获取门店对应的deptId
        Long deptIdByBranchId = remoteBranchService.selectDeptIdByBranchId(bo.getBranchId());
        if (deptIdByBranchId == null) {
            throw new ServiceException("门店对应部门不存在");
        }
        //尝试修改对应的sysUser
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(bo.getUserId());
        remoteUserBo.setSex(bo.getSex());
        remoteUserBo.setNickName(bo.getName());
        remoteUserBo.setEmail(bo.getEmail());
        remoteUserBo.setDeptId(deptIdByBranchId);
        remoteUserBo.setPostIds(new Long[]{bo.getPostId()});
        remoteUserBo.setRoleIds(bo.getRoleIds().toArray(Long[]::new));
        remoteUserBo.setDeptId(deptIdByBranchId);
        remoteUserService.updateUserInfo(remoteUserBo);
        branchStaff.setCreateDept(deptIdByBranchId);
        int i = baseMapper.updateById(branchStaff);
        if (i <= 0) {
            throw new ServiceException("更新失败");
        }
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchStaff entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分店员工
     * （其他属性采用sys_user对应数据）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void changUserStatus(Long userId, String userStatus) {
        if (!UserConstants.USER_NORMAL.equals(userStatus)
            && !UserConstants.USER_DISABLE.equals(userStatus)) {
            throw new ServiceException("用户状态不合法");
        }
        RemoteUserBo remoteUserBo = new RemoteUserBo();
        remoteUserBo.setUserId(userId);
        remoteUserBo.setStatus(userStatus);
        remoteUserService.updateUserInfo(remoteUserBo);
    }

    @Override
    public List<Long> queryStaffIdList(BranchStaffBo branchStaffBo) {
        QueryWrapper<BranchStaff> lqw = buildQueryWrapper(branchStaffBo);
        return baseMapper.queryStaffIdList(lqw);

    }

    @Override
    public List<BranchStaffVo> queryStaffOptionList(BranchStaffBo bo) {
        QueryWrapper<BranchStaff> lqw = buildQueryWrapper(bo);
        List<BranchStaffVo> result = null;
        if (bo.getBranchId() != null) {
            result = DataPermissionHelper.ignore(() -> baseMapper.selectStaffList(lqw));
        }else {
            result = baseMapper.selectStaffList(lqw);
        }
        putStaffUserInfo(result);
        return result;
    }

    @Override
    public List<RemoteStudentVo> getStudentListByStaffId(Long branchStaffId, String nameWithPhone) {
        return remoteStudentService.getStudentListByStaffId(branchStaffId, nameWithPhone);
    }

    @Override
    public Boolean handoverMember(List<Long> studentIds, Long staffId) {
        return remoteStudentConsultantRecordService.handoverMember(studentIds, staffId);
    }


    @Override
    public TableDataInfo<BranchStaffVo> staffStatisticPageList(BranchStaffBo bo, PageQuery pageQuery) {
        Page<BranchStaffVo> result = null;
        //确定查询的日期范围
        Date startTime = bo.getStaffStatisticStartTime();
        Date endTime = bo.getStaffStatisticEndTime();

        //如果传入的参数中日期范围为空，则默认查询昨天的统计数据
        if (startTime == null || endTime == null) {
            //默认设置为昨天的时间范围统计
            Date yesterday = DateUtil.offsetDay(DateUtil.date(), -1);
            bo.setStaffStatisticStartTime(DateUtil.beginOfDay(yesterday));
            bo.setStaffStatisticEndTime(DateUtil.endOfDay(yesterday));
        }

        //构建查询条件对象
        QueryWrapper<BranchStaff> queryWrapper = buildQueryWrapper(bo);
        //根据条件查询“分店员工”信息表 - 分页查询
        result = baseMapper.selectPageList(pageQuery.build(), queryWrapper);
        //封装“分店员工”统计信息
        putStatisticGroupInfoRealTime(result.getRecords(), startTime, endTime);

        putStaffUserInfo(result.getRecords());
        putBranchInfo(result.getRecords());
        putUserAvatar(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 封装“分店员工”统计信息 - 方案已经废弃
     * 新方案参考 putStatisticGroupInfo方法
     * @param records - 分店会员列表
     * @param staffStatisticStartTime - 开始时间范围
     * @param staffStatisticEndTime - 结束时间范围
     */
    @Deprecated
    private void putStatisticGroupInfo(List<BranchStaffVo> records, Date staffStatisticStartTime, Date staffStatisticEndTime) {
        if (CollUtil.isEmpty(records) || staffStatisticEndTime == null || staffStatisticStartTime == null) {
            return;
        }
        BranchStaffStatisticVo emptyVo = new BranchStaffStatisticVo();
        emptyVo.setTotalStudentNum(0L);
        emptyVo.setExperienceNum(0L);
        emptyVo.setNewSignNum(0L);
        emptyVo.setRenewNum(0L);
        emptyVo.setSignAmount(BigDecimal.ZERO);
        emptyVo.setStudyVideoNum(0L);
        emptyVo.setMarkingNum(0L);
        emptyVo.setMarkingRate(0L);
        emptyVo.setFeedbackNum(0L);
        emptyVo.setFeedbackRate(0L);
        emptyVo.setFollowUpNum(0L);
        emptyVo.setFollowUpRate(0L);
        emptyVo.setCarryForwardAmount(BigDecimal.ZERO);
        emptyVo.setDayStudyNum(0L);

        //获取分店会员ID集合
        List<Long> list = records.stream().map(BranchStaffVo::getBranchStaffId).toList();
        //查询分店会员统计列表 - 根据会员ID集合、开始时间、结束时间
        BranchStaffStatisticBo branchStaffStatisticBo = new BranchStaffStatisticBo();
        branchStaffStatisticBo.setBranchStaffIdList(list);
        branchStaffStatisticBo.setStaffStatisticStartTime(staffStatisticStartTime);
        branchStaffStatisticBo.setStaffStatisticEndTime(staffStatisticEndTime);
        //获取分店会员统计列表
        List<BranchStaffStatisticVo> branchStaffStatisticVos = branchStaffStatisticService.queryList(branchStaffStatisticBo);

        //如果会员统计列表为空，则填充默认对象
        if (CollUtil.isEmpty(branchStaffStatisticVos)) {
            records.forEach(item -> {
                item.setBranchStaffStatistic(emptyVo);
            });
            return;
        }

        //将查询出来的统计列表，按照员工id分组
        //key：员工ID，value：List<员工统计信息>
        Map<Long, List<BranchStaffStatisticVo>> map = branchStaffStatisticVos.stream().collect(Collectors.groupingBy(BranchStaffStatisticVo::getBranchStaffId));

        //循环处理员工信息列表
        for (BranchStaffVo record : records) {
            //根据员工ID，查询该员工对应的统计列表
            List<BranchStaffStatisticVo> branchStaffStatisticVoList = map.get(record.getBranchStaffId());
            //如果没有该员工的统计信息，则填充默认对象
            if (CollUtil.isEmpty(branchStaffStatisticVoList)) {
                record.setBranchStaffStatistic(emptyVo);
                continue;
            }

            //创建一个空的统计对象
            BranchStaffStatisticVo branchStaffStatisticVo = new BranchStaffStatisticVo();
            //total_student_num拿最新的数据（当前负责会员人数）
            branchStaffStatisticVoList.stream().max(Comparator.comparing(BranchStaffStatisticVo::getStartTime)).ifPresent(item -> {
                branchStaffStatisticVo.setTotalStudentNum(item.getTotalStudentNum());
            });
            //experience_num拿总和（体验人数）
            branchStaffStatisticVo.setExperienceNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getExperienceNum).sum());
            //new_sign_num拿总和（新签人数）
            branchStaffStatisticVo.setNewSignNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getNewSignNum).sum());
            //renew_num拿总和（续费人数）
            branchStaffStatisticVo.setRenewNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getRenewNum).sum());
            //sign_amount拿总和（签约金额）
            branchStaffStatisticVo.setSignAmount(branchStaffStatisticVoList.stream().map(BranchStaffStatisticVo::getSignAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            //study_video_num拿总和（学习次数（视频观看次数））
            branchStaffStatisticVo.setStudyVideoNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getStudyVideoNum).sum());
            //marking_num拿总和（批改次数）
            branchStaffStatisticVo.setMarkingNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getMarkingNum).sum());

            //当日学习人数（按照学习规划，当天的学习人数） 求和
            long dayStudyNum = branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getDayStudyNum).sum();

            //marking_rate计算（批改率（前端展示需加%））
            if (dayStudyNum == 0) {
                branchStaffStatisticVo.setMarkingRate(0L);
            } else {
                long round = Math.round(branchStaffStatisticVo.getMarkingNum() * 100.0 / dayStudyNum);
                if (round > 100){
                    round = 100;
                }
                branchStaffStatisticVo.setMarkingRate(round);
            }
            //feedback_num拿总和（反馈次数）
            branchStaffStatisticVo.setFeedbackNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getFeedbackNum).sum());
            //feedback_rate计算（反馈率（前端展示需加%））
            if (dayStudyNum == 0) {
                branchStaffStatisticVo.setFeedbackRate(0L);
            } else {
                long round = Math.round(branchStaffStatisticVo.getFeedbackNum() * 100.0 / dayStudyNum);
                if (round > 100){
                    round = 100;
                }
                branchStaffStatisticVo.setFeedbackRate(round);
            }
            //follow_up_num拿总和（跟进次数）
            branchStaffStatisticVo.setFollowUpNum(branchStaffStatisticVoList.stream().mapToLong(BranchStaffStatisticVo::getFollowUpNum).sum());
            //follow_up_rate计算（跟进会员比率（前端展示需加%））
            if (dayStudyNum == 0) {
                branchStaffStatisticVo.setFollowUpRate(0L);
            } else {
                long round = Math.round(branchStaffStatisticVo.getFollowUpNum() * 100.0 / dayStudyNum);
                if (round > 100){
                    round = 100;
                }
                branchStaffStatisticVo.setFollowUpRate(round);
            }
            //carry_forward_amount拿总和（结转费用）
            branchStaffStatisticVo.setCarryForwardAmount(branchStaffStatisticVoList.stream().map(BranchStaffStatisticVo::getCarryForwardAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            //day_study_num拿总和（当日学习人数（按照学习规划，当天的学习人数））
            branchStaffStatisticVo.setDayStudyNum(dayStudyNum);
            record.setBranchStaffStatistic(branchStaffStatisticVo);
        }

    }

    /**
     * 封装“分店员工（顾问）”统计信息 - 实时统计
     * 新需求统计规则：
     * 1、当前负责会员：时间段内 所负责的在籍会员
     * 2、体验：时间段内 负责过的体验会员-含当前时间段内过期体验
     * 3、新签：时间段内 产生购买正式卡的新生数量-第一次购买正式卡
     * 4、续费：时间段内 续费的老生数量 - 有购卡历史，新增正式卡
     * 5、签约：时间段内 新签+续费总数
     * 7、学习：时间段内 产生学习记录的在籍会员总数
     * 8、作答批改：时间段内 产生批改记录的在籍会员总数
     * 9、批改率：时间段内 自主批改申请通过数 / 当前在籍会员当前时间段内学习课程总数
     * 10、课后反馈：时间段内 学习反馈总数（会员去重 正式+体验）
     * 11、反馈率：时间段内 反馈率平均数
     * 12、跟进会员：时间段内 学习反馈总数（会员去重 仅正式）
     *
     *
     * @param records - 分店会员列表
     * @param staffStatisticStartTime - 开始时间范围
     * @param staffStatisticEndTime - 结束时间范围
     */
    private void putStatisticGroupInfoRealTime(List<BranchStaffVo> records, Date staffStatisticStartTime, Date staffStatisticEndTime) {
        log.info("【顾问数据统计】请求参数，顾问集合数量：{}，日期范围：{} - {}", records.size(), staffStatisticStartTime, staffStatisticEndTime);
        //参数校验
        if (!ObjectUtil.isAllNotEmpty(records, staffStatisticStartTime, staffStatisticEndTime)) {
            return;
        }

        //根据顾问ID集合，查询关联的会员集合
        //获取顾问ID集合
        List<Long> staffIds = records.stream().map(BranchStaffVo::getBranchStaffId).toList();
        log.info("【顾问数据统计】顾问ID集合：{}", staffIds);
        //远程调用 - 查顾问关联的会员ID集合，返回Map集合
        //key：顾问ID value：会员ID集合
//        Map<Long, List<Long>> staffResponsibleStudentIdMap = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdMap(staffIds, true);

        //根据顾员ID集合，查询会员统计相关数据
        RemotePaidMemberStaffsBo paidMemberStaffsBo = new RemotePaidMemberStaffsBo();
        paidMemberStaffsBo.setStaffIdList(staffIds);
        paidMemberStaffsBo.setStartDate(staffStatisticStartTime);
        paidMemberStaffsBo.setEndDate(staffStatisticEndTime);
        Map<Long, RemotePaidMemberStaffsVo> paidMemberMapByStaffs = Optional.ofNullable(remotePaidMemberService.getPaidMemberMapByStaffs(paidMemberStaffsBo)).orElse(new HashMap<>());
        log.info("【顾问数据统计】远程获取会员相关统计数据：{}", paidMemberMapByStaffs);


        //循环设置各个顾员的统计数据
        for (BranchStaffVo record : records) {
            //根据顾问ID，查询该顾问对应的统计列表
            RemotePaidMemberStaffsVo remotePaidMemberStaffsVo = paidMemberMapByStaffs.get(record.getBranchStaffId());
            //如果未查询到该顾员的统计数据，则设置默认统计对象
            if (ObjectUtil.isNull(remotePaidMemberStaffsVo)) {
                record.setBranchStaffStatistic(BranchStaffStatisticVo.empty());
                continue;
            }

            //创建统计对象
            BranchStaffStatisticVo branchStaffStatisticVo = new BranchStaffStatisticVo();
            record.setBranchStaffStatistic(branchStaffStatisticVo);

            //购买了正式卡的会员ID集合
            Set<Long> paidMemberStuIds = Optional.ofNullable(remotePaidMemberStaffsVo.getPaidMemberIdList()).orElse(new HashSet<>());
            //购买了体验卡的会员ID集合
            Set<Long> experienceMemberStuIds = Optional.ofNullable(remotePaidMemberStaffsVo.getExperienceMemberIdList()).orElse(new HashSet<>());
            //新签的会员ID集合
            Set<Long> newSignMemberStuIds = Optional.ofNullable(remotePaidMemberStaffsVo.getNewSignMemberIdList()).orElse(new HashSet<>());
            //续费的会员ID集合，需求由 续费人数 改为 续费次数
//            Set<Long> renewMemberStuIds = Optional.ofNullable(remotePaidMemberStaffsVo.getRenewMemberIdList()).orElse(new HashSet<>());
            //续费的次数
            Integer renewNum = Optional.ofNullable(remotePaidMemberStaffsVo.getRenewNum()).orElse(0);
            //学习会员人数
            Integer studyPersonNum = Optional.ofNullable(remotePaidMemberStaffsVo.getStudyPersonNum()).orElse(0);
            //批改会员人数
            Integer correctionPersonNum = Optional.ofNullable(remotePaidMemberStaffsVo.getCorrectionPersonNum()).orElse(0);
            //批改率
            Integer correntRate = Optional.ofNullable(remotePaidMemberStaffsVo.getCorrentRate()).orElse(0);
            //反馈会员人数
            Integer feedbackPersonNum = Optional.ofNullable(remotePaidMemberStaffsVo.getFeedbackPersonNum()).orElse(0);
            //反馈率平均值
            Integer feedbackRateAvg = Optional.ofNullable(remotePaidMemberStaffsVo.getFeedbackRateAvg()).orElse(0);
            //跟进会员人数
            Integer followPersonNum = Optional.ofNullable(remotePaidMemberStaffsVo.getFollowPersonNum()).orElse(0);


            //当前负责会员人数
            branchStaffStatisticVo.setTotalStudentNum(Long.valueOf(paidMemberStuIds.size()));
            //体验会员人数
            branchStaffStatisticVo.setExperienceNum(Long.valueOf(experienceMemberStuIds.size()));
            //新签会员人数
            branchStaffStatisticVo.setNewSignNum(Long.valueOf(newSignMemberStuIds.size()));
            //续费会员次数
            branchStaffStatisticVo.setRenewNum(Long.valueOf(renewNum));
            //签约会员人数（新签 + 续费）
            branchStaffStatisticVo.setSignAmountNum(branchStaffStatisticVo.getNewSignNum() + branchStaffStatisticVo.getRenewNum());
            //学习会员人数
            branchStaffStatisticVo.setStudyVideoNum(Long.valueOf(studyPersonNum));
            //作答批改会员人数
            branchStaffStatisticVo.setMarkingNum(Long.valueOf(correctionPersonNum));
            //批改率
            branchStaffStatisticVo.setMarkingRate(Long.valueOf(correntRate));
            //课后反馈(正式 + 体验 反馈人数)
            branchStaffStatisticVo.setFeedbackNum(Long.valueOf(feedbackPersonNum));
            //反馈率
            branchStaffStatisticVo.setFeedbackRate(Long.valueOf(feedbackRateAvg));
            //跟进会员(正式反馈人数)
            branchStaffStatisticVo.setFollowUpNum(Long.valueOf(followPersonNum));
        }
    }


    /**
     * 放置用户头像
     *
     * @param records 记录
     *
     * @date 2024/03/07 03:50:31
     */
    private void putUserAvatar(List<BranchStaffVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<RemoteUserVo> list = records.stream().map(BranchStaffVo::getUser).toList();
        String ossIds = list.stream().map(RemoteUserVo::getAvatar).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.isBlank(ossIds)) {
            return;
        }
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIds(ossIds);
        if (CollUtil.isEmpty(remoteFiles)) {
            return;
        }
        Map<Long, RemoteFile> remoteFileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, Function.identity()));
        list.forEach(item -> {
            RemoteFile remoteFile = remoteFileMap.get(item.getAvatar());
            if (remoteFile != null) {
                item.setAvatarUrl(remoteFile.getUrl());
            }
        });
    }

    @Override
    public List<BranchStaffVo> staffStatisticList(BranchStaffBo bo) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        pageQuery.setSearchCount(Boolean.FALSE);
        return this.staffStatisticPageList(bo, pageQuery).getRows();
    }

    /**
     * 生成密码,默认密码为后六位
     *
     * @param username 用户名
     *
     * @date 2024/02/27 10:52:32
     */
    private String generatePassword(String username) {
        return BCrypt.hashpw(StringUtils.substring(username, username.length() - 6));
    }


    @Cacheable(value = "branchStaff", key = "#branchStaffId", condition = "#branchStaffId != null")
    @Override
    public BranchStaff queryBranchStaffById(Long branchStaffId) {
        return baseMapper.selectById(branchStaffId);
    }

    @CacheEvict(value = "branchStaff", allEntries = true)
    @Override
    public void cleanCache() {
        log.info("===========branchStaffService cleanCache===========");
    }

    @Override
    public BranchStaffVo queryByUserId(Long userId) {
        LambdaQueryWrapper<BranchStaff> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BranchStaff::getCreateBy, userId);
        queryWrapper.last("LIMIT 1");
        return baseMapper.selectVoOne(queryWrapper);
    }

    @Override
    public BranchStaffVo queryBranchStaffInfo(Long branchStaffId) {
        BranchStaff branchStaff = SpringUtils.getBean(IBranchStaffService.class).queryBranchStaffById(branchStaffId);
        if (branchStaff == null) {
            return null;
        }
        BranchStaffVo branchStaffVo = MapstructUtils.convert(branchStaff, BranchStaffVo.class);
        List<BranchStaffVo> tempList = List.of(branchStaffVo);
        putStaffUserInfo(tempList);
        putBranchInfo(tempList);
        return tempList.get(0);
    }

    @Override
    public BranchStaffStatisticVo getTimelyStatistics(Long branchStaffId,Date startDate, Date endDate) {
        BranchStaffStatisticVo result = new BranchStaffStatisticVo();
        //查顾问关联的会员
        List<Long> staffResponsibleStudentIdList = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdList(branchStaffId, true);
        if (CollUtil.isEmpty(staffResponsibleStudentIdList)) {
            return null;
        }

        //所有已支付的订单
        List<RemoteOrderVo> payedOrderList = getPayedOrderList(staffResponsibleStudentIdList);
        //获取体验卡产品
        List<RemoteProductVo> experienceCardProductList = queryExperienceCardProductList();

        //获取学生时间区间内的学习规划,这里顺带明天的也查了
        List<RemoteStudyPlanningVo> remoteStudyPlanningVos = queryStudyPlanningList(startDate, DateUtils.addDays(endDate,1), staffResponsibleStudentIdList);


        //查询批改记录
        List<RemoteCorrectionRecordVo> remoteCorrectionRecordVos = queryCorrectionRecordList(startDate,endDate , staffResponsibleStudentIdList);

        //反馈记录
        List<RemoteFeedbackRecordVo> todayAllFeedbackRecordList = queryFeedbackRecordList(startDate, endDate, staffResponsibleStudentIdList);

        Map<String, List<RemoteStudyPlanningVo>> studyPlanningMap = StreamUtils.groupByKey(remoteStudyPlanningVos, e -> DateUtils.dateTime(e.getStudyPlanningDate()));

        //新签 当日下单付款 体验类型 的产品 的人数
        Long newSignNum = getNewSignNum(startDate, endDate, payedOrderList, experienceCardProductList);
        result.setNewSignNum(newSignNum);

        //续费 以前有下单付款任何产品的，在时间区间内又下单的人数
        Long renewNum = getRenewNum(startDate,endDate, payedOrderList);
        result.setRenewNum(renewNum);

        //学习人数
        List<RemoteStudyPlanningVo> dayStudyPlanningList = studyPlanningMap.get(DateUtils.dateTime(endDate));
        result.setDayStudyNum(Convert.toLong(CollUtil.isEmpty(dayStudyPlanningList)? 0 : dayStudyPlanningList.size()));

        //测验批改次数
        Long testCorrectNum = getTestCorrectNum(remoteCorrectionRecordVos);
        result.setMarkingNum(testCorrectNum);

        //课后反馈 当日课后反馈数量
        result.setFeedbackNum(Convert.toLong(todayAllFeedbackRecordList.size()));

        //明日预计学习人数

        List<RemoteStudyPlanningVo> tomorrowPlanDate = studyPlanningMap.get(DateUtils.dateTime(DateUtils.addDays(endDate, 1)));
        result.setTomorrowPlanStudyNum(Convert.toLong(CollUtil.isEmpty(tomorrowPlanDate)? 0 : tomorrowPlanDate.size()));

        return result;
    }

    @Override
    public List<ConsultantInfoVO> getConsultantByName(QueryConsultantNameDTO consultantNameDTO) {
        if (CollectionUtil.isEmpty(consultantNameDTO.getRoles())) {
            return Collections.emptyList();
        }
        return baseMapper.getConsultantByName(consultantNameDTO);
    }

    @Override
    public BranchStaffQrCodeVo getStaffQrCode(Long userId, String codeType) {
        BranchStaffVo branchStaffVo = queryByUserId(userId);
        if (ObjectUtil.isNull(branchStaffVo)) {
            throw new ServiceException("没有权限访问用户数据!");
        }
        switch (codeType) {
            case "WX": {
                if (null == branchStaffVo.getWechatQrCode()) {
                    throw new ServiceException("未设置微信二维码!");
                }
                return new BranchStaffQrCodeVo(
                    remoteFileService.selectUrlByIds(String.valueOf(branchStaffVo.getWechatQrCode())));
            }
            default: {
                throw new ServiceException("不支持的codeType类型!");
            }
        }
    }

    @Override
    public BranchStaffVo getPresentStaffId() {
        BranchStaffVo vo = new BranchStaffVo();
        if (LoginHelper.isBranchStaff()) {
            vo.setBranchStaffId(LoginHelper.getBranchStaffId());
        }
        return vo;
    }

    private List<RemoteOrderVo> getPayedOrderList(List<Long> studentIdList) {
        RemoteOrderBo remoteOrderBo = new RemoteOrderBo();
        remoteOrderBo.setStudentIdList(studentIdList);
        remoteOrderBo.setOrderStatus(OrderStatusEnum.PAYED.getCode());
        return remoteOrderService.selectOrderListAndInfo(remoteOrderBo, true);
    }

    //时间区间内的批改记录
    private List<RemoteCorrectionRecordVo> queryCorrectionRecordList(Date beginOfYesterday, Date endOfYesterday, List<Long> allStudentIds) {
        if (CollUtil.isEmpty(allStudentIds)) {
            return List.of();
        }
        RemoteCorrectionRecordBo remoteCorrectionRecordBo = new RemoteCorrectionRecordBo();
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeStart(beginOfYesterday);
        remoteCorrectionRecordBo.setCorrectionRecordCreateTimeEnd(endOfYesterday);
        remoteCorrectionRecordBo.setStudentIdList(allStudentIds);
        return remoteCorrectionRecordService.queryList(remoteCorrectionRecordBo, true);
    }

    //新签 当日下单付款 体验类型 的产品 的人数
    private Long getNewSignNum(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList, List<RemoteProductVo> experienceCardProductList) {
        if (CollUtil.isEmpty(payedOrderList) || CollUtil.isEmpty(experienceCardProductList)) {
            return 0L;
        }
        List<Long> productIdList = experienceCardProductList.stream().map(RemoteProductVo::getProductId).toList();
        //获取有购买这个体验卡产品的订单,并在有效期内
        List<RemoteOrderVo> orderList = payedOrderList.stream().filter(order -> {
            List<RemoteOrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollUtil.isEmpty(orderProductInfoList)) {
                return false;
            }
            //过滤体验卡
            List<RemoteOrderProductInfoVo> list = orderProductInfoList.stream().filter(item -> productIdList.contains(item.getProductId())).toList();
            if (CollUtil.isEmpty(list)) {
                return false;
            }
            //时间范围内下单的
            Date createTime = order.getOrderOperate().getCreateTime();
            return createTime != null && createTime.getTime() >= startTime.getTime() && createTime.getTime() <= endTime.getTime();
        }).toList();
        return orderList.stream().map(RemoteOrderVo::getStudentId).distinct().count();
    }

    //续费 以前有下单付款任何产品的，在时间区间内又下单的人数
    private Long getRenewNum(Date startTime, Date endTime, List<RemoteOrderVo> payedOrderList) {
        if (CollUtil.isEmpty(payedOrderList)) {
            return 0L;
        }
        //先按照studentId分组
        Map<Long, List<RemoteOrderVo>> orderMap = payedOrderList.stream().collect(Collectors.groupingBy(RemoteOrderVo::getStudentId));
        Long count = 0L;
        for (Map.Entry<Long, List<RemoteOrderVo>> entry : orderMap.entrySet()) {
            List<RemoteOrderVo> value = entry.getValue();
            //先判断在startTime之前有没有下过单
            List<RemoteOrderVo> beforeList = value.stream().filter(order -> order.getOrderOperate().getCreateTime().getTime() <= startTime.getTime()).toList();
            if (CollUtil.isEmpty(beforeList)) {
                continue;
            }
            //再判断在startTime和endTime之间有没有下过单
            List<RemoteOrderVo> betweenList = value.stream().filter(order -> order.getOrderOperate().getCreateTime().getTime() >= startTime.getTime()
                && order.getOrderOperate().getCreateTime().getTime() <= endTime.getTime()).toList();
            if (CollUtil.isNotEmpty(betweenList)) {
                count++;
            }
        }
        return count;

    }

    //反馈记录
    private List<RemoteFeedbackRecordVo> queryFeedbackRecordList(Date beginOfYesterday, Date endOfYesterday, List<Long> allStudentIds) {
        if (CollUtil.isEmpty(allStudentIds)) {
            return List.of();
        }
        RemoteFeedbackRecordBo remoteFeedbackRecordBo = new RemoteFeedbackRecordBo();
        remoteFeedbackRecordBo.setFeedbackRecordCreateTimeStart(beginOfYesterday);
        remoteFeedbackRecordBo.setFeedbackRecordCreateTimeEnd(endOfYesterday);
        remoteFeedbackRecordBo.setStudentIdList(allStudentIds);
        return remoteFeedbackRecordService.queryList(remoteFeedbackRecordBo, true);

    }


    //体验卡产品
    private List<RemoteProductVo> queryExperienceCardProductList() {
        //先获取体验类型的产品
        RemoteStudentTypeVo experienceStudentType = remoteStudentTypeService.getExperienceStudentType(true);
        if (experienceStudentType == null) {
            throw new ServiceException("体验类型不存在");
        }
        RemoteProductBo productBo = new RemoteProductBo();
        productBo.setStudentTypeId(experienceStudentType.getStudentTypeId());
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(productBo,true);
        if (CollUtil.isEmpty(remoteProductVos)) {
            return List.of();
        }
        return remoteProductVos;
    }

    //查询学生时间区间内的学习规划
    private List<RemoteStudyPlanningVo> queryStudyPlanningList(Date startTime, Date endTime, List<Long> studentIdList) {
        RemoteStudyPlanningBo remoteStudyPlanningBo = new RemoteStudyPlanningBo();
        remoteStudyPlanningBo.setStudyPlanningDateStart(startTime);
        remoteStudyPlanningBo.setStudyPlanningDateEnd(endTime);
        remoteStudyPlanningBo.setStudentIdList(studentIdList);
        return remoteStudyPlanningService.queryPlanAndRecordList(remoteStudyPlanningBo, true);
    }

    //获取学生学习规划详情列表
    private List<RemoteStudyPlanningRecordVo> queryStudyPlanningRecordList(List<RemoteStudyPlanningVo> remoteStudyPlanningVos) {
        if (CollUtil.isEmpty(remoteStudyPlanningVos)) {
            return List.of();
        }
        List<RemoteStudyPlanningRecordVo> list = new ArrayList<>();

        for (RemoteStudyPlanningVo remoteStudyPlanningVo : remoteStudyPlanningVos) {
            List<RemoteStudyPlanningRecordVo> studyPlanningRecordList = remoteStudyPlanningVo.getStudyPlanningRecordList();
            if (CollUtil.isEmpty(studyPlanningRecordList)) {
                continue;
            }
            list.addAll(studyPlanningRecordList);
        }
        return list;
    }

    //小测批改 当日批改小测数量
    private Long getTestCorrectNum(List<RemoteCorrectionRecordVo> list) {
        if (CollUtil.isEmpty(list)) {
            return 0L;
        }

        return list.stream().filter(item ->
            UserConstants.CORRECTION_PERSON_TYPE_STAFF.equals(item.getCorrectionPersonType()) && UserConstants.CORRECTION_TYPE_TEST.equals(item.getCorrectionType())
        ).map(item -> item.getStudentId() + "_" + item.getStudyPlanningRecordId()).distinct().count();
    }


    @Override
    public void init() {
        IBranchStaffService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchStaffService init===========");
        LambdaQueryWrapper<BranchStaff> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchStaff::getBranchStaffId);
        List<BranchStaff> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========branchStaffService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchStaffById(item.getBranchStaffId());
        });
        log.info("===========branchStaffService init end===========");
    }

}
