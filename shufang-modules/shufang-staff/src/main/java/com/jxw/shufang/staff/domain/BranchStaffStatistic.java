package com.jxw.shufang.staff.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据对象 branch_staff_statistic
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("branch_staff_statistic")
public class BranchStaffStatistic extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工数据id
     */
    @TableId(value = "branch_staff_statistic_id")
    private Long branchStaffStatisticId;

    /**
     * 分店员工id
     */
    private Long branchStaffId;

    /**
     * 负责总人数
     */
    private Long totalStudentNum;

    /**
     * 体验人数
     */
    private Long experienceNum;

    /**
     * 新签人数
     */
    private Long newSignNum;

    /**
     * 续费人数
     */
    private Long renewNum;

    /**
     * 签约金额
     */
    private BigDecimal signAmount;

    /**
     * 学习次数（视频观看次数）
     */
    private Long studyVideoNum;

    /**
     * 批改次数
     */
    private Long markingNum;

    /**
     * 批改率（前端展示需加%）
     */
    private Long markingRate;

    /**
     * 反馈次数
     */
    private Long feedbackNum;

    /**
     * 反馈率（前端展示需加%）
     */
    private Long feedbackRate;

    /**
     * 跟进次数
     */
    private Long followUpNum;

    /**
     * 跟进会员比率（前端展示需加%）
     */
    private Long followUpRate;

    /**
     * 结转费用
     */
    private BigDecimal carryForwardAmount;

    /**
     * 当日学习人数（按照学习规划，当天的学习人数）
     */
    private Long dayStudyNum;

    /**
     * 开始时间（如果用本表，此处只精确到日）
     */
    private Date startTime;

    /**
     * 结束时间（如果用本表，此处只精确到日）
     */
    private Date endTime;


}
