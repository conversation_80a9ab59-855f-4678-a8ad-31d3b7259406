package com.jxw.shufang.staff.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.staff.domain.bo.FollowUpRecordBo;
import com.jxw.shufang.staff.domain.vo.FollowUpRecordVo;
import com.jxw.shufang.staff.service.IFollowUpRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 跟进记录
 * 前端访问路由地址为:/staff/management/followUpRecord
 *
 *
 * @date 2024-05-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/followUpRecord")
public class FollowUpRecordController extends BaseController {

    private final IFollowUpRecordService followUpRecordService;

    /**
     * 查询跟进记录列表
     */
    @SaCheckPermission("staff:followUpRecord:list")
    @GetMapping("/list")
    public TableDataInfo<FollowUpRecordVo> list(FollowUpRecordBo bo, PageQuery pageQuery) {
        return followUpRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出跟进记录列表
     */
    @SaCheckPermission("staff:followUpRecord:export")
    @Log(title = "跟进记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(FollowUpRecordBo bo, HttpServletResponse response) {
        List<FollowUpRecordVo> list = followUpRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "跟进记录", FollowUpRecordVo.class, response);
    }

    /**
     * 获取跟进记录详细信息
     *
     * @param followUpRecordId 主键
     */
    @SaCheckPermission("staff:followUpRecord:query")
    @GetMapping("/{followUpRecordId}")
    public R<FollowUpRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long followUpRecordId) {
        return R.ok(followUpRecordService.queryById(followUpRecordId));
    }

    /**
     * 新增跟进记录
     */
    @SaCheckPermission("staff:followUpRecord:add")
    @Log(title = "跟进记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody FollowUpRecordBo bo) {
        if (!LoginHelper.isBranchStaff()){
            return R.fail("只有门店员工才能新增跟进记录");
        }
        return toAjax(followUpRecordService.insertByBo(bo));
    }

    /**
     * 修改跟进记录
     */
    @SaCheckPermission("staff:followUpRecord:edit")
    @Log(title = "跟进记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody FollowUpRecordBo bo) {
        return toAjax(followUpRecordService.updateByBo(bo));
    }

    /**
     * 删除跟进记录
     *
     * @param followUpRecordIds 主键串
     */
    @SaCheckPermission("staff:followUpRecord:remove")
    @Log(title = "跟进记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{followUpRecordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] followUpRecordIds) {
        return toAjax(followUpRecordService.deleteWithValidByIds(List.of(followUpRecordIds), true));
    }
}
