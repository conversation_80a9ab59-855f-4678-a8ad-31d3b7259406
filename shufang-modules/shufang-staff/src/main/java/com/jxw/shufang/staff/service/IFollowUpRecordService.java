package com.jxw.shufang.staff.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.staff.domain.bo.FollowUpRecordBo;
import com.jxw.shufang.staff.domain.vo.FollowUpRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 跟进记录Service接口
 *
 * @date 2024-05-24
 */
public interface IFollowUpRecordService {

    /**
     * 查询跟进记录
     */
    FollowUpRecordVo queryById(Long followUpRecordId);

    /**
     * 查询跟进记录列表
     */
    TableDataInfo<FollowUpRecordVo> queryPageList(FollowUpRecordBo bo, PageQuery pageQuery);

    /**
     * 查询跟进记录列表
     */
    List<FollowUpRecordVo> queryList(FollowUpRecordBo bo);

    /**
     * 新增跟进记录
     */
    Boolean insertByBo(FollowUpRecordBo bo);

    /**
     * 修改跟进记录
     */
    Boolean updateByBo(FollowUpRecordBo bo);

    /**
     * 校验并批量删除跟进记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
