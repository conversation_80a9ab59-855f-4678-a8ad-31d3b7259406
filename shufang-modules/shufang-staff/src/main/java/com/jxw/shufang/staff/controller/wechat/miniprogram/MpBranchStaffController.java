package com.jxw.shufang.staff.controller.wechat.miniprogram;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import com.jxw.shufang.staff.service.IBranchStaffService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 分店员工--小程序端
 * <p>
 * 前端访问路由地址为:/staff/miniProgram/branch_staff
 *
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/miniProgram/branchStaff")
public class MpBranchStaffController extends BaseController {

    private final IBranchStaffService branchStaffService;


    /**
     * 获取员工基本信息
     */
    @GetMapping("/getInfo")
    public R<BranchStaffVo> getInfo() {
        if (!LoginHelper.isBranchStaff()) {
            return R.fail("非分店员工，无权访问");
        }
        BranchStaffVo info = branchStaffService.queryBranchStaffInfo(LoginHelper.getBranchStaffId());
        return R.ok(info);
    }


    /**
     * 获取今天的及时统计数据,不查统计表，因为不及时
     */
    @GetMapping("/getTimelyStatistics")
    public R<BranchStaffStatisticVo> getTimelyStatistics() {
        if (!LoginHelper.isBranchStaff()) {
            return R.fail("非分店员工，无权访问");
        }
        Date now = new Date();
        DateTime startDate = DateUtil.beginOfDay(now);
        DateTime endDate = DateUtil.endOfDay(now);
        BranchStaffStatisticVo statistics = branchStaffService.getTimelyStatistics(LoginHelper.getBranchStaffId(),startDate,endDate);
        return R.ok(statistics);
    }


}
