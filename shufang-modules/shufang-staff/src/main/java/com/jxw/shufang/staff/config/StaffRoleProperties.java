package com.jxw.shufang.staff.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: cyj
 * @date: 2025/7/18
 * @Description: 屏蔽的staff角色ID
 */
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "staff.role")
public class StaffRoleProperties {
    private Set<Long> disabledIds = new HashSet<>();
}
