package com.jxw.shufang.staff.domain.excel;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 分店员工excel导出模型，用于excel导出
 *
 */
@Data
@ExcelIgnoreUnannotated
public class BranchStaffExcel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "员工名称")
    private String staffName;

    @ExcelProperty(value = "员工账号")
    private String staffAccount;

    @ExcelProperty(value = "员工性别",converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_user_sex")
    private String staffSex;

    @ExcelProperty(value = "角色")
    private String roleNames;

    @ExcelProperty(value = "全部用户数")
    private Integer allStudentCount;

    @ExcelProperty(value = "负责会员数")
    private Integer effectiveStudentCount;

    @ExcelProperty(value = "考勤ID")
    private String checkWorkAttendanceId;

    @ExcelProperty(value = "帐号状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    @ExcelProperty(value = "添加时间")
    private Date createTime;

    @ExcelProperty(value = "所属部门")
    private String branchName;

}
