package com.jxw.shufang.staff.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.staff.domain.BranchStaff;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 分店员工
（其他属性采用sys_user对应数据）视图对象 branch_staff
 *
 * @date 2024-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BranchStaff.class)
public class BranchStaffVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分店员工id
     */
    //@ExcelProperty(value = "分店员工id")
    private Long branchStaffId;

    /**
     * 考勤id
     */
    @ExcelProperty(value = "考勤id")
    private String checkWorkAttendanceId;

    /**
     * 分店授权类型id
     */
    //@ExcelProperty(value = "分店id")
    private Long branchId;

    /**
     * 真实头像(图片ossid)
     */
    //@ExcelProperty(value = "真实头像(图片ossid)")
    private Long realAvatar;

    /**
     * 微信二维码(图片ossid)
     */
    //@ExcelProperty(value = "微信二维码(图片ossid)")
    private Long wechatQrCode;

    /**
     * 对应sysUser里面的id
     */
    private Long createBy;

    private Date createTime;

    private Long createDept;

    private Long updateBy;

    private Date updateTime;


    /**
     * 门店员工统计数据
     */
    private BranchStaffStatisticVo branchStaffStatistic;

    /**
     * 用户信息
     */
    private RemoteUserVo user;

    /**
     * 门店
     */
    private RemoteBranchVo branch;

    /**
     * 角色名称列表
     */
    private List<String> roleNameList;


    /**
     * 总会员数量
     */
    private Integer totalStudentCount;

    /**
     * 负责会员数量
     */
    private Integer chargeStudentCount;



}
