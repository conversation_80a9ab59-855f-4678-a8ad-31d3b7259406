package com.jxw.shufang.staff.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.staff.domain.vo.BranchStaffVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BranchStaffVoConvert extends BaseMapper<BranchStaffVo, RemoteStaffVo> {
}




