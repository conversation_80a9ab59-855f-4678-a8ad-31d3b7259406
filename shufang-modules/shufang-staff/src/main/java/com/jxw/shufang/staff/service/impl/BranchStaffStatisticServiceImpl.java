package com.jxw.shufang.staff.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.staff.domain.BranchStaffStatistic;
import com.jxw.shufang.staff.domain.bo.BranchStaffStatisticBo;
import com.jxw.shufang.staff.domain.vo.BranchStaffStatisticVo;
import com.jxw.shufang.staff.mapper.BranchStaffStatisticMapper;
import com.jxw.shufang.staff.service.IBranchStaffStatisticService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据Service业务层处理
 *
 * @date 2024-02-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BranchStaffStatisticServiceImpl implements IBranchStaffStatisticService, BaseService {

    private final BranchStaffStatisticMapper baseMapper;

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @Override
    public BranchStaffStatisticVo queryById(Long branchStaffStatisticId){
        return baseMapper.selectVoById(branchStaffStatisticId);
    }

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据列表
     */
    @Override
    public TableDataInfo<BranchStaffStatisticVo> queryPageList(BranchStaffStatisticBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BranchStaffStatistic> lqw = buildQueryWrapper(bo);



        Page<BranchStaffStatisticVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据列表
     */
    @Override
    public List<BranchStaffStatisticVo> queryList(BranchStaffStatisticBo bo) {
        LambdaQueryWrapper<BranchStaffStatistic> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BranchStaffStatistic> buildQueryWrapper(BranchStaffStatisticBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BranchStaffStatistic> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBranchStaffId() != null, BranchStaffStatistic::getBranchStaffId, bo.getBranchStaffId());
        lqw.eq(bo.getTotalStudentNum() != null, BranchStaffStatistic::getTotalStudentNum, bo.getTotalStudentNum());
        lqw.eq(bo.getExperienceNum() != null, BranchStaffStatistic::getExperienceNum, bo.getExperienceNum());
        lqw.eq(bo.getNewSignNum() != null, BranchStaffStatistic::getNewSignNum, bo.getNewSignNum());
        lqw.eq(bo.getRenewNum() != null, BranchStaffStatistic::getRenewNum, bo.getRenewNum());
        lqw.eq(bo.getSignAmount() != null, BranchStaffStatistic::getSignAmount, bo.getSignAmount());
        lqw.eq(bo.getStudyVideoNum() != null, BranchStaffStatistic::getStudyVideoNum, bo.getStudyVideoNum());
        lqw.eq(bo.getMarkingNum() != null, BranchStaffStatistic::getMarkingNum, bo.getMarkingNum());
        lqw.eq(bo.getMarkingRate() != null, BranchStaffStatistic::getMarkingRate, bo.getMarkingRate());
        lqw.eq(bo.getFeedbackNum() != null, BranchStaffStatistic::getFeedbackNum, bo.getFeedbackNum());
        lqw.eq(bo.getFeedbackRate() != null, BranchStaffStatistic::getFeedbackRate, bo.getFeedbackRate());
        lqw.eq(bo.getFollowUpNum() != null, BranchStaffStatistic::getFollowUpNum, bo.getFollowUpNum());
        lqw.eq(bo.getFollowUpRate() != null, BranchStaffStatistic::getFollowUpRate, bo.getFollowUpRate());
        lqw.eq(bo.getCarryForwardAmount() != null, BranchStaffStatistic::getCarryForwardAmount, bo.getCarryForwardAmount());
        lqw.eq(bo.getDayStudyNum() != null, BranchStaffStatistic::getDayStudyNum, bo.getDayStudyNum());
        lqw.eq(bo.getStartTime() != null, BranchStaffStatistic::getStartTime, bo.getStartTime());
        lqw.eq(bo.getEndTime() != null, BranchStaffStatistic::getEndTime, bo.getEndTime());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchStaffIdList()), BranchStaffStatistic::getBranchStaffId, bo.getBranchStaffIdList());
        if (bo.getStaffStatisticEndTime() != null && bo.getStaffStatisticStartTime() != null){
            lqw.ge( BranchStaffStatistic::getStartTime,bo.getStaffStatisticStartTime());
            lqw.le( BranchStaffStatistic::getEndTime,bo.getStaffStatisticEndTime());
        }
        return lqw;
    }

    /**
     * 新增分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @Override
    public Boolean insertByBo(BranchStaffStatisticBo bo) {
        BranchStaffStatistic add = MapstructUtils.convert(bo, BranchStaffStatistic.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBranchStaffStatisticId(add.getBranchStaffStatisticId());
        }
        return flag;
    }

    /**
     * 修改分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @Override
    public Boolean updateByBo(BranchStaffStatisticBo bo) {
        BranchStaffStatistic update = MapstructUtils.convert(bo, BranchStaffStatistic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BranchStaffStatistic entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除分店员工数据统计（顾问数据）根据实际情况，可由本递增，也可不用本直接查询数据
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Date getLatestTime() {
        LambdaQueryWrapper<BranchStaffStatistic> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(BranchStaffStatistic::getStartTime);
        lqw.select(BranchStaffStatistic::getStartTime);
        lqw.last("LIMIT 1");
        BranchStaffStatistic branchStaffStatistic = baseMapper.selectOne(lqw);
        if (branchStaffStatistic != null) {
            return branchStaffStatistic.getStartTime();
        }
        return null;
    }

    @Cacheable(value = "branchStaffStatistic", key = "#branchStaffStatisticId",condition = "#branchStaffStatisticId != null")
    @Override
    public BranchStaffStatistic queryBranchStaffStatisticById(Long branchStaffStatisticId) {
        return baseMapper.selectById(branchStaffStatisticId);
    }

    @CacheEvict(value = "branchStaffStatistic",allEntries= true)
    public void cleanCache(){
        log.info("===========branchStaffStatisticService cleanCache===========");
    }

    @Override
    public void init() {
        IBranchStaffStatisticService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========branchStaffStatisticService init===========");
        LambdaQueryWrapper<BranchStaffStatistic> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(BranchStaffStatistic::getBranchStaffStatisticId);
        List<BranchStaffStatistic> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========branchStaffStatisticService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryBranchStaffStatisticById(item.getBranchStaffStatisticId());
        });
        log.info("===========branchStaffStatisticService init end===========");
    }

    @Override
    public boolean batchSaveOrUpdateStatistic(List<BranchStaffStatisticBo> list, Date date) {
        DateTime endTime = DateUtil.endOfDay(date);
        DateTime startTime = DateUtil.beginOfDay(date);
        List<Long> branchStaffIds = list.stream().map(BranchStaffStatisticBo::getBranchStaffId).distinct().toList();
        LambdaQueryWrapper<BranchStaffStatistic> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BranchStaffStatistic::getBranchStaffId, branchStaffIds);
        wrapper.ge(BranchStaffStatistic::getStartTime, startTime);
        wrapper.le(BranchStaffStatistic::getEndTime, endTime);
        wrapper.select(BranchStaffStatistic::getBranchStaffId, BranchStaffStatistic::getBranchStaffStatisticId);
        List<BranchStaffStatistic> oldList = baseMapper.selectList(wrapper);
        Map<Long, BranchStaffStatistic> oldMap = oldList.stream().collect(Collectors.toMap(BranchStaffStatistic::getBranchStaffId, Function.identity()));
        List<BranchStaffStatisticBo> updateList = list.stream().map(item -> {
            BranchStaffStatistic old = oldMap.get(item.getBranchStaffId());
            if (old!=null){
                item.setBranchStaffStatisticId(old.getBranchStaffStatisticId());
            }else {
                item.setStartTime(startTime);
                //mysql的bug，如果插入的时间毫秒数大于500，会自动向后加1秒，导致时间错乱，所以这里需要将毫秒数置为0
                item.setEndTime(endTime.setField(DateField.MILLISECOND, 0));
            }
            return item;
        }).toList();
        List<BranchStaffStatistic> convert = MapstructUtils.convert(updateList, BranchStaffStatistic.class);
        return baseMapper.insertOrUpdateBatch(convert);
    }



}
