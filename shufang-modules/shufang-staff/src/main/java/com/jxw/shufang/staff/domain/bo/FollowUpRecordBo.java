package com.jxw.shufang.staff.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.staff.domain.FollowUpRecord;

import java.util.Date;
import java.util.List;

/**
 * 跟进记录业务对象 follow_up_record
 *
 * @date 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FollowUpRecord.class, reverseConvertGenerate = false)
public class FollowUpRecordBo extends BaseEntity {

    /**
     * 跟进记录id
     */
    @NotNull(message = "跟进记录id不能为空", groups = { EditGroup.class })
    private Long followUpRecordId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 跟进类型（对应字典值，如微信沟通、电话访问、登门拜访）
     */
    @NotBlank(message = "跟进类型（对应字典值，如微信沟通、电话访问、登门拜访）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String followUpType;

    /**
     * 跟进内容（包含文字、图片、音频）
     */
    @NotBlank(message = "跟进内容（包含文字、图片、音频）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String followUpContent;

    /**
     * 跟进时间开始
     */
    private Date followUpRecordCreateTimeStart;

    /**
     * 跟进时间结束
     */
    private Date followUpRecordCreateTimeEnd;

    /**
     * studentIdList
     */
    private List<Long> studentIdList;


}
