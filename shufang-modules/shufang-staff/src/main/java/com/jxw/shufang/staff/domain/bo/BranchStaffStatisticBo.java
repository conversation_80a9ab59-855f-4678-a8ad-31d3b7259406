package com.jxw.shufang.staff.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.staff.domain.BranchStaffStatistic;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分店员工数据统计（顾问数据）
根据实际情况，可由本递增，也可不用本直接查询数据业务对象 branch_staff_statistic
 *
 * @date 2024-02-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BranchStaffStatistic.class, reverseConvertGenerate = false)
public class BranchStaffStatisticBo extends BaseEntity {

    /**
     * 分店员工数据id
     */
    @NotNull(message = "分店员工数据id不能为空", groups = { EditGroup.class })
    private Long branchStaffStatisticId;

    /**
     * 分店员工id
     */
    @NotNull(message = "分店员工id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchStaffId;

    /**
     * 负责总人数
     */
    @NotNull(message = "负责总人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long totalStudentNum;

    /**
     * 体验人数
     */
    @NotNull(message = "体验人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long experienceNum;

    /**
     * 新签人数
     */
    @NotNull(message = "新签人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long newSignNum;

    /**
     * 续费人数
     */
    @NotNull(message = "续费人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long renewNum;

    /**
     * 签约金额
     */
    @NotNull(message = "签约金额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal signAmount;

    /**
     * 学习次数（视频观看次数）
     */
    @NotNull(message = "学习次数（视频观看次数）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studyVideoNum;

    /**
     * 批改次数
     */
    @NotNull(message = "批改次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long markingNum;

    /**
     * 批改率（前端展示需加%）
     */
    @NotNull(message = "批改率（前端展示需加%）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long markingRate;

    /**
     * 反馈次数
     */
    @NotNull(message = "反馈次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long feedbackNum;

    /**
     * 反馈率（前端展示需加%）
     */
    @NotNull(message = "反馈率（前端展示需加%）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long feedbackRate;

    /**
     * 跟进次数
     */
    @NotNull(message = "跟进次数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long followUpNum;

    /**
     * 跟进会员比率（前端展示需加%）
     */
    @NotNull(message = "跟进会员比率（前端展示需加%）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long followUpRate;

    /**
     * 结转费用
     */
    @NotNull(message = "结转费用不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal carryForwardAmount;

    /**
     * 当日学习人数（按照学习规划，当天的学习人数）
     */
    @NotNull(message = "当日学习人数（按照学习规划，当天的学习人数）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long dayStudyNum;

    /**
     * 开始时间（如果用本表，此处只精确到日）
     */
    @NotNull(message = "开始时间（如果用本表，此处只精确到日）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 结束时间（如果用本表，此处只精确到日）
     */
    @NotNull(message = "结束时间（如果用本表，此处只精确到日）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    private List<Long> branchStaffIdList;

    private Date staffStatisticStartTime;

    private Date staffStatisticEndTime;



}
