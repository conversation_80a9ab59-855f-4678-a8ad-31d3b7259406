<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-staff</artifactId>

    <description>
        shufang-staff员工模块
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-sentinel</artifactId>
        </dependency>

        <!-- jxw Common Log -->
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-dict</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-seata</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-security</artifactId>
        </dependency>



        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-branch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-staff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-student</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-order</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-report</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-resource</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
