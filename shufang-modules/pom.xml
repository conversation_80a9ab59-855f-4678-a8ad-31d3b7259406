<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>shufang-system</module>
        <module>shufang-gen</module>
        <module>shufang-resource</module>
        <module>shufang-asynctask</module>
        <module>shufang-wxmp</module>
        <module>shufang-branch</module>
        <module>shufang-order</module>
        <module>shufang-staff</module>
        <module>shufang-student</module>
        <module>shufang-ext-resource</module>
        <module>shufang-report</module>
        <module>shufang-crm</module>
    </modules>

    <artifactId>shufang-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        shufang-modules业务模块
    </description>

    <dependencies>
        <!-- 自定义负载均衡(多团队开发使用) -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-loadbalancer</artifactId>-->
<!--        </dependency>-->

        <!-- ELK 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-logstash</artifactId>-->
<!--        </dependency>-->

        <!-- skywalking 日志收集 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-skylog</artifactId>-->
<!--        </dependency>-->

        <!-- prometheus 监控 -->
<!--        <dependency>-->
<!--            <groupId>com.jxw.shufang</groupId>-->
<!--            <artifactId>shufang-common-prometheus</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
    </dependencies>

</project>
