#FROM findepi/graalvm:java17-native
FROM registry.cn-shanghai.aliyuncs.com/jxw-midsoftware/jxwopenjdk:17.0.6

MAINTAINER Lion Li

RUN mkdir -p /jxw/extResource/logs \
    /jxw/extResource/temp \
    /jxw/extResource/agent

WORKDIR /jxw/extResource

ENV SERVER_PORT=9900 LANG=C.UTF-8 LC_ALL=C.UTF-8 JAVA_OPTS="-Ddubbo.network.interface.preferred=eth0"

EXPOSE ${SERVER_PORT}

ADD ./target/shufang-ext-resource.jar ./app.jar

ENTRYPOINT java -Djava.security.egd=file:/dev/./urandom -Dserver.port=${SERVER_PORT} \
           #-Dskywalking.agent.service_name=shufang-ext-resource \
           #-javaagent:/jxw/skywalking/agent/skywalking-agent.jar \
           -jar app.jar \
           -XX:+HeapDumpOnOutOfMemoryError -Xlog:gc*,:time,tags,level -XX:+UseZGC ${JAVA_OPTS}

