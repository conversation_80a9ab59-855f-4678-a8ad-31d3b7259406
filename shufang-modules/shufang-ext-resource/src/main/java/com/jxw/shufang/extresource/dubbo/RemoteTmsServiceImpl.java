package com.jxw.shufang.extresource.dubbo;

import com.jxw.shufang.extresource.api.RemoteTmsService;
import com.jxw.shufang.extresource.api.domain.bo.QueryTestPaperFileRequest;
import com.jxw.shufang.extresource.api.domain.vo.TestPaperFileVO;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.service.ApiTmsCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 17:08
 * @Version 1
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteTmsServiceImpl implements RemoteTmsService {
    @Resource
    private ApiTmsCommonService apiTmsCommonService;
    @Override
    public List<TestPaperFileVO> queryTestPaperFileByPaperId(QueryTestPaperFileRequest request) {
        ApiResp<List<TestPaperFileVO>> apiResp = apiTmsCommonService.queryTestPaperFileByPaperId(request);
        return apiResp.getData();
    }
}
