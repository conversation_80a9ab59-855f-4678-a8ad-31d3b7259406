package com.jxw.shufang.extresource.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.UnicodeUtil;
import com.jxw.shufang.extresource.config.SecretProperties;
import com.jxw.shufang.extresource.config.TaskExecutorConfig;
import com.jxw.shufang.extresource.domain.bo.ExtAiAnalysisBo;
import com.jxw.shufang.extresource.service.IAiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiServiceImpl implements IAiService {

    // 请求超时时间
    private final Long requestTimeout = 60000L;

    private final SecretProperties secretProperties;

    @Resource
    private ThreadPoolTaskExecutor aiTaskExecutor;

    private OkHttpClient client;

    @PostConstruct
    public void init() {
        client = new OkHttpClient.Builder()
            .connectionPool(new ConnectionPool(Runtime.getRuntime().availableProcessors() * 16, 60L, TimeUnit.SECONDS))
            // 连接超时
            .connectTimeout(requestTimeout, TimeUnit.SECONDS)
            // 读取超时（关键！与SSE的60秒对齐）
            .readTimeout(requestTimeout + 5000L, TimeUnit.SECONDS)
            // 写入超时
            .writeTimeout(requestTimeout, TimeUnit.SECONDS)
            .callTimeout(requestTimeout + 8000L, TimeUnit.SECONDS)
            .build();
    }

    @Override
    public Object aiAnalysis(ExtAiAnalysisBo extAiAnalysisBo) {
        // 1. 构建统一请求
        MultipartBody.Builder builder = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("problem_url", extAiAnalysisBo.getProblemUrl())
            .addFormDataPart("stream", String.valueOf(extAiAnalysisBo.getStream()))
            .addFormDataPart("selectModel", extAiAnalysisBo.getSelectModel());
        if (CharSequenceUtil.isNotBlank(extAiAnalysisBo.getAnswerUrl())) {
            builder.addFormDataPart("answer_url", extAiAnalysisBo.getAnswerUrl());
        }
        if (CharSequenceUtil.isNotBlank(extAiAnalysisBo.getAnswerContent())) {
            builder.addFormDataPart("answer_content", extAiAnalysisBo.getAnswerContent());
        }
        MultipartBody body = builder.build();

        Request request = new Request.Builder()
            .url(secretProperties.getAiUrl() + secretProperties.getAiAnalysisApi())
            .post(body)
            .addHeader("Accept", extAiAnalysisBo.getStream() > 0 ? "text/plain" : "application/json")
            .addHeader("Connection", "keep-alive")
            .build();

        // 2. 根据模式选择响应方式
        try {
            if (extAiAnalysisBo.getStream() > 0) {
                // 使用SseEmitter实现流式响应
                SseEmitter emitter = new SseEmitter(requestTimeout);
                aiTaskExecutor.execute(() -> {
                    try (Response response = client.newCall(request).execute();
                         InputStream input = response.body().byteStream()) {
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = input.read(buffer)) != -1) {
                            // 读取数据块
                            String chunk = new String(buffer, 0, bytesRead);
                            // 发送SSE事件
                            chunk = UnicodeUtil.toString(chunk);
                            emitter.send(chunk);
                        }
                        emitter.complete();
                    } catch (Exception e) {
                      log.error("SSE Exception error: ", e);
                      emitter.completeWithError(e);
                    }
                });
                emitter.onCompletion(() -> log.info("SSE completed"));
                emitter.onTimeout(() -> log.info("SSE timed out"));
                emitter.onError((ex) -> log.info("SSE error: ", ex));
                return emitter;
            } else {
                // 非流式响应
                try (Response response = client.newCall(request).execute()) {
                    byte[] responseBody = null;
                    if (response.body() != null) {
                        responseBody = response.body().bytes();
                    }
                    return ResponseEntity.ok()
                        .contentType(MediaType.APPLICATION_JSON)
                        .body(responseBody);
                }
            }
        } catch (IOException e) {
            log.error("SSE IOException error: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("SSE IOException error: " + e.getMessage());
        } catch (Exception e) {
            log.error("SSE Exception error: ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("SSE Exception error: " + e.getMessage());
        }
    }
    @PreDestroy
    public void shutdown() {
        client.dispatcher().executorService().shutdown();
        client.connectionPool().evictAll();
    }
}
