package com.jxw.shufang.extresource.dubbo;


import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.extresource.api.domain.vo.RemoteTeacherVo;
import com.jxw.shufang.extresource.domain.vo.TeacherVo;
import com.jxw.shufang.extresource.service.ApiExtCourseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.api.RemoteExtVideoService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.KnowledgeVideoBo;
import com.jxw.shufang.extresource.domain.vo.VideoVo;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteVideoServiceImpl implements RemoteExtVideoService {

    private final ApiExtCourseService apiExtCourseService;
    private final RemoteTeacherServiceImpl remoteTeacherService;

    @Override
    public List<RemoteKnowledgeVideoVo> getKnowledgeVideoList(RemoteKnowledgeVideoBo remoteKnowledgeVideoBo) {
        if (CollUtil.isEmpty(remoteKnowledgeVideoBo.getKnowledgeIdList()) && remoteKnowledgeVideoBo.getKnowledgeId() == null) {
            log.error("getKnowledgeVideoList: knowledgeIdList or knowledgeId is empty");
            return new ArrayList<>();
        }
        if (remoteKnowledgeVideoBo.getKnowledgeId() != null) {
            remoteKnowledgeVideoBo.setKnowledgeIdList(CollUtil.newArrayList(remoteKnowledgeVideoBo.getKnowledgeId()));
        }

        //因为接口没有通过idList的方法提供的查询，所以只能使用遍历加异步的方式来提高速度了
        Map<Long, Future<ApiResp<VideoVo>>> waitMap = new HashMap<>();
        Map<Long, VideoVo> resultMap = new HashMap<>();

        for (Long id : remoteKnowledgeVideoBo.getKnowledgeIdList()) {
//            KnowledgeVideoBo convert = MapstructUtils.convert(remoteKnowledgeVideoBo, KnowledgeVideoBo.class);
//            convert.setCatalogId(id);
            KnowledgeVideoBo convert = new KnowledgeVideoBo();
            convert.setCatalogId(id);
            Future<ApiResp<VideoVo>> apiRespFuture = apiExtCourseService.asyncGetCourseVideos(convert);
            waitMap.put(id, apiRespFuture);
        }
        List<Long> fileIds = new ArrayList<>();
        //获取结果
        if (CollUtil.isNotEmpty(waitMap)) {
            for (Long id : waitMap.keySet()) {
                try {
                    ApiResp<VideoVo> videoVoApiResp = waitMap.get(id).get();
                    if (Objects.nonNull(videoVoApiResp.getData())) {
                        VideoVo video = videoVoApiResp.getData();
                        resultMap.put(id, video);
                        fileIds.add(video.getId());
                    }
                }catch (Exception e) {
                    log.error("getKnowledgeVideoList error", e);
                }
            }
        }
        Map<Long, RemoteTeacherVo> fileIdTeacherMap = remoteTeacherService.getFileIdTeacherMap(fileIds);

        List<RemoteKnowledgeVideoVo> result = new ArrayList<>();

        for (Map.Entry<Long, VideoVo> entry : resultMap.entrySet()) {
            RemoteKnowledgeVideoVo vo = new RemoteKnowledgeVideoVo();
            vo.setKnowledgeId(entry.getKey());
            VideoVo video = entry.getValue();
            vo.setVideoList(Arrays.asList(MapstructUtils.convert(video, RemoteVideoVo.class)));
            //vo.setVideoTotalDuration(entry.getValue().stream().filter(Objects::nonNull).filter(e -> e.getDuration() != null).mapToLong(VideoVo::getDuration).sum());
            //需求更改，视频总时长直接取第一个视频的时长
            if (CollUtil.isNotEmpty(vo.getVideoList())) {
                vo.setVideoTotalDuration(vo.getVideoList().get(0).getDuration());
            }

            vo.setTeacher(fileIdTeacherMap.getOrDefault(video.getId(), null));
            result.add(vo);
        }
        return result;
    }

    @Override
    public RemoteKnowledgeVideoVo getKnowledgeVideoListById(Long knowledgeId) {
        RemoteKnowledgeVideoBo remoteKnowledgeVideoBo = new RemoteKnowledgeVideoBo();
        remoteKnowledgeVideoBo.setKnowledgeId(knowledgeId);
        List<RemoteKnowledgeVideoVo> knowledgeVideoList = getKnowledgeVideoList(remoteKnowledgeVideoBo);
        if (CollUtil.isNotEmpty(knowledgeVideoList)) {
            return knowledgeVideoList.get(0);
        }
        return null;
    }


}
