package com.jxw.shufang.extresource.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PhoneUtil;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.utils.StringUtils;
import com.dtflys.forest.utils.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.extresource.config.SecretProperties;
import com.jxw.shufang.extresource.constants.BasicConst;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.interceptor.HeaderInterceptor;
import com.jxw.shufang.extresource.service.ISmsService;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.SortedMap;
import java.util.TreeMap;

@RequiredArgsConstructor
@Service
@Slf4j
public class SmsServiceImpl implements ISmsService {

    private final SecretProperties secretProperties;

    private static final String AES_ALGORITHM = "AES";


    /**
     * 加密
     *
     * @param plaintext 待加密的字符串 手机号
     * @param key       加密密钥 appsecret
     * @return 加密后的字符串
     * @throws Exception 加密过程中的异常
     */
    public static String encryptToECB(String plaintext, String key) {
        // 添加时间戳到待加密的数据
        long currentTimeMillis = System.currentTimeMillis();
        String dataToEncrypt = plaintext + ":" + currentTimeMillis;

        // 加密数据
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), AES_ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            byte[] encryptedBytes = cipher.doFinal(dataToEncrypt.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new ServiceException("加密失败:" + e.getMessage());
        }

    }

    @Override
    public void sendSms(String mobile) {
        //校验手机号码
        Assert.isTrue(PhoneUtil.isMobile(mobile), () -> new ServiceException("手机号格式错误"));


        String smsUrl = secretProperties.getSmsUrl();
        String smsAppId = secretProperties.getSmsAppId();
        String smsAppSecret = secretProperties.getSmsAppSecret();

        //请求参数设置
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("mobile", mobile);
        paramMap.put("signature", URLEncoder.encode(encryptToECB(mobile, smsAppSecret), StandardCharsets.UTF_8) );
        paramMap.put("appId", smsAppId);

        //String encodedValue = URLEncoder.encode(originalValue, StandardCharsets.UTF_8.toString());
        //请求接口
        ForestRequest<?> forestRequest = Forest.get(smsUrl + "/sms/api/sms/send")
            .addQuery(paramMap)
            .addInterceptorAttribute(HeaderInterceptor.class, BasicConst.IGNORE_TOKEN_ATTR_NAME, true);

        ApiResp<Object> execute = forestRequest.execute(new TypeReference<>() {
        });
        log.info("发送验证码返回结果：{}", execute);
        if (!execute.isSuccess()) {
            throw new ServiceException("发送失败：" + execute.getMsg());
        }
    }

    @Override
    public void checkCode(String mobile, String code) {
        Assert.isTrue(PhoneUtil.isMobile(mobile), () -> new ServiceException("手机号格式错误"));
        Assert.isTrue(StringUtils.isNotBlank(code), () -> new ServiceException("验证码不能为空"));

        String smsUrl = secretProperties.getSmsUrl();
        String smsAppId = secretProperties.getSmsAppId();
        String smsAppSecret = secretProperties.getSmsAppSecret();
        //请求参数设置
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("mobile", mobile);
        paramMap.put("signature", URLEncoder.encode(encryptToECB(mobile, smsAppSecret), StandardCharsets.UTF_8) );
        paramMap.put("appId", smsAppId);
        paramMap.put("smsCode", code);

        //请求接口
        ForestRequest<?> forestRequest = Forest.get(smsUrl + "/sms/api/sms/check")
            .addQuery(paramMap)
            .addInterceptorAttribute(HeaderInterceptor.class, BasicConst.IGNORE_TOKEN_ATTR_NAME, true);
        ApiResp<Object> execute = forestRequest.execute(new TypeReference<>() {
        });
        log.info("校验验证码返回结果：{}", execute);
        if (!execute.isSuccess()) {
            throw new ServiceException("验证失败：" + execute.getMsg());
        }
    }
}
