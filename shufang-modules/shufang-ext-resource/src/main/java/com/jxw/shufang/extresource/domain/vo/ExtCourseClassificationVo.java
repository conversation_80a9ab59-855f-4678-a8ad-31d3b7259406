package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
@NoArgsConstructor
@Data
public class ExtCourseClassificationVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    private Long id;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 学段
     */
    private ExtCoursePhaseVo phase;

    /**
     * 科目
     */
    private ExtCourseSubjectVo subject;

    /**
     * 年级
     */
    private ExtCourseGradeVo standardGrade;

    /**
     * 学期
     */
    private ExtCourseSemesterVo semester;

    /**
     * 教材版本
     */
    private ExtCourseEditionVo edition;

    /**
     * 课程封面图url
     */
    private String coverUrl;

    /**
     * 课程简介
     */
    private String intro;


}
