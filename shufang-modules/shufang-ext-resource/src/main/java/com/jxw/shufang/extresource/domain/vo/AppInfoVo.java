package com.jxw.shufang.extresource.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * 应用信息
 */
@Data
public class AppInfoVo {

    /**
     * 应用ID
     */
    private Integer id;

    /**
     * 尾标
     */
    private String apkIsbn;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 版本号
     */
    private Integer versionCode;

    /**
     * 版本名
     */
    private String versionName;

    /**
     * 更新内容
     */
    private String updateContent;

    /**
     * 应用名
     */
    private String fileName;

    /**
     * 文件大小，单位：byte
     */
    private Integer fileSize;

    /**
     * 文件存放路径
     */
    @JsonIgnore
    private String filePath;

    /**
     * 文件下载地址
     */
    private String url;

    /**
     * 序列号，多个以逗号分割
     */
    @JsonIgnore
    private String serialNos;

    /**
     * 是否强制更新
     */
    private Integer forceUpdate;

    /**
     * 是否推送
     */
    private Integer updateFlag;

    /**
     * apk包签名字符串
     */
    private String sign;
}
