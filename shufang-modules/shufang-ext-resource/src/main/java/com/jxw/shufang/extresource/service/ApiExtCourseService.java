package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.*;
import com.jxw.shufang.extresource.domain.vo.*;

import java.util.List;
import java.util.concurrent.Future;

public interface ApiExtCourseService extends ApiBaseService{

    /**
     * 学段列表
     */
    @Get("/tms/api/excellent-course/phase/list")
    ApiResp<List<ExtCoursePhaseVo>> getPhaseList();

    /**
     * 科目列表
     */
    @Get("/tms/api/excellent-course/subject/list")
    ApiResp<List<ExtCourseSubjectVo>> getSubjectList();



    /**
     * 年级列表
     */
    @Get("/tms/api/excellent-course/standard-grade/list")
    ApiResp<List<ExtCourseGradeVo>> getGradeList();


    /**
     * 学期列表
     */
    @Get("/tms/api/excellent-course/semester/list")
    ApiResp<List<ExtCourseSemesterVo>> getSemesterList();


    /**
     * 教材版本列表
     */
    @Get("/tms/api/excellent-course/edition/list")
    ApiResp<List<ExtCourseEditionVo>> getEditionList();

    /**
     * 课程分类列表
     */
    @Get("/tms/api/excellent-course/page")
    ApiResp<ApiPage<ExtCourseClassificationVo>> getClassificationPage(@Query ExtCourseClassificationBo bo);

    /**
     * 培优课程目录列表
     */
    @Get("/tms/api/excellent-course-catalog/page")
    ApiResp<ApiPage<ExtCourseCatalogVo>> getCourseCatalogPage(@Query ExtCourseCatalogBo bo);

    /**
     * 培优课程目录（树状结构）
     */
    @Get("/tms/api/excellent-course-catalog/tree")
    ApiResp<ApiPage<ExtCourseCatalogVo>> getCourseCatalogTree(@Query ExtCourseCatalogBo bo);

    /**
     * 学-培优视频
     */
    @Get("/tms/api/excellent-course/video/get")
    ApiResp<VideoVo> getCourseVideo(@Query KnowledgeVideoBo courseVideoBo);

    /**
     * 学-培优视频
     */
    @Get(value = "/tms/api/excellent-course/video/get", async = true)
    Future<ApiResp<VideoVo>> asyncGetCourseVideos(@Query KnowledgeVideoBo courseVideoBo);

    /**
     * 获取对应资源
     */
    @Get("/tms/api/excellent-course/resource/get")
    ApiResp<KnowledgeResourceVo> getCourseResource(@Query KnowledgeResourceBo courseResourceBo);


    /**
     * 获取对应资源
     */
    @Get(value = "/tms/api/excellent-course/resource/get",async = true)
    Future<ApiResp<KnowledgeResourceVo>> asyncGetCourseResource(@Query KnowledgeResourceBo courseResourceBo);

    /**
     * 获取资源id是否是否资源完整
     *
     * @param
     * @return
     */
    @Post(value = "/tms/api/excellent-course-catalog/sourceComplete")
    ApiResp<KnowledgeSourceCompleteVo> getSourceComplete(@JSONBody List<Long> knowledgeIdList);
}
