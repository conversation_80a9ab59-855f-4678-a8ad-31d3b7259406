package com.jxw.shufang.extresource.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.RemoteVideoLabelService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeNoteVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoLabelVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.VideoLabelVo;
import com.jxw.shufang.extresource.service.ApiVideoLabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteVideoLabelServiceImpl implements RemoteVideoLabelService {

    private final ApiVideoLabelService apiVideoLabelService;
    @DubboReference
    private RemoteKnowledgeService remoteKnowledgeService;

    @Override
    public List<RemoteVideoLabelVo> listVideoLabels(Long videoId) {
        return listVideoLabels(videoId, false);
    }

    @Override
    public List<RemoteVideoLabelVo> listVideoLabels(Long videoId, Boolean needKnowledgeNote) {
        ApiResp<List<VideoLabelVo>> apiResp = apiVideoLabelService.listVideoLabels(videoId);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            List<VideoLabelVo> videoLabelVos = apiResp.getData();
            if (CollUtil.isEmpty(videoLabelVos)) {
                return List.of();
            }
            List<RemoteVideoLabelVo> remoteVideoLabelVos = MapstructUtils.convert(videoLabelVos, RemoteVideoLabelVo.class);

            // 获取知识点笔记
            if (Boolean.TRUE.equals(needKnowledgeNote) && CollUtil.isNotEmpty(remoteVideoLabelVos)) {
                List<Integer> hasNoteKnowledgeIds = remoteVideoLabelVos.stream()
                    .filter(remoteVideoLabelVo -> {
                        RemoteKnowledgeVo knowledge = remoteVideoLabelVo.getKnowledge();
                        return knowledge != null && knowledge.getHasNote() == 1;
                    })
                    .map(RemoteVideoLabelVo::getKnowledgeId)
                    .toList();
                processKnowledgeNotes(remoteVideoLabelVos, hasNoteKnowledgeIds);
            }
            return remoteVideoLabelVos;
        }
        return List.of();
    }

    private void processKnowledgeNotes(List<RemoteVideoLabelVo> remoteVideoLabelVos, List<Integer> knowledgeIds) {
        if (CollUtil.isNotEmpty(knowledgeIds)) {
            List<RemoteKnowledgeNoteVo> knowledgeNoteVos = remoteKnowledgeService.listKnowledgeNotes(knowledgeIds);

            Map<Integer, RemoteKnowledgeNoteVo> knowledgeIdNoteMap = knowledgeNoteVos.stream().collect(Collectors.toMap(RemoteKnowledgeNoteVo::getKnowledgeId, Function.identity()));

            remoteVideoLabelVos.forEach(remoteVideoLabelVo -> {
                RemoteKnowledgeVo knowledge = remoteVideoLabelVo.getKnowledge();
                if (knowledge != null && knowledge.getHasNote() == 1) {
                    knowledge.setKnowledgeNote(knowledgeIdNoteMap.get(remoteVideoLabelVo.getKnowledgeId()));
                }
            });
        }
    }
}
