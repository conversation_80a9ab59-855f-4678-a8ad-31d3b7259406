package com.jxw.shufang.extresource.domain.convert;

import com.jxw.shufang.extresource.api.domain.vo.RemoteTeacherVo;
import com.jxw.shufang.extresource.domain.vo.TeacherVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeacherVoConvert extends BaseMapper<TeacherVo, RemoteTeacherVo> {
}
