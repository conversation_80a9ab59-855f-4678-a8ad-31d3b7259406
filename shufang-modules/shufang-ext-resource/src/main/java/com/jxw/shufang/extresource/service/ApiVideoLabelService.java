package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.VideoLabelVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ApiVideoLabelService extends ApiBaseService {

    @Get("/tms/api/video-label/list")
    ApiResp<List<VideoLabelVo>> listVideoLabels(@Query("videoId") Long videoId);
}
