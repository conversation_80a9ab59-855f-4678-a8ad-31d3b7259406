package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.TeacherVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ApiTeacherService extends ApiBaseService {

    @Post("/tms/api/resource/teacher/map")
    ApiResp<Map<Long, TeacherVo>> listTeacherByFileIds(@JSONBody List<Long> fileIds);

}
