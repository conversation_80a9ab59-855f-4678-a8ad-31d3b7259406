package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.PageKnowledgeNotesBo;
import com.jxw.shufang.extresource.domain.vo.KnowledgeNoteVo;
import com.jxw.shufang.extresource.domain.vo.KnowledgeSourceCompleteVo;

import java.util.List;

public interface ApiKnowledgeService extends ApiBaseService {


    @Get("/tms/api/knowledge/note/info")
    ApiResp<KnowledgeNoteVo> getKnowledgeNoteInfo(@Query("knowledgeId") Integer knowledgeId);

    @Post("/tms/api/knowledge/note/page")
    ApiResp<ApiPage<KnowledgeNoteVo>> pageKnowledgeNotes(@JSONBody PageKnowledgeNotesBo pageKnowledgeNotesBo);


    @Get("/tms/api/knowledge/video/v2/page")
    ApiResp<ApiPage<RemoteVideoVo>> queryKnowledgePage(@Query("knowledgeIds")List<Long> knowledgeIdList,
                                                       @Query("pageNo") Integer pageNo,@Query("pageSize") Integer pageSize);
}
