package com.jxw.shufang.extresource.dubbo;


import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxw.shufang.common.core.enums.KnowledgeResourceType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeResourceBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.ExtCourseCatalogBo;
import com.jxw.shufang.extresource.domain.bo.KnowledgeResourceBo;
import com.jxw.shufang.extresource.domain.vo.*;
import com.jxw.shufang.extresource.service.ApiExtCourseService;
import com.jxw.shufang.extresource.service.ApiPlatformService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteExtResourceServiceImpl implements RemoteExtResourceService {

    private final ApiExtCourseService apiExtCourseService;

    private final ApiPlatformService apiPlatformService;


    @Override
    public List<RemoteGroupResourceVo> getKnowledgeResourceList(RemoteKnowledgeResourceBo query) throws ServiceException{
        if ((StringUtils.isBlank(query.getType()) && CollUtil.isEmpty(query.getTypeList())) ||
            (query.getKnowledgeId() == null && CollUtil.isEmpty(query.getKnowledgeIdList()))) {
            return List.of();
        }

        if (query.getType() != null) {
            query.setTypeList(List.of(query.getType()));
        }
        if (query.getKnowledgeId() != null) {
            query.setKnowledgeIdList(List.of(query.getKnowledgeId()));
        }
        //因为接口没有通过typeList的方法提供的查询，所以只能使用遍历加异步的方式来提高速度了
        Map<Long, Map<String, Future<ApiResp<KnowledgeResourceVo>>>> map = new HashMap<>();
        for (Long l : query.getKnowledgeIdList()) {
            Map<String, Future<ApiResp<KnowledgeResourceVo>>> typeMap = new HashMap<>();
            map.put(l, typeMap);
            for (String type : query.getTypeList()) {
//            KnowledgeResourceBo convert = MapstructUtils.convert(query, KnowledgeResourceBo.class);
                KnowledgeResourceBo knowledgeResourceBo = new KnowledgeResourceBo();
                knowledgeResourceBo.setCatalogId(l);
                knowledgeResourceBo.setType(type);
                Future<ApiResp<KnowledgeResourceVo>> apiRespFuture = apiExtCourseService.asyncGetCourseResource(knowledgeResourceBo);
                typeMap.put(type, apiRespFuture);
            }
        }


        //获取结果
        List<RemoteGroupResourceVo> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(map)) {
            for (Long knowledgeID : map.keySet()) {
                Map<String, Future<ApiResp<KnowledgeResourceVo>>> stringFutureMap = map.get(knowledgeID);
                if (CollUtil.isEmpty(stringFutureMap)) {
                    continue;
                }
                for (String type : stringFutureMap.keySet()) {
                    RemoteGroupResourceVo remoteGroupResourceVo = new RemoteGroupResourceVo();
                    remoteGroupResourceVo.setType(KnowledgeResourceType.getByType(type));
                    remoteGroupResourceVo.setKnowledgeId(knowledgeID);
                    try {
                        ApiResp<KnowledgeResourceVo> knowledgeResourceVoApiResp = stringFutureMap.get(type).get();
                        if (knowledgeResourceVoApiResp.isSuccess() && knowledgeResourceVoApiResp.getData() != null) {
                            KnowledgeResourceVo data = knowledgeResourceVoApiResp.getData();
                            remoteGroupResourceVo.setKnowledgeResource(MapstructUtils.convert(data, RemoteKnowledgeResourceVo.class));
                        }
                    } catch (Exception e) {
                        log.error("getKnowledgeResourceList error", e);
                    }
                    result.add(remoteGroupResourceVo);
                }
            }

        }
        return result;
    }

    @Override
    public RemoteGroupResourceVo getKnowledgeResourceById(Long knowledgeId, String type) throws ServiceException{
        RemoteKnowledgeResourceBo remoteKnowledgeResourceBo = new RemoteKnowledgeResourceBo();
        remoteKnowledgeResourceBo.setKnowledgeId(knowledgeId);
        remoteKnowledgeResourceBo.setType(type);
        List<RemoteGroupResourceVo> knowledgeResourceList = getKnowledgeResourceList(remoteKnowledgeResourceBo);
        return CollUtil.isEmpty(knowledgeResourceList) ? null : knowledgeResourceList.get(0);
    }

    @Override
    public List<RemoteExtCourseCatalogVo> getCourseCatalogTree(Long courseId, Long parentId) throws ServiceException {
        ExtCourseCatalogBo bo = new ExtCourseCatalogBo();
        bo.setCourseId(courseId);
        bo.setParentId(parentId);
        bo.setPageNo(1);
        bo.setPageSize(Integer.MAX_VALUE);
        ApiResp<ApiPage<ExtCourseCatalogVo>> catalogRes = apiExtCourseService.getCourseCatalogTree(bo);
        if (!catalogRes.isSuccess()) {
            log.error(catalogRes.toString());
            return List.of();
        }

        ApiPage<ExtCourseCatalogVo> data = catalogRes.getData();

        List<ExtCourseCatalogVo> records = data.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            return MapstructUtils.convert(records, RemoteExtCourseCatalogVo.class);
        }
        return List.of();
    }

    @Override
    public List<RemoteAppInfoVo> androidOtaCheckBySn(String extraIsbn, String sn) {
        ApiResp<Object> appInfoVoApiResp = apiPlatformService.androidOtaCheckBySn(extraIsbn, sn);
        if (appInfoVoApiResp.isSuccess()){
            ObjectMapper mapper = new ObjectMapper();
            try {
                return mapper.readValue(appInfoVoApiResp.getData().toString(), new TypeReference<>() {});
            } catch (JsonProcessingException e) {
                log.error("json转换异常",e);
                throw new ServiceException("json转换异常");
            }
        }
        return List.of();
    }

    @Override
    public RemoteKnowledgeCompleteVo getKnowledgeComplete(List<Long> knowledgeIds) {
        if (CollectionUtils.isEmpty(knowledgeIds)) {
            return new RemoteKnowledgeCompleteVo(new HashMap<>(1));
        }
        ApiResp<KnowledgeSourceCompleteVo> apiRespFuture = apiExtCourseService.getSourceComplete(knowledgeIds);
        if (apiRespFuture.isSuccess() && apiRespFuture.getData() != null) {
            return new RemoteKnowledgeCompleteVo(apiRespFuture.getData().getKnowledgeCompleteMap());
        }
        log.error("getKnowledgeComplete error,knowledgeIds:{}", knowledgeIds);
        return new RemoteKnowledgeCompleteVo(new HashMap<>(1));
    }
}
