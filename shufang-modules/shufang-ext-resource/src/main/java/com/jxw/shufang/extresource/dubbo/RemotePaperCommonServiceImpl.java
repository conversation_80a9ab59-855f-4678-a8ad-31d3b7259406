package com.jxw.shufang.extresource.dubbo;


import com.jxw.shufang.extresource.api.RemotePaperCommonService;
import com.jxw.shufang.extresource.api.domain.bo.*;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.service.ApiPaperCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemotePaperCommonServiceImpl implements RemotePaperCommonService {

    private final ApiPaperCommonService commonService;


    private static final Map<Integer, String> PhaseMAP = new HashMap<>();

    static {
        PhaseMAP.put(1, "小学");
        PhaseMAP.put(2, "初中");
        PhaseMAP.put(3, "高中");
    }


    private static void error(ApiResp<?> apiResp) {
        log.error("ExtApiPaperCommonService remote fail：{}", apiResp.getMsg());
    }


    @Override
    public List<RemoteIdNameVo> getEditionList(AiTestPaperBo bo) {
        return getApiList(commonService.getEditionList(bo));
    }


    @Override
    public String getPhaseName(Integer phaseId) {
        return PhaseMAP.get(phaseId);
    }

    @Override
    public List<RemoteIdNameVo> getGradeList(AiTestPaperBo bo) {
        return getApiList(commonService.getGradeList(bo));
    }

    @Override
    public List<RemoteIdNameVo> getGradeVolumeList(AiTestPaperBo bo) {
        return getApiList(commonService.getGradeVolumeList(bo));
    }

    @Override
    public List<Integer> getPhase(AiTestPaperBo gradeId) {
        return getApiList(commonService.getPhase(gradeId));
    }

    @Override
    public List<RemoteIdNameVo> getSubject(AiTestPaperBo bo) {
        return getApiList(commonService.getSubject(bo));

    }

    @Override
    public List<RemoteExtPaperVo> listPaper(AiTestPaperBo bo) {
        return getApiList(commonService.listPaper(bo));

    }

    @Override
    public List<RemoteAiPaperDetailVo> listPaperByIdList(List<Integer> ids) {
        return getApiList(commonService.listPaperByIdList(ids));

    }

    @Override
    public List<RemotePhaseSubjectVo> getSubjectPhase(RemotePhaseSubjectBo bo) {
        return getApiList(commonService.getSubjectPhase(bo));
    }

    public <T> List<T> getApiList(ApiResp<List<T>> apiResp) {
        if (apiResp.isSuccess()) {
            return apiResp.getData();
        }
        error(apiResp);
        return List.of();
    }
}
