package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PracticeByAnalogyVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 题目id对应知识点所包含的问题数量
     */
    private Integer total;

    /**
     * 反三题目集合
     */
    private List<QuestionVo> questionVoList;
}
