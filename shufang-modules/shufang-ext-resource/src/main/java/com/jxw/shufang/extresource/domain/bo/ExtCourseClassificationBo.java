package com.jxw.shufang.extresource.domain.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
public class ExtCourseClassificationBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学段ID
     */
    private Long phaseId;

    /**
     * 科目ID
     */
    private Long subjectId;

    /**
     * 年级ID
     */
    private Long standardGradeId;

    /**
     * 学期ID
     */
    private Long semesterId;

    /**
     * 教材版本ID
     */
    private Long editionId;

    /**
     * 分页页码（不传默认为1）
     */
    private Integer pageNo;

    /**
     * 分页条数（不传默认为10）
     */
    private Integer pageSize;

    /**
     * 课程名称
     */
    private String courseName;

}
