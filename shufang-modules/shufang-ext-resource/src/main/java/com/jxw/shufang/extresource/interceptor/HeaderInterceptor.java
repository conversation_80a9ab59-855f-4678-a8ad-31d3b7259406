package com.jxw.shufang.extresource.interceptor;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.extresource.annotation.IgnoreToken;
import com.jxw.shufang.extresource.config.SecretProperties;
import com.jxw.shufang.extresource.constants.BasicConst;
import com.jxw.shufang.extresource.service.ILoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HeaderInterceptor<T> implements Interceptor<T> {

    @Autowired
    private SecretProperties secretProperties;

    @Autowired
    private ILoginService loginService;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        IgnoreToken annotation = method.getMethod().getAnnotation(IgnoreToken.class);
        if (annotation != null) {
            request.addInterceptorAttribute(this.getClass(), BasicConst.IGNORE_TOKEN_ATTR_NAME, true);
        }
    }

    @Override
    public boolean beforeExecute(ForestRequest request) throws ServiceException {
        Boolean attribute = getAttribute(request, BasicConst.IGNORE_TOKEN_ATTR_NAME, Boolean.class);
        if (attribute != null && attribute) {
            log.debug("忽略token");
            return true;
        }
        log.debug("添加token");
        String tokenHeaderName = secretProperties.getTokenHeaderName();
        String token = loginService.getToken();
        request.addHeader(tokenHeaderName, token);
        return true;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        Interceptor.super.afterExecute(request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
    }
}
