package com.jxw.shufang.extresource.controller;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.extresource.api.domain.bo.RemoteBatchOssBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteOssVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteUploadConfigVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.UploadConfigBo;
import com.jxw.shufang.extresource.service.ApiCdsOssCommonService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/extOss")
public class ExtOssController {

    private final ApiCdsOssCommonService apiCdsCommonService;

    @Value("${oss.default.type: }")
    private String defaultOssType;


    @GetMapping("/getUploadConfig")
    public R<RemoteUploadConfigVo> getUploadConfig(@RequestParam String bucket,
                                                   @RequestParam String role) {
        UploadConfigBo uploadConfigBo = new UploadConfigBo();
        uploadConfigBo.setRole(role);
        uploadConfigBo.setBucket(bucket);
        uploadConfigBo.setOssType(defaultOssType);
        ApiResp<RemoteUploadConfigVo> uploadConfig = apiCdsCommonService.getUploadConfig(uploadConfigBo);
        if (uploadConfig.isSuccess()) {
            RemoteUploadConfigVo data = uploadConfig.getData();
            if (StringUtils.isNotBlank(defaultOssType)) {
                data.setService(defaultOssType);
                setService(defaultOssType, data);
            } else {
                String ossType = data.getOssType();
                if (StringUtils.isNotBlank(ossType)) {
                    setService(ossType, data);
                }
            }
            return R.ok(data);
        }

        return R.ok();
    }

    @PostMapping("/getUrl")
    public R<List<RemoteOssVo>> getUrl(@RequestBody RemoteBatchOssBo remoteBatchOssBo) {
        ApiResp<List<RemoteOssVo>> ossUrl = apiCdsCommonService.getOssUrl(remoteBatchOssBo);
        if (ossUrl.isSuccess()) {
            return R.ok(ossUrl.getData());
        }
        return R.fail(ossUrl.getMsg());
    }


    private static void setService(String ossType, RemoteUploadConfigVo data) {
        if (StringUtils.isBlank(ossType)) {
            return;
        }
        if (ossType.equals("tencent")) {
            data.setService("qcloud");
        } else {
            data.setService(ossType);
        }
    }
}
