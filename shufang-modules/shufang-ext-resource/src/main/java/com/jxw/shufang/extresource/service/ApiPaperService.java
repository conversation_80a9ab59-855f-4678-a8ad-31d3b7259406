package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.jxw.shufang.extresource.api.domain.vo.RemoteExtBookPaperVo;
import com.jxw.shufang.extresource.domain.ApiResp;

/**
 * 获取试卷
 */
public interface ApiPaperService extends ApiBaseService {


    /**
     *  根据书本id获取对应试卷
     */
    @Get("/tms/api/paper/getBookPaper/{0}")
    ApiResp<RemoteExtBookPaperVo> getPaperList(String bookId);

}
