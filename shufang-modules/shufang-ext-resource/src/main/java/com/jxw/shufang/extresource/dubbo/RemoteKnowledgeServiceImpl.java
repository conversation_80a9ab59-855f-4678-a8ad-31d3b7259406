package com.jxw.shufang.extresource.dubbo;

import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.api.RemoteKnowledgeService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeCompleteVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeNoteVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.PageKnowledgeNotesBo;
import com.jxw.shufang.extresource.domain.vo.KnowledgeNoteVo;
import com.jxw.shufang.extresource.domain.vo.KnowledgeSourceCompleteVo;
import com.jxw.shufang.extresource.service.ApiKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteKnowledgeServiceImpl implements RemoteKnowledgeService {

    private final ApiKnowledgeService apiKnowledgeService;

    public RemoteKnowledgeNoteVo getKnowledgeNoteInfo(Integer knowledgeId) {
        ApiResp<KnowledgeNoteVo> apiResp = apiKnowledgeService.getKnowledgeNoteInfo(knowledgeId);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            return MapstructUtils.convert(apiResp.getData(), RemoteKnowledgeNoteVo.class);
        }
        log.error("getKnowledgeNoteInfo error,knowledgeId:{}", knowledgeId);
        return null;
    }

    public List<RemoteKnowledgeNoteVo> listKnowledgeNotes(List<Integer> knowledgeIds) {
        PageKnowledgeNotesBo pageKnowledgeNotesBo = new PageKnowledgeNotesBo();
        pageKnowledgeNotesBo.setKnowledgeIds(knowledgeIds);
        pageKnowledgeNotesBo.setPageSize(knowledgeIds.size());
        ApiResp<ApiPage<KnowledgeNoteVo>> apiResp = apiKnowledgeService.pageKnowledgeNotes(pageKnowledgeNotesBo);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            List<KnowledgeNoteVo> knowledgeNoteVos = apiResp.getData().getRecords();
            List<RemoteKnowledgeNoteVo> remoteKnowledgeNoteVos = new ArrayList<>();
            knowledgeNoteVos.forEach(knowledgeNoteVo -> {
                RemoteKnowledgeNoteVo remoteKnowledgeNoteVo = MapstructUtils.convert(knowledgeNoteVo, RemoteKnowledgeNoteVo.class);
                remoteKnowledgeNoteVos.add(remoteKnowledgeNoteVo);
            });
            return remoteKnowledgeNoteVos;
        }
        log.error("listKnowledgeNotes error,knowledgeIds:{}", knowledgeIds);
        return List.of();
    }


    public List<RemoteVideoVo> queryKnowledge(List<Long> knowledgeIds) {
        if (CollectionUtils.isEmpty(knowledgeIds)) {
            return Lists.newArrayList();
        }

        ApiResp<ApiPage<RemoteVideoVo>> apiPageApiResp = apiKnowledgeService.queryKnowledgePage(knowledgeIds, 1,1000);
        if (apiPageApiResp.isSuccess() && apiPageApiResp.getData() != null) {
            return apiPageApiResp.getData().getRecords();
        }
        log.error("listKnowledgeVideo error,knowledgeIds:{}", knowledgeIds);
        return List.of();
    }



}
