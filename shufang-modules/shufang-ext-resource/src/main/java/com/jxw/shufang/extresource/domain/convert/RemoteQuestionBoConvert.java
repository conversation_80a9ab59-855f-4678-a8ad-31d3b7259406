package com.jxw.shufang.extresource.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionBo;
import com.jxw.shufang.extresource.domain.bo.KnowledgeQuestionBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteQuestionBoConvert extends BaseMapper<RemoteQuestionBo, KnowledgeQuestionBo> {
}
