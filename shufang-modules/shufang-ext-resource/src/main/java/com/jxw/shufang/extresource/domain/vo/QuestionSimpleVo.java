package com.jxw.shufang.extresource.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QuestionSimpleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 题干
     */
    private String title;

//    /**
//     * 题型。tms_dict.id
//     */
//    @JsonIgnore
//    private Integer typeId;

    /**
     * 题型。
     */
    private IdNameVo type;

    /**
     * 选择题选项,json数组
     */
    private List<String> options;

    /**
     * 答案,json数组(二维)
     */
    private List<List<String>> answer;

    /**
     * 解析
     */
    private String analysis;


//    /**
//     * 聽力音頻ID
//     */
//    @JsonIgnore
//    private Long audioId;

    /**
     * 是否经典例题 1-是 0-否
     */
    private Integer isClassic;

    /**
     * 是否主观题 1-是 0-否
     */
    private Integer isSubjective;

    /**
     * 父ID
     */
    private Long pid;

    /**
     * 难度 1-易 2-较易 3-中等 4-较难 5-难
     */
    private Integer difficulty;

    /**
     * 科目ID
     */
    private Integer subjectId;

    /**
     * 科目
     */
    private IdNameVo subject;

    /**
     * 學段ID
     */
    private Integer phaseId;

    /**
     * 学段ID
     */
    private IdNameVo phase;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 是否有子题 1-是 0-否
     */
    private Integer hasChildren;

    /**
     * 子题
     */
    private List<QuestionDetailVo> children;

    /**
     * 是否有视频 1-是， 0-否
     */
    private Integer hasVideo;

//    /**
//     * 是否已订正
//     */
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer isRevised;

//    /**
//     * 错误原因
//     */
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String wrongReason;

//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private Date updateTime;

//    /**
//     * 是否收藏
//     */
//    public Boolean isCollect;

//    /**
//     * 用户答案
//     * JsonIgnore: 与公版平板6.0安卓代码字段类型不匹配
//     */
//    @JsonIgnore
//    private String userAnswer;

//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Object userAnswerObj;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<IdNameVo> knowledges;

    /**
     * 主题干（大题干）
     */
    private String thematicStem;

}
