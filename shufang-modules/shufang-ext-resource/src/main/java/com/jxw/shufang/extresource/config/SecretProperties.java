package com.jxw.shufang.extresource.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "ext-resource")
public class SecretProperties {
    private String baseUrl;
    private String appId;
    private String appSecret;
    private String tokenHeaderName;

    private String smsUrl;
    private String smsAppId;
    private String smsAppSecret;

    // AI解题地址
    private String aiUrl;
    // AI解题接口
    private String aiAnalysisApi;
}
