package com.jxw.shufang.extresource.controller;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.ExtCourseCatalogBo;
import com.jxw.shufang.extresource.domain.bo.ExtCourseClassificationBo;
import com.jxw.shufang.extresource.domain.bo.KnowledgeVideoBo;
import com.jxw.shufang.extresource.domain.vo.*;
import com.jxw.shufang.extresource.service.ApiExtCourseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 外部课程资源题目
 * 前端访问路由地址为:/extResource/extCourse
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/extCourse")
public class ExtCourseController {

    private final ApiExtCourseService apiExtCourseService;

    /**
     * 学段列表
     */
    @GetMapping("/phase/list")
    public R<List<ExtCoursePhaseVo>> phaseList() {
        ApiResp<List<ExtCoursePhaseVo>> phaseRes = apiExtCourseService.getPhaseList();
        if (phaseRes.isSuccess()) {
            return R.ok(phaseRes.getData());
        }
        return R.fail(phaseRes.getMsg());
    }

    /**
     * 科目列表
     */
    @GetMapping("/subject/list")
    public R<List<ExtCourseSubjectVo>> subjectList() {
        ApiResp<List<ExtCourseSubjectVo>> subjectRes = apiExtCourseService.getSubjectList();
        if (subjectRes.isSuccess()) {
            return R.ok(subjectRes.getData());
        }
        return R.fail(subjectRes.getMsg());
    }

    /**
     * 年级列表
     */
    @GetMapping("/grade/list")
    public R<List<ExtCourseGradeVo>> gradeList() {
        ApiResp<List<ExtCourseGradeVo>> gradeRes = apiExtCourseService.getGradeList();
        if (gradeRes.isSuccess()) {
            return R.ok(gradeRes.getData());
        }
        return R.fail(gradeRes.getMsg());
    }

    /**
     * 学期列表
     */
    @GetMapping("/semester/list")
    public R<List<ExtCourseSemesterVo>> semesterList() {
        ApiResp<List<ExtCourseSemesterVo>> semesterRes = apiExtCourseService.getSemesterList();
        if (semesterRes.isSuccess()) {
            return R.ok(semesterRes.getData());
        }
        return R.fail(semesterRes.getMsg());
    }


    /**
     * 教材版本列表
     */
    @GetMapping("/edition/list")
    public R<List<ExtCourseEditionVo>> editionList() {
        ApiResp<List<ExtCourseEditionVo>> editionRes = apiExtCourseService.getEditionList();
        if (editionRes.isSuccess()) {
            return R.ok(editionRes.getData());
        }
        return R.fail(editionRes.getMsg());
    }

    /**
     * 课程分类列表
     */
    @GetMapping("/classification/page")
    public R<ApiPage<ExtCourseClassificationVo>> classificationPage(ExtCourseClassificationBo bo) {
        ApiResp<ApiPage<ExtCourseClassificationVo>> classificationRes = apiExtCourseService.getClassificationPage(bo);
        if (classificationRes.isSuccess()) {
            return R.ok(classificationRes.getData());
        }
        return R.fail(classificationRes.getMsg());
    }

    /**
     * 培优课程目录列表
     */
    @GetMapping("/catalog/page")
    public R<ApiPage<ExtCourseCatalogVo>> catalogPage(@Validated ExtCourseCatalogBo bo) {
        ApiResp<ApiPage<ExtCourseCatalogVo>> catalogRes = apiExtCourseService.getCourseCatalogPage(bo);
        if (catalogRes.isSuccess()) {
            return R.ok(catalogRes.getData());
        }
        return R.fail(catalogRes.getMsg());
    }


    /**
     * 培优课程目录（树状结构）
     */
    @GetMapping("/catalog/tree")
    public R<ApiPage<ExtCourseCatalogVo>> catalogTree(@Validated ExtCourseCatalogBo bo) {
        ApiResp<ApiPage<ExtCourseCatalogVo>> catalogRes = apiExtCourseService.getCourseCatalogTree(bo);
        if (catalogRes.isSuccess()) {
            return R.ok(catalogRes.getData());
        }
        return R.fail(catalogRes.getMsg());
    }

    /**
     *
     * @param courseVideoBo
     * @return
     */
    @GetMapping("/video/get")
    public R<VideoVo> getCourseVideo(KnowledgeVideoBo courseVideoBo) {
        ApiResp<VideoVo> courseVideoRes = apiExtCourseService.getCourseVideo(courseVideoBo);
        if (courseVideoRes.isSuccess()){
            return R.ok(courseVideoRes.getData());
        }
        return R.fail(courseVideoRes.getMsg());
    }
}
