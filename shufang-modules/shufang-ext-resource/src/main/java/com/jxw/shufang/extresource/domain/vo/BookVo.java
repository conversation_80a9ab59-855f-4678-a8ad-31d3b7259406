package com.jxw.shufang.extresource.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jxw.shufang.extresource.api.domain.vo.RemoteBookVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@AutoMapper(target = RemoteBookVo.class)
public class BookVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer bookId;
    /**
     * 课本编号
     */
    private String bookIsbn;
    /**
     * 课本名称
     */
    private String bookName;

    /**
     * 书本简介
     */
    private String intro;

    @JsonIgnore
    private Integer phaseId;

    @JsonIgnore
    private Integer subjectId;

    @JsonIgnore
    private Integer gradeId;

    @JsonIgnore
    private Integer gradeVolumeId;

    @JsonIgnore
    private Integer publisherId;

    /**
     * 出版社版本id
     */
    @JsonIgnore
    private Integer editionId;

//    private SubjectDTO subject;
//
//    private RemoteGradeVo grade;
//
//    private GradeVolumeDTO gradeVolume;
//
//    private PublisherDTO2 publisher;

    /**
     * 书本封面图ID
     */
    @JsonIgnore
    private Long coverId;

    @JsonIgnore
    private Long verticalCoverId;

    @JsonIgnore
    private Long verticalTextCoverId;

    @JsonIgnore
    private Long horizontalCoverId;

    /**
     * 书本封面图url
     */
    private String coverUrl;

    private String verticalCoverUrl;

    private String verticalTextCoverUrl;

    private String horizontalCoverUrl;

    /**
     * 视频个数 通过flag 判断是知识点/同步视频/单词视频/书法视频个数
     */
    private Integer videoNum;

    /**
     * 知识点视频简介
     */
    @JsonIgnore
    private String knowledgeVideoIntro;

    /**
     * 单词视频简介
     */
    @JsonIgnore
    private String wordVideoIntro;

    /**
     * 书法视频简介
     */
    @JsonIgnore
    private String calligraphyVideoIntro;

    /**
     * 同步视频简介
     */
    @JsonIgnore
    private String syncVideoIntro;

}
