package com.jxw.shufang.extresource.domain.convert;

import com.jxw.shufang.extresource.api.domain.vo.RemoteKnowledgeNoteVo;
import com.jxw.shufang.extresource.domain.vo.KnowledgeNoteVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface KnowledgeNoteVoConvert extends BaseMapper<KnowledgeNoteVo, RemoteKnowledgeNoteVo> {
}
