package com.jxw.shufang.extresource.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCoursePhaseVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
@AutoMapper(target = RemoteExtCoursePhaseVo.class, reverseConvertGenerate = false)
public class ExtCoursePhaseVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学段ID
     */
    private Long id;

    /**
     * 学段名称
     */
    private String phaseName;
}
