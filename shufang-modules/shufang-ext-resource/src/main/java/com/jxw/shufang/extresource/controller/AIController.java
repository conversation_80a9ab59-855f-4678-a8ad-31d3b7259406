package com.jxw.shufang.extresource.controller;


import com.jxw.shufang.extresource.domain.bo.ExtAiAnalysisBo;
import com.jxw.shufang.extresource.service.IAiService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestBody;
/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai")
public class AIController {

    private final IAiService aiService;
    /**
     * 错题AI解析
     */
    @PostMapping("/question/analysis")
    public Object aiAnalysis(@RequestBody ExtAiAnalysisBo extAiAnalysisBo) {

        return aiService.aiAnalysis(extAiAnalysisBo);
    }
}
