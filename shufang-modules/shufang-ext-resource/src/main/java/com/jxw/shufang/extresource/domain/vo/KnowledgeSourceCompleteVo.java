package com.jxw.shufang.extresource.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * @author: cyj
 * @date: 2025/4/11
 */
@Data
@AllArgsConstructor
public class KnowledgeSourceCompleteVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * id -> 是否资源完整
     */
    private Map<Long, Boolean> knowledgeCompleteMap;
}
