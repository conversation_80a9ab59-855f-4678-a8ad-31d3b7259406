package com.jxw.shufang.extresource.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.extresource.api.domain.bo.RemoteKnowledgeVideoBo;
import com.jxw.shufang.extresource.domain.bo.KnowledgeVideoBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteKnowledgeVideoBoConvert extends BaseMapper<RemoteKnowledgeVideoBo, KnowledgeVideoBo> {
}
