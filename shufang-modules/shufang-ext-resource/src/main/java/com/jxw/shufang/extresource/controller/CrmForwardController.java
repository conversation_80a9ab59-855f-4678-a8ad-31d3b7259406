package com.jxw.shufang.extresource.controller;


import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.extresource.constants.BasicConst;
import com.jxw.shufang.extresource.interceptor.HeaderInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 外部课程资源题目
 * 前端访问路由地址为:/ext-resource/crmForward
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/crmForward")
public class CrmForwardController {

    @Value("${ext-resource.crmBaseUrl}")
    private String crmBaseUrl;


    @GetMapping("/get")
    public R<Object> getForward(@RequestParam("uri") String uri, @RequestParam Map<String, Object> params) {
        preCheck();
        //请求接口
        ForestRequest<?> forestRequest = Forest.get(getFullUrl(uri))
            .addQuery(params)
            .addInterceptorAttribute(HeaderInterceptor.class, BasicConst.IGNORE_TOKEN_ATTR_NAME, true); //忽略token
        Object execute = forestRequest.execute();
        return R.ok(execute);
    }



    @PostMapping("/post")
    public R<Object> postForward(@RequestParam("uri") String uri, @RequestBody Map<String, Object> params) {
        preCheck();

        //请求接口
        ForestRequest<?> forestRequest = Forest.post(getFullUrl(uri))
            .contentTypeJson()
            .addBody(params)
            .addInterceptorAttribute(HeaderInterceptor.class, BasicConst.IGNORE_TOKEN_ATTR_NAME, true);

        Object execute = forestRequest.execute();
        return R.ok(execute);
    }


    private void preCheck() {
        if (StringUtils.isBlank(crmBaseUrl)) {
            throw new ServiceException("配置项不完整");
        }
    }

    private String getFullUrl(String uri) {
        if (StringUtils.isBlank(uri)) {
            throw new ServiceException("uri不能为空");
        }
        if (uri.startsWith("/")) {
            uri = uri.substring(1);
        }
        return crmBaseUrl + "/" + uri;
    }


}

