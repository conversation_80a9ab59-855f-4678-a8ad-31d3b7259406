package com.jxw.shufang.extresource.dubbo;


import com.jxw.shufang.extresource.api.RemoteCdsCommonService;
import com.jxw.shufang.extresource.api.domain.bo.ListVideoBO;
import com.jxw.shufang.extresource.api.domain.bo.RemoteOssBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.service.ApiCdsCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteCdsCommonServiceImpl implements RemoteCdsCommonService {

    private final ApiCdsCommonService commonService;

    private static void error(ApiResp<?> apiResp) {
        log.error("RemoteCdsCommonService remote fail：{}", apiResp.getMsg());
    }


    public <T> List<T> getApiList(ApiResp<List<T>> apiResp) {
        if (apiResp.isSuccess()) {
            return apiResp.getData();
        }
        error(apiResp);
        return List.of();
    }

    @Override
    public List<RemoteSubjectVo> listSubjects(List<Integer> ids) {
        return getApiList(commonService.listSubjects(ids));
    }

    @Override
    public Map<Integer, RemoteSubjectVo> getSubjectVoMap(List<Integer> list) {
        List<RemoteSubjectVo> subjectVoList = this.listSubjects(list);
        if (CollectionUtils.isEmpty(subjectVoList)) {
            return Collections.emptyMap();
        }
        return subjectVoList
            .stream()
            .collect(Collectors.toMap(RemoteSubjectVo::getId, Function.identity()));
    }

    @Override
    public List<RemoteGradeVo> getGradeList(List<Integer> ids) {
        return getApiList(commonService.getGradeList(ids));
    }

    @Override
    public List<RemoteGradeVo> getNewGradeList(List<Integer> ids) {
        return getApiList(commonService.getNewGradeList(ids));
    }

    @Override
    public List<RemoteGradeVolumeVo> findGradeVolumeList(List<Integer> ids) {
        return getApiList(commonService.findGradeVolumeList(ids));
    }

    @Override
    public List<RemotePublisherVo> listPublishers(List<Integer> ids) {
        return getApiList(commonService.listPublishers(ids));
    }

    @Override
    public List<RemoteOssVo> getOssUrl(List<RemoteOssBo> list) {
        return getApiList(commonService.getOssUrl(list));
    }

    @Override
    public List<VideoDTO> listVideosV2(ListVideoBO videoRequest) {
        return getApiList(commonService.listVideosV2(videoRequest));
    }
}
