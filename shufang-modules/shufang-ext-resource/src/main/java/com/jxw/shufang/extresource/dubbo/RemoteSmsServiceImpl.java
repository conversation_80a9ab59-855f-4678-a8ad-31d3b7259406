package com.jxw.shufang.extresource.dubbo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.extresource.api.RemoteSmsService;
import com.jxw.shufang.extresource.service.ISmsService;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteSmsServiceImpl implements RemoteSmsService {

    private final ISmsService smsService;



    @Override
    public void sendSmsCaptcha(String phones) throws ServiceException {
        smsService.sendSms(phones);
    }

    @Override
    public void checkSmsCaptcha(String phone, String code) throws ServiceException {
        smsService.checkCode(phone, code);
    }
}
