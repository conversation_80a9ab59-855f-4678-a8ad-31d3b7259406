package com.jxw.shufang.extresource.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCourseGradeVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
@AutoMapper(target = RemoteExtCourseGradeVo.class, reverseConvertGenerate = false)
public class ExtCourseGradeVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学年id
     */
    private Long id;

    /**
     * 学年名称
     */
    private String name;
}
