package com.jxw.shufang.extresource.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCourseSemesterVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
@AutoMapper(target = RemoteExtCourseSemesterVo.class, reverseConvertGenerate = false)
public class ExtCourseSemesterVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学期id
     */
    private Long id;

    /**
     * 学期名称
     */
    private String name;
}
