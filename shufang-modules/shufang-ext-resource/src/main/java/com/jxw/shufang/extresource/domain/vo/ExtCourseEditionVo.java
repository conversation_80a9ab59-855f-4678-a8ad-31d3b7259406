package com.jxw.shufang.extresource.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCourseEditionVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
@AutoMapper(target = RemoteExtCourseEditionVo.class, reverseConvertGenerate = false)
public class ExtCourseEditionVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 教材版本id
     */
    private Long id;

    /**
     * 教材版本名称
     */
    private String name;
}
