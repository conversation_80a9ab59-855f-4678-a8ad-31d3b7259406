package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class VideoLabelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Integer id;

    /**
     * 批注名称
     */
    private String labelName;

    /**
     * 批注颜色
     */
    private String labelColor;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 视频起始位置
     */
    private Integer startPosition;

    /**
     * 视频结束位置
     */
    private Integer endPosition;

    /**
     * 交互方式 0-无交互 1-问答,2-做题
     */
    private Integer type;

    /**
     * 交互方式为1时的提问
     */
    private String question;

    /**
     * 交互方式为2时做题标题,对应cds字典表id
     */
    private Integer subtitleDictId;

    /**
     * 交互方式为2时做题页面展示的标题
     */
    private String subtitle;

    /**
     * 知识点ID
     */
    private Integer knowledgeId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


    /**
     * 知识点
     */
    private KnowledgeVo knowledge;


    /**
     * 题目
     */
    private List<QuestionDetailVo> labelQuestions;
}
