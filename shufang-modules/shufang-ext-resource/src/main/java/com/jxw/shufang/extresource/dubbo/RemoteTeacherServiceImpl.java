package com.jxw.shufang.extresource.dubbo;

import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.api.RemoteTeacherService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteTeacherVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.TeacherVo;
import com.jxw.shufang.extresource.service.ApiTeacherService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteTeacherServiceImpl implements RemoteTeacherService {

    private final ApiTeacherService apiTeacherService;
    @Override
    public Map<Long, RemoteTeacherVo> getFileIdTeacherMap(List<Long> fileIds) {
        if (CollectionUtils.isEmpty(fileIds)){
            return Map.of();
        }
        ApiResp<Map<Long, TeacherVo>> apiResp = apiTeacherService.listTeacherByFileIds(fileIds);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            Map<Long, TeacherVo> fileIdTeacherMap = apiResp.getData();
            Map<Long, RemoteTeacherVo> fileIdRemoteTeacherMap = new HashMap<>();
            fileIdTeacherMap.forEach((k, v) -> {
                RemoteTeacherVo remoteTeacherVo = MapstructUtils.convert(v, RemoteTeacherVo.class);
                fileIdRemoteTeacherMap.put(k, remoteTeacherVo);
            });
            return fileIdRemoteTeacherMap;
        }
        return Map.of();
    }
}
