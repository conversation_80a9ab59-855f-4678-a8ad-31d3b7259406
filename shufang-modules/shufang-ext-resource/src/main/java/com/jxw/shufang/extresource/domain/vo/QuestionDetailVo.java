package com.jxw.shufang.extresource.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QuestionDetailVo extends QuestionSimpleVo {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渲染模板ID (视频批注用，对应表tms_video_label_question的template_id)
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer templateId;
}
