package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
public class KnowledgeResourceVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 文件ID
     */
    private Long id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 展示名称
     */
    private String showName;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 文件路径
     */
    private String path;

    private String pidPath;

    /**
     * 文件链接
     */
    private String url;

    /**
     * 文件大小
     */
    private Long size;

    private Long duration;

    private Long parentId;

    private String thumbUrl;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
