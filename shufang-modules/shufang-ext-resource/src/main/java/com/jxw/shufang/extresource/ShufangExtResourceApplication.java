package com.jxw.shufang.extresource;

import cn.hutool.json.JSONUtil;
import com.jxw.shufang.extresource.domain.ApiPage;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.ExtCourseCatalogBo;
import com.jxw.shufang.extresource.domain.bo.ExtCourseClassificationBo;
import com.jxw.shufang.extresource.domain.vo.*;
import com.jxw.shufang.extresource.service.ApiExtCourseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.List;

/**
 * 外部资源模块
 *
 *
 */
@EnableDubbo
@SpringBootApplication
@Slf4j
public class ShufangExtResourceApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangExtResourceApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        ConfigurableApplicationContext run = application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  外部资源模块启动成功   ლ(´ڡ`ლ)ﾞ  ");

    }
}
