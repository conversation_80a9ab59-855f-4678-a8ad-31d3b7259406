package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.api.domain.bo.RemoteBatchOssBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteOssBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteOssVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteUploadConfigVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.UploadConfigBo;

import java.util.List;

/**
 * 试卷通用接口
 */
public interface ApiCdsOssCommonService extends ApiBaseService {


    @Post("/cds/api/oss/url/getUrl")
    ApiResp<List<RemoteOssVo>> getTencentOssUrl(@JSONBody List<RemoteOssBo> list);

    @Post("/cds/api/oss/url/get")
    ApiResp<List<RemoteOssVo>> getOssUrl(@JSONBody RemoteBatchOssBo request);

    @Get("/cds/api/oss/file/upload-config")
    ApiResp<RemoteUploadConfigVo> getUploadConfig(@Query UploadConfigBo uploadConfigBo);
}
