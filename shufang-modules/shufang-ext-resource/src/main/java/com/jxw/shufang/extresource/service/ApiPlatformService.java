package com.jxw.shufang.extresource.service;


import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.AppInfoVo;


@BaseRequest(baseURL = "#{ext-resource.platformBaseUrl}")     // 默认域名
public interface ApiPlatformService {


    @Get("/api/jxwota/apk/checkBySn")
    ApiResp<Object> androidOtaCheckBySn(@Query("apkIsbn") String extraIsbn, @Query("series") String sn);
}
