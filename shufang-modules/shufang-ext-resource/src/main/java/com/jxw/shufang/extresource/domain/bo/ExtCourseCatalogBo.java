package com.jxw.shufang.extresource.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
public class ExtCourseCatalogBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    /**
     * 父级目录ID（初始为0）
     */
    @NotNull(message = "父级目录ID不能为空")
    private Long parentId;

    /**
     * 分页页码（不传默认为1）
     */
    private Integer pageNo;

    /**
     * 分页条数（不传默认为10）
     */
    private Integer pageSize;

}
