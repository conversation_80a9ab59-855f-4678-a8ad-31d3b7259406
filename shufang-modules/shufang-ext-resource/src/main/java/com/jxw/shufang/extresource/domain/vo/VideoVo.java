package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 培优视频
 */
@NoArgsConstructor
@Data
public class VideoVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *文件ID
     */
    private Long id;

    /**
     *视频唯一标识
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 视频展示名称
     */
    private String showName;

    /**
     * 视频缩略图
     */
    private String thumbUrl;

    /**
     * 视频url
     */
    private String playUrl;

    /**
     * 多分辨率列表
     */
    private List<PlayResolutionsVo> playResolutions;

    /**
     *
     */
    private Object downloadUrl;

    /**
     *
     */
    //private List<?> downloadResolutions;

    /**
     * 文件大小（字节）
     */
    private Integer size;

    /**
     * 视频时长（秒）
     */
    private Long duration;

}
