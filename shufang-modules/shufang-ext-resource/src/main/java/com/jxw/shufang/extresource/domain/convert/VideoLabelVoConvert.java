package com.jxw.shufang.extresource.domain.convert;

import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoLabelVo;
import com.jxw.shufang.extresource.domain.vo.VideoLabelVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VideoLabelVoConvert extends BaseMapper<VideoLabelVo, RemoteVideoLabelVo> {
}
