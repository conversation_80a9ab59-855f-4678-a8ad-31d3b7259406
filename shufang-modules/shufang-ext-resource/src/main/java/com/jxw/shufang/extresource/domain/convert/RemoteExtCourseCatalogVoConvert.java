package com.jxw.shufang.extresource.domain.convert;

import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCourseCatalogVo;
import com.jxw.shufang.extresource.domain.bo.QuestionVideoBo;
import com.jxw.shufang.extresource.domain.vo.ExtCourseCatalogVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteExtCourseCatalogVoConvert extends BaseMapper<ExtCourseCatalogVo ,RemoteExtCourseCatalogVo> {
}
