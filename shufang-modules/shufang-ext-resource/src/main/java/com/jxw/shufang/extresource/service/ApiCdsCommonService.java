package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.api.domain.bo.RemoteOssBo;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.api.domain.bo.ListVideoBO;
import com.jxw.shufang.extresource.api.domain.vo.VideoDTO;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 试卷通用接口
 */
public interface ApiCdsCommonService extends ApiBaseService {

    @Get("/cds/api/subject/list")
    ApiResp<List<RemoteSubjectVo>> listSubjects(@Query("ids") List<Integer> ids);


    /**
     * 获取年级
     */
    @Get("/cds/api/grade/detail/list")
    ApiResp<List<RemoteGradeVo>> getGradeList(@Query("ids") List<Integer> ids);


    @Get("/cds/api/grade/new/list")
    ApiResp<List<RemoteGradeVo>> getNewGradeList(@Query("ids") List<Integer> ids);


    /**
     * 获取学册
     */
    @Get("/cds/api/grade/volume/list")
    ApiResp<List<RemoteGradeVolumeVo>> findGradeVolumeList(@Query("ids") List<Integer> ids);


    /**
     * 获取细分出版社
     */
    @Get("/cds/api/publisher/v2/list")
    ApiResp<List<RemotePublisherVo>> listPublishers(@Query("ids") List<Integer> ids);

    @Post("/cds/api/oss/url/getUrl")
    ApiResp<List<RemoteOssVo>> getOssUrl(@JSONBody List<RemoteOssBo> list);

    @GetMapping("/cds/api/oss/file/upload-config")
    ApiResp<RemoteUploadConfigVo> getUploadConfig(@Query("ossType") String ossType,
                                                      @Query("bucket") String bucket,
                                                      @Query("role") String role);

    @Post("/cds/api/video/v2/list")
    ApiResp<List<VideoDTO>> listVideosV2(@JSONBody ListVideoBO videoRequest);
}
