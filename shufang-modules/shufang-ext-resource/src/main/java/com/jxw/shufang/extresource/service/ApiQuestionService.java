package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.api.domain.bo.RemotePaperQuestionBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionSimpleVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.KnowledgeQuestionBo;
import com.jxw.shufang.extresource.domain.bo.QuestionVideoBo;
import com.jxw.shufang.extresource.domain.vo.PracticeByAnalogyVo;
import com.jxw.shufang.extresource.domain.vo.QuestionVo;
import com.jxw.shufang.extresource.domain.vo.VideoVo;

import java.util.List;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public interface ApiQuestionService extends ApiBaseService {


    /**
     * 题目对应讲题视频
     */
    @Get("/tms/api/question/video/list")
    ApiResp<List<VideoVo>> getQuestionVideoList(@Query QuestionVideoBo questionVideoBo);


    /**
     * 错题本-获取题目信息
     */
    @Post("/tms/api/question/findQuestions")
    ApiResp<List<QuestionVo>> getQuestionList(@JSONBody List<Long> questionIds);

    /**
     * 错题本-获取题目信息
     */
    @Post("/tms/api/questionKnowledge/findQuestionsWithKnowledge")
    ApiResp<List<QuestionVo>> getQuestionListV2(@JSONBody List<Long> questionIds);


    @Get("/tms/api/paper/question/list")
    ApiResp<List<RemoteQuestionSimpleVo>> getPaperQuestions(@Query RemotePaperQuestionBo paperQuestionBo);

    /**
     * 题目信息
     */
    @Get("/tms/api/excellent-course/paper/questions/list")
    ApiResp<List<QuestionVo>> getKnowledgeQuestionList(@Query KnowledgeQuestionBo knowledgeQuestionBo);

    @Get(value = "/tms/api/question/video/list", async = true)
    Future<ApiResp<List<VideoVo>>> asyncGetQuestionVideoList(@Query QuestionVideoBo convert);

    /**
     * 获取错题举一反三的题目集合
     */
    @Get(value = "/tms/api/inner/knowledge-question/practiceByAnalogy")
    ApiResp<PracticeByAnalogyVo> getQuestionListByAnalogy(@Query("questionId") Long questionId);
}
