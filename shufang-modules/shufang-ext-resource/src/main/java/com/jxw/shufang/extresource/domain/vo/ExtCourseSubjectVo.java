package com.jxw.shufang.extresource.domain.vo;

import com.jxw.shufang.extresource.api.domain.vo.RemoteExtCourseSubjectVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@Data
@AutoMapper(target = RemoteExtCourseSubjectVo.class, reverseConvertGenerate = false)
public class ExtCourseSubjectVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 科目ID
     */
    private Long id;

    /**
     * 科目名称
     */
    private String name;
}
