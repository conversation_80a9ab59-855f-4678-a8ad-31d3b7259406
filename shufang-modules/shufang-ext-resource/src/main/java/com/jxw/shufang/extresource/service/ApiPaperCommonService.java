package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.api.domain.bo.*;
import com.jxw.shufang.extresource.api.domain.vo.*;
import com.jxw.shufang.extresource.domain.ApiResp;

import java.util.List;

/**
 * 试卷通用接口
 */
public interface ApiPaperCommonService extends ApiBaseService {


    /**
     * 获取出版社细分版本
     */
    @Get("/tms/api/paper/ai-paper/getEdition")
    ApiResp<List<RemoteIdNameVo>> getEditionList(@Query AiTestPaperBo publisherBo);


    /**
     * 获取年级
     */
    @Get("/tms/api/paper/ai-paper/getGrade")
    ApiResp<List<RemoteIdNameVo>> getGradeList(@Query AiTestPaperBo remoteGradeBo);


    /**
     * 获取学册
     */
    @Get("/tms/api/paper/ai-paper/getGradeVolume")
    ApiResp<List<RemoteIdNameVo>> getGradeVolumeList(@Query AiTestPaperBo gradeVolumeBo);


    /**
     * 根据年级Id获取学段
     */
    @Get("/tms/api/paper/ai-paper/getPhase")
    ApiResp<List<Integer>> getPhase(@Query AiTestPaperBo gradeId);

    @Get("/tms/api/paper/ai-paper/getSubject")
    ApiResp<List<RemoteIdNameVo>> getSubject(@Query AiTestPaperBo gradeId);


    @Get("/tms/api/paper/ai-paper/list")
    ApiResp<List<RemoteExtPaperVo>> listPaper(@Query AiTestPaperBo gradeId);


    @Get("/tms/api/paper/ai-paper/listByPaperIdList")
    ApiResp<List<RemoteAiPaperDetailVo>> listPaperByIdList(@Query("ids") List<Integer> ids);

    /**
     * 根据学段获取对应的科目
     */
    @Get("/cds/api/phase-subject/list")
    ApiResp<List<RemotePhaseSubjectVo>> getSubjectPhase(@Query RemotePhaseSubjectBo phaseSubjectBo);

}
