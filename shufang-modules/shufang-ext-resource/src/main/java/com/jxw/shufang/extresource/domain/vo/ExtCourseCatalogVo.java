package com.jxw.shufang.extresource.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class ExtCourseCatalogVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 目录ID
     */
    private Long id;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 父级目录ID
     */
    private Long parentId;

    /**
     * 是否是叶子节点（0:否；1:是）为叶子节点才可添加
     */
    private Integer leaf;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 所属课程ID
     */
    private Integer courseId;

    /**
     * 资源是否完整
     */
    private boolean completeSource;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;


    /**
     * 子节点列表
     */
    private List<ExtCourseCatalogVo> children;

}
