package com.jxw.shufang.extresource.domain.bo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class KnowledgeResourceBo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 培优目录叶子节点ID
     */
    private Long catalogId;

    /**
     * 讲义：handout，测验试卷：test，测验试卷带解析：test_analysis，练习试卷：practice，练习试卷带解析：practice_analysis
     */
    private String type;
}
