package com.jxw.shufang.extresource.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.utils.StringUtils;
import com.dtflys.forest.utils.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.extresource.config.SecretProperties;
import com.jxw.shufang.extresource.constants.BasicConst;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.vo.TokenVo;
import com.jxw.shufang.extresource.interceptor.HeaderInterceptor;
import com.jxw.shufang.extresource.service.ILoginService;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.SortedMap;
import java.util.TreeMap;

@RequiredArgsConstructor
@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {

    private final SecretProperties secretProperties;


    private final LockTemplate lockTemplate;

    //@BindingVar("token")
    @Override
    public String getToken() {
        String token = RedisUtils.getCacheObject(BasicConst.TOKEN_REDIS_KEY);
        //双重检查锁
        if (token == null) {
           // 多服务，得用分布式锁
            final LockInfo lockInfo = lockTemplate.lock(BasicConst.TOKEN_REDIS_LOCK_KEY, 30000L, 5000L, RedissonLockExecutor.class);
            if (null == lockInfo) {
                throw new ServiceException("业务处理中,请稍后再试");
            }
            // 获取锁成功，处理业务
            try {
                token = RedisUtils.getCacheObject(BasicConst.TOKEN_REDIS_KEY);
                if (token != null) {
                    return token;
                }
                ApiResp<TokenVo> tokenVoApiResp = requestExtResourceToken();
                if (tokenVoApiResp.isSuccess()) {
                    TokenVo tokenVo = tokenVoApiResp.getData();
                    token = tokenVo.getToken();
                    Long expire = tokenVo.getExpire();
                    if (StringUtils.isBlank(token)||expire==null) {
                        log.error("获取token失败,token或expire为空{}", JSONUtil.toJsonStr(tokenVoApiResp));
                        throw new ServiceException("获取extResourceToken失败,状态码：" + tokenVoApiResp.getCode() + ",错误信息：" + tokenVoApiResp.getMsg());
                    }
                    //数据传输的过程中已经失去了一部分精度，所以这里减去60秒，做一定的缓冲
                    expire = expire - 60000;
                    RedisUtils.setCacheObject(BasicConst.TOKEN_REDIS_KEY, token, Duration.ofMillis(expire));
                }else {
                    log.error("获取token失败,{}", JSONUtil.toJsonStr(tokenVoApiResp));
                    throw new ServiceException("获取extResourceToken失败,状态码：" + tokenVoApiResp.getCode() + ",错误信息：" + tokenVoApiResp.getMsg());
                }

            } finally {
                //释放锁
                lockTemplate.releaseLock(lockInfo);
            }
        }
        return token;
    }

    private ApiResp<TokenVo> requestExtResourceToken() {
        String baseUrl = secretProperties.getBaseUrl();
        String appId = secretProperties.getAppId();
        String appSecret = secretProperties.getAppSecret();
        long timestamp = System.currentTimeMillis();
        String nonce = RandomUtil.randomString(RandomUtil.randomInt(8, 16));

        //生成签名
        SortedMap<String, String> items = new TreeMap<>();
        items.put("appId", appId);
        items.put("timestamp", timestamp+"");
        //随机串，字符串，长度范围：8~16
        items.put("nonce", nonce);
        items.put("appsecret", appSecret);
        String genSignature = signature(items);

        //请求参数设置
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("appId", appId);
        paramMap.put("timestamp", timestamp+"");
        paramMap.put("nonce", nonce);
        paramMap.put("signature", genSignature);

        //请求接口
        ForestRequest<?> forestRequest = Forest.get(baseUrl+"/auth2/api/oauth/token")
            .addQuery(paramMap)
            .addInterceptorAttribute(HeaderInterceptor.class,BasicConst.IGNORE_TOKEN_ATTR_NAME,true);
        ApiResp<TokenVo> execute = forestRequest.execute(new TypeReference<>() {
        });
        log.debug("请求token返回结果：{}",execute);
        return execute;
    }

    public static String signature(SortedMap<String,String> items){
        StringBuilder forSign= new StringBuilder();

        for(String key:items.keySet()){
            forSign.append(key).append("=").append(items.get(key)).append("&");
        }

        forSign.setLength(forSign.length()-1);

        return encryptSHA1(forSign.toString());
    }

    public static String encryptSHA1(String content){
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(content.getBytes(StandardCharsets.UTF_8));
            byte[] messageDigest = digest.digest();
            StringBuilder hexString = new StringBuilder();

            for (byte b : messageDigest) {
                String shaHex = Integer.toHexString(b & 0xFF);

                if (shaHex.length() < 2) {
                    hexString.append(0);
                }

                hexString.append(shaHex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }
}
