package com.jxw.shufang.extresource.service;

import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Query;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.api.domain.bo.QueryTestPaperFileRequest;
import com.jxw.shufang.extresource.api.domain.vo.TestPaperFileVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/20 16:52
 * @Version 1
 * @Description
 */
public interface ApiTmsCommonService extends ApiBaseService{
    @Get("/tms/api/inner/paper-file/getPaperFileById")
    ApiResp<List<TestPaperFileVO>> queryTestPaperFileByPaperId(@Query QueryTestPaperFileRequest request);
}
