package com.jxw.shufang.extresource.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionTypeVo;
import com.jxw.shufang.extresource.domain.vo.QuestionTypeVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface QuestionTypeVoConvert extends BaseMapper<QuestionTypeVo, RemoteQuestionTypeVo> {
}
