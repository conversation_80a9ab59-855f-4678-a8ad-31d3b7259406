package com.jxw.shufang.extresource.dubbo;


import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.extresource.api.domain.bo.RemotePaperQuestionBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionSimpleVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.api.RemoteQuestionService;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionBo;
import com.jxw.shufang.extresource.api.domain.bo.RemoteQuestionVideoBo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteGroupVideoVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteQuestionVo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteVideoVo;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.KnowledgeQuestionBo;
import com.jxw.shufang.extresource.domain.bo.QuestionVideoBo;
import com.jxw.shufang.extresource.domain.vo.QuestionVo;
import com.jxw.shufang.extresource.domain.vo.VideoVo;
import com.jxw.shufang.extresource.service.ApiQuestionService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteQuestionServiceImpl implements RemoteQuestionService {

    private final ApiQuestionService apiQuestionService;

    @Override
    public List<RemoteQuestionVo> listQuestions(List<Long> questionIds) {
        ApiResp<List<QuestionVo>> apiResp = apiQuestionService.getQuestionList(questionIds);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            return MapstructUtils.convert(apiResp.getData(), RemoteQuestionVo.class);
        }
        return List.of();
    }

    @Override
    public List<RemoteQuestionVo> listQuestionsWithKnowledge(List<Long> questionIds) {
        ApiResp<List<QuestionVo>> apiResp = apiQuestionService.getQuestionListV2(questionIds);
        if (apiResp.isSuccess() && apiResp.getData() != null) {
            return MapstructUtils.convert(apiResp.getData(), RemoteQuestionVo.class);
        }
        return List.of();
    }

    @Override
    public List<RemoteQuestionVo> getKnowledgeQuestionList(RemoteQuestionBo remoteQuestionBo) {

//        KnowledgeQuestionBo convert = MapstructUtils.convert(remoteQuestionBo, KnowledgeQuestionBo.class);
        KnowledgeQuestionBo convert = new KnowledgeQuestionBo();
        convert.setCatalogId(remoteQuestionBo.getKnowledgeId());
        convert.setType(remoteQuestionBo.getType());
        ApiResp<List<QuestionVo>> knowledgeQuestionList = apiQuestionService.getKnowledgeQuestionList(convert);
        if (knowledgeQuestionList.isSuccess() && knowledgeQuestionList.getData() != null) {
            return MapstructUtils.convert(knowledgeQuestionList.getData(), RemoteQuestionVo.class);
        }
        return List.of();
    }

    @Override
    public List<RemoteQuestionSimpleVo> getPaperQuestions(RemotePaperQuestionBo remotePaperQuestionBo) {

        ApiResp<List<RemoteQuestionSimpleVo>> apiResp =
            apiQuestionService.getPaperQuestions(remotePaperQuestionBo);
        if (apiResp.isSuccess()) {
            return apiResp.getData();
        }

        return List.of();
    }

    @Override
    public List<RemoteGroupVideoVo> getQuestionVideoList(RemoteQuestionVideoBo remoteQuestionVideoBo) {
        if (CollUtil.isEmpty(remoteQuestionVideoBo.getQuestionIdList()) && remoteQuestionVideoBo.getQuestionId() == null) {
            log.error("getQuestionVideoList error, questionIdList is empty or questionId is null");
            return List.of();
        }

        if (remoteQuestionVideoBo.getQuestionId() != null) {
            remoteQuestionVideoBo.setQuestionIdList(CollUtil.newArrayList(remoteQuestionVideoBo.getQuestionId()));
        }
        Map<Long, List<VideoVo>> resMap = new HashMap<>();
        //因为接口没有通过idList的方法提供的查询，所以只能使用遍历加异步的方式来提高速度了
        Map<Long, Future<ApiResp<List<VideoVo>>>> map = new HashMap<>();
        for (Long id : remoteQuestionVideoBo.getQuestionIdList()) {
            QuestionVideoBo convert = MapstructUtils.convert(remoteQuestionVideoBo, QuestionVideoBo.class);
            convert.setQuestionId(id);
            Future<ApiResp<List<VideoVo>>> apiRespFuture = apiQuestionService.asyncGetQuestionVideoList(convert);
            map.put(id, apiRespFuture);
        }

        //获取结果
        List<RemoteGroupVideoVo> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(map)) {
            for (Map.Entry<Long, Future<ApiResp<List<VideoVo>>>> entry : map.entrySet()) {
                try {
                    ApiResp<List<VideoVo>> listApiResp = entry.getValue().get();
                    if (listApiResp.isSuccess()) {
                        List<VideoVo> data = listApiResp.getData();
                        if (CollUtil.isNotEmpty(data)) {
                            resMap.put(entry.getKey(), data);
                        }
                    }
                } catch (Exception e) {
                    log.error("getKnowledgeVideoList error", e);
                }

            }
        }

        if (CollUtil.isNotEmpty(resMap)) {
            for (Map.Entry<Long, List<VideoVo>> entry : resMap.entrySet()) {
                List<VideoVo> data = entry.getValue();
                if (CollUtil.isNotEmpty(data)) {
                    RemoteGroupVideoVo vo = new RemoteGroupVideoVo();
                    vo.setQuestionId(entry.getKey());
                    vo.setVideoVoList(MapstructUtils.convert(data, RemoteVideoVo.class));
                    result.add(vo);
                }
            }
            return result;
        }
        return List.of();
    }


}
