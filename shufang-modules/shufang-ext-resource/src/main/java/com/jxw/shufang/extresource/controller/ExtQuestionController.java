package com.jxw.shufang.extresource.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.extresource.domain.vo.PracticeByAnalogyVo;
import com.jxw.shufang.extresource.domain.vo.QuestionSimpleVo;
import com.jxw.shufang.extresource.domain.vo.QuestionVo;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.extresource.domain.ApiResp;
import com.jxw.shufang.extresource.domain.bo.QuestionVideoBo;
import com.jxw.shufang.extresource.domain.vo.VideoVo;
import com.jxw.shufang.extresource.service.ApiQuestionService;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


/**
 * 外部资源题目
 * 前端访问路由地址为:/extResource/extQuestion
 *
 * @date 2024-05-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/extQuestion")
public class ExtQuestionController extends BaseController {

    private final ApiQuestionService apiQuestionService;

    /**
     * 查询题目对应的视频信息
     */
    @GetMapping("/getQuestionVideo")
    public R<VideoVo> queryQuestionRecord(@NotNull(message = "题目Id不能为空") Long questionId) {
        QuestionVo videoQuestion = getVideoQuestion(questionId);
        if (ObjectUtils.isEmpty(videoQuestion)) {
            return R.ok();
        }
        QuestionVideoBo questionVideoBo = new QuestionVideoBo();
        questionVideoBo.setQuestionId(videoQuestion.getId());
        ApiResp<List<VideoVo>> questionVideoList = apiQuestionService.getQuestionVideoList(questionVideoBo);
        if (questionVideoList.isSuccess()) {
            List<VideoVo> data = questionVideoList.getData();
            return R.ok(CollUtil.isEmpty(data) ? null : data.get(0));
        } else {
            return R.fail(questionVideoList.getMsg());
        }

    }

    /**
     * 根据错题Id举一反三
     *
     * @param questionId 错题ID
     * @return 返回三道题目详情
     */
    @GetMapping("/getQuestionListByAnalogy")
    public R<PracticeByAnalogyVo> getQuestionListByAnalogy(@NotNull(message = "题目Id不能为空") Long questionId) {
        ApiResp<PracticeByAnalogyVo> questionListByAnalogyResp = apiQuestionService.getQuestionListByAnalogy(questionId);
        if (questionListByAnalogyResp.isSuccess()) {
           return R.ok(questionListByAnalogyResp.getData());
        } else {
            return R.fail(questionListByAnalogyResp.getMsg());
        }
    }


    private QuestionVo getVideoQuestion(Long questionId) {
        ApiResp<List<QuestionVo>> questionResp = apiQuestionService.getQuestionList(List.of(questionId));
        if (!questionResp.isSuccess()) {
            return null;
        }
        List<QuestionVo> questionVoList = questionResp.getData();
        if (CollUtil.isEmpty(questionVoList)) {
            return null;
        }
        QuestionVo questionVo = questionVoList.get(0);
        Long pid = questionVo.getPid();
        if (ObjectUtils.isEmpty(pid) || pid == 0) {
            return questionVo;
        } else {
            return getVideoQuestion(pid);
        }
    }

}
