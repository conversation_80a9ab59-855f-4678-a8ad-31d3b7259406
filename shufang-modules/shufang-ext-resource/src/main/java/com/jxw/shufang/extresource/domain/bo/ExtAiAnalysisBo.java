package com.jxw.shufang.extresource.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ExtAiAnalysisBo  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 题目图片的URL
     */
    private String problemUrl;

    /**
     * 答案图片url
     */
    private String answerUrl;

    /**
     * 大于0则流式输出，反之非流式输出，默认为流式输出
     */
    private Integer stream;

    /**
     * 选择模型 1：豆包+无推理； 2：豆包+推理
     */
    private String selectModel;

    private String answerContent;
}
