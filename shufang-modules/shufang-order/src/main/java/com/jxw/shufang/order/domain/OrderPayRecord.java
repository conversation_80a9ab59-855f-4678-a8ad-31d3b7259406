package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单支付记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 16:07:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_pay_record")
public class OrderPayRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 订单支付号
     */
    private String orderPayNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付单应付金额
     */
    private BigDecimal payAbleAmount;

    /**
     * 定金标识：0-否，1-是
     */
    private Integer depositAmountFlag;

    /**
     * 支付状态：0-待支付,2-支付成功,3-支付失败,4-已退款,5-已关闭
     */
    private Integer paymentStatus;

    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 付款备注
     */
    private String paymentDesc;

    /**
     * 第三方支付交易号
     */
    private String transactionNo;

    /**
     * 渠道订单号
     */
    private String channelOrderId;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 订单关闭原因
     */
    private String orderCloseReason;

    /**
     * 支付凭证文件ID
     */
    private String paymentVoucherImgId;

    /**
     * 是否异常支付 0：否，1-是
     */
    private Integer abnormalPayFlag;

    /**
     * 异常支付原因
     */
    private String abnormalPayDesc;

    /**
     * 支付阶段
     */
    private String paymentStage;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除, 1-已删除
     */
    private Integer delFlag;
}
