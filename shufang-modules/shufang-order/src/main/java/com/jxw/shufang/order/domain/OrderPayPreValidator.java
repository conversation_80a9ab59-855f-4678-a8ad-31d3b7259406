package com.jxw.shufang.order.domain;

import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.enums.PayOrderStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/8 10:17
 * @Version 1
 * @Description 订单支付前验证逻辑
 */
@Data
public class OrderPayPreValidator {
    private Order order;
    private OrderPayRecord orderPayRecord;
    private OrderOperate orderOperate;

    private OrderPayPreValidator() {
    }

    public static OrderPayPreValidator of(Order order, OrderPayRecord orderPayRecord, OrderOperate orderOperate) {
        OrderPayPreValidator orderPayPreValidator = new OrderPayPreValidator();
        orderPayPreValidator.setOrder(order);
        orderPayPreValidator.setOrderPayRecord(orderPayRecord);
        orderPayPreValidator.setOrderOperate(orderOperate);
        return orderPayPreValidator;
    }

    public void validate() {
        if (null == order) {
            throw new ServiceException("订单不存在");
        }
        Boolean onlinePayFlag = order.getOnlinePayFlag();
        if (!onlinePayFlag) {
            throw new ServiceException("订单不支持在线支付");
        }
        String orderOperateStatus = orderOperate.getOrderOperateStatus();
        if (PayOrderStatusEnum.PAYED.getCode().equals(orderPayRecord.getPaymentStatus())) {
            throw new ServiceException("订单已支付,请勿重复支付");
        }
        if (!OrderStatusEnum.WAIT_PAY.getCode().equals(orderOperateStatus)
            && !OrderStatusEnum.PENDING_PAY.getCode().equals(orderOperateStatus)) {
            throw new ServiceException("订单状态异常");
        }
        if (!PayOrderStatusEnum.WAIT_PAY.getCode().equals(orderPayRecord.getPaymentStatus())) {
            throw new ServiceException("支付订单状态异常");
        }
        if (null != order.getInstallmentPayDeadlineTime() && new Date().after(order.getInstallmentPayDeadlineTime())) {
            throw new ServiceException("当前订单已过支付截止时间,请走退款");
        }
    }
}
