package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.StudentProductTemplateAuth;
import com.jxw.shufang.order.domain.bo.ProductTemplateBo;
import com.jxw.shufang.order.domain.bo.StudentProductTemplateAuthBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateVo;
import com.jxw.shufang.order.domain.vo.StudentBaseTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductResTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductTemplateAuthVo;
import com.jxw.shufang.order.mapper.StudentProductTemplateAuthMapper;
import com.jxw.shufang.order.service.IProductTemplateService;
import com.jxw.shufang.order.service.IStudentProductTemplateAuthService;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.RemoteStudentCourseService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品（会员卡模板）授权Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class StudentProductTemplateAuthServiceImpl implements IStudentProductTemplateAuthService, BaseService {

    private final StudentProductTemplateAuthMapper studentProductTemplateAuthMapper;

    /**
     * 课程模板Service业务对象
     */
    private final IProductTemplateService productTemplateService;

    @DubboReference
    private final RemoteStudentCourseService remoteStudentCourseService;

    @DubboReference
    private RemoteDeptService remoteDeptService;

    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    /**
     * 查询产品（会员卡模板）授权
     */
    @Override
    public StudentProductTemplateAuthVo queryById(Long templateDeptAuthId) {
        return studentProductTemplateAuthMapper.selectVoById(templateDeptAuthId);
    }

    /**
     * 查询产品（会员卡模板）授权列表
     * 自已可用的模板
     */
    @Override
    public List<StudentProductResTemplateAuthVo> queryList() {
        Long deptId = LoginHelper.getDeptId();
        RemoteDeptAccountVo remoteDeptAccountVo = remoteDeptService.selectDeptById(deptId);
        Boolean isStore = remoteDeptAccountVo.getIsStore();
        if (isStore) {
            QueryWrapper<StudentProductTemplateAuth> lqw = buildQuery(remoteDeptAccountVo.getParentId(), 0);
            return studentProductTemplateAuthMapper.queryAuthList(lqw);
        } else {
            QueryWrapper<StudentProductTemplateAuth> lqw = buildQuery(LoginHelper.getDeptId(), 0);
            return studentProductTemplateAuthMapper.queryAuthList(lqw);
        }
    }

    /**
     * 可以用来给别人授权的模板
     *
     * @param bo
     * @return
     */
    @Override
    public List<? extends StudentBaseTemplateAuthVo> queryAuthList(StudentProductTemplateAuthBo bo) {
        log.info("【查询授权模板】请求参数：{} 代理商ID：{}", bo, LoginHelper.getDeptId());
        if (bo.getDeptId() == null && ObjectUtil.isEmpty(bo.getDeptIds())) {
            throw new ServiceException("组织ID参数不能为空");
        }

        //查询对应的模板授权列表
        List<? extends StudentBaseTemplateAuthVo> tempAuthVos = queryProductAuthList(bo);
        return tempAuthVos;
    }

    /**
     * 获取模板授权列表
     * @return
     */
    protected List<ProductTemplateVo> queryProductAuthList(StudentProductTemplateAuthBo bo) {
        //查询所有上架的 课程模板 列表
        ProductTemplateBo productTemplateBo = new ProductTemplateBo();
        productTemplateBo.setStatus(0);//上架状态
        if (ObjectUtil.isNotNull(bo.getTemplateType())) { //模板授权类型
            productTemplateBo.setProductTemplateType(bo.getTemplateType());
        }
        List<ProductTemplateVo> productTemplateVos = productTemplateService.queryList(productTemplateBo);
        log.info("【查询授权模板】查询模板类型：{}，相关模板列表：{}", bo.getTemplateType(), productTemplateVos);

        //如果没有上架的课程模板列表，则直接返回空集合
        if (ObjectUtil.isEmpty(productTemplateVos)) return ListUtil.toList();

        //兼容单个代理商查询 和 多个代理商查询
        List<Long> deptIds = ObjectUtil.isNotNull(bo.getDeptId()) ? ListUtil.toList(bo.getDeptId()) : bo.getDeptIds();
        if (ObjectUtil.isEmpty(deptIds)) return ListUtil.toList();

        //获取上一级代理商的ID
        List<Long> parentIds = Optional.ofNullable(remoteDeptService.selectDeptByIds(deptIds)).orElse(ListUtil.toList())
            .stream().map(RemoteDeptAccountVo::getParentId).toList();
        log.info("【查询授权模板】上级代理商ID：{}", parentIds);


        //判断是否存在会员ID，如果存在，则根据会员所在的门店来显示授权列表，否则根据登录的用户所属门店来展示
        Long deptId = LoginHelper.getDeptId();
        try {
            if (ObjectUtil.isNotNull(bo.getStudentId())) {
                //获取会员ID
                Long studentId = bo.getStudentId();
                //根据会员ID查询所属门店
                RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(studentId);
                Long branchId = remoteStudentVo.getBranchId();
                //根据门店查询门店所属组织ID
                Long createDeptId = remoteBranchService.selectDeptIdByBranchId(branchId);
                //查询上级组织ID
                RemoteDeptVo preDeptByDeptId = remoteDeptService.getPreDeptByDeptId(createDeptId);
                deptId = preDeptByDeptId.getDeptId();
                log.info("【查询授权模板】根据会员查询授权列表，会员ID：{}，所属上级部门ID：{}", studentId, deptId);
            } else {
                log.info("【查询授权模板】登录0用户的所属部门ID：{}", deptId);
                //deptId 判断是是否是门店
                if (remoteDeptService.deptIsShop(deptId)) {
                    RemoteDeptVo preDeptByDeptId = remoteDeptService.getPreDeptByDeptId(LoginHelper.getSelectDeptId());
                    deptId = preDeptByDeptId.getDeptId();
                    log.info("【查询授权模板】登录用户是门店用户，所属上级部门ID：{}", deptId);
                }
            }
        } catch (Exception e) {
            log.warn("【查询授权模板】查询上级组织ID异常!", e);
        }

        //查询当前代理商下，已经授权的课程模板列表
        List<StudentProductResTemplateAuthVo> authCourseTempVoList = studentProductTemplateAuthMapper.queryAuthList(buildQuery2(deptIds, bo.getTemplateType()));
        log.info("【查询授权模板】已授权的课程模板列表：{}", authCourseTempVoList);
        //获取所有已经授权过的模板ID集合
        Set<Long> tempIds = Optional.ofNullable(authCourseTempVoList).orElse(new ArrayList<>())
                .stream().map(StudentProductResTemplateAuthVo::getTemplateId).collect(Collectors.toSet());

        //当前查询应该查看到的模板ID集合（根据登录用户所属部门、上级部门、点击部门等多个因素组合）
        Set<Long> showTempIds = new HashSet<>();
        //上级代理授权的模板ID集合
        Set<Long> preTempIds = new HashSet<>();
        //如果上级代理商包含总部 或者 当前登录用户直接所属总部，则“上级授权的模板”就是“总部创建的所有模板”
        if (parentIds.contains(UserConstants.TOP_DEPT_ID) || UserConstants.TOP_DEPT_ID.equals(deptId)) {
            preTempIds.addAll(Optional.ofNullable(productTemplateVos).orElse(new ArrayList<>())
                    .stream()
                    .filter(productTemplateVo -> productTemplateVo.getCreateDept().equals(UserConstants.TOP_DEPT_ID))
                    .map(ProductTemplateVo::getProductTemplateId)
                    .collect(Collectors.toSet()));
        } else {
            //查询上一级代理商授权的模板列表（当前代理商能看到上级代理商授权过的模板列表）
            List<StudentProductResTemplateAuthVo> preAuthCourseTempVoList = studentProductTemplateAuthMapper.queryAuthList(buildQuery2(parentIds, bo.getTemplateType()));
            log.info("【查询授权模板】上一级代理商已授权的课程模板列表：{}", preAuthCourseTempVoList);
            //获取所有已经授权过的模板ID集合
            preTempIds.addAll(Optional.ofNullable(preAuthCourseTempVoList).orElse(new ArrayList<>())
                    .stream().map(StudentProductResTemplateAuthVo::getTemplateId).collect(Collectors.toSet()));
        }
        log.info("【查询授权模板】上一级代理商已授权的课程模板ID：{}", preTempIds);

        //如果登录用户所属部门，和查询代理商（部门）不一致，则只显示上级授权过的模板
        //如果是总部用户登录，也是直接查询”总部创建的所有模板“
        if (!deptIds.contains(deptId) || UserConstants.TOP_DEPT_ID.equals(deptId)) {
            showTempIds.addAll(preTempIds);
        }

        //如果登录用户所属部门，和查询代理商（部门）一致，则（上级授权 交集 自己授权） + 自己创建
        Long tempDeptId = deptId;
        if (deptIds.contains(deptId)) {
            showTempIds.addAll(preTempIds.stream().filter(tempIds::contains).toList());
            //加上自己创建的部门
            showTempIds.addAll(Optional.ofNullable(productTemplateVos).orElse(new ArrayList<>())
                    .stream()
                    .filter(productTemplateVo -> productTemplateVo.getCreateDept().equals(tempDeptId))
                    .map(ProductTemplateVo::getProductTemplateId)
                    .collect(Collectors.toSet()));
        }

        //处理产品模板列表，设置每个模板的授权状态
        productTemplateVos = productTemplateVos.stream()
             //根据可见的模板ID进行过滤
            .filter(s -> showTempIds.contains(s.getProductTemplateId()))
            .map(s -> {
                    s.setAuthStatus(tempIds.contains(s.getProductTemplateId()) ? 1 : 0);
                    return s;
                })
            //判断是否只显示授权的列表
            .filter(s -> ObjectUtil.isNull(bo.getShowAuth()) || !bo.getShowAuth() || s.getAuthStatus() == 1 || UserConstants.TOP_DEPT_ID.equals(tempDeptId))
            .collect(Collectors.toList());
        log.info("【查询授权模板】返回的授权课程模板列表：{}", productTemplateVos);

        //对产品模板进行否则处理
        postProcessorHandler(bo, productTemplateVos);

        return productTemplateVos;
    }

    /**
     * 产品模板后置处理器
     * @param productTemplateVos
     */
    private void postProcessorHandler(StudentProductTemplateAuthBo bo, List<ProductTemplateVo> productTemplateVos) {

        if (ObjectUtil.isEmpty(productTemplateVos)) return;

        //获取所有资源ID集合
        List<Long> resIds = productTemplateVos.stream().filter(productTemplateVo -> ObjectUtil.isNotEmpty(productTemplateVo.getResIds()))
            .flatMap(productTemplateVo -> productTemplateVo.getResIds().stream())
            .collect(Collectors.toList());

        //获取模板的类型列表
        List<Integer> templateTypes = productTemplateVos.stream().map(ProductTemplateVo::getProductTemplateType).toList();

        //根据查询模板类型，进行相关的处理
        if (templateTypes.contains(1)) {
            //如果是课程模板，则查询相应的学段集合
            //根据课程对象的课程ID集合，查询获得对应的学段集合
            //Map<Long, String> - key：课程ID，value：学段名称
            Map<Long, String> courseStageMap = remoteStudentCourseService.getStageListByCourseIds(resIds);

            if (ObjectUtil.isEmpty(courseStageMap)) return;

            //将课程对象集合中的课程ID集合，与学段集合进行关联
            productTemplateVos.stream()
                .filter(productTemplateVo -> productTemplateVo.getProductTemplateType() == 1)
                .filter(productTemplateVo -> ObjectUtil.isNotEmpty(productTemplateVo.getResIds()))
                .forEach(productTemplateVo -> {
                    productTemplateVo.getResIds().stream().forEach(resId -> {
                        productTemplateVo.addStageName(courseStageMap.get(resId));
                    });
                });
        }

        if (templateTypes.contains(0)) {
            //如果是会员卡模板，则查询相应的会员卡名称
            RemoteProductBo remoteProductBo = new RemoteProductBo();
            remoteProductBo.setProductIdList(resIds);
            //根据会员卡ID集合，查询会员卡名称
            List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(remoteProductBo, true);

            if (ObjectUtil.isEmpty(remoteProductVos)) return;

            //转换成Map集合，key：会员卡ID，value：会员卡名称
            Map<Long, String> productNameMap = remoteProductVos.stream()
                .collect(Collectors.toMap(RemoteProductVo::getProductId, RemoteProductVo::getProductName));

            productTemplateVos.stream()
                .filter(productTemplateVo -> productTemplateVo.getProductTemplateType() == 0)
                .filter(productTemplateVo -> ObjectUtil.isNotEmpty(productTemplateVo.getResIds()))
                .forEach(productTemplateVo -> {
                    productTemplateVo.getResIds().stream().forEach(resId -> {
                        productTemplateVo.addMemberName(productNameMap.get(resId));
                    });
                });
        }
    }

    private QueryWrapper<StudentProductTemplateAuth> buildQueryWrapper(Long deptId, List<Integer> templateTypes) {
        QueryWrapper<StudentProductTemplateAuth> lqw = Wrappers.query();
        lqw.eq("dept_id", deptId);
        lqw.in(ObjectUtil.isNotEmpty(templateTypes), "template_type", templateTypes);
        return lqw;
    }

    private QueryWrapper<StudentProductTemplateAuth> buildQuery(Long deptId, Integer templateType) {
        QueryWrapper<StudentProductTemplateAuth> lqw = Wrappers.query();
        lqw.eq("t.dept_id", deptId);
        //查询对应的类型
        lqw.eq(ObjectUtil.isNotNull(templateType), "pt.product_template_type", templateType);
        //只查询上架的会员卡模板
        lqw.eq("pt.status", 0);
        return lqw;
    }

    private QueryWrapper<StudentProductTemplateAuth> buildQuery2(List<Long> deptIds, Integer templateType) {
        QueryWrapper<StudentProductTemplateAuth> lqw = Wrappers.query();
        lqw.in("t.dept_id", deptIds);
        //查询对应的类型
        lqw.eq(ObjectUtil.isNotNull(templateType), "pt.product_template_type", templateType);
        //只查询上架的会员卡模板
        lqw.eq("pt.status", 0);
        return lqw;
    }

    /**
     * 新增产品（会员卡模板）授权
     */
    @Override
    public Boolean insertByBo(StudentProductTemplateAuthBo bo) {
        StudentProductTemplateAuth add = MapstructUtils.convert(bo, StudentProductTemplateAuth.class);
        validEntityBeforeSave(add);
        boolean flag = studentProductTemplateAuthMapper.insert(add) > 0;
        if (flag) {
            bo.setTemplateDeptAuthId(add.getTemplateDeptAuthId());
        }
        return flag;
    }

    /**
     * 修改产品（会员卡模板）授权
     */
    @Override
    public Boolean updateByBo(StudentProductTemplateAuthBo bo) {
        StudentProductTemplateAuth update = MapstructUtils.convert(bo, StudentProductTemplateAuth.class);
        validEntityBeforeSave(update);
        return studentProductTemplateAuthMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentProductTemplateAuth entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除产品（会员卡模板）授权
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return studentProductTemplateAuthMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean authByBo(StudentProductTemplateAuthBo bo) {
        log.info("【修改代理商模板授权】请求参数：{}", bo);

        //获取模板信息列表
        List<StudentProductTemplateAuthBo.TemplateInfo> templateInfos = bo.getTemplateInfos();

        //获取模板类型列表
        List<Integer> templateType = templateInfos.stream().map(StudentProductTemplateAuthBo.TemplateInfo::getTemplateType).collect(Collectors.toList());

        //转换成模板ID与模板类型的Map集合
        //key：课程模板ID，value：模板类型
        Map<Long, Integer> cTempIdTypeMap = templateInfos.stream().filter(templateInfo -> ObjectUtil.isNotEmpty(templateInfo.getTemplateIds()))
            .flatMap(templateInfo -> templateInfo.getTemplateIds().stream().collect(Collectors.toMap(courseTempId -> courseTempId, courseTempId -> templateInfo.getTemplateType())).entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        log.info("【修改代理商模板授权】转换后的模板ID和模板类型的关联集合：{}", cTempIdTypeMap);

        //查询当前已授权的模板
        QueryWrapper<StudentProductTemplateAuth> userLqw = buildQueryWrapper(bo.getDeptId(), templateType);
        List<StudentProductTemplateAuthVo> userList = studentProductTemplateAuthMapper.selectVoList(userLqw);
        Set<Long> authTempIds = new HashSet<>();
        if (ObjectUtil.isNotEmpty(userList)) {
            authTempIds.addAll(userList.stream().map(StudentProductTemplateAuthVo::getTemplateId).collect(Collectors.toSet()));
        }
        log.info("【修改代理商模板授权】查询现有的授权模板ID列表：{}", authTempIds);

        //查询当前登录用户能看到的授权列表
        StudentProductTemplateAuthBo bo2 = new StudentProductTemplateAuthBo();
        bo2.setDeptId(bo.getDeptId());
        bo2.setShowAuth(true);
        List<ProductTemplateVo> productTemplateVos = queryProductAuthList(bo2);
        Set<Long> canShowTempId = new HashSet<>();
        if (ObjectUtil.isNotEmpty(productTemplateVos)) {
            canShowTempId.addAll(productTemplateVos.stream().map(ProductTemplateVo::getProductTemplateId).collect(Collectors.toSet()));
        }
        log.info("【修改代理商模板授权】当前用户能看到的授权模板ID集合：{}", canShowTempId);

        //根据能看到的授权列表，找出实际删除的模板ID
        Set<Long> deleteTempIds = authTempIds.stream()
                .filter(authTempId -> !cTempIdTypeMap.containsKey(authTempId))
                .filter(deleteTempId -> canShowTempId.contains(deleteTempId))
                .collect(Collectors.toSet());
        log.info("【修改代理商模板授权】需要删除的模板ID：{}", deleteTempIds);

        //删除当前代理商以及子代理商相关的模板授权关系
        if (ObjectUtil.isNotEmpty(deleteTempIds)) {
            // 删除当前代理商下面的所有授权模式关系
            studentProductTemplateAuthMapper.deleteBatchIds(userList.stream()
                    .filter(studentProductTemplateAuthVo -> deleteTempIds.contains(studentProductTemplateAuthVo.getTemplateId()))
                    .map(StudentProductTemplateAuthVo::getTemplateDeptAuthId)
                    .collect(Collectors.toList()));

            // 删除当前代理商下面的子代理商 所有授权模式关系
            List<RemoteDeptVo> deptChildrenList = remoteDeptService.getDeptChildrenList(bo.getDeptId());
            if (CollUtil.isNotEmpty(deptChildrenList) && CollUtil.isNotEmpty(deleteTempIds)) {
                // 需要删除的子代理商
                List<Long> deptChildrenIds = deptChildrenList.stream().map(RemoteDeptVo::getDeptId).collect(Collectors.toList());
                deleteTempIds.forEach(templateId -> {
                    LambdaQueryWrapper<StudentProductTemplateAuth> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.in(StudentProductTemplateAuth::getDeptId, deptChildrenIds);
                    queryWrapper.eq(StudentProductTemplateAuth::getTemplateId, templateId);
                    studentProductTemplateAuthMapper.delete(queryWrapper);
                });
            }
        }

        //如果没有关联集合，则直接返回true
        if (ObjectUtil.isEmpty(cTempIdTypeMap)) {
            return true;
        }

        //批量插入新的授权模板关系
        List<StudentProductTemplateAuth> addList = cTempIdTypeMap.entrySet().stream()
            //过滤掉之前已经存在的模板ID
            .filter(entry -> !authTempIds.contains(entry.getKey()))
            .map(entry -> new StudentProductTemplateAuth(entry.getKey(), bo.getDeptId(), entry.getValue())).toList();
        studentProductTemplateAuthMapper.insertBatch(addList);
        return true;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
