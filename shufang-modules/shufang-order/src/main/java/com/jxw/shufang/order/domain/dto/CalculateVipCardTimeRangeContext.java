package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/7/1 13:37
 * @Version 1
 * @Description
 */
@Data
public class CalculateVipCardTimeRangeContext {
    /**
     * 课程开始时间
     */
    private Date courseStartTime;
    /**
     * 订单付款截止时间
     */
    private Date installmentPayDeadlineTime;
    /**
     * 订单产品信息
     */
    private OrderProductInfoVo orderProductInfoVo;
    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;

    /**
     * 会员卡开始时间
     */
    private Date vipStartTime;
    /**
     * 会员卡到期时间
     */
    private Date vipEndTime;

    public Date getVipStartTime() {
        return vipStartTime;
    }

    public Date getVipEndTime() {
        return vipEndTime;
    }

    private CalculateVipCardTimeRangeContext() {
    }

    public static CalculateVipCardTimeRangeContext of(Date courseStartTime,
                                                      Date installmentPayDeadlineTime,
                                                      OrderProductInfoVo orderProductInfoVo,
                                                      OrderStatusEnum orderStatus) {
        CalculateVipCardTimeRangeContext calculateVipCardTimeRangeContext = new CalculateVipCardTimeRangeContext();
        calculateVipCardTimeRangeContext.setCourseStartTime(courseStartTime);
        calculateVipCardTimeRangeContext.setOrderProductInfoVo(orderProductInfoVo);
        calculateVipCardTimeRangeContext.setOrderStatus(orderStatus);
        calculateVipCardTimeRangeContext.setInstallmentPayDeadlineTime(installmentPayDeadlineTime);
        return calculateVipCardTimeRangeContext;
    }

    /**
     * 计算会员卡开始时间和到期时间，当存在开课时间和补缴时间要做特殊处理
     * @param createTime
     * @return
     */
    public CalculateVipCardTimeRangeContext calculate(LocalDateTime createTime) {
        if (null == createTime) {
            throw new ServiceException("开始时间不能为空");
        }
        LocalDateTime productBeginDate = Optional.ofNullable(courseStartTime)
            .map(DateUtils::dateToLocalDateTime)
            .orElse(createTime);

        boolean isPendingPay = OrderStatusEnum.PENDING_PAY.equals(orderStatus);

        LocalDateTime productEndDate = null;
        Long productValidDays = orderProductInfoVo.getProductValidDays();
        String productValidTimeLimit = orderProductInfoVo.getProductValidTimeLimit();

        // 固定天数会员卡计算
        if (productValidDays != null) {
            productEndDate = isPendingPay ?
                DateUtils.dateToLocalDateTime(installmentPayDeadlineTime) :
                productBeginDate.plusDays(productValidDays);

        } else if (StringUtils.isNotBlank(productValidTimeLimit)) {
            // 区间范围会员卡计算
            String[] split = productValidTimeLimit.split(" 至 ");
            if (split.length != 2) {
                throw new ServiceException("产品有效期格式不正确");
            }
            productBeginDate = Optional.ofNullable(courseStartTime)
                .map(DateUtils::dateToLocalDateTime)
                .orElse(DateUtils.parseLocalDateTime(split[0]));

            productEndDate = isPendingPay ?
                DateUtils.dateToLocalDateTime(installmentPayDeadlineTime) :
                DateUtils.parseLocalDateTime(split[1]);
        } else {
        }

        //会员卡到期时间改为退卡时间
        if (OrderStatusEnum.REFUNDED.equals(orderStatus)) {
            productEndDate = createTime;
        }

        this.vipStartTime = DateUtils.setStartOfDay(DateUtils.getCreateTimeAsDate(productBeginDate));
        this.vipEndTime = DateUtils.setEndOfDay(DateUtils.getCreateTimeAsDate(null == productEndDate ?
            createTime : productEndDate));
        return this;
    }
}
