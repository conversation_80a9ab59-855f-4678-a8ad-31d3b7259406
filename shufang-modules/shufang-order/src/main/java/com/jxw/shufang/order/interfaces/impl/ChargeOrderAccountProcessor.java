package com.jxw.shufang.order.interfaces.impl;


import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.enums.RechargeOrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.domain.bo.TransferBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;
import com.jxw.shufang.order.interfaces.OrderAccountProcessor;
import com.jxw.shufang.order.service.IAccountService;
import com.jxw.shufang.order.service.IRechargeOrderService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@RequiredArgsConstructor

public class ChargeOrderAccountProcessor implements OrderAccountProcessor {

    private final IRechargeOrderService orderService;
    @DubboReference
    private RemoteBranchService remoteBranchService;
    @DubboReference
    private RemoteDeptService remoteDeptService;
    private final IAccountService accountService;

    @Override
    public void process(Long orderId) {
        RechargeOrderVo order = orderService.queryById(orderId);
        if (ObjectUtils.isEmpty(order)) {
            throwException("订单不存在");
        }
        Long fromAccount = order.getHandlingDeptId();

        RemoteDeptAccountVo vo = remoteDeptService.selectDeptById(fromAccount);
        if (ObjectUtils.isEmpty(vo)) {
            throwException("充值代理商不存在");
        }
        //获取当前订单状态：代付款
        if (getOrderPayStatus(order)) {
            TransferBo transferBo = new TransferBo();
            AccountInfo to = new AccountInfo();
            AccountInfo from = new AccountInfo();
            from.setAccountId(order.getHandlingDeptId());
            from.setAccountType(AccountTradeTypeEnum.AGENT);
            to.setAccountId(order.getRechargeId());
            to.setAccountType(AccountTradeTypeEnum.getByCode(order.getRechargeType()));
            OrderInfo orderInfo = new OrderInfo();
            Long productDays = order.getProductDays();
            Long productNums = order.getProductNums();

            long sum = productDays * productNums;
            orderInfo.setOrderId(order.getOrderId());
            orderInfo.setAmount(Math.toIntExact(sum));
            orderInfo.setOrderTypeEnum(OrderTypeEnum.CHARGE);
            transferBo.setFromAccountInfo(from);
            transferBo.setToAccountInfo(to);
            transferBo.setOrderInfo(orderInfo);
            accountService.doTransfer(transferBo);
        } else {
            throwException("请稍后再试");
        }


    }

    private static boolean getOrderPayStatus(RechargeOrderVo order) {
        return RechargeOrderStatusEnum.WAIT_PAY.getCode().equals(order.getOrderStatus());
    }


    private void throwException(String msg) {
        throw new ServiceException(msg);
    }

    @Override
    public Boolean support(OrderTypeEnum orderTypeEnum) {
        return OrderTypeEnum.CHARGE.equals(orderTypeEnum);
    }
}
