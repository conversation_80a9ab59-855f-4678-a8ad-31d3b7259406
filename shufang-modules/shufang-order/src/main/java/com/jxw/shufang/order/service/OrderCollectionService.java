package com.jxw.shufang.order.service;


import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.bo.OrderCollectionBo;
import com.jxw.shufang.order.domain.dto.OrderCollectionDTO;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.enums.PayModeEnum;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @Date 2025/6/17 10:54
 * @Version 1
 * @Description
 */
@Service
public interface OrderCollectionService {
    OrderCollectionDTO collection(OrderCollectionBo orderCollectionBo,Long orderOperateId);

    OrderPayVO generatePaymentUrl(Order order, String orderPayNo);

    PayModeEnum payMode();
}
