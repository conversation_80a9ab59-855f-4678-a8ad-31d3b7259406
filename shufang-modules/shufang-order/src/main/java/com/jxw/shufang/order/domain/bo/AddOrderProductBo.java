package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/13 14:51
 * @Version 1
 * @Description
 */
@Data
public class AddOrderProductBo {
    @NotNull(message = "产品id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    @NotNull(message = "优惠价格（门店直减）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal preferentialPrice;

    /**
     * 会员优惠额度（自动扣减，用户无法编辑）
     */
    private BigDecimal studentPreferentialAmount;
}
