package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.ProductTemplateResourceRelationBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateResourceRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品模板-资源关联Service接口
 *
 * @date 2024-06-26
 */
public interface IProductTemplateResourceRelationService {

    /**
     * 查询产品模板-资源关联
     */
    ProductTemplateResourceRelationVo queryById(Long relationId);

    /**
     * 根据产品模板ID集合 查询模板-资源关联集合
     */
    List<ProductTemplateResourceRelationVo> queryListByTemplateIds(List<Long> templateIds);

    /**
     * 查询产品模板-资源关联列表
     */
    TableDataInfo<ProductTemplateResourceRelationVo> queryPageList(ProductTemplateResourceRelationBo bo, PageQuery pageQuery);

    /**
     * 查询产品模板-资源关联列表
     */
    List<ProductTemplateResourceRelationVo> queryList(ProductTemplateResourceRelationBo bo);

    /**
     * 新增产品模板-资源关联
     */
    Boolean insertByBo(ProductTemplateResourceRelationBo bo);

    /**
     * 批量新增 产品模板-资源关联
     */
    Boolean insertBatchByBo(Long templateId, List<Long> resIds);

    /**
     * 批量修改 产品模板-资源关联
     */
    Boolean updateByBos(Long templateId, List<Long> resIds);

    /**
     * 修改产品模板-资源关联
     */
    Boolean updateByBo(ProductTemplateResourceRelationBo bo);

    /**
     * 校验并批量删除产品模板-资源关联信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据模板ID 删除关联信息
     */
    Boolean deleteByTemplateId(Long templateId);

    /**
     * 根据模板ID集合 删除关联信息
     */
    Boolean deleteBatchByTemplateIds(List<Long> templateIds);
}
