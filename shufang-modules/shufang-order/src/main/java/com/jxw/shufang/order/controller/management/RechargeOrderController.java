package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.RechargeOrderBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;
import com.jxw.shufang.order.service.IRechargeOrderService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商充值订单记录
 * 前端访问路由地址为:/rechargeOrder/order
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/recharge/order")
public class RechargeOrderController extends BaseController {

    private final IRechargeOrderService rechargeOrderService;

    /**
     * 新增代理商充值订单记录
     */
    @SaCheckPermission("rechargeOrder:order:add")
    @Log(title = "代理商充值订单记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RechargeOrderBo bo) {
        return toAjax(rechargeOrderService.insertByBo(bo));
    }

    /**
     * 查询代理商充值订单记录列表
     */
    @SaCheckPermission("rechargeOrder:order:list")
    @GetMapping("/list")
    public TableDataInfo<RechargeOrderVo> list(RechargeOrderBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return rechargeOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取代理商充值订单记录详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("rechargeOrder:order:query")
    @GetMapping("/{orderId}")
    public R<RechargeOrderVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long orderId) {
        return R.ok(rechargeOrderService.queryById(orderId));
    }
}
