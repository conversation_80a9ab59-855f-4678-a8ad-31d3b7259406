package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.RechargeOrder;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 代理商充值订单记录业务对象 agent_order
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RechargeOrder.class, reverseConvertGenerate = false)
public class RechargeOrderBo extends BaseEntity {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = {EditGroup.class})
    private Long orderId;

    /**
     * 组织id（0的话是代理商id，类型为1的话是门店id）
     */
    @NotNull(message = "组织id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long rechargeId;

    /**
     * 0是代理商，1是门店
     */
    @Range(min = 0, max = 1, message = "组织类型取值范围为0到1", groups = {AddGroup.class, EditGroup.class})
    @NotNull(message = "组织类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer rechargeType;

    /**
     * 经办代理商id
     */
//    @NotNull(message = "经办人id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long handlingDeptId;

    /**
     * 经办日期
     */
    @NotNull(message = "经办日期不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;


    @NotNull(message = "订单产品信息不能为空", groups = {AddGroup.class})
    private RechargeOrderProductBo orderProduct;

    /**
     * query
     */
    private String orderNo;

    private String rechargeName;

    private Integer orderStatus;
    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;
    /**
     * 下单时间(经办日期)开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createOrderStartTime;

    /**
     * 下单时间(经办日期)结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createOrderEndTime;

    private List<Long> orderIdList;

}
