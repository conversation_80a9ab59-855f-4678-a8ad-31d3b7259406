package com.jxw.shufang.order.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/12 15:46
 * @Version 1
 * @Description 订单收款操作入参
 */
@Data
public class OrderCollectionBo{
    @NotNull(message = "订单ID不能为空")
    private Long orderId;
    /**
     * 是否分期付款 false-整笔支付, true-分期支付
     */
    private Boolean installmentFlag;

    /**
     * 是否代付  false-否, true-是
     */
    private Boolean peerPayFlag;

    /**
     * 是否在线付款 false-线下支付，true-线上支付
     */
    @NotNull(message = "是否在线付款不能为空")
    private Boolean onlinePayFlag;

    /**
     * 是否定金付款 false-否, true-是
     */
    private Boolean depositAmountFlag;

    /**
     * 付款备注
     */
    private String paymentRemark;

    @NotNull(message = "支付金额不能为空")
    private BigDecimal paymentAmount;

    /**
     * 支付凭证url
     */
    private String paymentVoucherImgId;
}
