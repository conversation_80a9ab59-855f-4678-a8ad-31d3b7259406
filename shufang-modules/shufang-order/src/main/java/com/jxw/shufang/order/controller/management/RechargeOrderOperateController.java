package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.Assert;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.RechargeOrderOperateBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderOperateVo;
import com.jxw.shufang.order.domain.vo.OrderOperateVo;
import com.jxw.shufang.order.service.IRechargeOrderOperateService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）
 * 前端访问路由地址为:/rechargeOrder/operate
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/recharge/operate")
public class RechargeOrderOperateController extends BaseController {

    private final IRechargeOrderOperateService rechargeOrderOperateService;

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
//    @SaCheckPermission("rechargeOrder:operate:list")
//    @GetMapping("/list")
    public TableDataInfo<RechargeOrderOperateVo> list(RechargeOrderOperateBo bo, PageQuery pageQuery) {
        return rechargeOrderOperateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
//    @SaCheckPermission("rechargeOrder:operate:export")
//    @Log(title = "订单操作记录 （时间逆序取最后一条和订单中的对应）", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
    public void export(RechargeOrderOperateBo bo, HttpServletResponse response) {
        List<RechargeOrderOperateVo> list = rechargeOrderOperateService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单操作记录 （时间逆序取最后一条和订单中的对应）", RechargeOrderOperateVo.class, response);
    }

    /**
     * 获取订单操作记录 （时间逆序取最后一条和订单中的对应）详细信息
     *
     * @param orderOperateId 主键
     */
//    @SaCheckPermission("rechargeOrder:operate:query")
//    @GetMapping("/{orderOperateId}")
    public R<RechargeOrderOperateVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long orderOperateId) {
        return R.ok(rechargeOrderOperateService.queryById(orderOperateId));
    }

    /**
     * 新增订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
//    @SaCheckPermission("rechargeOrder:operate:add")
//    @Log(title = "订单操作记录 （时间逆序取最后一条和订单中的对应）", businessType = BusinessType.INSERT)
//    @RepeatSubmit()
//    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody RechargeOrderOperateBo bo) {
        return toAjax(rechargeOrderOperateService.insertByBo(bo));
    }

    /**
     * 修改订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
//    @SaCheckPermission("rechargeOrder:operate:edit")
//    @Log(title = "订单操作记录 （时间逆序取最后一条和订单中的对应）", businessType = BusinessType.UPDATE)
//    @RepeatSubmit()
//    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody RechargeOrderOperateBo bo) {
        return toAjax(rechargeOrderOperateService.updateByBo(bo));
    }

    /**
     * 删除订单操作记录 （时间逆序取最后一条和订单中的对应）
     *
     * @param orderOperateIds 主键串
     */
//    @SaCheckPermission("rechargeOrder:operate:remove")
//    @Log(title = "订单操作记录 （时间逆序取最后一条和订单中的对应）", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{orderOperateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderOperateIds) {
        return toAjax(rechargeOrderOperateService.deleteWithValidByIds(List.of(orderOperateIds), true));
    }

    /**
     * 收款
     *
     * @param bo bo
     */
    @SaCheckPermission("rechargeOrder:operate:collection")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/collection")
    public R<List<OrderOperateVo>> collection(@RequestBody RechargeOrderOperateBo bo) {
        Assert.notNull(bo.getOrderId(), "订单id不能为空");
        rechargeOrderOperateService.collection(bo);
        return R.ok();
    }

    /**
     * 取消订单
     * @param bo bo
     */
    @SaCheckPermission("rechargeOrder:operate:cancel")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public R<List<OrderOperateVo>> cancel(@RequestBody RechargeOrderOperateBo bo) {
        Assert.notNull(bo.getOrderId(), "订单id不能为空");
        rechargeOrderOperateService.cancel(bo);
        return R.ok();
    }
}
