package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/13 17:39
 * @Version 1
 * @Description 提前购卡标识标识枚举
 */
public enum AdvancePayFlagEnum {
    NO_ADVANCE_PAY(0, "不提前购卡"),
    ADVANCE_PAY(1, "提前购卡");
    private Integer code;
    private String message;
    AdvancePayFlagEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public Integer getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }

    public static String getMessageByCode(Integer code) {
        for (AdvancePayFlagEnum value : AdvancePayFlagEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMessage();
            }
        }
        return null;
    }
}
