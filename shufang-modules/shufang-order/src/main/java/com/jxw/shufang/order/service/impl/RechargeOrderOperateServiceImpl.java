package com.jxw.shufang.order.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.enums.RechargeOrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.RechargeOrder;
import com.jxw.shufang.order.domain.RechargeOrderOperate;
import com.jxw.shufang.order.domain.RechargeOrderProduct;
import com.jxw.shufang.order.domain.bo.RechargeOrderOperateBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderOperateVo;
import com.jxw.shufang.order.domain.vo.RechargeOrderProductVo;
import com.jxw.shufang.order.mapper.RechargeOrderMapper;
import com.jxw.shufang.order.mapper.RechargeOrderOperateMapper;
import com.jxw.shufang.order.mapper.RechargeOrderProductMapper;
import com.jxw.shufang.order.service.IOrderAccountService;
import com.jxw.shufang.order.service.IRechargeOrderOperateService;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class RechargeOrderOperateServiceImpl implements IRechargeOrderOperateService, BaseService {

    private final RechargeOrderOperateMapper baseMapper;
    private final RechargeOrderMapper rechargeOrderMapper;
    private final RechargeOrderProductMapper rechargeOrderProductMapper;
    private final IOrderAccountService iOrderAccountService;
    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    @Override
    public RechargeOrderOperateVo queryById(Long orderOperateId) {
        return baseMapper.selectVoById(orderOperateId);
    }

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
    @Override
    public TableDataInfo<RechargeOrderOperateVo> queryPageList(RechargeOrderOperateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<RechargeOrderOperate> lqw = buildQueryWrapper(bo);
        Page<RechargeOrderOperateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
    @Override
    public List<RechargeOrderOperateVo> queryList(RechargeOrderOperateBo bo) {
        LambdaQueryWrapper<RechargeOrderOperate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<RechargeOrderOperate> buildQueryWrapper(RechargeOrderOperateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RechargeOrderOperate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, RechargeOrderOperate::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderOperateStatus()), RechargeOrderOperate::getOrderOperateStatus, bo.getOrderOperateStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentType()), RechargeOrderOperate::getPaymentType, bo.getPaymentType());
        lqw.eq(bo.getPaymentAmount() != null, RechargeOrderOperate::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getRefundType()), RechargeOrderOperate::getRefundType, bo.getRefundType());
        lqw.eq(bo.getRefundAmount() != null, RechargeOrderOperate::getRefundAmount, bo.getRefundAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderOperateRemark()), RechargeOrderOperate::getOrderOperateRemark, bo.getOrderOperateRemark());
        return lqw;
    }

    /**
     * 新增订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean insertByBo(RechargeOrderOperateBo bo) {
        RechargeOrderOperate add = MapstructUtils.convert(bo, RechargeOrderOperate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderOperateId(add.getOrderOperateId());
        }
        return flag;
    }

    /**
     * 修改订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean updateByBo(RechargeOrderOperateBo bo) {
        RechargeOrderOperate update = MapstructUtils.convert(bo, RechargeOrderOperate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RechargeOrderOperate entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Long initRechargeOrderOperate(Long orderId) {
        RechargeOrderOperate add = new RechargeOrderOperate();
        add.setOrderId(orderId);
        //默认为待支付
        add.setOrderOperateStatus(RechargeOrderStatusEnum.WAIT_PAY.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("初始化订单操作记录失败");
        }
        return add.getOrderOperateId();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void collection(RechargeOrderOperateBo bo) {
        if (bo.getOrderId() == null || bo.getOrderId() <= 0) {
            throw new ServiceException("订单id不能为空");
        }
        //校验订单信息
        RechargeOrder rechargeOrder = rechargeOrderMapper.selectById(bo.getOrderId());
        if (rechargeOrder == null) {
            throw new ServiceException("订单不存在");
        }
        if (!Objects.equals(rechargeOrder.getHandlingDeptId(), LoginHelper.getDeptId())) {
            throw new ServiceException("无权限操作");
        }
        //获取订单当前操作
        RechargeOrderOperate curOperate = baseMapper.selectById(rechargeOrder.getOrderOperateId());
        if (curOperate == null) {
            throw new ServiceException("订单状态异常，请联系客服");
        }
        if (!Objects.equals(curOperate.getOrderOperateStatus(), RechargeOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new ServiceException("订单已处理，请刷新后再试");
        }
        RechargeOrderProductVo productVo = rechargeOrderProductMapper.selectVoOne(new LambdaQueryWrapper<RechargeOrderProduct>()
            .eq(RechargeOrderProduct::getOrderId, bo.getOrderId()));
        if (productVo == null) {
            throw new ServiceException("产品不存在");
        }
        iOrderAccountService.getProcessor(bo.getOrderId(), OrderTypeEnum.CHARGE);

        RechargeOrderOperate add = new RechargeOrderOperate();
        add.setOrderId(bo.getOrderId());
        add.setOrderOperateStatus(RechargeOrderStatusEnum.PAYED.getCode());
        add.setPaymentType("线下");
        add.setPaymentAmount(productVo.getAmount());
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("收款失败");
        }

        //更新订单操作id
        RechargeOrder update = new RechargeOrder();
        update.setOrderId(bo.getOrderId());
        update.setOrderOperateId(add.getOrderOperateId());
        int i = rechargeOrderMapper.updateById(update);
        if (i <= 0) {
            throw new ServiceException("收款失败");
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void cancel(RechargeOrderOperateBo bo) {
        if (bo.getOrderId() == null || bo.getOrderId() <= 0) {
            throw new ServiceException("订单id不能为空");
        }
        //校验订单信息
        RechargeOrder rechargeOrder = rechargeOrderMapper.selectById(bo.getOrderId());
        if (rechargeOrder == null) {
            throw new ServiceException("订单不存在");
        }
        if (!Objects.equals(rechargeOrder.getHandlingDeptId(), LoginHelper.getDeptId())) {
            throw new ServiceException("无权限操作");
        }
        //获取订单当前操作
        RechargeOrderOperate curOperate = baseMapper.selectById(rechargeOrder.getOrderOperateId());
        if (curOperate == null) {
            throw new ServiceException("订单状态异常，请联系客服");
        }
        if (!Objects.equals(curOperate.getOrderOperateStatus(), RechargeOrderStatusEnum.WAIT_PAY.getCode())) {
            throw new ServiceException("订单已处理，请刷新后再试");
        }

        RechargeOrderOperate add = new RechargeOrderOperate();
        add.setOrderId(bo.getOrderId());
        add.setOrderOperateStatus(RechargeOrderStatusEnum.CANCEL.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("收款失败");
        }

        //更新订单操作id
        RechargeOrder update = new RechargeOrder();
        update.setOrderId(bo.getOrderId());
        update.setOrderOperateId(add.getOrderOperateId());
        int i = rechargeOrderMapper.updateById(update);
        if (i <= 0) {
            throw new ServiceException("收款失败");
        }
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
