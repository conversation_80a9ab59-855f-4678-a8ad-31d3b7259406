package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.Assert;
import com.baomidou.lock.annotation.Lock4j;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.order.domain.OrderOperateApplyRecord;
import com.jxw.shufang.order.domain.bo.*;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.enums.PayModeEnum;
import com.jxw.shufang.order.service.OrderPayRecordService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.service.IOrderOperateService;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 订单操作记录
（时间逆序取最后一条和订单中的对应）
 * 前端访问路由地址为:/order/operate
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/operate")
public class OrderOperateController extends BaseController {

    private final IOrderOperateService orderOperateService;
    private final OrderPayRecordService orderPayRecordService;

    private final RedissonClient redissonClient;

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    @SaCheckPermission("order:operate:list")
    @GetMapping("/list")
    public TableDataInfo<OrderOperateVo> list(OrderOperateBo bo, PageQuery pageQuery) {
        return orderOperateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    @SaCheckPermission("order:operate:export")
    @Log(title = "订单操作记录（时间逆序取最后一条和订单中的对应）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrderOperateBo bo, HttpServletResponse response) {
        List<OrderOperateVo> list = orderOperateService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单操作记录（时间逆序取最后一条和订单中的对应）", OrderOperateVo.class, response);
    }

    /**
     * 获取订单操作记录（时间逆序取最后一条和订单中的对应）详细信息
     *
     * @param orderOperateId 主键
     */
    @SaCheckPermission("order:operate:query")
    @GetMapping("/{orderOperateId}")
    public R<OrderOperateVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long orderOperateId) {
        return R.ok(orderOperateService.queryById(orderOperateId));
    }

    /**
     * 新增订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @SaCheckPermission("order:operate:add")
    @Log(title = "订单操作记录（时间逆序取最后一条和订单中的对应）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrderOperateBo bo) {
        return toAjax(orderOperateService.insertByBo(bo));
    }

    /**
     * 修改订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @SaCheckPermission("order:operate:edit")
    @Log(title = "订单操作记录（时间逆序取最后一条和订单中的对应）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrderOperateBo bo) {
        return toAjax(orderOperateService.updateByBo(bo));
    }

    /**
     * 删除订单操作记录（时间逆序取最后一条和订单中的对应）
     *
     * @param orderOperateIds 主键串
     */
    @SaCheckPermission("order:operate:remove")
    @Log(title = "订单操作记录（时间逆序取最后一条和订单中的对应）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderOperateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderOperateIds) {
        return toAjax(orderOperateService.deleteWithValidByIds(List.of(orderOperateIds), true));
    }

    /**
     * 收款
     *
     * @param bo bo
     * @date 2024/03/03 03:48:25
     */
    @SaCheckPermission("order:operate:collection")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/collection")
    public R<OrderCollectionVo> collection(@Validated(EditGroup.class) @RequestBody OrderCollectionBo bo) {
        if (null == bo.getOrderId()){
            return R.fail("订单id不能为空");
        }
        if(null == bo.getPaymentAmount()){
            return R.fail("支付金额不能为空");
        }
        return R.ok(orderOperateService.collection(bo));
    }



    /**
     * 生成付款二维码
     *
     * @param bo bo
     * @date 2024/03/03 03:48:25
     */
    @SaCheckPermission("order:operate:collection")
    @PostMapping("/onlinePay/generateQrCode")
    public R<PaymentQrCodeVo> generatePaymentQrCode(@Validated(EditGroup.class) @RequestBody CreatePayQrCodeBo bo) {
        return R.ok(orderOperateService.generatePaymentQrCode(bo));
    }

    /**
     * 查看订单支持的支付类型
     *
     * @param orderId
     * @date 2024/03/03 03:48:25
     */
    @SaCheckPermission("order:operate:collection")
    @GetMapping("/onlinePay/payType")
    public R<List<OnlinePayTypeVo>> onlinePayType(@Validated(EditGroup.class) Long orderId) {
        if (null == orderId){
            return R.fail("订单id不能为空");
        }

        Set<PayModeEnum> payModeEnums = orderOperateService.queryOnlinePayTypeByOrderId(orderId);
        Boolean ableInstallment = orderOperateService.queryOrderAbleInstallment(orderId);
        List<OnlinePayTypeVo> onlinePayTypeList = payModeEnums.stream()
            .map(payModeEnum -> new OnlinePayTypeVo(1 == payModeEnum.getModeCode(),
                payModeEnum.getDesc(),
                new OnlinePayTypeSupportOperate(ableInstallment)))
            .toList();
        return R.ok(onlinePayTypeList);
    }

    /**
     * 取消当前支付单
     */
    @SaCheckPermission("order:operate:collection")
    @PostMapping("/onlinePay/cancelPayOrder")
    public R<Void> cancelPayOrder(@Validated(EditGroup.class) @RequestBody CancelPayOrderBo cancelPayOrder) {
        if(null == cancelPayOrder.getOrderPayNo()){
            return R.fail("分期付款支付单号不能为空");
        }
        Long userId = LoginHelper.getUserId();
        orderPayRecordService.cancelPayOrder(cancelPayOrder,userId);
        return R.ok("取消成功");
    }


    /**
     * 检查订单是否支付
     *
     * @param orderId
     * @date 2024/03/03 03:48:25
     */
    @SaCheckPermission("order:operate:collection")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @GetMapping("/onlinePay/check")
    public R<CheckOrderPayStatusVo> checkOrderPay(@Validated(EditGroup.class) Long orderId, String orderPayNo) {
        if(null == orderId){
            return R.fail("订单id不能为空");
        }
        if(null == orderPayNo){
            return R.fail("订单支付单号不能为空");
        }
        return R.ok(orderOperateService.checkOrderPay(orderId,orderPayNo));
    }

    /**
     * 退款
     *
     * @param bo bo
     */
    @SaCheckPermission("order:operate:refund")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/refund")
    public R<List<OrderOperateVo>> refund(@RequestBody OrderOperateBo bo) {
        Assert.notNull(bo.getOrderId(), "订单id不能为空");
        orderOperateService.refund(bo);
        return R.ok();
    }

    /**
     * 退款申请
     *
     * @param bo bo
     */
    @SaCheckPermission("order:operate:refundApply")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/refundApply")
    public R<List<OrderOperateVo>> refundApply(@RequestBody OrderOperateBo bo) {
        Assert.notNull(bo.getOrderId(), "订单id不能为空");
        Assert.notNull(bo.getPayMode(), "退款方式不为空");
        Assert.notNull(bo.getRefundAmount(), "退款金额不能为空");
        if (StringUtils.isBlank(bo.getOrderOperateRemark())) {
            return R.fail("请输入退款原因");
        }
        int maxRefundReasonLength = 255;
        if(bo.getOrderOperateRemark().length() > maxRefundReasonLength ){
            return R.fail("退款原因不能超过255个字符");
        }
        if (PayModeEnum.OFFLINE.getModeCode().equals(bo.getPayMode())) {
            Assert.notNull(bo.getRefundAccount(), "退款账号不为空");
            Assert.notNull(bo.getRefundAccountName(), "退款人姓名不为空");
            Assert.notNull(bo.getRefundBankName(), "退款开户行信息不为空");
        }
        orderOperateService.refundApply(bo);
        return R.ok();
    }

    /**
     * 退款申请
     *
     * @param bo bo
     */
    @SaCheckPermission("order:operate:refundReview")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PostMapping("/refundReview")
    public R<List<OrderOperateVo>> refundReview(@RequestBody OrderOperateApplyRecord bo) {
        Assert.notNull(bo.getOrderId(), "请选择订单后操作退款");
        Assert.notNull(bo.getOrderOperateId(), "请选择订单后操作退款");
        Assert.notNull(bo.getReviewStatus(), "请选择审批类型");
        if (StringUtils.isEmpty(bo.getRemark())) {
            return R.fail("请输入审核意见");
        }
        int maxRefundReasonLength = 255;
        if(bo.getRemark().length() > maxRefundReasonLength ){
            return R.fail("审核意见不能超过255个字符");
        }
        orderOperateService.refundReview(bo);
        return R.ok();
    }

    /**
     * 退款申请
     *
     * @param bo bo
     */
    @PostMapping("/refundApply/permission")
    public R<RefundingOrderPermissionVo> checkOperatePermission(@RequestBody OrderOperateApplyRecord bo) {
        Assert.notNull(bo.getOrderId(), "请选择订单后操作");
        Assert.notNull(bo.getOrderOperateId(), "请选择订单后操作退款");
        return R.ok(orderOperateService.getOperatePermissionVo(bo));
    }

    /**
     * 取消订单
     * @param bo bo
     */
    @SaCheckPermission("order:operate:cancel")
    @Log(title = "订单操作记录", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel")
    public R<List<OrderOperateVo>> cancel(@RequestBody OrderOperateBo bo) {
        Assert.notNull(bo.getOrderId(), "订单id不能为空");
        orderOperateService.cancel(bo);
        return R.ok();
    }

}
