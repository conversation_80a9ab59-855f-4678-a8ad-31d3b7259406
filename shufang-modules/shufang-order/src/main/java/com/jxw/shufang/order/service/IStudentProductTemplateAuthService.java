package com.jxw.shufang.order.service;

import com.jxw.shufang.order.domain.bo.StudentProductTemplateAuthBo;
import com.jxw.shufang.order.domain.vo.StudentBaseTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductResTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductTemplateAuthVo;

import java.util.Collection;
import java.util.List;

/**
 * 产品（会员卡模板）授权Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IStudentProductTemplateAuthService {

    /**
     * 查询产品（会员卡模板）授权
     */
    StudentProductTemplateAuthVo queryById(Long templateDeptAuthId);

    /**
     * 查询产品（会员卡模板）授权列表
     */
    List<StudentProductResTemplateAuthVo> queryList();

    List<? extends StudentBaseTemplateAuthVo> queryAuthList(StudentProductTemplateAuthBo bo);

    /**
     * 新增产品（会员卡模板）授权
     */
    Boolean insertByBo(StudentProductTemplateAuthBo bo);

    /**
     * 修改产品（会员卡模板）授权
     */
    Boolean updateByBo(StudentProductTemplateAuthBo bo);

    /**
     * 校验并批量删除产品（会员卡模板）授权信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean authByBo(StudentProductTemplateAuthBo bo);
}
