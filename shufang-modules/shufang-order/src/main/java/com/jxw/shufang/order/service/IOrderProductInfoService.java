package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.order.domain.bo.AddOrderProductBo;
import com.jxw.shufang.order.domain.bo.OrderProductInfoBo;
import com.jxw.shufang.order.domain.dto.OrderCreationContext;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import com.jxw.shufang.order.domain.vo.StudentProductVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 订单产品详情Service接口
 * @date 2024-02-27
 */
public interface IOrderProductInfoService {

    /**
     * 查询订单产品详情
     */
    OrderProductInfoVo queryById(Long orderInfoId);

    /**
     * 查询订单产品详情列表
     */
    TableDataInfo<OrderProductInfoVo> queryPageList(OrderProductInfoBo bo, PageQuery pageQuery);

    /**
     * 查询订单产品详情列表
     */
    List<OrderProductInfoVo> queryList(OrderProductInfoBo bo);

    /**
     * 新增订单产品详情
     */
    Boolean insertByBo(OrderProductInfoBo bo);


    void insertBatchAndReturnActualAmount(List<AddOrderProductBo> orderProductInfoList, OrderCreationContext orderCreationContext, Long orderId, Long studentTypeId);

    /**
     * 修改订单产品详情
     */
    Boolean updateByBo(OrderProductInfoBo bo);

    /**
     * 校验并批量删除订单产品详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 计算订单实际金额
     *
     * @param orderId 订单id
     * @date 2024/03/03 03:53:04
     */
    BigDecimal calOrderActualAmount(Long orderId);

    /**
     * 获取订单实际支付金额，考虑订单类型，如果是替换旧卡，减去旧卡的价格
     *
     * @param orderId
     * @param orderType
     * @param orderRelationId
     * @return
     */
    BigDecimal calculateOrderActualPayAmountByOrder(Order order, Long orderRelationId);

    /**
     * 获取订单应付金额
     * @param orderId
     * @param orderRelationId
     * @return
     */
    BigDecimal getOrderAblePayAmountByOrder(Long orderId, Long orderRelationId);
    /**
     * 获取最新有效会员卡
     *
     * @param studentId 会员id
     * @param showNum   show num
     * @date 2024/03/14 09:04:15
     */
    StudentProductVo getLatestValidMemberCard(Long studentId, Integer showNum);

    /**
     * 获取所有会员卡
     *
     * @param studentId 会员id
     * @date 2024/03/15 03:23:44
     */
    List<OrderProductInfoVo> getAllMemberCard(Long studentId);

    public void cleanCache();

    BigDecimal calOrderActualAmount(Order order,List<OrderProductInfo> orderProductInfoList);

    BigDecimal calOrderProductActualAmount(Order order,OrderProductInfo orderProductInfo);

    OrderProductInfo queryProductByOrderProductId(Long orderId, Long productId);
    List<OrderProductInfo> queryProductByOrder(Long orderId);
}
