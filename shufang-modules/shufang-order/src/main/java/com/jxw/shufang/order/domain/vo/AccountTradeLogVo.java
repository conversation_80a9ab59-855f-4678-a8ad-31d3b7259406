package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.AccountTradeLog;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 账户流水 （扣款退费充值记录）视图对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AccountTradeLog.class)
public class AccountTradeLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户流水id
     */
    @ExcelProperty(value = "账户流水id")
    private Long accountTradeLogId;

    /**
     * 流水交易类型(0 支出 1 收入)
     */
    @ExcelProperty(value = "流水交易类型(0 支出 1 收入)")
    private Integer tradeType;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 订单类型(0充值订单,1会员订单)
     */
    @ExcelProperty(value = "订单类型(0充值订单,1会员订单)")
    private Integer orderType;

    @ExcelProperty(value = "经办人")
    private String handlingPerson;
    /**
     * 主体交易账号
     */
    @ExcelProperty(value = "主体交易账号")
    private Long accountId;

    /**
     * 主体交易账号类型(0代理商，1门店，2学生)
     */
    @ExcelProperty(value = "主体交易账号类型(0代理商，1门店，2学生)")
    private Integer accountType;

    /**
     * 对应账号
     */
    @ExcelProperty(value = "对应账号")
    private Long replyAccount;

    /**
     * 对应账号类型(0代理商，1门店，2学生)
     */
    @ExcelProperty(value = "对应账号类型(0代理商，1门店，2学生)")
    private Integer replyAccountType;

    @ExcelProperty(value = "变更原因")
    private String reason;

    /**
     * 数量  xxx天
     */
    @ExcelProperty(value = "数量")
    private Integer amount;

    private Date createTime;

}
