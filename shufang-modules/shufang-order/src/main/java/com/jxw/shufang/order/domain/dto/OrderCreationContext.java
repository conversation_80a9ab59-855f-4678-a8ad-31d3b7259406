package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/14 10:07
 * @Version 1
 * @Description
 */
@Data
public class OrderCreationContext {
    private Date courseStartTime;
    private List<RemoteProductVo> remoteProductList;
    private Long branchId;

    public OrderCreationContext(Date courseStartTime, List<RemoteProductVo> remoteProductList, Long branchId) {
        this.courseStartTime = courseStartTime;
        this.remoteProductList = remoteProductList;
        this.branchId = branchId;
    }

}
