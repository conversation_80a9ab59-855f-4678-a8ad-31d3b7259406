package com.jxw.shufang.order.enums;


/**
 * <AUTHOR>
 * @Date 2025/7/4 13:39
 * @Version 1
 * @Description 支付阶段枚举
 */
public enum PaymentStageEnum {
    FIRST_PAY("first_pay", "首笔支付"),
    SECOND_PAY("second_pay", "次笔支付"),
    LAST_PAY("last_pay", "尾款支付"),
    DEPOSIT_PAY("deposit_pay", "定金支付"),
    COMPLETE_PAY("complete_pay", "整单支付");
    private String code;
    private String message;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    PaymentStageEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static PaymentStageEnum getByCode(String code) {
        for (PaymentStageEnum value : PaymentStageEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
