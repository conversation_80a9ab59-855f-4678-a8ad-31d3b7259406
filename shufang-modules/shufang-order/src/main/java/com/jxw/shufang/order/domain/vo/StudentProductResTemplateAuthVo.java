package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * 产品授权视图对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
public class StudentProductResTemplateAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板权限id
     */
    @ExcelProperty(value = "模板权限id")
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
    @ExcelProperty(value = "模板id")
    private Long templateId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long deptId;

    /**
     * 课程模板名称
     */
    @ExcelProperty(value = "产品模板名称")
    private String productTempName;

    /**
     * 课程模板描述
     */
    @ExcelProperty(value = "产品模板描述")
    private String productTempDesc;

    /**
     * 产品状态（0上架 1下架）
     */
    @ExcelProperty(value = "产品状态")
    private String productStatus;

    /**
     * 授权状态 0-未授权 1-已授权
     */
    private Integer authStatus;

    /**
     * 关联的资源ID集合
     */
    private List<Long> resIds;

}
