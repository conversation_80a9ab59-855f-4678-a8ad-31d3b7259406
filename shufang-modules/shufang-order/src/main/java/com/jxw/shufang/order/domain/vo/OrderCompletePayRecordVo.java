package com.jxw.shufang.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/28 16:44
 * @Version 1
 * @Description 订单完整的支付记录
 */
@Data
public class OrderCompletePayRecordVo {
    /**
     * 订单支付编号
     */
    private String orderPayNo;
    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付结果 0-待支付 1-成功，2-异常
     */
    private Integer payResult;

    /**
     * 支付结果阶段名
     */
    private String payResultStageName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 渠道订单号
     */
    private String channelOrderId;
}
