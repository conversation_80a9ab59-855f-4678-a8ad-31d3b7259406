package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.ProductTemplateResourceRelation;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品模板-资源关联业务对象 pms_product_template_resource_relation
 *
 * @date 2024-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductTemplateResourceRelation.class, reverseConvertGenerate = false)
public class ProductTemplateResourceRelationBo extends BaseEntity {

    /**
     * 产品模板-资源关联关联ID
     */
    @NotNull(message = "关联ID不能为空", groups = {EditGroup.class})
    private Long relationId;

    /**
     * 产品模板ID（pms_product_template表-product_template_id字段）
     */
    @NotNull(message = "产品模板ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productTemplateId;

    /**
     * 资源ID（课程或者会员卡ID）
     */
    @NotNull(message = "资源ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long resourceId;

    /**
     * 创建部门
     */
    private Long createDept;
}
