package com.jxw.shufang.order.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/6/18 14:57
 * @Version 1
 * @Description
 */
@Data
public class OrderCollectionDTO {
    private Long orderId;
    private BigDecimal amount;
    private String orderPayNo;

    public static OrderCollectionDTO of(Long orderId, BigDecimal amount, String orderPayNo) {
        OrderCollectionDTO orderCollectionDTO = new OrderCollectionDTO();
        orderCollectionDTO.setOrderId(orderId);
        orderCollectionDTO.setAmount(amount);
        orderCollectionDTO.setOrderPayNo(orderPayNo);
        return orderCollectionDTO;
    }
}
