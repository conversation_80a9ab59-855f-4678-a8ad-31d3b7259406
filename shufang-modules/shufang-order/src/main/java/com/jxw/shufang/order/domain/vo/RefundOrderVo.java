package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/22
 */
@Data
@Builder
public class RefundOrderVo {
    /**
     * 会员姓名
     */
    @ExcelProperty(value = "会员姓名")
    private String studentName;
    /**
     * 会员账号
     */
    @ExcelProperty(value = "会员顾问")
    private String consultAccount;
    /**
     * 门店名称
     */
    @ExcelProperty(value = "门店名称")
    private String branchName;
    /**
     * 申请人
     */
    @ExcelProperty(value = "申请人")
    private String createByName;
    /**
     * 退款方式
     */
    @ExcelProperty(value = "退款方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "已=退款才存在")
    private String refundMode;
    /**
     * 申请日期
     */
    @ExcelProperty(value = "申请日期")
    private Date createTime;
    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额")
    private String refundAmount;
    /**
     * 退款状态
     */
    @ExcelProperty(value = "退款状态")
    private String orderWorkflowStatus;
    /**
     * 退款时间
     */
    @ExcelProperty(value = "退款时间")
    private Date refundTime;
    /**
     * 退款单号
     */
    @ExcelProperty(value = "退款单号")
    private String orderNo;
    /**
     * 退款原因
     */
    @ExcelProperty(value = "退款原因")
    private String refundRemark;
    /**
     * 审批意见
     */
    @ExcelProperty(value = "审批意见")
    private String applyRemark;

}
