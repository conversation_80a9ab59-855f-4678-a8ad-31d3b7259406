package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.ProductTemplateResourceRelation;
import com.jxw.shufang.order.domain.bo.ProductTemplateResourceRelationBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateResourceRelationVo;
import com.jxw.shufang.order.mapper.ProductTemplateResourceRelationMapper;
import com.jxw.shufang.order.service.IProductTemplateResourceRelationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品模板-资源关联Service业务层处理
 *
 * @date 2024-06-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateResourceRelationServiceImpl implements IProductTemplateResourceRelationService {

    private final ProductTemplateResourceRelationMapper baseMapper;

    /**
     * 查询产品模板-资源关联
     */
    @Override
    public ProductTemplateResourceRelationVo queryById(Long relationId){
        return baseMapper.selectVoById(relationId);
    }

    /**
     * 根据产品模板ID集合 查询模板-资源关联集合
     *
     * @param templateIds
     */
    @Override
    public List<ProductTemplateResourceRelationVo> queryListByTemplateIds(List<Long> templateIds) {
        if (ObjectUtil.isEmpty(templateIds)) return ListUtil.toList();

        return baseMapper.selectVoList(Wrappers.lambdaQuery(ProductTemplateResourceRelation.class)
            .in(ProductTemplateResourceRelation::getProductTemplateId, templateIds));
    }

    /**
     * 查询产品模板-资源关联列表
     */
    @Override
    public TableDataInfo<ProductTemplateResourceRelationVo> queryPageList(ProductTemplateResourceRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductTemplateResourceRelation> lqw = buildQueryWrapper(bo);
        Page<ProductTemplateResourceRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询产品模板-资源关联列表
     */
    @Override
    public List<ProductTemplateResourceRelationVo> queryList(ProductTemplateResourceRelationBo bo) {
        LambdaQueryWrapper<ProductTemplateResourceRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductTemplateResourceRelation> buildQueryWrapper(ProductTemplateResourceRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductTemplateResourceRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProductTemplateId() != null, ProductTemplateResourceRelation::getProductTemplateId, bo.getProductTemplateId());
        lqw.eq(bo.getResourceId() != null, ProductTemplateResourceRelation::getResourceId, bo.getResourceId());
        lqw.eq(bo.getCreateDept() != null, ProductTemplateResourceRelation::getCreateDept, bo.getCreateDept());
        return lqw;
    }

    /**
     * 新增产品模板-资源关联
     */
    @Override
    public Boolean insertByBo(ProductTemplateResourceRelationBo bo) {
        ProductTemplateResourceRelation add = MapstructUtils.convert(bo, ProductTemplateResourceRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRelationId(add.getRelationId());
        }
        return flag;
    }

    /**
     * 批量新增 产品模板-资源关联
     *
     * @param templateId
     * @param resIds
     */
    @Override
    public Boolean insertBatchByBo(Long templateId, List<Long> resIds) {
        log.info("【批量新增产品模板-资源关联】产品模板ID：{}, 资源ID集合：{}", templateId, resIds);
        if (!ObjectUtil.isAllNotEmpty(resIds, templateId)) return false;

        //进行去重处理
        Set<Long> resIdsSet = new HashSet<>(resIds);

        //创建关联关系对象集合
        List<ProductTemplateResourceRelationBo> bos = resIdsSet.stream().filter(ObjectUtil::isNotNull).map(resId -> {
            ProductTemplateResourceRelationBo relationbo = new ProductTemplateResourceRelationBo();
            relationbo.setProductTemplateId(templateId);//课程模板ID
            relationbo.setResourceId(resId); //课程ID
            return relationbo;
        }).collect(Collectors.toList());

        List<ProductTemplateResourceRelation> relations = MapstructUtils.convert(bos, ProductTemplateResourceRelation.class);
        boolean flags = baseMapper.insertBatch(relations);
        log.info("【批量新增产品模板-资源关联】批量新增的结果：{}", flags);
        return flags;
    }

    /**
     * 批量修改 产品模板-资源关联
     *
     * @param templateId
     * @param resIds
     */
    @Override
    public Boolean updateByBos(Long templateId, List<Long> resIds) {
        //批量删除现有的关联关系
        log.info("【修改产品模板-资源关联关系】产品模板ID：{}, 资源ID集合：{}", resIds);

        //删除现有的关联关系，根据模板ID
        this.deleteByTemplateId(templateId);

        //重新调用新增关系
        return this.insertBatchByBo(templateId, resIds);
    }

    /**
     * 修改产品模板-资源关联
     */
    @Override
    public Boolean updateByBo(ProductTemplateResourceRelationBo bo) {
        ProductTemplateResourceRelation update = MapstructUtils.convert(bo, ProductTemplateResourceRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductTemplateResourceRelation entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除产品模板-资源关联
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据模板ID 删除关联信息
     *
     * @param templateId
     */
    @Override
    public Boolean deleteByTemplateId(Long templateId) {
        if (ObjectUtil.isNull(templateId)) return false;
        boolean flag = baseMapper.delete(Wrappers.lambdaQuery(ProductTemplateResourceRelation.class)
            .eq(ProductTemplateResourceRelation::getProductTemplateId, templateId)) > 0;
        log.info("【删除产品模板-资源关联关系】删除模板ID：{} 关联关系的结果：{}", templateId, flag);
        return flag;
    }

    /**
     * 根据模板ID集合 删除关联信息
     *
     * @param templateIds
     */
    @Override
    public Boolean deleteBatchByTemplateIds(List<Long> templateIds) {
        if (ObjectUtil.isNull(templateIds)) return false;
        boolean flag = baseMapper.delete(Wrappers.lambdaQuery(ProductTemplateResourceRelation.class)
            .in(ProductTemplateResourceRelation::getProductTemplateId, templateIds)) > 0;
        log.info("【批量删除产品模板-资源关联关系】删除模板ID集合：{} 关联关系的结果：{}", templateIds, flag);
        return flag;
    }
}
