package com.jxw.shufang.order.domain.convert;

import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.domain.bo.StudentProductTemplateAuthBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface RemoteStudentProductTemplateToStudentProductTemplate extends BaseMapper<RemoteStudentProductTemplateAuthBo, StudentProductTemplateAuthBo> {
}
