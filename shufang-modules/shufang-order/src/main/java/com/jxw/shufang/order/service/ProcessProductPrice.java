package com.jxw.shufang.order.service;

import cn.hutool.core.util.StrUtil;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import io.seata.common.util.DateUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/6/16 10:58
 * @Version 1
 * @Description 处理产品信息
 */
public class ProcessProductPrice {
    /**
     * 产品信息
     */
    private final RemoteProductVo remoteProductVo;

    private final OrderProductInfo orderProductInfo;
    /**
     * 课程开始时间
     */
    private final Date courseStartTime;

    /**
     * 产品原始价格
     */
    private BigDecimal originProductPrice;

    /**
     * 产品价格 实付金额
     */
    private BigDecimal productPrice;

    /**
     * 期中折扣金额
     */
    private BigDecimal signupDiscountAmount;

    /**
     * 门店直减价格
     */
    private BigDecimal preferentialPrice;

    /**
     * 學生積分优惠金额
     */
    private BigDecimal studentPreferentialAmount;

    public static ProcessProductPrice of(RemoteProductVo remoteProductVo, OrderProductInfo orderProductInfo, Date courseStartTime) {
        return new ProcessProductPrice(remoteProductVo, orderProductInfo, courseStartTime);
    }

    public static ProcessProductPrice preOrderOf(RemoteProductVo remoteProductVo,
                                                 BigDecimal preferentialPrice,
                                                 Date courseStartTime,
                                                 BigDecimal studentPreferentialAmount) {
        return new ProcessProductPrice(remoteProductVo, courseStartTime, preferentialPrice, studentPreferentialAmount);
    }


    private ProcessProductPrice(RemoteProductVo remoteProductVo,
                                OrderProductInfo orderProductInfo,
                                Date courseStartTime) {
        this.remoteProductVo = remoteProductVo;
        this.orderProductInfo = orderProductInfo;
        this.courseStartTime = null == courseStartTime ? new Date() : courseStartTime;
    }

    private ProcessProductPrice(RemoteProductVo remoteProductVo,
                                Date courseStartTime,
                                BigDecimal preferentialPrice,
                                BigDecimal studentPreferentialAmount) {
        this.remoteProductVo = remoteProductVo;
        this.courseStartTime = null == courseStartTime ? new Date() : courseStartTime;
        this.preferentialPrice = null == preferentialPrice ? BigDecimal.ZERO : preferentialPrice;
        this.orderProductInfo = new OrderProductInfo();
        this.studentPreferentialAmount = null == studentPreferentialAmount ? BigDecimal.ZERO : studentPreferentialAmount;
    }

    public BigDecimal getSignupDiscountAmount() {
        return signupDiscountAmount;
    }

    public BigDecimal getProductPrice() {
        return productPrice;
    }

    public BigDecimal getOriginProductPrice() {
        return originProductPrice;
    }

    public BigDecimal getStudentPreferentialPrice() {
        return studentPreferentialAmount;
    }

    /**
     * 预计订单价格
     *
     * @return
     */
    public ProcessProductPrice prePrepareOrderPrice() {
        this.originProductPrice = remoteProductVo.getProductPrice();
        this.signupDiscountAmount = BigDecimal.ZERO;
        this.productPrice = remoteProductVo.getProductPrice().subtract(preferentialPrice).subtract(studentPreferentialAmount);
        return calculateTimeRangeProductPrice(remoteProductVo.getProductValidTimeLimit());
    }

    /**
     * 准备订单价格
     *
     * @return
     */
    public ProcessProductPrice prepareOrderPrice() {
        String productValidTimeLimit = orderProductInfo.getProductValidTimeLimit();

        BigDecimal preferentialPrice = Optional.ofNullable(orderProductInfo.getPreferentialPrice()).orElse(BigDecimal.ZERO);
        BigDecimal studentPreferentialPrice = Optional.ofNullable(orderProductInfo.getStudentPreferentialPrice()).orElse(BigDecimal.ZERO);
        BigDecimal productOriginPrice = Optional.ofNullable(courseStartTime)
            .map(time -> orderProductInfo.getOriginProductPrice())
            .orElseGet(() -> null == orderProductInfo.getProductPrice() ?
                remoteProductVo.getProductPrice() : orderProductInfo.getProductPrice());

        this.signupDiscountAmount = BigDecimal.ZERO;
        this.originProductPrice = productOriginPrice;
        this.productPrice = productOriginPrice.subtract(preferentialPrice).subtract(studentPreferentialPrice);

        // 計算區間卡
        return calculateTimeRangeProductPrice(productValidTimeLimit);
    }


    /**
     * 计算时间范围卡价格
     *
     * @param productValidTimeLimit
     * @return
     */
    private ProcessProductPrice calculateTimeRangeProductPrice(String productValidTimeLimit) {
        if (StrUtil.isEmpty(productValidTimeLimit)) {
            return this;
        }

        Date productValidStartDate = getValidStartDate(productValidTimeLimit);
        Date productValidEndDate = getValidEndDate(productValidTimeLimit);
        if (null == productValidStartDate || null == productValidEndDate) {
            throw new ServiceException("会员卡有效期时间格式错误");
        }

        LocalDate courseStartDate = getDateToLocalDate(courseStartTime);
        LocalDate productStartLocalDate = getDateToLocalDate(productValidStartDate);
        LocalDate productEndLocalDate = getDateToLocalDate(productValidEndDate);
        if (courseStartDate.isAfter(productEndLocalDate)) {
            throw new ServiceException("商品校验失败：课程开始时间不能超过会员卡时间范围有效期");
        }

        // 会员产品为时间范围卡时:计算公式为：会员卡价格/会员卡有效期） * （会员产品到期日—开课时间）
        long courseValidDays = ChronoUnit.DAYS.between(courseStartDate, productEndLocalDate) + 1;
        long productValidDays = ChronoUnit.DAYS.between(productStartLocalDate, productEndLocalDate) + 1;
        if (0 == productValidDays) {
            productValidDays = BigDecimal.ONE.longValue();
        }

        // 计算期中满减优惠金额
        this.calculateSignupDiscountAmount(courseValidDays, productValidDays);
        this.productPrice = productPrice.subtract(signupDiscountAmount);
        return this;
    }

    /**
     * 计算期中满减优惠金额
     *
     * @param courseValidDays
     * @param productValidDays
     */
    private void calculateSignupDiscountAmount(long courseValidDays, long productValidDays) {
        if (courseValidDays == productValidDays) {
            this.signupDiscountAmount = BigDecimal.ZERO;
            return;
        }
        if (courseValidDays > productValidDays) {
            throw new ServiceException("开课时间设置有误");
        }

        // 非最后一天金额，四舍五入后的
        BigDecimal productDailyPrice = originProductPrice.divide(
            new BigDecimal(productValidDays), 2, RoundingMode.HALF_UP
        );

        //  最后一天金额 = 总价 - 已分摊金额（确保总额精确）
        BigDecimal partialAmount = productDailyPrice.multiply(
            BigDecimal.valueOf(courseValidDays - 1)
        );
        BigDecimal lastDayAmount = originProductPrice.subtract(partialAmount);

        long reduceDays = productValidDays - courseValidDays;
        if (reduceDays == productValidDays) {
            this.signupDiscountAmount = originProductPrice.subtract(partialAmount.add(lastDayAmount));
        } else {
            this.signupDiscountAmount = productDailyPrice.multiply(BigDecimal.valueOf(reduceDays));
        }
    }

    public static LocalDate getDateToLocalDate(Date courseStartTime) {
        if (null == courseStartTime) {
            return new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
        return courseStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date getValidEndDate(String productValidTimeLimit) {
        try {
            return DateUtil.parseDate(productValidTimeLimit.split("至")[1], DateUtils.YYYY_MM_DD_HH_MM_SS);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    public static Date getValidStartDate(String productValidTimeLimit) {
        try {
            return DateUtil.parseDate(productValidTimeLimit.split("至")[0], DateUtils.YYYY_MM_DD_HH_MM_SS);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }
}
