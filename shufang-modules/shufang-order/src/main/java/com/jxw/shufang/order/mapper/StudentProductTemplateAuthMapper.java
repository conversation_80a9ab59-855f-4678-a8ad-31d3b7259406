package com.jxw.shufang.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.order.domain.StudentProductTemplateAuth;
import com.jxw.shufang.order.domain.vo.StudentProductResTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductTemplateAuthVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品（会员卡模板）授权Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface StudentProductTemplateAuthMapper extends BaseMapperPlus<StudentProductTemplateAuth, StudentProductTemplateAuthVo> {

    /**
     * 查询产品模板授权列表
     */
    List<StudentProductResTemplateAuthVo> queryAuthList(@Param(Constants.WRAPPER) QueryWrapper<StudentProductTemplateAuth> lqw);

}
