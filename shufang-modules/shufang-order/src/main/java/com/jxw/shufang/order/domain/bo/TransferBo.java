package com.jxw.shufang.order.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 交易请求体
 */
@Data
public class TransferBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 转出账户
     */
    private AccountInfo fromAccountInfo;

    /**
     * 转入账户
     */
    private AccountInfo toAccountInfo;
    /**
     * 转账天数
     */

    /**
     * 订单id
     */
    private OrderInfo orderInfo;

}
