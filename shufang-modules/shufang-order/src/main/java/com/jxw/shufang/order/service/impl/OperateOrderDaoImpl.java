package com.jxw.shufang.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.mapper.OrderOperateMapper;
import com.jxw.shufang.order.service.OperateOrderDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/6/20 17:23
 * @Version 1
 * @Description
 */
@Service
public class OperateOrderDaoImpl implements OperateOrderDao {
    @Resource
    private OrderOperateMapper operateOrderMapper;

    @Override
    public void updateById(OrderOperate orderOperate, Long id) {
        if (null == id) {
            throw new ServiceException("id不能为空");
        }
        LambdaUpdateWrapper<OrderOperate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrderOperate::getOrderOperateId, id);
        operateOrderMapper.update(orderOperate, updateWrapper);
    }

    @Override
    public OrderOperate queryById(Long id) {
        if (null == id) {
            throw new ServiceException("id不能为空");
        }
        return operateOrderMapper.selectById(id);
    }
}
