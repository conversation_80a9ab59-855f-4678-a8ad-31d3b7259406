package com.jxw.shufang.order.domain.convert;


import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteOrderBoToOrderBo extends BaseMapper<RemoteOrderBo, OrderBo> {

}
