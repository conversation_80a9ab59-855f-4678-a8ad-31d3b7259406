package com.jxw.shufang.order.domain.convert;

import com.jxw.shufang.order.api.domain.vo.RemoteStudentProductTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductTemplateAuthVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 **/
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface StudentProductTemplateAuthToRemote extends BaseMapper<StudentProductTemplateAuthVo, RemoteStudentProductTemplateAuthVo> {
}
