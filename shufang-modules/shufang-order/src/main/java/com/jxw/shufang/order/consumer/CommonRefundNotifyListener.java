package com.jxw.shufang.order.consumer;

import com.jxw.shufang.common.pay.domain.dto.notify.RefundNotifyDTO;
import com.jxw.shufang.order.enums.PayTradeNotifyTopicConstant;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.jxw.shufang.common.core.utils.ValidatorUtils;
import com.jxw.shufang.order.service.IOrderOperateService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/4/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(topic = PayTradeNotifyTopicConstant.REFUND_ORDER_NOTIFY_TOPIC,
    consumerGroup = PayTradeNotifyTopicConstant.NOTIFY_REFUND_CONSUMER_GROUP, messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.CONCURRENTLY, selectorExpression = PayTradeNotifyTopicConstant.ORDER_REFUND_NOTIFY_TAG)
public class CommonRefundNotifyListener implements RocketMQListener<String> {

    private final IOrderOperateService orderOperateService;

    @Override
    public void onMessage(String s) {
        log.info("处理通用退款消息：{}", s);
        RefundNotifyDTO notifyDTO = JSONObject.parseObject(s, RefundNotifyDTO.class);
        ValidatorUtils.validate(notifyDTO);
        orderOperateService.processRefundNotify(notifyDTO);
    }
}
