package com.jxw.shufang.order.interfaces.impl;


import cn.hutool.core.util.ObjectUtil;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.AccountTradeLogBo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.domain.bo.TransferBo;
import com.jxw.shufang.order.domain.vo.AccountTradeLogVo;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import com.jxw.shufang.order.domain.vo.OrderVo;
import com.jxw.shufang.order.interfaces.OrderAccountProcessor;
import com.jxw.shufang.order.service.IAccountService;
import com.jxw.shufang.order.service.IAccountTradeLogService;
import com.jxw.shufang.order.service.IOrderService;
import com.jxw.shufang.order.service.ProcessProductAccountDays;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;


@Component
@RequiredArgsConstructor
@Slf4j
public class StudentOrderAccountProcessor implements OrderAccountProcessor {
    private final IOrderService orderService;
    @DubboReference
    private RemoteStudentService remoteStudentService;
    @DubboReference
    private RemoteBranchService remoteBranchService;
    private final IAccountService accountService;

    private final IAccountTradeLogService accountTradeLogService;

    @Override
    public void process(Long orderId) {
        OrderVo order = orderService.queryById(orderId);
        log.info("【订单充值处理】订单号：{}， 查询订单对象：{}", orderId, order);
        if (ObjectUtils.isEmpty(order)) {
            throwException("订单不存在");
        }
        Long studentId = order.getStudentId();
        RemoteStudentVo remoteStudentBo = remoteStudentService.queryStudentWithBranchById(studentId);
        if (ObjectUtils.isEmpty(remoteStudentBo)) {
            throwException("学生不存在");
        }
        Long branchId = remoteStudentBo.getBranchId();

        //根据订单状态，处理门店/会员账户流水
        if (getOrderPayStatus(order)) {
            //获取当前订单状态：待付款
            TransferBo transferBo = new TransferBo();
            AccountInfo to = new AccountInfo();
            AccountInfo from = new AccountInfo();
            from.setAccountId(branchId);
            from.setAccountType(AccountTradeTypeEnum.BRANCH);
            to.setAccountId(studentId);
            to.setAccountType(AccountTradeTypeEnum.STUDENT);
            OrderInfo orderInfo = new OrderInfo();
            List<OrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollectionUtils.isEmpty(orderProductInfoList)) {
                return;
            }
            int sum = orderProductInfoList.stream()
                    .map(orderProductInfo -> getDeductSum(order, remoteStudentBo, orderProductInfo))
                    .mapToInt(Long::intValue)
                    .sum();

            orderInfo.setOrderId(order.getOrderId());
            orderInfo.setAmount(sum);
            orderInfo.setOrderTypeEnum(OrderTypeEnum.STUDENT);
            transferBo.setFromAccountInfo(from);
            transferBo.setToAccountInfo(to);
            transferBo.setOrderInfo(orderInfo);
            accountService.doTransfer(transferBo);

        } else if (getOrderCancelStatus(order)) {
            //获取当前订单状态：已取消

            //获取订单流水记录，以及有效天数
            //获取订单有效天数
            List<AccountTradeLogVo> accountTradeLogVos = getBranchCountTradeLogByOrder(order);
            if (ObjectUtil.isEmpty(accountTradeLogVos)) {
                //如果该订单没有交易流水，说明是久订单，无需执行时长返还的操作
                return;
            }

            AccountTradeLogVo accountTradeLogVo = accountTradeLogVos.get(0);
            if (null == accountTradeLogVo.getAmount() || accountTradeLogVo.getAmount() <= 0) {
                return;
            }
            TransferBo transferBo = new TransferBo();
            AccountInfo to = new AccountInfo();
            AccountInfo from = new AccountInfo();
            to.setAccountId(branchId);
            to.setAccountType(AccountTradeTypeEnum.BRANCH);
            from.setAccountId(studentId);
            from.setAccountType(AccountTradeTypeEnum.STUDENT);
            OrderInfo orderInfo = new OrderInfo();
            List<OrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollectionUtils.isEmpty(orderProductInfoList)) {
                return;
            }
            orderInfo.setOrderId(order.getOrderId());
            orderInfo.setAmount(accountTradeLogVo.getAmount());
            orderInfo.setOrderTypeEnum(OrderTypeEnum.STUDENT);
            transferBo.setFromAccountInfo(from);
            transferBo.setToAccountInfo(to);
            transferBo.setOrderInfo(orderInfo);
            accountService.doTransfer(transferBo);
        } else if (getOrderRefundStatus(order)) {
            List<AccountTradeLogVo> accountTradeLogVos = getBranchCountTradeLogByOrder(order);
            if (ObjectUtil.isEmpty(accountTradeLogVos)) {
                return;
            }
            int backDaysByRefund = setBranchBackDaysByRefund(order, accountTradeLogVos);
            if (backDaysByRefund <= 0) {
                return;
            }
            //获取当前订单状态：待退款
            TransferBo transferBo = new TransferBo();
            AccountInfo to = new AccountInfo();
            AccountInfo from = new AccountInfo();
            to.setAccountId(branchId);
            to.setAccountType(AccountTradeTypeEnum.BRANCH);
            from.setAccountId(studentId);
            from.setAccountType(AccountTradeTypeEnum.STUDENT);
            OrderInfo orderInfo = new OrderInfo();
            List<OrderProductInfoVo> orderProductInfoList = order.getOrderProductInfoList();
            if (CollectionUtils.isEmpty(orderProductInfoList)) {
                return;
            }
            orderInfo.setOrderId(order.getOrderId());
            orderInfo.setAmount(backDaysByRefund);
            orderInfo.setOrderTypeEnum(OrderTypeEnum.STUDENT);
            transferBo.setFromAccountInfo(from);
            transferBo.setToAccountInfo(to);
            transferBo.setOrderInfo(orderInfo);
            accountService.doTransfer(transferBo);
        } else {
            throwException("请稍后再试");
        }


    }

    /**
     * 获取门店退回天数
     * @param order
     * @param accountTradeLogVos
     * @return
     */
    private static int setBranchBackDaysByRefund(OrderVo order, List<AccountTradeLogVo> accountTradeLogVos) {
        if (CollectionUtils.isEmpty(accountTradeLogVos)) {
            return 0;
        }
        AccountTradeLogVo accountTradeLogVo = accountTradeLogVos.stream().findFirst().orElse(null);
        Integer accountTradeLogAmount;
        if (accountTradeLogVo == null || null == (accountTradeLogAmount = accountTradeLogVo.getAmount())
            || accountTradeLogAmount <= 0) {
            return 0;
        }
        int remainingDays = Math.toIntExact(order.getRemainingDays());
        return remainingDays > accountTradeLogAmount ? accountTradeLogAmount : remainingDays;
    }

    private List<AccountTradeLogVo> getBranchCountTradeLogByOrder(OrderVo order) {
        AccountTradeLogBo accountTradeLogBo = new AccountTradeLogBo();
        accountTradeLogBo.setOrderId(order.getOrderId());
        //支出
        accountTradeLogBo.setTradeType(0);
        //交易主体 门店
        accountTradeLogBo.setAccountType(AccountTradeTypeEnum.BRANCH.getCode());
        List<AccountTradeLogVo> accountTradeLogVos = accountTradeLogService.queryList(accountTradeLogBo);
        log.info("【订单取消】账户交易明细对象：{}", accountTradeLogVos);
        return accountTradeLogVos;
    }

    /**
     * 获取扣除时长
     * @param order
     * @param remoteStudentBo
     * @param orderProductInfo
     * @return
     */
    private static Long getDeductSum(OrderVo order, RemoteStudentVo remoteStudentBo, OrderProductInfoVo orderProductInfo) {
        ProcessProductAccountDays accountDays = ProcessProductAccountDays
            .of(orderProductInfo.getProductValidDays(),
                orderProductInfo.getProductValidTimeLimit(),
                order.getCourseStartTime(),
                remoteStudentBo.getBranch().getRemainTime().longValue())
            .calculateDeductTime();
        return accountDays.getDeductTime();
    }

    /**
     * 待付款
     * @param order
     * @return
     */
    private static boolean getOrderPayStatus(OrderVo order) {
        return OrderStatusEnum.WAIT_PAY.getCode().equals(order.getOrderStatus());
    }

    /**
     * 已取消
     */
    private static boolean getOrderCancelStatus(OrderVo order) {
        return OrderStatusEnum.CANCEL.getCode().equals(order.getOrderStatus());
    }

    /**
     * 待退款
     */
    private boolean getOrderRefundStatus(OrderVo order) {
        return OrderStatusEnum.PAYED.getCode().equals(order.getOrderStatus())
            || OrderStatusEnum.REFUNDING.getCode().equals(order.getOrderStatus())
            || OrderStatusEnum.REFUNDED.getCode().equals(order.getOrderStatus());
    }

    @Override
    public Boolean support(OrderTypeEnum orderTypeEnum) {
        return OrderTypeEnum.STUDENT.equals(orderTypeEnum);
    }


    private void throwException(String msg) {
        throw new ServiceException(msg);
    }
}
