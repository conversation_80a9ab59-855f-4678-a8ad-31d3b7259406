package com.jxw.shufang.order.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.OrderProductInfo;

import java.math.BigDecimal;

/**
 * 订单产品详情业务对象 order_product_info
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderProductInfo.class, reverseConvertGenerate = false)
public class OrderProductInfoBo extends BaseEntity {

    /**
     * 订单详情id
     */
    @NotNull(message = "订单详情id不能为空", groups = { EditGroup.class })
    private Long orderInfoId;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 会员类型id
     */
    @NotNull(message = "会员类型id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentTypeId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 产品有效天数
     */
    @NotNull(message = "产品有效天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    @NotBlank(message = "产品有效期（时间段，用 至 隔开）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    @NotNull(message = "产品价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal productPrice;

    /**
     * 会员优惠额度（自动扣减，用户无法编辑）
     */
    private BigDecimal studentPreferentialAmount;

    /**
     * 优惠价格（门店直减）
     */
    @NotNull(message = "优惠价格（门店直减）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal preferentialPrice;
}
