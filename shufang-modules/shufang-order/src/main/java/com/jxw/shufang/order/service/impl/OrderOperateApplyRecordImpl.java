package com.jxw.shufang.order.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.OrderOperateApplyRecord;
import com.jxw.shufang.order.domain.bo.OrderOperateApplyRecordBo;
import com.jxw.shufang.order.domain.vo.OrderOperateApplyRecordVo;
import com.jxw.shufang.order.mapper.OrderOperateApplyRecordMapper;
import com.jxw.shufang.order.service.IOrderOperateApplyRecordService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/3/17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrderOperateApplyRecordImpl implements IOrderOperateApplyRecordService, BaseService {

    private final OrderOperateApplyRecordMapper baseMapper;

    @Override
    public boolean insertByBo(OrderOperateApplyRecordBo orderOperateApplyRecordBo) {
        return baseMapper.insert(MapstructUtils.convert(orderOperateApplyRecordBo, OrderOperateApplyRecord.class)) > 0;
    }

    @Override
    public List<OrderOperateApplyRecordVo> listByOperateIds(List<Long> orderOperateIdList) {
        LambdaQueryWrapper<OrderOperateApplyRecord> wrapper = Wrappers.lambdaQuery();
        wrapper.in(OrderOperateApplyRecord::getOrderOperateId, orderOperateIdList)
            .orderByDesc(OrderOperateApplyRecord::getApplyId);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public OrderOperateApplyRecordVo getOrderOperateApplyRecord(Long orderOperateId, Integer reviewStatus) {
        LambdaQueryWrapper<OrderOperateApplyRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OrderOperateApplyRecord::getOrderOperateId, orderOperateId)
            .eq(OrderOperateApplyRecord::getReviewStatus, reviewStatus).orderByDesc(OrderOperateApplyRecord::getApplyId)
            .last("limit 1");
        return baseMapper.selectVoOne(queryWrapper);
    }
}
