package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchConfigService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchPayModelConfigVo;
import com.jxw.shufang.common.core.constant.CacheConstants;
import com.jxw.shufang.common.core.constant.MqTagConstant;
import com.jxw.shufang.common.core.constant.MqTopicConstant;
import com.jxw.shufang.common.core.constant.NoticeMessageConstants;
import com.jxw.shufang.common.core.enums.*;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.pay.client.PayCommonClient;
import com.jxw.shufang.common.pay.domain.dto.notify.PayNotifyDTO;
import com.jxw.shufang.common.pay.domain.dto.notify.RefundNotifyDTO;
import com.jxw.shufang.common.pay.domain.dto.request.CallBackConfigDTO;
import com.jxw.shufang.common.pay.domain.dto.request.RefundRequestDTO;
import com.jxw.shufang.common.pay.domain.dto.response.ApiResp;
import com.jxw.shufang.common.pay.domain.dto.response.PayStatusDTO;
import com.jxw.shufang.common.pay.domain.dto.response.RefundResponseDTO;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.crm.api.RemoteCrmStudentService;
import com.jxw.shufang.crm.api.domain.vo.RemoteCrmStudentBo;
import com.jxw.shufang.order.domain.*;
import com.jxw.shufang.order.domain.bo.*;
import com.jxw.shufang.order.domain.dto.*;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.enums.*;
import com.jxw.shufang.order.interfaces.PaymentCollectionFactory;
import com.jxw.shufang.order.mapper.OrderOperateMapper;
import com.jxw.shufang.order.remote.WxService;
import com.jxw.shufang.order.service.*;
import com.jxw.shufang.student.api.RemoteStudentExpireService;
import com.jxw.shufang.student.api.RemoteStudentIntroduceService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.RemoteStudentTypeService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentSimpleVO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import com.jxw.shufang.system.api.model.RoleDTO;
import io.seata.common.util.CollectionUtils;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * 订单操作记录
 * （时间逆序取最后一条和订单中的对应）Service业务层处理
 *
 * @date 2024-02-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrderOperateServiceImpl implements IOrderOperateService, BaseService {

    private final OrderOperateMapper baseMapper;

    private final IOrderProductInfoService orderProductInfoService;

    private final IOrderService orderService;
    private final IStudentMembershipCardService studentMembershipCardService;

    private final IOrderOperateApplyRecordService orderOperateApplyRecordService;

    @DubboReference
    private RemoteStudentExpireService remoteStudentExpireService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteStudentIntroduceService remoteStudentIntroduceService;
    private final PayCommonClient payCommonClient;

    private final RocketMQTemplate rocketMQTemplate;
    @DubboReference
    private RemoteCrmStudentService remoteCrmStudentService;
    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;
    @DubboReference
    private final RemoteBranchConfigService remoteBranchConfigService;

    private final IOrderAccountService iOrderAccountService;


    @Resource
    private PaymentCollectionFactory paymentCollectionFactory;
    @Resource
    private OrderPayRecordDao orderPayRecordDao;
    @Resource
    private OrderPayRecordService orderPayRecordService;
    @Resource
    private WxService wxService;

    @DubboReference
    private final RemoteBranchService remoteBranchService;


    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @Override
    public OrderOperateVo queryById(Long orderOperateId) {
        return baseMapper.selectVoById(orderOperateId);
    }

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    @Override
    public TableDataInfo<OrderOperateVo> queryPageList(OrderOperateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderOperate> lqw = buildQueryWrapper(bo);
        Page<OrderOperateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    @Override
    public List<OrderOperateVo> queryList(OrderOperateBo bo) {
        LambdaQueryWrapper<OrderOperate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderOperate> buildQueryWrapper(OrderOperateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderOperate> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrderOperate::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderOperateStatus()), OrderOperate::getOrderOperateStatus, bo.getOrderOperateStatus());
        lqw.eq(bo.getPaymentAmount() != null, OrderOperate::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(bo.getRefundAmount() != null, OrderOperate::getRefundAmount, bo.getRefundAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderOperateRemark()), OrderOperate::getOrderOperateRemark, bo.getOrderOperateRemark());
        return lqw;
    }

    /**
     * 新增订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean insertByBo(OrderOperateBo bo) {
        OrderOperate add = MapstructUtils.convert(bo, OrderOperate.class);
        validEntityBeforeSave(add);
        add.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderOperateId(add.getOrderOperateId());
        }
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(
            new OrderOperateApplyRecordBo(
                null,
                add.getOrderId(),
                add.getOrderOperateId(),
                OrderOperateReviewStatus.WAIT_REVIEW.getCode(), null,
                add.getOrderOperateRemark(), bo.getOrderOperateStatus(), null)
        );
        if(!insertApplyRecord){
            throw new ServiceException("退款申请失败");
        }
        return flag;
    }

    /**
     * 修改订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean updateByBo(OrderOperateBo bo) {
        OrderOperate update = MapstructUtils.convert(bo, OrderOperate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderOperate entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long initOrderOperate(Long orderId) {
        OrderOperate orderOperate = new OrderOperate();
        orderOperate.setOrderId(orderId);
        //默认为待支付
        orderOperate.setOrderOperateStatus(OrderStatusEnum.WAIT_PAY.getCode());
        orderOperate.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
        int insert = baseMapper.insert(orderOperate);
        if (insert <= 0) {
            throw new ServiceException("初始化订单操作记录失败");
        }
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(
            new OrderOperateApplyRecordBo(
                null,
                orderId,
                orderOperate.getOrderOperateId(),
                OrderOperateReviewStatus.REVIEWED.getCode(), null,
                orderOperate.getOrderOperateRemark(), orderOperate.getOrderOperateStatus(), null)
        );
        if(!insertApplyRecord){
            throw new ServiceException("退款申请失败");
        }
        return orderOperate.getOrderOperateId();
    }

    /**
     * 收款
     *
     * @param bo bo
     * @date 2024/03/03 03:50:09
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"'order_collection_key:' + #bo.orderId"}, acquireTimeout = 0)
    public OrderCollectionVo collection(OrderCollectionBo bo) {
        Order order = this.validateOrder(bo);
        PayModeEnum payModeEnum = this.validatePayMode(bo, order);

        // 获取收款信息
        OrderCollectionDTO orderCollectionDTO = paymentCollectionFactory
            .executePayment(payModeEnum)
            .collection(bo,order.getOrderOperateId());

        // 线下支付处理
        if (PayModeEnum.OFFLINE == payModeEnum) {
            OrderOperateBo orderOperate = new OrderOperateBo();
            orderOperate.setOrderId(order.getOrderId());
            orderOperate.setPaymentAmount(orderCollectionDTO.getAmount());
            SpringUtils.getAopProxy(this).payOrderByBo(orderOperate, false, null, null, order.getOrderId());
        }

        // 会员收款接入交易记录
        iOrderAccountService.getProcessor(bo.getOrderId(), OrderTypeEnum.STUDENT);

        return OrderCollectionVo.of(order.getOrderId(), orderCollectionDTO.getOrderPayNo());
    }

    private void setDefaultPayMode(OrderCollectionBo bo) {
        if (null == bo.getInstallmentFlag()) {
            bo.setInstallmentFlag(false);
        }
        if (null == bo.getDepositAmountFlag()) {
            bo.setDepositAmountFlag(false);
        }
        if (null == bo.getOnlinePayFlag()) {
            bo.setOnlinePayFlag(false);
        }
        if (null == bo.getPeerPayFlag()) {
            bo.setPeerPayFlag(false);
        }
    }

    private PayModeEnum validatePayMode(OrderCollectionBo bo, Order order) {
        PayModeEnum payModeEnum = bo.getOnlinePayFlag() ? PayModeEnum.ONLINE : PayModeEnum.OFFLINE;
        Set<PayModeEnum> payModeEnums = this.queryOnlinePayTypeByOrderId(order.getOrderId());
        if (!payModeEnums.contains(payModeEnum)) {
            throw new ServiceException("当前订单不支持该支付方式");
        }
        return payModeEnum;
    }

    private Order validateOrder(OrderCollectionBo bo) {
        this.setDefaultPayMode(bo);
        Pair<Order, OrderOperate> orderInfo = getOrderInfo(bo.getOrderId(), OrderStatusEnum.WAIT_PAY,OrderStatusEnum.PENDING_PAY);
        Order order = orderInfo.getKey();
        if(null == order){
            throw new ServiceException("订单不存在");
        }

        OrderOperate presentOrderOperate = orderInfo.getValue();
        boolean orderAblePay = OrderStatusEnum.WAIT_PAY.getCode().equals(presentOrderOperate.getOrderOperateStatus())
            || OrderStatusEnum.PENDING_PAY.getCode().equals(presentOrderOperate.getOrderOperateStatus());
        if (!orderAblePay) {
            throw new ServiceException("当前订单状态非待支付，无法收款");
        }
        if(null != order.getInstallmentPayDeadlineTime() && new Date().after(order.getInstallmentPayDeadlineTime())){
            throw new ServiceException("当前订单已过支付截止时间,请走退款");
        }
        return order;
    }


    @Override
    @Lock4j(keys = {"'order_pay_lock:' + #lockOrderId"})
    @GlobalTransactional(rollbackFor = Exception.class)
    public void payOrderByBo(OrderOperateBo bo, boolean compareAmount, Long operator, Long operateDept,
        Long lockOrderId) {
        bo.setOrderOperateId(null);
        if (compareAmount && null == bo.getPaymentAmount()) {
            throw new ServiceException("订单金额不为空");
        }
        if (bo.getOrderId() == null) {
            throw new ServiceException("订单id不能为空");
        }
        Pair<Order, OrderOperate> orderInfo = getOrderInfo(bo.getOrderId(), OrderStatusEnum.WAIT_PAY,OrderStatusEnum.PENDING_PAY);
        Order order = orderInfo.getKey();

        OrderOperate orderOperate = MapstructUtils.convert(bo, OrderOperate.class);

        // 计算订单实际支付金额
        BigDecimal actualAmount = orderProductInfoService.calculateOrderActualPayAmountByOrder(order, order.getOrderRelationId());

        if (compareAmount && actualAmount.compareTo(bo.getPaymentAmount()) != 0) {
            throw new ServiceException("金额不匹配");
        }

        orderOperate.setPaymentAmount(actualAmount);
        // 订单状态改为已支付
        orderOperate.setOrderOperateStatus(OrderStatusEnum.PAYED.getCode());
        orderOperate.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
        orderOperate.setCreateBy(operator);
        orderOperate.setUpdateBy(operator);
        orderOperate.setCreateDept(operateDept);
        orderOperate.setUpdateTime(new Date());
        // 新建一个操作记录，状态为已支付
        int insert = baseMapper.insert(orderOperate);
        if (insert <= 0 || orderOperate.getOrderOperateId() == null) {
            throw new ServiceException("收款失败");
        }
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(new OrderOperateApplyRecordBo(null,
            orderOperate.getOrderId(), orderOperate.getOrderOperateId(), OrderOperateReviewStatus.REVIEWED.getCode(),
            orderOperate.getOrderOperateRemark(), orderOperate.getOrderOperateStatus(), operator, operateDept));
        if (!insertApplyRecord) {
            throw new ServiceException("退款申请失败");
        }

        OrderProductInfoBo orderProductInfoBo = new OrderProductInfoBo();
        orderProductInfoBo.setOrderId(order.getOrderId());
        List<OrderProductInfoVo> orderProductInfoVos = orderProductInfoService.queryList(orderProductInfoBo);
        List<RemoteProductBo> remoteProductBos = new ArrayList<>();
        // 判断当前订单购买的体验卡还是正式卡，只要有不是体验卡的产品就当作正式卡
        Long experienceStudentTypeId = remoteStudentTypeService.getExperienceStudentTypeId();
        AtomicBoolean isOfficialCard = new AtomicBoolean(false);
        orderProductInfoVos.forEach(info -> {
            if (!experienceStudentTypeId.equals(info.getStudentTypeId())) {
                isOfficialCard.set(true);
            }
            RemoteProductBo remoteProductBo = new RemoteProductBo();
            remoteProductBo.setProductId(info.getProductId());
            remoteProductBo.setProductName(info.getProductName());
            remoteProductBo.setStudentTypeId(info.getStudentTypeId());
            remoteProductBos.add(remoteProductBo);
        });

        // 更新订单关联的操作记录id
        OrderBo orderBo = new OrderBo();
        orderBo.setOrderId(bo.getOrderId());
        orderBo.setOrderOperateId(orderOperate.getOrderOperateId());
        orderBo.setUpdateBy(operator);

        // 更新 purchasedCardFlag
        if (isOfficialCard.get()) {
            updatePurchasedCardFlag(order, orderBo);
        }

        Boolean b = orderService.updateByBo(orderBo);
        if (!b) {
            throw new ServiceException("收款失败");
        }
        remoteStudentIntroduceService.remoteDealWithOrderSuccess(order.getStudentId(), order.getOrderId(),
            remoteProductBos, operator, operateDept);
        // 将会员移除过期列表
        remoteStudentExpireService.removeStudentExpireRecord(order.getStudentId());
        this.updateStudentVipInfo(bo.getOrderId(), operator, operateDept);
        RemoteCrmStudentBo crmStudentBo = new RemoteCrmStudentBo();
        crmStudentBo.setMainStudentId(order.getStudentId());
        crmStudentBo.setExperience(!isOfficialCard.get());
        crmStudentBo.setExperienceStudentTypeId(experienceStudentTypeId);
        crmStudentBo.setOperator(operator);
        crmStudentBo.setOperateDept(operateDept);
        rocketMQTemplate.convertAndSend(MqTopicConstant.CRM_PAY_NOTIFY_TOPIC + ":" + MqTagConstant.CRM_PAY_NOTIFY_TAG,
            crmStudentBo);
    }

    /**
     * 更新订单 会员的购卡标识
     *
     * @param order
     * @param updateBo
     */
    public void updatePurchasedCardFlag(Order order, OrderBo updateBo) {
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(order.getStudentId());
        PurchasedCardFlag flagByCode = PurchasedCardFlag.getFlagByCode(remoteStudentVo.getPurchasedCardFlag());
        if (PurchasedCardFlag.NON.equals(flagByCode)) {
            updateBo.setPurchasedCardFlag(PurchasedCardFlag.NEW.getCode());
            remoteStudentService.updateBuyCardFlag(order.getStudentId(), PurchasedCardFlag.NEW.getCode());
        } else {
            updateBo.setPurchasedCardFlag(PurchasedCardFlag.OLD.getCode());
            if (PurchasedCardFlag.NEW.equals(flagByCode)) {
                remoteStudentService.updateBuyCardFlag(order.getStudentId(), PurchasedCardFlag.OLD.getCode());
            }
        }
    }

    public void collectionMemberCard(Long orderId, Long operator, Long operateDept) {
        OrderVo orderVo = orderService.queryById(orderId,false);
        StudentMembershipCardBo studentMembershipCardBo = this.getStudentMembershipCardBo(orderVo);

        // 检查当前订单是否存在会员卡
        StudentMembershipCardBo membershipCardBo = StudentMembershipCardBo.builder().orderId(orderId).build();
        List<StudentMembershipCardVo> membershipCardList = studentMembershipCardService.queryList(membershipCardBo);
        if(CollectionUtil.isEmpty(membershipCardList)){
            studentMembershipCardBo.setCreateBy(operator);
            studentMembershipCardBo.setCreateDept(operateDept);
            studentMembershipCardService.insertByBo(studentMembershipCardBo);
        }else {
            // 分期订单做会员卡更新
            StudentMembershipCardVo updateBo = membershipCardList.get(0);
            updateBo.setProductBeginDate(studentMembershipCardBo.getProductBeginDate());
            updateBo.setProductEndDate(studentMembershipCardBo.getProductEndDate());
            studentMembershipCardService.updateBatchById(Collections.singletonList(updateBo));
        }
    }

    @Override
    public StudentMembershipCardBo getStudentMembershipCardBo(OrderVo orderVo) {
        Optional<OrderOperateVo> currentOperateOptional = this.getCurrentOperateOptional(orderVo);
        if (currentOperateOptional.isEmpty()) {
            return null;
        }
        OrderOperateVo currentOperate = currentOperateOptional.get();
        String orderOperateStatus = currentOperate.getOrderOperateStatus();
        if (OrderStatusEnum.WAIT_PAY.getCode().equals(orderOperateStatus)
            || OrderStatusEnum.CANCEL.getCode().equals(orderOperateStatus)) {
            return null;
        }
        Optional<OrderOperateVo> optionalOrderOperateVo = getPayedOperate(orderVo);
        if (optionalOrderOperateVo.isEmpty()) {
            return null;
        }

        List<OrderProductInfoVo> orderProductInfoList = orderVo.getOrderProductInfoList();
        if (CollUtil.isEmpty(orderProductInfoList)) {
            return null;
        }
        OrderProductInfoVo orderProductInfoVo = orderProductInfoList.get(0);

        LocalDateTime createTime = optionalOrderOperateVo
            .map(OrderOperateVo::getCreateTime)
            .map(DateUtils::dateToLocalDateTime)
            .orElseThrow(() -> new ServiceException("创建时间不存在"));

        // 计算会员卡开始时间和结束时间
        CalculateVipCardTimeRangeContext cardTimeRangeContext = CalculateVipCardTimeRangeContext
            .of(orderVo.getCourseStartTime(),
                orderVo.getInstallmentPayDeadlineTime(),
                orderProductInfoVo,
                OrderStatusEnum.getByCode(orderOperateStatus))
            .calculate(createTime);

        return StudentMembershipCardBo.builder()
            .studentId(orderVo.getStudentId())
            .productId(orderProductInfoVo.getProductId())
            .orderId(orderVo.getOrderId())
            .cardStatus(OrderStatusEnum.REFUNDED.getCode().equals(orderOperateStatus) ? 0 : 1)
            .studentTypeId(orderProductInfoVo.getStudentTypeId())
            .productBeginDate(cardTimeRangeContext.getVipStartTime())
            .productEndDate(cardTimeRangeContext.getVipEndTime())
            .build();
    }

    private Optional<OrderOperateVo> getCurrentOperateOptional(OrderVo orderVo) {
        if (CollUtil.isEmpty(orderVo.getOrderOperateList())){
            return Optional.ofNullable(orderVo.getOrderOperate());
        }
        return orderVo.getOrderOperateList().stream().filter(entity ->
            entity.getOrderOperateId().equals(orderVo.getOrderOperateId())).findFirst();
    }

    private  Optional<OrderOperateVo> getPayedOperate(OrderVo orderVo) {
        List<OrderOperateVo> orderOperateList = orderVo.getOrderOperateList();
        if (CollUtil.isEmpty(orderOperateList)) {
            OrderOperateVo orderOperate = orderVo.getOrderOperate();
            if (orderOperate == null) {
                return Optional.empty();
            }
            boolean existPayOrder = orderOperate.getOrderOperateStatus().equals(OrderStatusEnum.PAYED.getCode())
                || orderOperate.getOrderOperateStatus().equals(OrderStatusEnum.PENDING_PAY.getCode());
            if (existPayOrder) {
                return Optional.of(orderOperate);
            } else {
                return Optional.empty();
            }
        }
        return orderOperateList
            .stream()
            .filter(entity -> entity.getOrderOperateStatus().equals(OrderStatusEnum.PAYED.getCode())
                || entity.getOrderOperateStatus().equals(OrderStatusEnum.PENDING_PAY.getCode()))
            .findFirst();
    }

    @Override
    public void refund(OrderOperateBo bo) {
        OrderVo orderVo = orderService.queryById(bo.getOrderId(), false);
        if (orderVo == null || orderVo.getOrderOperateId() == null) {
            throw new ServiceException("订单不存在");
        }
        OrderOperateVo presentOrderOperate = orderVo.getOrderOperate();
        // 开启退费审核且订单未完成审核，提示异常
        if (!OrderStatusEnum.REFUNDING.getCode().equals(presentOrderOperate.getOrderOperateStatus())
            || !OrderOperateReviewStatus.REVIEWED.getCode().equals(presentOrderOperate.getReviewStatus())) {
            throw new ServiceException("退款未审核通过，不能操作退款。");
        }

        if (!checkOperatePermission(bo.getOrderId(), bo.getOrderOperateId(), orderVo, true)) {
            throw new ServiceException("退款无权限");
        }

        SpringUtils.getAopProxy(this).refundByOrderOperateBo(bo, null, null, orderVo.getOrderId());

    }

    @Override
    public Pair<Boolean, String> checkSupportRefund(Integer payMode, OrderVo orderVo,
        List<OrderPayRecord> orderPayRecords) {
        if (CollUtil.isEmpty(orderPayRecords)) {
            return Pair.of(false, "订单异常");
        }
        boolean isFirst = true;
        Date now = new Date();
        Boolean isOnlinePay = null;
        ListIterator<OrderPayRecord> orderPayRecordListIterator = orderPayRecords.listIterator();
        while (orderPayRecordListIterator.hasNext()) {
            OrderPayRecord orderPayRecord = orderPayRecordListIterator.next();
            if (isOnlinePay == null) {
                isOnlinePay = Boolean.TRUE.equals(orderVo.getOnlinePayFlag());
            }
            if (isFirst) {
                isFirst = false;
                if (!orderPayRecordListIterator.hasNext() && orderPayRecord.getDepositAmountFlag() == 1) {
                    // 只有一个订单且是定金，不支持线下退款
                    return Pair.of(false, "定金不支持退款");
                }
            }
            if (PayModeEnum.ONLINE.getModeCode().equals(payMode) && (orderPayRecord.getPaymentTime() == null
                || !DateUtil.isSameDay(now, orderPayRecord.getPaymentTime()))) {
                return Pair.of(false, "线上支付已超过可退款时间，请切换退款方式。");
            }
        }
        return Pair.of(true, null);
    }

    private void onlineRefund(OrderVo orderVo, OrderOperateVo presentOrderOperate, OrderOperateVo paymentOperate,
        List<OrderPayRecord> paidRecords) {
        log.info("退款开始: 订单ID={}, 金额={}", orderVo.getOrderId(), paymentOperate.getRefundAmount());

        List<PayOrderStatusEnum> orderPayTypeEnums = List.of(PayOrderStatusEnum.PAYED);
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(orderVo.getOrderId(), orderPayTypeEnums);
        if (CollectionUtil.isEmpty(orderPayRecords)){
            throw new ServiceException("当前无已支付的订单可退");
        }

        BigDecimal refundAmount = paymentOperate.getRefundAmount();
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退款金额不能小于0");
        }

        // 降序排序
        List<RefundRequestDTO> result = this.chooseRefundPayOrder(orderVo, paidRecords, refundAmount);
        if (result.isEmpty()) {
            throw new ServiceException("当前无已支付的订单可退");
        }
        this.executeRefund(result, presentOrderOperate);
    }

    private List<RefundRequestDTO> chooseRefundPayOrder(OrderVo orderVo,
                                                        List<OrderPayRecord> orderPayRecords,
                                                        BigDecimal refundAmount) {
        orderPayRecords.sort((a, b) -> b.getAmount().compareTo(a.getAmount()));

        String payAppId = remoteStudentService.getCommonPayAppId(orderVo.getStudentId());
        if (StringUtils.isEmpty(payAppId)) {
            throw new ServiceException("未配置支付应用ID");
        }
        String orderId = orderVo.getOrderId().toString();

        List<RefundRequestDTO> result = new ArrayList<>();
        for (OrderPayRecord record : orderPayRecords) {
            if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal recordAmount = record.getAmount();
            if (recordAmount.compareTo(refundAmount) <= 0) {
                // 当前支付单金额 ≤ 剩余退款额 → 全退
                result.add(this.buildRefundRequestDTO(orderId, record.getTransactionNo(), recordAmount, payAppId));
                refundAmount = refundAmount.subtract(recordAmount);
            } else {
                // 当前支付单金额 > 剩余退款额 → 部分退
                result.add(this.buildRefundRequestDTO(orderId, record.getTransactionNo(), refundAmount, payAppId));
                refundAmount = BigDecimal.ZERO;
                break;
            }
        }
        return result;
    }

    private RefundRequestDTO buildRefundRequestDTO(String orderId,
                                                   String payOrderId,
                                                   BigDecimal refundAmount,
                                                   String payAppId) {
        RefundRequestDTO refundRequestDTO = new RefundRequestDTO();
        refundRequestDTO.setRefundOrderId(orderId);
        refundRequestDTO.setPayOrderId(payOrderId);
        refundRequestDTO.setAppId(payAppId);
        refundRequestDTO.setRefundAmount(refundAmount.multiply(BigDecimal.valueOf(100)));
        this.setMqConfig(refundRequestDTO);
        refundRequestDTO.setExtParam(JSONUtil.toJsonStr(refundRequestDTO.getCallbackConfig()));
        return refundRequestDTO;
    }

    public void executeRefund(List<RefundRequestDTO> refundRequestList, OrderOperateVo presentOrderOperate) {
        if (CollUtil.isEmpty(refundRequestList)) {
            return;
        }
        refundRequestList.forEach(refundRequestDTO -> {
            ApiResp<RefundResponseDTO> refundResponseDTOApiResp = payCommonClient.refundOrder(refundRequestDTO);
            if (!refundResponseDTOApiResp.isSuccess()) {
                throw new ServiceException("退款失败");
            }
        });
    }

    private void setMqConfig(RefundRequestDTO refundRequestDTO) {
        CallBackConfigDTO callBackConfigDTO = new CallBackConfigDTO("1", PayTradeNotifyTopicConstant.REFUND_ORDER_NOTIFY_TOPIC,
            PayTradeNotifyTopicConstant.ORDER_REFUND_NOTIFY_TAG, null);
        refundRequestDTO.setCallbackConfig(callBackConfigDTO);
    }

    @Override
    @Lock4j(keys = {"'order_refund_lock:' + #lockRefundOrderId"})
    @GlobalTransactional(rollbackFor = Exception.class)
    public void refundByOrderOperateBo(OrderOperateBo bo, Long operator, Long operateDept, Long lockRefundOrderId) {
        Long orderId = bo.getOrderId();
        OrderVo orderVo = orderService.queryById(bo.getOrderId(), false);
        OrderOperateVo oldOrderOperate = orderVo.getOrderOperate();
        // 检验是否审核通过，审核通过后调整
        if (!OrderStatusEnum.REFUNDING.getCode().equals(oldOrderOperate.getOrderOperateStatus())) {
            throw new ServiceException("订单状态异常");
        }

        OrderOperateBo updateBo = new OrderOperateBo();
        updateBo.setOrderOperateId(oldOrderOperate.getOrderOperateId());
        updateBo.setOrderOperateStatus(OrderStatusEnum.REFUNDED.getCode());
        updateBo.setUpdateBy(operator);
        updateByBo(updateBo);
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(new OrderOperateApplyRecordBo(null,
            orderId, oldOrderOperate.getOrderOperateId(), OrderOperateReviewStatus.WAIT_REVIEW.getCode(),
            bo.getOrderOperateRemark(), OrderStatusEnum.REFUNDED.getCode(), operator, operateDept));
        if (!insertApplyRecord) {
            throw new ServiceException("退款申请失败");
        }
        if (!remoteStudentIntroduceService.remoteDealWithOrderRefund(orderVo.getStudentId(), orderId)) {
            throw new ServiceException("优惠额度退还操作失败");
        }
        SpringUtils.getAopProxy(this).refundedMemberCard(bo.getOrderId(), operator);
        SpringUtils.getAopProxy(this).updateStudentExpireTime(bo.getOrderId());
        // 退还代理商天数
        iOrderAccountService.getProcessor(bo.getOrderId(), OrderTypeEnum.STUDENT);
    }

    public void refundedMemberCard(Long orderId, Long operator) {
        OrderVo orderVo = orderService.queryById(orderId,false);
        OrderOperateVo orderOperate = orderVo.getOrderOperate();
        List<StudentMembershipCardVo> studentMembershipCardVos = studentMembershipCardService.queryList(StudentMembershipCardBo.builder().orderId(orderId).build());
        if (CollUtil.isEmpty(studentMembershipCardVos)){
            return;
        }
        studentMembershipCardVos.forEach(studentMembershipCardVo -> {
            //判断结束时间是否大于当前时间
            if (studentMembershipCardVo.getProductEndDate().after(new Date())){
                studentMembershipCardVo.setProductEndDate(orderOperate.getCreateTime());
            }
            studentMembershipCardVo.setCardStatus(StudentCardStatusEnum.EXPIRED.getCode());
            studentMembershipCardVo.setUpdateBy(operator);
        });
        studentMembershipCardService.updateBatchById(studentMembershipCardVos);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void cancel(OrderOperateBo bo) {
        log.info("【取消订单】订单号：{}", bo.getOrderId());
        //新建一个操作记录，状态为已取消
        OrderOperate orderOperate = MapstructUtils.convert(bo, OrderOperate.class);
        orderOperate.setOrderOperateStatus(OrderStatusEnum.CANCEL.getCode());
        orderOperate.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
        int insert = baseMapper.insert(orderOperate);
        if (insert <= 0 || orderOperate.getOrderOperateId() == null) {
            throw new ServiceException("取消失败");
        }
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(
            new OrderOperateApplyRecordBo(
                null,
                orderOperate.getOrderId(),
                orderOperate.getOrderOperateId(),
                OrderOperateReviewStatus.REVIEWED.getCode(), null,
                orderOperate.getOrderOperateRemark(), orderOperate.getOrderOperateStatus(), null)
        );
        if(!insertApplyRecord){
            throw new ServiceException("取消失败");
        }
        //更新订单关联的操作记录id
        OrderBo orderBo = new OrderBo();
        orderBo.setOrderId(bo.getOrderId());
        orderBo.setOrderOperateId(orderOperate.getOrderOperateId());
        Boolean b = orderService.updateByBo(orderBo);
        if (!b) {
            throw new ServiceException("取消失败");
        }
        // 取消支付单
        this.cancelPayOrder(bo);

        //返还门店时间
//        this.returnBranchRemainTime(bo.getOrderId());
        iOrderAccountService.getProcessor(bo.getOrderId(), OrderTypeEnum.STUDENT);
    }

    /**
     * 订单取消返还门店时间
     * @param orderId
     */
    private void returnBranchRemainTime(Long orderId) {

        //查询订单信息
        OrderVo orderVo = orderService.queryById(orderId);
        if (ObjectUtil.isNull(orderVo)) {
            throw new ServiceException("未查询到订单信息，订单取消失败！");
        }

        //查询订单详情信息
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.queryProductByOrder(orderId);
        log.info("【取消订单】订单号：{} 订单详情：{}", orderId, orderProductInfos);
        if (ObjectUtil.isEmpty(orderProductInfos)) {
            throw new ServiceException("未查询到订单详情信息，订单取消失败！");
        }

        //根据订单查询对应的门店信息
        Long branchId = Optional.ofNullable(orderVo.getStudentId())
                .map(remoteStudentService::queryStudentById)
                .map(RemoteStudentSimpleVO::getBranchId)
                .orElseThrow(() -> new ServiceException("未查询到订单对应的门店信息，订单取消失败！"));

        //计算订单天数
        int amountTime = orderProductInfos.stream()
                .map(remoteProductVo -> {
                    if (ObjectUtil.isNotNull(remoteProductVo.getProductValidDays())) {
                        return remoteProductVo.getProductValidDays();
                    } else if (ObjectUtil.isNotEmpty(remoteProductVo.getProductValidTimeLimit())){
                        String[] timeRanges = remoteProductVo.getProductValidTimeLimit().split(" 至 ");
                        if (ObjectUtil.isNotEmpty(timeRanges) && timeRanges.length == 2) {
                            return DateUtil.between(DateUtil.parseDateTime(timeRanges[0]), DateUtil.parseDateTime(timeRanges[1]), DateUnit.DAY);
                        }
                    }
                    return 0L;
                })
                .mapToInt(Long::intValue).sum();

        //扣除门店有效天数
        log.info("【取消订单】返还门店有效时长，门店ID：{}，返还时长：{}", branchId, amountTime);
        Boolean flag = remoteBranchService.transfer(branchId, amountTime);
        log.info("【取消订单】返还门店时长结果：{}", flag);
        if (!flag) {
            //门店有效天数不足，不足以抵扣订单天数，下单失败
            throw new ServiceException("返还门店时长失败！");
        }
    }

    private void cancelPayOrder(OrderOperateBo bo) {
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(bo.getOrderId(), PayOrderStatusEnum.getAllStatus());
        List<OrderPayRecord> waitPayRecord = orderPayRecords.stream()
            .filter(f -> PayOrderStatusEnum.ableCancelStatus().contains(f.getPaymentStatus()))
            .toList();
        List<OrderPayRecord> payRecord = orderPayRecords.stream()
            .filter(f -> !PayOrderStatusEnum.ableCancelStatus().contains(f.getPaymentStatus()))
            .toList();
        if (CollectionUtil.isNotEmpty(payRecord)) {
            throw new ServiceException("禁止非待支付的支付单的订单取消");
        }
        if (CollectionUtil.isEmpty(waitPayRecord)) {
            return;
        }
        waitPayRecord.forEach(f -> {
            CancelPayOrderBo cancelPayOrderBo = new CancelPayOrderBo();
            cancelPayOrderBo.setOrderPayNo(f.getOrderPayNo());
            orderPayRecordService.cancelPayOrder(cancelPayOrderBo, null);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundApply(OrderOperateBo bo) {
        Long orderId = bo.getOrderId();
        OrderVo orderVo = orderService.queryById(orderId,false);
        if (orderVo == null||orderVo.getOrderOperateId() == null) {
            throw new ServiceException("订单不存在");
        }
        OrderOperateVo oldOrderOperate = orderVo.getOrderOperate();
        boolean operateRejected = OrderStatusEnum.REFUNDING.getCode().equals(oldOrderOperate.getOrderOperateStatus())
            && OrderOperateReviewStatus.REJECT.getCode().equals(oldOrderOperate.getReviewStatus());
        // 只有已支付或者退款申请被拒绝
        if (!OrderStatusEnum.PAYED.getCode().equals(oldOrderOperate.getOrderOperateStatus())
            && !OrderStatusEnum.PENDING_PAY.getCode().equals(oldOrderOperate.getOrderOperateStatus())
            && !operateRejected) {
            throw new ServiceException("订单状态不允许申请退款");
        }
        validRefundOrder(bo, orderVo);
        Long orderOperateId;
        if (operateRejected) {
            orderOperateId = oldOrderOperate.getOrderOperateId();
            OrderOperateBo updateBo = new OrderOperateBo();
            updateBo.setOrderOperateId(orderOperateId);
            updateBo.setRefundAccount(bo.getRefundAccount());
            updateBo.setRefundBankName(bo.getRefundBankName());
            updateBo.setRefundAccountName(bo.getRefundAccountName());
            updateBo.setPayMode(bo.getPayMode());
            updateBo.setReviewStatus(OrderOperateReviewStatus.WAIT_REVIEW.getCode());
            updateBo.setRefundAmount(bo.getRefundAmount());
            updateBo.setReviewNode(RefundAuditNode.PLATFORM_ADMINISTRATOR_AUDIT.getCode());
            updateBo.setOrderOperateRemark(bo.getOrderOperateRemark());
            updateByBo(updateBo);
        } else {
            // 新建一个操作记录，状态为退款中
            OrderOperate orderOperate = MapstructUtils.convert(bo, OrderOperate.class);
            orderOperate.setPaymentAmount(oldOrderOperate.getPaymentAmount());
            orderOperate.setPaymentType(oldOrderOperate.getPaymentType());
            orderOperate.setOrderOperateStatus(OrderStatusEnum.REFUNDING.getCode());
            orderOperate.setReviewStatus(OrderOperateReviewStatus.WAIT_REVIEW.getCode());
            orderOperate.setReviewNode(RefundAuditNode.PLATFORM_ADMINISTRATOR_AUDIT.getCode());
            int insert = baseMapper.insert(orderOperate);
            if (insert <= 0 || orderOperate.getOrderOperateId() == null) {
                throw new ServiceException("退款申请失败");
            }
            orderOperateId = orderOperate.getOrderOperateId();
        }
        boolean insertApplyRecord = orderOperateApplyRecordService.insertByBo(
            new OrderOperateApplyRecordBo(
                null,
                orderId,
                orderOperateId,
                OrderOperateReviewStatus.WAIT_REVIEW.getCode(),
                RefundAuditNode.PLATFORM_ADMINISTRATOR_AUDIT.getCode(),
                bo.getOrderOperateRemark(), OrderStatusEnum.REFUNDING.getCode(), null)
        );
        if(!insertApplyRecord){
            throw new ServiceException("退款申请失败");
        }
        if (!operateRejected) {
            // 更新订单关联的操作记录id
            OrderBo orderBo = new OrderBo();
            orderBo.setOrderId(bo.getOrderId());
            orderBo.setOrderOperateId(orderOperateId);
            Boolean b = orderService.updateByBo(orderBo);
            if (!b) {
                throw new ServiceException("退款申请失败");
            }
        }
    }

    /**
     * 退款申请时检查订单状态
     *
     * @param bo
     * @param orderVo
     */
    private void validRefundOrder(OrderOperateBo bo, OrderVo orderVo) {
        OrderOperateVo oldOrderOperate = orderVo.getOrderOperate();
        if (oldOrderOperate == null) {
            throw new ServiceException("订单操作记录不存在");
        }
        Assert.notNull(bo.getPayMode(), "退款方式不为空");
        Assert.notNull(bo.getRefundAmount(), "退款金额不能为空");
        // 查相关的产品信息
        BigDecimal canRefundAmount;
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(orderVo.getOrderId(),
            Collections.singletonList(PayOrderStatusEnum.PAYED));
        if (CollUtil.isEmpty(orderPayRecords)) {
            // 老订单，取支付成功的订单操作支付金额作为订单总金额
            OrderOperateBo queryPaidOperateBo = new OrderOperateBo();
            queryPaidOperateBo.setOrderId(orderVo.getOrderId());
            queryPaidOperateBo.setOrderOperateStatus(PayStatusDTO.PayStatusEnum.PAID.getStatus().toString());
            List<OrderOperateVo> orderOperateVos = queryList(queryPaidOperateBo);
            if (CollUtil.isEmpty(orderOperateVos)) {
                canRefundAmount = BigDecimal.ZERO;
            } else {
                canRefundAmount = orderOperateVos.get(0).getPaymentAmount();
            }
        } else {
            canRefundAmount = orderPayRecords.stream().map(OrderPayRecord::getAmount).filter(Objects::nonNull)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        if (canRefundAmount == null) {
            throw new ServiceException("订单产品信息不存在");
        }
        if (bo.getRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("退款金额不小于0");
        }
        // 退款金额不能大于订单实际支付金额
        if (bo.getRefundAmount().compareTo(canRefundAmount) > 0) {
            throw new ServiceException("退款金额不能大于订单实际支付金额（产品总价-优惠金额）");
        }

        if (CollUtil.isEmpty(orderPayRecords)) {
            if (orderVo.getPaymentOperate() != null) {
                OrderPayRecord orderPayRecord = new OrderPayRecord();
                orderPayRecord.setDepositAmountFlag(0);
                orderPayRecord.setPaymentTime(orderVo.getPaymentOperate().getUpdateTime());
                orderPayRecords.add(orderPayRecord);
            }
        }
        Pair<Boolean, String> checkSupportRefundInfo = checkSupportRefund(bo.getPayMode(), orderVo, orderPayRecords);
        if (!checkSupportRefundInfo.getKey()) {
            throw new ServiceException("操作失败，" + checkSupportRefundInfo.getValue());
        }
    }

    @Override
    public void refundReview(OrderOperateApplyRecord bo) {
        RLock lock = RedisUtils.getClient().getLock(CacheConstants.REFUND_APPLY_LOCK_KEY + bo.getOrderId());
        try {
            if (lock.tryLock(30, 120, TimeUnit.SECONDS)) {
                SpringUtils.getAopProxy(this).innerRefundReview(bo);
            }
        } catch (Exception e) {
            log.error("退费审核异常,cacheKey:{}", CacheConstants.REFUND_APPLY_LOCK_KEY + bo.getOrderId(), e);
            throw new ServiceException(e.getMessage());
        } finally {
            if (null != lock) {
                lock.unlock();
            }
        }
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    public void innerRefundReview(OrderOperateApplyRecord bo) {
        OrderVo refundingOrder = getRefundingOrder(bo.getOrderId(), bo.getOrderOperateId());
        OrderOperateVo orderOperate = refundingOrder.getOrderOperate();
        if (!(OrderStatusEnum.REFUNDING.getCode().equals(orderOperate.getOrderOperateStatus())
            && OrderOperateReviewStatus.WAIT_REVIEW.getCode().equals(orderOperate.getReviewStatus()))) {
            throw new ServiceException("订单审核状态异常");
        }
        // 获取当前退款节点
        RefundAuditNode refundAuditNode = RefundAuditNode.fromCode(orderOperate.getReviewNode());
        boolean reviewReject = false;
        if (OrderOperateReviewStatus.REVIEWED.getCode().equals(bo.getReviewStatus())
            || (reviewReject = OrderOperateReviewStatus.REJECT.getCode().equals(bo.getReviewStatus()))) {
            // 检查当前用户是否有权限审核该节点
            if (!refundAuditNode
                .checkRole(LoginHelper.getRoles().stream().map(RoleDTO::getRoleId).collect(Collectors.toList()))) {
                throw new ServiceException("当前用户不可以进行审核。");
            }
            PayModeEnum payMode = PayModeEnum.fromCode(orderOperate.getPayMode());
            RefundAuditNode.AuditPolicy policy = refundAuditNode.getPolicy(payMode);
            if (reviewReject ? policy.isRejectNeedVoucher() : policy.isPassNeedVoucher()) {
                if (bo.getVoucher() == null) {
                    throw new ServiceException("请上传" + policy.getVoucherName());
                }
            }
            OrderOperateBo updateBo = new OrderOperateBo();
            updateBo.setOrderOperateId(orderOperate.getOrderOperateId());
            // 当审核失败或（审核成功且当前是最后一个审核节点）时才更新退款订单的审核状态
            if (reviewReject || null == refundAuditNode.getNextNode()) {
                if (!reviewReject && PayModeEnum.ONLINE.getModeCode().equals(orderOperate.getPayMode())) {
                    //审核通过，且最后一个节点，当前流程是公司财务确认，进行退款动作（线上支付情况下）
                    List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(
                        refundingOrder.getOrderId(), Collections.singletonList(PayOrderStatusEnum.PAYED));
                    if (CollUtil.isEmpty(orderPayRecords)) {
                        throw new ServiceException("当前无已支付的订单可退");
                    }
                    Pair<Boolean, String> checkSupportRefundInfo =
                        checkSupportRefund(PayModeEnum.ONLINE.getModeCode(), refundingOrder, orderPayRecords);
                    if (!checkSupportRefundInfo.getKey()) {
                        throw new ServiceException("操作失败，" + checkSupportRefundInfo.getValue());
                    }
                    List<OrderOperateVo> orderOperateVos = this.getOperateVoList(refundingOrder);
                    this.onlineRefund(refundingOrder, orderOperate, IteratorUtils.first(orderOperateVos.listIterator()),
                        orderPayRecords);
                }
                updateBo.setReviewStatus(bo.getReviewStatus());
            } else {
                // 当前流程审核通过，设置下一个审核节点
                updateBo.setReviewNode(refundAuditNode.getNextAuditCode());
            }
            updateByBo(updateBo);
        } else {
            throw new ServiceException("审核状态异常");
        }
        orderOperateApplyRecordService
            .insertByBo(new OrderOperateApplyRecordBo(null, orderOperate.getOrderId(), orderOperate.getOrderOperateId(),
                bo.getReviewStatus(), refundAuditNode.getCode(), bo.getRemark(),
                OrderStatusEnum.REFUNDING.getCode(), bo.getVoucher(), bo.getVoucherName()));
    }

    private List<OrderOperateVo> getOperateVoList(OrderVo refundingOrder) {
        OrderOperateBo paymentOperateBo = new OrderOperateBo();
        paymentOperateBo.setOrderId(refundingOrder.getOrderId());
        paymentOperateBo.setOrderOperateStatus(OrderStatusEnum.REFUNDING.getCode());
        List<OrderOperateVo> orderOperateVos = queryList(paymentOperateBo);
        return orderOperateVos;
    }

    private void onlineRefund(OrderVo refundingOrder, OrderOperateVo orderOperate) {

    }

    /**
     * 获取退款中的订单
     *
     * @param orderId
     * @param orderOperateId
     * @return
     */
    private OrderVo getRefundingOrder(Long orderId, Long orderOperateId) {
        OrderVo orderVo = orderService.queryById(orderId, false);
        if (null == orderVo || null == orderVo.getOrderOperateId()
            || !orderVo.getOrderOperateId().equals(orderOperateId)) {
            throw new ServiceException("订单信息异常");
        }
        OrderOperateVo orderOperate = orderVo.getOrderOperate();
        if (!OrderStatusEnum.REFUNDING.getCode().equals(orderOperate.getOrderOperateStatus())) {
            throw new ServiceException("订单状态异常");
        }
        return orderVo;
    }

    /**
     * 根据订单，及当前订单操作信息
     *
     * @param orderId
     * @return
     */
    @Override
    public Pair<Order, OrderOperate> getOrderInfo(Long orderId, OrderStatusEnum... expectStatusList) {
        Order order = orderService.queryOrderById(orderId);
        if (ObjectUtils.isEmpty(order)) {
            throw new ServiceException("订单为空");
        }
        OrderOperate presentOrderOperate = baseMapper.selectById(order.getOrderOperateId());
        if (null == presentOrderOperate) {
            throw new ServiceException("订单信息异常");
        }
        boolean statusMatch = false;
        for (OrderStatusEnum status : expectStatusList) {
            if (status.getCode().equals(presentOrderOperate.getOrderOperateStatus())) {
                statusMatch = true;
            }
        }
        if (!statusMatch) {
            throw new ServiceException("当前订单状态异常");
        }
        return Pair.of(order, presentOrderOperate);
    }

    @Override
    public RefundingOrderPermissionVo getOperatePermissionVo(OrderOperateApplyRecord bo) {
        return new RefundingOrderPermissionVo(
            checkOperatePermission(bo.getOrderId(), bo.getOrderOperateId(), null, true));
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void processPayNotify(PayNotifyDTO notifyDTO) {
        OrderOnlinePaySuccessContext orderOnlinePaySuccessContext = this.buildOrderOnlinePaySuccessContext(notifyDTO);
        if (null == orderOnlinePaySuccessContext) {
            return;
        }
        Order order = orderOnlinePaySuccessContext.getOrder();
        OrderOperateVo orderOperate = orderOnlinePaySuccessContext.getOrderOperate();

        if (AbnormalPayEnum.ABNORMAL_PAY.getCode().equals(notifyDTO.getAbnormalPayFlag())) {
            this.updatePayOrder(notifyDTO, AbnormalPayEnum.ABNORMAL_PAY);
            return;
        } else {
            this.updatePayOrder(notifyDTO, AbnormalPayEnum.NORMAL_PAY);
        }

        List<PayOrderStatusEnum> payOrderStatusEnums = Arrays.asList(PayOrderStatusEnum.WAIT_PAY, PayOrderStatusEnum.PAYED);
        List<OrderPayRecord> existOrderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(order.getOrderId(), payOrderStatusEnums);
        if (CollectionUtil.isEmpty(existOrderPayRecords)) {
            throw new ServiceException("不存在有效的支付单");
        }

        // 分期支付处理
        this.installmentPayProcess(order, orderOperate, notifyDTO);
        boolean finalInstallmentOrder = isOrderComplete(existOrderPayRecords, order);
        if (!finalInstallmentOrder) {
            return;
        }

        // 支付后处理
        this.afterPayProcess(notifyDTO, order, orderOperate, existOrderPayRecords);

        try {
            notifyMessage(order.getOrderId(), order.getOrderNo(), order.getStudentId(), true);
        } catch (Exception e) {
            log.error("发送站内信通知失败:{}", e.getMessage());
        }
    }

    private void installmentPayProcess(Order order, OrderOperateVo orderOperate,PayNotifyDTO notifyDTO) {
        if (!order.getInstallmentFlag()) {
            return;
        }
        this.paySuccessOrderOperateProcess(notifyDTO, order, orderOperate);
        remoteStudentExpireService.removeStudentExpireRecord(order.getStudentId());
        this.updateStudentVipInfo(order.getOrderId(), orderOperate.getCreateBy(), orderOperate.getCreateDept());
    }

    private void afterPayProcess(PayNotifyDTO notifyDTO,
                                 Order order,
                                 OrderOperateVo orderOperate,
                                 List<OrderPayRecord> existOrderPayRecords) {
        OrderOperateBo payOrderOperateBo = new OrderOperateBo();
        payOrderOperateBo.setOrderId(order.getOrderId());
        String payMethod = TradeOrderTypeMethodName.getByCode(notifyDTO.getPayMethod().toString()).getPayMethodNameEnum().getCode();
        payOrderOperateBo.setPaymentType(payMethod);
        payOrderOperateBo.setPayMode(PayModeEnum.ONLINE.getModeCode());
        payOrderOperateBo.setPaymentAmount(existOrderPayRecords.stream().map(OrderPayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        SpringUtils.getAopProxy(this).payOrderByBo(payOrderOperateBo, true, orderOperate.getUpdateBy(),
            orderOperate.getCreateDept(), order.getOrderId());
    }

    private OrderOnlinePaySuccessContext buildOrderOnlinePaySuccessContext(PayNotifyDTO notifyDTO) {
        OrderOnlinePaySuccessContext context = new OrderOnlinePaySuccessContext();
        if (StringUtils.isEmpty(notifyDTO.getBizOrderId()) || StringUtils.isEmpty(notifyDTO.getPayOrderId())) {
            throw new ServiceException("订单id或支付订单id不能为空");
        }
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(notifyDTO.getBizOrderId());
        if (null == orderPayRecord) {
            throw new ServiceException("无效的订单支付信息");
        }
        Order order = orderService.queryOrderById(orderPayRecord.getOrderId());
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }

        OrderOperateVo orderOperate = this.queryById(order.getOrderOperateId());
        if (null == orderOperate) {
            throw new ServiceException("无效的订单操作信息");
        }

        // 支付訂單可能已经被其他渠道支付，直接返回
        if (PayOrderStatusEnum.PAYED.getCode().equals(orderPayRecord.getPaymentStatus())) {
            return null;
        }
        context.setOrderOperate(orderOperate);
        context.setOrderPayRecord(orderPayRecord);
        context.setOrder(order);
        return context;
    }

    private void updateStudentVipInfo(Long orderId,Long operator,Long operateDept) {

        // 更新会员卡信息
        SpringUtils.getAopProxy(this).collectionMemberCard(orderId, operator, operateDept);
        // 更新过期时间
        SpringUtils.getAopProxy(this).updateStudentExpireTime(orderId);
    }


    private void paySuccessOrderOperateProcess(PayNotifyDTO notifyDTO,
                                               Order order,
                                               OrderOperateVo orderOperate) {

        OrderOperateBo orderOperateBo = new OrderOperateBo();
        orderOperateBo.setOrderId(order.getOrderId());
        orderOperateBo.setOrderOperateStatus(OrderStatusEnum.PENDING_PAY.getCode());
        List<OrderOperateVo> orderOperateVos = queryList(orderOperateBo);
        if (CollectionUtil.isNotEmpty(orderOperateVos)) {
            return;
        }
        OrderOperate insertOperate = new OrderOperate();
        insertOperate.setOrderId(order.getOrderId());
        insertOperate.setCreateDept(orderOperate.getCreateDept());
        insertOperate.setCreateBy(orderOperate.getCreateBy());
        insertOperate.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
        insertOperate.setCreateTime(new Date());
        insertOperate.setUpdateTime(new Date());
        insertOperate.setUpdateBy(orderOperate.getCreateBy());
        insertOperate.setPaymentAmount(notifyDTO.getTotalPayAmount());
        insertOperate.setOrderOperateStatus(OrderStatusEnum.PENDING_PAY.getCode());
        baseMapper.insert(insertOperate);

        OrderBo orderBo = new OrderBo();
        orderBo.setOrderId(order.getOrderId());
        orderBo.setOrderOperateId(insertOperate.getOrderOperateId());
        orderBo.setUpdateBy(orderOperate.getCreateBy());
        orderService.updateByBo(orderBo);
    }


    private void updatePayOrder(PayNotifyDTO notifyDTO, AbnormalPayEnum abnormalPayEnum) {
        OrderPayRecord updateOrderPayRecord = new OrderPayRecord();
        updateOrderPayRecord.setPaymentTime(new Date(notifyDTO.getPayTimeStamp()));
        updateOrderPayRecord.setAmount(notifyDTO.getTotalPayAmount());
        updateOrderPayRecord.setPaymentStatus(PayOrderStatusEnum.PAYED.getCode());
        updateOrderPayRecord.setChannelOrderId(notifyDTO.getChannelOrderId());
        if (AbnormalPayEnum.ABNORMAL_PAY.equals(abnormalPayEnum)) {
            updateOrderPayRecord.setAbnormalPayFlag(abnormalPayEnum.getCode());
            updateOrderPayRecord.setAbnormalPayDesc(AbnormalPayDescEnum.REPEAT_PAY.getDesc());
        }
        orderPayRecordDao.updateOrderPayRecordByPayNo(updateOrderPayRecord, notifyDTO.getBizOrderId());
    }

    private boolean isOrderComplete(List<OrderPayRecord> orderPayRecords,Order order) {
        BigDecimal totalProductAmount = orderPayRecords.stream().map(OrderPayRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        boolean payComplete = 0 == totalProductAmount.compareTo(order.getPayAbleAmount());
        boolean orderCount = orderPayRecords.size() == 3;
        return  payComplete || orderCount;
    }

    /**
     * 发送站内信
     *
     * @param orderId
     * @param orderNo
     * @param studentId
     * @param isPay 是否是支付，true 支付；false 退款
     */
    private void notifyMessage(Long orderId, String orderNo, Long studentId, boolean isPay) {
        // [userId,userName,orderNo,productName]
        NoticeBizTypeEnum type = isPay ? NoticeBizTypeEnum.ORDER_PAY : NoticeBizTypeEnum.ORDER_REFUND;
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(studentId);
        OrderProductInfoBo orderProductInfoBo = new OrderProductInfoBo();
        orderProductInfoBo.setOrderId(orderId);
        List<OrderProductInfoVo> orderProductInfoVos = orderProductInfoService.queryList(orderProductInfoBo);
        String productName;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", remoteStudentVo.getStudentId());
        paramMap.put("userName", remoteStudentVo.getStudentName());
        paramMap.put("orderNo", orderNo);
        paramMap.put("productName", (productName =
            CollectionUtil.isNotEmpty(orderProductInfoVos) ? orderProductInfoVos.get(0).getProductName() : "会员卡"));
        RemoteMessageDTO remoteMessage = RemoteMessageDTO.builder().templateCode(type.name()).bizType(type.getCode())
            .noticeType(NoticeTypeEnum.INTERNAL.getCode())
            .content(
                String.format("%s购买的%s已", remoteStudentVo.getStudentName(), productName) + (isPay ? "支付成功" : "退款成功"))
            .studentId(studentId).paramMap(paramMap).build();
        rocketMQTemplate.convertAndSend(NoticeMessageConstants.NOTICE_INTERNAL_TOPIC, remoteMessage);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void processRefundNotify(RefundNotifyDTO notifyDTO) {
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(notifyDTO.getBizOrderId());
        if(null == orderPayRecord){
            throw new ServiceException("支付单不存在，退款失败");
        }

        OrderVo orderVo = orderService.queryById(orderPayRecord.getOrderId(), false);
        if (orderVo == null || orderVo.getOrderOperateId() == null) {
            throw new ServiceException("订单不存在");
        }
        if (OrderStatusEnum.REFUNDED.getCode().equals(orderVo.getOrderOperate().getOrderOperateStatus())) {
            return;
        }

        OrderPayRecord updatePayRecord = new OrderPayRecord();
        updatePayRecord.setPaymentStatus(PayOrderStatusEnum.REFUNDED.getCode());
        orderPayRecordDao.updateOrderPayRecordByPayNo(updatePayRecord,notifyDTO.getBizOrderId());

        try {
            notifyMessage(orderVo.getOrderId(), orderVo.getOrderNo(), orderVo.getStudentId(), false);
        }catch (Exception e){
            log.error("退款成功回调发送通知失败:{}",e.getMessage());
        }
    }

    /**
     * 生成支付二维码
     *
     * @param bo
     * @return
     */
    public PaymentQrCodeVo generatePaymentQrCode(CreatePayQrCodeBo bo) {
        if (null == bo.getOrderId()) {
            throw new ServiceException("订单ID不能为空");
        }
        if (null == bo.getOrderPayNo()) {
            throw new ServiceException("支付单号不能为空");
        }

        GeneratePaymentQrcodeContext qrCodeContext = buildGeneratePaymentQrcodeContext(bo.getOrderId(), bo.getOrderPayNo());
        // 订单状态校验
        OrderPayPreValidator
            .of(qrCodeContext.getOrder(), qrCodeContext.getOrderPayRecord(), qrCodeContext.getOrderOperate())
            .validate();

        // 生成小程序url
        return createMiniProgramPayUrl(qrCodeContext);
    }

    private GeneratePaymentQrcodeContext buildGeneratePaymentQrcodeContext(Long orderId, String orderPayNo) {
        Pair<Order, OrderOperate> orderInfo = getOrderInfo(orderId, OrderStatusEnum.WAIT_PAY, OrderStatusEnum.PENDING_PAY);
        if (null == orderInfo || null == orderInfo.getKey() || null == orderInfo.getValue()) {
            throw new ServiceException("订单不存在");
        }
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(orderPayNo);
        if (null == orderPayRecord) {
            throw new ServiceException("支付单不存在");
        }
        if(!orderPayRecord.getOrderId().equals(orderId)){
            throw new ServiceException("支付单无效");
        }
        Order order = orderInfo.getKey();
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(order.getStudentId());
        if (null == remoteStudentVo) {
            throw new ServiceException("学生不存在");
        }

        return GeneratePaymentQrcodeContext.builder().order(order)
            .orderPayRecord(orderPayRecord)
            .orderOperate(orderInfo.getValue())
            .build();
    }

    private PaymentQrCodeVo createMiniProgramPayUrl(GeneratePaymentQrcodeContext qrcodeContext) {
        String payParamStr = String.format("orderId=%s&orderPayNo=%s",
            qrcodeContext.getOrder().getOrderId(),
            qrcodeContext.getOrderPayRecord().getOrderPayNo());
        String payPath = "#/pages/pay/index";
        return PaymentQrCodeVo.of(wxService.generatePayUrlByPath(payParamStr, payPath));
    }

    @Override
    public CheckOrderPayStatusVo checkOrderPay(Long orderId, String orderPayNo) {
        Order order = orderService.queryOrderById(orderId);
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }

        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(orderPayNo);
        if (null == orderPayRecord) {
            throw new ServiceException("无效的订单支付信息");
        }
        CheckOrderPayStatusVo vo = new CheckOrderPayStatusVo();
        vo.setOrderId(orderId);
        vo.setOrderPayNo(orderPayNo);
        vo.setPaymentStatus(orderPayRecord.getPaymentStatus());
        return vo;
    }

    @Override
    public Set<PayModeEnum> queryOnlinePayTypeByOrderId(Long orderId) {
        if (null == orderId) {
            throw new ServiceException("订单id不能为空");
        }
        Order order = orderService.queryOrderById(orderId);
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }

        RemoteStudentSimpleVO studentSimpleVO = remoteStudentService.queryStudentById(order.getStudentId());
        if (null == studentSimpleVO) {
            throw new ServiceException("无效的学生信息");
        }

        OrderProductInfoBo orderProductInfoBo = new OrderProductInfoBo();
        orderProductInfoBo.setOrderId(orderId);
        List<OrderProductInfoVo> orderProductInfoList = orderProductInfoService.queryList(orderProductInfoBo);

        return matchPayModeEnums(order, studentSimpleVO,orderProductInfoList);
    }

    private Set<PayModeEnum> matchPayModeEnums(Order order,
                                               RemoteStudentSimpleVO remoteStudentBo,
                                               List<OrderProductInfoVo> orderProductInfoList) {
        if (!isValidInput(order, remoteStudentBo, orderProductInfoList)) {
            return PayModeEnum.offLinePayType();
        }

        RemoteBranchPayModelConfigVo payModelConfig = remoteBranchConfigService.remoteGetPayModelConfig(remoteStudentBo.getBranchId());
        String payAppId = remoteStudentService.getCommonPayAppId(remoteStudentBo.getStudentId());

        return canUseOnlinePayment(payModelConfig, payAppId, orderProductInfoList.get(0), order)
            ? PayModeEnum.allPayType()
            : PayModeEnum.offLinePayType();
    }

    // 输入参数校验
    private boolean isValidInput(Order order, RemoteStudentSimpleVO student,
                                 List<OrderProductInfoVo> productList) {
        return order != null
            && student != null
            && student.getBranchId() != null
            && student.getStudentId() != null
            && productList != null
            && !productList.isEmpty();
    }

    // 在线支付条件判断
    private boolean canUseOnlinePayment(RemoteBranchPayModelConfigVo payModelConfig,
                                        String payAppId,
                                        OrderProductInfoVo productInfo,
                                        Order order) {
        Long orderOperateId = order.getOrderOperateId();
        OrderOperateVo orderOperateVo = this.queryById(orderOperateId);
        if (orderOperateVo == null){
            throw new ServiceException("订单操作记录不存在");
        }

        boolean noPayFlag = OrderStatusEnum.WAIT_PAY.getCode().equals(orderOperateVo.getOrderOperateStatus())
            || OrderStatusEnum.CANCEL.getCode().equals(orderOperateVo.getOrderOperateStatus());
        if (noPayFlag) {
            return payModelConfig != null
                && payModelConfig.existProductConfig(productInfo.getProductId())
                && StringUtils.isNotEmpty(payAppId)
                && order.getCourseStartTime() != null;
        }

        // 已经发生了支付的订单，限定只能使用第一次选择的支付方式
        return order.getOnlinePayFlag();
    }

    /**
     * 检查是否有操作权限
     *
     * @param orderId
     * @param orderOperateId
     * @param orderVo
     * @param onlyStaff
     * @return
     */
    private boolean checkOperatePermission(Long orderId, Long orderOperateId, OrderVo orderVo, Boolean onlyStaff) {
        return checkOperatePermission(orderVo, onlyStaff, () -> getRefundingOrder(orderId, orderOperateId));
    }

    /**
     * 检查是否有操作权限
     *
     * @param orderVo
     * @param onlyStaff
     * @param orderSupplier 用于提供 OrderVo 的函数式接口
     * @return
     */
    @Override
    public boolean checkOperatePermission(OrderVo orderVo, Boolean onlyStaff, Supplier<OrderVo> orderSupplier) {
        // 是否是店长
        if (LoginHelper.isBranchStaff() && LoginHelper.isExecutiveStoreManager()) {
            return true;
        }

        // 获取退款中的订单信息
        OrderVo refundingOrder = (orderVo != null) ? orderVo : orderSupplier.get();

        Long userId = LoginHelper.getLoginUser().getUserId();
        boolean canOperate = userId.equals(refundingOrder.getOrderOperate().getCreateBy());

        if (!canOperate && !LoginHelper.isBranchStaff()) {
            return !onlyStaff;
        }

        return canOperate;
    }

    @Override
    public List<OrderOperate> queryOperateOrderListByOrderIds(List<Long> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrderOperate> queryWrapper = Wrappers.lambdaQuery(OrderOperate.class)
            .in(OrderOperate::getOrderId, orderIds);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public Boolean queryOrderAbleInstallment(Long orderId) {
        Order order = orderService.queryOrderById(orderId);
        if (order == null) {
            throw new ServiceException("无效的订单信息");
        }
        List<OrderProductInfo> orderProductInfoList = orderProductInfoService.queryProductByOrder(orderId);
        if (CollectionUtils.isEmpty(orderProductInfoList)){
            throw new ServiceException("无效的订单产品信息");
        }
        String productValidTimeLimit = orderProductInfoList.get(0).getProductValidTimeLimit();
        if (StringUtils.isEmpty(productValidTimeLimit)){
            return true;
        }
        if (null == order.getInstallmentPayDeadlineTime()) {
            return false;
        }
        String productEndTimeStr = productValidTimeLimit.split("至")[1].trim();
        Date productEndTime = DateUtils.parseDate(productEndTimeStr);

        LocalDate payDeadlineTimeLocalDate = order.getInstallmentPayDeadlineTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate productEndTimeLocalDate = productEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 允许分期的条件：产品结束日期 > 补缴日期
        return productEndTimeLocalDate.isAfter(payDeadlineTimeLocalDate);
    }


    /**
     * 更新会员过期时间
     *
     * @param orderId 订单id
     * @date 2024/03/17 04:00:11
     */
    public void updateStudentExpireTime(Long orderId) {
        Order order = orderService.queryOrderById(orderId);

        Long studentId = order.getStudentId();
        Date maxExpireTime = this.getMaxExpireTime(studentId);
        remoteStudentService.updateStudentExpireTime(studentId, maxExpireTime);
    }

    private Date getMaxExpireTime(Long studentId) {
        // 获取所有有效的会员卡
        StudentMembershipCardBo membershipCardBo = StudentMembershipCardBo.builder()
            .studentId(studentId)
            .cardStatus(StudentCardStatusEnum.PAYER.getCode())
            .build();
        List<StudentMembershipCardVo> membershipCardVos = studentMembershipCardService.queryList(membershipCardBo);
        if (membershipCardVos.isEmpty()) {
            return null;
        }
        Optional<StudentMembershipCardVo> latestCard = membershipCardVos.stream()
            .filter(card -> null != card.getProductEndDate())
            .max(Comparator.comparing(StudentMembershipCardVo::getProductEndDate));
        return latestCard.map(StudentMembershipCardVo::getProductEndDate).orElse(null);
    }


    @CacheEvict(value = "orderOperate", allEntries = true)
    public void cleanCache() {
        log.info("===========orderOperateService cleanCache===========");
    }


    @Override
    public void init() {
        IOrderOperateService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
    }

}
