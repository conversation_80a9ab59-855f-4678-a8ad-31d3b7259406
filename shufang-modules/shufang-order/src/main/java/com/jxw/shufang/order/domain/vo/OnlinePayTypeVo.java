package com.jxw.shufang.order.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/12 16:43
 * @Version 1
 * @Description 订单支付方式
 */
@Data
public class OnlinePayTypeVo {
    /**
     * 支付方式 0-线下支付 1-线上支付
     */
    private Boolean code;

    /**
     * 支付方式名称 0-线下支付 1-线上支付
     */
    private String name;

    private OnlinePayTypeSupportOperate supportOperate;

    public OnlinePayTypeVo(Boolean code, String name, OnlinePayTypeSupportOperate operate) {
        this.code = code;
        this.name = name;
        if(!code){
            this.supportOperate =  new OnlinePayTypeSupportOperate(false);
        }else {
            this.supportOperate = operate;
        }
    }
}
