package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.order.domain.bo.AddOrderProductBo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.bo.OrderProductInfoBo;
import com.jxw.shufang.order.domain.dto.OrderCreationContext;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.mapper.OrderProductInfoMapper;
import com.jxw.shufang.order.service.IOrderProductInfoService;
import com.jxw.shufang.order.service.IOrderService;
import com.jxw.shufang.order.service.IStudentMembershipCardService;
import com.jxw.shufang.order.service.ProcessProductPrice;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.RemoteStudentTypeService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentTypeBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentTypeVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单产品详情Service业务层处理
 *
 * @date 2024-02-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class OrderProductInfoServiceImpl implements IOrderProductInfoService, BaseService {

    private final OrderProductInfoMapper baseMapper;

    private final IOrderService orderService;

    private final IStudentMembershipCardService studentMembershipCardService;

    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;

    /**
     * 查询订单产品详情
     */
    @Override
    public OrderProductInfoVo queryById(Long orderInfoId) {
        return baseMapper.selectVoById(orderInfoId);
    }

    /**
     * 查询订单产品详情列表
     */
    @Override
    public TableDataInfo<OrderProductInfoVo> queryPageList(OrderProductInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderProductInfo> lqw = buildQueryWrapper(bo);
        Page<OrderProductInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询订单产品详情列表
     */
    @Override
    public List<OrderProductInfoVo> queryList(OrderProductInfoBo bo) {
        LambdaQueryWrapper<OrderProductInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderProductInfo> buildQueryWrapper(OrderProductInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderProductInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, OrderProductInfo::getOrderId, bo.getOrderId());
        lqw.eq(bo.getProductId() != null, OrderProductInfo::getProductId, bo.getProductId());
        lqw.eq(bo.getStudentTypeId() != null, OrderProductInfo::getStudentTypeId, bo.getStudentTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), OrderProductInfo::getProductName, bo.getProductName());
        lqw.eq(bo.getProductValidDays() != null, OrderProductInfo::getProductValidDays, bo.getProductValidDays());
        lqw.eq(StringUtils.isNotBlank(bo.getProductValidTimeLimit()), OrderProductInfo::getProductValidTimeLimit, bo.getProductValidTimeLimit());
        lqw.eq(bo.getProductPrice() != null, OrderProductInfo::getProductPrice, bo.getProductPrice());
        lqw.eq(bo.getPreferentialPrice() != null, OrderProductInfo::getPreferentialPrice, bo.getPreferentialPrice());
        return lqw;
    }

    /**
     * 新增订单产品详情
     */
    @Override
    public Boolean insertByBo(OrderProductInfoBo bo) {
        OrderProductInfo add = MapstructUtils.convert(bo, OrderProductInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderInfoId(add.getOrderInfoId());
        }
        return flag;
    }

    /**
     * 新增订单产品详情
     */
    @Override
    public void insertBatchAndReturnActualAmount(List<AddOrderProductBo> orderProductInfoList,
                                                 OrderCreationContext orderCreationContext,
                                                 Long orderId, Long studentTypeId) {
        List<RemoteProductVo> remoteProductList = orderCreationContext.getRemoteProductList();
        Date courseStartTime = orderCreationContext.getCourseStartTime();

        //插入订单信息数据
        Map<Long, RemoteProductVo> productVoMap = StreamUtils.toMap(remoteProductList, RemoteProductVo::getProductId, Function.identity());
        List<OrderProductInfo> insertBeanList = new ArrayList<>();
        for (AddOrderProductBo orderProductInfoBo : orderProductInfoList) {
            RemoteProductVo remoteProductVo = productVoMap.get(orderProductInfoBo.getProductId());
            OrderProductInfo orderProductInfo = new OrderProductInfo();
            orderProductInfo.setOrderId(orderId);
            orderProductInfo.setStudentTypeId(studentTypeId);
            orderProductInfo.setPreferentialPrice(orderProductInfoBo.getPreferentialPrice());
            orderProductInfo.setProductId(remoteProductVo.getProductId());
            orderProductInfo.setProductName(remoteProductVo.getProductName());
            orderProductInfo.setProductValidDays(remoteProductVo.getProductValidDays());
            orderProductInfo.setProductValidTimeLimit(remoteProductVo.getProductValidTimeLimit());
            if (orderProductInfoBo.getStudentPreferentialAmount() != null) {
                if (orderProductInfoBo.getStudentPreferentialAmount().compareTo(remoteProductVo.getProductPrice()) > 0) {
                    orderProductInfoBo.setStudentPreferentialAmount(remoteProductVo.getProductPrice());
                }
                orderProductInfo.setStudentPreferentialPrice(orderProductInfoBo.getStudentPreferentialAmount());
            }
            this.processProductPrice(courseStartTime, remoteProductVo, orderProductInfo);

            insertBeanList.add(orderProductInfo);
        }
        //插入数据
        boolean b = baseMapper.insertBatch(insertBeanList);
        if (!b) {
            throw new ServiceException("插入订单商品信息失败");
        }
    }

    private void processProductPrice(Date courseStartTime, RemoteProductVo remoteProductVo, OrderProductInfo orderProductInfo) {
        ProcessProductPrice processProductPrice = ProcessProductPrice.of(remoteProductVo, orderProductInfo, courseStartTime).prepareOrderPrice();
        orderProductInfo.setProductPrice(processProductPrice.getProductPrice());
        orderProductInfo.setOriginProductPrice(processProductPrice.getOriginProductPrice());
        orderProductInfo.setSignupDiscountAmount(processProductPrice.getSignupDiscountAmount());
    }


    /**
     * 修改订单产品详情
     */
    @Override
    public Boolean updateByBo(OrderProductInfoBo bo) {
        OrderProductInfo update = MapstructUtils.convert(bo, OrderProductInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderProductInfo entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单产品详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public BigDecimal calOrderActualAmount(Long orderId) {
        Order order = orderService.queryOrderById(orderId);
        return calOrderActualAmount(order,getCalAmountProductList(orderId));
    }

    /**
     * 获取计算金额用途的订单商品列表
     *
     * @param orderId
     * @return
     */
    private List<OrderProductInfo> getCalAmountProductList(Long orderId) {
        LambdaQueryWrapper<OrderProductInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrderProductInfo::getOrderId, orderId);
        // 不走数据权限，避免总金额不准确
        List<OrderProductInfo> orderProductInfoList = DataPermissionHelper.ignore(() -> baseMapper.selectList(lqw));
        if (orderProductInfoList.isEmpty()) {
            throw new ServiceException("订单商品信息不存在");
        }
        return orderProductInfoList;
    }

    @Override
    public BigDecimal calculateOrderActualPayAmountByOrder(Order order, Long orderRelationId) {
        List<OrderProductInfo> productList = getCalAmountProductList(order.getOrderId());

        List<RemoteProductVo> queryProductList = this.getRemoteProductList(productList);
        ProcessProductPrice processProductPrice = ProcessProductPrice.of(queryProductList.get(0), productList.get(0), order.getCourseStartTime());

        BigDecimal productPrice = processProductPrice.prepareOrderPrice().getProductPrice();
        if (OrderTypeEnum.OLD_CARD_UPGRADE.getCode().equals(order.getOrderType()) && null != orderRelationId) {
            Order oldOrder = orderService.queryOrderById(orderRelationId);
            BigDecimal oldOrderActualPayAmount = calculateOrderActualPayAmountByOrder(oldOrder, null);
            if (null != oldOrderActualPayAmount) {
                productPrice = productPrice.subtract(oldOrderActualPayAmount);
            }
        }
        return productPrice;
    }

    private List<RemoteProductVo> getRemoteProductList(List<OrderProductInfo> productList) {
        List<Long> productIds = productList.stream().map(OrderProductInfo::getProductId).toList();

        RemoteProductBo remoteProductBo = new RemoteProductBo();
        remoteProductBo.setProductIdList(productIds);
        List<RemoteProductVo> queryProductList = remoteProductService.queryProductList(remoteProductBo, true);
        if (CollectionUtil.isEmpty(queryProductList) || productIds.size() != queryProductList.size()) {
            throw new ServiceException("产品信息不正确");
        }
        return queryProductList;
    }

    @Override
    public BigDecimal getOrderAblePayAmountByOrder(Long orderId, Long orderRelationId) {
        Order order = orderService.queryOrderById(orderId);
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }
        List<OrderProductInfo> orderProductInfoList = this.queryProductByOrder(order.getOrderId());
        if (CollectionUtil.isEmpty(orderProductInfoList)) {
            throw new ServiceException("无效的订单商品信息");
        }
        if (null != order.getCourseStartTime()) {
            return orderProductInfoList.stream().map(OrderProductInfo::getProductPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            // 旧订单兼容
            return orderProductInfoList.stream().map(productInfo -> {
                BigDecimal productPrice = productInfo.getProductPrice();
                BigDecimal studentPreferentialPrice = Optional.ofNullable(productInfo.getStudentPreferentialPrice()).orElse(BigDecimal.ZERO);
                BigDecimal preferentialPrice = Optional.ofNullable(productInfo.getPreferentialPrice()).orElse(BigDecimal.ZERO);
                return productPrice.subtract(preferentialPrice).subtract(studentPreferentialPrice);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }


    @Override
    public StudentProductVo getLatestValidMemberCard(Long studentId, Integer showNum) {
        OrderBo orderBo = new OrderBo();
        orderBo.setStudentId(studentId);
        List<String> orderStatus = Arrays.asList(OrderStatusEnum.PAYED.getCode(), OrderStatusEnum.REFUNDING.getCode(), OrderStatusEnum.PENDING_PAY.getCode());
        orderBo.setOrderStatusList(orderStatus);
        List<OrderVo> orderVos = orderService.selectOrderListAndInfo(orderBo);
        if (orderVos.isEmpty()) {
            StudentProductVo studentProductVo = new StudentProductVo();
            studentProductVo.setProductInfoList(Collections.emptyList());
            studentProductVo.setValidProductNum(0);
            studentProductVo.setTotalProductNum(0);
            return studentProductVo;
        }

        List<OrderProductInfoVo> validInfoList = new ArrayList<>();
        Date now = DateUtils.setStartOfDay(new Date());

        Set<Long> oldSet = getOldCardSet(orderVos);
        //按付款时间排序,最近的在最前面
        orderVos = orderVos.stream().filter(v->!oldSet.contains(v.getOrderId())).collect(Collectors.toList());
        orderVos.sort(Comparator.comparing(e -> ((OrderVo) e).getOrderOperate().getCreateTime()).reversed());

        int total = 0;

        // 查询所有的新订单
        Map<Long, StudentMembershipCardVo> orderMembershipCardVoMap = this.getOrderMembershipCardMap(orderVos);

        for (OrderVo orderVo : orderVos) {
            OrderOperateVo orderOperate = orderVo.getOrderOperate();
            if (orderOperate == null) {
                continue;
            }
            List<OrderProductInfoVo> orderProductInfoList = orderVo.getOrderProductInfoList();
            if (orderProductInfoList.isEmpty()) {
                continue;
            }
            Date payTime = getVipStartTime(orderVo, orderOperate);

            for (OrderProductInfoVo orderProductInfoVo : orderProductInfoList) {
                total++;
                Date date = null;
                Long productValidDays = orderProductInfoVo.getProductValidDays();
                if (productValidDays != null) {
                    date = DateUtils.addDays(payTime, productValidDays.intValue());
                } else {
                    String productValidTimeLimit = orderProductInfoVo.getProductValidTimeLimit();
                    if (StringUtils.isNotBlank(productValidTimeLimit)) {
                        String endTimeStr = productValidTimeLimit.split(" 至 ")[1];
                        date = DateUtils.parseDate(endTimeStr);
                    }
                }
                orderProductInfoVo.setProductStatus(orderOperate.getOrderOperateStatus());
                //有效期大于当前时间
                if (date != null && (date.after(now) || date.getTime() == now.getTime())) {
                    validInfoList.add(orderProductInfoVo);
                }
                // 设置会员卡有效日期
                StudentMembershipCardVo membershipCardVo = orderMembershipCardVoMap.get(orderVo.getOrderId());
                this.putValidTimeRangeByNewOrder(orderProductInfoVo, membershipCardVo);
            }
        }

        showNum = Math.min(showNum, validInfoList.size());
        List<OrderProductInfoVo> orderProductInfoVos = validInfoList.subList(0, showNum);

        StudentProductVo studentProductVo = new StudentProductVo();
        studentProductVo.setProductInfoList(orderProductInfoVos);
        studentProductVo.setValidProductNum(orderProductInfoVos.size());
        studentProductVo.setTotalProductNum(total);
        putStudentType(orderProductInfoVos);
        return studentProductVo;


    }

    private Map<Long, StudentMembershipCardVo> getOrderMembershipCardMap(List<OrderVo> orderVos) {
        List<Long> orderIds = orderVos.stream().map(OrderVo::getOrderId).toList();
        List<StudentMembershipCardVo> studentMemberships =  studentMembershipCardService.queryListByOrders(orderIds);
        return  studentMemberships.stream()
            .collect(Collectors.toMap(StudentMembershipCardVo::getOrderId, Function.identity(), (v1, v2) -> v2));
    }

    private void putValidTimeRangeByNewOrder(OrderProductInfoVo orderProductInfoVo, StudentMembershipCardVo studentMembershipCardVo) {
        if (studentMembershipCardVo == null) {
            return;
        }
        Date productBeginDate = studentMembershipCardVo.getProductBeginDate();
        Date productEndDate = studentMembershipCardVo.getProductEndDate();
        String endTimeStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, productEndDate);
        String payTimeStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, productBeginDate);
        orderProductInfoVo.setValidTimeRange(payTimeStr + " 至 " + endTimeStr);
    }

    private Date getVipStartTime(OrderVo orderVo, OrderOperateVo orderOperate) {
        Date payTime = orderOperate.getCreateTime();
        Date courseStartTime = orderVo.getCourseStartTime();
        return null != courseStartTime ? courseStartTime : payTime;
    }

    private Set<Long> getOldCardSet(List<OrderVo> orderVos) {
        if (CollectionUtils.isEmpty(orderVos)) {
            return Set.of();
        }
        return orderVos
            .stream()
            .filter(v -> OrderTypeEnum.OLD_CARD_UPGRADE.getCode().equals(v.getOrderType()))
            .map(OrderVo::getOrderRelationId)
            .collect(Collectors.toSet());
    }

    @Override
    public List<OrderProductInfoVo> getAllMemberCard(Long studentId) {
        OrderBo orderBo = new OrderBo();
        orderBo.setStudentId(studentId);
        orderBo.setOrderStatusList(List.of(OrderStatusEnum.PAYED.getCode(),OrderStatusEnum.PENDING_PAY.getCode()));
        List<OrderVo> orderVos = orderService.selectOrderListAndInfo(orderBo);
        if (orderVos.isEmpty()) {
            return Collections.emptyList();
        }

        //过滤已升级的旧卡
        Set<Long> oldSet = getOldCardSet(orderVos);
        orderVos = orderVos.stream().filter(v->!oldSet.contains(v.getOrderId())).collect(Collectors.toList());

        // 查询定的关联的所有会员卡
        Map<Long, StudentMembershipCardVo> orderMembershipCardMap = getOrderMembershipCardMap(orderVos);
        for (OrderVo orderVo : orderVos) {
            StudentMembershipCardVo membershipCardVo = orderMembershipCardMap.get(orderVo.getOrderId());
            if (null == membershipCardVo) {
                continue;
            }
            List<OrderProductInfoVo> orderProductInfoList = orderVo.getOrderProductInfoList();
            if (orderProductInfoList.isEmpty()) {
                continue;
            }
            for (OrderProductInfoVo orderProductInfoVo : orderProductInfoList) {
                putValidTimeRange(orderProductInfoVo, membershipCardVo);
            }
        }
        //按付款时间排序,最近的在最前面
        orderVos.sort(Comparator.comparing(e -> ((OrderVo) e).getOrderOperate().getCreateTime()).reversed());
        List<OrderProductInfoVo> orderProductInfoVos = orderVos.stream().map(OrderVo::getOrderProductInfoList).flatMap(Collection::stream).collect(Collectors.toList());
        if (orderProductInfoVos.isEmpty()) {
            return Collections.emptyList();
        }
        putStudentType(orderProductInfoVos);
        return orderProductInfoVos;
    }

    public void putStudentType(List<OrderProductInfoVo> orderProductInfoVos) {
        List<Long> studentTypeIdList = orderProductInfoVos.stream().map(OrderProductInfoVo::getStudentTypeId).collect(Collectors.toList());
        if (studentTypeIdList.isEmpty()) {
            return;
        }
        RemoteStudentTypeBo remoteProductBo = new RemoteStudentTypeBo();
        remoteProductBo.setStudentTypeIds(studentTypeIdList);
        List<RemoteStudentTypeVo> remoteStudentTypeVos = remoteStudentTypeService.queryStudentTypeList(remoteProductBo);
        Map<Long, RemoteStudentTypeVo> studentTypeVoMap = StreamUtils.toMap(remoteStudentTypeVos, RemoteStudentTypeVo::getStudentTypeId, Function.identity());
        for (OrderProductInfoVo orderProductInfoVo : orderProductInfoVos) {
            RemoteStudentTypeVo remoteStudentTypeVo = studentTypeVoMap.get(orderProductInfoVo.getStudentTypeId());
            if (remoteStudentTypeVo != null) {
                orderProductInfoVo.setStudentType(remoteStudentTypeVo);
            }else {
                RemoteStudentTypeVo studentProductVo = new RemoteStudentTypeVo();
                studentProductVo.setStudentTypeName("未知");
                orderProductInfoVo.setStudentType(studentProductVo);
            }
        }
    }

    public void putValidTimeRange(OrderProductInfoVo orderProductInfoVo, StudentMembershipCardVo studentMembershipCardVo) {
        if (studentMembershipCardVo == null) {
            return;
        }
        Date productBeginDate = studentMembershipCardVo.getProductBeginDate();
        Date productEndDate = studentMembershipCardVo.getProductEndDate();

        String productBeginDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, productBeginDate);
        String productEndDateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, productEndDate);
        orderProductInfoVo.setValidTimeRange(productBeginDateStr + " 至 " + productEndDateStr);

        orderProductInfoVo.setIsValid(DateUtils.isCurrentTimeBetweenDates(productBeginDate,productEndDate));
    }

    @CacheEvict(value = "orderProductInfo",allEntries= true)
    public void cleanCache(){
        log.info("===========orderProductInfoService cleanCache===========");
    }



    @Override
    public BigDecimal calOrderActualAmount(Order order,List<OrderProductInfo> orderProductInfoList) {
        return orderProductInfoList.stream()
            .map(productInfo->calOrderProductActualAmount(order,productInfo))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal calOrderProductActualAmount(Order order, OrderProductInfo orderProductInfo) {
        RemoteProductBo remoteProductBo = new RemoteProductBo();
        remoteProductBo.setProductId(orderProductInfo.getProductId());
        List<RemoteProductVo> remoteProductList = remoteProductService.queryProductList(remoteProductBo, false);
        return ProcessProductPrice.of(remoteProductList.get(0), orderProductInfo, order.getCourseStartTime())
            .prepareOrderPrice()
            .getProductPrice();
    }

    @Override
    public OrderProductInfo queryProductByOrderProductId(Long orderId, Long productId) {
        if (null == orderId || null == productId) {
            throw new ServiceException("参数错误");
        }
        LambdaQueryWrapper<OrderProductInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderProductInfo::getOrderId, orderId).eq(OrderProductInfo::getProductId, productId);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<OrderProductInfo> queryProductByOrder(Long orderId) {
        if (null == orderId) {
            throw new ServiceException("参数错误");
        }
        LambdaQueryWrapper<OrderProductInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderProductInfo::getOrderId, orderId);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public void init() {
        IOrderProductInfoService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========orderProductInfoService init===========");
        LambdaQueryWrapper<OrderProductInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(OrderProductInfo::getOrderInfoId);
        List<OrderProductInfo> list = DataPermissionHelper.ignore(()->baseMapper.selectList(wrapper));
        log.info("===========orderProductInfoService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
        });
        log.info("===========orderProductInfoService init end===========");
        OrderProductInfo orderProductInfo = new OrderProductInfo();
        orderProductInfo.setOrderInfoId(1L);
        orderProductInfo.setOrderId(1L);
        baseMapper.updateById(orderProductInfo);
    }

}
