package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.AccountTradeLog;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 账户流水 （扣款退费充值记录）业务对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AccountTradeLog.class, reverseConvertGenerate = false)
public class AccountTradeLogBo extends BaseEntity {

    /**
     * 账户流水id
     */
    @NotNull(message = "账户流水id不能为空", groups = { EditGroup.class })
    private Long accountTradeLogId;

    /**
     * 流水交易类型(0 支出 1 收入)
     */
    @NotNull(message = "流水交易类型(0 支出 1 收入)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer tradeType;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 订单类型(0充值订单,1会员订单)
     */
    @NotNull(message = "订单类型(0充值订单,1会员订单)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer orderType;

    /**
     * 主体交易账号
     */
    @NotNull(message = "主体交易账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long accountId;

    /**
     * 主体交易账号类型(0代理商，1门店，2学生)
     */
    @NotNull(message = "主体交易账号类型(0代理商，1门店，2学生)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer accountType;

    /**
     * 对应账号
     */
    @NotNull(message = "对应账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long replyAccount;

    /**
     * 对应账号类型(0代理商，1门店，2学生)
     */
    @NotNull(message = "对应账号类型(0代理商，1门店，2学生)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer replyAccountType;

    /**
     * 数量（对应支付类型0为xxx天 1为xxxx元）
     */
    @NotNull(message = "数量（xxx天 ）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer amount;





}
