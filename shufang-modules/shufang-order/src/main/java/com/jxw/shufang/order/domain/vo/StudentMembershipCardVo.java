package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.StudentMembershipCard;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 学生会员卡视图对象 student_membership_card
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentMembershipCard.class)
public class StudentMembershipCardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 学生产品表
     */
    @ExcelProperty(value = "学生产品表")
    private Long studentMembershipCardId;

    /**
     * 学生id
     */
    @ExcelProperty(value = "学生id")
    private Long studentId;

    /**
     * 产品id
     */
    @ExcelProperty(value = "产品id")
    private Long productId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 会员卡类型
     */
    @ExcelProperty(value = "会员卡类型")
    private Long studentTypeId;

    /**
     * 会员卡类型(0失效--退费 1启用--付款)
     */
    @ExcelProperty(value = "会员卡类型(0失效--退费 1启用--付款)")
    private Integer cardStatus;

    /**
     * 产品开始时间
     */
    @ExcelProperty(value = "产品开始时间")
    private Date productBeginDate;

    /**
     * 产品结束时间
     */
    @ExcelProperty(value = "产品结束时间")
    private Date productEndDate;
    /**
     * 更新者
     */
    private Long updateBy;


}
