package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.StudentMembershipCard;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 学生会员卡业务对象 student_membership_card
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@AutoMapper(target = StudentMembershipCard.class,reverseConvertGenerate = false)
public class StudentMembershipCardBo extends BaseEntity {

    /**
     * 学生产品表
     */
    @NotNull(message = "学生产品表不能为空", groups = { EditGroup.class })
    private Long studentMembershipCardId;

    /**
     * 学生id
     */
    @NotNull(message = "学生id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentId;

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productId;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 会员卡类型
     */
    @NotNull(message = "会员卡类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long studentTypeId;

    /**
     * 会员卡类型(0失效--退费 1启用--付款)
     */
    @NotNull(message = "会员卡类型(0失效--退费 1启用--付款)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer cardStatus;

    /**
     * 产品开始时间
     */
    @NotNull(message = "产品开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date productBeginDate;

    /**
     * 产品结束时间
     */
    @NotNull(message = "产品结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date productEndDate;
    /**
     * 非该卡类型id
     */
    private Long neStudentTypeId;

}
