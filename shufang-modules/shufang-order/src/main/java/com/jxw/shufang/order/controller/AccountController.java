package com.jxw.shufang.order.controller;

import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.vo.AccountVo;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 账户流水 （扣款退费充值记录）
 * 前端访问路由地址为:/order/history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/account")
public class AccountController extends BaseController {


    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteBranchService remoteBranchService;

    /**
     * 查询账户剩余时长
     */
    @GetMapping("/get")
    public AccountVo getAccount() {
        //获取门店管理员选择的部门id
        Long deptId = LoginHelper.getDeptId();
        boolean b = remoteDeptService.deptIsShop(deptId);

        if (Boolean.FALSE.equals(b)) {
            RemoteDeptAccountVo vo = remoteDeptService.selectDeptByIdWithoutCache(deptId);
            AccountVo accountVo = new AccountVo();
            accountVo.setAccountId(vo.getDeptId());
            accountVo.setAccountTradeTypeEnum(AccountTradeTypeEnum.AGENT);
            accountVo.setRemainTime(vo.getRemainTime());
            return accountVo;
        } else {
            //如果所属组织是门店，获取所选的部门id
            Long selectDeptId = LoginHelper.getSelectDeptId();
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchByDeptId(selectDeptId);
            AccountVo accountVo = new AccountVo();
            accountVo.setAccountId(remoteBranchVo.getBranchId());
            accountVo.setAccountTradeTypeEnum(AccountTradeTypeEnum.BRANCH);
            accountVo.setRemainTime(remoteBranchVo.getRemainTime());
            return accountVo;
        }
    }

    /**
     * 查询账户剩余时长
     */
    @GetMapping("/getBranchAmount")
    public AccountVo getBranchAmount(Long branchId) {
        if (branchId == null) {
            return null;
        }
        RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchById(branchId);
        AccountVo accountVo = new AccountVo();
        accountVo.setAccountTradeTypeEnum(AccountTradeTypeEnum.BRANCH);
        accountVo.setAccountId(branchId);
        if (!ObjectUtils.isEmpty(remoteBranchVo)) {
            accountVo.setRemainTime(remoteBranchVo.getRemainTime());
        }
        return accountVo;
    }

}
