package com.jxw.shufang.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/28 15:45
 * @Version 1
 * @Description 预计算订单价格
 */
@Data
public class PreCalculateOrderPriceVO {
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 商品ID
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    /**
     * 商品有效期
     */
    private Long productValidDays;
    /**
     * 商品有效期时间限制
     */
    private String productValidTimeLimit;
    /**
     * 优惠价格
     */
    private BigDecimal preferentialPrice;
    /**
     * 订单价格
     */
    private BigDecimal orderPrice;
    /**
     * 应付价格
     */
    private BigDecimal ablePayPrice;
    /**
     * 期中满减
     */
    private BigDecimal signupDiscountAmount;

    private BigDecimal studentPreferentialAmount;

    private ProductDeductInfoVO productDeductInfo;
}
