package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.order.domain.ProductTemplate;
import com.jxw.shufang.order.domain.bo.ProductTemplateBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateResourceRelationVo;
import com.jxw.shufang.order.domain.vo.ProductTemplateVo;
import com.jxw.shufang.order.mapper.ProductTemplateMapper;
import com.jxw.shufang.order.service.IProductTemplateResourceRelationService;
import com.jxw.shufang.order.service.IProductTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品模板管理Service业务层处理
 *
 * @date 2024-06-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ProductTemplateServiceImpl implements IProductTemplateService {

    private final ProductTemplateMapper baseMapper;

    /**
     * 产品模板 与 资源关联关系的业务处理对象
     */
    private final IProductTemplateResourceRelationService productTemplateResourceRelationService;

    /**
     * 查询产品模板管理
     *
     * @param productTemplateId 产品模板管理主键
     * @return 产品模板管理
     */
    @Override
    public ProductTemplateVo queryById(Long productTemplateId) {
        //根据ID查询授权模板
        ProductTemplateVo productTemplateVo = baseMapper.selectVoById(productTemplateId);
        setResIdsByTemplateIds(ListUtil.toList(productTemplateVo));
        return productTemplateVo;
    }

    /**
     * 查询产品模板管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品模板管理集合
     */
    @Override
    public TableDataInfo<ProductTemplateVo> queryPageList(ProductTemplateBo bo, PageQuery pageQuery) {
        //只能查询到自己创建的模板
        bo.setCreateDept(LoginHelper.getDeptId());
        LambdaQueryWrapper<ProductTemplate> lqw = buildQueryWrapper(bo);
        Page<ProductTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        //根据产品模板集合，查询关联的资源ID集合，并设置给对应的产品模板对象
        List<ProductTemplateVo> records = result.getRecords();
        setResIdsByTemplateIds(records);
        return TableDataInfo.build(result);
    }

    /**
     * 查询产品模板管理列表
     *
     * @param bo 查询条件
     * @return 产品模板管理集合
     */
    @Override
    public List<ProductTemplateVo> queryList(ProductTemplateBo bo) {
        LambdaQueryWrapper<ProductTemplate> lqw = buildQueryWrapper(bo);
        List<ProductTemplateVo> productTemplateVos = baseMapper.selectVoList(lqw);
        setResIdsByTemplateIds(productTemplateVos);
        return productTemplateVos;
    }

    /**
     * 根据模板ID，查询关联的资源ID集合
     * @return
     */
    public void setResIdsByTemplateIds(List<ProductTemplateVo> courseTemps) {
        if (ObjectUtil.isEmpty(courseTemps)) {
            return;
        }

        //获得所有模板ID
        List<Long> templateIds = courseTemps.stream().map(ProductTemplateVo::getProductTemplateId).collect(Collectors.toList());
        //查询关联的资源ID集合
        Map<Long, List<ProductTemplateResourceRelationVo>> maps = productTemplateResourceRelationService.queryListByTemplateIds(templateIds)
            .stream().collect(Collectors.groupingBy(ProductTemplateResourceRelationVo::getProductTemplateId));

        //给每个课程模板对象，设置关联的课程ID集合
        courseTemps.stream().forEach(record -> {
            record.setResIds(Optional.ofNullable(maps.get(record.getProductTemplateId())).orElse(new ArrayList<>())
                .stream()
                .map(ProductTemplateResourceRelationVo::getResourceId)
                .collect(Collectors.toList()));
        });
    }

    private LambdaQueryWrapper<ProductTemplate> buildQueryWrapper(ProductTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductTemplate> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getProductTemplateName()), ProductTemplate::getProductTemplateName, bo.getProductTemplateName());
        lqw.like(StringUtils.isNotBlank(bo.getProductTemplateDesc()), ProductTemplate::getProductTemplateDesc, bo.getProductTemplateDesc());
        lqw.eq(bo.getProductTemplateType() != null, ProductTemplate::getProductTemplateType, bo.getProductTemplateType());
        lqw.eq(bo.getStatus() != null, ProductTemplate::getStatus, bo.getStatus());
        lqw.eq(bo.getCreateDept() != null, ProductTemplate::getCreateDept, bo.getCreateDept());
        lqw.orderByDesc(ProductTemplate::getCreateTime);
        return lqw;
    }

    /**
     * 新增产品模板管理
     *
     * @param bo 产品模板管理
     * @return 新增结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ProductTemplateBo bo) {
        log.info("【新增产品模板】请求参数：{}", bo);
        ProductTemplate add = MapstructUtils.convert(bo, ProductTemplate.class);
        add.setCreateDept(LoginHelper.getDeptId());
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProductTemplateId(add.getProductTemplateId());

            //新增模板与资源之间的关联关系
            //获取关联的资源ID集合
            List<Long> resIds = bo.getResIdList();
            if (ObjectUtil.isEmpty(resIds)) return flag;

            //调用批量新增关联关系的业务方法
            productTemplateResourceRelationService.insertBatchByBo(bo.getProductTemplateId(), resIds);
        }
        return flag;
    }

    /**
     * 修改产品模板管理
     *
     * @param bo 产品模板管理
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ProductTemplateBo bo) {
        log.info("【修改产品模板】请求参数：{}", bo);
        ProductTemplate update = MapstructUtils.convert(bo, ProductTemplate.class);
        validEntityBeforeSave(update);
        //执行修改
        boolean flag = baseMapper.updateById(update) > 0;

        //如果上下架状态不为空，则表示，只是改变上下家状态，无需 修改课程模板与课程之间的关联关系
        if (ObjectUtils.isNotNull(bo.getStatus())) {
            return flag;
        }

        //修改课程模板与课程之间的关联关系
        productTemplateResourceRelationService.updateByBos(bo.getProductTemplateId(), bo.getResIdList());
        return flag;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(ProductTemplate entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除产品模板管理信息
     *
     * @param ids 待删除的主键集合
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(List<Long> ids) {
        log.info("【批量删除产品模板信息】请求参数：{}", ids);
        if (ObjectUtils.isEmpty(ids)) return false;

        //删除课程模板与课程之间的关联关系
        productTemplateResourceRelationService.deleteBatchByTemplateIds(ids);
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
