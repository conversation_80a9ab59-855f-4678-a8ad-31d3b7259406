package com.jxw.shufang.order.enums;

import com.jxw.shufang.common.core.enums.SysRole;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: cyj
 * @date: 2025/6/17
 */
@Getter
@AllArgsConstructor
public enum RefundAuditNode {

    PLATFORM_ADMINISTRATOR_AUDIT(1, "平台初审", List.of(SysRole.PLATFORM_ADMINISTRATOR.getRoleId()), 2),
    TEACHING_SUPERVISOR_AUDIT(2, "督导审核", List.of(SysRole.TEACHING_SUPERVISOR.getRoleId()), 3),
    COMPANY_FINANCE_AUDIT(3, "公司财务审核", List.of(SysRole.COMPANY_FINANCE.getRoleId()), null);

    private final int code;
    private final String name;
    private final List<Long> roleList;
    private final Integer nextAuditCode;

    public static RefundAuditNode fromCode(Integer code) {
        if (code == null)
            return PLATFORM_ADMINISTRATOR_AUDIT;
        for (RefundAuditNode node : values()) {
            if (node.code == code)
                return node;
        }
        throw new IllegalArgumentException("未知审核节点编号: " + code);
    }

    public static List<RefundAuditNode> listByRoles(List<Long> roleIdList) {
        return Arrays.stream(values())
            .filter(node -> node.roleList != null && node.roleList.stream().anyMatch(roleIdList::contains))
            .collect(Collectors.toList());
    }

    public RefundAuditNode getNextNode() {
        return nextAuditCode == null ? null : fromCode(nextAuditCode);
    }

    public boolean checkRole(List<Long> roleIdList) {
        return roleList.stream().anyMatch(roleIdList::contains);
    }

    /**
     * 获取当前审核流程是否需要上传凭证
     *
     * @param payMode
     * @return
     */
    public AuditPolicy getPolicy(PayModeEnum payMode) {
        return AuditPolicyRegistry.getPolicy(payMode, this);
    }

    /**
     * 审核策略类
     */
    @Data
    public static class AuditPolicy {
        private final boolean passNeedVoucher;
        private final boolean rejectNeedVoucher;
        private final String voucherName;

        public AuditPolicy(boolean passNeedVoucher, boolean rejectNeedVoucher, String voucherName) {
            this.passNeedVoucher = passNeedVoucher;
            this.rejectNeedVoucher = rejectNeedVoucher;
            this.voucherName = voucherName;
        }
    }

    /**
     * 策略注册表，获取具体流程中是否需要对应的凭证
     */
    private static class AuditPolicyRegistry {
        private static final Map<String, AuditPolicy> POLICY_MAP = new HashMap<>();

        static {
            // 线上策略
            put(PayModeEnum.ONLINE, PLATFORM_ADMINISTRATOR_AUDIT, false, false, "");
            put(PayModeEnum.ONLINE, TEACHING_SUPERVISOR_AUDIT, true, true, "跟进记录");
            put(PayModeEnum.ONLINE, COMPANY_FINANCE_AUDIT, false, false, "");

            // 线下策略
            put(PayModeEnum.OFFLINE, PLATFORM_ADMINISTRATOR_AUDIT, false, false, "");
            put(PayModeEnum.OFFLINE, TEACHING_SUPERVISOR_AUDIT, true, true, "跟进记录");
            put(PayModeEnum.OFFLINE, COMPANY_FINANCE_AUDIT, true, false, "支付凭证");
        }

        private static void put(PayModeEnum mode, RefundAuditNode node, boolean passNeedVoucher,
            boolean rejectNeedVoucher, String voucherName) {
            POLICY_MAP.put(key(mode, node), new AuditPolicy(passNeedVoucher, rejectNeedVoucher, voucherName));
        }

        private static String key(PayModeEnum mode, RefundAuditNode node) {
            return mode.name() + "_" + node.code;
        }

        public static AuditPolicy getPolicy(PayModeEnum mode, RefundAuditNode node) {
            return POLICY_MAP.getOrDefault(key(mode, node), new AuditPolicy(false, false, ""));
        }
    }
}
