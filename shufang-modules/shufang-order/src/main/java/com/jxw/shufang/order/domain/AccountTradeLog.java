package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 账户流水 （扣款退费充值记录）对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("account_trade_log")
public class AccountTradeLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 账户流水id
     */
    @TableId(value = "account_trade_log_id")
    private Long accountTradeLogId;

    /**
     * 流水交易类型(0 支出 1 收入)
     */
    private Integer tradeType;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单类型(0充值订单,1会员订单)
     */
    private Integer orderType;

    /**
     * 主体交易账号
     */
    private Long accountId;

    /**
     * 主体交易账号类型(0代理商，1门店，2学生)
     */
    private Integer accountType;

    /**
     * 对应账号
     */
    private Long replyAccount;

    /**
     * 对应账号类型(0代理商，1门店，2学生)
     */
    private Integer replyAccountType;

    /**
     * 数量（xxx天）
     */
    private Integer amount;


}
