package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.order.domain.StudentProductTemplateAuth;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 产品（会员卡模板）授权视图对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = StudentProductTemplateAuth.class)
public class StudentProductTemplateAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板权限id
     */
    @ExcelProperty(value = "模板权限id")
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
    @ExcelProperty(value = "模板id")
    private Long templateId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long deptId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品模板类型（0 按天数消耗，1按时段消耗）
     */
    @ExcelProperty(value = "模板类型")
    private Integer productTemplateType;

    /**
     * 时段开始时间，product_template_type为1时生效
     */
    @ExcelProperty(value = "时段开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productBeginDate;

    /**
     * 时段结束时间，product_template_type为1时生效
     */
    @ExcelProperty(value = "时段结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productEndDate;

    /**
     * 产品有效天数
     */
    @ExcelProperty(value = "产品有效天数")
    private Long productValidDays;

    /**
     * 产品状态（0上架 1下架）
     */
    @ExcelProperty(value = "产品状态")
    private String productStatus;

    /**
     * 授权状态 0-未授权 1-已授权
     */
    private Integer authStatus;


}
