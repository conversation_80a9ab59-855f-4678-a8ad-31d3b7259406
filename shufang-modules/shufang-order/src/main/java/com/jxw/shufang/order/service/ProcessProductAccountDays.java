package com.jxw.shufang.order.service;

import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import lombok.Data;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/17 20:06
 * @Version 1
 * @Description
 */
public class ProcessProductAccountDays {
    /**
     * 产品有效期(天數)
     */
    private final Long productValidDays;

    /**
     * 产品有效期时间限制
     */
    private final String productValidTimeLimit;

    /**
     * 课程开始时间
     */
    private final Date courseStartTime;

    /**
     * 账户剩余时间(天數)
     */
    private final Long accountRemainTime;

    /**
     * 扣除时间(天數)
     */
    private Long deductTime;

    public Long getDeductTime() {
        return deductTime;
    }

    private ProcessProductAccountDays(Long productValidDays,
                                      String productValidTimeLimit,
                                      Date courseStartTime,
                                      Long accountRemainTime) {
        this.productValidDays = productValidDays;
        this.productValidTimeLimit = productValidTimeLimit;
        this.courseStartTime = courseStartTime;
        this.accountRemainTime = accountRemainTime;
    }

    public static ProcessProductAccountDays of(Long productValidDays,
                                               String productValidTimeLimit,
                                               Date courseStartTime,
                                               Long accountRemainTime) {
        if (null == productValidDays && StringUtils.isEmpty(productValidTimeLimit)) {
            throw new RuntimeException("产品有效期不能为空");
        }
        courseStartTime = DateUtils.setStartOfDay(courseStartTime == null ? new Date() : courseStartTime);
        accountRemainTime = accountRemainTime == null ? 0 : accountRemainTime;
        return new ProcessProductAccountDays(productValidDays, productValidTimeLimit, courseStartTime, accountRemainTime);
    }

    public static ProcessProductAccountDays ofByRefund(Long productValidDays,
                                                       String productValidTimeLimit,
                                                       Date courseStartTime) {
        return of(productValidDays, productValidTimeLimit, courseStartTime, null);
    }

    public ProcessProductAccountDays calculateDeductTime() {
        if (0 == accountRemainTime) {
            throw new RuntimeException("账户余额不足");
        }

        Long productDeductTime = this.getProductDeductTime();
        if (productDeductTime > accountRemainTime) {
            throw new RuntimeException("账户扣款天數余额不足");
        }
        this.deductTime = productDeductTime;
        return this;
    }

    public RefundDaysDTO calculateRemainDaysByRefund(Date refundTime) {
        // 参数校验
        if (productValidDays == null && StringUtils.isEmpty(productValidTimeLimit)) {
            throw new IllegalArgumentException("产品有效期不能为空");
        }

        // 日期转换
        LocalDate courseStart =  this.getDateToLocalDate(courseStartTime);
        LocalDate refundDate = (refundTime != null)
            ?  this.getDateToLocalDate(refundTime)
            : LocalDate.now();

        // 计算逻辑
        if (refundDate.isBefore(courseStart)) {
            return calculatePreStartRefund();
        } else {
            long usedDays = ChronoUnit.DAYS.between(courseStart, refundDate);
            return calculateActiveRefund(usedDays);
        }
    }

    private RefundDaysDTO calculatePreStartRefund() {
        if (productValidDays != null) {
            return new RefundDaysDTO(productValidDays, 0L);
        } else {
            LocalDate endDate = getEndLocalDate();
            long totalDays = ChronoUnit.DAYS.between(
                this.getDateToLocalDate(courseStartTime),
                endDate
            ) + 1;
            return new RefundDaysDTO(totalDays, 0L);
        }
    }

    private RefundDaysDTO calculateActiveRefund(long usedDays) {
        long totalDays;
        if (productValidDays != null) {
            totalDays = productValidDays;
        } else {
            LocalDate endDate = getEndLocalDate();
            totalDays = ChronoUnit.DAYS.between(
                this.getDateToLocalDate(courseStartTime),
                endDate
            ) + 1;
        }

        long refundDays = Math.max(totalDays - usedDays, 0);
        return new RefundDaysDTO(refundDays, usedDays);
    }
    @Data
    public class RefundDaysDTO {
        private Long refundDays;
        private Long useDays;

        public RefundDaysDTO(Long refundDays, Long useDays) {
            this.refundDays = refundDays;
            this.useDays = useDays;
        }
    }


    private LocalDate getNowDateToLocalDate() {
        return this.getDateToLocalDate(new Date());
    }

    private LocalDate getEndLocalDate() {
        return this.getEndLocalDate(productValidTimeLimit);
    }

    private LocalDate getStartLocalDate() {
        return this.getStartLocalDate(productValidTimeLimit);
    }

    private Long getProductDeductTime() {
        if (StringUtils.isEmpty(productValidTimeLimit)) {
            return productValidDays;
        }
        LocalDate startLocalDate = getStartLocalDate();
        LocalDate endLocalDate = getEndLocalDate();
        LocalDate courseStartLocalDate = getDateToLocalDate(courseStartTime);

        // 检查时间
        this.checkDate(startLocalDate, endLocalDate, courseStartLocalDate);

        if (courseStartLocalDate.isBefore(startLocalDate)) {
            return getBetweenDays(startLocalDate, endLocalDate);
        }
        return getBetweenDays(courseStartLocalDate, endLocalDate);
    }

    private void checkDate(LocalDate startLocalDate, LocalDate endLocalDate, LocalDate courseStartLocalDate) {
        if (null == startLocalDate || null == endLocalDate) {
            throw new RuntimeException("产品类型没有配置有效时间");
        }
        if (courseStartLocalDate.isAfter(endLocalDate)) {
            throw new RuntimeException("课程开始时间不能在产品有效期之后");
        }
    }

    private static long getBetweenDays(LocalDate startLocalDate, LocalDate endLocalDate) {
        return ChronoUnit.DAYS.between(startLocalDate, endLocalDate) + 1;
    }

    private LocalDate getStartLocalDate(String productValidTimeLimit) {
        Date validStartDate = ProcessProductPrice.getValidStartDate(productValidTimeLimit);
        return getDateToLocalDate(validStartDate);
    }

    private LocalDate getDateToLocalDate(Date date) {
        return ProcessProductPrice.getDateToLocalDate(date);
    }

    private LocalDate getEndLocalDate(String productValidTimeLimit) {
        Date validEndDate = ProcessProductPrice.getValidEndDate(productValidTimeLimit);
        return getDateToLocalDate(validEndDate);
    }
}
