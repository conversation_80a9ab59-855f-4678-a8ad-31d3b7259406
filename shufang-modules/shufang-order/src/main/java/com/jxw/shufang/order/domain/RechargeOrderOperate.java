package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）对象 oms_recharge_order_operate
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oms_recharge_order_operate")
public class RechargeOrderOperate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单操作id
     */
    @TableId(value = "order_operate_id")
    private Long orderOperateId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
    private Integer orderOperateStatus;

    /**
     * 支付方式（已支付才存在 对应字典值，比如 微信、支付宝）
     */
    private String paymentType;

    /**
     * 收款金额（已支付才存在）
     */
    private BigDecimal paymentAmount;

    /**
     * 退款方式（已退款才存在 对应字典值，比如 微信、支付宝）
     */
    private String refundType;

    /**
     * 退款金额（已退款才存在）
     */
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    private String orderOperateRemark;


}
