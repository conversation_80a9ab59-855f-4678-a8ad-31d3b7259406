package com.jxw.shufang.order.consumer;

import com.jxw.shufang.common.core.utils.ValidatorUtils;
import com.jxw.shufang.common.pay.domain.dto.notify.PayNotifyDTO;
import com.jxw.shufang.order.enums.PayTradeNotifyTopicConstant;
import com.jxw.shufang.order.service.IOrderOperateService;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: cyj
 * @date: 2025/4/25
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(topic = PayTradeNotifyTopicConstant.ORDER_NOTIFY_TOPIC,
    consumerGroup = PayTradeNotifyTopicConstant.NOTIFY_CONSUMER_GROUP,
    messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.CONCURRENTLY,
    selectorExpression = PayTradeNotifyTopicConstant.ORDER_PAY_NOTIFY_TAG)
public class CommonPayNotifyListener implements RocketMQListener<String> {

    private final IOrderOperateService orderOperateService;

    @Override
    public void onMessage(String s) {
        log.info("处理通用支付消息：{}", s);
        PayNotifyDTO notifyDTO = JSONObject.parseObject(s, PayNotifyDTO.class);
        if (notifyDTO == null){
            throw new RuntimeException("通知内容不能为空");
        }
        if(!PayNotifyDTO.PayStatusEnum.PAID.getStatus().equals(notifyDTO.getPayStatus())){
            log.info("支付状态是非已支付，不处理");
            return;
        }
        // 支付成功才处理
        ValidatorUtils.validate(notifyDTO);
        orderOperateService.processPayNotify(notifyDTO);
    }
}
