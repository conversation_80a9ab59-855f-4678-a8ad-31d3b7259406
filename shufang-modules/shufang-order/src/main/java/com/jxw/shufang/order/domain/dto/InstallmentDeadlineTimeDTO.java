package com.jxw.shufang.order.domain.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/20 14:53
 * @Version 1
 * @Description 订单分期到期时间处理
 */
@Data
public class InstallmentDeadlineTimeDTO {
    private Date courseStartTime;
    private Long branchId;
    private Long productId;
    private Long studentId;


    public static InstallmentDeadlineTimeDTO of(Date courseStartTime, Long branchId, Long productId, Long studentId) {
        InstallmentDeadlineTimeDTO dto = new InstallmentDeadlineTimeDTO();
        dto.courseStartTime = courseStartTime;
        dto.branchId = branchId;
        dto.productId = productId;
        dto.studentId = studentId;
        return dto;
    }
}
