package com.jxw.shufang.order.service;

import java.util.List;

import com.jxw.shufang.order.domain.bo.OrderOperateApplyRecordBo;
import com.jxw.shufang.order.domain.vo.OrderOperateApplyRecordVo;

/**
 * @author: cyj
 * @date: 2025/3/17
 */
public interface IOrderOperateApplyRecordService {
    /**
     * 保存订单操作审核记录
     *
     * @param orderOperateApplyRecordBo
     * @return
     */
    boolean insertByBo(OrderOperateApplyRecordBo orderOperateApplyRecordBo);

    /**
     * 根据订单操作记录列表获取操作审核记录列表
     *
     * @param orderOperateIdList
     * @return
     */
    List<OrderOperateApplyRecordVo> listByOperateIds(List<Long> orderOperateIdList);

    /**
     * 获取订单操作指定状态的审核记录
     *
     * @param orderOperateId
     * @param reviewStatus
     * @return
     */
    OrderOperateApplyRecordVo getOrderOperateApplyRecord(Long orderOperateId, Integer reviewStatus);
}
