package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.OrderProductInfoBo;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import com.jxw.shufang.order.domain.vo.StudentProductVo;
import com.jxw.shufang.order.service.IOrderProductInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单产品详情
 * 前端访问路由地址为:/order/productInfo
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/productInfo")
public class OrderProductInfoController extends BaseController {

    private final IOrderProductInfoService orderProductInfoService;

    /**
     * 查询订单产品详情列表
     */
    @SaCheckPermission("order:productInfo:list")
    @GetMapping("/list")
    public TableDataInfo<OrderProductInfoVo> list(OrderProductInfoBo bo, PageQuery pageQuery) {
        return orderProductInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出订单产品详情列表
     */
    @SaCheckPermission("order:productInfo:export")
    @Log(title = "订单产品详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrderProductInfoBo bo, HttpServletResponse response) {
        List<OrderProductInfoVo> list = orderProductInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单产品详情", OrderProductInfoVo.class, response);
    }

    /**
     * 获取订单产品详情详细信息
     *
     * @param orderInfoId 主键
     */
    @SaCheckPermission("order:productInfo:query")
    @GetMapping("/{orderInfoId}")
    public R<OrderProductInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable Long orderInfoId) {
        return R.ok(orderProductInfoService.queryById(orderInfoId));
    }

    /**
     * 新增订单产品详情
     */
    @SaCheckPermission("order:productInfo:add")
    @Log(title = "订单产品详情", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OrderProductInfoBo bo) {
        return toAjax(orderProductInfoService.insertByBo(bo));
    }

    /**
     * 修改订单产品详情
     */
    @SaCheckPermission("order:productInfo:edit")
    @Log(title = "订单产品详情", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrderProductInfoBo bo) {
        return toAjax(orderProductInfoService.updateByBo(bo));
    }

    /**
     * 删除订单产品详情
     *
     * @param orderInfoIds 主键串
     */
    @SaCheckPermission("order:productInfo:remove")
    @Log(title = "订单产品详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderInfoIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderInfoIds) {
        return toAjax(orderProductInfoService.deleteWithValidByIds(List.of(orderInfoIds), true));
    }

    /**
     * 获取最新的showNum张有效的会员卡
     *
     * @param studentId 会员Id
     * @param showNum   显示数量
     */
    @SaCheckPermission("order:productInfo:list")
    @GetMapping("/getLatestValidMemberCard")
    public R<StudentProductVo> getLatestValidMemberCard(@NotNull(message = "会员Id不能为空") Long studentId,
                                                        @NotNull(message = "显示数量不能为空") Integer showNum) {
        return R.ok(orderProductInfoService.getLatestValidMemberCard(studentId, showNum));
    }

    /**
     * 获取所有会员卡，包含过期的
     *
     * @param studentId 会员Id
     */
    @SaCheckPermission("order:productInfo:list")
    @GetMapping("/getAllMemberCard")
    public R<List<OrderProductInfoVo>> getAllMemberCard(@NotNull(message = "会员Id不能为空") Long studentId) {
        return R.ok(orderProductInfoService.getAllMemberCard(studentId));
    }


}
