package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentTypeVo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 订单产品详情视图对象 order_product_info
 *
 * @date 2024-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderProductInfo.class)
public class OrderProductInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情id
     */
    @ExcelProperty(value = "订单详情id")
    private Long orderInfoId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 产品id
     */
    @ExcelProperty(value = "产品id")
    private Long productId;

    /**
     * 会员类型id
     */
    @ExcelProperty(value = "会员类型id")
    private Long studentTypeId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品有效天数
     */
    @ExcelProperty(value = "产品有效天数")
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    @ExcelProperty(value = "产品有效期", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "时=间段，用,至=,隔=开")
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    @ExcelProperty(value = "产品价格")
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    @ExcelProperty(value = "优惠价格", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "门=店直减")
    private BigDecimal preferentialPrice;


    private RemoteStudentTypeVo studentType;


    /**
     * 有效时间范围
     */
    private String validTimeRange;

    /**
     * 是否有效
     */
    private Boolean isValid;
    /**
     * 会员优惠额度
     */
    private BigDecimal studentPreferentialPrice;

    /**
     * 产品状态(同订单状态)
     */
    private String productStatus;
    /**
     * 产品原始价格
     */
    private BigDecimal originProductPrice;


    /**
     * 期中报名直减
     */
    private BigDecimal signupDiscountAmount;
}
