package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.OrderPayRecord;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/18 18:09
 * @Version 1
 * @Description 订单在线支付集合上下文
 */
@Data
public class OrderOnlinePayCollectionContext {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 是否是定金支付
     */
    private Boolean depositAmountFlag;
    /**
     * 是否分期支付
     */
    private Boolean installmentFlag;

    private Long orderOperateId;
    /**
     * 订单支付编号
     */
    private String orderPayNo;

    /**
     * 本次支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 实际应付金额
     */
    private BigDecimal actualAmount;
    private List<OrderPayRecord> orderPayRecordList;

    private Integer maxPayCount = 3;

    /**
     * 是否分期支付
     */
    public boolean isInstallmentPay() {
        return this.getInstallmentFlag();
    }


    /**
     * 首笔支付满足条件 1、第一笔支付
     *
     * @return
     */
    public boolean firstPayStage() {
        return orderPayRecordList.isEmpty();
    }

    /**
     * 次笔支付满足条件 1、分期支付 2、分期第二笔支付
     *
     * @return
     */
    public boolean isSecondInstallmentPayStage() {
        if (!this.isInstallmentPay()) {
            return false;
        }
        int size = orderPayRecordList.size();
        return size == 1 && !isLastInstallmentPayStage();
    }

    /**
     * 尾笔支付满足条件 1、分期支付 2、分期最后一笔支付
     *
     * @return
     */
    public boolean isLastInstallmentPayStage() {
        if (!this.isInstallmentPay()) {
            return false;
        }
        return isLastPay();
    }


    public void collectionCheck() {
        if (BigDecimal.ZERO.compareTo(paymentAmount) >= 0) {
            throw new ServiceException("支付金额不能小于等于0");
        }
        BigDecimal totalAmount = this.totalAmount();
        if (totalAmount.compareTo(actualAmount) > 0) {
            throw new ServiceException("支付金额不能大于订单实际金额");
        }
        if (isLastPay()) {
            if (0 != totalAmount.compareTo(actualAmount)) {
                throw new ServiceException("最后一笔支付单金额不正确");
            }
        }
    }

    private BigDecimal totalAmount() {
        BigDecimal paidTotal = this.calculatePaidTotal(orderPayRecordList);
        return paidTotal.add(paymentAmount);
    }

    /**
     * 是否是最后一笔支付单
     *
     * @return
     */
    private Boolean isLastPay() {
        return maxPayCount - 1 == orderPayRecordList.size() || 0 == totalAmount().compareTo(actualAmount);
    }

    private BigDecimal calculatePaidTotal(List<OrderPayRecord> records) {
        return records.stream()
            .map(OrderPayRecord::getAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
