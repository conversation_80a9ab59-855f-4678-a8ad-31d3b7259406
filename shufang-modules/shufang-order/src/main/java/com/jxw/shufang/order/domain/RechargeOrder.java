package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 代理商充值订单记录对象 oms_recharge_order
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oms_recharge_order")
public class RechargeOrder extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 组织id（0的话是代理商id，类型为1的话是门店id）
     */
    private Long rechargeId;

    /**
     * 0是代理商，1是门店
     */
    private Integer rechargeType;

    /**
     * 经办代理商id
     */
    private Long handlingDeptId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 经办日期
     */
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;

    /**
     * 订单操作id
     */
    private Long orderOperateId;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private Long delFlag;


}
