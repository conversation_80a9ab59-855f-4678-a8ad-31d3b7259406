package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.enums.AbnormalPayEnum;
import com.jxw.shufang.order.enums.PayOrderCloseTypeEnum;
import com.jxw.shufang.order.enums.PayOrderStatusEnum;
import com.jxw.shufang.order.mapper.OrderPayRecordMapper;
import com.jxw.shufang.order.service.OrderPayRecordDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/17 16:42
 * @Version 1
 * @Description
 */
@Service
public class OrderPayRecordDaoImpl implements OrderPayRecordDao {
    @Resource
    private OrderPayRecordMapper orderPayRecordMapper;


    @Override
    public List<OrderPayRecord> queryValidRecordByOrderId(Long orderId, List<PayOrderStatusEnum> orderPayTypeEnums) {
        if (null == orderId || CollectionUtil.isEmpty(orderPayTypeEnums)) {
            throw new ServiceException("缺少参数");
        }
        List<Integer> statusEnums = orderPayTypeEnums.stream().map(PayOrderStatusEnum::getCode).toList();
        LambdaQueryWrapper<OrderPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPayRecord::getOrderId, orderId);
        queryWrapper.in(OrderPayRecord::getPaymentStatus, statusEnums);
        queryWrapper.eq(OrderPayRecord::getAbnormalPayFlag, AbnormalPayEnum.NORMAL_PAY.getCode());
        queryWrapper.eq(OrderPayRecord::getDelFlag, 0);
        queryWrapper.orderByAsc(OrderPayRecord::getUpdateTime);
        return orderPayRecordMapper.selectList(queryWrapper);
    }

    @Override
    public OrderPayRecord queryRecordByOrderPayNo(String orderPayNo) {
        if (StringUtils.isEmpty(orderPayNo)) {
            throw new ServiceException("orderPayNo不能为空");
        }
        LambdaQueryWrapper<OrderPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPayRecord::getOrderPayNo, orderPayNo);
        queryWrapper.eq(OrderPayRecord::getAbnormalPayFlag, AbnormalPayEnum.NORMAL_PAY.getCode());
        queryWrapper.eq(OrderPayRecord::getDelFlag, 0);
        return orderPayRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateOrderPayRecordByPayNo(OrderPayRecord orderPayRecord, String orderPayNo) {
        if (StringUtils.isEmpty(orderPayNo)) {
            throw new ServiceException("orderPayNo不能为空");
        }
        LambdaQueryWrapper<OrderPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPayRecord::getOrderPayNo, orderPayNo);
        queryWrapper.eq(OrderPayRecord::getDelFlag, 0);
        return orderPayRecordMapper.update(orderPayRecord, queryWrapper);
    }


    @Override
    public int insertOrderPayRecord(OrderPayRecord orderPayRecord) {
        if(null == orderPayRecord){
            throw new ServiceException("参数不能为空");
        }
        return orderPayRecordMapper.insert(orderPayRecord);
    }

    @Override
    public void cancelOrderPayRecord(String orderPayNo) {
        OrderPayRecord orderPayRecord = new OrderPayRecord();
        orderPayRecord.setPaymentStatus(PayOrderStatusEnum.CANCEL.getCode());
        orderPayRecord.setOrderCloseReason(PayOrderCloseTypeEnum.CANCEL_ORDER_PAY_CANCEL.getMessage());
        this.updateOrderPayRecordByPayNo(orderPayRecord, orderPayNo);
    }

    @Override
    public OrderPayRecord queryLastNoPayRecordByOrderId(Long orderId) {
        List<OrderPayRecord> orderPayRecords = queryValidRecordByOrderId(orderId, List.of(PayOrderStatusEnum.WAIT_PAY));
        if (CollectionUtil.isEmpty(orderPayRecords)) {
            return null;
        }
        return orderPayRecords.stream().filter(Objects::nonNull)
            .max(Comparator.comparing(OrderPayRecord::getUpdateTime))
            .orElse(null);
    }

    @Override
    public List<OrderPayRecord> queryOrderPayRecordByOrderIds(List<Long> orderIds) {
        if (CollectionUtil.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderPayRecord> queryWrapper = Wrappers.lambdaQuery(OrderPayRecord.class)
            .eq(OrderPayRecord::getDelFlag, 0)
            .eq(OrderPayRecord::getAbnormalPayFlag, AbnormalPayEnum.NORMAL_PAY.getCode())
            .in(OrderPayRecord::getOrderId, orderIds);
        return orderPayRecordMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrderPayRecord> queryOrderAllPayRecord(Long orderId) {
        if (null == orderId) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrderPayRecord> queryWrapper = Wrappers.lambdaQuery(OrderPayRecord.class)
            .eq(OrderPayRecord::getDelFlag, 0)
            .ne(OrderPayRecord::getPaymentStatus, PayOrderStatusEnum.CANCEL.getCode())
            .eq(OrderPayRecord::getOrderId, orderId);
        return orderPayRecordMapper.selectList(queryWrapper);
    }
}
