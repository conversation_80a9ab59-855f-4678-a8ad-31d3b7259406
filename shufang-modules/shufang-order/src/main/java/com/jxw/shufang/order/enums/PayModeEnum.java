package com.jxw.shufang.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
 * @author: cyj
 * @date: 2025/4/25
 */
@AllArgsConstructor
@Getter
public enum PayModeEnum {

    ONLINE(1, "线上支付"), OFFLINE(0, "线下支付");

    /**
     * 模式code
     */
    private final Integer modeCode;
    /**
     * 备注
     */
    private final String desc;

    /**
     * 根据code 获取enum
     *
     * @return
     */
    public static PayModeEnum fromCode(Integer code) {
        for (PayModeEnum value : values()) {
            if (value.getModeCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static Set<PayModeEnum> allPayType(){
        return Set.of(ONLINE,OFFLINE);
    }

    public static Set<PayModeEnum> offLinePayType(){
        return Set.of(OFFLINE);
    }
}
