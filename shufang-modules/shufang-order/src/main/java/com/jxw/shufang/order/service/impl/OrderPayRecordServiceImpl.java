package com.jxw.shufang.order.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.pay.client.PayCommonClient;
import com.jxw.shufang.common.pay.domain.dto.response.ApiResp;
import com.jxw.shufang.order.domain.*;
import com.jxw.shufang.order.domain.bo.CancelPayOrderBo;
import com.jxw.shufang.order.domain.bo.OrderPayBo;
import com.jxw.shufang.order.domain.dto.CancelPayOrderContext;
import com.jxw.shufang.order.domain.dto.QueryStudentPayPageInfoContext;
import com.jxw.shufang.order.domain.vo.OrderCompletePayRecordVo;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.domain.vo.QueryStudentPayOrderVo;
import com.jxw.shufang.order.enums.*;
import com.jxw.shufang.order.interfaces.PaymentCollectionFactory;
import com.jxw.shufang.order.service.*;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentMerchantConfigDTO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/6/17 19:43
 * @Version 1
 * @Description
 */
@Service
public class OrderPayRecordServiceImpl implements OrderPayRecordService {
    @Resource
    private OrderPayRecordDao orderPayRecordDao;
    @Resource
    private PayCommonClient payCommonClient;
    @Resource
    private IOrderService orderService;
    @Resource
    private PaymentCollectionFactory paymentCollectionFactory;
    @Resource
    private OperateOrderDao operateOrderDao;
    @Resource
    private IOrderProductInfoService orderProductInfoService;
    @DubboReference
    private RemoteStudentService remoteStudentService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelPayOrder(CancelPayOrderBo cancelPayOrder, Long userId) {
        CancelPayOrderContext cancelPayOrderContext = this.buildCancelPayOrderContext(cancelPayOrder, userId);
        Integer paymentStatus = cancelPayOrderContext.getOrderPayRecord().getPaymentStatus();
        boolean checkDisablePayOrder = checkDisablePayOrder(cancelPayOrderContext, paymentStatus);
        if (checkDisablePayOrder){
            return;
        }
        if (!PayOrderStatusEnum.WAIT_PAY.getCode().equals(paymentStatus)) {
            throw new ServiceException("取消失败:支付单非待支付");
        }
        orderPayRecordDao.cancelOrderPayRecord(cancelPayOrder.getOrderPayNo());

        // 无transactionNo说明用户未打开过链接，不用删除银联订单
        if(StringUtils.isEmpty(cancelPayOrderContext.getOrderPayRecord().getTransactionNo())){
            return;
        }
        ApiResp<String> closeOrder = payCommonClient.closeOrder(cancelPayOrderContext.buildCloseOrderRequestDTO());
        if (null == closeOrder || !closeOrder.isSuccess()) {
            throw new ServiceException("取消失败:当前订单的支付单不可取消，请检查是否已被支付或者对应商户的配置密钥是否正确");
        }
    }

    private static boolean checkDisablePayOrder(CancelPayOrderContext cancelPayOrderContext, Integer paymentStatus) {
        if (PayOrderStatusEnum.CANCEL.getCode().equals(paymentStatus)) {
            return true;
        }
        return false;
    }

    @Override
    public List<OrderCompletePayRecordVo> queryCompleteRecord(Long orderId) {
        if (null == orderId) {
            return Collections.emptyList();
        }
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryOrderAllPayRecord(orderId);
        return orderPayRecords.stream()
            .filter(Objects::nonNull)
            .map(this::buildOrderCompletePayRecordVo)
            .toList();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"'order_pay_key:' + #bo.orderId"}, acquireTimeout = 0)
    public OrderPayVO pay(OrderPayBo bo) {
        Order order = orderService.queryOrderById(bo.getOrderId());
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }
        OrderOperate orderOperate = operateOrderDao.queryById(order.getOrderOperateId());
        if (null == orderOperate) {
            throw new ServiceException("无效的订单操作信息");
        }
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(bo.getOrderPayNo());
        if (null == orderPayRecord) {
            throw new ServiceException("支付单不存在");
        }
        if(!orderPayRecord.getOrderId().equals(bo.getOrderId())){
            throw new ServiceException("支付单无效");
        }

        // 订单状态校验
        OrderPayPreValidator.of(order, orderPayRecord, orderOperate).validate();

        // 生成支付链接
        OrderPayVO orderPayVo = paymentCollectionFactory
            .executePayment(PayModeEnum.ONLINE)
            .generatePaymentUrl(order, bo.getOrderPayNo());

        this.updatePayRecordByPayNo(orderPayRecord.getOrderPayNo(), orderPayVo.getTransactionNo());
        return orderPayVo;
    }

    @Override
    public QueryStudentPayOrderVo queryStudentPayOrder(Long orderId, String orderPayNo) {
        QueryStudentPayPageInfoContext qrCodeContext = this.buildGeneratePaymentQrcodeContext(orderId, orderPayNo);
        if (null == qrCodeContext) {
            throw new ServiceException("无效的订单信息");
        }
        QueryStudentPayOrderVo queryStudentPayOrderVo = new QueryStudentPayOrderVo();
        queryStudentPayOrderVo.setStudentName(qrCodeContext.getRemoteStudentVo().getStudentName());
        queryStudentPayOrderVo.setMerchantName(qrCodeContext.getCommonPayConfigByStudent().getMerchantName());
        queryStudentPayOrderVo.setPayAmount(qrCodeContext.getOrderPayRecord().getPayAbleAmount());
        queryStudentPayOrderVo.setProductName(qrCodeContext.getOrderProductInfoList().get(0).getProductName());
        return queryStudentPayOrderVo;
    }
    private QueryStudentPayPageInfoContext buildGeneratePaymentQrcodeContext(Long orderId, String orderPayNo) {
        Order order = orderService.queryOrderById(orderId);
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }
        OrderOperate orderOperate = operateOrderDao.queryById(order.getOrderOperateId());
        if (null == orderOperate) {
            throw new ServiceException("无效的订单操作信息");
        }
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(orderPayNo);
        if (null == orderPayRecord) {
            throw new ServiceException("支付单不存在");
        }
        if(!orderPayRecord.getOrderId().equals(orderId)){
            throw new ServiceException("支付单无效");
        }
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(order.getStudentId());
        if (null == remoteStudentVo) {
            throw new ServiceException("学生信息不存在");
        }
        List<OrderProductInfo> orderProductInfoList = orderProductInfoService.queryProductByOrder(order.getOrderId());
        if (CollectionUtil.isEmpty(orderProductInfoList)) {
            throw new ServiceException("订单商品不存在");
        }
        RemoteStudentMerchantConfigDTO commonPayConfigByStudent = remoteStudentService.getCommonPayConfigByStudent(order.getStudentId());
        if (null == commonPayConfigByStudent) {
            throw new ServiceException("商户信息不存在");
        }
        return QueryStudentPayPageInfoContext.builder().order(order)
            .orderPayRecord(orderPayRecord)
            .remoteStudentVo(remoteStudentVo)
            .orderProductInfoList(orderProductInfoList)
            .commonPayConfigByStudent(commonPayConfigByStudent)
            .orderOperate(orderOperate)
            .build();
    }

    private void updatePayRecordByPayNo(String orderPayNo, String transactionNo) {
        OrderPayRecord updateOrderPayRecord = new OrderPayRecord();
        updateOrderPayRecord.setTransactionNo(transactionNo);
        orderPayRecordDao.updateOrderPayRecordByPayNo(updateOrderPayRecord, orderPayNo);
    }

    private OrderCompletePayRecordVo buildOrderCompletePayRecordVo(OrderPayRecord orderPayRecord) {
        OrderCompletePayRecordVo orderCompletePayRecordVo = new OrderCompletePayRecordVo();
        orderCompletePayRecordVo.setOrderPayNo(orderPayRecord.getOrderPayNo());
        orderCompletePayRecordVo.setChannelOrderId(orderPayRecord.getChannelOrderId());
        orderCompletePayRecordVo.setAmount(orderPayRecord.getPayAbleAmount());
        orderCompletePayRecordVo.setPayTime(orderPayRecord.getPaymentTime());
        PaymentStageEnum paymentStageEnum = PaymentStageEnum.getByCode(orderPayRecord.getPaymentStage());
        orderCompletePayRecordVo.setPayResultStageName(Optional.ofNullable(paymentStageEnum).map(PaymentStageEnum::getMessage).orElse(null));
        orderCompletePayRecordVo.setRemark(orderPayRecord.getPaymentDesc());
        this.processPayResult(orderPayRecord, orderCompletePayRecordVo);
        return orderCompletePayRecordVo;
    }

    private void processPayResult(OrderPayRecord orderPayRecord, OrderCompletePayRecordVo orderCompletePayRecordVo) {
        if(null == orderPayRecord){
            return;
        }
        if (AbnormalPayEnum.ABNORMAL_PAY.getCode().equals(orderPayRecord.getAbnormalPayFlag())) {
            orderCompletePayRecordVo.setPayResult(PayResultEnum.EXCEPTION.getCode());
        } else {
            Integer paymentStatus = orderPayRecord.getPaymentStatus();
            if (PayOrderStatusEnum.WAIT_PAY.getCode().equals(paymentStatus)) {
                orderCompletePayRecordVo.setPayResult(PayResultEnum.WAIT_PAY.getCode());
            } else if (PayOrderStatusEnum.PAYED.getCode().equals(paymentStatus)) {
                orderCompletePayRecordVo.setPayResult(PayResultEnum.SUCCESS.getCode());
            }else {
                orderCompletePayRecordVo.setPayResult(PayResultEnum.SUCCESS.getCode());
            }
        }
    }

    public CancelPayOrderContext buildCancelPayOrderContext(CancelPayOrderBo cancelPayOrder, Long userId) {
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(cancelPayOrder.getOrderPayNo());
        if (null == orderPayRecord) {
            throw new ServiceException("取消失败:无效的支付单号");
        }

        if (!PayOrderStatusEnum.ableCancelStatus().contains(orderPayRecord.getPaymentStatus())) {
            throw new ServiceException("取消失败:订单为非待支付，禁止取消，请走退款");
        }

        Order order = orderService.queryOrderById(orderPayRecord.getOrderId());
        if (null == order) {
            throw new ServiceException("取消失败:无效的订单");
        }

        RemoteStudentMerchantConfigDTO configDTO = remoteStudentService.getCommonPayConfigByStudent(order.getStudentId());
        if (null == configDTO) {
            throw new ServiceException("查找商务信息失败:无效的商户信息");
        }
        return CancelPayOrderContext.of(configDTO, orderPayRecord, order);
    }
}
