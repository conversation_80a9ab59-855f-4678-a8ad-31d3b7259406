package com.jxw.shufang.order;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.Import;

/**
 * 订单模块
 *
 */
@EnableDubbo
@SpringBootApplication
@Import(RocketMQAutoConfiguration.class)
public class ShufangOrderApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangOrderApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  订单模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
