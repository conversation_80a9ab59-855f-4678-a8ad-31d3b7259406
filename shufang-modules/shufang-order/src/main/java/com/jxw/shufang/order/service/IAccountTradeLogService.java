package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.AccountTradeLogBo;
import com.jxw.shufang.order.domain.vo.AccountTradeLogVo;

import java.util.List;

/**
 * 账户流水 （扣款退费充值记录）Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IAccountTradeLogService {

    /**
     * 查询账户流水 （扣款退费充值记录）列表
     */
    TableDataInfo<AccountTradeLogVo> queryPageList(AccountTradeLogBo bo, PageQuery pageQuery);

    /**
     * 查询账户流水 （扣款退费充值记录）列表
     */
    List<AccountTradeLogVo> queryList(AccountTradeLogBo bo);

    /**
     * 新增账户流水 （扣款退费充值记录）
     */
    Boolean insertByBo(AccountTradeLogBo bo);



}
