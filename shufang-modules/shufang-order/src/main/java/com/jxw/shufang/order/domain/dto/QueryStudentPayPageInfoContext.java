package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentMerchantConfigDTO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/8 15:27
 * @Version 1
 * @Description
 */
@Data
@Builder
public class QueryStudentPayPageInfoContext {
    private OrderPayRecord orderPayRecord;
    private Order order;
    private OrderOperate orderOperate;
    private RemoteStudentVo remoteStudentVo;
    private List<OrderProductInfo> orderProductInfoList;
    private RemoteStudentMerchantConfigDTO commonPayConfigByStudent;
}
