package com.jxw.shufang.order.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/17 17:07
 * @Version 1
 * @Description 支付单状态
 */
public enum PayOrderStatusEnum {
    // 1待支付 2已支付 3已取消 4退款中 5已退款
    WAIT_PAY(1, "待支付"),
    PAYED(2, "已支付"),
    CANCEL(3, "已取消"),
    REFUNDING(4, "退款中"),
    REFUNDED(5, "已退款");
    private Integer code;
    private String info;

    PayOrderStatusEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public static List<PayOrderStatusEnum> getAllStatus() {
        return Arrays.stream(PayOrderStatusEnum.values()).collect(Collectors.toList());
    }

    public static List<PayOrderStatusEnum> getNoPayStatus() {
        return Arrays.stream(PayOrderStatusEnum.values())
            .filter(f -> WAIT_PAY.getCode().equals(f.getCode())).collect(Collectors.toList());
    }

    public static List<Integer> ableCancelStatus(){
        return Arrays.stream(PayOrderStatusEnum.values())
            .filter(f -> WAIT_PAY.getCode().equals(f.getCode()) || CANCEL.getCode().equals(f.getCode()))
            .map(PayOrderStatusEnum::getCode)
            .collect(Collectors.toList());
    }
}
