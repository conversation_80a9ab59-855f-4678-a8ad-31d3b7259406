package com.jxw.shufang.order.domain.convert;

import com.jxw.shufang.order.domain.bo.OrderProductBo;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteProductBoConvertOrderProductBo extends BaseMapper<OrderProductBo, RemoteProductBo> {
}




