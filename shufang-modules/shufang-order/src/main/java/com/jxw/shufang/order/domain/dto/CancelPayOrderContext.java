package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.common.pay.domain.dto.request.CloseOrderRequestDTO;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentMerchantConfigDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/20 17:02
 * @Version 1
 * @Description
 */
@Data
public class CancelPayOrderContext {
    private RemoteStudentMerchantConfigDTO configDTO;
    private OrderPayRecord orderPayRecord;
    private Order order;

    public static CancelPayOrderContext of(RemoteStudentMerchantConfigDTO configDTO, OrderPayRecord orderPayRecord, Order order) {
        CancelPayOrderContext cancelPayOrder = new CancelPayOrderContext();
        cancelPayOrder.setConfigDTO(configDTO);
        cancelPayOrder.setOrderPayRecord(orderPayRecord);
        cancelPayOrder.setOrder(order);
        return cancelPayOrder;
    }

    public CloseOrderRequestDTO buildCloseOrderRequestDTO() {
        if (configDTO != null && orderPayRecord != null) {
            return new CloseOrderRequestDTO(configDTO.getAppId(), orderPayRecord.getTransactionNo());
        }
        return null;
    }
}
