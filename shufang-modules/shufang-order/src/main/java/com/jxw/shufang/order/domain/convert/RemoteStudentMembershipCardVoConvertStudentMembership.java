package com.jxw.shufang.order.domain.convert;

import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.order.domain.vo.StudentMembershipCardVo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteStudentMembershipCardVoConvertStudentMembership
    extends BaseMapper<StudentMembershipCardVo, RemoteStudentMembershipCardVo> {
}




