package com.jxw.shufang.order.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.order.domain.ProductTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 产品模板管理视图对象 pms_product_template
 *
 * @date 2024-06-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductTemplate.class)
public class ProductTemplateVo extends StudentBaseTemplateAuthVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品模板信息-主键
     */
    @ExcelProperty(value = "产品模板ID")
    private Long productTemplateId;

    /**
     * 产品模板名称
     */
    @ExcelProperty(value = "产品模板名称")
    private String productTemplateName;

    /**
     * 产品模板描述
     */
    @ExcelProperty(value = "产品模板描述")
    private String productTemplateDesc;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    @ExcelProperty(value = "模板类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "product_template_type")
    private Integer productTemplateType;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门ID")
    private Long createDept;

    /**
     * 状态 0-上架 1-下架
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "product_template_status")
    private Integer status;

    /**
     * 关联的资源ID集合
     */
    private List<Long> resIds;

    /**
     * 涉及的学段名称集合
     */
    private Set<String> stageNames = new HashSet<>();

    /**
     * 会员卡名称列表
     */
    private Set<String> memberNames = new HashSet<>();

    /**
     * 添加学段名称集合
     * @param stageName
     */
    public void addStageName(String stageName) {
        if (ObjectUtil.isEmpty(stageName)) return;
        stageNames.add(stageName);
    }

    /**
     * 添加会员卡名称
     */
    public void addMemberName(String memberName) {
        if (ObjectUtil.isEmpty(memberName)) return;
        memberNames.add(memberName);
    }
}
