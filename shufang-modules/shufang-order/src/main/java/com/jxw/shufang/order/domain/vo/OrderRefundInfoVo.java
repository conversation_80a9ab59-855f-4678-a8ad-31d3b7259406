package com.jxw.shufang.order.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: cyj
 * @date: 2025/6/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderRefundInfoVo {
    /**
     * 允许的退款方式
     */
    private List<Integer> payModeList;
    /**
     * 最高可退金额
     */
    private BigDecimal canRefundAmount;
}
