package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.RechargeOrderBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;

import java.util.Collection;
import java.util.List;

/**
 * 代理商充值订单记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IRechargeOrderService {

    /**
     * 查询代理商充值订单记录
     */
    RechargeOrderVo queryById(Long orderId);

    /**
     * 查询代理商充值订单记录列表
     */
    TableDataInfo<RechargeOrderVo> queryPageList(RechargeOrderBo bo, PageQuery pageQuery);

    /**
     * 查询代理商充值订单记录列表
     */
    List<RechargeOrderVo> queryList(RechargeOrderBo bo);

    /**
     * 新增代理商充值订单记录
     */
    Boolean insertByBo(RechargeOrderBo bo);

    /**
     * 修改代理商充值订单记录
     */
    Boolean updateByBo(RechargeOrderBo bo);

    /**
     * 校验并批量删除代理商充值订单记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
