package com.jxw.shufang.order.interfaces.impl;


import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.interfaces.AccountTransferProcessor;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@RequiredArgsConstructor
public class BranchAccountTransfer implements AccountTransferProcessor {
    @DubboReference
    private RemoteBranchService remoteBranchService;


    @Override
    public Boolean support(AccountTradeTypeEnum typeEnum) {
        return AccountTradeTypeEnum.BRANCH.equals(typeEnum);
    }

    @Override
    public void to(AccountInfo accountInfo, OrderInfo orderInfo) {
        RemoteBranchVo vo = remoteBranchService.selectBranchById(accountInfo.getAccountId());
        if (ObjectUtils.isEmpty(vo)) {
            throw new ServiceException("转入账号不存在");
        }
        remoteBranchService.transfer(vo.getBranchId(), orderInfo.getAmount());

    }

    @Override
    public void from(AccountInfo accountInfo, OrderInfo orderInfo) {
        RemoteBranchVo vo = remoteBranchService.selectBranchById(accountInfo.getAccountId());
        if (ObjectUtils.isEmpty(vo)) {
            throw new ServiceException("转入账号不存在");
        }
        if (vo.getRemainTime() >= orderInfo.getAmount()) {
            remoteBranchService.transfer(vo.getBranchId(), -orderInfo.getAmount());
        } else {
            throw new ServiceException("账户余额不足");
        }
    }
}
