package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.RechargeOrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.RechargeOrder;
import com.jxw.shufang.order.domain.bo.RechargeOrderBo;
import com.jxw.shufang.order.domain.bo.RechargeOrderProductBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderOperateVo;
import com.jxw.shufang.order.domain.vo.RechargeOrderProductVo;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;
import com.jxw.shufang.order.mapper.RechargeOrderMapper;
import com.jxw.shufang.order.service.IRechargeOrderOperateService;
import com.jxw.shufang.order.service.IRechargeOrderProductService;
import com.jxw.shufang.order.service.IRechargeOrderService;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理商充值订单记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class RechargeOrderServiceImpl implements IRechargeOrderService, BaseService {

    private final RechargeOrderMapper baseMapper;

    private final IRechargeOrderProductService rechargeOrderProductService;

    private final IRechargeOrderOperateService rechargeOrderOperateService;

    @DubboReference
    private RemoteDeptService remoteDeptService;
    @DubboReference
    private RemoteBranchService remoteBranchService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询代理商充值订单记录
     */
    @Override
    public RechargeOrderVo queryById(Long orderId) {
        RechargeOrderVo orderVo = baseMapper.selectVoById(orderId);
        if (orderVo == null) {
            throw new ServiceException("订单不存在");
        }
        //查询订单最新操作
        RechargeOrderOperateVo orderOperateVo = rechargeOrderOperateService.queryById(orderVo.getOrderOperateId());
        if (orderOperateVo == null) {
            throw new ServiceException("订单操作记录不存在");
        }
        orderVo.setOrderStatus(orderOperateVo.getOrderOperateStatus());
        if (Objects.equals(orderOperateVo.getOrderOperateStatus(), RechargeOrderStatusEnum.PAYED.getCode())) {
            orderVo.setPaymentType(orderOperateVo.getPaymentType());
            orderVo.setPaymentAmount(orderOperateVo.getPaymentAmount());
            orderVo.setOpCreateTime(orderOperateVo.getCreateTime());
        }
        //查询订单产品详情
        RechargeOrderProductVo productVo = rechargeOrderProductService.queryByOrderId(orderId);
        if (productVo == null) {
            throw new ServiceException("订单产品信息不存在");
        }
        orderVo.setProductNums(productVo.getProductNums());
        orderVo.setProductDays(productVo.getProductDays());
        orderVo.setProductPrice(productVo.getProductPrice());
        orderVo.setPreferentialPrice(productVo.getPreferentialPrice());
        orderVo.setAmount(productVo.getAmount());
        if (orderVo.getRechargeType() == 0) {
            RemoteDeptVo remoteDeptVo = remoteDeptService.selectDeptInfoById(orderVo.getRechargeId());
            if (remoteDeptVo != null) {
                orderVo.setRechargeName(remoteDeptVo.getDeptName());
                if (remoteDeptVo.getLeader() != null && remoteDeptVo.getLeader() > 0) {
                    LoginUser loginUser = remoteUserService.getSpecifyAgentUserInfo(remoteDeptVo.getLeader());
                    orderVo.setAdminUserName(Optional.ofNullable(loginUser).map(LoginUser::getUsername).orElse(""));
                    orderVo.setAdminNickName(Optional.ofNullable(loginUser).map(LoginUser::getNickname).orElse(""));
                }
            }
        } else {
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchById(orderVo.getRechargeId());
            if (remoteBranchVo != null) {
                orderVo.setRechargeName(remoteBranchVo.getBranchName());
                Set<Long> branchDeptIds = new HashSet<>();
                branchDeptIds.add(remoteBranchVo.getCreateDept());
                Map<Long, RemoteUserVo> branchAdmin = remoteUserService.getBranchAdmin(branchDeptIds);
                if (branchAdmin != null && !branchAdmin.isEmpty()) {
                    orderVo.setAdminUserName(Optional.ofNullable(branchAdmin.get(remoteBranchVo.getCreateDept())).map(RemoteUserVo::getUserName).orElse(""));
                    orderVo.setAdminNickName(Optional.ofNullable(branchAdmin.get(remoteBranchVo.getCreateDept())).map(RemoteUserVo::getNickName).orElse(""));
                }
            }
        }
        //获取经办人组织名称
        String deptName = remoteDeptService.selectDeptNameByIds(String.valueOf(orderVo.getHandlingDeptId()));
        orderVo.setHandlingDeptName(deptName);
        return orderVo;
    }

    /**
     * 查询代理商充值订单记录列表
     */
    @Override
    public TableDataInfo<RechargeOrderVo> queryPageList(RechargeOrderBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<RechargeOrder> lqw = buildPageQueryWrapper(bo);
        Page<RechargeOrderVo> result = baseMapper.selectPageList(pageQuery.build(), lqw);
        putRechargeInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * 放置被充值用户信息
     */
    public void putRechargeInfo(List<RechargeOrderVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> deptIds = list.stream().filter(s -> s.getRechargeType() == 0).map(RechargeOrderVo::getRechargeId)
            .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deptIds)) {
            RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
            remoteDeptBo.setDeptIdList(deptIds);
            List<RemoteDeptVo> remoteDeptVos = remoteDeptService.getDeptListIgnore(remoteDeptBo);
            if (CollUtil.isNotEmpty(remoteDeptVos)) {
                Map<Long, RemoteUserVo> userMap = new HashMap<>();
                //获取代理商负责人
                List<Long> userIds = remoteDeptVos.stream().map(RemoteDeptVo::getLeader).filter(s -> s != null && s > 0).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(userIds)) {
                    RemoteUserBo remoteUserBo = new RemoteUserBo();
                    remoteUserBo.setUserIds(userIds);
                    List<RemoteUserVo> remoteUserVos = remoteUserService.queryUserList(remoteUserBo, true);
                    userMap = remoteUserVos.stream().collect(Collectors.toMap(RemoteUserVo::getUserId, s -> s));
                }

                Map<Long, RemoteDeptVo> deptMap = remoteDeptVos.stream().collect(Collectors.toMap(RemoteDeptVo::getDeptId, s -> s));
                Map<Long, RemoteUserVo> finalUserMap = userMap;
                list.forEach(s -> {
                    if (s.getRechargeType() == 0 && deptMap.containsKey(s.getRechargeId())) {
                        RemoteDeptVo tmpDept = deptMap.get(s.getRechargeId());
                        s.setRechargeName(tmpDept.getDeptName());
                        if (tmpDept.getLeader() != null) {
                            s.setAdminUserName(Optional.ofNullable(finalUserMap.get(tmpDept.getLeader())).map(RemoteUserVo::getUserName).orElse(""));
                            s.setAdminNickName(Optional.ofNullable(finalUserMap.get(tmpDept.getLeader())).map(RemoteUserVo::getNickName).orElse(""));
                        }
                    }
                });
            }
        }

        List<Long> branchIds = list.stream().filter(s -> s.getRechargeType() == 1).map(RechargeOrderVo::getRechargeId)
            .distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(branchIds)) {
            RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
            remoteBranchBo.setBranchIds(branchIds);
            List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo,true);
            if (CollUtil.isNotEmpty(remoteBranchVos)) {

                Map<Long, RemoteBranchVo> branchMap = remoteBranchVos.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, s -> s));
                list.forEach(s -> {
                    if (s.getRechargeType() == 1 && branchMap.containsKey(s.getRechargeId())) {
                        RemoteBranchVo tmpBranch = branchMap.get(s.getRechargeId());
                        s.setRechargeName(tmpBranch.getBranchName());
                        s.setAdminUserName(Optional.ofNullable(tmpBranch.getAdminUserName()).orElse(""));
                        s.setAdminNickName(Optional.ofNullable(tmpBranch.getAdminNickName()).orElse(""));
                    }
                });
            }
        }
    }


    private QueryWrapper<RechargeOrder> buildPageQueryWrapper(RechargeOrderBo bo) {
//        Map<String, Object> params = bo.getParams();
        QueryWrapper<RechargeOrder> lqw = Wrappers.query();
        lqw.eq(bo.getHandlingDeptId() != null, "t.handling_dept_id", bo.getHandlingDeptId());
        lqw.eq(bo.getRechargeId() != null, "t.recharge_id", bo.getRechargeId());
        lqw.eq(bo.getRechargeType() != null, "t.recharge_type", bo.getRechargeType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), "t.order_no", bo.getOrderNo());
        lqw.eq(bo.getHandlingDate() != null, "t.handling_date", bo.getHandlingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHandlingPerson()), "t.handling_person", bo.getHandlingPerson());

        lqw.eq(bo.getOrderStatus() != null, "oo.order_operate_status", bo.getOrderStatus());
        lqw.between(bo.getCreateOrderStartTime() != null && bo.getCreateOrderEndTime() != null,
            "t.create_time", bo.getCreateOrderStartTime(), bo.getCreateOrderEndTime());

        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq("t.del_flag", bo.getDelFlag());
        } else {
            bo.setDelFlag(UserConstants.DEL_FLAG_NO);
            lqw.eq("t.del_flag", UserConstants.DEL_FLAG_NO);
        }
        if (StringUtils.isNotBlank(bo.getRechargeName())) {
            List<Long> rechargeIds = new ArrayList<>();
            boolean deptFlag = bo.getRechargeType() == null || bo.getRechargeType() == 0;
            boolean branchFlag = bo.getRechargeType() == null || bo.getRechargeType() == 1;
            if (deptFlag) {
                RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
                remoteDeptBo.setDeptName(bo.getRechargeName());
                remoteDeptBo.setIsStore(false);
                List<RemoteDeptVo> deptVoList = remoteDeptService.getDeptList(remoteDeptBo);
                if (CollUtil.isNotEmpty(deptVoList)) {
                    rechargeIds.addAll(deptVoList.stream().map(RemoteDeptVo::getDeptId).toList());
                }
            }
            if (branchFlag) {
                RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
                remoteBranchBo.setBranchName(bo.getRechargeName());
                remoteBranchBo.setDeptParentId(bo.getHandlingDeptId());
                List<RemoteBranchVo> branchVoList = remoteBranchService.selectBranchList(remoteBranchBo);
                if (CollUtil.isNotEmpty(branchVoList)) {
                    rechargeIds.addAll(branchVoList.stream().map(RemoteBranchVo::getBranchId).toList());
                }
            }
            if (CollUtil.isNotEmpty(rechargeIds)) {
                lqw.in("t.recharge_id", rechargeIds);
            } else {
                lqw.in("t.recharge_id", -1);
            }
        }
        return lqw;
    }

    private void handleQueryParam(RechargeOrderBo record) {
//        if (record.getHandlingDeptId() == null) {
//            record.setHandlingDeptId(LoginHelper.getDeptId());
//        }
    }

    /**
     * 查询代理商充值订单记录列表
     */
    @Override
    public List<RechargeOrderVo> queryList(RechargeOrderBo bo) {
        LambdaQueryWrapper<RechargeOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<RechargeOrder> buildQueryWrapper(RechargeOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RechargeOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, RechargeOrder::getOrderId, bo.getOrderId());
        lqw.eq(bo.getRechargeId() != null, RechargeOrder::getRechargeId, bo.getRechargeId());
        lqw.eq(bo.getRechargeType() != null, RechargeOrder::getRechargeType, bo.getRechargeType());
        lqw.eq(bo.getHandlingDeptId() != null, RechargeOrder::getHandlingDeptId, bo.getHandlingDeptId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), RechargeOrder::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getHandlingDate() != null, RechargeOrder::getHandlingDate, bo.getHandlingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHandlingPerson()), RechargeOrder::getHandlingPerson, bo.getHandlingPerson());
        lqw.in(!CollectionUtils.isEmpty(bo.getOrderIdList()), RechargeOrder::getOrderId,bo.getOrderIdList());
        return lqw;
    }

    /**
     * 新增代理商充值订单记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(RechargeOrderBo bo) {
        RechargeOrder add = MapstructUtils.convert(bo, RechargeOrder.class);
        validEntityBeforeSave(add);
        add.setHandlingDeptId(LoginHelper.getDeptId());
        add.setOrderNo(String.valueOf(System.currentTimeMillis()));
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag || add.getOrderId() == null) {
            throw new ServiceException("订单创建失败");
        }
        Long orderId = add.getOrderId();
        //创建订单商品信息
        RechargeOrderProductBo orderProduct = bo.getOrderProduct();
        orderProduct.setOrderId(orderId);
        orderProduct.setDeptId(add.getHandlingDeptId());
        rechargeOrderProductService.insertByBo(orderProduct);

        //创建订单操作数据
        Long orderOperateId = rechargeOrderOperateService.initRechargeOrderOperate(orderId);

        //更新订单操作id
        RechargeOrder update = new RechargeOrder();
        update.setOrderId(orderId);
        update.setOrderOperateId(orderOperateId);
        int i = baseMapper.updateById(update);
        if (i <= 0) {
            throw new ServiceException("订单操作id更新失败");
        }
        return true;
    }

    /**
     * 修改代理商充值订单记录
     */
    @Override
    public Boolean updateByBo(RechargeOrderBo bo) {
        RechargeOrder update = MapstructUtils.convert(bo, RechargeOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RechargeOrder entity) {
        //做一些数据校验,如唯一约束
        if (null == entity) {
            throw new ServiceException("订单创建失败");
        }
    }

    /**
     * 批量删除代理商充值订单记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
