package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/7/11 11:03
 * @Version 1
 * @Description
 */
@Data
public class CheckOrderPriceDTO {
    private RemoteProductVo remoteProductVo;
    /**
     * 门店优惠价格
     */
    private BigDecimal preferentialPrice;
    /**
     * 会员积分优惠金额
     */
    private BigDecimal studentPreferentialAmount;
    private Long studentId;
}
