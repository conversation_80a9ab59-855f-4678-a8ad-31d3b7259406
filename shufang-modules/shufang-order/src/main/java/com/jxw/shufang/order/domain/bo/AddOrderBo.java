package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/12 15:30
 * @Version 1
 * @Description 添加订单的参数
 */
@Data
public class AddOrderBo {

    /**
     * 课程开始时间 yyyy-mm-dd
     */
    private String courseStartTime;

    @NotNull(message = "会员id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long studentId;

    @NotNull(message = "会员类型不能为空", groups = {AddGroup.class})
    private Long studentTypeId;

    private String handlingPerson;

    /**
     * 0为 购买新卡 1为 旧卡升级
     */
    private Integer orderType;

    /**
     * 如果订单类型为旧卡升级  值为旧卡订单ID
     */
    private Long orderRelationId;

    @NotNull(message = "销售顾问id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long salesPerson;

    @NotEmpty(message = "订单产品信息不能为空", groups = {AddGroup.class})
    private List<AddOrderProductBo> orderProductInfoList;
}
