package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/7/4 14:55
 * @Version 1
 * @Description 支付结果枚举
 */
public enum PayResultEnum {
    WAIT_PAY(0, "待支付"),
    SUCCESS(1, "支付成功"),
    EXCEPTION(2, "支付异常");
    private Integer code;
    private String message;

    PayResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
