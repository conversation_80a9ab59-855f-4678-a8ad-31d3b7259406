package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.RechargeOrderProductBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderProductVo;

import java.util.Collection;
import java.util.List;

/**
 * 代理商订单产品详情Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IRechargeOrderProductService {

    /**
     * 查询代理商订单产品详情
     */
    RechargeOrderProductVo queryById(Long orderInfoId);

    /**
     * 查询代理商订单产品详情列表
     */
    TableDataInfo<RechargeOrderProductVo> queryPageList(RechargeOrderProductBo bo, PageQuery pageQuery);

    /**
     * 查询代理商订单产品详情列表
     */
    List<RechargeOrderProductVo> queryList(RechargeOrderProductBo bo);

    /**
     * 新增代理商订单产品详情
     */
    Boolean insertByBo(RechargeOrderProductBo bo);

    /**
     * 修改代理商订单产品详情
     */
    Boolean updateByBo(RechargeOrderProductBo bo);

    /**
     * 校验并批量删除代理商订单产品详情信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询代理商订单产品详情
     */
    RechargeOrderProductVo queryByOrderId(Long orderId);

}
