package com.jxw.shufang.order.domain.vo;

import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 账户流水 （扣款退费充值记录）视图对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
public class AccountVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer remainTime;
    private AccountTradeTypeEnum accountTradeTypeEnum;

    private Long accountId;

}
