package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/7/4 10:55
 * @Version 1
 * @Description 异常支付枚举
 */
public enum AbnormalPayEnum {
    NORMAL_PAY(0, "正常支付"),
    ABNORMAL_PAY(1, "异常支付");
    private Integer code;
    private String message;
    AbnormalPayEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
    public static AbnormalPayEnum getByCode(Integer code) {
        for (AbnormalPayEnum value : AbnormalPayEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NORMAL_PAY;
    }
}
