package com.jxw.shufang.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchConfigService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchPayModelConfigVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.OrderOperateReviewStatus;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.enums.PreferentialModifyTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataBaseHelper;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.order.domain.bo.*;
import com.jxw.shufang.order.domain.dto.*;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.enums.*;
import com.jxw.shufang.order.mapper.OrderMapper;
import com.jxw.shufang.order.service.*;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.student.api.*;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentPreferentialBo;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentTypeBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentSimpleVO;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentTypeVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.model.RoleDTO;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 订单Service业务层处理
 * @date 2024-02-27
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
@Slf4j
public class OrderServiceImpl implements IOrderService, BaseService {

    private final OrderMapper baseMapper;

    private final IOrderProductInfoService orderProductInfoService;

    private final IOrderOperateService orderOperateService;

    private final IStudentMembershipCardService studentMembershipCardService;

    private final IOrderOperateApplyRecordService orderOperateApplyRecordService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteStudentConsultantRecordService remoteStudentConsultantRecordService;


    @DubboReference
    private RemoteProductService remoteProductService;

    @DubboReference
    private RemoteStudentTypeService remoteStudentTypeService;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteStudentPreferentialService remoteStudentPreferentialService;

    @DubboReference
    private final RemoteBranchConfigService remoteBranchConfigService;

    @Resource
    private OrderPayRecordDao orderPayRecordDao;

    @DubboReference
    private final RemoteFileService remoteFileService;

    @DubboReference
    private final RemoteBranchService remoteBranchService;

    private final IOrderAccountService iOrderAccountService;

    /**
     * 查询订单
     */
    @Override
    public OrderVo queryById(Long orderId) {
        OrderVo orderVo = queryById(orderId, true);

        if (ObjectUtils.isEmpty(orderVo)) {
            return null;
        }
        if (OrderTypeEnum.OLD_CARD_UPGRADE.getCode().equals(orderVo.getOrderType())) {
            OrderVo oldOrder = queryById(orderVo.getOrderRelationId(), false);
            orderVo.setOldOrder(oldOrder);
        }
        return orderVo;
    }

    @Override
    public OrderVo queryById(Long orderId, Boolean putStudent) {
        OrderVo orderVo = baseMapper.selectVoById(orderId);
        Order order = baseMapper.selectById(orderId);
        //查询订单操作历史
        OrderOperateBo orderOperateBo = new OrderOperateBo();
        orderOperateBo.setOrderId(orderId);
        List<OrderOperateVo> orderOperateVos = orderOperateService.queryList(orderOperateBo);
        if (CollUtil.isEmpty(orderOperateVos)) {
            throw new ServiceException("订单操作记录不存在");
        }

        for (OrderOperateVo orderOperateVo : orderOperateVos) {
            orderOperateVo.setOrderWorkflowStatus(OrderWorkflowStatusEnum
                .getWorkflowStatus(orderOperateVo.getOrderOperateStatus(), orderOperateVo.getReviewStatus()));
        }

        //如果存在已支付，拿出来
        Optional<OrderOperateVo> paidOperate = orderOperateVos.stream()
            .filter(item -> OrderStatusEnum.PAYED.getCode().equals(item.getOrderOperateStatus())
                || OrderStatusEnum.PENDING_PAY.getCode().equals(item.getOrderOperateStatus()))
            .findFirst();
        paidOperate.ifPresent(orderVo::setPaymentOperate);

        //如果存在已退款，拿出来
        Optional<OrderOperateVo> refundOperate = orderOperateVos.stream().filter(item -> OrderStatusEnum.REFUNDED.getCode().equals(item.getOrderOperateStatus())
            || OrderStatusEnum.REFUNDING.getCode().equals(item.getOrderOperateStatus())).findFirst();
        if (refundOperate.isPresent()) {
            OrderOperateVo orderOperateVo = refundOperate.get();
            //查询退款申请人
            Long createBy = orderOperateVo.getCreateBy();
            String name = remoteUserService.selectNicknameById(createBy);
            orderOperateVo.setCreateByName(name);
            orderVo.setRefundOperate(orderOperateVo);
        }

        //订单当前状态
        Optional<OrderOperateVo> first = orderOperateVos.stream().filter(e -> orderVo.getOrderOperateId().equals(e.getOrderOperateId())).findFirst();
        if (first.isPresent()) {
            OrderOperateVo presentOrderOperate = first.get();
            orderVo.setOrderStatus(presentOrderOperate.getOrderOperateStatus());
            orderVo.setOrderOperate(presentOrderOperate);
            // 设置退款审核相关属性
            if (OrderStatusEnum.REFUNDING.getCode().equals(presentOrderOperate.getOrderOperateStatus())
                && OrderOperateReviewStatus.WAIT_REVIEW.getCode().equals(presentOrderOperate.getReviewStatus())) {
                RefundAuditNode node = RefundAuditNode.fromCode(presentOrderOperate.getReviewNode());
                presentOrderOperate.setReviewable(node
                    .checkRole(LoginHelper.getRoles().stream().map(RoleDTO::getRoleId).collect(Collectors.toList())));
                if (presentOrderOperate.getReviewable()) {
                    RefundAuditNode.AuditPolicy policy =
                        node.getPolicy(PayModeEnum.fromCode(presentOrderOperate.getPayMode()));
                    presentOrderOperate.setPassNeedVoucher(policy.isPassNeedVoucher());
                    presentOrderOperate.setRejectNeedVoucher(policy.isRejectNeedVoucher());
                    presentOrderOperate.setVoucherName(policy.getVoucherName());
                }
            } else if (OrderStatusEnum.REFUNDING.getCode().equals(presentOrderOperate.getOrderOperateStatus())
                && OrderOperateReviewStatus.REVIEWED.getCode().equals(presentOrderOperate.getReviewStatus())) {
                // 执行店长操作退款待确认订单
                if (LoginHelper.isBranchStaff() && LoginHelper.isExecutiveStoreManager()) {
                    presentOrderOperate.setReviewable(true);
                }
            } else {
                presentOrderOperate.setReviewable(false);
            }
        }

        //查询订单详情
        OrderProductInfoBo orderProductInfoBo = new OrderProductInfoBo();
        orderProductInfoBo.setOrderId(orderId);
        List<OrderProductInfoVo> orderProductInfoVos = this.setOrderProductInfoList(orderVo, orderProductInfoBo);
        //计算实际金额
        BigDecimal preferentialAmount = BigDecimal.ZERO;
        BigDecimal studentPreferentialAmount = BigDecimal.ZERO;
        for (OrderProductInfoVo orderProductInfoVo : orderProductInfoVos) {
            if (orderProductInfoVo.getPreferentialPrice() != null) {
                preferentialAmount = preferentialAmount.add(orderProductInfoVo.getPreferentialPrice());
            }
            if (orderProductInfoVo.getStudentPreferentialPrice() != null) {
                studentPreferentialAmount =
                    studentPreferentialAmount.add(orderProductInfoVo.getStudentPreferentialPrice());
            }
        }
        // 改为实时查询
        orderVo.setActualPayPrice(orderProductInfoService.getOrderAblePayAmountByOrder(order.getOrderId(), orderVo.getOrderRelationId()));
        orderVo.setPreferentialPriceSum(preferentialAmount);
        orderVo.setStudentPreferentialPriceSum(studentPreferentialAmount);

        if (paidOperate.isPresent()) {
            OrderOperateVo paidOrderOperate = paidOperate.get();
            // 计算订单产品总剩余时间，
            if (OrderStatusEnum.PAYED.getCode().equals(first.get().getOrderOperateStatus())
                || OrderStatusEnum.REFUNDING.getCode().equals(first.get().getOrderOperateStatus())) {
                calUsedDays(orderVo, orderProductInfoVos.get(0), paidOrderOperate, new Date());
            } else if (OrderStatusEnum.REFUNDED.getCode().equals(first.get().getOrderOperateStatus())) {
                calUsedDays(orderVo, orderProductInfoVos.get(0), paidOrderOperate, first.get().getCreateTime());
            }
        }

        //会员信息
        List<OrderVo> orderVos = CollUtil.newArrayList(orderVo);
        if (Boolean.TRUE.equals(putStudent)) {
            putStudentInfo(orderVos);
        }

        // 查询订单操作审核记录 店长及以上查询所有，以下仅查看自己创建的记录
        List<Long> orderOperateIdList;
        if (LoginHelper.isBranchStaff() && !LoginHelper.isExecutiveStoreManager()) {
            Long userId = LoginHelper.getLoginUser().getUserId();
            orderOperateIdList = orderOperateVos.stream().filter(operate -> userId.equals(operate.getCreateBy()))
                .map(OrderOperateVo::getOrderOperateId).collect(Collectors.toList());
        } else {
            orderOperateIdList =
                orderOperateVos.stream().map(OrderOperateVo::getOrderOperateId).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(orderOperateIdList)) {
            Map<Long, String> operatorNameMap = new HashMap<>();
            List<OrderOperateApplyRecordVo> orderOperateApplyRecordVos =
                orderOperateApplyRecordService.listByOperateIds(orderOperateIdList);
            orderOperateApplyRecordVos.forEach(record -> {
                record.setOrderWorkflowStatus(OrderWorkflowStatusEnum.getWorkflowStatus(
                    record.getOrderOperateStatus(),
                    record.getReviewStatus()));
                record.setOperatorName(operatorNameMap.computeIfAbsent(record.getCreateBy(),
                    name -> remoteUserService.selectNicknameById(record.getCreateBy())));
                record.setVoucherUrl(null != record.getVoucher()
                    ? remoteFileService.selectUrlByIds(String.valueOf(record.getVoucher())) : "");
                if (Integer.valueOf(RefundAuditNode.COMPANY_FINANCE_AUDIT.getCode()).equals(record.getReviewNode())
                    && OrderOperateReviewStatus.REVIEWED.getCode().equals(record.getReviewStatus())
                    && StringUtils.isNotEmpty(record.getVoucherUrl())) {
                    orderVo.setRefundVoucherUrl(record.getVoucherUrl());
                }
            });
            orderVo.setOrderOperateApplyRecords(orderOperateApplyRecordVos);
        }

        // 获取订单操作支付记录
        OrderPayTimeAmountContext orderPayTimeAmountContext = this.getPayRecordContext(order);
        Date payTime = orderPayTimeAmountContext.getPayTime(orderId);
        BigDecimal payAmount = orderPayTimeAmountContext.getPayAmount(orderId);

        List<OrderPayRecordVO> payRecords = this.getPayRecords(orderId);
        orderVo.setPayRecordList(payRecords);
        orderVo.setCurrentPayRecord(payRecords.stream().reduce((a1, a2) -> a2).orElse(null));
        orderVo.setPayVoucherPath(this.getOfflinePayFilePath(orderVo.getOfflinePayVoucherFileId()));
        orderVo.setPayTime(payTime);
        orderVo.setPaidAmount(payAmount);
        orderVo.setPendingPayAmount(this.getPendingPayAmount(orderVo.getActualPayPrice(),payAmount));
        return orderVo;
    }

    /**
     * 获取订单支付记录
     * @param order
     * @return
     */
    private OrderPayTimeAmountContext getPayRecordContext(Order order) {
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryOrderPayRecordByOrderIds(Collections.singletonList(order.getOrderId()));
        List<OrderOperate> orderOperateList = orderOperateService.queryOperateOrderListByOrderIds(Collections.singletonList(order.getOrderId()));
        List<Order> orderList = Collections.singletonList(order);
        OrderPayTimeAmountContext payRecordContext = OrderPayTimeAmountContext.of(orderOperateList, orderPayRecords, orderList);
        return payRecordContext.execute();
    }


    private String getOfflinePayFilePath(String offlinePayVoucherFileId) {
        if (StringUtils.isEmpty(offlinePayVoucherFileId)) {
            return null;
        }
        return remoteFileService.selectUrlByIds(offlinePayVoucherFileId);
    }

    private List<OrderProductInfoVo> setOrderProductInfoList(OrderVo orderVo, OrderProductInfoBo orderProductInfoBo) {
        List<OrderProductInfoVo> orderProductInfoVos = orderProductInfoService.queryList(orderProductInfoBo);
        if (CollUtil.isEmpty(orderProductInfoVos)) {
            throw new ServiceException("订单商品信息不存在");
        }
        // 兼容历史订单产品无原始价格情况
        orderProductInfoVos.stream()
            .filter(f -> null == f.getOriginProductPrice())
            .forEach(orderProductInfoVo -> orderProductInfoVo.setOriginProductPrice(orderProductInfoVo.getProductPrice()));
        orderVo.setOrderProductInfoList(orderProductInfoVos);
        return orderProductInfoVos;
    }


    private BigDecimal getPendingPayAmount(BigDecimal actualPayPrice, BigDecimal paidAmount) {
        return actualPayPrice.subtract(paidAmount);
    }

    private List<OrderPayRecordVO> getPayRecords(Long orderId) {
        List<PayOrderStatusEnum> orderStatusEnums = List.of(PayOrderStatusEnum.WAIT_PAY, PayOrderStatusEnum.PAYED);
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(orderId, orderStatusEnums);
        if (CollUtil.isEmpty(orderPayRecords)) {
            return Collections.emptyList();
        }

        return orderPayRecords.stream()
            .map(this::buildOrderPayRecord)
            .toList();
    }

    private OrderPayRecordVO buildOrderPayRecord(OrderPayRecord orderPayRecord) {
        OrderPayRecordVO paymentRecord = new OrderPayRecordVO();
        paymentRecord.setOrderId(orderPayRecord.getOrderId());
        paymentRecord.setOrderPayNo(orderPayRecord.getOrderPayNo());
        paymentRecord.setPaidAmount(orderPayRecord.getAmount());
        paymentRecord.setPayAbleAmount(orderPayRecord.getPayAbleAmount());
        paymentRecord.setPaymentStatus(orderPayRecord.getPaymentStatus());
        paymentRecord.setPaymentRemark(orderPayRecord.getPaymentDesc());
        paymentRecord.setDepositAmountFlag(DepositAmountEnum.DEPOSIT.getCode().equals(orderPayRecord.getDepositAmountFlag()));
        paymentRecord.setPaymentTime(orderPayRecord.getPaymentTime());
        return paymentRecord;
    }

    private void calUsedDays(OrderVo orderVo, OrderProductInfoVo orderProductInfoVo, OrderOperateVo orderOperateVo, Date endTime) {
        long remainingDays = 0L;
        long usedDays = 0L;
        if (orderOperateVo.getCreateTime() == null) {
            if (OrderStatusEnum.PAYED.getCode().equals(orderVo.getOrderStatus())) {
                throw new ServiceException("支付时间为空");
            } else {
                throw new ServiceException("退款时间为空");
            }
        }

        Long productValidDays = orderProductInfoVo.getProductValidDays();
        ProcessProductAccountDays processProductAccountDays = ProcessProductAccountDays.ofByRefund(productValidDays,
            orderProductInfoVo.getProductValidTimeLimit(),
            orderVo.getCourseStartTime());
        ProcessProductAccountDays.RefundDaysDTO refundDaysDTO = processProductAccountDays.calculateRemainDaysByRefund(new Date());
        orderVo.setRemainingDays(refundDaysDTO.getRefundDays());
        orderVo.setUsedDays(refundDaysDTO.getUseDays());
    }


    /**
     * 查询订单列表
     */
    @Override
    public TableDataInfo<OrderVo> queryPageList(OrderBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        Page<OrderVo> result = selectPageList(pageQuery, lqw);
        putStudentInfo(result.getRecords());
        putProductInfo(result.getRecords());
        putStudentTypeInfo(result.getRecords());
        return TableDataInfo.build(result);
    }

    /**
     * selectPageList
     *
     * @param pageQuery
     * @param lqw
     * @return
     */
    private Page<OrderVo> selectPageList(PageQuery pageQuery, QueryWrapper<Order> lqw) {
        Page<OrderVo> orderVoPage = baseMapper.selectPageList(pageQuery.build(), lqw);
        if(CollectionUtil.isEmpty(orderVoPage.getRecords())){
            return orderVoPage;
        }
        List<Long> orderIds = orderVoPage.getRecords().stream().map(OrderVo::getOrderId).toList();
        List<Order> orderList = queryOrderListByOrderId(orderIds);

        // 查询订单所有的操作记录
        OrderPayTimeAmountContext payRecordContext = this.getPayRecordContext(orderIds, orderList);

        for (OrderVo record : orderVoPage.getRecords()) {
            BigDecimal productPriceSum = record.getProductPriceSum();
            this.setOrderPrice(record, productPriceSum);
            record.setInstallmentPayDeadlineTime(this.setInstallmentPayDeadlineTime(record));
            record.setPayTime(payRecordContext.getPayTime(record.getOrderId()));
            record.setPaidAmount(payRecordContext.getPayAmount(record.getOrderId()));
        }
        return orderVoPage;
    }

    private OrderPayTimeAmountContext getPayRecordContext(List<Long> orderIds, List<Order> orderList) {
        List<OrderOperate> orderOperateList = orderOperateService.queryOperateOrderListByOrderIds(orderIds);
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryOrderPayRecordByOrderIds(orderIds);
        return OrderPayTimeAmountContext
            .of(orderOperateList, orderPayRecords, orderList)
            .execute();
    }

    /**
     * 设置订单价格相关
     * @param record
     * @param productPriceSum
     */
    private void setOrderPrice(OrderVo record, BigDecimal productPriceSum) {
        // 原始价格是后加的，新订单才有这个字段，有这个字段的productPrice是实付商品价格
        BigDecimal originPriceSum = record.getOriginPriceSum();
        BigDecimal preferentialPriceSum = Optional.ofNullable(record.getPreferentialPriceSum()).orElse(BigDecimal.ZERO);
        BigDecimal studentPreferentialPriceSum = Optional.ofNullable(record.getStudentPreferentialPriceSum()).orElse(BigDecimal.ZERO);
        boolean historyOrder = null == record.getCourseStartTime();
        if(historyOrder){
            record.setActualPayPrice(productPriceSum.subtract(preferentialPriceSum).subtract(studentPreferentialPriceSum));
            record.setProductPriceSum(productPriceSum);
        }else {
            record.setActualPayPrice(productPriceSum);
            record.setProductPriceSum(originPriceSum);
        }
    }


    private Date setInstallmentPayDeadlineTime(OrderVo orderVo) {
        OrderOperateVo orderOperate = orderVo.getOrderOperate();
        if(null == orderOperate){
            return null;
        }
        // 存在分期和支付了首笔金额才展示补缴日期
        Boolean installmentFlag = orderVo.getInstallmentFlag();
        boolean existInstallment = null != installmentFlag && installmentFlag;
        boolean pendingPayFlag = OrderStatusEnum.PENDING_PAY.getCode().equals(orderOperate.getOrderOperateStatus());
        if (existInstallment && pendingPayFlag) {
            return orderVo.getInstallmentPayDeadlineTime();
        }
        return null;
    }

    /**
     * 放置会员信息
     *
     * @param list 列表
     * @date 2024/03/01 03:16:44
     */
    public void putStudentInfo(List<OrderVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentIdList = StreamUtils.toList(list, OrderVo::getStudentId);
        //去除null
        studentIdList.remove(null);
        if (CollUtil.isEmpty(studentIdList)) {
            return;
        }
        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setStudentIds(studentIdList);
        remoteStudentBo.setWithBranchInfo(true);
        remoteStudentBo.setWithConsultantInfo(true);
        List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
        Map<Long, RemoteStudentVo> studentMap = StreamUtils.toMap(remoteStudentVos, RemoteStudentVo::getStudentId, vo -> vo);
        for (OrderVo item : list) {
            RemoteStudentVo studentVo = studentMap.get(item.getStudentId());
            //去掉密码
            if (studentVo != null) {
                studentVo.setStudentPassword(null);
            }
            item.setStudent(studentVo);
        }
    }

    /**
     * 放置产品信息
     *
     * @param list 列表
     * @date 2024/03/01 08:39:25
     */
    public void putProductInfo(List<OrderVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> productIdList = list.stream().map(OrderVo::getProductIdGroup).filter(StringUtils::isNotBlank).flatMap(ids -> Arrays.stream(ids.split(","))).map(Long::parseLong).collect(Collectors.toList());
        productIdList.remove(null);
        if (CollUtil.isEmpty(productIdList)) {
            return;
        }
        RemoteProductBo remoteProductBo = new RemoteProductBo();
        remoteProductBo.setProductIdList(productIdList);
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(remoteProductBo, true);
        Map<Long, RemoteProductVo> productMap = StreamUtils.toMap(remoteProductVos, RemoteProductVo::getProductId, vo -> vo);
        for (OrderVo item : list) {
            String productIdGroup = item.getProductIdGroup();
            if (StringUtils.isBlank(productIdGroup)) {
                continue;
            }
            List<Long> idList = Arrays.stream(productIdGroup.split(",")).map(Long::parseLong).toList();
            List<RemoteProductVo> productVos = idList.stream().map(productMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            item.setProductList(productVos);
        }
    }

    /**
     * 输入会员类型信息
     *
     * @param list 列表
     * @date 2024/03/01 08:39:28
     */
    public void putStudentTypeInfo(List<OrderVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> studentTypeIds = list.stream().map(OrderVo::getStudentTypeIdGroup)
            .flatMap(ids -> Arrays.stream(ids.split(","))).map(Long::parseLong).collect(Collectors.toList());
        RemoteStudentTypeBo remoteStudentTypeBo = new RemoteStudentTypeBo();
        remoteStudentTypeBo.setStudentTypeIds(studentTypeIds);
        List<RemoteStudentTypeVo> remoteStudentTypeVos = remoteStudentTypeService.queryStudentTypeList(remoteStudentTypeBo);
        Map<Long, RemoteStudentTypeVo> studentTypeVoMap = StreamUtils.toMap(remoteStudentTypeVos, RemoteStudentTypeVo::getStudentTypeId, Function.identity());
        for (OrderVo item : list) {
            String studentTypeIdGroup = item.getStudentTypeIdGroup();
            if (StringUtils.isBlank(studentTypeIdGroup)) {
                continue;
            }
            List<Long> idList = Arrays.stream(studentTypeIdGroup.split(",")).map(Long::parseLong).collect(Collectors.toList());
            List<RemoteStudentTypeVo> productVos = idList.stream().map(studentTypeVoMap::get).collect(Collectors.toList());
            item.setStudentTypeList(productVos);
        }
    }


    /**
     * 查询订单列表
     */
    @Override
    public List<OrderVo> queryList(OrderBo bo) {
        LambdaQueryWrapper<Order> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<OrderVo> selectOrderListAndInfo(OrderBo bo) {
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        return baseMapper.selectOrderListAndInfo(lqw);
    }

    @Override
    public List<OrderVo> selectOrderListAndOperateList(OrderBo bo) {
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        return baseMapper.selectOrderListAndOperateList(lqw);
    }

    private QueryWrapper<Order> buildQueryWrapper(OrderBo bo) {
        QueryWrapper<Order> lqw = Wrappers.query();
        lqw.eq(bo.getStudentId() != null, "t.student_id", bo.getStudentId());
        lqw.eq(bo.getSalesPerson() != null, "t.sales_person", bo.getSalesPerson());
        lqw.eq(bo.getOrderType() != null, "t.order_type", bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), "t.order_no", bo.getOrderNo());
        lqw.eq(bo.getHandlingDate() != null, "t.handling_date", bo.getHandlingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHandlingPerson()), "t.handling_person", bo.getHandlingPerson());
        lqw.eq(bo.getOrderOperateId() != null, "t.order_operate_id", bo.getOrderOperateId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderStatus()), "oo.order_operate_status", bo.getOrderStatus());
        lqw.in(!CollectionUtils.isEmpty(bo.getOrderStatusList()), "oo.order_operate_status", bo.getOrderStatusList());
        lqw.apply(bo.getStudentTypeId() != null, DataBaseHelper.findInSet(bo.getStudentTypeId(), "opi.student_type_id_group"));
        lqw.eq(bo.getSingleStudentTypeId() != null, "opi.student_type_id", bo.getSingleStudentTypeId());
        lqw.eq(bo.getPurchasedCardFlag() != null, "t.purchased_card_flag", bo.getPurchasedCardFlag());
        if(StringUtils.isNotEmpty(bo.getPaymentType())){
            if(PayModeEnum.OFFLINE.getModeCode().toString().equals(bo.getPaymentType())){
                lqw.eq("t.online_pay_flag",PayModeEnum.OFFLINE.getModeCode());
            }else {
                lqw.eq("t.online_pay_flag",PayModeEnum.ONLINE.getModeCode());
            }
        }
        if (null != bo.getPayMode()) {
            if (PayModeEnum.OFFLINE.getModeCode().equals(bo.getPayMode())) {
                lqw.eq("oo.pay_mode", PayModeEnum.OFFLINE.getModeCode());
            } else {
                lqw.eq("oo.pay_mode", PayModeEnum.ONLINE.getModeCode());
            }
        }
        lqw.between(bo.getPaymentStartTime() != null && bo.getPaymentEndTime() != null, "oo.create_time",
            bo.getPaymentStartTime(), bo.getPaymentEndTime());
        lqw.between(bo.getCreateOrderStartTime() != null && bo.getCreateOrderEndTime() != null, "t.handling_date", bo.getCreateOrderStartTime(), bo.getCreateOrderEndTime());
        lqw.between(StringUtils.isNotBlank(bo.getOrderAmountStart()) && StringUtils.isNotBlank(bo.getOrderAmountEnd()), "oo.payment_amount", bo.getOrderAmountStart(), bo.getOrderAmountEnd());
        lqw.apply(bo.getProductId() != null, DataBaseHelper.findInSet(bo.getProductId(), "opi.product_id_group"));
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudentIdList()), "t.student_id", bo.getNotInStudentIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getStudentIdList()), "t.student_id", bo.getStudentIdList());
        lqw.eq(null != bo.getReviewStatus(), "oo.review_status", bo.getReviewStatus())
            .between(bo.getApplyStartTime() != null, "oo.create_time", bo.getApplyStartTime(), bo.getApplyEndTime())
            .eq(StringUtils.isNotBlank(bo.getRefundType()), "oo.refund_type", bo.getRefundType());
        lqw.ne(bo.getExcludeStudentTypeId() != null, "opi.student_type_id", bo.getExcludeStudentTypeId());
        lqw.in(CollUtil.isNotEmpty(bo.getReviewNodeList()), "oo.review_node", bo.getReviewNodeList());
        if (null != bo.getRefundStartTime()) {
            lqw.eq("oo.order_operate_status", OrderStatusEnum.REFUNDED.getCode()).between("oo.update_time",
                bo.getRefundStartTime(), bo.getRefundEndTime());
        }

        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq("t.del_flag", bo.getDelFlag());
        } else {
            bo.setDelFlag(UserConstants.DEL_FLAG_NO);
            lqw.eq("t.del_flag", UserConstants.DEL_FLAG_NO);
        }
        if (StringUtils.isNotBlank(bo.getStudentName()) || StringUtils.isNotBlank(bo.getStudentAccount())
            || StringUtils.isNotBlank(bo.getConsultantName()) || bo.getBranchId() != null) {
            RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
            remoteStudentBo.setStudentName(bo.getStudentName());
            remoteStudentBo.setStudentAccount(bo.getStudentAccount());
            remoteStudentBo.setConsultantName(bo.getConsultantName());
            remoteStudentBo.setBranchId(bo.getBranchId());
            List<Long> studentIds = remoteStudentService.queryStudentIdList(remoteStudentBo);
            if (CollUtil.isNotEmpty(studentIds)) {
                lqw.in("t.student_id", studentIds);
                bo.setStudentIdList(studentIds);
            } else {
                lqw.in("t.student_id", -1);
                bo.setStudentIdList(CollUtil.newArrayList(-1L));
            }
        }
        if(StringUtils.isNotEmpty(bo.getInstallmentPayDeadlineStartTime()) || StringUtils.isNotEmpty(bo.getInstallmentPayDeadlineEndTime())){
            lqw.eq("oo.order_operate_status",OrderStatusEnum.PENDING_PAY.getCode());
            lqw.eq("t.installment_flag",Boolean.TRUE);
            if(StringUtils.isNotEmpty(bo.getInstallmentPayDeadlineStartTime())){
                lqw.ge("t.installment_pay_deadline_time", bo.getInstallmentPayDeadlineStartTime().concat(" 00:00:00"));

            }
            if (StringUtils.isNotEmpty(bo.getInstallmentPayDeadlineEndTime())){
                lqw.le("t.installment_pay_deadline_time", bo.getInstallmentPayDeadlineEndTime().concat(" 23:59:59"));
            }
        }
        if(OrderStatusEnum.PENDING_PAY.getCode().equals(bo.getOrderStatus())){
            lqw.eq("t.installment_flag",Boolean.TRUE);
        }
        if(StringUtils.isNotEmpty(bo.getChannelOrderId())){
            lqw.exists(
                "SELECT 1 FROM order_pay_record opay " +
                    "WHERE opay.order_id = t.order_id " +
                    "AND opay.channel_order_id = '" + bo.getChannelOrderId() + "'"
            );
        }
        return lqw;
    }

    private LambdaQueryWrapper<Order> buildLambdaQueryWrapper(OrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Order> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, Order::getStudentId, bo.getStudentId());
        lqw.eq(bo.getSalesPerson() != null, Order::getSalesPerson, bo.getSalesPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderNo()), Order::getOrderNo, bo.getOrderNo());
        lqw.eq(bo.getHandlingDate() != null, Order::getHandlingDate, bo.getHandlingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHandlingPerson()), Order::getHandlingPerson, bo.getHandlingPerson());
        lqw.eq(bo.getOrderOperateId() != null, Order::getOrderOperateId, bo.getOrderOperateId());
        lqw.notIn(CollUtil.isNotEmpty(bo.getNotInStudentIdList()), Order::getStudentId, bo.getNotInStudentIdList());
        if (StringUtils.isNotBlank(bo.getDelFlag())) {
            lqw.eq(Order::getDelFlag, bo.getDelFlag());
        } else {
            lqw.eq(Order::getDelFlag, "0");
        }
        return lqw;
    }

    /**
     * 新增订单
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean insertByBo(AddOrderBo bo) {
        OrderCreationContext orderCreationContext = this.paperOrderCreationContext(bo);
        RemoteProductVo remoteProductVo = orderCreationContext.getRemoteProductList().get(0);

        //创建订单和产品记录
        this.checkAmountByAddOrder(bo, remoteProductVo);
        Order order = this.createOrder(bo, orderCreationContext);
        log.info("【下单】创建订单成功：{}", order);

        // 更新会员应付金额
        this.updateStudentAblePayAmount(order);

        // 更新会员优惠额度 记录优惠额度变动记录
        this.updateStudentPreferentialAmount(bo, order.getOrderId(), bo.getOrderProductInfoList());

        //创建订单操作数据
        Long orderOperateId = orderOperateService.initOrderOperate(order.getOrderId());
        this.updateOrderOperateId(order.getOrderId(), orderOperateId);

        //校验门店剩余时长，并且锁定时长，如果时长不足，则终止下单
//        this.checkBranchRemainTime(orderCreationContext.getBranchId(), orderCreationContext.getRemoteProductList());
        iOrderAccountService.getProcessor(order.getOrderId(), OrderTypeEnum.STUDENT);
        return true;
    }

    /**
     * 校验本店有效时长是否足够 - 使用2B端的逻辑，该方法废弃
     * @param branchId - 会员所属门店ID
     * @param remoteProductList - 产品集合
     */
    @Deprecated
    private void checkBranchRemainTime(Long branchId, List<RemoteProductVo> remoteProductList) {
        log.info("【购买会员卡产品-下单】所属门店：{}，下单产品列表：{}", branchId, remoteProductList);
        if (ObjectUtil.isNull(branchId)) {
            throw new ServiceException("学生所属门店ID不能为空");
        }

        if (ObjectUtil.isEmpty(remoteProductList)) {
            throw new ServiceException("下单产品列表不能为空");
        }

        //获取需要下单的有效时间
        int amountTime = remoteProductList.stream()
                .map(remoteProductVo -> {
                    if (ObjectUtil.isNotNull(remoteProductVo.getProductValidDays())) {
                        return remoteProductVo.getProductValidDays();
                    } else if (ObjectUtil.isNotEmpty(remoteProductVo.getProductValidTimeLimit())){
                        String[] timeRanges = remoteProductVo.getProductValidTimeLimit().split(" 至 ");
                        if (ObjectUtil.isNotEmpty(timeRanges) && timeRanges.length == 2) {
                            return DateUtil.between(DateUtil.parseDateTime(timeRanges[0]), DateUtil.parseDateTime(timeRanges[1]), DateUnit.DAY);
                        }
                    }
                    return 0L;
                })
                .mapToInt(Long::intValue).sum();
        log.info("【购买会员卡产品-下单】购买的有效天数：{}", amountTime);

        if (amountTime <= 0) return;

        //扣除门店有效天数
        Boolean flag = remoteBranchService.transfer(branchId, -amountTime);
        if (!flag) {
            //门店有效天数不足，不足以抵扣订单天数，下单失败
            throw new ServiceException("门店有效时长不足！");
        }
    }

    private void checkAmountByAddOrder(AddOrderBo bo, RemoteProductVo remoteProductVo) {
        List<AddOrderProductBo> orderProductInfoList = bo.getOrderProductInfoList();
        if (CollUtil.isEmpty(orderProductInfoList)) {
            throw new ServiceException("请选择产品");
        }
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryById(bo.getStudentId());
        if (null == remoteStudentVo) {
            throw new ServiceException("学生信息不存在");
        }

        AddOrderProductBo addOrderProductBo = orderProductInfoList.get(0);
        CheckOrderPriceDTO checkOrderPriceDTO = new CheckOrderPriceDTO();
        checkOrderPriceDTO.setRemoteProductVo(remoteProductVo);
        checkOrderPriceDTO.setPreferentialPrice(null == addOrderProductBo.getPreferentialPrice() ? BigDecimal.ZERO : addOrderProductBo.getPreferentialPrice());
        checkOrderPriceDTO.setStudentPreferentialAmount(null == addOrderProductBo.getStudentPreferentialAmount() ? BigDecimal.ZERO : addOrderProductBo.getStudentPreferentialAmount());
        checkOrderPriceDTO.setStudentId(bo.getStudentId());
        this.checkPreCalculateOrderPriceAmount(checkOrderPriceDTO,remoteStudentVo.getPreferentialAmount());
    }

    private void updateStudentAblePayAmount(Order order) {
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.queryProductByOrder(order.getOrderId());
        BigDecimal reduceAmount = orderProductInfos.stream().map(OrderProductInfo::getProductPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (BigDecimal.ZERO.compareTo(reduceAmount) >= 0) {
            throw new ServiceException("商品收款金额必须大于0，请确认");
        }
        order.setPayAbleAmount(reduceAmount);
        baseMapper.updateById(order);
    }

    private void processOrderProducts(AddOrderBo bo, OrderCreationContext orderCreationContext, Order order) {
        List<AddOrderProductBo> orderProductInfoList = bo.getOrderProductInfoList();
        Long studentTypeId = bo.getStudentTypeId();
        orderProductInfoService.insertBatchAndReturnActualAmount(orderProductInfoList, orderCreationContext, order.getOrderId(), studentTypeId);
        BigDecimal orderActualPayAmount = orderProductInfoService.calculateOrderActualPayAmountByOrder(order, order.getOrderRelationId());
        if (orderActualPayAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException("订单创建异常，订单应收金额不能小于或者等于0");
        }
    }

    /**
     *  创建订单上下文准备
     * @param bo
     * @return
     */
    private OrderCreationContext paperOrderCreationContext(AddOrderBo bo) {
        // 处理开课时间
        Date courseStartTime = this.getCourseStartTime(bo.getCourseStartTime());

        List<Long> productIds = bo.getOrderProductInfoList().stream()
            .map(AddOrderProductBo::getProductId).filter(Objects::nonNull)
            .toList();
        List<RemoteProductVo> remoteProductList = this.getRemoteProductVoList(productIds);
        this.validateCourseStartTime(courseStartTime, remoteProductList);

        // 获取学生部门
        RemoteStudentSimpleVO remoteStudentBo = remoteStudentService.queryStudentById(bo.getStudentId());
        if (null == remoteStudentBo){
            throw new ServiceException("学生不存在");
        }
        return new OrderCreationContext(courseStartTime, remoteProductList, remoteStudentBo.getBranchId());
    }

    private void validateCourseStartTime(Date courseStartTime, List<RemoteProductVo> remoteProductList) {
        RemoteProductVo remoteProductVo = remoteProductList.stream()
            .findFirst()
            .orElseThrow(() -> new ServiceException("缺少产品"));
        this.validateExistValidTime(remoteProductVo);
        LocalDate courseStartTimeLocalDate = DateUtils.dateToLocalDate(courseStartTime);
        LocalDate productMaxValidDate = this.getMaxProductValidDate(remoteProductVo,courseStartTimeLocalDate);
        LocalDate productMinValidDate = this.getMinProductValidDate(remoteProductVo,courseStartTimeLocalDate);
        if (courseStartTimeLocalDate.isBefore(productMinValidDate) ||
            courseStartTimeLocalDate.isAfter(productMaxValidDate)) {
            throw new IllegalArgumentException("课程开始日期必须在产品有效期内");
        }
    }

    private LocalDate getMaxProductValidDate(RemoteProductVo remoteProductVo,LocalDate courseStartTimeLocalDate) {
        LocalDate productValidDate = LocalDate.now();
        // 非时间范围的会员卡天数
        if (null != remoteProductVo.getProductValidDays()) {
            productValidDate = courseStartTimeLocalDate.plusDays(remoteProductVo.getProductValidDays());
        } else if (StringUtils.isNotEmpty(remoteProductVo.getProductValidTimeLimit())) {
            Date productEndTime = ProcessProductPrice.getValidEndDate(remoteProductVo.getProductValidTimeLimit());
            if (null == productEndTime) {
                throw new ServiceException("产品类型没有配置有效时间");
            }
            productValidDate = DateUtils.dateToLocalDate(productEndTime);
        }
        return productValidDate;
    }
    private LocalDate getMinProductValidDate(RemoteProductVo remoteProductVo,LocalDate courseStartTimeLocalDate) {
        LocalDate productValidDate = courseStartTimeLocalDate;
        // 非时间范围的会员卡天数
        if (StringUtils.isNotEmpty(remoteProductVo.getProductValidTimeLimit())) {
            Date productStartTime = ProcessProductPrice.getValidStartDate(remoteProductVo.getProductValidTimeLimit());
            if (null == productStartTime) {
                throw new ServiceException("产品类型没有配置有效时间");
            }
            productValidDate = DateUtils.dateToLocalDate(productStartTime);
        }
        return productValidDate;
    }

    private void validateExistValidTime(RemoteProductVo remoteProductVo) {
        boolean existValidateTime = StringUtils.isNotEmpty(remoteProductVo.getProductValidTimeLimit())
            || remoteProductVo.getProductValidDays() != null;
        if(!existValidateTime){
            throw new ServiceException("产品类型没有配置有效时间");
        }
    }

    private List<RemoteProductVo> getRemoteProductVoList(List<Long> productIds) {
        RemoteProductBo remoteProductBo = new RemoteProductBo();
        remoteProductBo.setProductIdList(productIds);
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(remoteProductBo,true);
        boolean existErrorProductId =  this.existErrorProductId(productIds, remoteProductVos);
        if(existErrorProductId){
             throw new ServiceException("商品存在异常或者会员卡已经下架！请重新下单");
        }
        return remoteProductVos;
    }

    private boolean existErrorProductId(List<Long> productIds, List<RemoteProductVo> remoteProductVos) {
        return CollectionUtil.isEmpty(remoteProductVos) || remoteProductVos.size() != productIds.size();
    }

    /**
     * 更新会员优惠额度 记录优惠额度变动记录
     * @param bo
     * @param orderId
     * @param orderProductInfoList
     */
    private void updateStudentPreferentialAmount(AddOrderBo bo, Long orderId, List<AddOrderProductBo> orderProductInfoList) {
        AtomicReference<BigDecimal> studentPreferentialAmount = new AtomicReference<>(BigDecimal.ZERO);
        orderProductInfoList.forEach(item -> {
            if (null != item.getStudentPreferentialAmount()) {
                if (item.getStudentPreferentialAmount().compareTo(BigDecimal.ZERO) > 0) {
                    studentPreferentialAmount.getAndSet(studentPreferentialAmount.get().add(item.getStudentPreferentialAmount()));
                }
            }
        });
        BigDecimal totalPreferentialAmount = studentPreferentialAmount.get();
        if(totalPreferentialAmount.compareTo(BigDecimal.ZERO)>0){
            RemoteStudentPreferentialBo remoteStudentPreferentialBo = new RemoteStudentPreferentialBo();
            remoteStudentPreferentialBo.setStudentId(bo.getStudentId());
            remoteStudentPreferentialBo.setBusinessId(orderId);
            remoteStudentPreferentialBo.setModifyType(PreferentialModifyTypeEnum.STUDENT_USED);
            remoteStudentPreferentialBo.setModifyPreferentialAmount(totalPreferentialAmount.negate());
            if (!remoteStudentPreferentialService.modifyStudentPreferential(remoteStudentPreferentialBo)) {
                log.error("调整会员优惠额度失败");
                throw new ServiceException("下单失败");
            }
        }
    }

    private void updateOrderOperateId(Long orderId, Long orderOperateId) {
        Order update = new Order();
        update.setOrderId(orderId);
        update.setOrderOperateId(orderOperateId);
        int i = baseMapper.updateById(update);
        if (i <= 0) {
            throw new ServiceException("订单操作id更新失败");
        }
    }

    private Order createOrder(AddOrderBo bo,OrderCreationContext orderCreationContext) {
        Order order = this.buildCreateOrderDTO(bo, orderCreationContext);

        int insert = baseMapper.insert(order);
        if (insert <= 0 || order.getOrderId() == null) {
            throw new ServiceException("订单创建失败");
        }
        this.processOrderProducts(bo, orderCreationContext, order);
        return order;
    }

    private Order buildCreateOrderDTO(AddOrderBo bo, OrderCreationContext orderCreationContext) {
        Date courseStartTime = orderCreationContext.getCourseStartTime();

        Order order = new Order();
        order.setStudentId(bo.getStudentId());
        order.setSalesPerson(bo.getSalesPerson());
        order.setOrderNo(String.valueOf(System.currentTimeMillis()));
        order.setHandlingDate(new Date());
        order.setHandlingPerson(bo.getHandlingPerson());
        order.setOrderRelationId(bo.getOrderRelationId());
        order.setOrderType(bo.getOrderType());
        order.setCourseStartTime(courseStartTime);
        order.setAdvancePayFlag(1 == this.compareAdvancePayFlag(courseStartTime).getCode());
        order.setInstallmentPayDeadlineTime(this.setDeadLineTime(bo, orderCreationContext, courseStartTime));
        if (ObjectUtils.isEmpty(order.getOrderType())){
            order.setOrderType(OrderTypeEnum.BUY_NEW_CARD.getCode());
        }
        if (OrderTypeEnum.OLD_CARD_UPGRADE.getCode().equals(order.getOrderType())){
            Order oldOrder = queryOrderById(order.getOrderRelationId());
            if (ObjectUtils.isEmpty(oldOrder)){
                throw new ServiceException("旧卡订单不存在");
            }
        }
        return order;
    }

    /**
     * 设置分期付款截止时间
     * @param bo
     * @param orderCreationContext
     * @param courseStartTime
     * @return
     */
    private Date setDeadLineTime(AddOrderBo bo, OrderCreationContext orderCreationContext, Date courseStartTime) {
        Optional<AddOrderProductBo> orderProductBoOptional = bo.getOrderProductInfoList().stream().findFirst();
        if (orderProductBoOptional.isEmpty()) {
            throw new ServiceException("请选择产品");
        }
        Long productId = orderProductBoOptional.get().getProductId();
        Long branchId = orderCreationContext.getBranchId();
        Optional<RemoteProductVo> remoteProductVoOptional = orderCreationContext.getRemoteProductList().stream().findFirst();
        if (remoteProductVoOptional.isEmpty()) {
            throw new ServiceException("无效的产品信息");
        }

        InstallmentDeadlineTimeDTO deadlineTimeDTO = InstallmentDeadlineTimeDTO.of(courseStartTime, branchId, productId, bo.getStudentId());
        return this.getInstallmentPayDeadlineTime(deadlineTimeDTO);
    }

    /**
     * 获取分期付款截止时间
     * @param deadlineTimeDTO
     * @return
     */
    private Date getInstallmentPayDeadlineTime(InstallmentDeadlineTimeDTO deadlineTimeDTO) {
        // 参数基础校验
        if (!this.isValidDeadlineTimeDTO(deadlineTimeDTO)) {
            return null;
        }

        // 获取支付配置和AppID
        RemoteBranchPayModelConfigVo payModelConfig = remoteBranchConfigService.remoteGetPayModelConfig(deadlineTimeDTO.getBranchId());
        String payAppId = remoteStudentService.getCommonPayAppId(deadlineTimeDTO.getStudentId());

        // 检查基础支付配置是否有效
        if (payModelConfig == null || StringUtils.isEmpty(payAppId)) {
            return null;
        }

        // 检查是否支持分期
        RemoteBranchPayModelConfigVo.PayModelDetail payModelDetail = this.getPayModelDetail(payModelConfig, deadlineTimeDTO.getProductId());
        if (!isInstallmentSupported(payModelDetail)) {
            return null;
        }

        // 计算截止日期
        return this.calculateDeadlineDate(deadlineTimeDTO.getCourseStartTime(), payModelDetail.getInstallmentDeadlineDays());
    }

    // 参数校验
    private boolean isValidDeadlineTimeDTO(InstallmentDeadlineTimeDTO dto) {
        return dto != null
            && dto.getBranchId() != null
            && dto.getStudentId() != null
            && dto.getProductId() != null
            && dto.getCourseStartTime() != null;
    }

    // 获取支付详情配置
    private RemoteBranchPayModelConfigVo.PayModelDetail getPayModelDetail(
        RemoteBranchPayModelConfigVo config, Long productId) {
        return config != null ? config.findPayModelDetailId(productId) : null;
    }

    // 查是否支持分期
    private boolean isInstallmentSupported(RemoteBranchPayModelConfigVo.PayModelDetail detail) {
        return detail != null && detail.getInstallmentFlag();
    }

    // 计算截止日期
    private Date calculateDeadlineDate(Date startDate, Long deadlineDays) {
        if (startDate == null || deadlineDays == null) {
            return null;
        }
        return DateUtils.setEndOfDay(DateUtils.addDays(startDate, deadlineDays.intValue()));
    }

    /**
     * 获取课程开始时间
     * @param courseStartTime
     * @return
     */

    private Date getCourseStartTime(String courseStartTime) {
        if (StringUtils.isEmpty(courseStartTime)) {
            return DateUtils.parseDateByPattern(DateUtils.getStartOfToday(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
        return DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, courseStartTime.concat(" 00:00:00"));
    }

    /**
     * 比较是否是提前付款
     * @param courseStartTime
     * @return
     */
    private AdvancePayFlagEnum compareAdvancePayFlag(Date courseStartTime) {
        if (null == courseStartTime) {
            return AdvancePayFlagEnum.NO_ADVANCE_PAY;
        }
        Date courseStartTimeDate = DateUtils.parseDateByPattern(courseStartTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
        Date currentDate = DateUtils.parseDateByPattern(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        return courseStartTimeDate.compareTo(currentDate) > 0 ? AdvancePayFlagEnum.ADVANCE_PAY : AdvancePayFlagEnum.NO_ADVANCE_PAY;
    }


    /**
     * 修改订单
     */
    @Override
    @CacheEvict(value = "order", key = "#bo.orderId", condition = "#bo.orderId != null")
    public Boolean updateByBo(OrderBo bo) {
        Order update = MapstructUtils.convert(bo, Order.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Order entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除订单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<OrderVo> queryRefundPageList(OrderBo bo, PageQuery pageQuery) {
        handleQueryParam(bo);
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        //默认只查退款中，已退款
        lqw.in("oo.order_operate_status", OrderStatusEnum.REFUNDING.getCode(), OrderStatusEnum.REFUNDED.getCode());
        lqw.orderByDesc("oo.update_time");
        Page<OrderVo> result = selectPageList(pageQuery, lqw);
        putStudentInfo(result.getRecords());
        putRefundApplicantInfo(result.getRecords());
        setOrderWorkflowStatus(result.getRecords());
        return TableDataInfo.build(result);
    }

    @Override
    public List<RefundOrderVo> listExportRefundOrder(OrderBo bo) {
        handleQueryParam(bo);
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        // 默认只查退款中，已退款
        lqw.in("oo.order_operate_status", OrderStatusEnum.REFUNDING.getCode(), OrderStatusEnum.REFUNDED.getCode());
        lqw.orderByDesc("oo.update_time");
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        pageQuery.setSearchCount(false);
        List<OrderVo> orderVos = selectPageList(pageQuery, lqw).getRecords();
        if (CollectionUtils.isEmpty(orderVos)) {
            return new ArrayList<>(1);
        }
        putStudentInfo(orderVos);
        putRefundApplicantInfo(orderVos);
        setOrderWorkflowStatus(orderVos);
        Map<String, String> workflowStatusMap = new HashMap<>(6);
        workflowStatusMap.put(OrderWorkflowStatusEnum.REFUND_WAIT.getCode(),
            OrderWorkflowStatusEnum.REFUND_WAIT.getInfo());
        workflowStatusMap.put(OrderWorkflowStatusEnum.REFUNDING.getCode(), OrderWorkflowStatusEnum.REFUNDING.getInfo());
        workflowStatusMap.put(OrderWorkflowStatusEnum.REFUNDED_REJECT.getCode(),
            OrderWorkflowStatusEnum.REFUNDED_REJECT.getInfo());
        workflowStatusMap.put(OrderWorkflowStatusEnum.REFUNDED.getCode(), OrderWorkflowStatusEnum.REFUNDED.getInfo());

        List<RefundOrderVo> refundOrderVoList = new ArrayList<>();
        for (OrderVo orderVo : orderVos) {
            boolean haveStudentInfo = null != orderVo.getStudent();
            boolean haveConsultUserInfo = haveStudentInfo && null != orderVo.getStudent().getConsultant()
                && null != orderVo.getStudent().getConsultant().getUser();
            OrderOperateApplyRecordVo applyRecordVo;
            refundOrderVoList
                .add(RefundOrderVo.builder().studentName(haveStudentInfo ? orderVo.getStudent().getStudentName() : "")
                    .consultAccount(
                        haveConsultUserInfo ? orderVo.getStudent().getConsultant().getUser().getNickName() : "")
                    .branchName(haveStudentInfo ? null != orderVo.getStudent().getBranch()
                        ? orderVo.getStudent().getBranch().getBranchName() : "" : "")
                    .createByName(orderVo.getOrderOperate().getCreateByName())
                    .refundMode(orderVo.getOrderOperate().getPayMode().toString())
                    .createTime(orderVo.getOrderOperate().getCreateTime())
                    .refundAmount(
                        orderVo.getOrderOperate().getRefundAmount().setScale(2, RoundingMode.HALF_UP).toString())
                    .orderWorkflowStatus(workflowStatusMap.get(orderVo.getOrderOperate().getOrderWorkflowStatus()))
                    .refundTime(OrderWorkflowStatusEnum.REFUNDED.getCode()
                        .equals(orderVo.getOrderOperate().getOrderWorkflowStatus())
                            ? orderVo.getOrderOperate().getUpdateTime() : null)
                    .orderNo(orderVo.getOrderNo()).refundRemark(orderVo.getOrderOperate().getOrderOperateRemark())
                    .applyRemark(OrderWorkflowStatusEnum.REFUNDED_REJECT.getCode()
                        .equals(orderVo.getOrderOperate().getOrderWorkflowStatus())
                            ? (applyRecordVo = orderOperateApplyRecordService.getOrderOperateApplyRecord(
                                orderVo.getOrderOperateId(), orderVo.getReviewStatus())) == null ? ""
                                    : applyRecordVo.getRemark()
                            : "")
                    .build());
        }
        return refundOrderVoList;
    }

    /**
     * 设置订单的操作状态
     *
     * @param result
     */
    private void setOrderWorkflowStatus(List<OrderVo> result) {
        if (!CollectionUtils.isEmpty(result)) {
            List<Integer> refundAuditNodeCodes = RefundAuditNode
                .listByRoles(LoginHelper.getRoles().stream().map(RoleDTO::getRoleId).collect(Collectors.toList()))
                .stream().map(RefundAuditNode::getCode).collect(Collectors.toList());
            for (OrderVo orderVo : result) {
                if (null != orderVo.getOrderOperate()) {
                    orderVo.getOrderOperate().setOrderWorkflowStatus(
                        OrderWorkflowStatusEnum.getWorkflowStatus(orderVo.getOrderOperate().getOrderOperateStatus(),orderVo.getReviewStatus())
                    );
                    if (OrderStatusEnum.REFUNDING.getCode().equals(orderVo.getOrderOperate().getOrderOperateStatus())) {
                        if (OrderOperateReviewStatus.WAIT_REVIEW.getCode().equals(orderVo.getReviewStatus())) {
                            orderVo.setReviewable(CollUtil.isNotEmpty(refundAuditNodeCodes)
                                && refundAuditNodeCodes.contains(orderVo.getOrderOperate().getReviewNode()));
                        } else if (OrderOperateReviewStatus.REVIEWED.getCode().equals(orderVo.getReviewStatus())) {
                            orderVo.setReviewable(LoginHelper.isBranchStaff() && LoginHelper.isExecutiveStoreManager());
                        } else if (OrderOperateReviewStatus.REJECT.getCode().equals(orderVo.getReviewStatus())) {
                            orderVo.setReviewable(
                                orderOperateService.checkOperatePermission(orderVo, true, () -> orderVo));
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<OrderVo> queryOptionList(OrderBo bo) {
        QueryWrapper<Order> lqw = buildQueryWrapper(bo);
        return baseMapper.queryOptionList(lqw);
    }

    @Override
    public List<Long> getEffectiveOrderStudentIds(List<Long> studentIdList, List<Long> productIdList) {
        //有效订单指的是已支付的订单，并且产品处于有效期内
        return baseMapper.getEffectiveOrderStudentIds(studentIdList, productIdList);
    }

    @Override
    public List<OrderVo> selectStudentLastOrder(OrderBo convert) {
        Long productId = convert.getProductId();
        convert.setProductId(null);
        QueryWrapper<Order> orderQueryWrapper = buildQueryWrapper(convert);
        convert.setProductId(productId);
        return baseMapper.selectStudentLastOrder(orderQueryWrapper, convert);
    }

    @Override
    public OrderVo selectStudentLastOrderByStudentId(OrderBo convert) {
        Long studentId = convert.getStudentId();
        if (ObjectUtils.isEmpty(studentId)){
            return null;
        }
        convert.setOrderStatus(OrderStatusEnum.PAYED.getCode());
        List<OrderVo> orderVos = selectStudentLastOrder(convert);
        if (CollectionUtils.isEmpty(orderVos)){
            return null;
        }
        return orderVos.get(0);
    }

    public void putRefundApplicantInfo(List<OrderVo> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> orderOperateCreateByIds = records.stream().map(OrderVo::getOrderOperate).map(OrderOperateVo::getCreateBy).collect(Collectors.toList());
        orderOperateCreateByIds.remove(null);
        Map<Long, String> map = remoteUserService.selectNicknameByIds(orderOperateCreateByIds);
        for (OrderVo record : records) {
            record.getOrderOperate().setCreateByName(map.get(record.getOrderOperate().getCreateBy()));
        }
    }

    @Override
    public List<RemoteProductVo> queryProductOption(OrderProductBo bo) {
        RemoteProductBo convert = MapstructUtils.convert(bo, RemoteProductBo.class);
        return remoteProductService.queryProductOption(convert);
    }

    @Override
    public Order queryOrderById(Long orderId) {
        return baseMapper.selectById(orderId);
    }

    @CacheEvict(value = "order", allEntries = true)
    public void cleanCache() {
        log.info("===========orderService cleanCache===========");
    }

    @Override
    public void init() {
        IOrderService aopProxy = SpringUtils.getBean(this.getClass());
        aopProxy.cleanCache();
        log.info("===========orderService init===========");
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Order::getOrderId);
        List<Order> list = DataPermissionHelper.ignore(() -> baseMapper.selectList(wrapper));
        log.info("===========orderService size:{}===========", list.size());
        list.parallelStream().forEach(item -> {
            aopProxy.queryOrderById(item.getOrderId());
        });
        log.info("===========orderService init end===========");
    }


    private void handleQueryParam(OrderBo record) {
        if (record.getStudentId()!=null) {
            return;
        }
        if (null != record.getOrderWorkflowStatus()) {
            if (OrderWorkflowStatusEnum.REFUND_WAIT.getCode().equals(record.getOrderWorkflowStatus())) {
                record.setOrderStatus(OrderStatusEnum.REFUNDING.getCode());
                record.setReviewStatus(OrderOperateReviewStatus.WAIT_REVIEW.getCode());
            } else if (OrderWorkflowStatusEnum.REFUNDING.getCode().equals(record.getOrderWorkflowStatus())) {
                record.setOrderStatus(OrderStatusEnum.REFUNDING.getCode());
                record.setReviewStatus(OrderOperateReviewStatus.REVIEWED.getCode());
            } else if (OrderWorkflowStatusEnum.REFUNDED_REJECT.getCode().equals(record.getOrderWorkflowStatus())) {
                record.setOrderStatus(OrderStatusEnum.REFUNDING.getCode());
                record.setReviewStatus(OrderOperateReviewStatus.REJECT.getCode());
            } else {
                record.setOrderStatus(record.getOrderWorkflowStatus());
            }
        }
        if (Boolean.TRUE.equals(record.getReviewable())) {
            // 获取当前角色可以审核的节点
            List<RefundAuditNode> refundAuditNodes = RefundAuditNode
                .listByRoles(LoginHelper.getRoles().stream().map(RoleDTO::getRoleId).collect(Collectors.toList()));
            if (CollUtil.isEmpty(refundAuditNodes)) {
                record.setReviewNodeList(Arrays.asList(-1));
            } else {
                record.setReviewNodeList(
                    refundAuditNodes.stream().map(RefundAuditNode::getCode).collect(Collectors.toList()));
            }
        }
        if (LoginHelper.isSaleConsultant()&&LoginHelper.getBranchStaffId() != null ){
            record.setSalesPerson(LoginHelper.getBranchStaffId());
            return;
        }
        //判断登录的是不是员工
        if (LoginHelper.isConsultant() && LoginHelper.getBranchStaffId() != null ) {
            List<Long> staffResponsibleStudentIdList = remoteStudentConsultantRecordService.getStaffResponsibleStudentIdList(LoginHelper.getBranchStaffId(),true);
            if (CollUtil.isEmpty(record.getStudentIdList())) {
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : staffResponsibleStudentIdList);
            } else {
                //取交集
                record.setStudentIdList(CollUtil.isEmpty(staffResponsibleStudentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), staffResponsibleStudentIdList)));
            }
        } else {
            //判断是不是门店
            if (CollUtil.isNotEmpty(LoginHelper.getBranchIdList())){
                List<Long> studentIdList = remoteStudentService.getStudentIdListByBranchIdList(LoginHelper.getBranchIdList(),true);
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
            if (null != LoginHelper.getBranchId()) {
                List<Long> studentIdList = remoteStudentService.getStudentIdListByBranchId(LoginHelper.getBranchId(),true);
                if (CollUtil.isEmpty(record.getStudentIdList())){
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : studentIdList);
                }else {
                    //取交集
                    record.setStudentIdList(CollUtil.isEmpty(studentIdList) ? List.of(-1L) : new ArrayList<>(CollUtil.intersectionDistinct(record.getStudentIdList(), studentIdList)));
                }
            }
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initStudentCard(List<Long> studentIdList){
        // 获取所有订单信息
        OrderBo orderBo = new OrderBo();
        orderBo.setStudentIdList(studentIdList);
        List<OrderVo> orderList = selectOrderListAndOperateList(orderBo);
        if (CollUtil.isEmpty(orderList)){
            return;
        }
        //1.根据学生ID分组排序
        Map<Long, List<OrderVo>> orderMap = orderList.stream().collect(Collectors.groupingBy(OrderVo::getStudentId));
        //计算每份订单生成的会员卡起始时间和结束时间
        orderMap.forEach((studentId,v)-> {
            //获取学生订单购买过的产品ID
            List<OrderVo> orderVos = orderMap.get(studentId);
            if (CollUtil.isEmpty(orderVos)) {
                return;
            }
            List<StudentMembershipCardBo> studentMembershipCardBoList = orderVos
                .stream()
                .map(orderOperateService::getStudentMembershipCardBo)
                .filter(Objects::nonNull).toList();
            if (CollUtil.isEmpty(studentMembershipCardBoList)){
                return;
            }
            studentMembershipCardService.deleteByStudentIds(Collections.singleton(studentId));
            studentMembershipCardService.insertBatchBo(studentMembershipCardBoList);
        });
    }

    @Override
    public OrderVo queryOneOrder(Long studentId, Long excludeStudentTypeId) {
        return baseMapper.queryLastOrderByType(studentId, excludeStudentTypeId);
    }

    @Override
    public TableDataInfo<OrderReferrerVo> pageReferrer(OrderBo bo, PageQuery pageQuery) {
        if (StringUtils.isNotEmpty(bo.getStudentAccount())) {
            RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
            remoteStudentBo.setStudentAccount(bo.getStudentAccount());
            remoteStudentBo.setIgnoreUserDataScope(true);
            List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
            if (CollectionUtils.isEmpty(remoteStudentVos)) {
                return TableDataInfo.build(new ArrayList<>(1));
            }
            bo.setStudentIdList(
                remoteStudentVos.stream().map(RemoteStudentVo::getStudentId).collect(Collectors.toList()));
        } else {
            handleQueryParam(bo);
            if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
                pageQuery.setOrderByColumn("t.create_time");
                pageQuery.setIsAsc("desc");
            }
        }
        QueryWrapper<Order> orderQueryWrapper = buildQueryWrapper(bo);
        orderQueryWrapper.ne("opi.student_type_id", remoteStudentTypeService.getExperienceStudentTypeId());
        Page<OrderReferrerVo> referrerVoPage = baseMapper.pageReferrer(orderQueryWrapper, pageQuery.build());
        if (CollectionUtils.isEmpty(referrerVoPage.getRecords())) {
            return TableDataInfo.build(new ArrayList<>(1));
        }
        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setStudentIds(
            referrerVoPage.getRecords().stream().map(OrderReferrerVo::getStudentId).collect(Collectors.toList()));
        List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
        Map<Long, RemoteStudentVo> studentMap =
            StreamUtils.toMap(remoteStudentVos, RemoteStudentVo::getStudentId, vo -> vo);
        referrerVoPage.getRecords().forEach(vo -> {
            RemoteStudentVo remoteStudentVo = studentMap.get(vo.getStudentId());
            if (remoteStudentVo != null) {
                vo.setStudentName(remoteStudentVo.getStudentName());
                vo.setStudentAccount(remoteStudentVo.getStudentAccount());
                if (remoteStudentVo.getStudentAccount() != null && remoteStudentVo.getStudentAccount().length() > 4) {
                    vo.setNameWithPhone(remoteStudentVo.getStudentName() + "(" + remoteStudentVo.getStudentAccount()
                        .substring(remoteStudentVo.getStudentAccount().length() - 4) + ")");
                }
            }
        });
        return TableDataInfo.build(referrerVoPage);
    }

    @Override
    public List<OrderVo> selectBatchStudentLastOrder(OrderBo orderBo) {
        return baseMapper.selectBatchStudentLastOrder(buildQueryWrapper(orderBo), orderBo);
    }

    @Override
    public List<OrderVo> selectBatchStudentOrder(OrderBo orderBo) {
        return baseMapper.selectBatchStudentOrder(buildQueryWrapper(orderBo), orderBo);
    }

    @Override
    public void updateOrderCollectionInfo(Order order, Long orderId) {
        if(null == orderId){
            throw new ServiceException("订单ID不能为空");
        }
        LambdaQueryWrapper<Order>  updateWrapper =  new LambdaQueryWrapper<>();
        updateWrapper.eq(Order::getOrderId, orderId);
        updateWrapper.eq(Order::getDelFlag, "0");
        baseMapper.update(order, updateWrapper);
    }

    @Override
    public BigDecimal calculateOrderPrice(Long orderId, Long productId) {
        Order order = queryOrderById(orderId);
        if (null == order) {
            throw new ServiceException("无效的订单信息");
        }
        List<RemoteProductVo> remoteProductVoList = getRemoteProductVoList(Collections.singletonList(productId));
        RemoteProductVo remoteProductVo = remoteProductVoList.stream().findFirst().orElse(null);
        if (null == remoteProductVo) {
            throw new ServiceException("产品不存在");
        }
        OrderProductInfo orderProductInfo = orderProductInfoService.queryProductByOrderProductId(order.getOrderId(), productId);
        if (null == orderProductInfo) {
            throw new ServiceException("产品不存在");
        }
        return ProcessProductPrice.of(remoteProductVo, orderProductInfo,order.getCourseStartTime())
            .prepareOrderPrice().getProductPrice();
    }

    @Override
    public OrderRefundInfoVo getRefundInfo(OrderOperateBo bo) {
        OrderRefundInfoVo infoVo = new OrderRefundInfoVo();
        OrderVo order = queryById(bo.getOrderId());
        OrderOperateBo orderOperateBo = new OrderOperateBo();
        orderOperateBo.setOrderId(order.getOrderId());
        List<OrderOperateVo> orderOperateVos = orderOperateService.queryList(orderOperateBo);
        OrderOperateVo presentOperate = null;
        OrderOperateVo payOperate = null;
        for (OrderOperateVo operate : orderOperateVos) {
            if (OrderStatusEnum.PAYED.getCode().equals(operate.getOrderOperateStatus())) {
                payOperate = operate;
            }
            if (order.getOrderOperateId().equals(operate.getOrderOperateId())) {
                presentOperate = operate;
            }
        }
        if (presentOperate == null
            || OrderStatusEnum.WAIT_PAY.getCode().equals(presentOperate.getOrderOperateStatus())) {
            return new OrderRefundInfoVo(Collections.singletonList(PayModeEnum.OFFLINE.getModeCode()), BigDecimal.ZERO);
        }

        if (OrderOperateReviewStatus.WAIT_REVIEW.getCode().equals(presentOperate.getReviewStatus())
            || OrderStatusEnum.REFUNDED.getCode().equals(presentOperate.getOrderOperateStatus())) {
            // 退款中且待审核、已退款，直接返回当前的退费类型列表
            return new OrderRefundInfoVo(Collections.singletonList(presentOperate.getPayMode()), BigDecimal.ZERO);
        }
        // 线上支付且不是代付支持线上退款
        boolean supportOnlineRefund =
            Boolean.TRUE.equals(order.getOnlinePayFlag()) && Boolean.FALSE.equals(order.getPeerPayFlag());
        List<OrderPayRecord> orderPayRecords = orderPayRecordDao.queryValidRecordByOrderId(order.getOrderId(),
            Collections.singletonList(PayOrderStatusEnum.PAYED));
        if (CollUtil.isEmpty(orderPayRecords)) {
            // 兼容旧数据，支付后生成一条OrderPayRecord记录
            if (payOperate == null) {
                return new OrderRefundInfoVo(Collections.singletonList(PayModeEnum.OFFLINE.getModeCode()),
                    BigDecimal.ZERO);
            }
            OrderPayRecord record = new OrderPayRecord();
            record.setDepositAmountFlag(0);
            record.setPaymentTime(payOperate.getUpdateTime());
            record.setAmount(payOperate.getPaymentAmount());
            orderPayRecords.add(record);
        }
        Date now = new Date();
        if (supportOnlineRefund) {
            // 现在只能整笔退款，如果某次支付的支付时间和当前不是同一天不支持退款
            if (orderPayRecords.stream().anyMatch(orderPayRecord -> orderPayRecord.getPaymentTime() == null
                || !DateUtil.isSameDay(now, orderPayRecord.getPaymentTime()))) {
                supportOnlineRefund = false;
            }
        }
        OrderProductInfoBo orderProductInfoBo = new OrderProductInfoBo();
        orderProductInfoBo.setOrderId(order.getOrderId());
        List<OrderProductInfoVo> orderProductInfoVos = orderProductInfoService.queryList(orderProductInfoBo);
        // 当前仅支持一笔订单支付一次产品
        OrderProductInfoVo orderProductInfoVo = orderProductInfoVos.get(0);
        OrderPayRecord firstPayRecord;
        // 只支付一次，且订单是支付定金，这种情况不可以退款
        BigDecimal depositDontSupportRefund = orderPayRecords.size() == 1
            && Integer.valueOf(1).equals((firstPayRecord = orderPayRecords.get(0)).getDepositAmountFlag())
                ? firstPayRecord.getAmount() : BigDecimal.ZERO;


        // 退款默认按照季卡价格计算使用金额，再进行退款
        RemoteProductVo productVo = remoteStudentService.queryStudentRecentPeriodCard(order.getStudentId());
        BigDecimal productPriceForRefund;
        String productValidTimeLimit = null;
        Integer productValidDays = null;
        if (productVo == null) {
            // 历史订单兼容新的退费逻辑计算
            productPriceForRefund = null == orderProductInfoVo.getOriginProductPrice()
                ? orderProductInfoVo.getProductPrice() : orderProductInfoVo.getOriginProductPrice();

            if (StringUtils.isNotEmpty(orderProductInfoVo.getProductValidTimeLimit())) {
                productValidTimeLimit = orderProductInfoVo.getProductValidTimeLimit();
            } else {
                productValidDays = orderProductInfoVo.getProductValidDays().intValue();
            }
        } else {
            productPriceForRefund = productVo.getProductPrice();
            if (StringUtils.isNotEmpty(productVo.getProductValidTimeLimit())) {
                productValidTimeLimit = productVo.getProductValidTimeLimit();
            } else {
                productValidDays = productVo.getProductValidDays().intValue();
            }
        }
        orderPayRecords.sort(Comparator.comparing(OrderPayRecord::getPaymentTime));
        Pair<Date, Date> productForRefundPeriod =
            getProductPeriod(productValidTimeLimit, productValidDays, () -> orderPayRecords.get(0).getPaymentTime());
        Pair<Date,
            Date> orderProductPeriod = getProductPeriod(orderProductInfoVo.getProductValidTimeLimit(),
                StringUtils.isEmpty(orderProductInfoVo.getProductValidTimeLimit())
                    ? orderProductInfoVo.getProductValidDays().intValue() : null,
                () -> orderPayRecords.get(0).getPaymentTime());
        BigDecimal usedAmount =
            calculateConsumedAmount(productForRefundPeriod.getKey(), productForRefundPeriod.getValue(),
                productPriceForRefund, order.getCourseStartTime() == null ? orderPayRecords.get(0).getPaymentTime()
                    : order.getCourseStartTime(),
                bo.getRefundStartTime(),
                orderProductPeriod.getValue());
        infoVo.setPayModeList(
            supportOnlineRefund ? Arrays.asList(PayModeEnum.OFFLINE.getModeCode(), PayModeEnum.ONLINE.getModeCode())
                : Collections.singletonList(PayModeEnum.OFFLINE.getModeCode()));
        BigDecimal amountDontRefund =
            usedAmount.compareTo(depositDontSupportRefund) > 0 ? usedAmount : depositDontSupportRefund;
        infoVo.setCanRefundAmount(orderPayRecords.stream().map(OrderPayRecord::getAmount).reduce(BigDecimal::add)
            .orElse(BigDecimal.ZERO).subtract(amountDontRefund));
        if (infoVo.getCanRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            infoVo.setCanRefundAmount(BigDecimal.ZERO);
        }
        return infoVo;
    }

    private Pair<Date, Date> getProductPeriod(String productValidTimeLimit, Integer productValidDays,
        Supplier<Date> paymentTimeSupplier) {
        Date beginDate;
        Date endDate;
        if (null != productValidTimeLimit && StringUtils.isNotEmpty(productValidTimeLimit)) {
            String[] split = productValidTimeLimit.split(" 至 ");
            if (split.length != 2) {
                throw new ServiceException("计算退款金额异常，产品有效期格式不正确");
            }
            beginDate = DateUtils.parseDate(split[0]);
            endDate = DateUtils.parseDate(split[1]);
        } else {
            beginDate = paymentTimeSupplier.get();
            endDate = DateUtils.addDays(beginDate, productValidDays);
        }
        return Pair.of(beginDate, endDate);
    }

    @Override
    public List<Order> queryOrderListByOrderId(List<Long> orderIdList) {
        if (CollectionUtil.isEmpty(orderIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Order::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public PreCalculateOrderPriceVO preCalculateOrderPrice(PreCalculatePriceDTO calculatePriceDTO) {
        List<RemoteProductVo> remoteProductVoList = this.getRemoteProductVoList(Collections.singletonList(calculatePriceDTO.getProductId()));
        RemoteProductVo remoteProductVo = remoteProductVoList.stream().findFirst().orElse(null);
        if (null == remoteProductVo) {
            throw new ServiceException("产品不存在");
        }

        RemoteStudentVo remoteStudentVo = remoteStudentService.queryStudentWithBranchById(calculatePriceDTO.getStudentId());
        if (null == remoteStudentVo) {
            throw new ServiceException("学生信息不存在");
        }

        BigDecimal preferentialPrice = calculatePriceDTO.getPreferentialPrice();
        Date courseStartDate = calculatePriceDTO.getCourseStartDate();
        BigDecimal studentPreferentialAmount = calculatePriceDTO.getStudentPreferentialAmount();

        // 校验预计算的金额
        CheckOrderPriceDTO preCalculatePriceDTO = new CheckOrderPriceDTO();
        preCalculatePriceDTO.setRemoteProductVo(remoteProductVo);
        preCalculatePriceDTO.setPreferentialPrice(preferentialPrice);
        preCalculatePriceDTO.setStudentPreferentialAmount(studentPreferentialAmount);
        preCalculatePriceDTO.setStudentId(calculatePriceDTO.getStudentId());
        this.checkPreCalculateOrderPriceAmount(preCalculatePriceDTO,remoteStudentVo.getPreferentialAmount());

        // 计算金额
        ProcessProductPrice processProductPrice = this.getProcessProductPrice(remoteProductVo, preferentialPrice,
            courseStartDate, studentPreferentialAmount);
        // 計算產品抵扣天數
        Long productAccountDays = this.getProductAccountDays(calculatePriceDTO, remoteProductVo,remoteStudentVo.getBranch().getRemainTime());

        return this.convertToPreCalculateOrderPriceVO(preferentialPrice, remoteProductVo, processProductPrice, productAccountDays);
    }

    /**
     * 計算產品價格
     * @param remoteProductVo
     * @param preferentialPrice
     * @param courseStartDate
     * @param studentPreferentialAmount
     * @return
     */
    private ProcessProductPrice getProcessProductPrice(RemoteProductVo remoteProductVo, BigDecimal preferentialPrice, Date courseStartDate, BigDecimal studentPreferentialAmount) {
        return ProcessProductPrice
            .preOrderOf(remoteProductVo, preferentialPrice, courseStartDate, studentPreferentialAmount)
            .prePrepareOrderPrice();
    }

    /**
     * 計算產品抵扣天數
     * @param calculatePriceDTO
     * @param remoteProductVo
     * @return
     */
    private Long getProductAccountDays(PreCalculatePriceDTO calculatePriceDTO, RemoteProductVo remoteProductVo,Integer remainTime) {
        return ProcessProductAccountDays.of(remoteProductVo.getProductValidDays(),
                                            remoteProductVo.getProductValidTimeLimit(),
                                            calculatePriceDTO.getCourseStartDate(),
                                            remainTime.longValue())
            .calculateDeductTime()
            .getDeductTime();
    }

    private void checkPreCalculateOrderPriceAmount(CheckOrderPriceDTO checkOrderPriceDTO,BigDecimal preferentialAmount) {
        BigDecimal preferentialPrice = checkOrderPriceDTO.getPreferentialPrice();
        BigDecimal studentPreferentialAmount = checkOrderPriceDTO.getStudentPreferentialAmount();
        Long studentId = checkOrderPriceDTO.getStudentId();
        RemoteProductVo remoteProductVo = checkOrderPriceDTO.getRemoteProductVo();
        BigDecimal productPrice = remoteProductVo.getProductPrice();

        if (studentPreferentialAmount.compareTo(Optional.ofNullable(preferentialAmount).orElse(BigDecimal.ZERO)) > 0) {
            throw new ServiceException("学生积分不足");
        }
        if (preferentialPrice.compareTo(productPrice) >= 0) {
            throw new ServiceException("门店优惠金额不能大于等于产品金额");
        }
        if (studentPreferentialAmount.compareTo(productPrice) >= 0) {
            throw new ServiceException("积分优惠金额不能大于等于产品金额");
        }
        BigDecimal totalPreferentialAmount = preferentialPrice.add(studentPreferentialAmount);
        if (totalPreferentialAmount.compareTo(productPrice) >= 0) {
            throw new ServiceException("优惠总金额（门店优惠+积分优惠）不能大于等于产品金额");
        }
    }

    private PreCalculateOrderPriceVO convertToPreCalculateOrderPriceVO(BigDecimal preferentialPrice,
                                                                       RemoteProductVo remoteProductVo,
                                                                       ProcessProductPrice processProductPrice,
                                                                       Long productAccountDays) {
        PreCalculateOrderPriceVO preCalculateOrderPriceVO = new PreCalculateOrderPriceVO();
        preCalculateOrderPriceVO.setCreateTime(remoteProductVo.getCreateTime());
        preCalculateOrderPriceVO.setProductId(remoteProductVo.getProductId().toString());
        preCalculateOrderPriceVO.setProductName(remoteProductVo.getProductName());
        preCalculateOrderPriceVO.setProductPrice(remoteProductVo.getProductPrice());
        preCalculateOrderPriceVO.setProductValidDays(remoteProductVo.getProductValidDays());
        preCalculateOrderPriceVO.setProductValidTimeLimit(remoteProductVo.getProductValidTimeLimit());
        preCalculateOrderPriceVO.setPreferentialPrice(preferentialPrice);
        preCalculateOrderPriceVO.setOrderPrice(processProductPrice.getProductPrice());
        preCalculateOrderPriceVO.setAblePayPrice(processProductPrice.getProductPrice());
        preCalculateOrderPriceVO.setSignupDiscountAmount(processProductPrice.getSignupDiscountAmount());
        preCalculateOrderPriceVO.setStudentPreferentialAmount(processProductPrice.getStudentPreferentialPrice());
        preCalculateOrderPriceVO.setProductDeductInfo(new ProductDeductInfoVO(productAccountDays));
        return preCalculateOrderPriceVO;
    }

    /**
     * 计算预计退款金额
     *
     * @param refundBeginDate 退款参照产品开始时间 用于计算每日单价
     * @param refundEndDate 退款参照产品结束时间 用于计算每日单价
     * @param productPriceForRefund 退款参照产品价格 用于计算每日单价
     * @param calculateStartDate 计费开始时间
     * @param calculateEndDate 计费结束时间
     * @param maxCalculateEndDate 最大计费结束时间
     * @return
     */
    public static BigDecimal calculateConsumedAmount(Date refundBeginDate, Date refundEndDate,
        BigDecimal productPriceForRefund, Date calculateStartDate, Date calculateEndDate, Date maxCalculateEndDate) {
        LocalDateTime begin = toLocalDateTime(refundBeginDate);
        LocalDateTime end = toLocalDateTime(refundEndDate);
        LocalDateTime billingStartDate = toLocalDateTime(calculateStartDate);
        LocalDateTime billingEndDate =
            calculateEndDate == null ? LocalDateTime.now() : toLocalDateTime(calculateEndDate);
        LocalDateTime maxBillingEndDate = toLocalDateTime(maxCalculateEndDate);
        LocalDateTime refundEndTime =
            calculateEndDate == null ? LocalDateTime.now() : toLocalDateTime(calculateEndDate);
        // 订单还未开始
        if (refundEndTime.isBefore(begin)) {
            return BigDecimal.ZERO;
        }
        // 如果 calculateEndDate 超过 MaxCalculateEndDate + 1 天，就限制为 MaxCalculateEndDate + 1 天 （后续计算是向下取整的，所以这里超过最大限度时提前+1天）
        if (billingEndDate.isAfter(maxBillingEndDate.plusDays(1))) {
            billingEndDate = maxBillingEndDate.plusDays(1);
        }
        long totalDays = ChronoUnit.DAYS.between(begin, end) + 1; // 包含结束日，线上取整
        long usedDays = ChronoUnit.DAYS.between(billingStartDate, billingEndDate); // 不包含退款日期 向下取整
        if (totalDays <= 0 || usedDays <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal dailyPrice = productPriceForRefund.divide(BigDecimal.valueOf(totalDays), 6, RoundingMode.HALF_UP);
        return dailyPrice.multiply(BigDecimal.valueOf(usedDays)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 转换为本地时间
     *
     * @param date
     * @return
     */
    private static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
