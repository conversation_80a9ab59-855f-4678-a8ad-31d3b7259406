package com.jxw.shufang.order.interfaces;

import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 默认工厂
 */
@Component
@AllArgsConstructor
public class AccountTransferFactoryImpl implements AccountTransferFactory {

    private final List<AccountTransferProcessor> processorList;

    @Override
    public AccountTransferProcessor getProcessor(AccountTradeTypeEnum typeEnum) {
        for (AccountTransferProcessor processor : processorList) {
            if (Boolean.TRUE.equals(processor.support(typeEnum))) {
                return processor;
            }
        }
        throw new ServiceException("不支持的账号类型");
    }
}
