package com.jxw.shufang.order.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/7/11 10:14
 * @Version 1
 * @Description
 */
@Data
public class PreCalculatePriceDTO {
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 优惠价格
     */
    private BigDecimal preferentialPrice;
    /**
     * 课程开始时间
     */
    private Date courseStartDate;
    /**
     * 学生优惠金额
     */
    private BigDecimal studentPreferentialAmount;
    /**
     * 学生id
     */
    private Long studentId;
}
