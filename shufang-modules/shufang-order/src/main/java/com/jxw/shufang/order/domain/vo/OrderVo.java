package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentTypeVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 订单视图对象 order
 *
 * @date 2024-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Order.class)
public class OrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 会员id
     */
    @ExcelProperty(value = "会员id")
    private Long studentId;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    @ExcelProperty(value = "销售人员id")
    private Long salesPerson;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 经办日期
     */
    @ExcelProperty(value = "经办日期")
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    @ExcelProperty(value = "经办人", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "姓=名")
    private String handlingPerson;

    /**
     * 订单操作id
     */
    @ExcelProperty(value = "订单操作id")
    private Long orderOperateId;

    /**
     * 如果订单类型为旧卡升级  值为旧卡订单ID
     */
    private Long orderRelationId;

    /**
     * 0为 购买新卡 1为 旧卡升级
     */
    private Integer orderType;

    /**
     * 订单操作对象
     */
    private OrderOperateVo orderOperate;

    /**
     * 订单产品名字组合，逗号分隔
     */
    private String productNameGroup;

    /**
     * 会员类型id组合，逗号分隔
     */
    private String studentTypeIdGroup;

    /**
     * 订单产品总价
     */
    private BigDecimal productPriceSum;

    private BigDecimal originPriceSum;

    /**
     * 优惠总价
     */
    private BigDecimal preferentialPriceSum;

    /**
     * 优惠总价
     */
    private BigDecimal studentPreferentialPriceSum;

    /**
     * 应付金额，实付金额使用paidAmount
     */
    private BigDecimal actualPayPrice;

    /**
     * 会员信息
     */
    private RemoteStudentVo student;

    /**
     * 订单产品ID组合，逗号分隔
     */
    private String productIdGroup;

    /**
     * 产品列表
     */
    private List<RemoteProductVo> productList;


    /**
     * 会员类型列表
     */
    private List<RemoteStudentTypeVo> studentTypeList;

    /**
     * 订单操作列表
     */
    List<OrderOperateVo> orderOperateList;

    /**
     * 订单产品信息列表
     */
    List<OrderProductInfoVo> orderProductInfoList;

    /**
     * 支付操作信息
     */
    private OrderOperateVo paymentOperate;
    private OrderVo oldOrder;

    /**
     * 退款操作信息
     */
    private OrderOperateVo refundOperate;

    /**
     * 订单当前状态
     */
    private String orderStatus;

    /**
     * 剩余天数
     */
    private Long remainingDays;

    /**
     * 使用天数
     */
    private Long usedDays;

    /**
     * 审核状态
     */
    private Integer reviewStatus;
    /**
     * 是否可以审核，当前是退款待审核的状态
     */
    private Boolean reviewable;
    /**
     * 订单审核记录
     */
    private List<OrderOperateApplyRecordVo> orderOperateApplyRecords;

    /**
     * 订单数量
     */
    private Integer orderNum;

    /**
     * 课程开始时间
     */
    private Date courseStartTime;

    /**
     * 分期付款截止时间
     */
    private Date installmentPayDeadlineTime;

    /**
     * 是否分期付款 false-整笔支付, true-分期支付
     */
    private Boolean installmentFlag;

    /**
     * 付款凭证图片路径
     */
    private String payVoucherPath;

    /**
     * 是否线上支付
     */
    private Boolean onlinePayFlag;

    /**
     * 提前购卡标识 false-否, true-是
     */
    private Boolean advancePayFlag;

    /**
     * 是否代付  false-否, true-是
     */
    private Boolean peerPayFlag;

    /**
     * 已经支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 待缴金额
     */
    private BigDecimal pendingPayAmount;

    /**
     * 分期付款记录
     */
    private List<OrderPayRecordVO> payRecordList;

    /**
     * 当前分期付款信息
     */
    private OrderPayRecordVO currentPayRecord;
    /**
     * 退款凭证
     */
    private String refundVoucherUrl;

    /**
     * 线下支付凭证文件
     */
    private String offlinePayVoucherFileId;

    private BigDecimal payRecordAmount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 购卡标识 0-无 1-新生购卡 2-老生续费
     */
    private Integer purchasedCardFlag;
}
