package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.order.domain.OrderOperate;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单操作记录
（时间逆序取最后一条和订单中的对应）视图对象 order_operate
 *
 * @date 2024-02-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderOperate.class)
public class OrderOperateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单操作id
     */
    @ExcelProperty(value = "订单操作id")
    private Long orderOperateId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "order_operate_status")
    private String orderOperateStatus;
    /**
     * 订单状态结合订单审核状态
     */
    private String orderWorkflowStatus;

    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;

    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;

    /**
     * 支付方式（0-线下 1-线上）
     */
    @ExcelProperty(value = "支付方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=线下支付 1=线上支付")
    private Integer payMode;

    /**
     * 收款金额（已支付才存在）
     */
    @ExcelProperty(value = "收款金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "已=支付才存在")
    private BigDecimal paymentAmount;

    /**
     * 退款金额（已退款才存在）
     */
    @ExcelProperty(value = "退款金额", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "已=退款才存在")
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String orderOperateRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long createBy;

    private Long createDept;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 创建人名称
     */
    private String createByName;
    /**
     * 退款账号名称
     */
    private String refundAccountName;

    /**
     * 退款账号
     */
    private String refundAccount;

    /**
     * 退款开户行信息
     */
    private String refundBankName;

    /**
     * 是否可以审核，当前是退款待审核的状态
     */
    private Boolean reviewable;
    /**
     * 审核通过是否需要上传退款凭证
     */
    private Boolean passNeedVoucher;

    /**
     * 审核驳回是否需要上传退款凭证
     */
    private Boolean rejectNeedVoucher;
    /**
     * 凭证名
     */
    private String voucherName;

    private String paymentType;
}
