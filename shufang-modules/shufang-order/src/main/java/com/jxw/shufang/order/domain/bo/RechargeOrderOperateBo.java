package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.order.domain.RechargeOrderOperate;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）业务对象 agent_order_operate
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RechargeOrderOperate.class, reverseConvertGenerate = false)
public class RechargeOrderOperateBo extends BaseEntity {

    /**
     * 订单操作id
     */
    @NotNull(message = "订单操作id不能为空", groups = { EditGroup.class })
    private Long orderOperateId;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
//    @NotBlank(message = "订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderOperateStatus;

    /**
     * 支付方式（已支付才存在 对应字典值，比如 微信、支付宝）
     */
    @NotBlank(message = "支付方式（已支付才存在 对应字典值，比如 微信、支付宝）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String paymentType;

    /**
     * 收款金额（已支付才存在）
     */
//    @NotNull(message = "收款金额（已支付才存在）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal paymentAmount;

    /**
     * 退款方式（已退款才存在 对应字典值，比如 微信、支付宝）
     */
//    @NotBlank(message = "退款方式（已退款才存在 对应字典值，比如 微信、支付宝）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String refundType;

    /**
     * 退款金额（已退款才存在）
     */
//    @NotNull(message = "退款金额（已退款才存在）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal refundAmount;

    /**
     * 备注
     */
//    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderOperateRemark;


}
