package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/17 17:02
 * @Version 1
 * @Description 是否定金付款
 */
public enum DepositAmountEnum {
    DEPOSIT(1, "定金付款"),
    NO_DEPOSIT(0, "非定金付款");
    private final Integer code;
    private final String desc;

    DepositAmountEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
