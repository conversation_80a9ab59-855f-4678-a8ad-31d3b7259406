package com.jxw.shufang.order.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.AccountTradeLogBo;
import com.jxw.shufang.order.domain.vo.AccountTradeLogVo;
import com.jxw.shufang.order.service.IAccountTradeLogService;
import com.jxw.shufang.system.api.RemoteDeptService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 账户流水 （扣款退费充值记录）
 * 前端访问路由地址为:/order/history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/tradeLog")
public class AccountTradeLogController extends BaseController {

    private final IAccountTradeLogService transactionHistoryService;
    @DubboReference
    private final RemoteDeptService remoteDeptService;
    @DubboReference
    private final RemoteBranchService remoteBranchService;

    /**
     * 查询账户流水 （扣款退费充值记录）列表
     */
    @SaCheckPermission("order:history:list")
    @GetMapping("/list")
    public TableDataInfo<AccountTradeLogVo> list(AccountTradeLogBo bo, PageQuery pageQuery) {
        if (ObjectUtils.isEmpty(bo)) {
            bo = new AccountTradeLogBo();
        }
        Long deptId = LoginHelper.getDeptId();
        boolean b = remoteDeptService.deptIsShop(deptId);
        if (Boolean.TRUE.equals(b)) {
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchByDeptId(deptId);
            if (ObjectUtils.isEmpty(remoteBranchVo)) {
                throw new ServiceException("门店不存在");
            }
            bo.setAccountType(AccountTradeTypeEnum.BRANCH.getCode());
            bo.setAccountId(remoteBranchVo.getBranchId());
        } else {
            bo.setAccountType(AccountTradeTypeEnum.AGENT.getCode());
            bo.setAccountId(deptId);
        }
        return transactionHistoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出账户流水 （扣款退费充值记录）列表
     */
//    @SaCheckPermission("order:history:export")
//    @Log(title = "账户流水 （扣款退费充值记录）", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
    public void export(AccountTradeLogBo bo, HttpServletResponse response) {
        List<AccountTradeLogVo> list = transactionHistoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "账户流水 （扣款退费充值记录）", AccountTradeLogVo.class, response);
    }

}
