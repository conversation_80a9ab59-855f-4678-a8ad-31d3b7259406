package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.enums.OrderOperateReviewStatus;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.AddOrderBo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.bo.OrderOperateBo;
import com.jxw.shufang.order.domain.bo.OrderProductBo;
import com.jxw.shufang.order.domain.dto.PreCalculatePriceDTO;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.service.IOrderService;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

/**
 * 订单
 * 前端访问路由地址为:/order/order
 *
 * @date 2024-02-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/order")
public class OrderController extends BaseController {

    private final IOrderService orderService;

    /**
     * 查询订单列表
     */
    @SaCheckPermission("order:order:list")
    @GetMapping("/list")
    public TableDataInfo<OrderVo> list(OrderBo bo, PageQuery pageQuery) {
        if(StringUtils.isBlank(pageQuery.getOrderByColumn())){
            pageQuery.setOrderByColumn("t.create_time");
            pageQuery.setIsAsc("desc");
        }
        return orderService.queryPageList(bo, pageQuery);
    }

    /**
     * 会员退费列表
     */
    @SaCheckPermission("order:order:refundList")
    @GetMapping("/refundList")
    public TableDataInfo<OrderVo> refundList(OrderBo bo, PageQuery pageQuery) {
        if (Boolean.TRUE.equals(bo.getReviewable())) {
            bo.setReviewStatus(OrderOperateReviewStatus.WAIT_REVIEW.getCode());
        }
        return orderService.queryRefundPageList(bo, pageQuery);
    }

    /**
     * 会员退费列表
     */
    @SaCheckPermission("order:order:refundList")
    @PostMapping("/refundList/export")
    public void exportRefundList(OrderBo bo, HttpServletResponse response) {
        ExcelUtil.exportExcel(orderService.listExportRefundOrder(bo), "订单", RefundOrderVo.class, response);
    }


    /**
     * 导出订单列表
     */
    @SaCheckPermission("order:order:export")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OrderBo bo, HttpServletResponse response) {
        List<OrderVo> list = orderService.queryList(bo);
        ExcelUtil.exportExcel(list, "订单", OrderVo.class, response);
    }

    /**
     * 获取订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("order:order:query")
    @GetMapping("/{orderId}")
    public R<OrderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long orderId) {
        return R.ok(orderService.queryById(orderId));
    }

    /**
     * 新增订单
     */
    @SaCheckPermission("order:order:add")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AddOrderBo bo) {
        this.validateCourseStartTime(bo.getCourseStartTime());
        orderService.insertByBo(bo);
        return R.ok();
    }

    /**
     * 修改订单
     */
    @SaCheckPermission("order:order:edit")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OrderBo bo) {
        return toAjax(orderService.updateByBo(bo));
    }

    /**
     * 删除订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("order:order:remove")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(orderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 选项列表
     *
     * @param bo bo
     * @date 2024/02/29 03:41:49
     */
    @SaCheckPermission("order:order:list")
    @GetMapping("/queryOptionList")
    public R<List<OrderVo>> queryOptionList(OrderBo bo) {
        return R.ok(orderService.queryOptionList(bo));
    }

    /**
     *  获取学生最近的订单会员产品
     * @param bo
     * @return
     */
    @SaCheckPermission("order:order:list")
    @GetMapping("/queryStudentLastOrder")
    public R<OrderVo> selectStudentLastOrder(OrderBo bo) {
       return R.ok(orderService.selectStudentLastOrderByStudentId(bo));
    }
    /**
     * 查询选项列表
     *
     * @param bo bo
     */
    @SaCheckPermission("student:product:optionList")
    @GetMapping("/queryProductOption")
    public R<List<RemoteProductVo>> queryProductOption(OrderProductBo bo) {
        return R.ok(orderService.queryProductOption(bo));
    }



    /**
     * 查询订单列表
     */
    @GetMapping("/initStudentCard")
    public R<Void>initStudentCard(@RequestParam(required = false) List<Long> studentIdList){
         orderService.initStudentCard(studentIdList);
         return R.ok();
    }

    /**
     * 获取介绍人列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("listReferrer")
    public TableDataInfo<OrderReferrerVo> listReferrer(OrderBo bo, PageQuery pageQuery) {
        return orderService.pageReferrer(bo, pageQuery);
    }

    /**
     * 查看订单状态类型
     */
    @GetMapping("/queryOrderStatusType")
    public R<List<OrderStatusVO>> queryOrderStatusType(){
        List<OrderStatusEnum> orderStatusVOList = OrderStatusEnum.queryOrderStatusList();
        List<OrderStatusVO> statusVOList = orderStatusVOList.stream()
            .map(m -> new OrderStatusVO(m.getCode(), m.getInfo())).toList();
        return R.ok(statusVOList);
    }

    /**
     * 预计算订单价格
     */
    @GetMapping("/preCalculateOrderPrice")
    public R<PreCalculateOrderPriceVO> preCalculateOrderPrice(@RequestParam(name = "productId") Long productId,
                                                              @RequestParam(name = "preferentialPrice") BigDecimal preferentialPrice,
                                                              @RequestParam(name = "courseStartTime") String courseStartTime,
                                                              @RequestParam(name = "studentPreferentialAmount") BigDecimal studentPreferentialAmount,
                                                              @RequestParam(name = "studentId") Long studentId) {
        if (StringUtils.isEmpty(courseStartTime)
            || productId == null
            || studentId == null) {
            throw new ServiceException("参数不能为空");
        }

        PreCalculatePriceDTO preCalculatePriceDTO = new PreCalculatePriceDTO();
        preCalculatePriceDTO.setProductId(productId);
        preCalculatePriceDTO.setPreferentialPrice(preferentialPrice);
        preCalculatePriceDTO.setCourseStartDate(this.getCourseStartDate(courseStartTime));
        preCalculatePriceDTO.setStudentPreferentialAmount(studentPreferentialAmount);
        preCalculatePriceDTO.setStudentId(studentId);
        return R.ok(orderService.preCalculateOrderPrice(preCalculatePriceDTO));
    }

    private Date getCourseStartDate(String courseStartTime) {
        LocalDate localDate = LocalDate.parse(courseStartTime);
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算订单价格
     * @param orderId
     * @param productId
     */
    @GetMapping("/calculateOrderPrice")
    public R<CalculateOrderPriceVO> calculateOrderPrice(@RequestParam(name = "orderId") Long orderId,
                                                        @RequestParam(name = "productId") Long productId) {
        if (orderId == null || productId == null) {
            throw new ServiceException("订单id或产品id不能为空");
        }
        CalculateOrderPriceVO calculateOrderPriceVO = new CalculateOrderPriceVO();
        calculateOrderPriceVO.setOrderId(orderId);
        calculateOrderPriceVO.setProductId(productId);
        calculateOrderPriceVO.setOrderPrice(orderService.calculateOrderPrice(orderId, productId));
        return R.ok(calculateOrderPriceVO);
    }

    @PostMapping("getRefundInfo")
    public R<OrderRefundInfoVo> getRefundInfo(@RequestBody OrderOperateBo bo) {
        return R.ok(orderService.getRefundInfo(bo));
    }

    private void validateCourseStartTime(String courseStartTime) {
        if (StringUtils.isEmpty(courseStartTime)) {
            return;
        }
        LocalDate localDate = LocalDate.parse(courseStartTime);
        if (localDate.isBefore(LocalDate.now())) {
            throw new ServiceException("课程开始时间不能早于当前时间");
        }
    }

}
