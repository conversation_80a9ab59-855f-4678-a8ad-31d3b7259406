package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.RechargeOrderOperateBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderOperateVo;

import java.util.Collection;
import java.util.List;

/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）Service接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface IRechargeOrderOperateService {

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    RechargeOrderOperateVo queryById(Long orderOperateId);

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
    TableDataInfo<RechargeOrderOperateVo> queryPageList(RechargeOrderOperateBo bo, PageQuery pageQuery);

    /**
     * 查询订单操作记录 （时间逆序取最后一条和订单中的对应）列表
     */
    List<RechargeOrderOperateVo> queryList(RechargeOrderOperateBo bo);

    /**
     * 新增订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    Boolean insertByBo(RechargeOrderOperateBo bo);

    /**
     * 修改订单操作记录 （时间逆序取最后一条和订单中的对应）
     */
    Boolean updateByBo(RechargeOrderOperateBo bo);

    /**
     * 校验并批量删除订单操作记录 （时间逆序取最后一条和订单中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 初始化订单操作信息
     */
    Long initRechargeOrderOperate(Long orderId);

    /**
     * 收款
     */
    void collection(RechargeOrderOperateBo bo);

    /**
     * 取消订单
     */
    void cancel(RechargeOrderOperateBo bo);
}
