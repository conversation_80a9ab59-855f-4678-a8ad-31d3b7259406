package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.StudentProductTemplateAuthBo;
import com.jxw.shufang.order.domain.vo.StudentProductResTemplateAuthVo;
import com.jxw.shufang.order.domain.vo.StudentProductTemplateAuthVo;
import com.jxw.shufang.order.service.IStudentProductTemplateAuthService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品（会员卡模板）授权
 * 前端访问路由地址为:/student/productTemplateAuth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/productTemplateAuth")
public class StudentProductTemplateAuthController extends BaseController {

    private final IStudentProductTemplateAuthService studentProductTemplateAuthService;

    /**
     * 获取已拥有的模板列表
     * @return
     */
    @SaCheckPermission("student:productTemplateAuth:list")
    @GetMapping("/list")
    public R<?> list() {
        return R.ok(studentProductTemplateAuthService.queryList());
    }

    /**
     * 获取可授权的模板列表
     */
    @GetMapping("/authList")
    public R<?> authList(StudentProductTemplateAuthBo bo) {
        return R.ok(studentProductTemplateAuthService.queryAuthList(bo));
    }

    /**
     * 导出产品（会员卡模板）授权列表
     */
    @SaCheckPermission("student:productTemplateAuth:export")
    @Log(title = "产品（会员卡模板）授权", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(StudentProductTemplateAuthBo bo, HttpServletResponse response) {
        List<StudentProductResTemplateAuthVo> list = studentProductTemplateAuthService.queryList();
        ExcelUtil.exportExcel(list, "产品（会员卡模板）授权", StudentProductResTemplateAuthVo.class, response);
    }

    /**
     * 获取产品（会员卡模板）授权详细信息
     *
     * @param templateDeptAuthId 主键
     */
    @SaCheckPermission("student:productTemplateAuth:query")
    @GetMapping("/{templateDeptAuthId}")
    public R<StudentProductTemplateAuthVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long templateDeptAuthId) {
        return R.ok(studentProductTemplateAuthService.queryById(templateDeptAuthId));
    }

    /**
     * 新增产品（会员卡模板）授权
     */
    @SaCheckPermission("student:productTemplateAuth:add")
    @Log(title = "产品（会员卡模板）授权", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody StudentProductTemplateAuthBo bo) {
        return toAjax(studentProductTemplateAuthService.insertByBo(bo));
    }

    /**
     * 修改产品（会员卡模板）授权
     */
    @SaCheckPermission("student:productTemplateAuth:edit")
    @Log(title = "产品（会员卡模板）授权", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody StudentProductTemplateAuthBo bo) {
        return toAjax(studentProductTemplateAuthService.updateByBo(bo));
    }

    /**
     * 删除产品（会员卡模板）授权
     *
     * @param templateDeptAuthIds 主键串
     */
    @SaCheckPermission("student:productTemplateAuth:remove")
    @Log(title = "产品（会员卡模板）授权", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateDeptAuthIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] templateDeptAuthIds) {
        return toAjax(studentProductTemplateAuthService.deleteWithValidByIds(List.of(templateDeptAuthIds), true));
    }

    /**
     * 批量授权
     */
    @SaCheckPermission("student:productTemplateAuth:auth")
    @Log(title = "产品（会员卡模板）授权", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/auth")
    public R<Void> auth(@Validated @RequestBody StudentProductTemplateAuthBo bo) {
        return toAjax(studentProductTemplateAuthService.authByBo(bo));
    }
}
