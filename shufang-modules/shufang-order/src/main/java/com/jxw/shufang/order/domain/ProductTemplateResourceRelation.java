package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 产品模板-资源关联对象 pms_product_template_resource_relation
 *
 * @date 2024-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pms_product_template_resource_relation")
public class ProductTemplateResourceRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品模板-资源关联关联ID
     */
    @TableId(value = "relation_id")
    private Long relationId;

    /**
     * 产品模板ID（pms_product_template表-product_template_id字段）
     */
    private Long productTemplateId;

    /**
     * 资源ID（课程或者会员卡ID）
     */
    private Long resourceId;

    /**
     * 创建部门
     */
    private Long createDept;
}
