package com.jxw.shufang.order.controller.management;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.ProductTemplateBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateVo;
import com.jxw.shufang.order.service.IProductTemplateService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品模板管理
 * 前端访问路由地址为:/order/productTemplate
 *
 * @date 2024-06-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/productTemplate")
public class ProductTemplateController extends BaseController {

    private final IProductTemplateService productTemplateService;

    /**
     * 查询产品模板管理列表
     */
    @SaCheckPermission("order:productTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<ProductTemplateVo> list(ProductTemplateBo bo, PageQuery pageQuery) {
        return productTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品模板管理列表
     */
    @SaCheckPermission("order:productTemplate:export")
    @Log(title = "产品模板管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductTemplateBo bo, HttpServletResponse response) {
        List<ProductTemplateVo> list = productTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品模板管理", ProductTemplateVo.class, response);
    }

    /**
     * 获取产品模板管理详细信息
     *
     * @param productTemplateId 主键
     */
    @SaCheckPermission("order:productTemplate:query")
    @GetMapping("/{productTemplateId}")
    public R<ProductTemplateVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long productTemplateId) {
        return R.ok(productTemplateService.queryById(productTemplateId));
    }

    /**
     * 新增产品模板管理
     */
    @SaCheckPermission("order:productTemplate:add")
    @Log(title = "产品模板管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductTemplateBo bo) {
        return toAjax(productTemplateService.insertByBo(bo));
    }

    /**
     * 修改产品模板管理
     */
    @SaCheckPermission("order:productTemplate:edit")
    @Log(title = "产品模板管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductTemplateBo bo) {
        return toAjax(productTemplateService.updateByBo(bo));
    }

    /**
     * 删除产品模板管理
     *
     * @param productTemplateIds 主键串
     */
    @SaCheckPermission("order:productTemplate:remove")
    @Log(title = "产品模板管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productTemplateIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] productTemplateIds) {
        return toAjax(productTemplateService.deleteWithValidByIds(List.of(productTemplateIds)));
    }

    /**
     * 查询产品模板选项列表
     */
    @SaCheckPermission("order:productTemplate:list")
    @GetMapping("/optionList")
    public R<List<ProductTemplateVo>> optionList(ProductTemplateBo bo) {
        List<ProductTemplateVo> list = productTemplateService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 上下架
     */
    @SaCheckPermission("system:productCourseTemplate:edit")
    @Log(title = "产品（课程模板）信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody ProductTemplateBo bo) {
        return toAjax(productTemplateService.updateByBo(bo));
    }
}
