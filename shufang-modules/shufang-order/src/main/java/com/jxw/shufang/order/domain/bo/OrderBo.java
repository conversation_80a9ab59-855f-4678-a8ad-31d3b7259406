package com.jxw.shufang.order.domain.bo;

import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import org.springframework.format.annotation.DateTimeFormat;

import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.Order;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单业务对象 order
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Order.class, reverseConvertGenerate = false)
public class OrderBo extends BaseEntity {

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = {EditGroup.class})
    private Long orderId;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long studentId;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    @NotNull(message = "经办人不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long salesPerson;

    /**
     * 订单编号
     */
//    @NotBlank(message = "订单编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;

    /**
     * 经办日期
     */
    @NotNull(message = "经办日期不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;

    /**
     * 订单操作id
     */
    // @NotNull(message = "订单操作id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orderOperateId;


    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 会员类型Id
     */
    @NotNull(message = "会员类型不能为空", groups = {AddGroup.class})
    private Long studentTypeId;

    @NotEmpty(message = "订单产品信息不能为空", groups = {AddGroup.class})
    List<OrderProductInfoBo> orderProductInfoList;



    /**
     * 会员姓名
     */
    private String studentName;

    /**
     * 会员帐户
     */
    private String studentAccount;

    /**
     * 会员顾问名称
     */
    private String consultantName;

    /**
     * 门店Id
     */
    private Long branchId;

    /**
     * 订单状态，枚举类 {@link OrderStatusEnum}
     */
    private String orderStatus;

    /**
     * 订单状态，枚举类 {@link OrderStatusEnum}
     */
    private List<String> orderStatusList;


    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 下单时间(经办日期)开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createOrderStartTime;

    /**
     * 下单时间(经办日期)结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createOrderEndTime;

    /**
     * 付款时间(订单操作时间)开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentStartTime;

    /**
     * 付款时间(订单操作时间)结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentEndTime;


    /**
     * 订单金额范围,开始
     */
    private String orderAmountStart;

    /**
     * 订单金额范围,结束
     */
    private String orderAmountEnd;


    /**
     * 商品（产品）
     */
    private Long productId;
    /**
     * 如果订单类型为旧卡升级  值为旧卡订单ID
     */
    private Long orderRelationId;

    /**
     * 0为 购买新卡 1为 旧卡升级
     */
    private Integer orderType;
    /**
     * 产品IdList
     */
    private List<Long> productIdList;

    private List<Long> notInStudentIdList;

    private List<Long> studentIdList;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    private List<Long> salesPersonIdList;

    /**
     * 订单工作流状态 base on OrderWorkflowStatusEnum
     */
    private String orderWorkflowStatus;
    /**
     * 审核状态
     */
    private Integer reviewStatus;
    /**
     * 退款申请时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date applyStartTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date applyEndTime;
    /**
     * 退款时间 （订单状态已退款）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date refundStartTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date refundEndTime;

    public void setCreateOrderEndTime(Date createOrderEndTime) {
        if (null != createOrderEndTime) {
            this.createOrderEndTime = DateUtil.endOfDay(createOrderEndTime);
        }
    }

    public void setPaymentEndTime(Date paymentEndTime) {
        if (null != paymentEndTime) {
            this.paymentEndTime = DateUtil.endOfDay(paymentEndTime);
        }
    }

    public void setApplyEndTime(Date applyEndTime) {
        if (null != applyEndTime) {
            this.applyEndTime = DateUtil.endOfDay(applyEndTime);
        }
    }

    public void setRefundEndTime(Date refundEndTime) {
        if (null != refundEndTime) {
            this.refundEndTime = DateUtil.endOfDay(refundEndTime);
        }
    }

    /**
     * 退款方式
     */
    private String refundType;
    /**
     * 排除的产品类型id
     */
    private Long excludeStudentTypeId;
    /**
     * 产品类型id
     */
    private Long singleStudentTypeId;

    /**
     * 分期付款开始时间
     */
    private String installmentPayDeadlineStartTime;

    /**
     * 分期付款结束时间
     */
    private String installmentPayDeadlineEndTime;

    /**
     * 是否可以审核，搜索待审核的记录
     */
    private Boolean reviewable;

    private List<Integer> reviewNodeList;
    /**
     * 线上或者线下支付方式：线上 1 线下 0
     */
    private Integer payMode;

    private List<Long> orderIdList;

    /**
     * 渠道订单号
     */
    private String channelOrderId;

    /**
     * 购卡标识 0-无 1-新生购卡 2-老生续费
     */
    private Integer purchasedCardFlag;
}
