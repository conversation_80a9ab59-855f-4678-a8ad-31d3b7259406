package com.jxw.shufang.order.remote;

import com.jxw.shufang.wxmp.api.RemoteWxService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2025/7/8 11:10
 * @Version 1
 * @Description
 */
@Service
public class WxService {
    @DubboReference
    private RemoteWxService remoteWxService;

    public String generatePayUrlByPath(String paramStr,String path) {
        return remoteWxService.generatePayUrlByPath(paramStr,path);
    }
}
