package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/21 20:00
 * @Version 1
 * @Description
 */
public enum PayOrderCloseTypeEnum {
    CANCEL_ORDER_TIME_OUT(1, "订单超时未支付"),
    CANCEL_ORDER_PAY_CANCEL(2, "订单支付取消"),
    CANCEL_ORDER_PAY_REFUND(3, "订单支付退款");
    private Integer code;
    private String message;

    PayOrderCloseTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
