package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.bo.ProductTemplateBo;
import com.jxw.shufang.order.domain.vo.ProductTemplateVo;

import java.util.List;

/**
 * 产品模板管理Service接口
 *
 * @date 2024-06-26
 */
public interface IProductTemplateService {

    /**
     * 查询产品模板管理
     *
     * @param productTemplateId 产品模板管理主键
     * @return 产品模板管理
     */
    ProductTemplateVo queryById(Long productTemplateId);

    /**
     * 查询产品模板管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品模板管理集合
     */
    TableDataInfo<ProductTemplateVo> queryPageList(ProductTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询产品模板管理列表
     *
     * @param bo 查询条件
     * @return 产品模板管理集合
     */
    List<ProductTemplateVo> queryList(ProductTemplateBo bo);

    /**
     * 新增产品模板管理
     *
     * @param bo 产品模板管理
     * @return 新增结果
     */
    Boolean insertByBo(ProductTemplateBo bo);

    /**
     * 修改产品模板管理
     *
     * @param bo 产品模板管理
     * @return 修改结果
     */
    Boolean updateByBo(ProductTemplateBo bo);

    /**
     * 校验并批量删除产品模板管理信息
     *
     * @param ids 待删除的主键集合
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(List<Long> ids);
}
