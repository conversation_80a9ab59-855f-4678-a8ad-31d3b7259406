package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.RechargeOrderProduct;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 代理商订单产品详情业务对象 agent_order_product_info
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = RechargeOrderProduct.class, reverseConvertGenerate = false)
public class RechargeOrderProductBo extends BaseEntity {

    /**
     * 订单详情id
     */
//    @NotNull(message = "订单详情id不能为空", groups = { EditGroup.class })
    private Long orderInfoId;

    /**
     * 订单id
     */
//    @NotNull(message = "订单id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long orderId;

    /**
     * 代理商id
     */
//    @NotNull(message = "代理商id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    /**
     * 产品名称
     */
//    @NotBlank(message = "产品名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String productName;

    /**
     * 产品天数
     */
//    @NotNull(message = "产品天数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productDays = 30L;

    /**
     * 产品数量
     */
    @NotNull(message = "产品数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long productNums;

    /**
     * 产品价格
     */
    @NotNull(message = "产品价格不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    @NotNull(message = "优惠价格（门店直减）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal preferentialPrice;

    /**
     * 总额
     */
//    @NotNull(message = "总额不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal amount;


}
