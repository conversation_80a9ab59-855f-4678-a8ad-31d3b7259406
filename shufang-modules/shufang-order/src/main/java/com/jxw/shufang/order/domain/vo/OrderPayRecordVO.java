package com.jxw.shufang.order.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/12 16:14
 * @Version 1
 * @Description 订单分期记录VO
 */
@Data
public class OrderPayRecordVO {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 分期订单编号
     */
    private String orderPayNo;
    /**
     * 已支付金额
     */
    private BigDecimal paidAmount;
    /**
     * 应付金额
     */
    private BigDecimal payAbleAmount;
    /**
     * 1待支付 2已支付 3已取消 4退款中 5已退款
     */
    private Integer paymentStatus;

    /**
     * 支付备注
     */
    private String paymentRemark;

    /**
     * 定金标识
     */
    private Boolean depositAmountFlag;

    /**
     * 支付时间
     */
    private Date paymentTime;
}
