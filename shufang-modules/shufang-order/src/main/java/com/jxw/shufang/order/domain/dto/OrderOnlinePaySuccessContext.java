package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.vo.OrderOperateVo;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/4 10:11
 * @Version 1
 * @Description
 */
@Data
public class OrderOnlinePaySuccessContext {
    private OrderPayRecord orderPayRecord;
    private Order order;
    private OrderOperateVo orderOperate;
}
