package com.jxw.shufang.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.StudentCardStatusEnum;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.StudentMembershipCard;
import com.jxw.shufang.order.domain.bo.StudentMembershipCardBo;
import com.jxw.shufang.order.domain.vo.StudentMembershipCardVo;
import com.jxw.shufang.order.mapper.StudentMembershipCardMapper;
import com.jxw.shufang.order.service.IStudentMembershipCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 学生会员卡Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
@RequiredArgsConstructor
@Service
public class StudentMembershipCardServiceImpl implements IStudentMembershipCardService, BaseService {

    private final StudentMembershipCardMapper baseMapper;

    /**
     * 查询学生会员卡
     */
    @Override
    public StudentMembershipCardVo queryById(Long studentMembershipCardId){
        return baseMapper.selectVoById(studentMembershipCardId);
    }

    /**
     * 查询学生会员卡列表
     */
    @Override
    public TableDataInfo<StudentMembershipCardVo> queryPageList(StudentMembershipCardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<StudentMembershipCard> lqw = buildQueryWrapper(bo);
        Page<StudentMembershipCardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询学生会员卡列表
     */
    @Override
    public List<StudentMembershipCardVo> queryList(StudentMembershipCardBo bo) {
        LambdaQueryWrapper<StudentMembershipCard> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<StudentMembershipCard> buildQueryWrapper(StudentMembershipCardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<StudentMembershipCard> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getStudentId() != null, StudentMembershipCard::getStudentId, bo.getStudentId());
        lqw.eq(bo.getProductId() != null, StudentMembershipCard::getProductId, bo.getProductId());
        lqw.eq(bo.getOrderId() != null, StudentMembershipCard::getOrderId, bo.getOrderId());
        lqw.eq(bo.getStudentTypeId() != null, StudentMembershipCard::getStudentTypeId, bo.getStudentTypeId());
        lqw.eq(bo.getCardStatus() != null, StudentMembershipCard::getCardStatus, bo.getCardStatus());
        lqw.eq(bo.getProductBeginDate() != null, StudentMembershipCard::getProductBeginDate, bo.getProductBeginDate());
        lqw.eq(bo.getProductEndDate() != null, StudentMembershipCard::getProductEndDate, bo.getProductEndDate());
        lqw.ne(bo.getNeStudentTypeId() != null, StudentMembershipCard::getStudentTypeId, bo.getNeStudentTypeId());
        return lqw;
    }

    /**
     * 新增学生会员卡
     */
    @Override
    public Boolean insertByBo(StudentMembershipCardBo bo) {
        StudentMembershipCard add = MapstructUtils.convert(bo, StudentMembershipCard.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStudentMembershipCardId(add.getStudentMembershipCardId());
        }
        return flag;
    }

    /**
     * 新增学生会员卡
     */
    @Override
    public Boolean insertBatchBo(List<StudentMembershipCardBo> membershipCardBos) {
        List<StudentMembershipCard> convert = MapstructUtils.convert(membershipCardBos, StudentMembershipCard.class);
        if (CollectionUtils.isEmpty(convert)) {
            return false;
        }
        convert.forEach(this::validEntityBeforeSave);
        return baseMapper.insertBatch(convert);
    }

    /**
     * 修改学生会员卡
     */
    @Override
    public Boolean updateByBo(StudentMembershipCardBo bo) {
        StudentMembershipCard update = MapstructUtils.convert(bo, StudentMembershipCard.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }


    @Override
    public void updateBatchById(List<StudentMembershipCardVo> studentMembershipCardVos) {
        if (CollectionUtils.isEmpty(studentMembershipCardVos)) {
            return;
        }
        List<StudentMembershipCard> updateList = MapstructUtils.convert(studentMembershipCardVos, StudentMembershipCard.class);
        baseMapper.updateBatchById(updateList);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(StudentMembershipCard entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除学生会员卡
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量删除学生会员卡
     */
    @Override
    public Boolean deleteByStudentIds(Collection<Long> studentIds) {
        if (CollectionUtils.isEmpty(studentIds)){
            return true;
        }
        LambdaQueryWrapper<StudentMembershipCard> lqw = Wrappers.lambdaQuery();
        lqw.in(StudentMembershipCard::getStudentId, studentIds);
        return baseMapper.delete(lqw) > 0;
    }

    @Override
    public List<StudentMembershipCardVo> queryListByOrders(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StudentMembershipCard> lqw = Wrappers.lambdaQuery();
        lqw.in(StudentMembershipCard::getOrderId, orderIds);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<StudentMembershipCardVo> queryStudentMembershipCard(List<Long> studentIds) {
        if (CollectionUtils.isEmpty(studentIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StudentMembershipCard> lqw = Wrappers.lambdaQuery();
        lqw.in(StudentMembershipCard::getStudentId, studentIds);
        lqw.in(StudentMembershipCard::getCardStatus, StudentCardStatusEnum.PAYER.getCode());
        lqw.ge(StudentMembershipCard::getProductEndDate, DateUtils.getNowDate());
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
