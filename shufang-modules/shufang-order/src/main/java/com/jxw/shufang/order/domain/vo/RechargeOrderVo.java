package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.RechargeOrder;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 代理商充值订单记录视图对象 agent_order
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RechargeOrder.class)
public class RechargeOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "组织id")
    private Long rechargeId;

    @ExcelProperty(value = "组织类型")
    private Integer rechargeType;

    /**
     * 经办代理商id
     */
    @ExcelProperty(value = "经办代理商id")
    private Long handlingDeptId;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 经办日期
     */
    @ExcelProperty(value = "经办日期")
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    @ExcelProperty(value = "经办人")
    private String handlingPerson;

    /**
     * 订单操作id
     */
    @ExcelProperty(value = "订单操作id")
    private Long orderOperateId;

    /**
     * 下单时间
     */
    private Date createTime;


    private String rechargeName;
    private String adminNickName;
    private String adminUserName;

    /**
     * 产品天数
     */
    @ExcelProperty(value = "产品天数")
    private Long productDays;

    /**
     * 产品数量
     */
    @ExcelProperty(value = "产品数量")
    private Long productNums;

    /**
     * 产品价格
     */
    @ExcelProperty(value = "产品价格")
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    @ExcelProperty(value = "优惠价格")
    private BigDecimal preferentialPrice;

    /**
     * 总额
     */
    @ExcelProperty(value = "总额")
    private BigDecimal amount;

    /**
     * 订单状态（1待支付 2已支付 3已取消
     */
    @ExcelProperty(value = "订单状态")
    private Integer orderStatus;

    private String paymentType;
    private BigDecimal paymentAmount;
    private Date opCreateTime;

    /**
     * 经办组织名称
     */
    private String handlingDeptName;

}
