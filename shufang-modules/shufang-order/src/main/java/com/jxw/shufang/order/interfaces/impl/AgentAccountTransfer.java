package com.jxw.shufang.order.interfaces.impl;


import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.interfaces.AccountTransferProcessor;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@RequiredArgsConstructor
public class AgentAccountTransfer implements AccountTransferProcessor {
    @DubboReference
    private RemoteDeptService remoteDeptService;

    @Override
    public Boolean support(AccountTradeTypeEnum typeEnum) {
        return AccountTradeTypeEnum.AGENT.equals(typeEnum);
    }

    @Override
    public void to(AccountInfo accountInfo, OrderInfo orderInfo) {
        RemoteDeptAccountVo vo = remoteDeptService.selectDeptByIdWithoutCache(accountInfo.getAccountId());
        if (ObjectUtils.isEmpty(vo)) {
            throw new ServiceException("转入账号不存在");
        }
        remoteDeptService.transfer(vo.getDeptId(), orderInfo.getAmount());

    }

    @Override
    public void from(AccountInfo accountInfo, OrderInfo orderInfo) {
        RemoteDeptAccountVo vo = remoteDeptService.selectDeptByIdWithoutCache(accountInfo.getAccountId());
        if (ObjectUtils.isEmpty(vo)) {
            throw new ServiceException("转入账号不存在");
        }
        if (accountInfo.getAccountId() == 100) {
            return;
        }
        if (vo.getRemainTime() >= orderInfo.getAmount()) {
            remoteDeptService.transfer(vo.getDeptId(), -orderInfo.getAmount());
        } else {
            throw new ServiceException("账户余额不足");
        }
    }
}
