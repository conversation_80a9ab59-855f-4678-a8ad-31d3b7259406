package com.jxw.shufang.order.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品（会员卡模板）业务对象 student_product_template
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
public class StudentProductTemplateStatusBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板id
     */
    @NotNull(message = "模板id不能为空")
    private Long templateId;


    /**
     * （0上架 1下架）
     */
    private Integer productStatus;


}
