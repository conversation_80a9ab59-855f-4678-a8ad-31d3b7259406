package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.enums.PayOrderStatusEnum;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/28 11:44
 * @Version 1
 * @Description
 */
public class OrderPayTimeAmountContext {
    private List<OrderOperate> orderOperateList;
    private List<OrderPayRecord> orderPayRecords;
    private List<Order> orders;

    private Map<Long, Date> payTimeMap;
    private Map<Long, BigDecimal> payAmountMap;

    public static OrderPayTimeAmountContext of(List<OrderOperate> orderOperateList,
                                               List<OrderPayRecord> orderPayRecords,
                                               List<Order> orders) {
        OrderPayTimeAmountContext context = new OrderPayTimeAmountContext();
        context.orderOperateList = orderOperateList;
        context.orderPayRecords = orderPayRecords;
        context.orders = orders;
        return context;
    }

    public OrderPayTimeAmountContext execute() {
        Map<Long, List<OrderOperate>> operateMap = orderOperateList.stream()
            .collect(Collectors.groupingBy(OrderOperate::getOrderId));

        Map<Long, List<OrderPayRecord>> payRecordMap = orderPayRecords.stream()
            .collect(Collectors.groupingBy(OrderPayRecord::getOrderId));

        // 合并处理逻辑
        Map<Long, Date> payTimeResult = new HashMap<>();
        Map<Long, BigDecimal> amountResult = new HashMap<>();

        for (Order order : orders) {
            Long orderId = order.getOrderId();
            boolean historyOrder = (order.getCourseStartTime() == null);

            if (historyOrder) {
                processHistoryOrder(orderId, operateMap, payTimeResult, amountResult);
            } else {
                processCurrentOrder(orderId, payRecordMap, payTimeResult, amountResult);
            }
        }

        this.payTimeMap = payTimeResult;
        this.payAmountMap = amountResult;
        return this;
    }

    public Date getPayTime(long orderId) {
        return payTimeMap.get(orderId);
    }

    public BigDecimal getPayAmount(long orderId) {
        return payAmountMap.get(orderId);
    }

    private void processHistoryOrder(Long orderId,
                                     Map<Long, List<OrderOperate>> operateMap,
                                     Map<Long, Date> timeResult,
                                     Map<Long, BigDecimal> amountResult) {
        List<OrderOperate> operates = operateMap.getOrDefault(orderId, Collections.emptyList());

        // 支付时间：最后支付时间
        Date payTime = operates.stream()
            .filter(op -> OrderStatusEnum.PAYED.getCode().equals(op.getOrderOperateStatus()))
            .map(OrderOperate::getCreateTime)
            .max(Date::compareTo)
            .orElse(null);
        timeResult.put(orderId, payTime);

        // 支付金额：累加有效支付
        BigDecimal amount = operates.stream()
            .filter(op -> OrderStatusEnum.PAYED.getCode().equals(op.getOrderOperateStatus()))
            .map(OrderOperate::getPaymentAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        amountResult.put(orderId, amount);
    }

    private void processCurrentOrder(Long orderId,
                                     Map<Long, List<OrderPayRecord>> payRecordMap,
                                     Map<Long, Date> timeResult,
                                     Map<Long, BigDecimal> amountResult) {
        List<OrderPayRecord> records = payRecordMap.getOrDefault(orderId, Collections.emptyList());

        // 支付时间：最后支付时间
        Date payTime = records.stream()
            .filter(pr -> !PayOrderStatusEnum.WAIT_PAY.getCode().equals(pr.getPaymentStatus())
                && !PayOrderStatusEnum.CANCEL.getCode().equals(pr.getPaymentStatus()))
            .map(OrderPayRecord::getPaymentTime)
            .max(Date::compareTo)
            .orElse(null);
        timeResult.put(orderId, payTime);

        // 支付金额：累加有效支付
        BigDecimal amount = records.stream()
            .filter(pr -> !PayOrderStatusEnum.WAIT_PAY.getCode().equals(pr.getPaymentStatus())
                && !PayOrderStatusEnum.CANCEL.getCode().equals(pr.getPaymentStatus()))
            .map(OrderPayRecord::getAmount)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        amountResult.put(orderId, amount);
    }

}
