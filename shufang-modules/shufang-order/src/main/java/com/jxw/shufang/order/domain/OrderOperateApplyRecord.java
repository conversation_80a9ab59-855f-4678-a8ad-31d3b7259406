package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * @author: cyj
 * @date: 2025/3/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_operate_apply_record")
public class OrderOperateApplyRecord extends BaseEntity{
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 退款申请ID
     */
    @TableId
    private Long applyId;
    /**
     * 订单操作ID
     */
    private Long orderOperateId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;
    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;
    /**
     * 审核意见
     */
    private String remark;
    /**
     * 订单操作记录状态
     */
    private String orderOperateStatus;
    /**
     * 凭证
     */
    private Long voucher;

    /**
     * 凭证名称
     */
    private String voucherName;
}
