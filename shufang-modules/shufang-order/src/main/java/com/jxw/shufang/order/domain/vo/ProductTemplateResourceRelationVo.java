package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.ProductTemplateResourceRelation;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 产品模板-资源关联视图对象 pms_product_template_resource_relation
 *
 * @date 2024-06-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProductTemplateResourceRelation.class)
public class ProductTemplateResourceRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品模板-资源关联关联ID
     */
    @ExcelProperty(value = "关联ID")
    private Long relationId;

    /**
     * 产品模板ID（pms_product_template表-product_template_id字段）
     */
    @ExcelProperty(value = "产品模板ID")
    private Long productTemplateId;

    /**
     * 资源ID（课程或者会员卡ID）
     */
    @ExcelProperty(value = "资源ID")
    private Long resourceId;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
}
