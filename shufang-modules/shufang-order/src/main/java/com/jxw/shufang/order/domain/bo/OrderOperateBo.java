package com.jxw.shufang.order.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.OrderOperate;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单操作记录
（时间逆序取最后一条和订单中的对应）业务对象 order_operate
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderOperate.class, reverseConvertGenerate = false)
public class OrderOperateBo extends BaseEntity {

    /**
     * 订单操作id
     */
    //@NotNull(message = "订单操作id不能为空", groups = { EditGroup.class })
    private Long orderOperateId;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空", groups = { EditGroup.class })
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
    private String orderOperateStatus;

    private String paymentType;
    /**
     * 支付方式（0-线下 1-线上）
     */
    private Integer payMode;

    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;
    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;

    /**
     * 收款金额（已支付才存在）
     */
    //@NotNull(message = "收款金额（已支付才存在）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal paymentAmount;

    /**
     * 退款金额（已退款才存在）
     */
    //@NotNull(message = "退款金额（已退款才存在）不能为空", groups = { AddGroup.class, EditGroup.class })
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    //@NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String orderOperateRemark;

    /**
     * 支付凭证url
     */
    private String paymentVoucherImgId;

    /**
     * 退款账号名称
     */
    private String refundAccountName;

    /**
     * 退款账号
     */
    private String refundAccount;

    /**
     * 退款开户行信息
     */
    private String refundBankName;
    /**
     * 退单时间
     */
    private Date refundStartTime;
}
