package com.jxw.shufang.order.service;

import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.enums.PayOrderStatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 16:42
 * @Version 1
 * @Description
 */
public interface OrderPayRecordDao {

    /**
     * 查询有效订单支付记录
     * @param orderId
     * @param orderPayTypeEnums
     * @return
     */
    List<OrderPayRecord> queryValidRecordByOrderId(Long orderId, List<PayOrderStatusEnum> orderPayTypeEnums);

    OrderPayRecord queryRecordByOrderPayNo(String orderPayNo);

    int updateOrderPayRecordByPayNo(OrderPayRecord orderPayRecord,String orderPayNo);

    int insertOrderPayRecord(OrderPayRecord orderPayRecord);

    void cancelOrderPayRecord(String orderPayNo);

    OrderPayRecord queryLastNoPayRecordByOrderId(Long orderId);

    List<OrderPayRecord> queryOrderPayRecordByOrderIds(List<Long> orderIds);

    /**
     * 获取订单完整的支付记录 包含异常支付的记录
     * @param orderId
     * @return
     */
    List<OrderPayRecord> queryOrderAllPayRecord(Long orderId);
}
