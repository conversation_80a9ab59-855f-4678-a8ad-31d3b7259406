package com.jxw.shufang.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.enums.TradeTypeEnum;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.AccountTradeLog;
import com.jxw.shufang.order.domain.bo.AccountTradeLogBo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.bo.RechargeOrderBo;
import com.jxw.shufang.order.domain.vo.AccountTradeLogVo;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import com.jxw.shufang.order.domain.vo.OrderVo;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;
import com.jxw.shufang.order.mapper.AccountTradeLogMapper;
import com.jxw.shufang.order.service.IAccountTradeLogService;
import com.jxw.shufang.order.service.IOrderService;
import com.jxw.shufang.order.service.IRechargeOrderService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 账户流水 （扣款退费充值记录）Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor
@Service
public class AccountTradeLogServiceImpl implements IAccountTradeLogService, BaseService {

    private final AccountTradeLogMapper baseMapper;
    /**
     * 会员订单
     */
    private final IOrderService orderService;
    private final IRechargeOrderService rechargeOrderService;
    @DubboReference
    private RemoteStudentService remoteStudentService;
    @DubboReference
    private RemoteBranchService remoteBranchService;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    /**
     * 查询账户流水 （扣款退费充值记录）列表
     */
    @Override
    public TableDataInfo<AccountTradeLogVo> queryPageList(AccountTradeLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AccountTradeLog> lqw = buildQueryWrapper(bo);
        Page<AccountTradeLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<AccountTradeLogVo> records = result.getRecords();
        if (!CollectionUtils.isEmpty(records)) {
            buildResult(records, bo.getAccountType());
        }

        return TableDataInfo.build(result);
    }

    private void buildResult(List<AccountTradeLogVo> records, Integer accountType) {
        if (AccountTradeTypeEnum.AGENT.getCode().equals(accountType)) {
            buildResultAgent(records);
        }
        if (AccountTradeTypeEnum.BRANCH.getCode().equals(accountType)) {
            buildResultBranch(records);
        }

    }

    private void buildResultBranch(List<AccountTradeLogVo> records) {
        //如果是门店 有代理商充值 以及 学生购卡 退卡
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        Map<Long, RemoteStudentVo> studnetVoMap = getStudnetVoMap(records);
        Map<Long, RemoteDeptVo> remoteDeptVoMap = getDeptVoMap(records);
        Map<Long, OrderVo> orderVoMap = getOrderVoMap(records);
        Map<Long, RechargeOrderVo> rechargeVoMap = getRechargeVoMap(records);


        //给【会员姓名（账号后四位）】购买【会员卡名称】
        //String message = "给【" + memberName + "（" + accountLastFourDigits + "）】购买【" + membershipCardName + "】";
        //收到【会员姓名（账号后四位）】的退回的【会员卡名称】剩余天数
        //String message = "收到【" + memberName + "（" + accountLastFourDigits + "）】的退回的【" + membershipCardName + "】：" + remainingDays;
        //收到【代理商组织名称】充值【充值的天数】
        //String message = "收到【" + agentOrganizationName + "】充值【" + rechargeDays + "天】";
        for (AccountTradeLogVo record : records) {
            String reason = "";
            String handlingPerson = "";
            String studentName = "";
            String studentPhone = "";
            String deptName = "";
            Integer amount = record.getAmount();
            String orderNo = "";
            String cardName = "";
            if (record.getOrderType().equals(OrderTypeEnum.CHARGE.getCode())) {
                RechargeOrderVo rechargeOrderVo = rechargeVoMap.get(record.getOrderId());
                if (!ObjectUtils.isEmpty(rechargeOrderVo)) {
                    orderNo = rechargeOrderVo.getOrderNo();
                    handlingPerson = rechargeOrderVo.getHandlingPerson();
                }
            } else {
                OrderVo orderVo = orderVoMap.get(record.getOrderId());
                if (!ObjectUtils.isEmpty(orderVo)) {
                    orderNo = orderVo.getOrderNo();
                    handlingPerson = orderVo.getHandlingPerson();
                }
                if (!CollectionUtils.isEmpty(orderVo.getOrderProductInfoList())) {
                    OrderProductInfoVo orderProductInfoVo = orderVo.getOrderProductInfoList().get(0);
                    cardName = orderProductInfoVo.getProductName();
                }
            }
            if (AccountTradeTypeEnum.STUDENT.getCode().equals(record.getReplyAccountType())) {
                RemoteStudentVo remoteStudentVo = studnetVoMap.get(record.getReplyAccount());
                studentName = remoteStudentVo.getStudentName();
                studentPhone = getLastFourDigits(remoteStudentVo.getStudentAccount());
            } else {
                RemoteDeptVo remoteDeptVo = remoteDeptVoMap.get(record.getReplyAccount());
                deptName = remoteDeptVo.getDeptName();
            }
            if (TradeTypeEnum.INCOME.getCode().equals(record.getTradeType())) {
                if (record.getReplyAccountType().equals(AccountTradeTypeEnum.AGENT.getCode())) {
                    reason = "收到【" + deptName + "】充值【" + amount + "天】";
                } else {
                    reason = "回收【" + studentName + "（" + studentPhone + "）】的【" + cardName + "】剩余天数";
                }
            } else {
                reason = "给【" + studentName + "（" + studentPhone + "）】购买【" + cardName + "】";
            }
            //收到【代理商组织名称】充值【充值的天数】
            record.setReason(reason);
            record.setHandlingPerson(handlingPerson);
            record.setOrderNo(orderNo);
        }


    }

    private Map<Long, RemoteBranchVo> getBranchVoMap(List<AccountTradeLogVo> records) {
        List<Long> list = records.stream().filter(v -> v.getReplyAccountType().equals(AccountTradeTypeEnum.BRANCH.getCode())).map(AccountTradeLogVo::getReplyAccount).toList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(list);
        List<RemoteBranchVo> branchList = remoteBranchService.selectBranchList(remoteBranchBo);
        if (CollectionUtils.isEmpty(branchList)) {
            return Collections.emptyMap();
        }
        return branchList.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, Function.identity()));
    }


    private Map<Long, RechargeOrderVo> getRechargeVoMap(List<AccountTradeLogVo> records) {
        List<Long> list = records.stream().filter(v -> v.getOrderType().equals(OrderTypeEnum.CHARGE.getCode())).map(AccountTradeLogVo::getOrderId).toList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        RechargeOrderBo remoteDeptBo = new RechargeOrderBo();
        remoteDeptBo.setOrderIdList(list);
        List<RechargeOrderVo> rechargeOrderVoList = rechargeOrderService.queryList(remoteDeptBo);
        if (CollectionUtils.isEmpty(rechargeOrderVoList)) {
            return Collections.emptyMap();
        }
        return rechargeOrderVoList.stream().collect(Collectors.toMap(RechargeOrderVo::getOrderId, Function.identity()));
    }

    private Map<Long, OrderVo> getOrderVoMap(List<AccountTradeLogVo> records) {
        List<Long> list = records.stream().filter(v -> v.getOrderType().equals(OrderTypeEnum.STUDENT.getCode())).map(AccountTradeLogVo::getOrderId).toList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        OrderBo orderBo = new OrderBo();
        orderBo.setOrderIdList(list);
        List<OrderVo> orderVoList = orderService.selectOrderListAndInfo(orderBo);
        if (CollectionUtils.isEmpty(orderVoList)) {
            return Collections.emptyMap();
        }
        return orderVoList.stream().collect(Collectors.toMap(OrderVo::getOrderId, Function.identity()));
    }

    private Map<Long, RemoteDeptVo> getDeptVoMap(List<AccountTradeLogVo> records) {
        List<Long> list = records.stream()
            .filter(v -> v.getReplyAccountType().equals(AccountTradeTypeEnum.AGENT.getCode()))
            .map(AccountTradeLogVo::getReplyAccount).distinct().toList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        RemoteDeptBo remoteDeptBo = new RemoteDeptBo();
        remoteDeptBo.setDeptIdList(list);
        List<RemoteDeptVo> deptList = remoteDeptService.getDeptListWithoutPermission(remoteDeptBo);
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyMap();
        }
        return deptList.stream().collect(Collectors.toMap(RemoteDeptVo::getDeptId, Function.identity()));
    }

    private Map<Long, RemoteStudentVo> getStudnetVoMap(List<AccountTradeLogVo> records) {
        List<Long> list = records.stream().filter(v -> v.getReplyAccountType().equals(AccountTradeTypeEnum.STUDENT.getCode())).map(AccountTradeLogVo::getReplyAccount).toList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        RemoteStudentBo remoteStudentBo = new RemoteStudentBo();
        remoteStudentBo.setStudentIds(list);
        List<RemoteStudentVo> remoteStudentVos = remoteStudentService.queryStudentList(remoteStudentBo);
        if (CollectionUtils.isEmpty(remoteStudentVos)) {
            return Collections.emptyMap();
        }
        return remoteStudentVos.stream().collect(Collectors.toMap(RemoteStudentVo::getStudentId, Function.identity()));
    }


    public String getLastFourDigits(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return phoneNumber;
        }
        return phoneNumber.substring(phoneNumber.length() - 4);
    }

    /**
     * 给【代理商组织名称】充值【充值的天数】
     * 给【门店名称】充值【充值的天数】
     * 给【代理商组织名称】充值【充值的天数】
     * 收到【代理商组织名称】充值【充值的天数】
     * 给【门店名称】充值【充值的天数】
     * 给【会员姓名（账号后四位）】购买【会员卡名称】
     * 收到【会员姓名（账号后四位）】的退回的【会员卡名称】剩余天数
     * 收到【代理商组织名称】充值【充值的天数】
     */
    private void buildResultAgent(List<AccountTradeLogVo> records) {

        //如果是代理商 有上级代理商充值 以及 下级代理商充值门店
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Map<Long, RemoteDeptVo> remoteDeptVoMap = getDeptVoMap(records);
        Map<Long, RechargeOrderVo> rechargeVoMap = getRechargeVoMap(records);
        Map<Long, RemoteBranchVo> branchVoMap = getBranchVoMap(records);

        //收到【代理商组织名称】充值【充值的天数】
        //给【代理商组织名称/门店】充值【充值的天数】;
        for (AccountTradeLogVo record : records) {
            String reason = "";
            String name = "";
            Integer amount = record.getAmount();
            if (AccountTradeTypeEnum.AGENT.getCode().equals(record.getReplyAccountType())) {
                RemoteDeptVo remoteDeptVo = remoteDeptVoMap.get(record.getReplyAccount());
                if (!ObjectUtils.isEmpty(remoteDeptVo)) {
                    name = remoteDeptVo.getDeptName();
                }
            } else {
                RemoteBranchVo remoteBranchVo = branchVoMap.get(record.getReplyAccount());
                if (!ObjectUtils.isEmpty(remoteBranchVo)) {
                    name = remoteBranchVo.getBranchName();
                }
            }
            if (TradeTypeEnum.INCOME.getCode().equals(record.getTradeType())) {
                reason = "收到【" + name + "】充值【" + amount + "天】";
            } else {
                reason = "给【" + name + "】充值【" + amount + "天】";
            }

            record.setReason(reason);
            Long orderId = record.getOrderId();
            RechargeOrderVo rechargeOrderVo = rechargeVoMap.get(orderId);
            if (!ObjectUtils.isEmpty(rechargeOrderVo)) {
                record.setHandlingPerson(rechargeOrderVo.getHandlingPerson());
                record.setOrderNo(rechargeOrderVo.getOrderNo());
            }
        }
    }

    /**
     * 查询账户流水 （扣款退费充值记录）列表
     */
    @Override
    public List<AccountTradeLogVo> queryList(AccountTradeLogBo bo) {
        LambdaQueryWrapper<AccountTradeLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AccountTradeLog> buildQueryWrapper(AccountTradeLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AccountTradeLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTradeType() != null, AccountTradeLog::getTradeType, bo.getTradeType());
        lqw.eq(bo.getOrderId() != null, AccountTradeLog::getOrderId, bo.getOrderId());
        lqw.eq(bo.getOrderType() != null, AccountTradeLog::getOrderType, bo.getOrderType());
        lqw.eq(bo.getAccountId() != null, AccountTradeLog::getAccountId, bo.getAccountId());
        lqw.eq(bo.getAccountType() != null, AccountTradeLog::getAccountType, bo.getAccountType());
        lqw.eq(bo.getReplyAccount() != null, AccountTradeLog::getReplyAccount, bo.getReplyAccount());
        lqw.eq(bo.getReplyAccountType() != null, AccountTradeLog::getReplyAccountType, bo.getReplyAccountType());
        lqw.eq(bo.getAmount() != null, AccountTradeLog::getAmount, bo.getAmount());
        lqw.orderByDesc(AccountTradeLog::getCreateTime);
        return lqw;
    }

    /**
     * 新增账户流水 （扣款退费充值记录）
     */
    @Override
    public Boolean insertByBo(AccountTradeLogBo bo) {
        AccountTradeLog add = MapstructUtils.convert(bo, AccountTradeLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAccountTradeLogId(add.getAccountTradeLogId());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AccountTradeLog entity) {
        //做一些数据校验,如唯一约束
    }


    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
