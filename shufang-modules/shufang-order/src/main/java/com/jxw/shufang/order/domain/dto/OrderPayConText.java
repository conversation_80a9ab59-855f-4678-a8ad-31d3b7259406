package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderPayRecord;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 17:46
 * @Version 1
 * @Description 订单支付上下文
 */
@Data
public class OrderPayConText {
    private Long orderId;
    private Order order;
    private BigDecimal actualAmount;
    private List<OrderPayRecord> orderPayRecordList;
}
