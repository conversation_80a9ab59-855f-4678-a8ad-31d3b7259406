package com.jxw.shufang.order.interfaces;


import com.jxw.shufang.order.enums.PayModeEnum;
import com.jxw.shufang.order.service.OrderCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/18 10:51
 * @Version 1
 * @Description
 */
@Component
public class PaymentCollectionFactory {

    private final Map<PayModeEnum, OrderCollectionService> collectionServiceMap = new HashMap<>();

    @Autowired
    public PaymentCollectionFactory(List<OrderCollectionService> orderCollectionServiceList) {
        orderCollectionServiceList.forEach(service ->
            collectionServiceMap.put(service.payMode(), service));
    }

    public OrderCollectionService executePayment(PayModeEnum mode) {
        OrderCollectionService strategy = collectionServiceMap.get(mode);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付模式");
        }
        return strategy;
    }
}
