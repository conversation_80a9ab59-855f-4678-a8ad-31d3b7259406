package com.jxw.shufang.order.domain.convert;

import com.jxw.shufang.order.api.domain.bo.RemoteStudentMembershipBo;
import com.jxw.shufang.order.domain.bo.StudentMembershipCardBo;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteStudentMembershipBoConvertStudentMembershipBo extends BaseMapper<RemoteStudentMembershipBo, StudentMembershipCardBo> {
}




