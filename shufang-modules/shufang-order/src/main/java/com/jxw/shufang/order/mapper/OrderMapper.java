package com.jxw.shufang.order.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.order.domain.vo.OrderReferrerVo;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.vo.OrderVo;

import java.util.List;

/**
 * 订单Mapper接口
 *
 * @date 2024-02-27
 */
public interface OrderMapper extends BaseMapperPlus<Order, OrderVo> {

    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.user_id")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    Page<OrderVo> selectPageList(@Param("page") Page<Order> build,@Param(Constants.WRAPPER) QueryWrapper<Order> lqw);

    //重写底层方法，实现默认方法的数据权限
    //@Override
    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "create_dept"),
    //    @DataColumn(key = "userName", value = "user_id")
    //})
    //@BranchColumn(key = "deptName", value = "create_dept")
    List<Order> selectList(IPage<Order> page, @Param(Constants.WRAPPER) Wrapper<Order> queryWrapper);

    //
    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "t.create_dept"),
    //    @DataColumn(key = "userName", value = "t.user_id")
    //})
    //@BranchColumn(key = "deptName", value = "t.create_dept")
    List<OrderVo> queryOptionList(@Param(Constants.WRAPPER) QueryWrapper<Order> queryWrapper);

    List<Long> getEffectiveOrderStudentIds(List<Long> studentIdList, List<Long> productIdList);

    List<OrderVo> selectOrderListAndInfo(@Param(Constants.WRAPPER) QueryWrapper<Order> lqw);

    List<OrderVo> selectOrderListAndOperateList(@Param(Constants.WRAPPER) QueryWrapper<Order> lqw);

    List<OrderVo> selectStudentLastOrder(@Param(Constants.WRAPPER) QueryWrapper<Order> orderQueryWrapper,@Param("bo") OrderBo bo);

    OrderVo queryLastOrderByType(@Param("studentId") Long studentId,
        @Param("excludeStudentTypeId") Long excludeStudentTypeId);

    Page<OrderReferrerVo> pageReferrer(@Param(Constants.WRAPPER) QueryWrapper<Order> studentQueryWrapper,
        @Param("page") Page<Object> build);

    /**
     * huo
     *
     * @param orderBo
     * @return
     */
    List<OrderVo> selectBatchStudentLastOrder(@Param(Constants.WRAPPER) QueryWrapper<Order> orderBo,
        @Param("bo") OrderBo bo);

    List<OrderVo> selectBatchStudentOrder(@Param(Constants.WRAPPER) QueryWrapper<Order> orderBo,
        @Param("bo") OrderBo bo);
}
