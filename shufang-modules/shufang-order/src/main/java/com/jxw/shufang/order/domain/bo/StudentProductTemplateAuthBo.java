package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.StudentProductTemplateAuth;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品（会员卡模板）授权业务对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = StudentProductTemplateAuth.class, reverseConvertGenerate = false)
public class StudentProductTemplateAuthBo extends BaseEntity {

    /**
     * 模板权限id
     */
//    @NotNull(message = "模板权限id不能为空", groups = { EditGroup.class })
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
//    @NotNull(message = "模板id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long templateId;

    /**
     * 代理商id
     */
    @NotNull(message = "组织id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    /**
     * 代理商ID集合
     */
    private List<Long> deptIds;

    /**
     * 会员ID
     */
    private Long studentId;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    private Integer templateType;

    /**
     * 模板信息列表
     */
    private List<TemplateInfo> templateInfos = new ArrayList<>();

    /**
     * 是否只返回授权的模板列表，默认false
     */
    private Boolean showAuth;

    /**
     * 模板信息对象
     */
    @Data
    public static class TemplateInfo {
        /**
         * 模板类型 0-会员卡模板 1-课程模板
         */
        private Integer templateType = 0;

        /**
         * 模板ID数组，为空将清除所有
         */
        private List<Long> templateIds;
    }

}
