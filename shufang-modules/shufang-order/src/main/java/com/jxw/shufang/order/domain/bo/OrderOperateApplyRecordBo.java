package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.OrderOperateApplyRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/3/17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OrderOperateApplyRecord.class, reverseConvertGenerate = false)
@NoArgsConstructor
@AllArgsConstructor
public class OrderOperateApplyRecordBo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 退款申请ID
     */
    private Long applyId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单操作ID
     */
    private Long orderOperateId;
    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;
    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;
    /**
     * 审核意见
     */
    private String remark;
    /**
     * 订单操作记录状态
     */
    private String orderOperateStatus;

    private Long voucher;

    private String voucherName;

    public OrderOperateApplyRecordBo(Long applyId, Long orderId, Long orderOperateId, Integer reviewStatus,
        Integer reviewNode, String remark, String orderOperateStatus, Long voucher) {
        this.applyId = applyId;
        this.orderId = orderId;
        this.orderOperateId = orderOperateId;
        this.reviewStatus = reviewStatus;
        this.reviewNode = reviewNode;
        this.remark = remark;
        this.orderOperateStatus = orderOperateStatus;
        this.voucher = voucher;
    }

    public OrderOperateApplyRecordBo(Long applyId, Long orderId, Long orderOperateId, Integer reviewStatus,
        String remark, String orderOperateStatus, Long createBy, Long createDept) {
        this.applyId = applyId;
        this.orderId = orderId;
        this.orderOperateId = orderOperateId;
        this.reviewStatus = reviewStatus;
        this.remark = remark;
        this.orderOperateStatus = orderOperateStatus;
        setCreateBy(createBy);
        setCreateDept(createDept);
        setUpdateBy(createBy);
        setUpdateTime(new Date());
    }
}
