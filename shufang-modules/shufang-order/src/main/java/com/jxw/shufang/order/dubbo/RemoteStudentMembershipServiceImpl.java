package com.jxw.shufang.order.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.order.api.RemoteStudentMembershipService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentMembershipBo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.order.domain.bo.StudentMembershipCardBo;
import com.jxw.shufang.order.domain.vo.StudentMembershipCardVo;
import com.jxw.shufang.order.service.IStudentMembershipCardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 远程学生会员卡服务实现
 *
 * @author: cyj
 * @date: 2025/5/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteStudentMembershipServiceImpl implements RemoteStudentMembershipService {

    private final IStudentMembershipCardService studentMembershipCardService;

    @Override
    public List<RemoteStudentMembershipCardVo> listMembershipCard(RemoteStudentMembershipBo remoteStudentMembershipBo) {
        List<StudentMembershipCardVo> studentMembershipCardVos = studentMembershipCardService
            .queryList(MapstructUtils.convert(remoteStudentMembershipBo, StudentMembershipCardBo.class));
        return MapstructUtils.convert(studentMembershipCardVos, RemoteStudentMembershipCardVo.class);
    }

    /**
     * 查询指定时间段内的在籍会员
     * 根据会员卡有效期判断是否在籍
     */
    @Override
    public List<RemoteStudentMembershipCardVo> getEnrolledStudentsInPeriod(Date startDate, Date endDate) {
        try {
            // 使用新的 RemoteStudentMembershipBo 创建查询条件
            RemoteStudentMembershipBo remoteBo = RemoteStudentMembershipBo.createEnrolledQueryForPeriod(startDate, endDate);

            // 转换为本地 BO
            StudentMembershipCardBo bo = MapstructUtils.convert(remoteBo, StudentMembershipCardBo.class);
            bo.setCardStatus(1); // 启用状态

            List<StudentMembershipCardVo> membershipCards = studentMembershipCardService.queryList(bo);

            if (CollUtil.isEmpty(membershipCards)) {
                return List.of();
            }

            // 过滤在籍会员
            Date now = new Date();
            List<StudentMembershipCardVo> enrolledCards = membershipCards.stream()
                .filter(card -> {
                    // 产品开始时间 <= 当前时间 && 产品结束时间 >= 当前时间
                    boolean isValid = (card.getProductBeginDate() == null || !card.getProductBeginDate().after(now)) &&
                                     (card.getProductEndDate() == null || !card.getProductEndDate().before(now));

                    if (!isValid) {
                        return false;
                    }

                    // 如果指定了时间段，检查时间段条件
                    if (startDate != null && card.getProductEndDate() != null && card.getProductEndDate().before(startDate)) {
                        return false;
                    }
                    if (endDate != null && card.getProductBeginDate() != null && card.getProductBeginDate().after(endDate)) {
                        return false;
                    }

                    return true;
                })
                .collect(Collectors.toList());

            // 转换为远程 VO 并设置计算字段
            List<RemoteStudentMembershipCardVo> result = MapstructUtils.convert(enrolledCards, RemoteStudentMembershipCardVo.class);

            // 设置计算字段
            result.forEach(vo -> {
                vo.setIsEnrolled(vo.isValid());
                vo.setRemainingDays(vo.calculateRemainingDays());
            });

            return result;

        } catch (Exception e) {
            log.error("查询指定时间段内的在籍会员失败", e);
            return List.of();
        }
    }

    /**
     * 查询所有在籍会员
     */
    @Override
    public List<RemoteStudentMembershipCardVo> getAllEnrolledStudents() {
        return getEnrolledStudentsInPeriod(null, null);
    }

    /**
     * 检查学生是否在籍
     */
    @Override
    public Boolean isStudentEnrolled(Long studentId) {
        if (studentId == null) {
            return false;
        }

        try {
            // 使用新的 RemoteStudentMembershipBo 创建查询条件
            RemoteStudentMembershipBo remoteBo = RemoteStudentMembershipBo.createEnrolledQueryForStudent(studentId);

            // 转换为本地 BO
            StudentMembershipCardBo bo = MapstructUtils.convert(remoteBo, StudentMembershipCardBo.class);
            bo.setStudentId(studentId);
            bo.setCardStatus(1); // 启用状态

            List<StudentMembershipCardVo> membershipCards = studentMembershipCardService.queryList(bo);

            if (CollUtil.isEmpty(membershipCards)) {
                return false;
            }

            // 检查是否有有效的会员卡
            Date now = new Date();
            return membershipCards.stream()
                .anyMatch(card ->
                    (card.getProductBeginDate() == null || !card.getProductBeginDate().after(now)) &&
                    (card.getProductEndDate() == null || !card.getProductEndDate().before(now))
                );

        } catch (Exception e) {
            log.error("检查学生{}是否在籍失败", studentId, e);
            return false;
        }
    }

    /**
     * 检查学生在指定时间段内是否在籍
     */
    @Override
    public Boolean isStudentEnrolledInPeriod(Long studentId, Date startDate, Date endDate) {
        if (studentId == null) {
            return false;
        }

        try {
            StudentMembershipCardBo bo =  StudentMembershipCardBo.builder()
                .studentId(studentId).cardStatus(1).build();

            List<StudentMembershipCardVo> membershipCards = studentMembershipCardService.queryList(bo);

            if (CollUtil.isEmpty(membershipCards)) {
                return false;
            }

            // 检查是否有在指定时间段内有效的会员卡
            Date now = new Date();
            return membershipCards.stream()
                .anyMatch(card -> {
                    // 基本有效性检查
                    boolean isValid = (card.getProductBeginDate() == null || !card.getProductBeginDate().after(now)) &&
                                     (card.getProductEndDate() == null || !card.getProductEndDate().before(now));

                    if (!isValid) {
                        return false;
                    }

                    // 时间段检查
                    if (startDate != null && card.getProductEndDate() != null && card.getProductEndDate().before(startDate)) {
                        return false;
                    }
                    if (endDate != null && card.getProductBeginDate() != null && card.getProductBeginDate().after(endDate)) {
                        return false;
                    }

                    return true;
                });

        } catch (Exception e) {
            log.error("检查学生{}在时间段内是否在籍失败", studentId, e);
            return false;
        }
    }

    /**
     * 获取学生的会员卡过期时间
     */
    @Override
    public Date getStudentExpireTime(Long studentId) {
        if (studentId == null) {
            return null;
        }

        try {
            StudentMembershipCardBo bo =  StudentMembershipCardBo.builder()
                .studentId(studentId).cardStatus(1).build();

            List<StudentMembershipCardVo> membershipCards = studentMembershipCardService.queryList(bo);

            if (CollUtil.isEmpty(membershipCards)) {
                return null;
            }

            // 返回最晚的过期时间
            return membershipCards.stream()
                .map(StudentMembershipCardVo::getProductEndDate)
                .filter(Objects::nonNull)
                .max(Date::compareTo)
                .orElse(null);

        } catch (Exception e) {
            log.error("获取学生{}的会员卡过期时间失败", studentId, e);
            return null;
        }
    }

    @Override
    public List<RemoteStudentMembershipCardVo> queryStudentMembershipCard(List<Long> studentIds) {
        if (CollectionUtils.isEmpty(studentIds)) {
            return Collections.emptyList();
        }
        List<StudentMembershipCardVo> membershipCardVos = studentMembershipCardService.queryStudentMembershipCard(studentIds);
        return MapstructUtils.convert(membershipCardVos, RemoteStudentMembershipCardVo.class);
    }
}
