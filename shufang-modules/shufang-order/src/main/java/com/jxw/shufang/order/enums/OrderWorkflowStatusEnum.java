package com.jxw.shufang.order.enums;

import com.jxw.shufang.common.core.enums.OrderOperateReviewStatus;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: cyj
 * @date: 2025/3/18
 * @Description 订单状态结合订单审核状态，涵盖操作、审核等各个订单生命周期的情况
 */
@Getter
@AllArgsConstructor
public enum OrderWorkflowStatusEnum {
    WAIT_PAY("1", "待支付"),
    PAYED("2", "已支付"),
    CANCEL("3", "已取消"),
    REFUND_WAIT("4", "待审核"),
    REFUNDING("401", "已通过"),
    REFUNDED("5", "已退款"),
    REFUNDED_REJECT("402", "已拒绝"),
    PENDING_PAY("6","待补缴");
    private final String code;
    private final String info;

    /**
     * 获取订单生命周期角度的订单状态
     *
     * @param orderOperateStatus 订单状态 from OrderStatusEnum
     * @param reviewStatus 审核状态 from OrderOperateReviewStatus
     * @return
     */
    public static String getWorkflowStatus(String orderOperateStatus, Integer reviewStatus) {
        if (null != reviewStatus && OrderStatusEnum.REFUNDING.getCode().equals(orderOperateStatus)) {
            if(OrderOperateReviewStatus.WAIT_REVIEW.getCode().equals(reviewStatus)){
                return REFUND_WAIT.getCode();
            }else if(OrderOperateReviewStatus.REJECT.getCode().equals(reviewStatus)){
                return REFUNDED_REJECT.getCode();
            }else {
                return REFUNDING.getCode();
            }
        }
        return orderOperateStatus;
    }
}
