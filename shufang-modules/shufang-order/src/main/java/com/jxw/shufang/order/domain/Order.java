package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 order
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("`order`")
public class Order extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 订单id
     */
    @TableId(value = "order_id")
    private Long orderId;

    /**
     * 会员id
     */
    private Long studentId;

    /**
     * 销售人员ID（原销售顾问ID）
     */
    private Long salesPerson;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 经办日期
     */
    private Date handlingDate;

    /**
     * 经办人（姓名）
     */
    private String handlingPerson;

    /**
     * 订单操作id
     */
    private Long orderOperateId;
    /**
     * 如果订单类型为旧卡升级  值为旧卡订单ID
     */
    private Long orderRelationId;

    /**
     * 0为 购买新卡 1为 旧卡升级
     */
    private Integer orderType;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 开课时间
     */
    private Date courseStartTime;

    /**
     * 提前购卡标识： 0-否, 1-是
     */
    private Boolean advancePayFlag;

    /**
     * 订单应付金额
     */
    private BigDecimal payAbleAmount;

    /**
     * 分期支付标识: 0-整笔支付, 1-分期支付
     */
    private Boolean installmentFlag;

    /**
     * 线上支付标识:0-线下支付，1-线上支付
     */
    private Boolean onlinePayFlag;

    /**
     * 是否代付: 0-否, 1-是
     */
    private Boolean peerPayFlag;

    /**
     * 线下支付凭证文件
     */
    private String offlinePayVoucherFileId;

    /**
     * 分期补缴截止时间
     */
    private Date installmentPayDeadlineTime;

    /**
     * 付款备注
     */
    private String paymentDesc;
    /**
     * 购卡标识 0-无 1-新生购卡 2-老生续费
     */
    private Integer purchasedCardFlag;

}
