package com.jxw.shufang.order.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.order.api.RemoteOrderService;
import com.jxw.shufang.order.api.domain.bo.RemoteOrderBo;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentProductTemplateAuthBo;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentBaseTemplateAuthVo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentProductTemplateVo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.bo.StudentProductTemplateAuthBo;
import com.jxw.shufang.order.domain.vo.OrderVo;
import com.jxw.shufang.order.domain.vo.StudentBaseTemplateAuthVo;
import com.jxw.shufang.order.service.IOrderProductInfoService;
import com.jxw.shufang.order.service.IOrderService;
import com.jxw.shufang.order.service.IStudentProductTemplateAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
public class RemoteOrderServiceImpl implements RemoteOrderService {

    private final IOrderService orderService;
    private final IOrderProductInfoService orderProductInfoService;

    private final IStudentProductTemplateAuthService studentProductTemplateAuthService;

    @Override
    public List<Long> getEffectiveOrderStudentIds(List<Long> studentIdList, List<Long> productIdList) {
        return orderService.getEffectiveOrderStudentIds(studentIdList,productIdList);
    }

    @Override
    public List<RemoteOrderVo> selectOrderListAndInfo(RemoteOrderBo remoteOrderBo,boolean ignoreDataPermission) {
        OrderBo convert = MapstructUtils.convert(remoteOrderBo, OrderBo.class);
        List<OrderVo> orderVos = null;
        if (ignoreDataPermission){
            orderVos = DataPermissionHelper.ignore(() -> orderService.selectOrderListAndInfo(convert));
        }else {
            orderVos = orderService.selectOrderListAndInfo(convert);
        }
        return MapstructUtils.convert(orderVos, RemoteOrderVo.class);
    }

    @Override
    public List<RemoteOrderVo> selectStudentLastOrder(RemoteOrderBo remoteOrderBo) {
        OrderBo convert = MapstructUtils.convert(remoteOrderBo, OrderBo.class);
        List<OrderVo> orderVos = orderService.selectStudentLastOrder(convert);
        return MapstructUtils.convert(orderVos, RemoteOrderVo.class);
    }

    @Override
    public RemoteOrderVo getRemoteOrder(Long studentId, Long excludeStudentTypeId) {
        OrderVo orderVo =
            orderService.queryOneOrder(studentId, excludeStudentTypeId);
        return MapstructUtils.convert(orderVo, RemoteOrderVo.class);
    }

    /**
     * 查询会员id列表的最近一个订单
     *
     * @param remoteOrderBo bo.studentIds必须不为空
     * @return
     */
    @Override
    public List<RemoteOrderVo> getBatchStudentLastOrder(RemoteOrderBo remoteOrderBo) {
        if (CollUtil.isEmpty(remoteOrderBo.getStudentIdList())) {
            return new ArrayList<>(0);
        }
        List<OrderVo> orderVos =
            orderService.selectBatchStudentLastOrder(MapstructUtils.convert(remoteOrderBo, OrderBo.class));
        putOrderActualPrice(orderVos);
        return MapstructUtils.convert(orderVos, RemoteOrderVo.class);
    }

    @Override
    public List<RemoteOrderVo> listStudentOrder(RemoteOrderBo remoteOrderBo) {
        List<OrderVo> orderVos =
            orderService.selectBatchStudentOrder(MapstructUtils.convert(remoteOrderBo, OrderBo.class));
        putOrderActualPrice(orderVos);
        return MapstructUtils.convert(orderVos, RemoteOrderVo.class);
    }

    private void putOrderActualPrice(List<OrderVo> orderVos) {
        orderVos.forEach(orderVo -> {
            orderVo.setActualPayPrice(orderProductInfoService.getOrderAblePayAmountByOrder(orderVo.getOrderId(), orderVo.getOrderRelationId()));
        });
    }
    /**
     * 查询代理商授权课程ID集合
     *
     * @param bo
     */
    @Override
    public List<Long> queryAuthCourseIds(RemoteStudentProductTemplateAuthBo bo) {
        List<? extends RemoteStudentBaseTemplateAuthVo> remoteStudentBaseTemplateAuthVos = this.queryAuthList(bo);

        //根据授权模板列表，获取已授权的课程ID集合
        List<Long> authCourseId = Optional.ofNullable(remoteStudentBaseTemplateAuthVos).orElse(ListUtil.toList()).stream()
            .filter(remoteStudentBaseTemplateAuthVo -> ObjectUtil.isNotNull(remoteStudentBaseTemplateAuthVo.getAuthStatus()) && remoteStudentBaseTemplateAuthVo.getAuthStatus() == 1)
            .flatMap(remoteStudentBaseTemplateAuthVo -> {
                if (remoteStudentBaseTemplateAuthVo instanceof RemoteStudentProductTemplateVo studentProductTemplateAuthVo) {
                    return Optional.ofNullable(studentProductTemplateAuthVo.getResIds()).orElse(new ArrayList<>()).stream();
                }
                return Stream.empty();
            }).collect(Collectors.toList());
        return authCourseId;
    }

    /**
     * 查询代理商授权模板列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<RemoteStudentProductTemplateVo> queryAuthList(RemoteStudentProductTemplateAuthBo bo) {
        StudentProductTemplateAuthBo studentProductTemplateAuthBo = MapstructUtils.convert(bo, StudentProductTemplateAuthBo.class);
        List<? extends StudentBaseTemplateAuthVo> studentBaseTemplateAuthVos = studentProductTemplateAuthService.queryAuthList(studentProductTemplateAuthBo);
        if (ObjectUtils.isEmpty(studentBaseTemplateAuthVos)) return ListUtil.toList();

        List<RemoteStudentProductTemplateVo> result = studentBaseTemplateAuthVos.stream()
            .map(studentBaseTemplateAuthVo -> BeanUtil.copyProperties(studentBaseTemplateAuthVo, RemoteStudentProductTemplateVo.class)
        ).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        return result;
    }

}
