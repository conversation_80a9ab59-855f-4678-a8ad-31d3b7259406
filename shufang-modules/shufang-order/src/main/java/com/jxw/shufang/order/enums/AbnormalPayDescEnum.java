package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/7/4 11:00
 * @Version 1
 * @Description
 */
public enum AbnormalPayDescEnum {
    REPEAT_PAY("重复支付", AbnormalPayEnum.ABNORMAL_PAY);

    private String desc;
    private AbnormalPayEnum abnormalPayEnum;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public AbnormalPayEnum getAbnormalPayEnum() {
        return abnormalPayEnum;
    }

    public void setAbnormalPayEnum(AbnormalPayEnum abnormalPayEnum) {
        this.abnormalPayEnum = abnormalPayEnum;
    }

    AbnormalPayDescEnum(String desc, AbnormalPayEnum abnormalPayEnum) {
        this.desc = desc;
        this.abnormalPayEnum = abnormalPayEnum;
    }
}
