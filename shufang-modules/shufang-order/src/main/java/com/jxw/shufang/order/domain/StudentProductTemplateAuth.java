package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;

/**
 * 产品（会员卡模板）授权对象 student_product_template_auth
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("pms_product_template_auth")
public class StudentProductTemplateAuth extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模板权限id
     */
    @TableId(value = "template_dept_auth_id")
    private Long templateDeptAuthId;

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 代理商id
     */
    private Long deptId;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    private Integer templateType;


    public StudentProductTemplateAuth(Long templateId, Long deptId, Integer templateType) {
        this.templateId = templateId;
        this.deptId = deptId;
        this.templateType = templateType;
    }
}
