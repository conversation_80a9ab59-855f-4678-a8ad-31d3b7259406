package com.jxw.shufang.order.interfaces;

import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.OrderInfo;

/**
 * AccountTransferProcessor
 * 针对账号出账入账审查
 */
public interface AccountTransferProcessor {

    Boolean support(AccountTradeTypeEnum typeEnum);

    /**
     * 对该账号进行转入操作
     */
    void to(AccountInfo accountInfo, OrderInfo orderInfo);


    /**
     * 对该账号进行转出操作
     */
    void from(AccountInfo accountInfo, OrderInfo orderInfo);
}
