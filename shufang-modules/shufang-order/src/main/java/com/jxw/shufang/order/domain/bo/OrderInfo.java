package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 账户流水 （扣款退费充值记录）业务对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
public class OrderInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private OrderTypeEnum orderTypeEnum;

    private Integer amount;
}
