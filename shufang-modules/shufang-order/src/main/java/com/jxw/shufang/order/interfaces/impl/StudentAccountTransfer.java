package com.jxw.shufang.order.interfaces.impl;


import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.interfaces.AccountTransferProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class StudentAccountTransfer implements AccountTransferProcessor {
    @Override
    public Boolean support(AccountTradeTypeEnum typeEnum) {
        return AccountTradeTypeEnum.STUDENT.equals(typeEnum);
    }

    @Override
    public void to(AccountInfo accountInfo, OrderInfo orderInfo) {
        //目前Student是授权账号到期后续迁移
    }

    @Override
    public void from(AccountInfo accountInfo, OrderInfo orderInfo) {
        //目前Student是授权账号到期后续迁移

    }
}
