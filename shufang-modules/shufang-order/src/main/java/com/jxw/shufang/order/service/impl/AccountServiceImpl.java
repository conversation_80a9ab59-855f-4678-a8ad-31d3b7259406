package com.jxw.shufang.order.service.impl;

import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.order.domain.bo.AccountInfo;
import com.jxw.shufang.order.domain.bo.AccountTradeLogBo;
import com.jxw.shufang.order.domain.bo.OrderInfo;
import com.jxw.shufang.order.domain.bo.TransferBo;
import com.jxw.shufang.order.interfaces.AccountTransferFactory;
import com.jxw.shufang.order.interfaces.AccountTransferProcessor;
import com.jxw.shufang.order.service.IAccountService;
import com.jxw.shufang.order.service.IAccountTradeLogService;
import com.jxw.shufang.system.api.RemoteDeptService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


@RequiredArgsConstructor
@Service
public class AccountServiceImpl implements IAccountService {

    @DubboReference
    private RemoteBranchService remoteBranchService;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    private final IAccountTradeLogService accountTradeLogService;
    private final AccountTransferFactory accountTransferFactory;

    @Override
    public void doTransfer(TransferBo transferBo) {
        if (ObjectUtils.isEmpty(transferBo)) {
            return;
        }
        validEntityBeforeSave(transferBo);

        //下列操作需要做成原子操作
        //支出用户
        this.transferFrom(transferBo);
        //收入用户
        this.transferTo(transferBo);

        buildLog(buildLogFrom(transferBo));
        buildLog(buildLogTo(transferBo));
    }

    private void validEntityBeforeSave(TransferBo transferBo) {
        //做一些数据校验,如唯一约束

        if (ObjectUtils.isEmpty(transferBo.getToAccountInfo())) {
            throw new ServiceException("转入用户不能为空");
        }
        if (ObjectUtils.isEmpty(transferBo.getFromAccountInfo())) {
            throw new ServiceException("转出用户不能为空");
        }
        if (ObjectUtils.isEmpty(transferBo.getOrderInfo())) {
            throw new ServiceException("订单不能为空");
        }
    }

    public void buildLog(AccountTradeLogBo bo) {
        accountTradeLogService.insertByBo(bo);
    }

    private void transferTo(TransferBo bo) {
        AccountInfo toAccountInfo = bo.getToAccountInfo();
        AccountTransferProcessor processor = accountTransferFactory.getProcessor(toAccountInfo.getAccountType());
        processor.to(toAccountInfo, bo.getOrderInfo());
    }

    private void transferFrom(TransferBo bo) {
        AccountTransferProcessor processor = accountTransferFactory.getProcessor(bo.getFromAccountInfo().getAccountType());
        processor.from(bo.getFromAccountInfo(), bo.getOrderInfo());
    }


    public AccountTradeLogBo buildLogFrom(TransferBo transferBo) {
        AccountTradeLogBo bo = new AccountTradeLogBo();
        AccountInfo fromAccountInfo = transferBo.getFromAccountInfo();
        AccountInfo toAccountInfo = transferBo.getToAccountInfo();

        bo.setAccountId(fromAccountInfo.getAccountId());
        bo.setAccountType(fromAccountInfo.getAccountType().getCode());
        bo.setReplyAccount(toAccountInfo.getAccountId());
        bo.setReplyAccountType(toAccountInfo.getAccountType().getCode());
        //支出
        bo.setTradeType(0);
        buildOrderInfo(transferBo.getOrderInfo(), bo);
        return bo;
    }

    private static void buildOrderInfo(OrderInfo orderInfo, AccountTradeLogBo bo) {
        bo.setOrderId(orderInfo.getOrderId());
        bo.setOrderType(orderInfo.getOrderTypeEnum().getCode());
        bo.setAmount(orderInfo.getAmount());
    }

    public AccountTradeLogBo buildLogTo(TransferBo transferBo) {
        AccountTradeLogBo bo = new AccountTradeLogBo();
        AccountInfo fromAccountInfo = transferBo.getFromAccountInfo();
        AccountInfo toAccountInfo = transferBo.getToAccountInfo();
        bo.setAccountId(toAccountInfo.getAccountId());
        bo.setAccountType(toAccountInfo.getAccountType().getCode());
        bo.setReplyAccount(fromAccountInfo.getAccountId());
        bo.setReplyAccountType(fromAccountInfo.getAccountType().getCode());
        //支出
        bo.setTradeType(1);
        buildOrderInfo(transferBo.getOrderInfo(), bo);
        return bo;
    }

}
