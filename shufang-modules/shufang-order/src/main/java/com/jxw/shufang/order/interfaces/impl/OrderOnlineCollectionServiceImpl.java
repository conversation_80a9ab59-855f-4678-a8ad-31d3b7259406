package com.jxw.shufang.order.interfaces.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.pay.client.PayCommonClient;
import com.jxw.shufang.common.pay.constant.CommonPayConstant;
import com.jxw.shufang.common.pay.domain.dto.request.CallBackConfigDTO;
import com.jxw.shufang.common.pay.domain.dto.request.UnifiedOrderRequestDTO;
import com.jxw.shufang.common.pay.domain.dto.response.ApiResp;
import com.jxw.shufang.common.pay.domain.dto.response.UnifiedOrderResponseDTO;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.bo.OrderCollectionBo;
import com.jxw.shufang.order.domain.dto.OrderCollectionDTO;
import com.jxw.shufang.order.domain.dto.OrderOnlinePayCollectionContext;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.enums.*;
import com.jxw.shufang.order.service.*;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentMerchantConfigDTO;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/6/17 10:54
 * @Version 1
 * @Description 订单线上收款服务实现类
 */
@Service
public class OrderOnlineCollectionServiceImpl implements OrderCollectionService {
    @Resource
    private OrderPayRecordDao orderPayRecordDao;
    @Resource
    private IOrderProductInfoService orderProductInfoService;
    @Resource
    private IOrderService orderService;
    @Resource
    private PayCommonClient payCommonClient;
    @Resource
    private RemoteStudentService remoteStudentService;

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public OrderCollectionDTO collection(OrderCollectionBo orderCollectionBo, Long orderOperateId) {
        // 参数验证
        this.validateParam(orderCollectionBo);

        Long orderId = orderCollectionBo.getOrderId();

        // 获取数据上下文
        OrderOnlinePayCollectionContext collectionContext = this.createOrderCollectionContext(orderId, orderCollectionBo, orderOperateId);
        Optional<OrderPayRecord> existWaitPayRecord = this.existWaitPayRecord(collectionContext.getOrderPayRecordList());
        if (existWaitPayRecord.isPresent()) {
            return OrderCollectionDTO.of(orderId,
                existWaitPayRecord.get().getPayAbleAmount(),
                existWaitPayRecord.get().getOrderPayNo());
        }

        // 验证支付订单金额和处理支付单
        collectionContext.collectionCheck();
        this.handlerPayOrder(orderCollectionBo, collectionContext);

        return OrderCollectionDTO.of(orderId, collectionContext.getPaymentAmount(), collectionContext.getOrderPayNo());
    }

    private void handlerPayOrder(OrderCollectionBo orderCollectionBo, OrderOnlinePayCollectionContext collectionContext) {
        if (collectionContext.firstPayStage()) {
            this.updateMasterOrder(orderCollectionBo);
        }
        OrderPayRecord newRecord = this.createPaymentRecord(orderCollectionBo, collectionContext);
        orderPayRecordDao.insertOrderPayRecord(newRecord);
    }

    @Override
    public OrderPayVO generatePaymentUrl(Order order, String orderPayNo) {
        RemoteStudentMerchantConfigDTO commonPayConfigByStudent = remoteStudentService.getCommonPayConfigByStudent(order.getStudentId());
        if (null == commonPayConfigByStudent) {
            throw new ServiceException("创建二维码失败:无效的商户信息");
        }
        OrderPayRecord orderPayRecord = orderPayRecordDao.queryRecordByOrderPayNo(orderPayNo);

        UnifiedOrderRequestDTO unifiedOrderRequest = new UnifiedOrderRequestDTO();
        unifiedOrderRequest.setOutTradeNo(orderPayRecord.getOrderPayNo());
        unifiedOrderRequest.setAmount(orderPayRecord.getPayAbleAmount().multiply(BigDecimal.valueOf(100)));
        unifiedOrderRequest.setBizSystemType(CommonPayConstant.DEFAULT_SYSTEM_CODE);
        unifiedOrderRequest.setAppId(commonPayConfigByStudent.getAppId());
        setMqConfig(unifiedOrderRequest);
        ApiResp<UnifiedOrderResponseDTO> response = payCommonClient.unifiedOrder(unifiedOrderRequest);
        if (response.isSuccess()) {
            UnifiedOrderResponseDTO data = response.getData();
            return new OrderPayVO(data.getPayUrl(), data.getPayOrderId());
        } else {
            throw new ServiceException("获取支付二维码失败:".concat(response.getMsg()));
        }
    }

    private static void setMqConfig(UnifiedOrderRequestDTO unifiedOrderRequest) {
        CallBackConfigDTO callbackConfig = new CallBackConfigDTO("1", PayTradeNotifyTopicConstant.ORDER_NOTIFY_TOPIC,
            PayTradeNotifyTopicConstant.ORDER_PAY_NOTIFY_TAG, null);
        unifiedOrderRequest.setCallbackConfig(callbackConfig);
    }


    private OrderOnlinePayCollectionContext createOrderCollectionContext(Long orderId,
                                                                         OrderCollectionBo orderCollectionBo,
                                                                         Long orderOperateId) {
        OrderOnlinePayCollectionContext collectionContext = new OrderOnlinePayCollectionContext();
        collectionContext.setOrderId(orderId);
        collectionContext.setActualAmount(orderProductInfoService.calOrderActualAmount(orderId));
        collectionContext.setOrderPayRecordList(this.queryValidPayRecords(orderId));
        collectionContext.setOrderPayNo(UuidUtils.generateUuid());
        collectionContext.setOrderOperateId(orderOperateId);
        collectionContext.setPaymentAmount(orderCollectionBo.getPaymentAmount());
        Boolean depositAmountFlag = null != orderCollectionBo.getDepositAmountFlag() && orderCollectionBo.getDepositAmountFlag();
        collectionContext.setDepositAmountFlag(depositAmountFlag);
        boolean installmentFlag = null != orderCollectionBo.getInstallmentFlag() && orderCollectionBo.getInstallmentFlag();
        collectionContext.setInstallmentFlag(installmentFlag);
        return collectionContext;
    }


    private OrderPayRecord createPaymentRecord(OrderCollectionBo orderCollectionBo,
                                               OrderOnlinePayCollectionContext collectionContext) {
        OrderPayRecord insertOrderPayRecord = new OrderPayRecord();
        insertOrderPayRecord.setOrderPayNo(collectionContext.getOrderPayNo());
        insertOrderPayRecord.setOrderId(orderCollectionBo.getOrderId());
        insertOrderPayRecord.setPayAbleAmount(orderCollectionBo.getPaymentAmount());
        insertOrderPayRecord.setDepositAmountFlag(orderCollectionBo.getDepositAmountFlag() ? 1 : 0);
        insertOrderPayRecord.setPaymentStatus(PayOrderStatusEnum.WAIT_PAY.getCode());
        insertOrderPayRecord.setPaymentDesc(orderCollectionBo.getPaymentRemark());
        insertOrderPayRecord.setAbnormalPayFlag(AbnormalPayEnum.NORMAL_PAY.getCode());
        insertOrderPayRecord.setAbnormalPayDesc(AbnormalPayEnum.NORMAL_PAY.getMessage());
        // 支付阶段
        this.processPaymentStage(orderCollectionBo.getDepositAmountFlag(), collectionContext, insertOrderPayRecord);
        insertOrderPayRecord.setOrderTime(new Date());
        insertOrderPayRecord.setUpdateTime(new Date());
        insertOrderPayRecord.setDelFlag(0);
        return insertOrderPayRecord;
    }

    /**
     * 处理支付阶段
     * @param depositAmountFlag
     * @param collectionContext
     * @param insertOrderPayRecord
     */
    private void processPaymentStage(Boolean depositAmountFlag,
                                     OrderOnlinePayCollectionContext collectionContext,
                                     OrderPayRecord insertOrderPayRecord) {
        boolean firstInstallmentPayStage = collectionContext.firstPayStage() && collectionContext.isInstallmentPay();
        if (firstInstallmentPayStage) {
            if (depositAmountFlag) {
                insertOrderPayRecord.setPaymentStage(PaymentStageEnum.DEPOSIT_PAY.getCode());
            } else {
                insertOrderPayRecord.setPaymentStage(PaymentStageEnum.FIRST_PAY.getCode());
            }
        } else if (collectionContext.isSecondInstallmentPayStage()) {
            insertOrderPayRecord.setPaymentStage(PaymentStageEnum.SECOND_PAY.getCode());
        } else if (collectionContext.isLastInstallmentPayStage()) {
            insertOrderPayRecord.setPaymentStage(PaymentStageEnum.LAST_PAY.getCode());
        } else {
            insertOrderPayRecord.setPaymentStage(PaymentStageEnum.COMPLETE_PAY.getCode());
        }
    }

    private List<OrderPayRecord> queryValidPayRecords(Long orderId) {
        List<PayOrderStatusEnum> validStatuses = Arrays.asList(
            PayOrderStatusEnum.WAIT_PAY,
            PayOrderStatusEnum.PAYED
        );
        return orderPayRecordDao.queryValidRecordByOrderId(orderId, validStatuses);
    }

    private Optional<OrderPayRecord> existWaitPayRecord(List<OrderPayRecord> records) {
        return records.stream()
            .filter(r -> r.getPaymentStatus().equals(PayOrderStatusEnum.WAIT_PAY.getCode()))
            .findFirst();
    }

    private void updateMasterOrder(OrderCollectionBo orderCollectionBo) {
        Order updatedOrder = new Order();
        updatedOrder.setOrderId(orderCollectionBo.getOrderId());
        updatedOrder.setInstallmentFlag(orderCollectionBo.getInstallmentFlag());
        updatedOrder.setOnlinePayFlag(orderCollectionBo.getOnlinePayFlag());
        updatedOrder.setPeerPayFlag(orderCollectionBo.getPeerPayFlag());
        orderService.updateOrderCollectionInfo(updatedOrder, orderCollectionBo.getOrderId());
    }

    private void validateParam(OrderCollectionBo orderCollectionBo) {
        if (StrUtil.isEmpty(orderCollectionBo.getPaymentRemark())) {
            throw new ServiceException("请填写收款备注");
        }

        boolean existPaymentAmount = null != orderCollectionBo.getPaymentAmount()
            && orderCollectionBo.getPaymentAmount().compareTo(BigDecimal.ZERO) >= 0;
        if (!existPaymentAmount) {
            throw new ServiceException("请填写正确的收款金额");
        }
    }

    @Override
    public PayModeEnum payMode() {
        return PayModeEnum.ONLINE;
    }
}
