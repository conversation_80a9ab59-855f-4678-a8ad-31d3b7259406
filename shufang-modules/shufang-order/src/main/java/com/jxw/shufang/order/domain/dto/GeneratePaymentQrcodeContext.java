package com.jxw.shufang.order.domain.dto;

import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderPayRecord;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/7/8 11:52
 * @Version 1
 * @Description 构建支付二维码的参数上下文
 */
@Data
@Builder
public class GeneratePaymentQrcodeContext {
    private OrderPayRecord orderPayRecord;
    private Order order;
    private OrderOperate orderOperate;
}
