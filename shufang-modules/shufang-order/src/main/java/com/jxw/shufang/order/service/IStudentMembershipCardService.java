package com.jxw.shufang.order.service;


import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.order.domain.bo.StudentMembershipCardBo;
import com.jxw.shufang.order.domain.vo.StudentMembershipCardVo;

import java.util.Collection;
import java.util.List;

/**
 * 学生会员卡Service接口
 *
 * <AUTHOR>
 * @date 2025-03-06
 */
public interface IStudentMembershipCardService {

    /**
     * 查询学生会员卡
     */
    StudentMembershipCardVo queryById(Long studentMembershipCardId);

    /**
     * 查询学生会员卡列表
     */
    TableDataInfo<StudentMembershipCardVo> queryPageList(StudentMembershipCardBo bo, PageQuery pageQuery);

    /**
     * 查询学生会员卡列表
     */
    List<StudentMembershipCardVo> queryList(StudentMembershipCardBo bo);

    /**
     * 新增学生会员卡
     */
    Boolean insertByBo(StudentMembershipCardBo bo);

    Boolean insertBatchBo(List<StudentMembershipCardBo> membershipCardBos);

    /**
     * 修改学生会员卡
     */
    Boolean updateByBo(StudentMembershipCardBo bo);

    /**
     * 校验并批量删除学生会员卡信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void updateBatchById(List<StudentMembershipCardVo> studentMembershipCardVos);

    Boolean deleteByStudentIds(Collection<Long> ids);

    List<StudentMembershipCardVo> queryListByOrders(List<Long> orderIds);

    /**
     * 查询学生会员卡列表
     * @param studentIds
     * @return
     */
    List<StudentMembershipCardVo> queryStudentMembershipCard(List<Long> studentIds);
}
