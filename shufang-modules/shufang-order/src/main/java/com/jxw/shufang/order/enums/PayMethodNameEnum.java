package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:11
 * @Version 1
 * @Description
 */
public enum PayMethodNameEnum {
    WECHAT("wechat","微信"),
    ALIPAY("alipay","支付宝"),
    BANK_TRANSFER("bank_transfer","银行转账"),
    OTHER("other","其他");

    private String code;

    private String name;

    PayMethodNameEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
