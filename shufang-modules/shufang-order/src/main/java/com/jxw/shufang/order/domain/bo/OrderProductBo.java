package com.jxw.shufang.order.domain.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 远程服务产品业务对象
 *
 * @date 2024-02-21
 */
@Data
public class OrderProductBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品ID列表
     */
    private List<Long> productIdList;

    /**
     * 会员类型id
     */
    private Long studentTypeId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品有效天数
     */
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    private BigDecimal productPrice;

    /**
     * 产品状态（0上架 1下架）
     */
    private String productStatus;

    /*
     * 非会员类型id
     */
    private Long neStudentTypeId;
    /**
     * 学生ID
     */
    private Long studentId;
    /**
     * 是否开启旧卡升级
     */
    private Boolean filterNewProduct;


}
