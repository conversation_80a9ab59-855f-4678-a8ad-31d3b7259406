package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.OrderOperateApplyRecord;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;

/**
 * @author: cyj
 * @date: 2025/3/17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OrderOperateApplyRecord.class)
public class OrderOperateApplyRecordVo extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 退款申请ID
     */
    private Long applyId;
    /**
     * 订单操作ID
     */
    private Long orderOperateId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单操作记录状态
     */
    private String orderOperateStatus;
    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;
    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;
    /**
     * 凭证ossId
     */
    private Long voucher;
    /**
     * 凭证名称
     */
    private String voucherName;
    /**
     * 凭证url
     */
    private String voucherUrl;
    /**
     * 审核意见
     */
    private String remark;
    /**
     * 人员
     */
    private String operatorName;

    /**
     * 操作后订单的工作流状态
     */
    private String orderWorkflowStatus;
}
