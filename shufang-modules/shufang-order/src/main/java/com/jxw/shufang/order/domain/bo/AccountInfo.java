package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.enums.AccountTradeTypeEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 账户流水 （扣款退费充值记录）业务对象 transaction_history
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
public class AccountInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主体交易账号
     */
    private Long accountId;

    /**
     * 主体交易账号类型(0 代理商，1 门店，2 学生)
     */
    private AccountTradeTypeEnum accountType;

}
