package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单操作记录
（时间逆序取最后一条和订单中的对应）对象 order_operate
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_operate")
public class OrderOperate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单操作id
     */
    @TableId(value = "order_operate_id")
    private Long orderOperateId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消 4退款中 5已退款）
     */
    private String orderOperateStatus;

    private String paymentType;

    /**
     * 审批状态 0-待审核、1-通过、2-驳回
     */
    private Integer reviewStatus;
    /**
     * 当前审核节点 对应 RefundAuditNode
     */
    private Integer reviewNode;
    /**
     * 支付方式（0-线下 1-线上）
     */
    private Integer payMode;
    /**
     * 收款金额（已支付才存在）
     */
    private BigDecimal paymentAmount;

    /**
     * 退款金额（已退款才存在）
     */
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    private String orderOperateRemark;
    /**
     * 支付凭证url
     */
    private String paymentVoucherImgUrl;

    /**
     * 退款账号名称
     */
    private String refundAccountName;

    /**
     * 退款账号
     */
    private String refundAccount;

    /**
     * 退款开户行信息
     */
    private String refundBankName;
}
