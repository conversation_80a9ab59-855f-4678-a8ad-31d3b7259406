package com.jxw.shufang.order.interfaces.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.OrderProductInfo;
import com.jxw.shufang.order.domain.bo.OrderCollectionBo;
import com.jxw.shufang.order.domain.dto.OrderCollectionDTO;
import com.jxw.shufang.order.domain.dto.OrderPayConText;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.enums.*;
import com.jxw.shufang.order.service.*;
import com.jxw.shufang.student.api.RemoteProductService;
import com.jxw.shufang.student.api.domain.bo.RemoteProductBo;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/6/17 14:33
 * @Version 1
 * @Description 订单线下收款服务实现类
 */
@Service
public class OrderOfflineCollectionServiceImpl implements OrderCollectionService {
    @Resource
    private OrderPayRecordDao orderPayRecordDao;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOrderProductInfoService orderProductInfoService;
    @DubboReference
    private  RemoteProductService remoteProductService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCollectionDTO collection(OrderCollectionBo orderCollectionBo,Long orderOperateId) {
        // 数据校验
        this.validateParam(orderCollectionBo);

        OrderPayConText orderPayConText = this.createOrderOfflinePayConText(orderCollectionBo.getOrderId());
        BigDecimal actualAmount = orderPayConText.getActualAmount();

        Long orderId = orderPayConText.getOrderId();

        this.validateAmount(orderCollectionBo.getPaymentAmount(), actualAmount);

        // 创建支付单
        OrderPayRecord orderPayRecord = this.createOrderPayRecord(orderCollectionBo, actualAmount);

        // 更新主订单
        this.updateMasterOrder(orderCollectionBo);

        return OrderCollectionDTO.of(orderId, actualAmount, orderPayRecord.getOrderPayNo());
    }

    @Override
    public OrderPayVO generatePaymentUrl(Order order, String orderPayNo) {
        throw new ServiceException("线下收款暂不支持生成二维码");
    }

    private void updateMasterOrder(OrderCollectionBo orderCollectionBo) {
        Order updatedOrder = new Order();
        updatedOrder.setOrderId(orderCollectionBo.getOrderId());
        updatedOrder.setInstallmentFlag(orderCollectionBo.getInstallmentFlag());
        updatedOrder.setOnlinePayFlag(orderCollectionBo.getOnlinePayFlag());
        updatedOrder.setPeerPayFlag(orderCollectionBo.getPeerPayFlag());
        updatedOrder.setOfflinePayVoucherFileId(orderCollectionBo.getPaymentVoucherImgId());
        orderService.updateOrderCollectionInfo(updatedOrder, orderCollectionBo.getOrderId());
    }

    private OrderPayRecord createOrderPayRecord(OrderCollectionBo orderCollectionBo, BigDecimal actualAmount) {
        OrderPayRecord orderPayRecord = this.buildOrderPayRecord(orderCollectionBo, actualAmount);
        orderPayRecordDao.insertOrderPayRecord(orderPayRecord);
        return orderPayRecord;
    }

    private OrderPayRecord buildOrderPayRecord(OrderCollectionBo orderCollectionBo, BigDecimal actualAmount) {
        OrderPayRecord orderPayRecord = new OrderPayRecord();
        Date now = new Date();
        orderPayRecord.setOrderPayNo(UuidUtils.generateUuid());
        orderPayRecord.setOrderId(orderCollectionBo.getOrderId());
        orderPayRecord.setPayAbleAmount(actualAmount);
        orderPayRecord.setAmount(actualAmount);
        orderPayRecord.setDepositAmountFlag(checkDepositAmountFlag(orderCollectionBo));
        orderPayRecord.setPaymentStatus(PayOrderStatusEnum.PAYED.getCode());
        orderPayRecord.setPaymentTime(now);
        orderPayRecord.setOrderTime(now);
        orderPayRecord.setPaymentVoucherImgId(orderCollectionBo.getPaymentVoucherImgId());
        orderPayRecord.setUpdateTime(now);
        orderPayRecord.setDelFlag(0);
        orderPayRecord.setAbnormalPayDesc(AbnormalPayEnum.NORMAL_PAY.getMessage());
        orderPayRecord.setAbnormalPayFlag(AbnormalPayEnum.NORMAL_PAY.getCode());
        orderPayRecord.setPaymentStage(PaymentStageEnum.COMPLETE_PAY.getCode());
        return orderPayRecord;
    }

    private static int checkDepositAmountFlag(OrderCollectionBo orderCollectionBo) {
        boolean existDepositAmountFlag = null == orderCollectionBo.getDepositAmountFlag();
        return existDepositAmountFlag ? 0 : orderCollectionBo.getDepositAmountFlag() ? 1 : 0;
    }


    private void validateAmount(BigDecimal paymentAmount, BigDecimal actualAmount) {
        if (actualAmount.compareTo(paymentAmount) != 0) {
            throw new RuntimeException("收款金额不正确");
        }
    }

    private OrderPayConText createOrderOfflinePayConText(Long orderId) {
        Order order = orderService.queryOrderById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        List<OrderProductInfo> orderProductInfoList = this.getProductIds(orderId);
        List<Long> productIds = orderProductInfoList.stream().map(OrderProductInfo::getProductId).collect(Collectors.toList());

        RemoteProductBo remoteProductBo = new RemoteProductBo();
        remoteProductBo.setProductIdList(productIds);
        List<RemoteProductVo> remoteProductVos = remoteProductService.queryProductList(remoteProductBo,true);
        if(CollectionUtil.isEmpty(remoteProductVos) || productIds.size() !=remoteProductVos.size()){
            throw new ServiceException("商品ID不正确");
        }

        BigDecimal productPrice = ProcessProductPrice.of(remoteProductVos.get(0), orderProductInfoList.get(0), order.getCourseStartTime())
            .prepareOrderPrice()
            .getProductPrice();

        OrderPayConText orderPayConText = new OrderPayConText();
        orderPayConText.setOrderId(orderId);
        orderPayConText.setOrder(order);
        orderPayConText.setActualAmount(productPrice);
        return orderPayConText;
    }

    private List<OrderProductInfo> getProductIds(Long orderId) {
        List<OrderProductInfo> orderProductInfos = orderProductInfoService.queryProductByOrder(orderId);
        return orderProductInfos;
    }

    private void validateParam(OrderCollectionBo bo) {
        if (null !=bo.getInstallmentFlag() && bo.getInstallmentFlag()) {
            throw new ServiceException("线下收款不允许分期订单");
        }
        if (null != bo.getDepositAmountFlag() && bo.getDepositAmountFlag()) {
            throw new ServiceException("线下收款不允许定金付款");
        }
        if (StringUtils.isEmpty(bo.getPaymentVoucherImgId())) {
            throw new ServiceException("线下收款请上传收款凭证");
        }
        if (null == bo.getPaymentAmount() || bo.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("请填写正确的收款金额");
        }
    }


    @Override
    public PayModeEnum payMode() {
        return PayModeEnum.OFFLINE;
    }
}
