package com.jxw.shufang.order.domain.convert;


import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderVo;
import com.jxw.shufang.order.domain.vo.OrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderVoToRemoteOrderVo extends BaseMapper<OrderVo, RemoteOrderVo> {

}
