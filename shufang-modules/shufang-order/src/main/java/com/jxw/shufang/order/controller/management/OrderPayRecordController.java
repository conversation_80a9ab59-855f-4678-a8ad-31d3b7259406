package com.jxw.shufang.order.controller.management;

import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.order.domain.bo.OrderPayBo;
import com.jxw.shufang.order.domain.vo.OrderCompletePayRecordVo;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.domain.vo.QueryStudentPayOrderVo;
import com.jxw.shufang.order.service.OrderPayRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/28 16:42
 * @Version 1
 * @Description 订单支付记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/management/orderpay")
public class OrderPayRecordController extends BaseController {
    @Resource
    private OrderPayRecordService orderPayRecordService;

    /**
     * 支付
     */
    @PostMapping("/pay")
    public R<OrderPayVO> pay(@Validated(EditGroup.class) @RequestBody OrderPayBo bo) {
        if (bo.getOrderId() == null) {
            throw new ServiceException("订单ID不能为空");
        }
        if (StrUtil.isEmpty(bo.getOrderPayNo())) {
            throw new ServiceException("支付单号不能为空");
        }
        return R.ok(orderPayRecordService.pay(bo));
    }

    /**
     * 查询支付单和学生信息
     */
    @GetMapping("/queryStudentPayOrder")
    public R<QueryStudentPayOrderVo> queryStudentPayOrder(@Validated(EditGroup.class) Long orderId, String orderPayNo) {
        if (orderId == null) {
            throw new ServiceException("订单ID不能为空");
        }
        if (StringUtils.isEmpty(orderPayNo)) {
            throw new ServiceException("支付单号不能为空");
        }
        QueryStudentPayOrderVo studentPayOrderVo = orderPayRecordService.queryStudentPayOrder(orderId, orderPayNo);
        return R.ok(studentPayOrderVo);
    }


    /**
     * 订单完整支付记录
     *
     * @param orderId
     * @return
     */
    @GetMapping("/completeRecord")
    public R<List<OrderCompletePayRecordVo>> list(@Validated(EditGroup.class) Long orderId) {
        if (orderId == null) {
            throw new RuntimeException("订单ID不能为空");
        }
        List<OrderCompletePayRecordVo> orderCompletePayRecordVos = orderPayRecordService.queryCompleteRecord(orderId);
        return R.ok(orderCompletePayRecordVos);
    }
}
