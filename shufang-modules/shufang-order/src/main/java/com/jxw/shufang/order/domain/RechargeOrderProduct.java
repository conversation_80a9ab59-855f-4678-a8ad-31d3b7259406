package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 代理商订单产品详情对象 agent_order_product_info
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oms_recharge_order_product")
public class RechargeOrderProduct extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情id
     */
    @TableId(value = "order_info_id")
    private Long orderInfoId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 代理商id
     */
    private Long deptId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品天数
     */
    private Long productDays;

    /**
     * 产品数量
     */
    private Long productNums;

    /**
     * 产品价格
     */
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    private BigDecimal preferentialPrice;

    /**
     * 总额
     */
    private BigDecimal amount;


}
