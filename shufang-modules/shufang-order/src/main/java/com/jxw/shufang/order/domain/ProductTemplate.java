package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 产品模板管理对象 pms_product_template
 *
 * @date 2024-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pms_product_template")
public class ProductTemplate extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品模板信息-主键
     */
    @TableId(value = "product_template_id")
    private Long productTemplateId;

    /**
     * 产品模板名称
     */
    private String productTemplateName;

    /**
     * 产品模板描述
     */
    private String productTemplateDesc;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    private Integer productTemplateType;

    /**
     * 状态 0-上架 1-下架
     */
    private Integer status;
}
