package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 订单产品详情对象 order_product_info
 *
 * @date 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("order_product_info")
public class OrderProductInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情id
     */
    @TableId(value = "order_info_id")
    private Long orderInfoId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 会员类型id
     */
    private Long studentTypeId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品有效天数
     */
    private Long productValidDays;

    /**
     * 产品有效期（时间段，用 至 隔开）
     */
    private String productValidTimeLimit;

    /**
     * 产品价格
     */
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    private BigDecimal preferentialPrice;


    /**
     * 会员优惠额度
     */
    private BigDecimal studentPreferentialPrice;

    /**
     * 产品原始价格
     */
    private BigDecimal originProductPrice;

    /**
     * 期中报名直减
     */
    private BigDecimal signupDiscountAmount;
}
