package com.jxw.shufang.order.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.order.api.domain.vo.RemoteOrderProductInfoVo;
import com.jxw.shufang.order.domain.vo.OrderProductInfoVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderProductInfoVoToRemoteOrderProductInfoVo extends BaseMapper<OrderProductInfoVo, RemoteOrderProductInfoVo> {

}
