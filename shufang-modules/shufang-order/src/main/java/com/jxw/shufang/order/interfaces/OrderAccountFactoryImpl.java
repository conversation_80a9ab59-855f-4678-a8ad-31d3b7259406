package com.jxw.shufang.order.interfaces;

import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 默认工厂
 */
@Component
@AllArgsConstructor
public class OrderAccountFactoryImpl implements OrderAccountFactory {

    private final List<OrderAccountProcessor> processorList;

    @Override
    public OrderAccountProcessor getProcessor(OrderTypeEnum orderTypeEnum) {

        for (OrderAccountProcessor orderAccountProcessor : processorList) {
            if (Boolean.TRUE.equals(orderAccountProcessor.support(orderTypeEnum))) {
                return orderAccountProcessor;
            }
        }
        throw new ServiceException("不支持的订单类型");
    }
}
