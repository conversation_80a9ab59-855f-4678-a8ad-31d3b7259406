package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.order.domain.Order;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Order.class)
public class StudentProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总会员卡数量，包含过期的
     */
    private Integer totalProductNum;

    /**
     * 有效的会员卡数量，不包含过期的
     */
    private Integer validProductNum;


    /**
     * 会员卡列表
     */
    private List<OrderProductInfoVo> productInfoList;


}
