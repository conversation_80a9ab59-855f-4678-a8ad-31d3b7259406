package com.jxw.shufang.order.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.order.domain.RechargeOrderProduct;
import com.jxw.shufang.order.domain.bo.RechargeOrderProductBo;
import com.jxw.shufang.order.domain.vo.RechargeOrderProductVo;
import com.jxw.shufang.order.mapper.RechargeOrderProductMapper;
import com.jxw.shufang.order.service.IRechargeOrderProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 代理商订单产品详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@RequiredArgsConstructor
@Service
public class RechargeOrderProductServiceImpl implements IRechargeOrderProductService, BaseService {

    private final RechargeOrderProductMapper baseMapper;

    /**
     * 查询代理商订单产品详情
     */
    @Override
    public RechargeOrderProductVo queryById(Long orderInfoId) {
        return baseMapper.selectVoById(orderInfoId);
    }

    /**
     * 查询代理商订单产品详情列表
     */
    @Override
    public TableDataInfo<RechargeOrderProductVo> queryPageList(RechargeOrderProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<RechargeOrderProduct> lqw = buildQueryWrapper(bo);
        Page<RechargeOrderProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询代理商订单产品详情列表
     */
    @Override
    public List<RechargeOrderProductVo> queryList(RechargeOrderProductBo bo) {
        LambdaQueryWrapper<RechargeOrderProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<RechargeOrderProduct> buildQueryWrapper(RechargeOrderProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<RechargeOrderProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrderId() != null, RechargeOrderProduct::getOrderId, bo.getOrderId());
        lqw.eq(bo.getDeptId() != null, RechargeOrderProduct::getDeptId, bo.getDeptId());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), RechargeOrderProduct::getProductName, bo.getProductName());
        lqw.eq(bo.getProductDays() != null, RechargeOrderProduct::getProductDays, bo.getProductDays());
        lqw.eq(bo.getProductNums() != null, RechargeOrderProduct::getProductNums, bo.getProductNums());
        lqw.eq(bo.getProductPrice() != null, RechargeOrderProduct::getProductPrice, bo.getProductPrice());
        lqw.eq(bo.getPreferentialPrice() != null, RechargeOrderProduct::getPreferentialPrice, bo.getPreferentialPrice());
        lqw.eq(bo.getAmount() != null, RechargeOrderProduct::getAmount, bo.getAmount());
        return lqw;
    }

    /**
     * 新增代理商订单产品详情
     */
    @Override
    public Boolean insertByBo(RechargeOrderProductBo bo) {
        RechargeOrderProduct add = MapstructUtils.convert(bo, RechargeOrderProduct.class);
        validEntityBeforeSave(add);
        //总额计算
        BigDecimal amount = add.getProductPrice().multiply(BigDecimal.valueOf(add.getProductNums()))
            .subtract(add.getPreferentialPrice()).setScale(4, RoundingMode.HALF_UP);
        add.setAmount(amount);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderInfoId(add.getOrderInfoId());
        }
        return flag;
    }

    /**
     * 修改代理商订单产品详情
     */
    @Override
    public Boolean updateByBo(RechargeOrderProductBo bo) {
        RechargeOrderProduct update = MapstructUtils.convert(bo, RechargeOrderProduct.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(RechargeOrderProduct entity) {
        //做一些数据校验,如唯一约束
        if (entity == null) {
            throw new ServiceException("产品存在异常");
        }
    }

    /**
     * 批量删除代理商订单产品详情
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

    @Override
    public RechargeOrderProductVo queryByOrderId(Long orderId) {
        LambdaQueryWrapper<RechargeOrderProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(RechargeOrderProduct::getOrderId, orderId);
        return baseMapper.selectVoOne(lqw);
    }
}
