package com.jxw.shufang.order.domain.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/18 11:39
 * @Version 1
 * @Description
 */
@Data
public class OrderCollectionVo {
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单支付单号
     */
    private String orderPayNo;
    public static OrderCollectionVo of(Long orderId, String orderPayNo){
        OrderCollectionVo vo = new OrderCollectionVo();
        vo.setOrderId(orderId);
        vo.setOrderPayNo(orderPayNo);
        return vo;
    }
}
