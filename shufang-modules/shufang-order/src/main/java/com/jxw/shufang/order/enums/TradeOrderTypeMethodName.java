package com.jxw.shufang.order.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/25 15:13
 * @Version 1
 * @Description
 */
public enum TradeOrderTypeMethodName {
    WECHAT("1", "微信", PayMethodNameEnum.WECHAT),
    ALIPAY("2", "支付宝", PayMethodNameEnum.ALIPAY),
    OTHER("", "其他", PayMethodNameEnum.OTHER);

    private String code;

    private String name;

    private PayMethodNameEnum payMethodNameEnum;

    TradeOrderTypeMethodName(String code, String name, PayMethodNameEnum payMethodNameEnum) {
        this.code = code;
        this.name = name;
        this.payMethodNameEnum = payMethodNameEnum;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PayMethodNameEnum getPayMethodNameEnum() {
        return payMethodNameEnum;
    }

    public void setPayMethodNameEnum(PayMethodNameEnum payMethodNameEnum) {
        this.payMethodNameEnum = payMethodNameEnum;
    }

    public static TradeOrderTypeMethodName getByCode(String code) {
        for (TradeOrderTypeMethodName tradeOrderTypeMethodName : TradeOrderTypeMethodName.values()) {
            if (tradeOrderTypeMethodName.getCode().equals(code)) {
                return tradeOrderTypeMethodName;
            }
        }
        return OTHER;
    }
}
