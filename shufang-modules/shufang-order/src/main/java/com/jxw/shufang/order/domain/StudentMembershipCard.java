package com.jxw.shufang.order.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 学生会员卡表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("student_membership_card")
public class StudentMembershipCard extends BaseEntity {
    /**
     * 学生产品表
     */
    @TableId(value = "student_membership_card_id")
    private Long studentMembershipCardId;

    /**
     * 学生id
     */
    private Long studentId;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 会员卡类型
     */
    private Long studentTypeId;

    /**
     * 会员卡状态(0失效--退费 1启用--付款)
     */
    private Integer cardStatus;

    /**
     * 产品开始时间
     */
    private Date productBeginDate;

    /**
     * 产品结束时间
     */
    private Date productEndDate;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
