package com.jxw.shufang.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.order.domain.RechargeOrder;
import com.jxw.shufang.order.domain.vo.RechargeOrderVo;
import org.apache.ibatis.annotations.Param;

/**
 * 代理商充值订单记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
public interface RechargeOrderMapper extends BaseMapperPlus<RechargeOrder, RechargeOrderVo> {

    @DataPermission({
        @DataColumn(key = "deptName", value = "t.create_dept")
    })
    @DataColumn(key = "deptName", value = "t.create_dept")
    Page<RechargeOrderVo> selectPageList(@Param("page") Page<RechargeOrder> build, @Param(Constants.WRAPPER) QueryWrapper<RechargeOrder> lqw);

}
