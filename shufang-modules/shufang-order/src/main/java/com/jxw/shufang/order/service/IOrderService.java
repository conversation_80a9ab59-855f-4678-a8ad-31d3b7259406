package com.jxw.shufang.order.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.bo.AddOrderBo;
import com.jxw.shufang.order.domain.bo.OrderBo;
import com.jxw.shufang.order.domain.bo.OrderOperateBo;
import com.jxw.shufang.order.domain.bo.OrderProductBo;
import com.jxw.shufang.order.domain.dto.PreCalculatePriceDTO;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.student.api.domain.vo.RemoteProductVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 订单Service接口
 *
 * @date 2024-02-27
 */
public interface IOrderService {

    /**
     * 查询订单
     */
    OrderVo queryById(Long orderId);

    OrderVo queryById(Long orderId, Boolean putStudent);

    /**
     * 查询订单列表
     */
    TableDataInfo<OrderVo> queryPageList(OrderBo bo, PageQuery pageQuery);

    /**
     * 查询订单列表
     */
    List<OrderVo> queryList(OrderBo bo);

    /**
     * 查询订单列表和信息，所以会有关联表的多条信息
     *
     * @param bo bo
     * @date 2024/03/08 07:48:54
     */
    List<OrderVo> selectOrderListAndInfo(OrderBo bo);

    List<OrderVo> selectOrderListAndOperateList(OrderBo bo);

    /**
     * 新增订单
     */
    Boolean insertByBo(AddOrderBo bo);

    /**
     * 修改订单
     */
    Boolean updateByBo(OrderBo bo);

    /**
     * 校验并批量删除订单信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<OrderVo> queryRefundPageList(OrderBo bo, PageQuery pageQuery);

    /**
     * 获取退款导出订单记录列表
     *
     * @param bo
     * @return
     */
    List<RefundOrderVo> listExportRefundOrder(OrderBo bo);

    /**
     * 查询选项列表
     *
     * @param bo bo
     * @date 2024/03/04 03:30:19
     */
    List<OrderVo> queryOptionList(OrderBo bo);

    /**
     * 获取有效订单会员ID
     *
     * @param studentIdList 会员id列表
     * @param productIdList 产品id列表(可为空)
     * @date 2024/03/05 11:45:26
     */
    List<Long> getEffectiveOrderStudentIds(List<Long> studentIdList, List<Long> productIdList);

    /**
     * 选择会员最后订单
     *
     * @param convert 转换
     * @date 2024/03/10 10:50:12
     */
    List<OrderVo> selectStudentLastOrder(OrderBo convert);

    Order queryOrderById(Long orderId);

    void cleanCache();

    OrderVo selectStudentLastOrderByStudentId(OrderBo convert);

    List<RemoteProductVo> queryProductOption(OrderProductBo bo);

    void initStudentCard(List<Long> studentIdList);

    /**
     * 获取订单
     *
     * @param studentId
     * @param excludeStudentTypeId
     * @return
     */
    OrderVo queryOneOrder(Long studentId, Long excludeStudentTypeId);

    TableDataInfo<OrderReferrerVo> pageReferrer(OrderBo bo, PageQuery pageQuery);

    /**
     * 查询多个会员的最近一个订单
     *
     * @param orderBo
     * @return
     */
    List<OrderVo> selectBatchStudentLastOrder(OrderBo orderBo);

    List<OrderVo> selectBatchStudentOrder(OrderBo convert);

    void updateOrderCollectionInfo(Order order, Long orderId);

    /**
     * 计算订单价格
     * @param orderId
     * @param productId
     */
    BigDecimal calculateOrderPrice(Long orderId, Long productId);

    /**
     * 获取订单的退款方式，可退款金额等信息
     *
     * @param bo
     * @return
     */
    OrderRefundInfoVo getRefundInfo(OrderOperateBo bo);

    List<Order> queryOrderListByOrderId(List<Long> orderIdList);

    /**
     * 预计算订单价格
     * @param preCalculatePriceDTO
     */
    PreCalculateOrderPriceVO preCalculateOrderPrice(PreCalculatePriceDTO preCalculatePriceDTO);
}
