package com.jxw.shufang.order.service;

import com.jxw.shufang.order.domain.bo.CancelPayOrderBo;
import com.jxw.shufang.order.domain.bo.OrderPayBo;
import com.jxw.shufang.order.domain.vo.OrderCompletePayRecordVo;
import com.jxw.shufang.order.domain.vo.OrderPayVO;
import com.jxw.shufang.order.domain.vo.QueryStudentPayOrderVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17 19:43
 * @Version 1
 * @Description
 */
public interface OrderPayRecordService {
    void cancelPayOrder(CancelPayOrderBo cancelPayOrder, Long userId);

    List<OrderCompletePayRecordVo> queryCompleteRecord(Long orderId);

    OrderPayVO pay(OrderPayBo bo);

    QueryStudentPayOrderVo queryStudentPayOrder(Long orderId, String orderPayNo);
}
