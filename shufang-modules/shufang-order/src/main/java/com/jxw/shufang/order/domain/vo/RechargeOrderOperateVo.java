package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.RechargeOrderOperate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 订单操作记录 （时间逆序取最后一条和订单中的对应）视图对象 agent_order_operate
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RechargeOrderOperate.class)
public class RechargeOrderOperateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单操作id
     */
    @ExcelProperty(value = "订单操作id")
    private Long orderOperateId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 订单状态（1待支付 2已支付 3已取消
     */
    @ExcelProperty(value = "订单状态")
    private Integer orderOperateStatus;

    /**
     * 支付方式（已支付才存在 对应字典值，比如 微信、支付宝）
     */
    @ExcelProperty(value = "支付方式")
    private String paymentType;

    /**
     * 收款金额（已支付才存在）
     */
    @ExcelProperty(value = "收款金额")
    private BigDecimal paymentAmount;

    /**
     * 退款方式（已退款才存在 对应字典值，比如 微信、支付宝）
     */
    @ExcelProperty(value = "退款方式")
    private String refundType;

    /**
     * 退款金额（已退款才存在）
     */
    @ExcelProperty(value = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String orderOperateRemark;

    /**
     * 创建时间
     */
    private Date createTime;


}
