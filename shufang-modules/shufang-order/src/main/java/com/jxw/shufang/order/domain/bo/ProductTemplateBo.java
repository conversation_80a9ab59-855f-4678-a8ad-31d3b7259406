package com.jxw.shufang.order.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.order.domain.ProductTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 产品模板管理业务对象 pms_product_template
 *
 * @date 2024-06-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProductTemplate.class, reverseConvertGenerate = false)
public class ProductTemplateBo extends BaseEntity {

    /**
     * 产品模板信息-主键
     */
    @NotNull(message = "产品模板ID不能为空", groups = {EditGroup.class})
    private Long productTemplateId;

    /**
     * 产品模板名称
     */
    @NotBlank(message = "产品模板名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productTemplateName;

    /**
     * 产品模板描述
     */
    private String productTemplateDesc;

    /**
     * 模板类型 0-会员卡模板 1-课程模板
     */
    @NotNull(message = "模板类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer productTemplateType;

    /**
     * 状态 0-上架 1-下架
     */
    private Integer status;

    /**
     * 资源ID
     */
    private List<Long> resIdList;
}
