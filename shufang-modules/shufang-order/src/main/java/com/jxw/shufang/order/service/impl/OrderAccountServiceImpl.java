package com.jxw.shufang.order.service.impl;


import com.jxw.shufang.common.core.enums.OrderTypeEnum;
import com.jxw.shufang.order.interfaces.OrderAccountFactory;
import com.jxw.shufang.order.interfaces.OrderAccountProcessor;
import com.jxw.shufang.order.service.IOrderAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class OrderAccountServiceImpl implements IOrderAccountService {

    private final OrderAccountFactory orderAccountFactory;

    @Override
    public Boolean getProcessor(Long orderId, OrderTypeEnum orderTypeEnum) {
        OrderAccountProcessor processor = orderAccountFactory.getProcessor(orderTypeEnum);
        processor.process(orderId);
        return Boolean.TRUE;
    }
}
