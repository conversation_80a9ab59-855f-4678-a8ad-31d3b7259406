package com.jxw.shufang.order.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.order.domain.RechargeOrderProduct;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 代理商订单产品详情视图对象 agent_order_product_info
 *
 * <AUTHOR>
 * @date 2024-09-01
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = RechargeOrderProduct.class)
public class RechargeOrderProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单详情id
     */
    @ExcelProperty(value = "订单详情id")
    private Long orderInfoId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 代理商id
     */
    @ExcelProperty(value = "代理商id")
    private Long deptId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品天数
     */
    @ExcelProperty(value = "产品天数")
    private Long productDays;

    /**
     * 产品数量
     */
    @ExcelProperty(value = "产品数量")
    private Long productNums;

    /**
     * 产品价格
     */
    @ExcelProperty(value = "产品价格")
    private BigDecimal productPrice;

    /**
     * 优惠价格（门店直减）
     */
    @ExcelProperty(value = "优惠价格")
    private BigDecimal preferentialPrice;

    /**
     * 总额
     */
    @ExcelProperty(value = "总额")
    private BigDecimal amount;


}
