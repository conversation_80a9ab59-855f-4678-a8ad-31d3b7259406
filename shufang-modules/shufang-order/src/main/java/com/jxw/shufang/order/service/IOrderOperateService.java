package com.jxw.shufang.order.service;

import cn.hutool.core.lang.Pair;
import com.jxw.shufang.common.core.enums.OrderStatusEnum;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.pay.domain.dto.notify.PayNotifyDTO;
import com.jxw.shufang.common.pay.domain.dto.notify.RefundNotifyDTO;
import com.jxw.shufang.order.domain.Order;
import com.jxw.shufang.order.domain.OrderOperate;
import com.jxw.shufang.order.domain.OrderOperateApplyRecord;
import com.jxw.shufang.order.domain.OrderPayRecord;
import com.jxw.shufang.order.domain.bo.*;
import com.jxw.shufang.order.domain.vo.*;
import com.jxw.shufang.order.enums.PayModeEnum;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;

/**
 * 订单操作记录
（时间逆序取最后一条和订单中的对应）Service接口
 * @date 2024-02-27
 */
public interface IOrderOperateService {

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    OrderOperateVo queryById(Long orderOperateId);

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    TableDataInfo<OrderOperateVo> queryPageList(OrderOperateBo bo, PageQuery pageQuery);

    /**
     * 查询订单操作记录（时间逆序取最后一条和订单中的对应）列表
     */
    List<OrderOperateVo> queryList(OrderOperateBo bo);

    /**
     * 新增订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    Boolean insertByBo(OrderOperateBo bo);

    /**
     * 修改订单操作记录（时间逆序取最后一条和订单中的对应）
     */
    Boolean updateByBo(OrderOperateBo bo);

    /**
     * 校验并批量删除订单操作记录（时间逆序取最后一条和订单中的对应）信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 初始化订单操作信息
     *
     * @param orderId 订单id
     * @date 2024/03/03 03:50:20
     */
    Long initOrderOperate(Long orderId);

    /**
     * 收款
     *
     * @param bo bo
     * @date 2024/03/03 03:50:16
     */
    OrderCollectionVo collection(OrderCollectionBo bo);

    /**
     * 支付订单
     *
     * @param bo
     * @param compareAmount
     * @param operator
     * @param operateDept
     * @param lockOrderId
     */
    void payOrderByBo(OrderOperateBo bo, boolean compareAmount, Long operator, Long operateDept, Long lockOrderId);

    StudentMembershipCardBo getStudentMembershipCardBo(OrderVo orderVo);

    /**
     * 退款
     *
     * @param bo bo
     * @date 2024/03/03 09:32:23
     */
    void refund(OrderOperateBo bo);

    Pair<Boolean, String> checkSupportRefund(Integer payMode, OrderVo orderVo, List<OrderPayRecord> orderPayRecords);

    /**
     * 退款
     *
     * @param bo
     * @param lockRefundOrderId
     */
    void refundByOrderOperateBo(OrderOperateBo bo, Long operator, Long operateDept, Long lockRefundOrderId);

    void cleanCache();

    void cancel(OrderOperateBo bo);

    /**
     * 退款申请
     *
     * @param bo
     */
    void refundApply(OrderOperateBo bo);

    /**
     * 退款审核
     *
     * @param bo
     */
    void refundReview(OrderOperateApplyRecord bo);

    Pair<Order, OrderOperate> getOrderInfo(Long orderId, OrderStatusEnum... expectStatusList);

    /**
     * 检查当前用户是否又权限操作退款
     *
     * @param bo
     * @return
     */
    RefundingOrderPermissionVo getOperatePermissionVo(OrderOperateApplyRecord bo);

    /**
     * 处理通知回调内容
     *
     * @param notifyDTO
     */
    void processPayNotify(PayNotifyDTO notifyDTO);

    /**
     * 处理退款回调
     *
     * @param notifyDTO
     */
    void processRefundNotify(RefundNotifyDTO notifyDTO);

    /**
     * 获取订单线上支付信息
     *
     * @param bo
     * @return
     */
    PaymentQrCodeVo generatePaymentQrCode(CreatePayQrCodeBo bo);

    /**
     * 检查订单是否支付成功
     *
     * @param bo
     * @return
     */
    CheckOrderPayStatusVo checkOrderPay(Long orderId, String orderPayNo);

    Set<PayModeEnum> queryOnlinePayTypeByOrderId(Long orderId);

    boolean checkOperatePermission(OrderVo orderVo, Boolean onlyStaff, Supplier<OrderVo> orderSupplier);

    List<OrderOperate> queryOperateOrderListByOrderIds(List<Long> id);

    Boolean queryOrderAbleInstallment(Long orderId);
}
