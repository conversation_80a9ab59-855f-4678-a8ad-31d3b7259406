<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.order.mapper.OrderProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.order.domain.OrderProductInfo">
        <id column="order_info_id" property="orderInfoId" />
        <result column="order_id" property="orderId" />
        <result column="product_id" property="productId" />
        <result column="student_type_id" property="studentTypeId" />
        <result column="product_name" property="productName" />
        <result column="product_valid_days" property="productValidDays" />
        <result column="product_valid_time_limit" property="productValidTimeLimit" />
        <result column="product_price" property="productPrice" />
        <result column="preferential_price" property="preferentialPrice" />
        <result column="create_dept" property="createDept" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="student_preferential_price" property="studentPreferentialPrice" />
        <result column="origin_product_price" property="originProductPrice" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        order_info_id, order_id, product_id, student_type_id, product_name, product_valid_days, product_valid_time_limit, product_price, preferential_price, create_dept, create_by, create_time, update_by, update_time, student_preferential_price, origin_product_price
    </sql>

</mapper>
