<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.order.mapper.OrderMapper">

    <resultMap id="orderSingleResult" type="com.jxw.shufang.order.domain.vo.OrderVo">
        <id property="orderId" column="order_id"/>
        <result property="orderOperateId" column="order_operate_id"/>
        <result property="productNameGroup" column="product_name_group"/>
        <result property="studentTypeIdGroup" column="student_type_id_group"/>
        <result property="productPriceSum" column="product_price_sum"/>
        <result property="preferentialPriceSum" column="preferential_price_sum"/>
        <result property="actualPayPrice" column="actual_pay_price"/>
        <result property="productIdGroup" column="product_id_group"/>
        <result property="onlinePayFlag" column="online_pay_flag"/>
        <result property="installmentPayDeadlineTime" column="installment_pay_deadline_time"/>
        <result property="payRecordAmount" column="payRecordAmount"></result>
        <association property="orderOperate" column="order_operate_id" resultMap="OrderOperateResult"/>
    </resultMap>

    <resultMap id="orderMoreResult" type="com.jxw.shufang.order.domain.vo.OrderVo">
        <id property="orderId" column="order_id"/>
        <result property="orderOperateId" column="order_operate_id"/>
        <result property="productNameGroup" column="product_name_group"/>
        <result property="studentTypeIdGroup" column="student_type_id_group"/>
        <result property="productPriceSum" column="product_price_sum"/>
        <result property="preferentialPriceSum" column="preferential_price_sum"/>
        <result property="actualPayPrice" column="actual_pay_price"/>
        <result property="productIdGroup" column="product_id_group"/>
        <result property="orderNum" column="orderNum"/>
        <result property="courseStartTime" column="course_start_time"/>
        <association property="orderOperate" column="order_operate_id" resultMap="OrderOperateResult"/>
        <collection property="orderProductInfoList" column="order_id" resultMap="OrderInfoResult"/>
    </resultMap>

    <resultMap id="orderMoreOperateResult" type="com.jxw.shufang.order.domain.vo.OrderVo">
        <id  property="orderId" column="order_id"/>
        <result property="orderOperateId" column="order_operate_id"/>
        <result property="productNameGroup" column="product_name_group"/>
        <result property="studentTypeIdGroup" column="student_type_id_group"/>
        <result property="productPriceSum" column="product_price_sum"/>
        <result property="preferentialPriceSum" column="preferential_price_sum"/>
        <result property="actualPayPrice" column="actual_pay_price"/>
        <result property="productIdGroup" column="product_id_group"/>
        <collection property="orderOperateList" column="order_id" resultMap="OrderOperateResult"/>
        <collection property="orderProductInfoList" column="order_id" resultMap="OrderInfoResult"/>
    </resultMap>

    <resultMap id="OrderOperateResult" type="com.jxw.shufang.order.domain.vo.OrderOperateVo">
        <id  property="orderOperateId" column="oo_order_operate_id"/>
        <result property="createTime" column="op_create_time"/>
        <result property="updateTime" column="op_update_time"/>
        <result property="orderId" column="order_id"/>
        <result property="reviewNode" column="review_node"/>
        <result property="createBy" column="op_create_by"/>
        <result property="paymentType" column="payment_type"/>
        <result property="orderOperateStatus" column="order_operate_status"/>
    </resultMap>

    <resultMap id="OrderInfoResult" type="com.jxw.shufang.order.domain.vo.OrderProductInfoVo">
        <id  property="orderInfoId" column="order_info_id"/>
    </resultMap>

    <select id="selectPageList" resultMap="orderSingleResult">
        select t.order_id,
               t.student_id,
               t.sales_person,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.order_operate_id,
               t.order_type,
               t.order_relation_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               t.installment_flag,
               t.installment_pay_deadline_time,
               t.course_start_time,
               t.online_pay_flag,
               t.purchased_card_flag,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.refund_type,
               oo.refund_amount,
               oo.review_status,
               oo.order_operate_remark,
               oo.pay_mode,
               oo.review_node,
               oo.create_time as op_create_time,
               oo.update_time as op_update_time,
               oo.create_by   as op_create_by,
               opi.*
        from `order` t
                 LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
                 LEFT JOIN (SELECT order_id  as product_order_id,
                                   group_concat(product_name)                 as product_name_group,
                                   group_concat(student_type_id)              as student_type_id_group,
                                   group_concat(product_id)                   as product_id_group,
                                   sum(product_price)                         as product_price_sum,
                                   ifnull(sum(origin_product_price),0)        as originPriceSum,
                                   IFNULL(sum(preferential_price), 0)         as preferential_price_sum,
                                   IFNULL(sum(student_preferential_price), 0) as student_preferential_price_sum
                            FROM order_product_info
                            GROUP BY order_id) opi ON t.order_id = opi.product_order_id
            ${ew.customSqlSegment}
    </select>

    <select id="queryOptionList" resultMap="orderSingleResult">
        select t.order_id,
               t.student_id,
               t.order_no,
               oo.order_operate_status,
               oo.payment_type
        from `order` t
        LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
        ${ew.customSqlSegment}
    </select>


    <select id="getEffectiveOrderStudentIds" resultType="Long">
        select distinct t.student_id
        from `order` t
        left join order_operate oo on oo.order_operate_id = t.order_operate_id
        left join order_product_info opi on opi.order_id = t.order_id
        where oo.order_operate_status = 2
        and IF(opi.product_valid_days is not null,
        date_add(oo.create_time, interval opi.product_valid_days day) >= now()
        ,
        now() between substring_index(opi.product_valid_time_limit, ' 至 ', 1) and
        substring_index(opi.product_valid_time_limit, ' 至 ', -1)
        )

        <if test="studentIdList != null and studentIdList.size > 0">
            AND t.student_id IN
            <foreach item="studentId" index="index" collection="studentIdList" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        <if test="productIdList != null and productIdList.size > 0">
            AND opi.product_id IN
            <foreach item="productId" index="index" collection="productIdList" open="(" separator="," close=")">
                #{productId}
            </foreach>
        </if>
    </select>

    <select id="selectOrderListAndInfo" resultMap="orderMoreResult">
        select t.order_id,
               t.student_id,
               t.sales_person,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.order_operate_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.order_type,
               t.order_relation_id,
               t.del_flag,
               t.course_start_time,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.refund_type,
               oo.refund_amount,
               oo.create_time as op_create_time,
               oo.create_by as op_create_by,
                opi.*
        from `order` t
            LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
            LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
            ${ew.customSqlSegment}
    </select>

    <select id="selectOrderListAndOperateList" resultMap="orderMoreOperateResult">
        select t.order_id,
        t.student_id,
        t.sales_person,
        t.order_no,
        t.handling_date,
        t.handling_person,
        t.order_operate_id,
        t.create_dept,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.order_type,
        t.order_relation_id,
        t.del_flag,
        oo.order_operate_id as oo_order_operate_id,
        oo.order_operate_status,
        oo.payment_type,
        oo.payment_amount,
        oo.refund_type,
        oo.refund_amount,
        oo.create_time as op_create_time,
        oo.create_by as op_create_by,
        opi.*
        from `order` t
        LEFT JOIN order_operate oo ON t.order_id = oo.order_id
        LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectStudentLastOrder" resultMap="orderMoreResult">
        select t.order_id,
               t.student_id,
               t.sales_person,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.order_operate_id,
               t.order_type,
               t.order_relation_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.refund_type,
               oo.refund_amount,
               oo.create_time as op_create_time,
               oo.create_by as op_create_by,
               opi.order_info_id,
               opi.product_id,
               opi.student_type_id,
               opi.product_name,
               opi.product_valid_days,
               opi.product_valid_time_limit,
               opi.product_price,
               opi.preferential_price
        from (
            select CONVERT(substring_index(group_concat(t.order_id order by oo.create_time desc), ',', 1),SIGNED ) as order_id,
                     t.student_id
              from `order` t
                       LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id

                <where>
                    <if test="bo.studentId != null">
                        and t.student_id = #{bo.studentId}
                    </if>
                    <if test="bo.orderStatus != null and bo.orderStatus!=''">
                        and oo.order_operate_status = #{bo.orderStatus}
                    </if>
                    <if test="bo.delFlag != null and bo.delFlag!=''">
                        and t.del_flag = #{bo.delFlag}
                    </if>
                    <if test="bo.studentIdList!=null and bo.studentIdList.size>0">
                        and t.student_id in
                        <foreach item="studentId" index="index" collection="bo.studentIdList" open="(" separator="," close=")">
                            #{studentId}
                        </foreach>
                    </if>
                </where>
              group by t.student_id

        ) o
        LEFT JOIN `order` t ON t.order_id = o.order_id
        LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
        LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
        where 1=1
        <if test="bo.productId!=null">
            and opi.product_id = #{bo.productId}
        </if>
        <if test="ew.sqlSegment != null and ew.sqlSegment != ''">
            and ${ew.sqlSegment}
        </if>
    </select>

    <select id="queryLastOrderByType" resultMap="orderSingleResult">
        SELECT o.order_id,
               o.order_operate_id,
               oo.order_operate_status
        FROM `order` o
                 INNER JOIN `order_operate` oo ON oo.order_id = o.order_id
                 INNER JOIN `order_product_info` opi ON o.order_id = opi.order_id
        WHERE opi.student_type_id != #{excludeStudentTypeId}
          AND student_id = #{studentId}
          AND oo.order_operate_status = 2 LIMIT 1
    </select>

    <select id="pageReferrer" resultType="com.jxw.shufang.order.domain.vo.OrderReferrerVo">
        SELECT t.student_id
        FROM `order` t
                 LEFT JOIN order_operate oo ON oo.order_operate_id = t.order_operate_id
                 LEFT JOIN order_product_info opi ON opi.order_id = t.order_id ${ew.getCustomSqlSegment}
            AND  oo.order_operate_status = 2
            AND (
                    (
                      opi.product_valid_days IS NOT NULL
                      AND DATE_ADD(oo.create_time, INTERVAL opi.product_valid_days DAY) > NOW()
                    )
                    OR (
                      opi.product_valid_days IS NULL
                      AND opi.product_valid_time_limit IS NOT NULL
                      AND NOW() BETWEEN STR_TO_DATE(
                        SUBSTRING_INDEX(opi.product_valid_time_limit, ' 至 ', 1),
                        '%Y-%m-%d %H:%i:%s'
                      )
                      AND STR_TO_DATE(
                        SUBSTRING_INDEX(opi.product_valid_time_limit, ' 至 ', -1),
                        '%Y-%m-%d %H:%i:%s'
                      )
                    )
                )
        GROUP BY
            t.student_id
    </select>
    <select id="selectBatchStudentLastOrder" resultMap="orderMoreResult">
        select t.order_id,
               t.student_id,
               t.sales_person,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.order_operate_id,
               t.order_type,
               t.order_relation_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               t.installment_flag,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.refund_type,
               oo.refund_amount,
               oo.create_time as op_create_time,
               oo.update_time as op_update_time,
               oo.create_by   as op_create_by,
               opi.order_info_id,
               opi.product_id,
               opi.student_type_id,
               opi.product_name,
               opi.product_valid_days,
               opi.product_valid_time_limit,
               opi.product_price,
               opi.preferential_price,
               o.order_num       orderNum
        from (select max(t.order_id)   order_id,
                     count(t.order_id) order_num
              from `order` t
                       LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
                       LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
                  ${ew.customSqlSegment}
              group by t.student_id) o
                 LEFT JOIN `order` t ON t.order_id = o.order_id
                 LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
                 LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
    </select>
    <select id="selectBatchStudentOrder" resultMap="orderMoreResult">
        select t.order_id,
               t.student_id,
               t.sales_person,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.order_operate_id,
               t.order_type,
               t.order_relation_id,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               t.installment_flag,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.refund_type,
               oo.refund_amount,
               oo.create_time as op_create_time,
               oo.update_time as op_update_time,
               oo.create_by   as op_create_by,
               opi.order_info_id,
               opi.product_id,
               opi.student_type_id,
               opi.product_name,
               opi.product_valid_days,
               opi.product_valid_time_limit,
               opi.product_price,
               opi.preferential_price
        from `order` t
                 LEFT JOIN order_operate oo ON t.order_operate_id = oo.order_operate_id
                 LEFT JOIN order_product_info opi ON t.order_id = opi.order_id
            ${ew.customSqlSegment}
    </select>
</mapper>
