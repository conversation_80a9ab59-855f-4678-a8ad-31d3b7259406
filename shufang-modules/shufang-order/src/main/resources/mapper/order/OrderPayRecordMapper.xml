<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.order.mapper.OrderPayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.order.domain.OrderPayRecord">
        <id column="id" property="id" />
        <result column="order_pay_no" property="orderPayNo" />
        <result column="order_id" property="orderId" />
        <result column="amount" property="amount" />
        <result column="pay_able_amount" property="payAbleAmount" />
        <result column="deposit_amount_flag" property="depositAmountFlag" />
        <result column="payment_status" property="paymentStatus" />
        <result column="payment_time" property="paymentTime" />
        <result column="payment_desc" property="paymentDesc" />
        <result column="transaction_no" property="transactionNo" />
        <result column="order_time" property="orderTime" />
        <result column="order_close_reason" property="orderCloseReason" />
        <result column="payment_voucher_Img_id" property="paymentVoucherImgId" />
        <result column="create_dept" property="createDept" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_pay_no, order_id, amount, pay_able_amount, deposit_amount_flag, payment_status, payment_time, payment_desc, transaction_no, installment_time, order_close_reason, payment_voucher_Img_id, create_dept, create_by, create_time, update_time, update_by, del_flag
    </sql>

</mapper>
