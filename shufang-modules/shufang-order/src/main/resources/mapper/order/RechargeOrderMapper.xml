<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.order.mapper.RechargeOrderMapper">

    <resultMap id="orderSingleResult" type="com.jxw.shufang.order.domain.vo.RechargeOrderVo">
        <id property="orderId" column="order_id"/>
        <result property="rechargeId" column="recharge_id"/>
        <result property="rechargeType" column="recharge_type"/>
        <result property="handlingDeptId" column="handling_dept_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="handlingDate" column="handling_date"/>
        <result property="handlingPerson" column="handling_person"/>
        <result property="createTime" column="create_time"/>
        <result property="productDays" column="product_days"/>
        <result property="productNums" column="product_nums"/>
        <result property="productPrice" column="product_price"/>
        <result property="preferentialPrice" column="preferential_price"/>
        <result property="amount" column="amount"/>
        <result property="orderStatus" column="order_operate_status"/>
        <result property="paymentType" column="payment_type"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="opCreateTime" column="op_create_time"/>
    </resultMap>

    <select id="selectPageList" resultMap="orderSingleResult">
        select t.order_id,
               t.recharge_id,
               t.recharge_type,
               t.handling_dept_id,
               t.order_no,
               t.handling_date,
               t.handling_person,
               t.create_dept,
               t.create_by,
               t.create_time,
               t.update_by,
               t.update_time,
               t.del_flag,
               oo.order_operate_status,
               oo.payment_type,
               oo.payment_amount,
               oo.create_time as op_create_time,
               opi.product_days,
               opi.product_nums,
               opi.product_price,
               opi.preferential_price,
               opi.amount
        from `oms_recharge_order` t
         INNER JOIN oms_recharge_order_operate oo ON t.order_operate_id = oo.order_operate_id
         INNER JOIN oms_recharge_order_product opi ON t.order_id = opi.order_id
        ${ew.customSqlSegment}
    </select>

</mapper>
