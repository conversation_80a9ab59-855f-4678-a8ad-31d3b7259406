<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.order.mapper.StudentProductTemplateAuthMapper">

    <resultMap id="courseAuthResult" type="com.jxw.shufang.order.domain.vo.StudentProductResTemplateAuthVo">
        <id property="templateDeptAuthId" column="template_dept_auth_id"/>
        <result property="templateId" column="template_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="productTempName" column="product_template_name"/>
        <result property="productTempDesc" column="product_template_desc"/>
        <result property="productStatus" column="status"/>
        <collection property="resIds" ofType="java.lang.Long">
            <result column="resource_id"/>
        </collection>
    </resultMap>

    <!-- 查询模板授权列表 -->
    <select id="queryAuthList" resultMap="courseAuthResult">
        select t.template_dept_auth_id,
               t.template_id,
               t.dept_id,
               pt.product_template_name,
               pt.product_template_desc,
               pt.status,
               ptr.resource_id
        from pms_product_template_auth t
            inner join pms_product_template pt on t.template_id = pt.product_template_id
            inner join pms_product_template_resource_relation ptr on pt.product_template_id = ptr.product_template_id
        ${ew.customSqlSegment}
    </select>
</mapper>
