<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.report.mapper.WrongQuestionStatisticsMapper">

    <resultMap id="WrongQuestionStudentResult" type="com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo">
        <result property="branchId" column="branch_id"/>
        <result property="branchName" column="branch_name"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="consultantId" column="consultant_id"/>
        <result property="consultantName" column="consultant_name"/>
        <result property="grade" column="grade"/>
        <result property="sex" column="sex"/>
        <result property="lastWeekWrongCount" column="last_week_wrong_count"/>
        <result property="wrongTotal" column="wrong_total"/>
        <result property="printLastWeek" column="print_last_week"/>
        <result property="lastWeekReviseCount" column="last_week_revise_count"/>
        <result property="reviseTotal" column="revise_total"/>
        <result property="lastLoginTime" column="last_login_time"/>
    </resultMap>

    <resultMap id="WrongQuestionCollectionResult" type="com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo">
        <result property="branchId" column="branch_id"/>
        <result property="branchName" column="branch_name"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="consultantId" column="consultant_id"/>
        <result property="consultantName" column="consultant_name"/>
        <result property="collectionType" column="collection_type"/>
        <result property="wrongCount" column="wrong_count"/>
        <result property="rightRate" column="right_rate"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="selectWrongQuestionStudentPage" resultMap="WrongQuestionStudentResult">
        SELECT
            s.branch_id,
            b.branch_name,
            s.student_id,
            s.student_name,
            bs.branch_staff_id as consultant_id,
            su.nick_name as consultant_name,
            s.student_grade as grade,
            s.student_sex as sex,
            COALESCE(last_week_wrong.wrong_count, 0) as last_week_wrong_count,
            COALESCE(total_wrong.wrong_count, 0) as wrong_total,
            CASE WHEN print_collection.student_id IS NOT NULL THEN 1 ELSE 0 END as print_last_week,
            COALESCE(last_week_revise.revise_count, 0) as last_week_revise_count,
            COALESCE(total_revise.revise_count, 0) as revise_total,
            login_user.login_date as last_login_time
        FROM student s
        LEFT JOIN branch b ON s.branch_id = b.branch_id
        LEFT JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
        LEFT JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
        LEFT JOIN sys_user su ON bs.create_by = su.user_id
        LEFT JOIN sys_user login_user ON s.student_account = login_user.user_name
        -- 上周错题数量（根据题目id去重）
        LEFT JOIN (
            SELECT
                wqr.student_id,
                COUNT(DISTINCT wqr.question_id) as wrong_count
            FROM wrong_question_record wqr
            WHERE wqr.create_time >= #{lastWeekStart}
              AND wqr.create_time &lt;= #{lastWeekEnd}
            GROUP BY wqr.student_id
        ) last_week_wrong ON s.student_id = last_week_wrong.student_id
        -- 错题总数（根据题目id去重）
        LEFT JOIN (
            SELECT
                wqr.student_id,
                COUNT(DISTINCT wqr.question_id) as wrong_count
            FROM wrong_question_record wqr
            GROUP BY wqr.student_id
        ) total_wrong ON s.student_id = total_wrong.student_id
        -- 是否打印上周错题（collection_type=2）
        LEFT JOIN (
            SELECT DISTINCT wqc.student_id
            FROM wrong_question_collection wqc
            WHERE wqc.collection_type = 2
              AND wqc.create_time >= #{lastWeekStart}
              AND wqc.create_time &lt;= #{lastWeekEnd}
        ) print_collection ON s.student_id = print_collection.student_id
        -- 上周订正数量
        LEFT JOIN (
            SELECT
                wqr.student_id,
                COUNT(DISTINCT wqr.question_id) as revise_count
            FROM wrong_question_record wqr
            WHERE wqr.revise_status = 1
              AND wqr.revise_time >= #{lastWeekStart}
              AND wqr.revise_time &lt;= #{lastWeekEnd}
            GROUP BY wqr.student_id
        ) last_week_revise ON s.student_id = last_week_revise.student_id
        -- 订正总数
        LEFT JOIN (
            SELECT
                wqr.student_id,
                COUNT(DISTINCT wqr.question_id) as revise_count
            FROM wrong_question_record wqr
            WHERE wqr.revise_status = 1
            GROUP BY wqr.student_id
        ) total_revise ON s.student_id = total_revise.student_id
        WHERE 1=1
        <if test="bo.branchId != null">
            AND s.branch_id = #{bo.branchId}
        </if>
        <if test="bo.studentName != null and bo.studentName != ''">
            AND s.student_name LIKE CONCAT('%', #{bo.studentName}, '%')
        </if>
        <if test="bo.consultantName != null and bo.consultantName != ''">
            AND su.nick_name LIKE CONCAT('%', #{bo.consultantName}, '%')
        </if>
        <if test="bo.reviseStatus != null">
            AND EXISTS (
                SELECT 1 FROM wrong_question_record wqr2
                WHERE wqr2.student_id = s.student_id
                AND wqr2.revise_status = #{bo.reviseStatus}
            )
        </if>
        ORDER BY s.create_time DESC
    </select>

    <select id="selectWrongQuestionCollectionPage" resultMap="WrongQuestionCollectionResult">
        SELECT
            s.branch_id,
            b.branch_name,
            s.student_id,
            s.student_name,
            bs.branch_staff_id as consultant_id,
            su.nick_name as consultant_name,
            wqc.collection_type,
            COALESCE(collection_detail.wrong_count, 0) as wrong_count,
            0.00 as right_rate,
            wqc.remark,
            wqc.create_time
        FROM wrong_question_collection wqc
        LEFT JOIN student s ON wqc.student_id = s.student_id
        LEFT JOIN branch b ON s.branch_id = b.branch_id
        LEFT JOIN student_consultant_record scr ON s.student_consultant_record_id = scr.student_consultant_record_id
        LEFT JOIN branch_staff bs ON scr.student_consultant_id = bs.branch_staff_id
        LEFT JOIN sys_user su ON bs.create_by = su.user_id
        LEFT JOIN (
            SELECT
                wqcd.wrong_question_collection_id,
                COUNT(DISTINCT wqcd.question_id) as wrong_count
            FROM wrong_question_collection_detail wqcd
            GROUP BY wqcd.wrong_question_collection_id
        ) collection_detail ON wqc.wrong_question_collection_id = collection_detail.wrong_question_collection_id
        WHERE 1=1
        <if test="bo.branchId != null">
            AND s.branch_id = #{bo.branchId}
        </if>
        <if test="bo.studentName != null and bo.studentName != ''">
            AND s.student_name LIKE CONCAT('%', #{bo.studentName}, '%')
        </if>
        <if test="bo.consultantName != null and bo.consultantName != ''">
            AND su.nick_name LIKE CONCAT('%', #{bo.consultantName}, '%')
        </if>
        <if test="bo.month != null and bo.month != ''">
            AND DATE_FORMAT(wqc.create_time, '%Y-%m') = #{bo.month}
        </if>
        ORDER BY wqc.create_time DESC
    </select>

</mapper>
