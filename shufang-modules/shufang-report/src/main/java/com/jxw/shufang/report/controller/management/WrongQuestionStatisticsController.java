package com.jxw.shufang.report.controller.management;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.report.domain.bo.WrongQuestionStatisticsBo;
import com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo;
import com.jxw.shufang.report.service.IWrongQuestionStatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/management/wrongQuestion/statistics")
@RequiredArgsConstructor
public class WrongQuestionStatisticsController {

    private final IWrongQuestionStatisticsService wrongQuestionStatisticsService;

    /**
     * 统计会员的错题情况，会员按照创建时间倒序排列
     * 错题汇总需要根据题目id去重，数据取wrong_question_record表
     * 上周指的是上一个自然周，即上周一到上周日
     * 是否打印上周错题取wrong_question_collection表中collection_type=2的数据
     * 订正上周错题取上周时间段内订正状态为1的数据
     * 订正总数为该学生所有错题订正状态为1的数据
     * 最后登录时间取sys_user表中login_date
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/student")
    public TableDataInfo<WrongQuestionStudentVo> wrongQuestionOfStudent(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {
        return wrongQuestionStatisticsService.wrongQuestionOfStudent(bo, pageQuery);
    }


    @GetMapping("/collection")
    public TableDataInfo<WrongQuestionCollectionVo> wrongQuestionCollection(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {
        return wrongQuestionStatisticsService.wrongQuestionCollection(bo, pageQuery);
    }

}
