package com.jxw.shufang.report.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.report.domain.bo.WrongQuestionStatisticsBo;
import com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 错题统计Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Mapper
public interface WrongQuestionStatisticsMapper {

    /**
     * 分页查询学生错题统计
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @param lastWeekStart 上周开始时间
     * @param lastWeekEnd 上周结束时间
     * @return 分页结果
     */
    Page<WrongQuestionStudentVo> selectWrongQuestionStudentPage(@Param("page") Page<WrongQuestionStudentVo> page,
                                                                @Param("bo") WrongQuestionStatisticsBo bo,
                                                                @Param("lastWeekStart") Date lastWeekStart,
                                                                @Param("lastWeekEnd") Date lastWeekEnd);

    /**
     * 分页查询错题合集统计
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @return 分页结果
     */
    Page<WrongQuestionCollectionVo> selectWrongQuestionCollectionPage(@Param("page") Page<WrongQuestionCollectionVo> page,
                                                                      @Param("bo") WrongQuestionStatisticsBo bo);
}
