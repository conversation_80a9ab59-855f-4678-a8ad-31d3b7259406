package com.jxw.shufang.report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.report.domain.bo.WrongQuestionStatisticsBo;
import com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo;
import com.jxw.shufang.report.mapper.WrongQuestionStatisticsMapper;
import com.jxw.shufang.report.service.IWrongQuestionStatisticsService;
import com.jxw.shufang.report.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 错题统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionStatisticsServiceImpl implements IWrongQuestionStatisticsService {

    private final WrongQuestionStatisticsMapper baseMapper;

    @Override
    public TableDataInfo<WrongQuestionStudentVo> wrongQuestionOfStudent(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {

        // 1. 根据查询条件获取符合条件的学生ID列表
        IPage<WrongQuestionStudentVo> page = baseMapper.selectWrongQuestionOfStudent(pageQuery.build(), bo);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return TableDataInfo.build();
        }

        List<Long> studentIds= page.getRecords().stream().map(WrongQuestionStudentVo::getStudentId).toList();
        // 2.查询错题总数
        Map<Long, WrongQuestionStudentVo> wrongTotalMap = baseMapper.selectWrongQuestionCount(studentIds, null, null)
            .stream().collect(Collectors.toMap(WrongQuestionStudentVo::getStudentId, Function.identity()));

        // 3. 计算上周时间范围
        Map<Long, WrongQuestionStudentVo> wrongCountMap = baseMapper.selectWrongQuestionCount(studentIds, DateUtils.getLastWeekStart(), DateUtils.getLastWeekEnd())
            .stream().collect(Collectors.toMap(WrongQuestionStudentVo::getStudentId, Function.identity()));

        page.getRecords().forEach(i -> {
            WrongQuestionStudentVo wrongTotal = wrongTotalMap.get(i.getStudentId());
            if (wrongTotal != null) {
                i.setWrongTotal(wrongTotal.getWrongTotal());
                i.setReviseTotal(wrongTotal.getReviseTotal());
            }
            WrongQuestionStudentVo wrongCount = wrongCountMap.get(i.getStudentId());
            if (wrongCount != null) {
                i.setLastWeekWrongCount(wrongCount.getLastWeekWrongCount());
                i.setLastWeekReviseCount(wrongCount.getLastWeekReviseCount());
            }
        });

        return TableDataInfo.build(page);
    }


    @Override
    public TableDataInfo<WrongQuestionCollectionVo> wrongQuestionCollection(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {
        Page<WrongQuestionCollectionVo> page = baseMapper.selectWrongQuestionCollectionPage(pageQuery.build(), bo);
        return TableDataInfo.build(page);
    }

}
