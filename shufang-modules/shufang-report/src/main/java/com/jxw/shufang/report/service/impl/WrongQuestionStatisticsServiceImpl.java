package com.jxw.shufang.report.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.report.domain.bo.WrongQuestionStatisticsBo;
import com.jxw.shufang.report.domain.vo.WrongQuestionCollectionVo;
import com.jxw.shufang.report.domain.vo.WrongQuestionStudentVo;
import com.jxw.shufang.report.mapper.WrongQuestionStatisticsMapper;
import com.jxw.shufang.report.service.IWrongQuestionStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

/**
 * 错题统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WrongQuestionStatisticsServiceImpl implements IWrongQuestionStatisticsService, BaseService {

    private final WrongQuestionStatisticsMapper baseMapper;

    @Override
    public TableDataInfo<WrongQuestionStudentVo> wrongQuestionOfStudent(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {
        // 计算上周时间范围（上周一到上周日）
        Date lastWeekStart = getLastWeekStart();
        Date lastWeekEnd = getLastWeekEnd();

        log.debug("查询学生错题统计，上周时间范围：{} - {}", lastWeekStart, lastWeekEnd);

        Page<WrongQuestionStudentVo> page = baseMapper.selectWrongQuestionStudentPage(
            pageQuery.build(), bo, lastWeekStart, lastWeekEnd);

        return TableDataInfo.build(page);
    }

    /**
     * 获取上周一的开始时间（00:00:00）
     * 上周指的是上一个自然周，即上周一到上周日
     */
    private Date getLastWeekStart() {
        LocalDate today = LocalDate.now();
        // 获取本周一
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 上周一 = 本周一 - 7天
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        LocalDateTime lastWeekStart = lastMonday.atStartOfDay();
        return Date.from(lastWeekStart.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上周日的结束时间（23:59:59）
     * 上周指的是上一个自然周，即上周一到上周日
     */
    private Date getLastWeekEnd() {
        LocalDate today = LocalDate.now();
        // 获取本周一
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 上周日 = 本周一 - 1天
        LocalDate lastSunday = thisMonday.minusDays(1);
        LocalDateTime lastWeekEnd = lastSunday.atTime(23, 59, 59);
        return Date.from(lastWeekEnd.atZone(ZoneId.systemDefault()).toInstant());
    }

    @Override
    public TableDataInfo<WrongQuestionCollectionVo> wrongQuestionCollection(WrongQuestionStatisticsBo bo, PageQuery pageQuery) {
        Page<WrongQuestionCollectionVo> page = baseMapper.selectWrongQuestionCollectionPage(pageQuery.build(), bo);
        return TableDataInfo.build(page);
    }
}
