package com.jxw.shufang.report.utils;

import lombok.experimental.UtilityClass;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

@UtilityClass
public class DateUtils {

    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_DATE_FORMAT_1 = "yyyy/MM/dd";

    public static String dateToStr(Date date) {
        return dateToLocalDateTime(date).format(DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT));
    }

    public static String dateToStr(LocalDateTime localDateTime) {
        return localDateTime.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT));
    }

    public static String dateToStr(LocalDate localDate) {
        return localDate.format(DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
    }

    public static String dateToStr(Date date, String format) {
        return dateToLocalDateTime(date).format(DateTimeFormatter.ofPattern(format));
    }

    public static String dateToStr(LocalDateTime localDateTime, String format) {
        return localDateTime.format(DateTimeFormatter.ofPattern(format));
    }

    public static Date dateTimeStrToDate(String dateStr, String format) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(format));
        return localDateTimeToDate(localDateTime);
    }

    public static Date dateStrToDate(String dateStr, String format) {
        LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format));
        return localDateToDate(localDate);
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    public static LocalDate dateToLocalDate(Date date) {
        return dateToLocalDateTime(date).toLocalDate();
    }

    public static LocalDateTime plusDays(Date date, int days) {
        return dateToLocalDateTime(date).plusDays(days);
    }

    public static LocalDate strToLocalDate(String dateStr) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
    }

    public static LocalDate strToLocalDate(String dateStr, String format) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(format));
    }

    public static int daysBetween(LocalDate start, LocalDate end) {
        return Period.between(start, end).getDays();
    }

    public static Date getTodayEndTime() {
        return Date.from(LocalDate.now().atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());
    }
    /**
     * 获取上周一的开始时间（00:00:00）
     * 上周指的是上一个自然周，即上周一到上周日
     */
    public static Date getLastWeekStart() {
        LocalDate today = LocalDate.now();
        // 获取本周一
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 上周一 = 本周一 - 7天
        LocalDate lastMonday = thisMonday.minusWeeks(1);
        LocalDateTime lastWeekStart = lastMonday.atStartOfDay();
        return Date.from(lastWeekStart.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取上周日的结束时间（23:59:59）
     * 上周指的是上一个自然周，即上周一到上周日
     */
    public static Date getLastWeekEnd() {
        LocalDate today = LocalDate.now();
        // 获取本周一
        LocalDate thisMonday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 上周日 = 本周一 - 1天
        LocalDate lastSunday = thisMonday.minusDays(1);
        LocalDateTime lastWeekEnd = lastSunday.atTime(23, 59, 59);
        return Date.from(lastWeekEnd.atZone(ZoneId.systemDefault()).toInstant());
    }
}
