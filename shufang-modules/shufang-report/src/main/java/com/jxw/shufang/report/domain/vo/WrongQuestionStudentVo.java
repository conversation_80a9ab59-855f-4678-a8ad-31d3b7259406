package com.jxw.shufang.report.domain.vo;

import lombok.Data;

import java.util.Date;

@Data
public class WrongQuestionStudentVo {

    private Long branchId;

    private String branchName;

    private Long studentId;

    private String studentName;

    private Long consultantId;

    private String consultantName;

    private String grade;

    private Integer sex;

    /**
     * 上周错题数量
     */
    private Integer lastWeekWrongCount;

    /**
     * 错题总数
     */
    private Integer wrongTotal;

    /**
     * 是否打印上周错题
     */
    private Integer printLastWeek;

    /**
     * 上周订正数量
     */
    private Integer lastWeekReviseCount;

    /**
     * 订正总数
     */
    private Integer reviseTotal;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

}
