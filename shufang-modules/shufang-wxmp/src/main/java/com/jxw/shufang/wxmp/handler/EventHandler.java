package com.jxw.shufang.wxmp.handler;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutTextMessage;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.student.api.RemoteStudentParentRecordService;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class EventHandler extends AbstractHandler {

    @DubboReference
    private RemoteStudentParentRecordService remoteStudentParentRecordService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService wxMpService,
                                    WxSessionManager sessionManager) {
        log.info("\n接收到请求消息，内容：{}", JsonUtils.toJsonString(wxMessage));

        try {
            if (null != wxMessage) {
                //扫码绑定学生
                if (StringUtils.isNotBlank(wxMessage.getEventKey()) && wxMessage.getEventKey().contains("bind:")) {

                    log.info(wxMessage.getFromUser() + "绑定学生");

                    String studentId = wxMessage.getEventKey().replace("qrscene_", "").replace("bind:", "");
                    if (StringUtils.isNotBlank(studentId)) {

                        String openId = wxMessage.getFromUser();

                        if (StringUtils.isNotBlank(openId)) {
                            if (remoteStudentParentRecordService.bind(Long.parseLong(studentId), openId)) {

                                log.info(wxMessage.getFromUser() + "绑定成功");

                                WxMpXmlOutTextMessage m
                                    = WxMpXmlOutMessage.TEXT()
                                    .content("成功绑定学生")
                                    .fromUser(wxMessage.getToUser())
                                    .toUser(wxMessage.getFromUser())
                                    .build();
                                return m;
                            }
                        }

                    }

                    WxMpXmlOutTextMessage m
                        = WxMpXmlOutMessage.TEXT()
                        .content("欢迎使用学王书房！\n" +
                            "请绑定学生")
                        .fromUser(wxMessage.getToUser())
                        .toUser(wxMessage.getFromUser())
                        .build();
                    return m;
                }


                //自主关注
                if (WxConsts.EventType.SUBSCRIBE.equals(wxMessage.getEvent())) {

                    log.info(wxMessage.getFromUser() + "关注了");

                    WxMpXmlOutTextMessage m
                        = WxMpXmlOutMessage.TEXT()
                        .content("欢迎使用学王书房！")
                        .fromUser(wxMessage.getToUser())
                        .toUser(wxMessage.getFromUser())
                        .build();

                    return m;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return null;
    }
}
