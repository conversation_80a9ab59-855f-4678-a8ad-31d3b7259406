package com.jxw.shufang.wxmp.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.wxmp.domain.SysWxConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 微信配置视图对象 sys_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysWxConfig.class)
public class SysWxConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long wxConfigId;

    /**
     * 0公众号 1小程序
     */
    @ExcelProperty(value = "0公众号 1小程序")
    private Integer appType;

    /**
     * AppID
     */
    @ExcelProperty(value = "AppID")
    private String appId;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String appName;

    /**
     * 小程序AppSecret
     */
    @ExcelProperty(value = "小程序AppSecret")
    private String appSecret;

    /**
     * 公众号类型 0服务号 1订阅号 2企业号
     */
    @ExcelProperty(value = "公众号类型 0服务号 1订阅号 2企业号")
    private Integer wxmpType;

    /**
     * 公众号原始id
     */
    @ExcelProperty(value = "公众号原始id")
    private String wxmpAccount;

    /**
     * 公众号密钥
     */
    @ExcelProperty(value = "公众号密钥")
    private String wxmpAppsecret;

    /**
     * 公众号令牌
     */
    @ExcelProperty(value = "公众号令牌")
    private String wxmpToken;

    /**
     * 公众号消息密钥
     */
    @ExcelProperty(value = "公众号消息密钥")
    private String wxmpAeskey;

    private Date createTime;
}
