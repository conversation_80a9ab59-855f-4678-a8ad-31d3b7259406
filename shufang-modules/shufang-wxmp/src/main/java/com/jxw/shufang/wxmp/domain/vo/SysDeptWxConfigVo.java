package com.jxw.shufang.wxmp.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.jxw.shufang.wxmp.domain.SysDeptWxConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 代理商微信配置视图对象 sys_dept_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysDeptWxConfig.class)
public class SysDeptWxConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long deptWxConfigId;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    private Long deptId;

    private Long wxConfigId;

    private String appId;

    private String appName;


}
