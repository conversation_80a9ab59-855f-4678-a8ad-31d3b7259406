package com.jxw.shufang.wxmp.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.wxmp.consumer.WxMpConfigurationListener;
import com.jxw.shufang.wxmp.consumer.vo.WxMpConfigConsumerVo;
import com.jxw.shufang.wxmp.domain.SysWxConfig;
import com.jxw.shufang.wxmp.domain.bo.SysWxConfigBo;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;
import com.jxw.shufang.wxmp.mapper.SysWxConfigMapper;
import com.jxw.shufang.wxmp.service.ISysWxConfigService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 微信配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Service
public class SysWxConfigServiceImpl implements ISysWxConfigService, BaseService {

    private final SysWxConfigMapper baseMapper;
    private final RocketMQTemplate rocketMQTemplate;

    public SysWxConfigServiceImpl(SysWxConfigMapper baseMapper, RocketMQTemplate rocketMqTemplate) {
        this.baseMapper = baseMapper;
        this.rocketMQTemplate = rocketMqTemplate;
    }

    /**
     * 查询微信配置
     */
    @Override
    public SysWxConfigVo queryById(Long wxConfigId){
        return baseMapper.selectVoById(wxConfigId);
    }

    /**
     * 查询微信配置列表
     */
    @Override
    public TableDataInfo<SysWxConfigVo> queryPageList(SysWxConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysWxConfig> lqw = buildQueryWrapper(bo);
        Page<SysWxConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询微信配置列表
     */
    @Override
    public List<SysWxConfigVo> queryList(SysWxConfigBo bo) {
        LambdaQueryWrapper<SysWxConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysWxConfig> buildQueryWrapper(SysWxConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysWxConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAppType() != null, SysWxConfig::getAppType, bo.getAppType());
        lqw.eq(StringUtils.isNotBlank(bo.getAppId()), SysWxConfig::getAppId, bo.getAppId());
        lqw.like(StringUtils.isNotBlank(bo.getAppName()), SysWxConfig::getAppName, bo.getAppName());
        lqw.eq(StringUtils.isNotBlank(bo.getAppSecret()), SysWxConfig::getAppSecret, bo.getAppSecret());
        lqw.eq(bo.getWxmpType() != null, SysWxConfig::getWxmpType, bo.getWxmpType());
        lqw.eq(StringUtils.isNotBlank(bo.getWxmpAccount()), SysWxConfig::getWxmpAccount, bo.getWxmpAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getWxmpAppsecret()), SysWxConfig::getWxmpAppsecret, bo.getWxmpAppsecret());
        lqw.eq(StringUtils.isNotBlank(bo.getWxmpToken()), SysWxConfig::getWxmpToken, bo.getWxmpToken());
        lqw.eq(StringUtils.isNotBlank(bo.getWxmpAeskey()), SysWxConfig::getWxmpAeskey, bo.getWxmpAeskey());
        lqw.ge(bo.getCreateTimeStart() != null, SysWxConfig::getCreateTime, bo.getCreateTimeStart() != null ? DateUtils.dateTime(bo.getCreateTimeStart()) : null);
        lqw.le(bo.getCreateTimeEnd() != null, SysWxConfig::getCreateTime, bo.getCreateTimeEnd() != null ? DateUtils.dateTime(bo.getCreateTimeEnd()) : null);
        return lqw;
    }

    /**
     * 新增微信配置
     */
    @Override
    public Boolean insertByBo(SysWxConfigBo bo) {
        SysWxConfig add = MapstructUtils.convert(bo, SysWxConfig.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setWxConfigId(add.getWxConfigId());
            // 插入成功后，发送消息到 RocketMQ
            rocketMQTemplate.convertAndSend(WxMpConfigurationListener.TOPIC, new WxMpConfigConsumerVo());
        }
        return flag;
    }

    /**
     * 修改微信配置
     */
    @Override
    public Boolean updateByBo(SysWxConfigBo bo) {
        SysWxConfig update = MapstructUtils.convert(bo, SysWxConfig.class);
        validEntityBeforeSave(update);
        // 插入成功后，发送消息到 RocketMQ
        Boolean successFlag = baseMapper.updateById(update) > 0;
        if (successFlag) {
            rocketMQTemplate.convertAndSend(WxMpConfigurationListener.TOPIC, new WxMpConfigConsumerVo());
        }
        return successFlag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysWxConfig entity){
        String appId = entity.getAppId();
        LambdaQueryWrapper<SysWxConfig> sysWxConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysWxConfigLambdaQueryWrapper.eq(SysWxConfig::getAppId, appId);
        SysWxConfig sysWxConfig = baseMapper.selectOne(sysWxConfigLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(sysWxConfig)){
            return;
        }
        if (!sysWxConfig.getWxConfigId().equals(entity.getWxConfigId())) {
            throw new ServiceException("appId已存在，该配置为："+entity.getAppName());
        }
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除微信配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        Boolean successFlag =baseMapper.deleteBatchIds(ids) > 0;
        if (successFlag) {
            rocketMQTemplate.convertAndSend(WxMpConfigurationListener.TOPIC, new WxMpConfigConsumerVo());
        }
        return successFlag;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
