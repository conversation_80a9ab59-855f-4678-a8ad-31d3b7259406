package com.jxw.shufang.wxmp.controller;

import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;
import com.jxw.shufang.wxmp.service.ISysWxConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMenuService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.menu.WxMpMenu;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;


@AllArgsConstructor
@RestController
@RequestMapping("/wxMenu")
@Slf4j
public class WxMenuController extends BaseController {
    private final WxMpService wxMpService;

    private final ISysWxConfigService sysWxConfigService;

    /**
     * 菜单创建,要求全量添加
     *
     * @param menuJson 菜单
     * @throws WxErrorException wx错误异常
     * @date 2024/02/05 04:52:52
     */
    @PostMapping("/create")
    public String menuCreate(@RequestBody String menuJson,@RequestParam Long wxConfigId) throws WxErrorException {
        if (StringUtils.isBlank(menuJson)) {
            throw new ServiceException("菜单不能为空");
        }
        SysWxConfigVo sysWxConfigVo = sysWxConfigService.queryById(wxConfigId);
        if (ObjectUtils.isEmpty(sysWxConfigVo)) {
            return "";
        }
        String appId = sysWxConfigVo.getAppId();
        if (Boolean.FALSE.equals(wxMpService.switchover(appId))) {
            getError(wxConfigId, appId);
            return "";
        }
        return wxMpService.getMenuService().menuCreate(menuJson);
    }


    /**
     * 删除个性化菜单接口
     * @param menuId 个性化菜单的menuid
     */
    @DeleteMapping("/delete/{menuId}")
    public R<Void> menuDelete(@PathVariable String menuId,Long wxConfigId)  {
        try {
            SysWxConfigVo sysWxConfigVo = sysWxConfigService.queryById(wxConfigId);
            if (ObjectUtils.isEmpty(sysWxConfigVo)) {
                return R.ok();
            }
            String appId = sysWxConfigVo.getAppId();
            if (Boolean.FALSE.equals(wxMpService.switchover(appId))) {
                getError(wxConfigId, appId);
                return R.ok();
            }
            wxMpService.getMenuService().menuDelete(menuId);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 删除菜单接口,全量删除，在个性化菜单时，调用此接口会删除默认菜单及全部个性化菜单。
     */
    @DeleteMapping("/menuDeleteAll")
    public R<Void> menuDeleteAll(Long wxConfigId)  {
        try {
            SysWxConfigVo sysWxConfigVo = sysWxConfigService.queryById(wxConfigId);
            if (ObjectUtils.isEmpty(sysWxConfigVo)) {
                return R.ok();
            }
            String appId = sysWxConfigVo.getAppId();
            if (Boolean.FALSE.equals(wxMpService.switchover(appId))) {
                getError(wxConfigId, appId);
                return R.ok();
            }
            wxMpService.getMenuService().menuDelete();
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        return R.ok();
    }

    /**
     * 自定义菜单查询接口
     */
    @GetMapping("/get")
    public R<Dict> menuGet(Long wxConfigId){
        try {
            if (ObjectUtil.isNull(wxConfigId)) {
                WxMpMenu wxMpMenu = wxMpService.getMenuService().menuGet();
                String json = wxMpMenu.toJson();
                return R.ok(JsonUtils.parseMap(json));
            }

            SysWxConfigVo sysWxConfigVo = sysWxConfigService.queryById(wxConfigId);
            if (ObjectUtils.isEmpty(sysWxConfigVo)) {
                return R.ok();
            }
            String appId = sysWxConfigVo.getAppId();
            if (Boolean.FALSE.equals(wxMpService.switchover(appId))) {
                getError(wxConfigId, appId);
                return R.ok();
            }
            WxMpMenuService menuService = wxMpService.getMenuService();
            if (ObjectUtils.isEmpty(menuService)){
                return R.ok();
            }
            WxMpMenu wxMpMenu = menuService.menuGet();
            if (ObjectUtils.isEmpty(wxMpMenu)){
                return R.ok();
            }
            String json = wxMpMenu.toJson();
            return R.ok(JsonUtils.parseMap(json));
        }catch (Exception e){
            throw new ServiceException(e.getMessage());
        }
    }

    private static void getError(Long wxConfigId, String appId) {
        log.error("微信公众号配置找不到：wxConfigID:{},appID:{}", wxConfigId, appId);
    }
}
