package com.jxw.shufang.wxmp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 代理商微信配置对象 sys_dept_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dept_wx_config")
public class SysDeptWxConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "dept_wx_config_id")
    private Long deptWxConfigId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 小程序配置 sys_wx_config.wx_config_id
     */
    private Long appConfigId;

    /**
     * 微信配置 sys_wx_config.wx_config_id
     */
    private Long wxmpConfigId;


}
