package com.jxw.shufang.wxmp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.enums.AppTypeEnum;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.wxmp.domain.SysDeptWxConfig;
import com.jxw.shufang.wxmp.domain.bo.SysDeptWxConfigBo;
import com.jxw.shufang.wxmp.domain.bo.SysDeptWxRelatedBo;
import com.jxw.shufang.wxmp.domain.vo.SysDeptWxConfigVo;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;
import com.jxw.shufang.wxmp.mapper.SysDeptWxConfigMapper;
import com.jxw.shufang.wxmp.service.ISysDeptWxConfigService;
import com.jxw.shufang.wxmp.service.ISysWxConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * 代理商微信配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@RequiredArgsConstructor
@Service
public class SysDeptWxConfigServiceImpl implements ISysDeptWxConfigService, BaseService {

    private final SysDeptWxConfigMapper baseMapper;
    private final ISysWxConfigService sysWxConfigService;
    /**
     * 查询代理商微信配置
     */
    @Override
    public SysDeptWxConfigVo queryById(Long deptId, Integer appType) {
        LambdaQueryWrapper<SysDeptWxConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDeptWxConfig::getDeptId, deptId);
        SysDeptWxConfig sysDeptWxConfig = baseMapper.selectOne(wrapper);
        SysDeptWxConfigVo convert = MapstructUtils.convert(sysDeptWxConfig, SysDeptWxConfigVo.class);

        if (ObjectUtils.isEmpty(convert)){
            return null;
        }

        if (AppTypeEnum.WXMP.getCode().equals(appType)) {
            convert.setWxConfigId(sysDeptWxConfig.getWxmpConfigId());
        } else {
            convert.setWxConfigId(sysDeptWxConfig.getAppConfigId());
        }

        SysWxConfigVo sysWxConfigVo = sysWxConfigService.queryById(convert.getWxConfigId());
        if (!ObjectUtils.isEmpty(sysWxConfigVo)){
            convert.setAppName(sysWxConfigVo.getAppName());
            convert.setAppId(sysWxConfigVo.getAppId());
        }else {
            //如果没有找到对应的微信配置、有可能对应的配置给删除了、直接返回空
            return null;
        }
        return convert;
    }

    /**
     * 查询代理商微信配置列表
     */
    @Override
    public TableDataInfo<SysDeptWxConfigVo> queryPageList(SysDeptWxConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysDeptWxConfig> lqw = buildQueryWrapper(bo);
        Page<SysDeptWxConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询代理商微信配置列表
     */
    @Override
    public List<SysDeptWxConfigVo> queryList(SysDeptWxConfigBo bo) {
        LambdaQueryWrapper<SysDeptWxConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysDeptWxConfig> buildQueryWrapper(SysDeptWxConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysDeptWxConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getDeptId() != null, SysDeptWxConfig::getDeptId, bo.getDeptId());
        return lqw;
    }

    /**
     * 新增代理商微信配置
     */
    @Override
    public Boolean related(SysDeptWxRelatedBo bo) {
        LambdaQueryWrapper<SysDeptWxConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDeptWxConfig::getDeptId, bo.getDeptId());
        SysDeptWxConfig sysDeptWxConfig = baseMapper.selectOne(wrapper);
        if (ObjectUtils.isEmpty(sysDeptWxConfig)) {
            sysDeptWxConfig = new SysDeptWxConfig();
        }
        Integer appType = bo.getAppType();

        sysDeptWxConfig.setDeptId(bo.getDeptId());

        if (AppTypeEnum.APP.getCode().equals(appType)) {
            sysDeptWxConfig.setAppConfigId(bo.getWxConfigId());
        }else if (AppTypeEnum.WXMP.getCode().equals(appType)){
            sysDeptWxConfig.setWxmpConfigId(bo.getWxConfigId());
        }

        return baseMapper.insertOrUpdate(sysDeptWxConfig);
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysDeptWxConfig entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
