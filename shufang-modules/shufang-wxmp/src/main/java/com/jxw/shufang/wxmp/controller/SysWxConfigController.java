package com.jxw.shufang.wxmp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.wxmp.consumer.WxMpConfigurationListener;
import com.jxw.shufang.wxmp.consumer.vo.WxMpConfigConsumerVo;
import com.jxw.shufang.wxmp.domain.bo.SysWxConfigBo;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;
import com.jxw.shufang.wxmp.service.ISysWxConfigService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信配置
 * 前端访问路由地址为:/wxmp/wxConfig
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wxConfig")
public class SysWxConfigController extends BaseController {

    private final ISysWxConfigService sysWxConfigService;
    @DubboReference
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 查询微信配置列表
     */
    @SaCheckPermission("wxmp:wxConfig:list")
    @GetMapping("/list")
    public TableDataInfo<SysWxConfigVo> list(SysWxConfigBo bo, PageQuery pageQuery) {
        return sysWxConfigService.queryPageList(bo, pageQuery);
    }

    /**
     *  查询可关联机构
     */
    @SaCheckPermission("wxmp:wxConfig:list")
    @GetMapping("/queryDept")
    public R<List<Tree<Long>>> getDeptList(RemoteDeptBo bo) {
        bo.setIsStore(false);
        return R.ok(remoteDeptService.selectDeptTreeList(bo));
    }

    /**
     * 获取微信配置详细信息
     *
     * @param wxConfigId 主键
     */
    @SaCheckPermission("wxmp:wxConfig:query")
    @GetMapping("/{wxConfigId}")
    public R<SysWxConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long wxConfigId) {
        return R.ok(sysWxConfigService.queryById(wxConfigId));
    }


    /**
     * 新增微信配置
     */
    @SaCheckPermission("wxmp:wxConfig:add")
    @Log(title = "微信配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysWxConfigBo bo) {
        return toAjax(sysWxConfigService.insertByBo(bo));
    }

    /**
     * 修改微信配置
     */
    @SaCheckPermission("wxmp:wxConfig:edit")
    @Log(title = "微信配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()

    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysWxConfigBo bo) {
        return toAjax(sysWxConfigService.updateByBo(bo));
    }

    /**
     * 删除微信配置
     *
     * @param wxConfigIds 主键串
     */
    @SaCheckPermission("wxmp:wxConfig:remove")
    @Log(title = "微信配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{wxConfigIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] wxConfigIds) {
        return toAjax(sysWxConfigService.deleteWithValidByIds(List.of(wxConfigIds), true));
    }

    /**
     * 主动触发刷新配置
     * @return
     */
    @GetMapping("/refresh")
    public R<Void> refresh() {
        rocketMQTemplate.convertAndSend(WxMpConfigurationListener.TOPIC, new WxMpConfigConsumerVo());
        return toAjax(true);
    }

}
