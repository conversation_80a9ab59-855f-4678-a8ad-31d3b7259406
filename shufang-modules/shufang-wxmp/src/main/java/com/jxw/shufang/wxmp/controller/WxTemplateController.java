package com.jxw.shufang.wxmp.controller;

import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@AllArgsConstructor
@RestController
@RequestMapping("/wxMenu")
public class WxTemplateController extends BaseController {
    private final WxMpService wxMpService;

    /**
     * 发送模板消息
     */
    @PostMapping("/sendTemplateMsg")
    public R<String> sendTemplateMsg(WxMpTemplateMessage templateMessage) {
        try {
            String msgId = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
            return R.ok(msgId);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }


}
