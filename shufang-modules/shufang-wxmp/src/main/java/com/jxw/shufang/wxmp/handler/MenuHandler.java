package com.jxw.shufang.wxmp.handler;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import com.jxw.shufang.common.json.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
@Slf4j
public class MenuHandler extends AbstractHandler {

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) {
        log.info("\n接收到菜单消息，内容：{}", JsonUtils.toJsonString(wxMessage));

        return null;
    }

}
