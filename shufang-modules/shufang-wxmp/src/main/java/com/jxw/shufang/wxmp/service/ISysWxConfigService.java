package com.jxw.shufang.wxmp.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.wxmp.domain.bo.SysWxConfigBo;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 微信配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
public interface ISysWxConfigService {

    /**
     * 查询微信配置
     */
    SysWxConfigVo queryById(Long wxConfigId);

    /**
     * 查询微信配置列表
     */
    TableDataInfo<SysWxConfigVo> queryPageList(SysWxConfigBo bo, PageQuery pageQuery);

    /**
     * 查询微信配置列表
     */
    List<SysWxConfigVo> queryList(SysWxConfigBo bo);

    /**
     * 新增微信配置
     */
    Boolean insertByBo(SysWxConfigBo bo);

    /**
     * 修改微信配置
     */
    Boolean updateByBo(SysWxConfigBo bo);

    /**
     * 校验并批量删除微信配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
