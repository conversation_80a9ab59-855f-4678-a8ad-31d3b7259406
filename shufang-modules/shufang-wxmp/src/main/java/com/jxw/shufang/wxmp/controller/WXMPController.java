package com.jxw.shufang.wxmp.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import org.apache.commons.lang3.StringUtils;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/wxmp")
public class WXMPController extends BaseController {
    private final WxMpService wxMpService;

    private final WxMpMessageRouter wxMpMessageRouter;

    private final WxMpConfigStorage wxMpConfigStorage;

    //忽略登录校验
//    @GetMapping(value = "/checkSignature",produces = "text/plain;charset=utf-8")
//    public String checkSignature(
//                          @RequestParam(name = "signature", required = false) String signature,
//                          @RequestParam(name = "timestamp", required = false) String timestamp,
//                          @RequestParam(name = "nonce", required = false) String nonce,
//                          @RequestParam(name = "echostr", required = false) String echostr) {
//
//
//        if (StringUtils.isAnyBlank(signature, timestamp, nonce)) {
//            throw new IllegalArgumentException("请求参数非法，请核实!");
//        }
//
//
//        if (wxMpService.checkSignature(timestamp, nonce, signature)) {
//            return echostr;
//        }
//
//        return "非法请求";
//    }


    /**
     * 处理公众号事件
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/event/handle")
    public void handleEvent(HttpServletRequest request, HttpServletResponse response) throws Exception {

        log.info("开始处理公众号事件");

        response.setContentType("text/html;charset=utf-8");
        response.setStatus(HttpServletResponse.SC_OK);

        String signature = request.getParameter("signature");
        String nonce = request.getParameter("nonce");
        String timestamp = request.getParameter("timestamp");

        if (!this.wxMpService.checkSignature(timestamp, nonce, signature)) {
            // 消息签名不正确，说明不是公众平台发过来的消息
            response.getWriter().println("非法请求");
            return;
        }

        String echoStr = request.getParameter("echostr");
        if (StringUtils.isNotBlank(echoStr)) {
            // 说明是一个仅仅用来验证的请求，回显echostr
            String echoStrOut = String.copyValueOf(echoStr.toCharArray());
            response.getWriter().println(echoStrOut);
            return;
        }

        String encryptType = StringUtils.isBlank(request.getParameter("encrypt_type")) ? "raw" : request.getParameter("encrypt_type");

        if ("raw".equals(encryptType)) {
            // 明文传输的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(request.getInputStream());
            WxMpXmlOutMessage outMessage = wxMpMessageRouter.route(inMessage);
            if (outMessage == null) {
                response.getWriter().write("");
            } else {
                response.getWriter().write(outMessage.toXml());
            }
            return;
        }

        if ("aes".equals(encryptType)) {
            // 是aes加密的消息
            String msgSignature = request.getParameter("msg_signature");
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(
                request.getInputStream(), wxMpConfigStorage, timestamp, nonce,
                msgSignature);
            log.info("\n消息解密后内容为：\n{" + inMessage.toString() + "}");
            WxMpXmlOutMessage outMessage = wxMpMessageRouter.route(inMessage);
            if (outMessage == null) {
                response.getWriter().write("");
            } else {
                response.getWriter().write(outMessage.toEncryptedXml(wxMpConfigStorage));
            }
            return;
        }
        response.getWriter().println("不可识别的加密类型");
    }


    @RequestMapping("/initJSSDKConfig")
    public R<WxJsapiSignature> initJSSDKConfig(String url) {
        try{
            WxJsapiSignature jsapiSignature = wxMpService.createJsapiSignature(url);
            return R.ok(jsapiSignature);
        }catch(Exception e){
            log.error("获取JSSDK配置失败", e);
            return R.fail("获取JSSDK配置失败");
        }
    }
}
