package com.jxw.shufang.wxmp.controller;

import cn.dev33.satoken.stp.SaLoginModel;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.CacheConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.student.api.RemoteStudentParentRecordService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentParentRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentParentRecordVo;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.model.XcxLoginUser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/wxUser")
public class WxUserController extends BaseController {
    private final WxMpService wxMpService;

    @Value("${wx.mp.domainName}")
    public String domainName;

    @DubboReference
    private RemoteStudentParentRecordService remoteStudentParentRecordService;

    @DubboReference
    private RemoteUserService remoteUserService;

    public R<String> sendTemplateMsg(WxMpTemplateMessage templateMessage) {
        try {
            String msgId = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
            return R.ok(msgId);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }


    /**
     * 包装URL再转发
     */
    @GetMapping("/forward")
    public void forward(HttpServletRequest request, HttpServletResponse response) {
        log.info("原始url：" + request.getRequestURL().toString());

        //授权后要跳转的链接
        String path = request.getParameter("path");

        if (StringUtils.isNotBlank(path)) {
            if (path.endsWith("\\")) {
                path = path.substring(0, path.length() - 1);
            }
            path += path.contains("?") ? "" : "?";
        }

        //所包含的其它参数
        Map<String, String[]> paramMap = request.getParameterMap();
        String param = "";
        for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
            if (!"type".equals(entry.getKey()) && !"path".equals(entry.getKey())) {
                param += "&" + entry.getKey() + "=" + entry.getValue()[0];
            }
        }
        log.info("param：" + param);

        String keyId = System.currentTimeMillis() + "_" + UUID.randomUUID().toString();

        String url = "", backUri = "";

        //http://ben866454826.51vip.biz/dev-api/wxmp/wxUser/forward?path=http://ben866454826.51vip.biz/dev-api/wxmp/wxmp/checkSignature


        if (StringUtils.isNotBlank(path)) {
            backUri = domainName+"/wxmp/wxUser/getWeChatInfo?path=" + path + param + "&keyId=" + keyId;

            url = wxMpService.getOAuth2Service().buildAuthorizationUrl(backUri, "snsapi_userinfo", "");
        }

        log.info("url：" + url);
        try {
            response.sendRedirect(url);
        } catch (IOException e) {
            log.error("微信公众号转发有误", e);
        }
    }



    /**
     * 获取用户信息统一处理 然后转发
     *
     * @return
     */
    @GetMapping("/getWeChatInfo")
    public void getWeChatInfo(HttpServletRequest request, HttpServletResponse response) {
        //微信的参数
        String code = request.getParameter("code");
        String lang = request.getParameter("lang");

        String openId = this.getOpenId(code, lang);
        String param = "";
        if(StringUtils.isNotBlank(openId)){
            this.updateMemberWeChat(openId,code, lang);

            String keyId = request.getParameter("keyId");

            if(StringUtils.isNotBlank(keyId)){
                RedisUtils.setCacheObject(CacheConstants.WX_OPEN_ID + keyId, openId, Duration.ofHours(12));
            }
        }

        String backUri = "";
        try {
            //授权后要跳转的链接
            String path = request.getParameter("path");

            path += path.contains("?") ? "" : "?";

            //所包含的其它参数
            Map<String, String[]> paramMap = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
                if (!",path,code,lang,".contains("," + entry.getKey() + ",")) {
                    param += "&" + entry.getKey() + "=" + entry.getValue()[0];
                }
            }

            if (StringUtils.isNotBlank(path)) {
                backUri = path + param ;
                log.info("sendRedirec2=" + backUri);
                response.sendRedirect(backUri);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //获取openId
    public String getOpenId(String code,String lang) {
        String openId = null;
        WxOAuth2AccessToken accessToken = null;
        try {
            if (StringUtils.isNotBlank(code)){
                accessToken = RedisUtils.getCacheObject(CacheConstants.WX_CODE + code);
            }
            if (null == accessToken) {
                accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
                log.info("accessToken:"+accessToken);
                RedisUtils.setCacheObject(CacheConstants.WX_CODE + code, accessToken, Duration.ofMinutes(100));
            }
            openId = accessToken.getOpenId();
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return openId;
    }

    //新增或更新微信人员信息
    public Boolean updateMemberWeChat(String openId,String code, String lang) {
        WxOAuth2AccessToken accessToken = null;
        WxMpUser wxMpUser = null;
        WxOAuth2UserInfo wxOAuth2UserInfo = null;
        try {
            if (StringUtils.isNotBlank(code)){
                accessToken = RedisUtils.getCacheObject(CacheConstants.WX_CODE + code);
            }
            if (null == accessToken) {
                accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
                RedisUtils.setCacheObject(CacheConstants.WX_CODE + code, accessToken, Duration.ofMinutes(100));
            }
            wxMpUser = wxMpService.getUserService().userInfo(accessToken.getOpenId(), lang);
            if (null != wxMpUser && (null == wxMpUser.getSubscribe() || !wxMpUser.getSubscribe())) {
                wxOAuth2UserInfo = wxMpService.getOAuth2Service().getUserInfo(accessToken, lang);
                wxMpUser.setSubscribe(false);
            }
        } catch (WxErrorException e) {
            e.printStackTrace();
        }

        if(null != wxOAuth2UserInfo && null == wxMpUser){
           return this.updateWeChatInfo(wxOAuth2UserInfo.getOpenid(),wxOAuth2UserInfo.getUnionId(),wxOAuth2UserInfo.getNickname(),null,wxOAuth2UserInfo.getHeadImgUrl());
        }
        if(null == wxOAuth2UserInfo && null != wxMpUser){
           return this.updateWeChatInfo(wxMpUser.getOpenId(),wxMpUser.getUnionId(),wxMpUser.getNickname(),null,wxMpUser.getHeadImgUrl());
        }
        if(null != openId){
            return this.updateWeChatInfo(openId,null,null,null,null);
        }
        log.error("微信公众号更新微信信息失败");
        return false;
    }


    //新增或更新微信人员信息
    public Boolean updateWeChatInfo(String openId,String unionId,String wechatNickname,String wechatNo,String wechatImg) {
        if(StringUtils.isBlank(openId) && StringUtils.isBlank(unionId)){
            log.error("微信公众号获取不到相应的openId和unionId！");
            return false;
        }

        RemoteStudentParentRecordBo bo = new RemoteStudentParentRecordBo();
        if(StringUtils.isNotBlank(unionId)){
            bo.setParentWechatUnionId(unionId);
        }else {
            bo.setParentWechatOpenId(openId);
        }

        try {
            XcxLoginUser loginUser = remoteUserService.getUserInfoByOpenid(openId);
            loginUser.setClientKey("officialaccount");
            loginUser.setDeviceType("officialaccount");
            loginUser.setRoles(new ArrayList<>());

            SaLoginModel model = new SaLoginModel();
            model.setDevice("officialaccount");
            model.setTimeout(604800L);
            model.setActiveTimeout(18000L);
            model.setExtra(LoginHelper.CLIENT_KEY, "officialaccount"+openId);
            // 登录状态
            LoginHelper.login(loginUser, model);

            log.info("token:"+loginUser.getToken());
        }catch (Exception e){
            log.error("尝试登录 {}",e);
        }

        List<RemoteStudentParentRecordVo> voList = remoteStudentParentRecordService.queryList(bo);
        if(null != voList && voList.size()>0){
            voList.forEach(vo->{
                bo.setParentWechatUnionId(unionId);
                bo.setParentWechatOpenId(openId);
                bo.setStudentParentRecordId(vo.getStudentParentRecordId());
                bo.setParentWechatImg(wechatImg);
                bo.setParentWechatNickname(wechatNickname);
                bo.setParentWechatNo(wechatNo);
                remoteStudentParentRecordService.updateByBo(bo);
            });
            return true;
        }else {
            bo.setParentWechatUnionId(unionId);
            bo.setParentWechatOpenId(openId);
            bo.setParentWechatImg(wechatImg);
            bo.setParentWechatNickname(wechatNickname);
            bo.setParentWechatNo(wechatNo);
            return remoteStudentParentRecordService.insertByBo(bo);
        }
    }

}
