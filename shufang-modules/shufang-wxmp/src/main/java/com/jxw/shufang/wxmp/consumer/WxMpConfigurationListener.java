package com.jxw.shufang.wxmp.consumer;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.wxmp.consumer.vo.WxMpConfigConsumerVo;
import com.jxw.shufang.wxmp.domain.bo.SysWxConfigBo;
import com.jxw.shufang.wxmp.domain.vo.SysWxConfigVo;
import com.jxw.shufang.wxmp.service.ISysWxConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = WxMpConfigurationListener.TOPIC, // 修改为新的 Topic 名称
    consumerGroup = "shufang-wx-config-consumer", // 修改为新的 ConsumerGroup 名称
    messageModel = MessageModel.BROADCASTING // 设置为广播模式
)
@Slf4j
public class WxMpConfigurationListener implements RocketMQListener<JSONObject> {
    public static final String TOPIC = "wx-config-sync";


    @Value("${wx.mp.useRedis: false}")
    private Boolean isUseRedis;

    private final WxMpService wxMpService;

    private final ISysWxConfigService sysWxConfigService;

    @Override
    public void onMessage(JSONObject message) {
        try {

            // message 解析为WxMpConfigConsumerVo 实体
            // WxMpConfigConsumerVo wxMpConfigConsumerVo = deserializeMessage(message);

            List<SysWxConfigVo> configList = sysWxConfigService.queryList(new SysWxConfigBo());
            // 获取本机 IP 地址
            String ipAddress = getLocalIpAddress();

            // 遍历列表，更新本地缓存
            for (SysWxConfigVo config : configList) {
                WxMpDefaultConfigImpl configStorage = createConfigStorage(config);
                wxMpService.addConfigStorage(config.getAppId(), configStorage);
                // 打印成功标识和机器 IP
                log.info("配置同步成功！AppId: {}, 机器 IP: {}", config.getAppId(), ipAddress);
            }
        } catch (Exception e) {
            // 处理异常，例如记录日志或抛出运行时异常
            log.error("Failed to process message", e);
            throw new RuntimeException("Failed to process message", e);
        }
    }

    private WxMpConfigConsumerVo deserializeMessage(JSONObject message) throws JsonProcessingException {
        // 使用 Jackson 将 JSONObject 转换为 List<SysWxConfigBo>
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(message.toString(), new TypeReference<WxMpConfigConsumerVo>() {});
    }

    private WxMpDefaultConfigImpl createConfigStorage(SysWxConfigVo config) {
        WxMpDefaultConfigImpl configStorage;

        if (Boolean.TRUE.equals(isUseRedis)) {
            RedissonClient redissonClient = SpringUtils.getBean(RedissonClient.class);
            configStorage = new WxMpRedissonConfigImpl(redissonClient, config.getAppId());
        } else {
            configStorage = new WxMpDefaultConfigImpl();
        }

        configStorage.setAppId(config.getAppId());
        configStorage.setSecret(config.getWxmpAppsecret());
        configStorage.setToken(config.getWxmpToken());
        configStorage.setAesKey(config.getWxmpAeskey());

        return configStorage;
    }

    private String getLocalIpAddress() {
        try {
            // 获取本机 IP 地址
            InetAddress localHost = InetAddress.getLocalHost();
            return localHost.getHostAddress();
        } catch (UnknownHostException e) {
            // 如果获取 IP 失败，返回未知标识
            log.warn("Failed to get local IP address", e);
            return "Unknown IP";
        }
    }
}
