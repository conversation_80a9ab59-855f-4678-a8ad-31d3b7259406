package com.jxw.shufang.wxmp.dubbo;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.wxmp.api.RemoteWxService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpQrcodeService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@DubboService
@RefreshScope
public class RemoteWxImpl implements RemoteWxService {

    private final WxMpQrcodeService wxMpQrcodeService;
    private final WxMpService wxMpService;

    @Value("${wx.mp.feedbackPageUrl}")
    public String feedbackPageUrl;

    @Value("${wx.mp.feedbackTemplateId}")
    public String feedbackTemplateId;

    @Value("${wx.mp.feedbackScene}")
    public String feedbackScene;

    @Value("${wx.mp.feedbackShareTemplateId:7JFo37GdteV86yMEhfBfk732MVxKkcVTayoRI6I7PBA}")
    public String feedbackShareTemplateId;

    @Value("${wx.mp.attendanceTemplateId}")
    private String attendanceTemplateId;

    @Value("${wx.mp.attendancePageUrl}")
    private String attendancePageUrl;

    @Value("${wx.mp.attendanceScene}")
    private String attendanceScene;

    @Value("${wx.mp.studyPlanningPageUrl:}")
    private String studyPlanningPageUrl;

    @Value("${wx.mp.domainName}")
    public String domainName;

    @Override
    public String getBoundQrCode(Long studentId) {
        //生成临时二维码
        WxMpQrCodeTicket wxMpQrCodeTicket = new WxMpQrCodeTicket();
        try {
            wxMpQrCodeTicket = wxMpQrcodeService.qrCodeCreateTmpTicket("bind:" + studentId, 2592000);
        } catch (WxErrorException e) {
            log.error("生成二维码有误 {}", e);
        }
        String codeUrl = wxMpQrCodeTicket.getUrl();
        if (StringUtils.isNotBlank(codeUrl)) {
            return QrCodeUtil.generateAsBase64(codeUrl, new QrConfig(300, 300), ImgUtil.IMAGE_TYPE_PNG);
        } else {
            log.error("获取不到二维码的路径");
            return "";
        }
    }

    @Override
    public String getQrCode() {
        //生成临时二维码
        WxMpQrCodeTicket wxMpQrCodeTicket = new WxMpQrCodeTicket();
        try {
            wxMpQrCodeTicket = wxMpQrcodeService.qrCodeCreateTmpTicket("open",2592000);
        } catch (WxErrorException e) {
            log.error("生成二维码有误 {}", e);
        }
        String codeUrl = wxMpQrCodeTicket.getUrl();
        if (StringUtils.isNotBlank(codeUrl)) {
            return QrCodeUtil.generateAsBase64(codeUrl, new QrConfig(300, 300), ImgUtil.IMAGE_TYPE_PNG);
        } else {
            log.error("获取不到二维码的路径");
            return "";
        }
    }

    @Override
    public String getFeedbackUrl(Long feedbackId) {
        return feedbackPageUrl+"?feedbackRecordId="+feedbackId;
    }

    @Override
    public String getStudyPlanningUrl(Long studyPlanningId) {
        return studyPlanningPageUrl+"?id="+studyPlanningId;
    }

    @Override
    public Boolean sendFeedbackMessage(String openId,Long feedbackId,String title, String content,String color) throws ServiceException {
        if (StringUtils.isEmpty(openId)){
            return true;
        }

        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        wxMpSubscribeMessage.setToUser(openId);
        wxMpSubscribeMessage.setUrl(getFeedbackUrl(feedbackId));
        wxMpSubscribeMessage.setTemplateId(feedbackTemplateId);
        wxMpSubscribeMessage.setScene(feedbackScene);
        wxMpSubscribeMessage.setTitle(title);
        wxMpSubscribeMessage.setContentColor(color);
        wxMpSubscribeMessage.setContentValue(content);
        try {
            wxMpService.getSubscribeMsgService().sendOnce(wxMpSubscribeMessage);
        } catch (WxErrorException e) {
            if (ObjectUtil.equals(e.getError().getErrorCode(),43101)) {
                return true;
//                2024.12 学习反馈不再依赖微信推送模板 改由线下老师推送
//                throw new ServiceException("家长未订阅下一次的消息通知，无法发送反馈订阅消息");
            }
            throw new ServiceException(e.getError().getErrorMsg());
        }
        return true;
    }

    @Override
    public Boolean sendFeedbackShareMessage(String openId, Long feedbackId, Map<String, String> dataMap) throws ServiceException {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        wxMpSubscribeMessage.setToUser(openId);
        wxMpSubscribeMessage.setUrl(getFeedbackUrl(feedbackId));
        wxMpSubscribeMessage.setTemplateId(feedbackShareTemplateId);
        wxMpSubscribeMessage.setDataMap(dataMap);
        try {
            wxMpService.getSubscribeMsgService().send(wxMpSubscribeMessage);
        } catch (WxErrorException e) {
            if (ObjectUtil.equals(e.getError().getErrorCode(),43101)) {
                return true;
//                throw new ServiceException("家长拒绝接受消息，无法发送反馈订阅消息");
            }
            throw new ServiceException(e.getError().getErrorMsg());
        }
        return true;
    }


    @Override
    public Boolean sendAttendanceMessage(String openId,String title, String content, String color) throws ServiceException {
        WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
        wxMpSubscribeMessage.setToUser(openId);
        wxMpSubscribeMessage.setTemplateId(attendanceTemplateId);
        wxMpSubscribeMessage.setScene(attendanceScene);
        wxMpSubscribeMessage.setTitle(title);
        wxMpSubscribeMessage.setUrl(attendancePageUrl);
        wxMpSubscribeMessage.setContentColor(color);
        wxMpSubscribeMessage.setContentValue(content);
        try {
            boolean b = wxMpService.getSubscribeMsgService().sendOnce(wxMpSubscribeMessage);
        } catch (WxErrorException e) {
            if (ObjectUtil.equals(e.getError().getErrorCode(),43101)) {
                throw new ServiceException("家长未订阅下一次的考勤通知，无法发送考勤消息");
            }
            throw new ServiceException(e.getError().getErrorMsg());
        }
        return true;
    }

    @Override
    public String generateMiniProgramUrl(String query) {
        String patternString = "^(https?://)?([^/]+)";
        String s = ReUtil.get(patternString, domainName, 0);
        return s + "/miniprogram?param=" + query;
    }

    @Override
    public String generatePayUrlByPath(String paramStr, String path) {
        String patternString = "^(https?://)?([^/]+)";
        String s = ReUtil.get(patternString, domainName, 0);
        return s.concat("/").concat(path).concat("?")+ paramStr;
    }
}
