package com.jxw.shufang.wxmp.controller;

import lombok.AllArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.web.core.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * wx订阅消息控制器
 *
 * @date 2024/02/05 06:02:09
 */
@AllArgsConstructor
@RestController
@RequestMapping("/wxSubscribeMsg")
public class WxSubscribeMsgController extends BaseController {
    private final WxMpService wxMpService;

    /**
     * 发送订阅消息
     */
    @PostMapping("/subscribeMsg")
    public R<String> subscribeMsg(WxMpSubscribeMessage subscribeMessage) {
        try {
            String msgId = wxMpService.getSubscribeMsgService().send(subscribeMessage);
            return R.ok(msgId);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }
    /**
     * 发送一次性订阅消息
     */
    @PostMapping("/subscribeMsgOnce")
    public R<Void> subscribeMsgOnce(WxMpSubscribeMessage subscribeMessage) {
        try {
            boolean b = wxMpService.getSubscribeMsgService().sendOnce(subscribeMessage);
            return toAjax(b);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }
}
