package com.jxw.shufang.wxmp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.wxmp.domain.bo.SysDeptWxRelatedBo;
import com.jxw.shufang.wxmp.domain.vo.SysDeptWxConfigVo;
import com.jxw.shufang.wxmp.service.ISysDeptWxConfigService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 代理商微信配置
 * 前端访问路由地址为:/wxmp/deptWxConfig
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/deptWxConfig")
public class SysDeptWxConfigController extends BaseController {

    private final ISysDeptWxConfigService sysDeptWxConfigService;

    /**
     * 获取代理商微信配置详细信息
     *
     * @param deptId 主键
     */
    @SaCheckPermission("wxmp:deptWxConfig:related")
    @GetMapping("/getInfo")
    public R<SysDeptWxConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @RequestParam Long deptId,@RequestParam Integer appType) {
        return R.ok(sysDeptWxConfigService.queryById(deptId,appType));
    }

    /**
     * 关联代理商微信配置
     */
    @SaCheckPermission("wxmp:deptWxConfig:related")
    @Log(title = "代理商微信配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> related(@Validated(AddGroup.class) @RequestBody SysDeptWxRelatedBo bo) {
        return toAjax(sysDeptWxConfigService.related(bo));
    }

}
