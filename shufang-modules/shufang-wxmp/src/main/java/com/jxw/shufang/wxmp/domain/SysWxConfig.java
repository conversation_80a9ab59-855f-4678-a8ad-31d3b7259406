package com.jxw.shufang.wxmp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 微信配置对象 sys_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_wx_config")
public class SysWxConfig extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "wx_config_id")
    private Long wxConfigId;

    /**
     * 0公众号 1小程序
     */
    private Integer appType;

    /**
     * AppID
     */
    private String appId;

    /**
     * 名称
     */
    private String appName;

    /**
     * 小程序AppSecret
     */
    private String appSecret;

    /**
     * 公众号类型 0服务号 1订阅号 2企业号
     */
    private Integer wxmpType;

    /**
     * 公众号原始id
     */
    private String wxmpAccount;

    /**
     * 公众号密钥
     */
    private String wxmpAppsecret;

    /**
     * 公众号令牌
     */
    private String wxmpToken;

    /**
     * 公众号消息密钥
     */
    private String wxmpAeskey;


}
