package com.jxw.shufang.wxmp.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.wxmp.domain.SysWxConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 微信配置业务对象 sys_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysWxConfig.class, reverseConvertGenerate = false)
public class SysWxConfigBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long wxConfigId;

    /**
     * 0公众号 1小程序
     */
    @NotNull(message = "appType配置类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer appType;

    /**
     * AppID
     */
    @NotBlank(message = "appId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appId;

    /**
     * 名称
     */
    @NotBlank(message = "appName不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appName;

    /**
     * 小程序AppSecret
     */
    private String appSecret;

    /**
     * 公众号类型 0服务号 1订阅号 2企业号
     */
    private Integer wxmpType;

    /**
     * 公众号原始id
     */
    private String wxmpAccount;

    /**
     * 公众号密钥
     */
    private String wxmpAppsecret;

    /**
     * 公众号令牌
     */
    private String wxmpToken;

    /**
     * 公众号消息密钥
     */
    private String wxmpAeskey;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;


}
