package com.jxw.shufang.wxmp.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理商微信配置业务对象 sys_dept_wx_config
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDeptWxRelatedBo extends BaseEntity {

    /**
     * 部门id
     */
    @NotNull(message = "部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long deptId;

    @NotNull(message = "appType配置类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer appType;

    private Long wxConfigId;



}
