package com.jxw.shufang.wxmp.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.wxmp.domain.SysWxConfig;
import com.jxw.shufang.wxmp.handler.EventHandler;
import com.jxw.shufang.wxmp.handler.LogHandler;
import com.jxw.shufang.wxmp.handler.MenuHandler;
import com.jxw.shufang.wxmp.mapper.SysWxConfigMapper;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpQrcodeService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.stream.Collectors;

import static me.chanjar.weixin.common.api.WxConsts.EventType;
import static me.chanjar.weixin.common.api.WxConsts.XmlMsgType.EVENT;


@RequiredArgsConstructor
@Configuration
public class WxMpConfiguration {

    private final MenuHandler menuHandler;
    private final LogHandler logHandler;
    private final EventHandler eventHandler;
    private final SysWxConfigMapper configMapper;

    @Value("${wx.mp.useRedis: false}")
    private Boolean isUseRedis;

    //微信公众平台
    @Value("${wx.mp.appId}")
    private String appId;
    @Value("${wx.mp.appSecret}")
    private String appSecret;
    @Value("${wx.mp.token}")
    private String token;
    @Value("${wx.mp.aesKey}")
    private String aesKey;

    @Bean
    public WxMpConfigStorage wxMpConfigStorage() {
        WxMpDefaultConfigImpl configStorage = new WxMpDefaultConfigImpl();
        configStorage.setAppId(this.appId);
        configStorage.setSecret(this.appSecret);
        configStorage.setToken(this.token);
        configStorage.setAesKey(this.aesKey);
        return configStorage;
    }

    @Bean
    public WxMpService wxMpService() {
        LambdaQueryWrapper<SysWxConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysWxConfig::getAppType, 0);
        List<SysWxConfig> propertiesList = DataPermissionHelper.ignore(() -> configMapper.selectList(wrapper));

        WxMpService service = new WxMpServiceImpl();
        if (CollectionUtils.isEmpty(propertiesList)) {
            service.setWxMpConfigStorage(wxMpConfigStorage());
            return service;
//            throw new RuntimeException("微信配置不存在！");
        }
        service.setMultiConfigStorages(propertiesList
            .stream().map(a -> {
                WxMpDefaultConfigImpl configStorage;
                if (Boolean.TRUE.equals(isUseRedis)) {
                    RedissonClient bean = SpringUtils.getBean(RedissonClient.class);
                    configStorage = new WxMpRedissonConfigImpl(bean, a.getAppId());
                } else {
                    configStorage = new WxMpDefaultConfigImpl();
                }
                configStorage.setAppId(a.getAppId());
                configStorage.setSecret(a.getWxmpAppsecret());
                configStorage.setToken(a.getWxmpToken());
                configStorage.setAesKey(a.getWxmpAeskey());
                return configStorage;
            }).collect(Collectors.toMap(WxMpDefaultConfigImpl::getAppId, a -> a, (o, n) -> o)));
        return service;
    }

    @Bean
    public WxMpQrcodeService wxMpQrcodeService() {
        WxMpQrcodeService wxMpQrcodeService = wxMpService().getQrcodeService();
        return wxMpQrcodeService;
    }

    @Bean
    public WxMpMessageRouter messageRouter(WxMpService wxMpService) {
        final WxMpMessageRouter newRouter = new WxMpMessageRouter(wxMpService);

        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(logHandler).next();

        // 处理关注事件
        newRouter.rule().async(false).msgType(EVENT).event(EventType.SUBSCRIBE).handler(eventHandler).end();

        // 自定义菜单事件
        newRouter.rule().async(false).msgType(EVENT).event(EventType.CLICK).handler(this.menuHandler).end();

        return newRouter;
    }


}
