package com.jxw.shufang.wxmp.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.wxmp.domain.bo.SysDeptWxConfigBo;
import com.jxw.shufang.wxmp.domain.bo.SysDeptWxRelatedBo;
import com.jxw.shufang.wxmp.domain.vo.SysDeptWxConfigVo;

import java.util.List;

/**
 * 代理商微信配置Service接口
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
public interface ISysDeptWxConfigService {

    /**
     * 查询代理商微信配置
     */
    SysDeptWxConfigVo queryById(Long deptWxConfigId, Integer appType);

    /**
     * 查询代理商微信配置列表
     */
    TableDataInfo<SysDeptWxConfigVo> queryPageList(SysDeptWxConfigBo bo, PageQuery pageQuery);

    /**
     * 查询代理商微信配置列表
     */
    List<SysDeptWxConfigVo> queryList(SysDeptWxConfigBo bo);

    /**
     * 新增代理商微信配置
     */
    Boolean related(SysDeptWxRelatedBo bo);

}
