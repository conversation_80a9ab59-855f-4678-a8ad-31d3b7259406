package com.jxw.shufang.wxmp;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.Import;

/**
 * 系统模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication(scanBasePackages = {"com.jxw.shufang"})
@Import(RocketMQAutoConfiguration.class)  // 手动导入 RocketMQ 自动配置
public class ShufangWxMpApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ShufangWxMpApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  微信服务模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
