package com.jxw.shufang.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.pay.client.PayCommonClient;
import com.jxw.shufang.common.pay.constant.CommonPayConstant;
import com.jxw.shufang.common.pay.domain.dto.request.DeleteMerchantConfigRequest;
import com.jxw.shufang.common.pay.domain.dto.request.MerchantConfigDTO;
import com.jxw.shufang.common.pay.domain.dto.request.UpdateUnionpayConfigBO;
import com.jxw.shufang.common.pay.domain.dto.response.ApiResp;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.domain.bo.*;
import com.jxw.shufang.system.domain.dto.UnionPayParamConfigDTO;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigBranchVo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.system.domain.PayMerchantConfig;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigVo;
import com.jxw.shufang.system.mapper.PayMerchantConfigMapper;
import com.jxw.shufang.system.service.IPayMerchantConfigService;
import com.jxw.shufang.system.service.ISysDeptService;

/**
 * @author: cyj
 * @date: 2025/5/6
 */
@Service
@Slf4j
public class PayMerchantConfigServiceImpl implements IPayMerchantConfigService, BaseService {
    @Resource
    private  PayMerchantConfigMapper baseMapper;
    @Resource
    private  ISysDeptService sysDeptService;
    @Resource
    private  PayCommonClient payCommonClient;
    @DubboReference
    private  RemoteBranchService remoteBranchService;

    @Override
    public PayMerchantConfig queryConfigById(Long payMerchantConfigId) {
        LambdaQueryWrapper<PayMerchantConfig> queryWrapper = getQueryWrapper()
            .eq(PayMerchantConfig::getIsDelete, 0)
            .eq(PayMerchantConfig::getId, payMerchantConfigId);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public IPage<PayMerchantConfigVo> listConfig(QueryMerchantBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PayMerchantConfig> wrapper = buildWrapper(bo);
        IPage<PayMerchantConfig> configPage = baseMapper.selectPage(pageQuery.build(), wrapper);
        List<PayMerchantConfig> records = configPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        }

        List<PayMerchantConfigVo> merchantConfigs = records.stream()
            .map(PayMerchantConfigServiceImpl::convertToPayMerchantConfigVo)
            .toList();

        // 获取配置部门数量
        this.setMerchantDeptNum(merchantConfigs);


        Page page = new Page();
        page.setRecords(merchantConfigs);
        page.setSize(configPage.getSize());
        page.setTotal(configPage.getTotal());
        page.setPages(configPage.getPages());
        return page;
    }

    private void setMerchantDeptNum(List<PayMerchantConfigVo> merchantConfigs) {
        Set<Long> ids = merchantConfigs.stream()
            .map(PayMerchantConfigVo::getId)
            .collect(Collectors.toSet());
        Map<Long, Integer> countMap = sysDeptService.countPayMerchant(ids);
        merchantConfigs.forEach(config -> config.setDeptNum(countMap.getOrDefault(config.getId(), 0)));
    }

    private static PayMerchantConfigVo convertToPayMerchantConfigVo(PayMerchantConfig record) {
        PayMerchantConfigVo configVO = new PayMerchantConfigVo();
        configVO.setId(record.getId());
        configVO.setMerchantName(record.getMerchantName());
        configVO.setChannelCode(record.getChannelCode());
        configVO.setEnable(record.getEnable());
        configVO.setAppId(record.getAppId());
        configVO.setAppName(record.getAppName());
        configVO.setPayType(record.getPayType());
        configVO.setPayCode(record.getPayCode());
        configVO.setWayCode(record.getWayCode());
        configVO.setDefaultMerchant(record.getDefaultMerchant());
        String configParamJson = record.getConfigParamJson();
        if (StrUtil.isNotEmpty(configParamJson)) {
            configVO.setUnionPayParamConfig(JsonUtils.parseObject(configParamJson, UnionPayParamConfigDTO.class));
        }
        return configVO;
    }

    @Override
    public PayMerchantConfigVo queryVoById(Long payMerchantConfigId) {
        PayMerchantConfig payMerchantConfig =
            baseMapper.selectOne(getQueryWrapper().eq(PayMerchantConfig::getId, payMerchantConfigId).eq(PayMerchantConfig::getIsDelete, 0));
        if (null == payMerchantConfig) {
            return null;
        }

        PayMerchantConfigVo payMerchantConfigVo = MapstructUtils.convert(payMerchantConfig, PayMerchantConfigVo.class);
        if (null == payMerchantConfigVo) {
            throw new ServiceException("商户信息convert失败");
        }

        this.setUnionPayConfig(payMerchantConfig.getConfigParamJson(), payMerchantConfigVo);
        this.setDeptList(payMerchantConfigId, payMerchantConfigVo);

        return payMerchantConfigVo;
    }

    private void setUnionPayConfig(String configParamJson, PayMerchantConfigVo payMerchantConfigVo) {
        UnionPayParamConfigDTO paramConfigDTO = JsonUtils.parseObject(configParamJson, UnionPayParamConfigDTO.class);
        payMerchantConfigVo.setUnionPayParamConfig(paramConfigDTO);
    }

    private void setDeptList(Long payMerchantConfigId, PayMerchantConfigVo payMerchantConfigVo) {
        List<SysDeptVo> deptListByPayMerchantConfigId = sysDeptService.getDeptListByPayMerchantConfigId(payMerchantConfigId);
        payMerchantConfigVo.setDeptList(deptListByPayMerchantConfigId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveConfig(SaveMerchantConfigBo bo, Long deptId, Long userId) {
        PayMerchantConfig payMerchantConfig = this.queryConfigByAppId(bo.getAppId().trim());
        if (null != payMerchantConfig) {
            throw new ServiceException("唯一标识重复");
        }

        PayMerchantConfig saveConfig = this.savePayMerchantConfig(bo, deptId, userId);

        ApiResp<String> remoteSaveConfig = payCommonClient.saveConfig(new MerchantConfigDTO(bo.getAppId(), saveConfig.getConfigParamJson()));
        if (!remoteSaveConfig.isSuccess()) {
            log.error("通用支付模块新增配置失败，错误信息：{}", remoteSaveConfig.getMsg());
            throw new ServiceException("商户支付配置保存失败");
        }

        sysDeptService.flushPayMerchantId(bo.getDeptIdList(), saveConfig.getId());
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateConfig(UpdateMerchantConfigBo mergeConfigBo) {
        Long mergeConfigBoId = mergeConfigBo.getId();

        PayMerchantConfig payMerchantConfig = this.queryConfigById(mergeConfigBoId);
        if (null == payMerchantConfig) {
            throw new ServiceException("商户支付配置不存在");
        }

        UnionPayParamConfigDTO newConfigParam = this.buildNewConfigParam(mergeConfigBo, payMerchantConfig.getConfigParamJson());
        this.updateConfigById(mergeConfigBo, payMerchantConfig, newConfigParam);
        this.updateCommonPayConfig(payMerchantConfig, newConfigParam);
        this.updateDeptConfig(mergeConfigBo, mergeConfigBoId);

        return true;
    }

    private void updateDeptConfig(UpdateMerchantConfigBo mergeConfigBo, Long mergeConfigBoId) {
        if (CollectionUtils.isNotEmpty(mergeConfigBo.getDeptIdList())) {
            // 全部清除
            sysDeptService.clearMerchantByPayMerchantConfigId(mergeConfigBoId);
            sysDeptService.flushPayMerchantId(mergeConfigBo.getDeptIdList(), mergeConfigBoId);
        }
    }

    private void updateCommonPayConfig(PayMerchantConfig payMerchantConfig, UnionPayParamConfigDTO newConfigParam) {
        UnionPayParamConfigDTO paramConfigDTO = JsonUtils.parseObject(payMerchantConfig.getConfigParamJson(), UnionPayParamConfigDTO.class);
        if (null != paramConfigDTO && paramConfigDTO.equals(newConfigParam)) {
            log.info("通用支付模块配置未发生变更");
            return;
        }
        payCommonClient.updateConfig(new UpdateUnionpayConfigBO(payMerchantConfig.getAppId(), JSONUtil.toJsonStr(newConfigParam)));
    }

    private void updateConfigById(UpdateMerchantConfigBo mergeConfigBo,PayMerchantConfig merchantConfig, UnionPayParamConfigDTO newParamConfig) {
        UnionPayParamConfigDTO oldParamConfig = JsonUtils.parseObject(merchantConfig.getConfigParamJson(), UnionPayParamConfigDTO.class);
        PayMerchantConfig updateConfig = this.buildMerchantConfigByUpdate(mergeConfigBo, newParamConfig, oldParamConfig);

        LambdaQueryWrapper<PayMerchantConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayMerchantConfig::getId, merchantConfig.getId());
        wrapper.eq(PayMerchantConfig::getIsDelete, 0);
        if (baseMapper.update(updateConfig, wrapper) <= 0) {
            throw new ServiceException("商户支付配置更新失败");
        }
    }

    private PayMerchantConfig buildMerchantConfigByUpdate(UpdateMerchantConfigBo mergeConfigBo,
                                                          UnionPayParamConfigDTO newParamConfigDTO,
                                                          UnionPayParamConfigDTO oldParamConfigDTO) {
        PayMerchantConfig updateConfig = new PayMerchantConfig();
        if (null == oldParamConfigDTO || !oldParamConfigDTO.equals(newParamConfigDTO)) {
            updateConfig.setConfigParamJson(JsonUtils.toJsonString(newParamConfigDTO));
        }
        if (StrUtil.isNotEmpty(mergeConfigBo.getMerchantName())) {
            updateConfig.setMerchantName(mergeConfigBo.getMerchantName());
        }
        if (null != mergeConfigBo.getEnable()) {
            updateConfig.setEnable(mergeConfigBo.getEnable());
        }
        if (null != mergeConfigBo.getChannelCode()) {
            updateConfig.setChannelCode(mergeConfigBo.getChannelCode());
        }
        return updateConfig;
    }

    private UnionPayParamConfigDTO buildNewConfigParam(UpdateMerchantConfigBo bo, String oldConfigParamJson) {
        UnionPayParamConfigDTO oldConfigParam = JsonUtils.parseObject(oldConfigParamJson, UnionPayParamConfigDTO.class);
        UnionPayParamConfigDTO newConfigParam = new UnionPayParamConfigDTO();
        if (null == oldConfigParam) {
            newConfigParam.setUnionAppId(bo.getUnionAppId());
            newConfigParam.setUnionKey(bo.getUnionKey());
            newConfigParam.setMidNumber(bo.getMidNumber());
            newConfigParam.setTidNumber(bo.getTidNumber());
            newConfigParam.setSourceNumber(bo.getSourceNumber());
            newConfigParam.setOnlineSecretKey(bo.getOnlineSecretKey());
        } else {
            newConfigParam.setUnionAppId(StrUtil.isEmpty(bo.getUnionAppId()) ? oldConfigParam.getUnionAppId() : bo.getUnionAppId());
            newConfigParam.setUnionKey(StrUtil.isEmpty(bo.getUnionKey()) ? oldConfigParam.getUnionKey() : bo.getUnionKey());
            newConfigParam.setMidNumber(StrUtil.isEmpty(bo.getMidNumber()) ? oldConfigParam.getMidNumber() : bo.getMidNumber());
            newConfigParam.setTidNumber(StrUtil.isEmpty(bo.getTidNumber()) ? oldConfigParam.getTidNumber() : bo.getTidNumber());
            newConfigParam.setSourceNumber(StrUtil.isEmpty(bo.getSourceNumber()) ? oldConfigParam.getSourceNumber() : bo.getSourceNumber());
            newConfigParam.setOnlineSecretKey(StrUtil.isEmpty(bo.getOnlineSecretKey()) ? oldConfigParam.getOnlineSecretKey() : bo.getOnlineSecretKey());
        }
        return newConfigParam;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(Long id) {
        if (id == null) {
            throw new ServiceException("请选择配置后操作。");
        }
        PayMerchantConfigVo payMerchantConfigVo = queryVoById(id);
        if (payMerchantConfigVo == null) {
            throw new ServiceException("商户支付配置不存在");
        }
        boolean defaultMerchant = null != payMerchantConfigVo.getDefaultMerchant() && payMerchantConfigVo.getDefaultMerchant();
        if(defaultMerchant){
            throw new ServiceException("默认商户不能删除");
        }

        this.updateConfig(id);
        this.updateCommonPayConfig(payMerchantConfigVo);
        this.updateDeptConfig(id);

        return true;
    }

    private void updateDeptConfig(Long id) {
        sysDeptService.cancelDeptMerchantConfig(id);
    }

    private void updateCommonPayConfig(PayMerchantConfigVo payMerchantConfigVo) {
        ApiResp<String> voidApiResp = payCommonClient.deleteConfig(new DeleteMerchantConfigRequest(payMerchantConfigVo.getAppId()));
        if (!voidApiResp.isSuccess()) {
            log.error("通用支付模块修改配置失败，错误信息：{}", voidApiResp.getMsg());
            throw new ServiceException("商户支付配置删除失败");
        }
    }

    private void updateConfig(Long id) {
        boolean updateSuccess = baseMapper.update(Wrappers.lambdaUpdate(PayMerchantConfig.class)
            .set(PayMerchantConfig::getIsDelete, true).eq(PayMerchantConfig::getId, id)) > 0;
        if (!updateSuccess) {
            throw new ServiceException("支付记录删除失败");
        }
    }

    @Override
    public TableDataInfo<PayMerchantConfigBranchVo> listConfigBranch(Long merchantConfigId, PageQuery pageQuery) {
        PayMerchantConfigVo payMerchantConfigVo = queryVoById(merchantConfigId);
        if (payMerchantConfigVo == null) {
            return TableDataInfo.build(new ArrayList<>(1));
        }
        // 查询配置的部门分页信息，最终分页信息以部门分页为准
        SysDeptBo sysDeptBo = new SysDeptBo();
        sysDeptBo.setPayMerchantConfigId(payMerchantConfigVo.getId());
        sysDeptBo.setIsStore(Boolean.TRUE);
        IPage<SysDeptVo> sysDeptVoIPage = sysDeptService.selectDeptPage(sysDeptBo, pageQuery);
        if (CollectionUtils.isEmpty(sysDeptVoIPage.getRecords())) {
            return TableDataInfo.build(new ArrayList<>(1), sysDeptVoIPage.getTotal());
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setCreateDeptIds(sysDeptVoIPage.getRecords().stream().filter(SysDeptVo::getIsStore)
            .map(SysDeptVo::getDeptId).collect(Collectors.toSet()));
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo, true);
        List<PayMerchantConfigBranchVo> returnList = new ArrayList<>();
        remoteBranchVos.forEach(remoteBranchVo -> {
            returnList
                .add(new PayMerchantConfigBranchVo(remoteBranchVo.getBranchName(), remoteBranchVo.getBranchStatus(),
                    remoteBranchVo.getBranchAuthTypeName(), remoteBranchVo.getCreateTime()));
        });
        return TableDataInfo.build(returnList, sysDeptVoIPage.getTotal());
    }

    @Override
    public PayMerchantConfig getConfigByDeptId(Long deptId) {
        SysDeptVo sysDeptVo = sysDeptService.selectDeptById(deptId);
        if (sysDeptVo == null || sysDeptVo.getPayMerchantConfigId() == null) {
            return null;
        }
        return queryConfigById(sysDeptVo.getPayMerchantConfigId());
    }

    private PayMerchantConfig savePayMerchantConfig(SaveMerchantConfigBo bo, Long deptId, Long userId) {
        PayMerchantConfig saveConfig = new PayMerchantConfig();
        saveConfig.setMerchantName(bo.getMerchantName());
        saveConfig.setEnable(bo.getEnable());
        saveConfig.setChannelCode(bo.getChannelCode());
        saveConfig.setAppId(bo.getAppId());
        saveConfig.setConfigParamJson(this.buildConfigJson(bo));
        saveConfig.setCreateDept(deptId);
        saveConfig.setCreateBy(userId);
        saveConfig.setUpdateBy(userId);
        saveConfig.setAppName(CommonPayConstant.DEFAULT_APP_NAME);
        saveConfig.setPayType(CommonPayConstant.DEFAULT_PAY_TYPE);
        saveConfig.setPayCode(CommonPayConstant.DEFAULT_PAY_CODE);
        saveConfig.setWayCode(CommonPayConstant.DEFAULT_WAY_CODE);
        if (baseMapper.insert(saveConfig) <= 0) {
            throw new ServiceException("商户支付配置保存失败");
        }
        return saveConfig;
    }

    private String buildConfigJson(SaveMerchantConfigBo bo) {
        UnionPayParamConfigDTO unionPayParamConfig = new UnionPayParamConfigDTO();
        unionPayParamConfig.setUnionAppId(bo.getUnionAppId());
        unionPayParamConfig.setUnionKey(bo.getUnionKey());
        unionPayParamConfig.setMidNumber(bo.getMidNumber());
        unionPayParamConfig.setTidNumber(bo.getTidNumber());
        unionPayParamConfig.setSourceNumber(bo.getSourceNumber());
        unionPayParamConfig.setOnlineSecretKey(bo.getOnlineSecretKey());
        return JSONUtil.toJsonStr(unionPayParamConfig);
    }

    /**
     * 根据appid获取配置
     *
     * @param appId
     * @return
     */
    private PayMerchantConfig queryConfigByAppId(String appId) {
        return baseMapper.selectOne(getQueryWrapper()
            .eq(PayMerchantConfig::getAppId, appId)
            .eq(PayMerchantConfig::getIsDelete, 0)
            .last("limit 1 "));
    }


    private LambdaQueryWrapper<PayMerchantConfig> getQueryWrapper() {
        return Wrappers.lambdaQuery(PayMerchantConfig.class).eq(PayMerchantConfig::getIsDelete, false);
    }

    /**
     * 获取wrapper
     *
     * @param bo
     * @return
     */
    private LambdaQueryWrapper<PayMerchantConfig> buildWrapper(QueryMerchantBo bo) {
        return getQueryWrapper().like(StringUtils.isNotEmpty(bo.getMerchantName()), PayMerchantConfig::getMerchantName,
                bo.getMerchantName())
            .like(StringUtils.isNotEmpty(bo.getAppId()), PayMerchantConfig::getAppId, bo.getAppId());
    }
}
