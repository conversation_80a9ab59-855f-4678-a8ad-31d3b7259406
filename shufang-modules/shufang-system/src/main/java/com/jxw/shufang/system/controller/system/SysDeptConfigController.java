package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.SysDeptConfigBo;
import com.jxw.shufang.system.domain.vo.SysDeptConfigVo;
import com.jxw.shufang.system.service.ISysDeptConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 部门信息
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/deptConfig")
public class SysDeptConfigController extends BaseController {

    private final ISysDeptConfigService deptService;


    /**
     * 根据部门编号获取详细信息
     *
     * @param deptId 部门ID
     */
    @SaCheckPermission("system:dept:config:query")
    @GetMapping(value = "/{deptId}")
    public R<SysDeptConfigVo> getInfo(@PathVariable Long deptId) {
        return R.ok(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @SaCheckPermission("system:dept:config:edit")
    @Log(title = "部门管理配置", businessType = BusinessType.UPDATE)
    @PostMapping
    public R<Boolean> saveOrUpdate(@Validated @RequestBody SysDeptConfigBo bo) {
        return R.ok(deptService.saveOrUpdate(bo));
    }


}
