package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/6/5 16:16
 * @Version 1
 * @Description
 */
@Data
public class UpdateMerchantConfigBo {
    @NotEmpty(message = "id不能为空")
    private Long id;

    /**
     * 商户名称
     */
    @Size(max = 17, message = "商户名称长度不能超过17个字符", groups = {AddGroup.class, EditGroup.class})
    private String merchantName;

    /**
     * 支付渠道编码
     */
    private String channelCode;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 商户部门id
     */
    private Set<Long> deptIdList;
    /**
     * 银联appid
     */
    @Size(max = 50, message = "银联平台appId不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String unionAppId;
    /**
     * 银联密钥
     */
    @Size(max = 50, message = "银联密钥key不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String unionKey;
    /**
     * 银联商户号
     */
    @Size(max = 50, message = "银联商户号mid不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String midNumber;
    /**
     * 银联终端号
     */
    @Size(max = 50, message = "银联终端号不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String tidNumber;
    /**
     * 银联来源编号
     */
    @Size(max = 5, message = "银联来源编号不能长度不能超过5个字符", groups = {AddGroup.class, EditGroup.class})
    private String sourceNumber;
    /**
     * 银联网付密钥
     */
    @Size(max = 50, message = "银联平台网付密钥不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String onlineSecretKey;
}
