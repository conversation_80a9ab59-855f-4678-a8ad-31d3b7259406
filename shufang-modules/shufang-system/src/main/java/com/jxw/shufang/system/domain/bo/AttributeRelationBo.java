package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.AttributeRelation;

import java.util.List;

/**
 * 属性关系业务对象 attribute_relation
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AttributeRelation.class, reverseConvertGenerate = false)
public class AttributeRelationBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long attributeId;

    /**
     * 类型（直接填表名）
     */
    @NotBlank(message = "类型（直接填表名）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String type;

    /**
     * 类型ID
     */
    @NotNull(message = "类型ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long typeId;

    /**
     * 值（|||隔开）
     */
    @NotBlank(message = "值（|||隔开）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String value;

    /**
     * 属性ID集合
     */
    private List<Long> attributeIdList;


    /**
     * ID集合
     */
    private List<Long> typeIdList;


}
