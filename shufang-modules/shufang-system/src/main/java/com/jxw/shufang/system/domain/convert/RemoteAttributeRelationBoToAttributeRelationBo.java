package com.jxw.shufang.system.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteAttributeRelationBoToAttributeRelationBo extends BaseMapper<RemoteAttributeRelationBo, AttributeRelationBo> {

}
