package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.api.model.RemoteSysUserSignBo;
import com.jxw.shufang.system.domain.SysUserSign;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMappers({
    @AutoMapper(target = SysUserSign.class, reverseConvertGenerate = false),
    @AutoMapper(target = RemoteSysUserSignBo.class)
})
public class SysUserSignBo extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    @NotNull(message = "用户id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 类型
     */
    @NotNull(message = "类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 版本
     */
    @NotNull(message = "版本不能为空", groups = {AddGroup.class, EditGroup.class})
    private String version;

    /**
     * 签署状态 0为否 1为是
     */
    private Integer signStatus;

}
