package com.jxw.shufang.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.domain.SysNoticeUser;
import com.jxw.shufang.system.domain.vo.SysNoticeUserVo;

import java.util.List;

/**
 * noticeUserMapper接口
 *
 *
 * @date 2024-06-30
 */
@Mapper
public interface SysNoticeUserMapper extends BaseMapperPlus<SysNoticeUser, SysNoticeUserVo> {

    Long getUnreadCount(Long userId);

    List<SysNoticeUserVo> selectNoticeUserVoList(@Param(Constants.WRAPPER) QueryWrapper<SysNoticeUser> lqw);

    int saveBatch(@Param("list") List<SysNoticeUser> sysNoticeUserList);

}
