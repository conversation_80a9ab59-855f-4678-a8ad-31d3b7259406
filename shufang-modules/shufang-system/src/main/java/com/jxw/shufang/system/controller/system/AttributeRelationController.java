package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttributeRelationVo;
import com.jxw.shufang.system.service.IAttributeRelationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性关系
 * 前端访问路由地址为:/system/relation
 *
 * @date 2024-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/relation")
public class AttributeRelationController extends BaseController {

    private final IAttributeRelationService attributeRelationService;

    /**
     * 查询属性关系列表
     */
    @SaCheckPermission("system:relation:list")
    @GetMapping("/list")
    public TableDataInfo<AttributeRelationVo> list(AttributeRelationBo bo, PageQuery pageQuery) {
        return attributeRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出属性关系列表
     */
    @SaCheckPermission("system:relation:export")
    @Log(title = "属性关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttributeRelationBo bo, HttpServletResponse response) {
        List<AttributeRelationVo> list = attributeRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "属性关系", AttributeRelationVo.class, response);
    }

    /**
     * 获取属性关系详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:relation:query")
    @GetMapping("/{id}")
    public R<AttributeRelationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(attributeRelationService.queryById(id));
    }

    /**
     * 新增属性关系
     */
    @SaCheckPermission("system:relation:add")
    @Log(title = "属性关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttributeRelationBo bo) {
        return toAjax(attributeRelationService.insertByBo(bo));
    }

    /**
     * 修改属性关系
     */
    @SaCheckPermission("system:relation:edit")
    @Log(title = "属性关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttributeRelationBo bo) {
        return toAjax(attributeRelationService.updateByBo(bo));
    }

    /**
     * 删除属性关系
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:relation:remove")
    @Log(title = "属性关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(attributeRelationService.deleteWithValidByIds(List.of(ids), true));
    }
}
