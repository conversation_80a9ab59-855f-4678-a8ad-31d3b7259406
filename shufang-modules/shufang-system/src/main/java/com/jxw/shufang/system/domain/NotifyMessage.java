package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
@TableName("notify_message")
public class NotifyMessage {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 模板id
     */
    private Integer template_id;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 拼接参数
     */
    private String params;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 通知类型 1-站内信
     */
    private Integer noticeType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

}
