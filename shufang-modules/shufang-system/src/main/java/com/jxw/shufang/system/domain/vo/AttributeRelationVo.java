package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.system.domain.AttributeRelation;

import java.io.Serial;
import java.io.Serializable;


/**
 * 属性关系视图对象 attribute_relation
 *
 * @date 2024-03-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AttributeRelation.class)
public class AttributeRelationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 属性ID
     */
    @ExcelProperty(value = "属性ID")
    private Long attributeId;

    /**
     * 类型（直接填表名）
     */
    @ExcelProperty(value = "类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "直=接填表名")
    private String type;

    /**
     * 类型ID
     */
    @ExcelProperty(value = "类型ID")
    private Long typeId;

    /**
     * 值（|||隔开）
     */
    @ExcelProperty(value = "值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "|=||隔开")
    private String value;


    private AttributeVo attributeVo;


}
