package com.jxw.shufang.system.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.domain.bo.AttributeGroupingBo;
import com.jxw.shufang.system.domain.vo.AttributeGroupingVo;

import java.util.Collection;
import java.util.List;

/**
 * 属性分组Service接口
 *
 *
 * @date 2024-03-26
 */
public interface IAttributeGroupingService {

    /**
     * 查询属性分组
     */
    AttributeGroupingVo queryById(Long attributeGroupingId);

    /**
     * 查询属性分组列表
     */
    TableDataInfo<AttributeGroupingVo> queryPageList(AttributeGroupingBo bo, PageQuery pageQuery);

    /**
     * 查询属性分组列表
     */
    List<AttributeGroupingVo> queryList(AttributeGroupingBo bo);

    /**
     * 新增属性分组
     */
    Boolean insertByBo(AttributeGroupingBo bo);

    /**
     * 修改属性分组
     */
    Boolean updateByBo(AttributeGroupingBo bo);

    /**
     * 校验并批量删除属性分组信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<AttributeGroupingVo> getByType(String type);

    void cleanAllCache();

    List<CourseAttributeDetailDTO> batchQueryAttributeValueByTypeId(List<Long> courseId, String type);
}
