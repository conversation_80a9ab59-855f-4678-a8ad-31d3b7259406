package com.jxw.shufang.system.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


/**
 * 系统角色数据转换器
 *
 * @date 2024/02/18 11:30:43
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysDeptVoConvert extends BaseMapper<SysDeptVo,RemoteDeptVo> {
}




