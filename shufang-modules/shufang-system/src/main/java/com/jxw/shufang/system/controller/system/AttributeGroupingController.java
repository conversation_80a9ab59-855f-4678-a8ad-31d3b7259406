package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.AttributeGroupingBo;
import com.jxw.shufang.system.domain.vo.AttributeGroupingVo;
import com.jxw.shufang.system.service.IAttributeGroupingService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性分组
 * 前端访问路由地址为:/system/grouping
 *
 * @date 2024-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/grouping")
public class AttributeGroupingController extends BaseController {

    private final IAttributeGroupingService attributeGroupingService;

    /**
     * 查询属性分组列表
     */
    @SaCheckPermission("system:grouping:list")
    @GetMapping("/list")
    public TableDataInfo<AttributeGroupingVo> list(AttributeGroupingBo bo, PageQuery pageQuery) {
        return attributeGroupingService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出属性分组列表
     */
    @SaCheckPermission("system:grouping:export")
    @Log(title = "属性分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttributeGroupingBo bo, HttpServletResponse response) {
        List<AttributeGroupingVo> list = attributeGroupingService.queryList(bo);
        ExcelUtil.exportExcel(list, "属性分组", AttributeGroupingVo.class, response);
    }

    /**
     * 获取属性分组详细信息
     *
     * @param attributeGroupingId 主键
     */
    @SaCheckPermission("system:grouping:query")
    @GetMapping("/{attributeGroupingId}")
    public R<AttributeGroupingVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attributeGroupingId) {
        return R.ok(attributeGroupingService.queryById(attributeGroupingId));
    }

    /**
     * 新增属性分组
     */
    @SaCheckPermission("system:grouping:add")
    @Log(title = "属性分组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttributeGroupingBo bo) {
        return toAjax(attributeGroupingService.insertByBo(bo));
    }

    /**
     * 修改属性分组
     */
    @SaCheckPermission("system:grouping:edit")
    @Log(title = "属性分组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttributeGroupingBo bo) {
        return toAjax(attributeGroupingService.updateByBo(bo));
    }

    /**
     * 删除属性分组
     *
     * @param attributeGroupingIds 主键串
     */
    @SaCheckPermission("system:grouping:remove")
    @Log(title = "属性分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attributeGroupingIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attributeGroupingIds) {
        return toAjax(attributeGroupingService.deleteWithValidByIds(List.of(attributeGroupingIds), true));
    }
}
