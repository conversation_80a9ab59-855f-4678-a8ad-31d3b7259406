package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * sys_user_sign_record
 * 用户签署协约通用接口
 */

@Data
@NoArgsConstructor
@TableName("sys_user_sign_record")
@EqualsAndHashCode(callSuper=false)
public class SysUserSign extends BaseEntity {

    /**
     * 用户ID
     */
    private Long userId;


    /**
     * 自增id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 租户ID
     */
    private Integer type;

    /**
     * 用户账号
     */
    private String version;

    /**
     * 用户昵称
     */
    private Integer signStatus;

}
