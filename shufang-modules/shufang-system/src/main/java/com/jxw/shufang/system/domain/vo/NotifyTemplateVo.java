package com.jxw.shufang.system.domain.vo;

import com.jxw.shufang.system.domain.NotifyTemplate;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.util.List;

@Data
@AutoMapper(target = NotifyTemplate.class)
public class NotifyTemplateVo {

    private Integer id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模版编码
     */
    private String code;

    /**
     * 模版内容
     */
    private String content;

    /**
     * 模板类型 1-站内信
     */
    private Integer type;

    /**
     * 参数数组
     */
    private List<String> params;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

}
