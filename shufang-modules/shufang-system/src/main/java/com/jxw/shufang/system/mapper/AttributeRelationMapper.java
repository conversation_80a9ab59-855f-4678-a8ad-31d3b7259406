package com.jxw.shufang.system.mapper;

import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.domain.AttributeRelation;
import com.jxw.shufang.system.domain.vo.AttributeRelationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 属性关系Mapper接口
 *
 *
 * @date 2024-03-26
 */
public interface AttributeRelationMapper extends BaseMapperPlus<AttributeRelation, AttributeRelationVo> {

    List<CourseAttributeDetailDTO> queryCourseAttributeDetailByIdAndTypeId(@Param("typeIdList") List<Long> courseIds, @Param("typeAttributeIdList") List<Long> typeAttributeIds, @Param("type") String type);

}
