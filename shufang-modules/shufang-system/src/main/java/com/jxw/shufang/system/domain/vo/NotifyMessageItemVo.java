package com.jxw.shufang.system.domain.vo;

import com.jxw.shufang.system.domain.NotifyMessageItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

@Data
@AutoMapper(target = NotifyMessageItem.class)
public class NotifyMessageItemVo {

    private Integer id;

    /**
     * 消息id
     */
    private Integer messageId;

    /**
     * 发送人
     */
    private Long fromUserId;

    /**
     * 接收人
     */
    private Long toUserId;

    /**
     * 阅读时间
     */
    private Long readTime;

}
