package com.jxw.shufang.system.service;

import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.NotifyMessage;
import com.jxw.shufang.system.domain.NotifyMessageItem;
import com.jxw.shufang.system.domain.bo.NoticeMessageBo;
import com.jxw.shufang.system.domain.vo.NoticeBizTypeVo;
import com.jxw.shufang.system.domain.vo.NoticeMessageVo;
import com.jxw.shufang.system.domain.vo.NotifyTemplateVo;

import java.util.List;

public interface INotifyMessageService {

    NotifyTemplateVo getNotifyTemplateVo(String templateCode);

    int saveNotifyMessage(NotifyMessage notifyMessage);

    int saveNotifyMessageItem(NotifyMessageItem notifyMessageItem);

    TableDataInfo<NoticeMessageVo> queryNoticeMessage(NoticeMessageBo messageBo);

    List<NoticeBizTypeVo> getBizTypeList();

    boolean updateReadStatus(List<Integer> messageIds, Long toUserId);

    TableDataInfo<NoticeMessageVo> queryTodayNoticeMessage(Long toUserId, Integer pageNum, Integer pageSize);

    long getUnreadMessageCount(Long userId);

}
