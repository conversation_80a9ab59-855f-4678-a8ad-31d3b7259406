package com.jxw.shufang.system.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteMenuService;
import com.jxw.shufang.system.api.domain.vo.RemoteMenuVo;
import com.jxw.shufang.system.domain.vo.SysMenuVo;
import com.jxw.shufang.system.service.ISysMenuService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteMenuServiceImpl implements RemoteMenuService {

    private final ISysMenuService menuService;

    @Override
    public List<RemoteMenuVo> getUserMenuList(Long userId) {
        List<SysMenuVo> sysMenuVos = menuService.selectMenuList(userId);
        return MapstructUtils.convert(sysMenuVos, RemoteMenuVo.class);
    }

}
