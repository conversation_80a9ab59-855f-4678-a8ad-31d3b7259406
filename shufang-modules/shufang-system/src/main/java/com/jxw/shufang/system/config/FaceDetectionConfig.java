package com.jxw.shufang.system.config;

import com.jxw.shufang.tencentFace.FaceDetectionClient;
import com.jxw.shufang.tencentFace.config.FaceDetectionProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: cyj
 * @date: 2025/4/21
 */
@Configuration
@EnableConfigurationProperties(FaceDetectionProperties.class)
public class FaceDetectionConfig {

    @Bean("faceDetectionClient")
    public FaceDetectionClient getClient(FaceDetectionProperties faceDetectionProperties) {
        return new FaceDetectionClient(faceDetectionProperties);
    }

}
