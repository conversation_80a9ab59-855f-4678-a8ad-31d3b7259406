package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.extresource.api.RemoteExtResourceService;
import com.jxw.shufang.extresource.api.domain.vo.RemoteAppInfoVo;
import com.jxw.shufang.system.domain.vo.AppVersionV2Vo;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.domain.AppVersion;
import com.jxw.shufang.system.domain.bo.AppVersionBo;
import com.jxw.shufang.system.domain.vo.AppVersionVo;
import com.jxw.shufang.system.mapper.AppVersionMapper;
import com.jxw.shufang.system.service.IAppVersionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * APP版本管理Service业务层处理
 *
 * @date 2024-07-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppVersionServiceImpl implements IAppVersionService, BaseService {

    private final AppVersionMapper baseMapper;

    @DubboReference
    private RemoteExtResourceService remoteExtResourceService;

    /**
     * 查询APP版本管理
     */
    @Override
    public AppVersionVo queryById(Long appVersionId) {
        return baseMapper.selectVoById(appVersionId);
    }

    /**
     * 查询APP版本管理列表
     */
    @Override
    public TableDataInfo<AppVersionVo> queryPageList(AppVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppVersion> lqw = buildQueryWrapper(bo);
        Page<AppVersionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询APP版本管理列表
     */
    @Override
    public List<AppVersionVo> queryList(AppVersionBo bo) {
        LambdaQueryWrapper<AppVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    private LambdaQueryWrapper<AppVersion> buildQueryWrapper(AppVersionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppVersion> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAppOssId() != null, AppVersion::getAppOssId, bo.getAppOssId());
        lqw.eq(StringUtils.isNotBlank(bo.getAppVersion()), AppVersion::getAppVersion, bo.getAppVersion());
        lqw.eq(bo.getAppVersionCode() != null, AppVersion::getAppVersionCode, bo.getAppVersionCode());
        lqw.eq(StringUtils.isNotBlank(bo.getAppVersionRemarks()), AppVersion::getAppVersionRemarks, bo.getAppVersionRemarks());
        return lqw;
    }

    /**
     * 新增APP版本管理
     */
    @Override
    public Boolean insertByBo(AppVersionBo bo) {
        AppVersion add = MapstructUtils.convert(bo, AppVersion.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAppVersionId(add.getAppVersionId());
        }
        return flag;
    }

    /**
     * 修改APP版本管理
     */
    @Override
    public Boolean updateByBo(AppVersionBo bo) {
        AppVersion update = MapstructUtils.convert(bo, AppVersion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppVersion entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除APP版本管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public AppVersionVo queryLatestVerInfo() {
        LambdaQueryWrapper<AppVersion> queryWrapper = Wrappers.lambdaQuery(AppVersion.class);
        queryWrapper.orderByDesc(AppVersion::getCreateTime);
        queryWrapper.last("LIMIT 1");
        return baseMapper.selectVoOne(queryWrapper);
    }

    @Override
    public AppVersionV2Vo queryLatestVerInfoV2(String extraIsbn, String sn) {
        // 集合返回的数据：每个包名下面最新的app信息
        List<RemoteAppInfoVo> remoteAppInfoVoList = remoteExtResourceService.androidOtaCheckBySn(extraIsbn, sn);
        if (CollUtil.isNotEmpty(remoteAppInfoVoList)) {
//            RemoteAppInfoVo remoteAppInfoVo = Collections.max(remoteAppInfoVoList, Comparator.comparing(RemoteAppInfoVo::getVersionCode));
            RemoteAppInfoVo remoteAppInfoVo = remoteAppInfoVoList.stream().filter(appInfoVo -> "com.jxw.xwsf".equals(appInfoVo.getPackageName())).findFirst().orElse(null);
            if (remoteAppInfoVo !=null) {
                AppVersionV2Vo appVersionV2Vo = new AppVersionV2Vo();
                appVersionV2Vo.setAppVersionId(Long.valueOf(remoteAppInfoVo.getId()));
                appVersionV2Vo.setAppVersion(remoteAppInfoVo.getVersionName());
                appVersionV2Vo.setAppVersionCode(remoteAppInfoVo.getVersionCode());
                appVersionV2Vo.setAppVersionRemarks(remoteAppInfoVo.getUpdateContent());
                appVersionV2Vo.setAppOssUrl(remoteAppInfoVo.getUrl());
                log.info("appVersionVo: {}", appVersionV2Vo);
                return appVersionV2Vo;
            }
        }
        return null;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
