package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.SysDept;

import java.util.List;

/**
 * 部门业务对象 sys_dept
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysDept.class, reverseConvertGenerate = false)
public class SysDeptBo extends BaseEntity {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 部门名称
     */
    @NotBlank(message = "部门名称不能为空")
    @Size(min = 0, max = 30, message = "部门名称长度不能超过{max}个字符")
    private String deptName;

    /**
     * 显示顺序
     */
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    /**
     * 负责人
     */
    private Long leader;

    /**
     * 联系电话
     */
    @Size(min = 0, max = 11, message = "联系电话长度不能超过{max}个字符")
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过{max}个字符")
    private String email;

    /**
     * 部门状态（0正常 1停用）
     */
    private String status;

    /**
     * 文件地址id
     */
    private Long logo;


    /**
     * 是否门店（ 0：不是 1：是）
     */
    @NotNull(message = "是否门店不能为空")
    private Boolean isStore;

    private List<Long> deptIds;
    /**
     * 精准的部门名称，不like模糊查询
     */
    private String deptNameExact;

    /**
     * 系统展示Logo
     */
    private Long systemLogo;

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 支付商户配置id
     */
    private Long payMerchantConfigId;

    private List<Long> deptIdList;

    private Boolean openDetail;

    private Boolean openLogo;


}
