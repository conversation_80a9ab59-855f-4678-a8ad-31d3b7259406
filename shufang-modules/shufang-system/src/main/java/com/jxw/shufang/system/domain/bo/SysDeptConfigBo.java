package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.SysDeptConfig;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysDeptConfig.class, reverseConvertGenerate = false)
public class SysDeptConfigBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 参数主键
     */
    private Long configId;


    /**
     * 0-在线作答，1-打印作答
     */
    private Integer answerPatternType;

    /**
     * 部门id
     */
    private Long deptId;


}
