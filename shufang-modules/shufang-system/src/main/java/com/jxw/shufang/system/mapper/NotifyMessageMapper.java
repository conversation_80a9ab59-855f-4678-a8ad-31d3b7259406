package com.jxw.shufang.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.domain.NotifyMessage;
import com.jxw.shufang.system.domain.bo.NoticeMessageBo;
import com.jxw.shufang.system.domain.vo.NoticeMessageVo;
import com.jxw.shufang.system.domain.vo.NotifyMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface NotifyMessageMapper extends BaseMapperPlus<NotifyMessage, NotifyMessageVo> {

    List<NoticeMessageVo> selectNoticeMessageList(IPage<NoticeMessageVo> page,
                                                  NoticeMessageBo bo);

    List<String> selectBizTypeList(Long userId);

    Page<NoticeMessageVo> selectTodayNoticeMessage(IPage<NoticeMessageVo> page,
                                                   @Param("toUserId") Long toUserId,
                                                   @Param("today") LocalDate today,
                                                   @Param("readStatus") Integer readStatus);

}
