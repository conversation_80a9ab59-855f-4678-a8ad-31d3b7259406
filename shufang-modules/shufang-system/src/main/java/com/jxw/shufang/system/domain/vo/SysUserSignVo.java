package com.jxw.shufang.system.domain.vo;

import com.jxw.shufang.system.domain.SysUserSign;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUserSign.class)
public class SysUserSignVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;
    private Long userId;

    /**
     * 租户ID
     */
    private Integer type;

    /**
     * 用户账号
     */
    private String version;

    /**
     * 用户昵称
     */
    private Integer signStatus;

}
