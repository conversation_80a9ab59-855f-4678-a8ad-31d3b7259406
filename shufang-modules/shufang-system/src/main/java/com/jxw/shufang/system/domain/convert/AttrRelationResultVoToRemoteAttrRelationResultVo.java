package com.jxw.shufang.system.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.system.api.domain.vo.RemoteAttrRelationResultVo;
import com.jxw.shufang.system.domain.vo.AttrRelationResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;



@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AttrRelationResultVoToRemoteAttrRelationResultVo extends BaseMapper<AttrRelationResultVo, RemoteAttrRelationResultVo> {

}
