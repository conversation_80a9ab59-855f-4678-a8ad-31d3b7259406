package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataBaseHelper;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.api.RemoteConfigService;
import com.jxw.shufang.system.domain.Attribute;
import com.jxw.shufang.system.domain.AttributeRelation;
import com.jxw.shufang.system.domain.bo.AttributeBo;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttrRelationResultVo;
import com.jxw.shufang.system.domain.vo.AttributeGroupingVo;
import com.jxw.shufang.system.domain.vo.AttributeRelationVo;
import com.jxw.shufang.system.domain.vo.AttributeVo;
import com.jxw.shufang.system.mapper.AttributeMapper;
import com.jxw.shufang.system.mapper.AttributeRelationMapper;
import com.jxw.shufang.system.service.IAttributeGroupingService;
import com.jxw.shufang.system.service.IAttributeRelationService;
import com.jxw.shufang.system.service.IAttributeService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 属性Service业务层处理
 *
 *
 * @date 2024-03-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AttributeServiceImpl implements IAttributeService, BaseService {

    //输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)
    //为了加快查询速度，优先能使用 = 查询的，其次in查询，其次再到like查询
    private static final List<String> inputTypeQuerySort = Arrays.asList("1", "8", "4", "2", "3", "7", "5", "6");

    private final AttributeMapper baseMapper;

    private final IAttributeGroupingService attributeGroupingService;

    private final IAttributeRelationService attributeRelationService;

    private final AttributeRelationMapper attributeRelationMapper;

    @DubboReference
    private RemoteConfigService remoteConfigService;


    /**
     * 查询属性
     */
    @Override
    public AttributeVo queryById(Long attributeId) {
        return baseMapper.selectVoById(attributeId);
    }

    /**
     * 查询属性列表
     */
    @Override
    public TableDataInfo<AttributeVo> queryPageList(AttributeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Attribute> lqw = buildQueryWrapper(bo);
        Page<AttributeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询属性列表
     */
    @Override
    public List<AttributeVo> queryList(AttributeBo bo) {
        LambdaQueryWrapper<Attribute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Attribute> buildQueryWrapper(AttributeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Attribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeGroupingIds()), Attribute::getAttributeGroupingIds, bo.getAttributeGroupingIds());
        lqw.like(StringUtils.isNotBlank(bo.getAttributeName()), Attribute::getAttributeName, bo.getAttributeName());
        lqw.eq(StringUtils.isNotBlank(bo.getInputType()), Attribute::getInputType, bo.getInputType());
        lqw.eq(StringUtils.isNotBlank(bo.getAttributeValues()), Attribute::getAttributeValues, bo.getAttributeValues());
        lqw.eq(bo.getAttributeSort() != null, Attribute::getAttributeSort, bo.getAttributeSort());
        lqw.eq(StringUtils.isNotBlank(bo.getCanNullStatus()), Attribute::getCanNullStatus, bo.getCanNullStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getParentAttributeIds()), Attribute::getParentAttributeIds, bo.getParentAttributeIds());
        lqw.eq(StringUtils.isNotBlank(bo.getShowList()), Attribute::getShowList, bo.getShowList());
        lqw.eq(StringUtils.isNotBlank(bo.getSearch()), Attribute::getSearch, bo.getSearch());
        lqw.eq(bo.getWidth() != null, Attribute::getWidth, bo.getWidth());
        lqw.eq(StringUtils.isNotBlank(bo.getFixed()), Attribute::getFixed, bo.getFixed());
        lqw.eq(StringUtils.isNotBlank(bo.getSort()), Attribute::getSort, bo.getSort());
        lqw.eq(StringUtils.isNotBlank(bo.getAlign()), Attribute::getAlign, bo.getAlign());
        lqw.in(CollUtil.isNotEmpty(bo.getAttributeGroupingIdList()), Attribute::getAttributeGroupingIds, bo.getAttributeGroupingIdList());
        lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), Attribute::getDelFlag, bo.getDelFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getShowLocation()), Attribute::getShowLocation, bo.getShowLocation());
        lqw.orderByAsc(List.of(Attribute::getAttributeSort,Attribute::getAttributeId));
        return lqw;
    }

    /**
     * 新增属性
     */
    @Override
    public Boolean insertByBo(AttributeBo bo) {
        Attribute add = MapstructUtils.convert(bo, Attribute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            // 清理缓存
            RedisUtils.deleteObject("attributeListByGroupingId");
            bo.setAttributeId(add.getAttributeId());
        }
        return flag;
    }

    /**
     * 修改属性
     */
    @Override
    public Boolean updateByBo(AttributeBo bo) {
        Attribute update = MapstructUtils.convert(bo, Attribute.class);
        validEntityBeforeSave(update);
        if (baseMapper.updateById(update) > 0) {
            // 清理缓存
            RedisUtils.deleteObject("attributeListByGroupingId");
            return true;
        }
        return false;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Attribute entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除属性
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        if (baseMapper.deleteBatchIds(ids) > 0) {
            // 清理缓存
            RedisUtils.deleteObject("attributeListByGroupingId");
            return true;
        }
        return false;
    }

    @Override
    public List<AttributeVo> getAttributeByGroupType(String groupType, Long branchId, boolean withUniversally) {
        Assert.notBlank(groupType, "分组类型不能为空");
        Assert.notNull(branchId, "分店id不能为空，如果只查通用属性，传-1，和withUniversally为true");

        List<AttributeGroupingVo> attributeGroupingList = attributeGroupingService.getByType(groupType);
        List<Long> filterBranchIdList = new ArrayList<>();
        filterBranchIdList.add(branchId);
        if (withUniversally) {
            //如果查询通用属性，再加一个门店id为-1的
            filterBranchIdList.add(-1L);
        }
        //查分组
        attributeGroupingList = attributeGroupingList.stream().filter(attributeGroupingVo -> filterBranchIdList.contains(attributeGroupingVo.getBranchId())).collect(Collectors.toList());
        if (attributeGroupingList.isEmpty()) {
            return List.of();
        }
        //查分组下的所有属性
        List<AttributeVo> returnList = new ArrayList<>();
        AttributeServiceImpl aopProxy = SpringUtils.getAopProxy(this);
        for (AttributeGroupingVo attributeGroupingVo : attributeGroupingList) {
            List<AttributeVo> listByGroupId = aopProxy.getListByGroupId(attributeGroupingVo.getAttributeGroupingId());
            if (listByGroupId != null) {
                returnList.addAll(listByGroupId);
            }
        }
        //排序 attributeSort从小到大排序
        returnList.sort(Comparator.comparingInt(AttributeVo::getAttributeSort));
        return returnList;
    }

    /**
     * TODO 在考虑已填的值要不要做缓存
     */
    @Override
    public List<AttrRelationResultVo> getAttrWithValueByGroupType(List<Long> typeIdList, String groupType, Long branchId, boolean withUniversally) {

        List<AttributeVo> attributeList = getAttributeByGroupType(groupType, branchId, withUniversally);
        if (attributeList.isEmpty()) {
            return List.of();
        }
        //查询这个typeid所填的值
        List<Long> attributeIds = attributeList.stream().map(AttributeVo::getAttributeId).collect(Collectors.toList());

        AttributeRelationBo attributeRelationBo = new AttributeRelationBo();
        attributeRelationBo.setTypeIdList(typeIdList);
        attributeRelationBo.setAttributeIdList(attributeIds);
        List<AttributeRelationVo> attributeRelationVos = attributeRelationService.queryList(attributeRelationBo);
        Map<Long, List<AttributeRelationVo>> map = null;
        if (CollUtil.isEmpty(attributeRelationVos)) {
            map = new HashMap<>();
        } else {
            map = StreamUtils.groupByKey(attributeRelationVos, AttributeRelationVo::getTypeId);
        }
        List<AttrRelationResultVo> resList = new ArrayList<>();
        for (Long typeId : typeIdList) {
            AttrRelationResultVo attrRelationResultVo = new AttrRelationResultVo();
            attrRelationResultVo.setTypeId(typeId);
            attrRelationResultVo.setAttributeList(attributeList);
            attrRelationResultVo.setRelationList(map.get(typeId));
            resList.add(attrRelationResultVo);
        }
        return resList;
    }

    @Override
    public List<AttributeVo> querySearchAttr(String type, String showLocation, Long branchId, boolean withUniversally) {
        List<AttributeVo> attributeByGroupType = getAttributeByGroupType(type, branchId, withUniversally);

        if (CollUtil.isEmpty(attributeByGroupType)) {
            return List.of();
        }
        if (StringUtils.isNotBlank(showLocation)) {
            attributeByGroupType = attributeByGroupType.stream().filter(attributeVo -> showLocation.equals(attributeVo.getShowLocation())).collect(Collectors.toList());
        }
        //只返回需要搜索的属性
        List<AttributeVo> collect = attributeByGroupType.stream().filter(attributeVo -> UserConstants.ATTR_STATUS_YES.equals(attributeVo.getSearch())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)){
            //排序 attributeSort从小到大排序
            collect.sort(Comparator.comparingInt(AttributeVo::getAttributeSort));
        }
        return collect;
    }

    @Override
    public List<AttributeVo> queryAddAttr(String type, String showLocation, Long branchId, boolean withUniversally) {
        List<AttributeVo> attributeByGroupType = getAttributeByGroupType(type, branchId, withUniversally);

        if (CollUtil.isEmpty(attributeByGroupType)) {
            return List.of();
        }
        if (StringUtils.isNotBlank(showLocation)) {
            attributeByGroupType = attributeByGroupType.stream().filter(attributeVo -> showLocation.equals(attributeVo.getShowLocation())).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(attributeByGroupType)){
            //排序 attributeSort从小到大排序
            attributeByGroupType.sort(Comparator.comparingInt(AttributeVo::getAttributeSort));
        }
        return attributeByGroupType;
    }

    @Override
    public List<Long> search(String searchJson, String groupType) throws ServiceException {
        if (StringUtils.isBlank(groupType)) {
            throw new ServiceException("组类型不能为空");
        }
        if (StringUtils.isBlank(searchJson)) {
            return List.of(-1L);
        }
        List<AttributeRelationBo> attributeRelationBos = null;
        try {
            attributeRelationBos = JsonUtils.parseObject(searchJson, new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new ServiceException("动态属性搜索条件格式错误");
        }
        if (CollUtil.isEmpty(attributeRelationBos)) {
            return List.of(-1L);
        }
        attributeRelationBos = attributeRelationBos.stream().filter(attributeRelationBo -> attributeRelationBo.getAttributeId() != null && StringUtils.isNotBlank(attributeRelationBo.getValue())).collect(Collectors.toList());
        if (CollUtil.isEmpty(attributeRelationBos)) {
            return List.of(-1L);
        }

        IAttributeService selfService = SpringUtils.getBean(IAttributeService.class);
        Map<Long, AttributeVo> attrMap = new HashMap<>();
        for (AttributeRelationBo attributeRelationBo : attributeRelationBos) {
            Long attributeId = attributeRelationBo.getAttributeId();
            AttributeVo attributeVo = selfService.getById(attributeId);
            if (attributeVo == null) {
                throw new ServiceException("属性不存在,id:" + attributeId);
            }
            attrMap.put(attributeId, attributeVo);
        }

        //attributeRelationBos按照inputTypeQuerySort中的索引顺序进行排序，从小到大
        attributeRelationBos.sort(Comparator.comparingInt(o -> inputTypeQuerySort.indexOf(attrMap.get(o.getAttributeId()).getInputType())));
        String attrValueSeparator = remoteConfigService.selectAttrValueSeparator();
        //开始查询，每一次循环得出的typeId给下一个查询用
        List<Long> resList = new ArrayList<>();
        for (AttributeRelationBo attributeRelationBo : attributeRelationBos) {
            resList = queryTypeIdList(groupType, attributeRelationBo.getValue(), attributeRelationBo.getAttributeId(), attrMap.get(attributeRelationBo.getAttributeId()).getInputType(), resList, attrValueSeparator);
            if (CollUtil.isEmpty(resList)) {
                return List.of(-1L);
            }
        }
        return resList;
    }

    @Override
    public void updateTypeRelation(String type, Long courseId, List<AttributeRelationBo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> attributeIdList = list.stream().map(AttributeRelationBo::getAttributeId).distinct().collect(Collectors.toList());

        AttributeRelationBo query = new AttributeRelationBo();
        query.setTypeId(courseId);
        query.setType(type);
        query.setAttributeIdList(attributeIdList);
        List<AttributeRelationVo> attributeRelationVos = attributeRelationService.queryList(query);
        Map<Long, AttributeRelationVo> map = StreamUtils.toMap(attributeRelationVos, AttributeRelationVo::getAttributeId, Function.identity());

        List<AttributeRelationBo> insertList = new ArrayList<>();
        List<AttributeRelationBo> updateList = new ArrayList<>();

        for (AttributeRelationBo attributeRelationBo : list) {
            AttributeRelationVo attributeRelationVo = map.get(attributeRelationBo.getAttributeId());
            if (attributeRelationVo == null) {
                attributeRelationBo.setTypeId(courseId);
                attributeRelationBo.setType(type);
                insertList.add(attributeRelationBo);
            } else {
                attributeRelationBo.setId(attributeRelationVo.getId());
                updateList.add(attributeRelationBo);
            }
        }
        if (CollUtil.isNotEmpty(insertList)) {
            boolean b = attributeRelationService.insertRelationBatch(insertList);
            if (!b) {
                throw new ServiceException("插入关系失败");
            }
        }

        if (CollUtil.isNotEmpty(updateList)) {
            boolean b = attributeRelationService.updateRelationBatch(updateList, true);
            if (!b) {
                throw new ServiceException("更新关系失败");
            }
        }


    }


    public List<Long> queryTypeIdList(String groupType, String queryValue, Long attributeId, String inputType, List<Long> limitTypeIdList, String attrValueSeparator) {

        QueryWrapper<AttributeRelation> query = Wrappers.query();
        query.eq("type", groupType);
        query.eq("attribute_id", attributeId);
        query.in(CollUtil.isNotEmpty(limitTypeIdList), "type_id", limitTypeIdList);

        //输入方式(3输入  5上传图片 6上传附件 7多行输入 8日期)
        switch (inputType) {
            case "1":
            case "8":
                query.eq("value", queryValue);
                break;
            case "2":
            case "4":
                for (String itemStr : queryValue.split(attrValueSeparator)) {
                    query.apply(" find_in_set({0},replace(value,'" + attrValueSeparator + "',','))", itemStr);
                }
                break;
            case "3":
            case "5":
            case "6":
            case "7":
                query.like("value", queryValue);
                break;
        }
        query.select("distinct type_id");
        List<AttributeRelation> attributeRelations = attributeRelationMapper.selectList(query);

        if (CollUtil.isEmpty(attributeRelations)) {
            return List.of();
        }
        return attributeRelations.stream().map(AttributeRelation::getTypeId).collect(Collectors.toList());

    }


    @Override
    @Cacheable(value = "attributeListByGroupingId", key = "#groupId", condition = "#groupId != null")
    public List<AttributeVo> getListByGroupId(Long groupId) {
        LambdaQueryWrapper<Attribute> lqw = Wrappers.lambdaQuery();
        lqw.apply(DataBaseHelper.findInSet(groupId, "attribute_grouping_ids"));
        lqw.eq(Attribute::getDelFlag, UserConstants.DEL_FLAG_NO);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Cacheable(value = "attribute", key = "#attributeId", condition = "#attributeId != null")
    public AttributeVo getById(Long attributeId) {
        LambdaQueryWrapper<Attribute> lqw = Wrappers.lambdaQuery();
        lqw.eq(Attribute::getAttributeId, attributeId);
        lqw.eq(Attribute::getDelFlag, UserConstants.DEL_FLAG_NO);
        return baseMapper.selectVoOne(lqw);
    }

    @Override
    public void cleanAllCache() {
        log.info("===========attributeService cleanAllCache===========");
        //清除所有缓存
        RedisUtils.deleteObject("attributeListByGroupingId");
        RedisUtils.deleteObject("attribute");
        log.info("===========attributeService cleanAllCache end===========");
    }


    @Override
    public void init() {
        log.info("===========attributeService init start===========");
        IAttributeService aopBean = SpringUtils.getBean(IAttributeService.class);
        aopBean.cleanAllCache();

        log.info("===========attributeService init attributeByGroupingId===========");
        QueryWrapper<Attribute> query = Wrappers.query();
        query.select("distinct attribute_grouping_ids as `attribute_grouping_ids`");
        query.eq("del_flag", UserConstants.DEL_FLAG_NO);
        List<Attribute> attributes = baseMapper.selectList(query);
        log.info("===========attributeService init attributeByGroupingId size {}===========", attributes.size());
        for (Attribute attribute : attributes) {
            String attributeGroupingIds = attribute.getAttributeGroupingIds();
            if (StringUtils.isBlank(attributeGroupingIds)) {
                continue;
            }
            for (String groupId : attributeGroupingIds.split(",")) {
                aopBean.getListByGroupId(Long.valueOf(groupId));

            }
        }


        LambdaQueryWrapper<Attribute> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.select(Attribute::getAttributeId);
        lambdaQuery.eq(Attribute::getDelFlag, UserConstants.DEL_FLAG_NO);
        List<Attribute> attributeList = baseMapper.selectList(lambdaQuery);
        log.info("===========attributeService init attribute size {}===========", attributeList.size());
        for (Attribute attribute : attributeList) {
            aopBean.getById(attribute.getAttributeId());
        }


        log.info("===========attributeService init  end===========");

    }
}
