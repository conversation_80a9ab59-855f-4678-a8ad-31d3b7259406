package com.jxw.shufang.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.domain.SysDeptConfig;
import com.jxw.shufang.system.domain.bo.SysDeptConfigBo;
import com.jxw.shufang.system.domain.vo.SysDeptConfigVo;
import com.jxw.shufang.system.mapper.SysDeptConfigMapper;
import com.jxw.shufang.system.service.ISysDeptConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@RequiredArgsConstructor
@Service
public class SysDeptConfigServiceImpl implements ISysDeptConfigService {

    private final SysDeptConfigMapper baseMapper;


    @Override
    public SysDeptConfigVo selectDeptById(Long deptId) {
        LambdaQueryWrapper<SysDeptConfig> eq = new LambdaQueryWrapper<SysDeptConfig>().eq(SysDeptConfig::getDeptId, deptId);
        SysDeptConfig sysDeptConfig = baseMapper.selectOne(eq, false);
        //部门配置
        if (ObjectUtils.isEmpty(sysDeptConfig)) {
            SysDeptConfigBo sysDeptConfigBo = new SysDeptConfigBo();
            sysDeptConfigBo.setDeptId(deptId);
            sysDeptConfigBo.setAnswerPatternType(0);
            this.saveOrUpdate(sysDeptConfigBo);
            return MapstructUtils.convert(baseMapper.selectOne(eq, false), SysDeptConfigVo.class);
        }


        return MapstructUtils.convert(sysDeptConfig, SysDeptConfigVo.class);
    }

    @Override
    public Boolean saveOrUpdate(SysDeptConfigBo bo) {
        SysDeptConfig config = MapstructUtils.convert(bo, SysDeptConfig.class);
        return baseMapper.insertOrUpdate(config);
    }
}
