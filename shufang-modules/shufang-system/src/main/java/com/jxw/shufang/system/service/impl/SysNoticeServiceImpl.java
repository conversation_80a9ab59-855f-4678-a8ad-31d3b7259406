package com.jxw.shufang.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.api.enums.NoticeEnum;
import com.jxw.shufang.system.api.model.LoginUser;
import com.jxw.shufang.system.domain.SysDept;
import com.jxw.shufang.system.domain.SysNoticeUser;
import com.jxw.shufang.system.domain.bo.SysNoticeNewBo;
import com.jxw.shufang.system.domain.bo.SysNoticeQueryBo;
import com.jxw.shufang.system.domain.vo.NoticeTypeVo;
import com.jxw.shufang.system.mapper.SysDeptMapper;
import com.jxw.shufang.system.mapper.SysNoticeUserMapper;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.SysNotice;
import com.jxw.shufang.system.domain.bo.SysNoticeBo;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;
import com.jxw.shufang.system.domain.vo.SysUserVo;
import com.jxw.shufang.system.mapper.SysNoticeMapper;
import com.jxw.shufang.system.mapper.SysUserMapper;
import com.jxw.shufang.system.service.ISysNoticeService;
import com.jxw.shufang.system.service.ISysNoticeUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 公告 服务层实现
 *

 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {

    @Value("${notice.role:}")
    private String roleString;

    private final SysNoticeMapper baseMapper;
    private final SysUserMapper userMapper;
    private final SysNoticeUserMapper sysNoticeUserMapper;
    private final SysDeptMapper deptMapper;

    private final ISysNoticeUserService sysNoticeUserService;

    @Override
    public TableDataInfo<SysNoticeVo> selectPageNoticeList(SysNoticeBo notice, PageQuery pageQuery) {
        QueryWrapper<SysNotice> lqw = buildQueryWrapper(notice);
        Page<SysNoticeVo> page = baseMapper.selectNoticePage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<SysNoticeVo> pageQuery(SysNoticeQueryBo queryBo, PageQuery pageQuery) {
        if (LoginHelper.isSuperAdmin()) {
            queryBo.setIsAdmin(1);
        } else {
            LoginUser loginUser = LoginHelper.getLoginUser();
            Assert.notNull(loginUser, "登录信息为空");
            Long deptId = loginUser.getDeptId();
            Assert.notNull(loginUser.getDeptId(), "用户所属组织为空");
            SysDept sysDept = deptMapper.selectById(deptId);
            List<Long> toDeptIds = new ArrayList<>(Arrays.stream(sysDept.getAncestors().split(",")).map(Long::valueOf).toList());
            toDeptIds.add(deptId);
            queryBo.setToDeptList(toDeptIds);
            queryBo.setOwnerId(loginUser.getUserId());
        }

        if (StringUtils.isNotBlank(queryBo.getCreateByName())) {
            SysUserVo sysUser = userMapper.selectUserByUserName(queryBo.getCreateByName());
            queryBo.setCreateBy(Objects.isNull(sysUser) ? 0L : sysUser.getUserId());
        }

        IPage<SysNoticeVo> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page<SysNoticeVo> pageData = baseMapper.pageQuery(page, queryBo);
        return TableDataInfo.build(pageData);
    }

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNoticeVo selectNoticeById(Long noticeId) {
        return baseMapper.selectVoById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNoticeVo> selectNoticeList(SysNoticeBo notice) {
        LambdaQueryWrapper<SysNotice> lqw = buildLambdaQueryWrapper(notice);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysNotice> buildLambdaQueryWrapper(SysNoticeBo bo) {
        LambdaQueryWrapper<SysNotice> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNoticeTitle()), SysNotice::getNoticeTitle, bo.getNoticeTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeType()), SysNotice::getNoticeType, bo.getNoticeType());
        lqw.in(CollUtil.isNotEmpty(bo.getNoticeIdList()), SysNotice::getNoticeId, bo.getNoticeIdList());
        if (StringUtils.isNotBlank(bo.getCreateByName())) {
            SysUserVo sysUser = userMapper.selectUserByUserName(bo.getCreateByName());
            lqw.eq(SysNotice::getCreateBy, ObjectUtil.isNotNull(sysUser) ? sysUser.getUserId() : null);
        }
        if (Boolean.TRUE.equals(bo.getNotWithContentInfo())) {
            lqw.select(SysNotice.class, i -> !"noticeContent".equals(i.getProperty()));
        }
        lqw.orderByAsc(SysNotice::getNoticeId);
        return lqw;
    }

    private QueryWrapper<SysNotice> buildQueryWrapper(SysNoticeBo bo) {
        QueryWrapper<SysNotice> lqw = Wrappers.query();
        lqw.like(StringUtils.isNotBlank(bo.getNoticeTitle()), "t.notice_title", bo.getNoticeTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeType()), "t.notice_type", bo.getNoticeType());
        lqw.in(CollUtil.isNotEmpty(bo.getNoticeIdList()), "t.notice_id", bo.getNoticeIdList());
        if (StringUtils.isNotBlank(bo.getCreateByName())) {
            SysUserVo sysUser = userMapper.selectUserByUserName(bo.getCreateByName());
            lqw.eq("t.create_by", ObjectUtil.isNotNull(sysUser) ? sysUser.getUserId() : null);
        }
        if (Boolean.TRUE.equals(bo.getNotWithContentInfo())) {
            lqw.select(SysNotice.class, i -> !"noticeContent".equals(i.getProperty()));
        }
        lqw.orderByAsc("t.notice_id");
        return lqw;
    }

    /**
     * 新增公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertNotice(SysNoticeBo bo) {
        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        int insert = baseMapper.insert(notice);
        if (insert <= 0 || notice.getNoticeId() == null) {
            return insert;
        }
        List<Long> noticeUserIdList = bo.getNoticeUserIdList();
        //如果接收人为空，代表所有人接受
        if (CollUtil.isEmpty(noticeUserIdList)) {
            List<SysUserVo> sysUserVos = userMapper.selectSysAndStaffUserList();
            //如果这样都没有了，那就不用保存了
            if (CollUtil.isEmpty(sysUserVos)) {
                throw new ServiceException("没有任何用户可以接收");
            }
            noticeUserIdList = StreamUtils.toList(sysUserVos, SysUserVo::getUserId);
        }
        boolean b = sysNoticeUserService.insertNoticeUser(notice.getNoticeId(), noticeUserIdList);
        if (!b) {
            throw new ServiceException("保存公告接收者失败");
        }
        return insert;
    }

    /**
     * 修改公告
     *
     * @param bo 公告信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNotice(SysNoticeBo bo) {
        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        int i = baseMapper.updateById(notice);
        if (i <= 0) {
            return i;
        }
        List<Long> noticeUserIdList = bo.getNoticeUserIdList();
        //先删除原有接收者
        int j = sysNoticeUserService.deleteNoticeUserByNoticeIds(new Long[]{notice.getNoticeId()});
        if (j <= 0) {
            throw new ServiceException("删除旧公告接收者失败");
        }
        //如果接收人为空，代表所有人接受
        if (CollUtil.isEmpty(noticeUserIdList)) {
            List<SysUserVo> sysUserVos = userMapper.selectSysAndStaffUserList();
            //如果这样都没有了，那就不用保存了
            if (CollUtil.isEmpty(sysUserVos)) {
                throw new ServiceException("没有任何用户可以接收");
            }
            noticeUserIdList = StreamUtils.toList(sysUserVos, SysUserVo::getUserId);
        }
        boolean b = sysNoticeUserService.insertNoticeUser(notice.getNoticeId(), noticeUserIdList);
        if (!b) {
            throw new ServiceException("保存公告接收者失败");
        }
        return i;
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteNoticeById(Long noticeId) {
        return baseMapper.deleteById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNoticeByIds(Long[] noticeIds) {
        List<SysNotice> sysNotices = baseMapper.selectBatchIds(Arrays.asList(noticeIds));
        for (SysNotice sysNotice : sysNotices) {
            if (!LoginHelper.isSuperAdmin() && !LoginHelper.getUserId().equals(sysNotice.getCreateBy())) {
                return 0;
            }
        }

        int i = baseMapper.deleteBatchIds(Arrays.asList(noticeIds));
        //删除接受者数据
        sysNoticeUserService.deleteNoticeUserByNoticeIds(noticeIds);
        return i;
    }

    @Override
    public int changeStatus(SysNoticeBo notice) {
        return baseMapper.update(Wrappers.<SysNotice>lambdaUpdate()
            .set(SysNotice::getStatus, notice.getStatus())
            .eq(SysNotice::getNoticeId, notice.getNoticeId())
        );
    }

    @Transactional
    @Override
    public boolean addNew(SysNoticeNewBo bo) {
        SysNotice notice = MapstructUtils.convert(bo, SysNotice.class);
        if (Objects.isNull(notice)) {
            return false;
        }

        LoginUser user = LoginHelper.getLoginUser();
        notice.setCreateBy(user.getUserId());
        notice.setCreateDept(user.getDeptId());
        Date date = new Date();
        notice.setCreateTime(date);
        return baseMapper.insert(notice) > 0;
    }

    @Transactional
    @Override
    public boolean editNew(SysNoticeNewBo bo) {
        SysNotice notice = baseMapper.selectById(bo.getNoticeId());
        if (Objects.isNull(notice)) {
            return false;
        }

        if (!LoginHelper.isSuperAdmin() && !LoginHelper.getUserId().equals(notice.getCreateBy())) {
            return false;
        }

        BeanUtil.copyProperties(bo, notice, CopyOptions.create().setIgnoreNullValue(true));
        notice.setCreateBy(LoginHelper.getUserId());
        notice.setUpdateBy(LoginHelper.getUserId());
        notice.setUpdateTime(new Date());
        return baseMapper.updateById(notice) > 0;
    }

    @Override
    public List<NoticeTypeVo> getNoticeType() {
        List<NoticeTypeVo> list = new ArrayList<>();
        for (NoticeEnum noticeEnum : NoticeEnum.values()) {
            NoticeTypeVo vo = new NoticeTypeVo();
            vo.setCode(noticeEnum.getCode());
            vo.setName(noticeEnum.getDesc());
            list.add(vo);
        }
        return list;
    }

    @Override
    public List<NoticeTypeVo> getNoticeTypeOfRole() {
        if (StringUtils.isBlank(roleString)) {
            return getNoticeType();
        }

        List<String> splitList = Arrays.asList(roleString.split(","));
        LoginUser user = LoginHelper.getLoginUser();
        long count = user.getRoles().stream().filter(i -> !splitList.contains(i.getRoleKey())).count();
        if (count <= 0) {
            NoticeTypeVo vo = new NoticeTypeVo();
            vo.setCode(NoticeEnum.RT.getCode());
            vo.setName(NoticeEnum.RT.getDesc());
            return Collections.singletonList(vo);
        }

        return getNoticeType();
    }

    @Transactional
    @Override
    public boolean hasUnread() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Assert.notNull(loginUser, "登录信息为空");
        Long userId = loginUser.getUserId();
        Long deptId = loginUser.getDeptId();
        Assert.notNull(userId, "用户id为空");
        Assert.notNull(deptId, "用户所属组织为空");

        SysDept sysDept = deptMapper.selectById(deptId);
        List<Long> deptIds = new ArrayList<>(Arrays.stream(sysDept.getAncestors().split(",")).map(Long::valueOf).toList());
        deptIds.add(deptId);
        List<Long> noticeIds = baseMapper.selectUnreadNotice(userId, deptIds);
        if (!noticeIds.isEmpty()) {
            List<SysNoticeUser> list = noticeIds.stream().map(noticeId -> {
                SysNoticeUser noticeUser = new SysNoticeUser();
                noticeUser.setNoticeId(noticeId);
                noticeUser.setUserId(userId);
                noticeUser.setCreateDept(deptId);
                noticeUser.setCreateBy(userId);
                noticeUser.setCreateTime(new Date());
                return noticeUser;
            }).toList();
            sysNoticeUserMapper.saveBatch(list);
            return true;
        }
        return false;
    }

    @Override
    public boolean doRead() {
        Long userId = LoginHelper.getUserId();
        log.info("【书房公告栏】设置用户公告已读{}", userId);
        LambdaQueryWrapper<SysNoticeUser> wrapper = Wrappers.<SysNoticeUser>lambdaQuery()
            .eq(SysNoticeUser::getUserId, userId)
            .eq(SysNoticeUser::getReadStatus, '2');
        List<SysNoticeUser> list = sysNoticeUserMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("【书房公告栏】设置用户公告已读列表{}", list.stream().map(SysNoticeUser::getNoticeId).distinct().toList());
            list.forEach(i -> i.setReadStatus("0"));
            sysNoticeUserMapper.updateBatchById(list);
        }
        return true;
    }

}
