package com.jxw.shufang.system.domain.convert;


import com.jxw.shufang.system.api.domain.bo.RemoteNotifyMessageBo;
import com.jxw.shufang.system.domain.NotifyMessage;
import com.jxw.shufang.system.domain.NotifyMessageItem;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RemoteNotifyMessageBoConvert {

    RemoteNotifyMessageBoConvert INSTANCE = Mappers.getMapper(RemoteNotifyMessageBoConvert.class);

    NotifyMessage toNotifyMessage(RemoteNotifyMessageBo messageBo);

    NotifyMessageItem toNotifyMessageItem(RemoteNotifyMessageBo messageBo);

}
