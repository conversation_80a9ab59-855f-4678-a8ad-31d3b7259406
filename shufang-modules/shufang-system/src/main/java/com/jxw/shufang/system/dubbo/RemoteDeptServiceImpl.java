package com.jxw.shufang.system.dubbo;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.system.api.RemoteDeptService;
import com.jxw.shufang.system.api.domain.bo.RemoteDeptBo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptAccountVo;
import com.jxw.shufang.system.api.domain.vo.RemoteDeptVo;
import com.jxw.shufang.system.domain.bo.SysDeptBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 部门服务
 *

 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteDeptServiceImpl implements RemoteDeptService {

    private final ISysDeptService sysDeptService;

    @Override
    public String selectDeptNameByIds(String deptIds) {
        return sysDeptService.selectDeptNameByIds(deptIds);
    }

    @Override
    public List<RemoteDeptVo> getSelfAndChildShopList(Long deptId) {
        List<SysDeptVo> selfAndChildShopList = sysDeptService.getSelfAndChildShopList(deptId);
        return MapstructUtils.convert(selfAndChildShopList, RemoteDeptVo.class);
    }

    @Override
    public boolean checkDeptNameUnique(Long deptId, String deptName, Long parentId) {
        SysDeptBo sysDeptBo = new SysDeptBo();
        sysDeptBo.setDeptId(deptId);
        sysDeptBo.setDeptName(deptName);
        sysDeptBo.setParentId(parentId);
        return sysDeptService.checkDeptNameUnique(sysDeptBo);
    }

    @Override
    public boolean deptIsShop(Long deptId) {
        SysDeptVo sysDeptVo = sysDeptService.selectDeptById(deptId);
        if (sysDeptVo == null) {
            return false;
        }
        return sysDeptVo.getIsStore()!=null && sysDeptVo.getIsStore();
    }

    @Override
    public boolean deptListIsAllShop(List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return false;
        }
        SysDeptBo bo = new SysDeptBo();
        bo.setDeptIds(deptIdList);
        List<SysDeptVo> sysDeptVos = sysDeptService.selectDeptList(bo);
        return sysDeptVos.stream().noneMatch(dept -> Boolean.FALSE.equals(dept.getIsStore()));
    }


    @Override
    public List<RemoteDeptVo> getDeptChildrenList(Long deptId) {
        List<SysDeptVo> sysDeptVos = sysDeptService.getDeptChildrenList(deptId);
        return MapstructUtils.convert(sysDeptVos, RemoteDeptVo.class);
    }

    @Override
    public Long insertDept(RemoteDeptBo bo) throws ServiceException {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        sysDeptService.insertDept(sysDeptBo);
        return sysDeptBo.getDeptId();
    }

    @Override
    public Long updateDept(RemoteDeptBo bo) throws ServiceException {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        sysDeptService.updateDept(sysDeptBo);
        return sysDeptBo.getDeptId();
    }

    @Override
    public List<RemoteDeptVo> getDeptList(RemoteDeptBo bo) {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        List<SysDeptVo> sysDeptVos = sysDeptService.selectDeptList(sysDeptBo);
        List<RemoteDeptVo> result = MapstructUtils.convert(sysDeptVos, RemoteDeptVo.class);
        log.info("根据ID集合，查询代理商列表，请求参数:{}，转换后的对象：{}，查询结果：{}, 响应的结果：{}", bo, sysDeptBo, sysDeptVos, result);
        return result;
    }

    @Override
    public List<RemoteDeptVo> getDeptListIgnore(RemoteDeptBo bo) {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        List<SysDeptVo> sysDeptVos = DataPermissionHelper.ignore(()->
            sysDeptService.selectDeptList(sysDeptBo));
        return MapstructUtils.convert(sysDeptVos, RemoteDeptVo.class);
    }

    @Override
    public List<Long> getAllParentDeptId(Long deptId) {
        return sysDeptService.getAllParentDeptId(deptId);
    }

    @Override
    public RemoteDeptVo getDeptSystemConfig(Long deptId) {
        SysDeptVo deptSystemConfig = sysDeptService.getDeptSystemConfig(deptId);
        return MapstructUtils.convert(deptSystemConfig, RemoteDeptVo.class);
    }

    @Override
    public RemoteDeptVo selectDeptInfoById(Long createDept) {
        SysDeptVo sysDeptVo = sysDeptService.selectDeptById(createDept);
        return MapstructUtils.convert(sysDeptVo, RemoteDeptVo.class);
    }

    @Override
    public List<RemoteDeptVo> getDeptListByPayMerchantConfigIds(List<Long> payMerchantConfigId) {
        List<SysDeptVo> selfAndChildShopListByDeptIds = sysDeptService.getDeptListByPayMerchantConfigIds(payMerchantConfigId);
        return MapstructUtils.convert(selfAndChildShopListByDeptIds, RemoteDeptVo.class);
    }

    @Override
    public List<RemoteDeptVo> getDeptListByPayMerchantConfigId(Long payMerchantConfigId) {
        if (null == payMerchantConfigId){
            return Collections.emptyList();
        }
        return getDeptListByPayMerchantConfigIds(Collections.singletonList(payMerchantConfigId));
    }

    /**
     * 通过部门Id获取门店Id
     *
     * @param deptId 部门Id
     * @return 门店信息
     */
    @Override
    public RemoteDeptAccountVo selectDeptByIdWithoutCache(Long deptId) {
        return MapstructUtils.convert(sysDeptService.selectDeptByIdWithoutCache(deptId), RemoteDeptAccountVo.class);
    }

    @Override
    public List<RemoteDeptVo> getDeptListWithoutPermission(RemoteDeptBo bo) {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        List<SysDeptVo> sysDeptVos = sysDeptService.selectDeptListWithoutPermission(sysDeptBo);
        return MapstructUtils.convert(sysDeptVos, RemoteDeptVo.class);
    }

    //    @Lock4j(keys = {"#deptId"})
    @Override
    public Boolean transfer(Long deptId, Integer amount) {
        //todo 分布式锁处理
        SysDeptVo sysDeptVo = sysDeptService.selectDeptById(deptId);
        if (ObjectUtils.isEmpty(sysDeptVo)) {
            return false;
        }

        return sysDeptService.transfer(deptId, amount);

    }

    @Override
    public RemoteDeptAccountVo selectDeptById(Long deptId) {
        return MapstructUtils.convert(sysDeptService.selectDeptById(deptId), RemoteDeptAccountVo.class);
    }

    /**
     * 查询多个代理商的集合
     *
     * @param deptIds
     */
    @Override
    public List<RemoteDeptAccountVo> selectDeptByIds(List<Long> deptIds) {
        return sysDeptService.selectDeptByIds(deptIds).stream().map(v -> MapstructUtils.convert(v, RemoteDeptAccountVo.class)).toList();
    }

    /**
     * 通过代理商ID，查询直属上一级代理商信息
     *
     * @param deptId
     */
    @Override
    public RemoteDeptVo getPreDeptByDeptId(Long deptId) {
        SysDeptVo sysDeptVo = sysDeptService.selectPreDeptById(deptId);
        return MapstructUtils.convert(sysDeptVo, RemoteDeptVo.class);
    }

    @Override
    public  List<Tree<Long>> selectDeptTreeList(RemoteDeptBo bo) {
        SysDeptBo sysDeptBo = MapstructUtils.convert(bo, SysDeptBo.class);
        return sysDeptService.selectDeptTreeList(sysDeptBo);
    }

}
