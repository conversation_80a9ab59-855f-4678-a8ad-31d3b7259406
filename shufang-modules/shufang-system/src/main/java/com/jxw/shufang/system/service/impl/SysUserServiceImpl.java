package com.jxw.shufang.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.common.core.constant.CacheNames;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.mybatis.helper.DataBaseHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.system.domain.*;
import com.jxw.shufang.system.domain.bo.SysUserBo;
import com.jxw.shufang.system.domain.bo.SysUserFaceBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.domain.vo.SysPostVo;
import com.jxw.shufang.system.domain.vo.SysRoleVo;
import com.jxw.shufang.system.domain.vo.SysUserVo;
import com.jxw.shufang.system.mapper.*;
import com.jxw.shufang.system.service.ISysUserService;
import com.jxw.shufang.tencentFace.FaceDetectionClient;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.iai.v20200303.models.CreatePersonResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *

 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final FaceDetectionClient faceDetectionClient;

    @DubboReference
    private final RemoteFileService remoteFileService;

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery) {
        QueryWrapper<SysUser> sysUserWrapper = this.buildQueryWrapper(user);
        sysUserWrapper.notIn("u.user_type", UserType.APP_STU_USER.getUserType(), UserType.STAFF_USER.getUserType());
        Page<SysUserVo> page = baseMapper.selectPageUserList(pageQuery.build(), sysUserWrapper);
        List<SysUserVo> records = page.getRecords();
        buildRecordsRole(records);
        return TableDataInfo.build(page);
    }

    private void buildRecordsRole(List<SysUserVo> records) {
        if (ObjectUtil.isEmpty(records)) {
            return;
        }

        List<Long> list = records.stream().map(SysUserVo::getUserId).distinct().toList();

        LambdaQueryWrapper<SysUserRole> lqw = Wrappers.lambdaQuery();
        lqw.in(SysUserRole::getUserId, list);
        List<SysUserRole> sysUserRoles = userRoleMapper.selectList(lqw);
        Map<Long, List<SysUserRole>> userRoleMap = getUserRoleMap(sysUserRoles);

        List<Long> allRoleId = sysUserRoles.stream().map(SysUserRole::getRoleId).distinct().toList();

        QueryWrapper<SysRole> roleLqw = Wrappers.query();
        roleLqw.in("r.role_id", allRoleId);
        List<SysRoleVo> sysRoleVos = roleMapper.selectRoleList(roleLqw);
        Map<Long, SysRoleVo> roleMap = getRoleMap(sysRoleVos);

        for (SysUserVo record : records) {
            List<SysUserRole> sysUserRole = userRoleMap.get(record.getUserId());
            if (ObjectUtil.isEmpty(sysUserRole)) {
                continue;
            }
            List<SysRoleVo> roleVos = new ArrayList<>(sysUserRole.size());
            for (SysUserRole roleVo : sysUserRole) {
                if (!roleMap.containsKey(roleVo.getRoleId())) {
                    continue;
                }
                roleVos.add(roleMap.get(roleVo.getRoleId()));
            }
            if (ObjectUtil.isNotEmpty(roleVos)) {
                record.setRoleNameList(roleVos.stream().map(SysRoleVo::getRoleName).toList());
            }
        }
    }

    private Map<Long, List<SysUserRole>> getUserRoleMap(List<SysUserRole> sysUserRoles) {
        if (ObjectUtil.isEmpty(sysUserRoles)) {
            return MapUtil.newHashMap();
        }
        return sysUserRoles.stream().filter(v -> v.getUserId() != null).collect(Collectors.groupingBy(SysUserRole::getUserId));
    }

    private Map<Long, SysRoleVo> getRoleMap(List<SysRoleVo> sysRoleVos) {
        if (ObjectUtil.isEmpty(sysRoleVos)) {
            return MapUtil.newHashMap();
        }
        return sysRoleVos.stream().collect(Collectors.toMap(SysRoleVo::getRoleId, Function.identity()));
    }


    /**
     * 根据条件查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserList(SysUserBo user) {
        QueryWrapper<SysUser> sysUserWrapper = this.buildQueryWrapper(user);
        List<SysUserVo> sysUserVos = baseMapper.selectUserList(sysUserWrapper);
        sysUserVos.forEach(u -> {
            if (CollUtil.isNotEmpty(u.getRoles())) {
                u.setRoleIds(StreamUtils.toList(u.getRoles(), SysRoleVo::getRoleId).toArray(new Long[0]));
            }
            if (CollUtil.isNotEmpty(u.getPosts())) {
                u.setPostIds(StreamUtils.toList(u.getPosts(), SysPostVo::getPostId).toArray(new Long[0]));
            }
        });
        return sysUserVos;
    }

    /**
     * 根据条件查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<Long> selectUserIdList(SysUserBo user) {
        QueryWrapper<SysUser> sysUserWrapper = this.buildQueryWrapper(user);
        return baseMapper.selectUserIdList(sysUserWrapper);
    }

    @Override
    public List<SysUserVo> selectUserOfRole(Long deptId, Long roleId) {
        List<SysUserVo> sysUserVos = baseMapper.selectUserOfRole(deptId, roleId);
        return CollectionUtils.isEmpty(sysUserVos) ? Lists.newArrayList() : sysUserVos;
    }

    private QueryWrapper<SysUser> buildQueryWrapper(SysUserBo user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        if (CollUtil.isEmpty(user.getStatusList())) {
            wrapper.eq("u.del_flag", UserConstants.USER_NORMAL);
        } else {
            wrapper.in("u.del_flag", user.getStatusList());
        }

        wrapper.in(CollUtil.isNotEmpty(user.getUserIds()), "u.user_id", user.getUserIds())
            .in(CollUtil.isNotEmpty(user.getUserNames()), "u.user_name", user.getUserNames())
            .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .between(params.get("beginTime") != null && params.get("endTime") != null,
                "u.create_time", params.get("beginTime"), params.get("endTime"))
            .in(user.getRoleIds() != null && user.getRoleIds().length > 0, "sur.role_id", Arrays.asList(ObjectUtil.defaultIfNull(user.getRoleIds(), new Long[0])))
            .and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                    .select(SysDept::getDeptId)
                    .apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(user.getDeptId());
                w.in("u.dept_id", ids);
            }).orderByAsc("u.user_id");
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectAllocatedPageList(SysUserBo user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectAllocatedPageList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectAllocatedList(SysUserBo user) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getNickName()), "u.nick_name", user.getNickName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber());
        return baseMapper.selectAllocatedList(wrapper);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectUnallocatedList(SysUserBo user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id"))
            .notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds)
            .like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName())
            .like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectUnallocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByUserName(String userName) {
        return baseMapper.selectUserByUserName(userName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectUserByPhonenumber(phonenumber);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserById(Long userId) {
        return baseMapper.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<SysRoleVo> list = roleMapper.selectRolesByUserName(userName);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(String userName) {
        List<SysPostVo> list = postMapper.selectPostsByUserName(userName);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPostVo::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getUserName, user.getUserName())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getPhonenumber, user.getPhonenumber())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户ID
     */
    @Override
    public void checkUserAllowed(Long userId) {
        if (ObjectUtil.isNotNull(userId) && LoginHelper.isSuperAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        if (ObjectUtil.isNull(baseMapper.selectUserById(userId))) {
            throw new ServiceException("没有权限访问用户数据！");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUserBo user) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 新增用户信息
        int rows = baseMapper.insert(sysUser);
        user.setUserId(sysUser.getUserId());
        // 新增用户岗位关联
        insertUserPost(user, false);
        // 新增用户与角色管理
        insertUserRole(user, false);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUserBo user, String tenantId) {
        user.setCreateBy(user.getUserId());
        user.setUpdateBy(user.getUserId());
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        sysUser.setTenantId(tenantId);
        return baseMapper.insert(sysUser) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    public int updateUser(SysUserBo user) {
        SysUserVo sysUserVo = baseMapper.selectUserById(user.getUserId());
        Long newDeptId = user.getDeptId();
        Long oldDeptId = sysUserVo.getDeptId();

        if (Objects.nonNull(oldDeptId)&&Objects.nonNull(newDeptId)) {
            if (!oldDeptId.equals(newDeptId)) {
                //更换上级，判断之前是否是负责人，是负责人则清空
                SysDeptVo sysDeptVo = deptMapper.selectDeptById(oldDeptId);
                if (!ObjectUtils.isEmpty(sysDeptVo)) {
                    if (Objects.nonNull(sysDeptVo.getLeader())&&sysDeptVo.getLeader().equals(user.getUserId())) {
                        sysDeptVo.setLeader(null);
                        LambdaUpdateWrapper<SysDept> updateWrapper = Wrappers.lambdaUpdate(SysDept.class);
                        updateWrapper.eq(SysDept::getDeptId, sysDeptVo.getDeptId());
                        updateWrapper.set(SysDept::getLeader, null);
                        deptMapper.update(updateWrapper);
                    }
                }
            }
        }

        // 新增用户与角色管理
        insertUserRole(user, true);
        // 新增用户与岗位管理
        insertUserPost(user, true);
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 防止错误更新后导致的数据误删除
        int flag = baseMapper.updateById(sysUser);
        if (flag < 1) {
            throw new ServiceException("修改用户" + user.getUserName() + "信息失败");
        }
        forceOffline(user.getUserId());
        return flag;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        insertUserRole(userId, roleIds, true);
    }

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 帐号状态
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserStatus(Long userId, String status) {
        int update = baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getStatus, status)
                .eq(SysUser::getUserId, userId));
        forceOffline(userId);
        return update;
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    public int updateUserProfile(SysUserBo user) {
        int update = baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(ObjectUtil.isNotNull(user.getNickName()), SysUser::getNickName, user.getNickName())
                .set(SysUser::getPhonenumber, user.getPhonenumber())
                .set(SysUser::getEmail, user.getEmail())
                .set(SysUser::getSex, user.getSex())
                .eq(SysUser::getUserId, user.getUserId()));
        if (update < 1) {
            throw new ServiceException("修改用户信息失败");
        }
        forceOffline(user.getUserId());
        return update;

    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(Long userId, Long avatar) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getAvatar, avatar)
                .eq(SysUser::getUserId, userId)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(Long userId, String password) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getPassword, password)
                .eq(SysUser::getUserId, userId));
    }

    /**
     * 新增用户角色信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserRole(SysUserBo user, boolean clear) {
        this.insertUserRole(user.getUserId(), user.getRoleIds(), clear);
    }

    /**
     * 新增用户岗位信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserPost(SysUserBo user, boolean clear) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            if (clear) {
                // 删除用户与岗位关联
                userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, user.getUserId()));
            }
            // 新增用户与岗位管理
            List<SysUserPost> list = StreamUtils.toList(Arrays.asList(posts), postId -> {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                return up;
            });
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     * @param clear   清除已存在的关联数据
     */
    private void insertUserRole(Long userId, Long[] roleIds, boolean clear) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            // 判断是否具有此角色的操作权限
            List<SysRoleVo> roles = roleMapper.queryRoleList(new LambdaQueryWrapper<>());
            if (CollUtil.isEmpty(roles)) {
                throw new ServiceException("没有权限访问角色的数据");
            }
            List<Long> roleList = StreamUtils.toList(roles, SysRoleVo::getRoleId);
            if (!LoginHelper.isSuperAdmin(userId)) {
                roleList.remove(UserConstants.SUPER_ADMIN_ID);
            }
            List<Long> canDoRoleList = StreamUtils.filter(Arrays.asList(roleIds), roleList::contains);
            if (CollUtil.isEmpty(canDoRoleList)) {
                throw new ServiceException("没有权限访问角色的数据");
            }
            if (clear) {
                // 删除用户与角色关联
                userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
            }
            // 新增用户与角色管理
            List<SysUserRole> list = StreamUtils.toList(canDoRoleList, roleId -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                return ur;
            });
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteById(userId);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId);
        }
        List<Long> ids = Arrays.asList(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteBatchIds(ids);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        for (Long userId : userIds) {
            forceOffline(userId);
        }
        return flag;
    }

    /**
     * 通过部门id查询当前部门所有用户
     *
     * @param deptId
     * @return
     */
    @Override
    public List<SysUserVo> selectUserListByDept(Long deptId) {
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUser::getDeptId, deptId);
        lqw.orderByAsc(SysUser::getUserId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SysUserVo> getBranchAdmin(Set<Long> branchDeptIds, Long storeAdminRoleId) {
        return baseMapper.getBranchAdmin(branchDeptIds, storeAdminRoleId);
    }

    @Override
    public void forceOffline(Long userId) {
        SysUserVo sysUser = baseMapper.getByIdContainsDel(userId);
        String userType = sysUser.getUserType();
        String loginId = userType + ":" + userId;
        StpUtil.kickout(loginId);
    }

    @Override
    public List<SysUserVo> queryUserList(SysUserBo user) {
        QueryWrapper<SysUser> sysUserWrapper = this.buildQueryWrapper(user);
        sysUserWrapper.notIn("u.user_type", UserType.APP_STU_USER.getUserType(), UserType.STAFF_USER.getUserType());
        List<SysUserVo> sysUserVos = baseMapper.selectUserList(sysUserWrapper);
        sysUserVos.forEach(u -> {
            if (CollUtil.isNotEmpty(u.getRoles())) {
                u.setRoleIds(StreamUtils.toList(u.getRoles(), SysRoleVo::getRoleId).toArray(new Long[0]));
            }
            if (CollUtil.isNotEmpty(u.getPosts())) {
                u.setPostIds(StreamUtils.toList(u.getPosts(), SysPostVo::getPostId).toArray(new Long[0]));
            }
        });
        return sysUserVos;

    }

    @Override
    public List<SysUserVo> selectSysAndStaffUserList() {
        return baseMapper.selectSysAndStaffUserList();
    }

    @Override
    public List<SysUserVo> queryUserByNickNames(List<String> nickNames, Long deptId) {
        if(CollectionUtil.isEmpty(nickNames)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getDelFlag, UserConstants.USER_NORMAL);
        queryWrapper.in(SysUser::getNickName, nickNames);
        if (ObjectUtil.isNotNull(deptId)) {
            queryWrapper.eq(SysUser::getDeptId, deptId);
        }
        return baseMapper.selectVoList(queryWrapper);
    }

    @Override
    public void bindFace(Long userId, SysUserFaceBo sysUserFaceBo) {
        SysUserVo sysUserVo = selectUserById(userId);
        if (sysUserVo.isFaceBound()) {
            throw new ServiceException("当前用户已录入人脸，请勿重复录入。");
        }
        try {
            String faceImgUrl = remoteFileService.selectUrlByIds(String.valueOf(sysUserFaceBo.getFaceId()));
            CreatePersonResponse createPersonResponse =
                faceDetectionClient.createPerson(userId.toString(), sysUserVo.getNickName(), faceImgUrl);
            // 当前添加的人脸是否有匹配，目前不支持一人脸多账户，如果有相似人脸则添加失败
            if (StringUtils.isNotBlank(createPersonResponse.getSimilarPersonId())) {
                log.error("添加人脸失败，人员库中存在相同人脸，用户id：{}，相似personId:{}", sysUserVo.getUserId(),
                    createPersonResponse.getSimilarPersonId());
                SysUserVo similarUser;
                if (isNumeric(createPersonResponse.getSimilarPersonId()) && (similarUser =
                    selectUserById(Long.valueOf(createPersonResponse.getSimilarPersonId()))) != null) {
                    log.error("相似人员账号：{}", similarUser.getUserName());
                    throw new ServiceException(
                        "添加人脸失败，系统中当前用户人脸已绑定其他账户，相似账户信息：" + maskPhone(similarUser.getUserName()));
                } else {
                    throw new ServiceException("添加人脸失败");
                }
            }
            if (!updateBindFace(sysUserVo.getUserId(), true, sysUserFaceBo.getFaceId())) {
                log.error("添加人脸出错，用户数据更新异常");
                throw new ServiceException("添加人脸失败");
            }
        } catch (TencentCloudSDKException e) {
            log.error("创建人员出错", e);
            throw new ServiceException("添加人脸失败");
        }
    }

    @Override
    public void unbindFace(Long userId) {
        SysUserVo sysUserVo = selectUserById(userId);
        if (!sysUserVo.isFaceBound()) {
            throw new ServiceException("当前用户已尚未录入人脸，不能解绑。");
        }
        try {
            faceDetectionClient.deletePersonFromGroup(userId.toString());
            if (!updateBindFace(sysUserVo.getUserId(), false, null)) {
                log.error("解绑人脸出错，用户数据更新异常");
                throw new ServiceException("删除人脸失败");
            }
        } catch (TencentCloudSDKException e) {
            log.error("删除人脸失败", e);
            throw new ServiceException("删除人脸失败");
        }
    }

    /**
     * 屏蔽手机号信息
     *
     * @param phone
     * @return
     */
    public static String maskPhone(String phone) {
        if (phone == null || phone.length() < 8) {
            return phone;
        }
        return phone.substring(0, 3) + "xxxx" + phone.substring(7);
    }

    /**
     * 是否是数字
     *
     * @param str
     * @return
     */
    private static boolean isNumeric(String str) {
        return str != null && str.matches("\\d+");
    }

    public boolean updateBindFace(Long userId, boolean faceBound, Long faceOssId) {
        return baseMapper.update(
            Wrappers.lambdaUpdate(SysUser.class).eq(SysUser::getUserId, userId).set(SysUser::getFaceBound,
                faceBound).set(SysUser::getFace, faceBound ? faceOssId : null)) > 0;
    }

    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getUserName();
    }

    @Override
    @Cacheable(cacheNames = CacheNames.SYS_NICKNAME, key = "#userId")
    public String selectNicknameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getNickName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getNickName();
    }

}
