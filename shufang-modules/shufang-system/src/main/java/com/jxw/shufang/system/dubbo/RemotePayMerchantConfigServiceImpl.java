package com.jxw.shufang.system.dubbo;

import com.jxw.shufang.system.api.RemotePayMerchantConfigService;
import com.jxw.shufang.system.api.domain.vo.RemotePayMerchantConfig;
import com.jxw.shufang.system.domain.PayMerchantConfig;
import com.jxw.shufang.system.service.IPayMerchantConfigService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
@DubboService
public class RemotePayMerchantConfigServiceImpl implements RemotePayMerchantConfigService {

    private final IPayMerchantConfigService payMerchantConfigService;

    @Override
    public String getCommonPayAppId(Long deptId) {
        PayMerchantConfig configByDeptId = payMerchantConfigService.getConfigByDeptId(deptId);
        return null == configByDeptId ? null : (configByDeptId.getEnable() ? configByDeptId.getAppId() : null);
    }

    @Override
    public RemotePayMerchantConfig getCommonPayConfig(Long deptId) {
        PayMerchantConfig configByDeptId = payMerchantConfigService.getConfigByDeptId(deptId);
        if (null == configByDeptId) {
            return null;
        }
        RemotePayMerchantConfig remotePayMerchantConfig = new RemotePayMerchantConfig();
        remotePayMerchantConfig.setMerchantName(configByDeptId.getMerchantName());
        remotePayMerchantConfig.setAppId(configByDeptId.getAppId());
        remotePayMerchantConfig.setAppName(configByDeptId.getAppName());
        remotePayMerchantConfig.setPayType(configByDeptId.getPayType());
        remotePayMerchantConfig.setPayCode(configByDeptId.getPayCode());
        remotePayMerchantConfig.setWayCode(configByDeptId.getWayCode());
        remotePayMerchantConfig.setDefaultMerchant(configByDeptId.getDefaultMerchant());
        remotePayMerchantConfig.setConfigParamJson(configByDeptId.getConfigParamJson());
        return remotePayMerchantConfig;
    }
}
