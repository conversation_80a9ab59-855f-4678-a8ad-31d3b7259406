package com.jxw.shufang.system.service;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.bo.SysConfigBo;
import com.jxw.shufang.system.domain.vo.SysConfigVo;

import java.util.List;

/**
 * 参数配置 服务层
 *

 */
public interface ISysConfigService {


    TableDataInfo<SysConfigVo> selectPageConfigList(SysConfigBo config, PageQuery pageQuery);

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    SysConfigVo selectConfigById(Long configId);

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数键名
     * @return 参数键值
     */
    String selectConfigByKey(String configKey);

    /**
     * 获取注册开关
     * @param tenantId 租户id
     * @return true开启，false关闭
     */
    boolean selectRegisterEnabled(String tenantId);

    /**
     * 选择会员过期时间
     *
     *
     * @date 2024/03/08 05:53:59
     */
    Integer selectStudentExpireTime();

    /**
     * 编辑会员过期时间
     *
     * @param expireDays 过期天数
     *
     * @date 2024/03/11 11:03:39
     */
    void editStudentExpireTime(Integer expireDays) throws ServiceException;


    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    List<SysConfigVo> selectConfigList(SysConfigBo config);

    /**
     * 新增参数配置
     *
     * @param bo 参数配置信息
     * @return 结果
     */
    String insertConfig(SysConfigBo bo);

    /**
     * 修改参数配置
     *
     * @param bo 参数配置信息
     * @return 结果
     */
    String updateConfig(SysConfigBo bo);

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    void deleteConfigByIds(Long[] configIds);

    /**
     * 重置参数缓存数据
     */
    void resetConfigCache();

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数信息
     * @return 结果
     */
    boolean checkConfigKeyUnique(SysConfigBo config);


    /**
     * 选择课程详细信息分隔符
     *
     *
     * @date 2024/04/09 04:59:49
     */
    String selectCourseDetailSeparator();


    /**
     * 获取属性填写值分隔符
     *
     *
     * @date 2024/03/08 05:54:49
     */
    String selectAttrValueSeparator();


    /**
     * 选择短信captcha模板id
     *
     *
     * @date 2024/04/18 05:44:46
     */
    String selectSmsCaptchaTemplateId();

    /**
     * 获取同手机号 24小时内发送短信验证码最大次数，默认10次
     *
     *
     * @date 2024/04/18 06:36:17
     */
    Integer select24hSmsCaptchaMaxCount();

    /**
     * 24小时内连续错误多少次以后不可以接着使用的配置次数,默认5次
     *
     *
     * @date 2024/04/18 06:36:19
     */
    Integer select24hSmsCaptchaErrorMaxCount();

    /**
     * 获取短信验证码有效期,单位秒，默认300秒
     */
    Long selectSmsCaptchaExpireTime();

    /**
     * 查询打印记录过期时间,默认30分钟
     *
     *
     * @date 2024/05/08 12:23:38
     */
    Integer selectPrintRecordExpireTime();

    /**
     * 查询crm会员过期前多少天提醒
     * @return
     */
    Integer selectStudentExpireWarningDays();
}
