package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.AttributeBo;
import com.jxw.shufang.system.domain.vo.AttributeVo;
import com.jxw.shufang.system.service.IAttributeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 属性
 * 前端访问路由地址为:/system/attribute
 *
 * @date 2024-03-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/attribute")
public class AttributeController extends BaseController {

    private final IAttributeService attributeService;

    /**
     * 查询属性列表
     */
    @SaCheckPermission("system:attribute:list")
    @GetMapping("/list")
    public TableDataInfo<AttributeVo> list(AttributeBo bo, PageQuery pageQuery) {
        return attributeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出属性列表
     */
    @SaCheckPermission("system:attribute:export")
    @Log(title = "属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AttributeBo bo, HttpServletResponse response) {
        List<AttributeVo> list = attributeService.queryList(bo);
        ExcelUtil.exportExcel(list, "属性", AttributeVo.class, response);
    }

    /**
     * 获取属性详细信息
     *
     * @param attributeId 主键
     */
    @SaCheckPermission("system:attribute:query")
    @GetMapping("/{attributeId}")
    public R<AttributeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long attributeId) {
        return R.ok(attributeService.queryById(attributeId));
    }

    /**
     * 新增属性
     */
    @SaCheckPermission("system:attribute:add")
    @Log(title = "属性", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AttributeBo bo) {
        return toAjax(attributeService.insertByBo(bo));
    }

    /**
     * 修改属性
     */
    @SaCheckPermission("system:attribute:edit")
    @Log(title = "属性", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AttributeBo bo) {
        return toAjax(attributeService.updateByBo(bo));
    }

    /**
     * 删除属性
     *
     * @param attributeIds 主键串
     */
    @SaCheckPermission("system:attribute:remove")
    @Log(title = "属性", businessType = BusinessType.DELETE)
    @DeleteMapping("/{attributeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] attributeIds) {
        return toAjax(attributeService.deleteWithValidByIds(List.of(attributeIds), true));
    }

    /**
     * 获取对应type的动态查询条件
     *
     * @param type 表名
     * @param showLocation 显示的位置
     */
    @GetMapping("/searchAttr/{type}")
    public R<List<AttributeVo>> querySearchAttr(@NotBlank(message = "类型不能为空")
                                  @PathVariable String type,String showLocation) {
        if (LoginHelper.isBranchUser()) {
            return R.ok(attributeService.querySearchAttr(type,showLocation,LoginHelper.getBranchId(),true));
        }else {
            return R.ok(attributeService.querySearchAttr(type,showLocation,-1L,true));
        }
    }

    /**
     * 获取对应type的动态查询条件
     *
     * @param type 表名
     * @param showLocation 显示的位置
     */
    @GetMapping("/addAttr/{type}")
    public R<List<AttributeVo>> queryAddAttr(@NotBlank(message = "类型不能为空")
                                                @PathVariable String type,String showLocation) {
        if (LoginHelper.isBranchUser()) {
            return R.ok(attributeService.queryAddAttr(type,showLocation,LoginHelper.getBranchId(),true));
        }else {
            return R.ok(attributeService.queryAddAttr(type,showLocation,-1L,true));
        }
    }



}
