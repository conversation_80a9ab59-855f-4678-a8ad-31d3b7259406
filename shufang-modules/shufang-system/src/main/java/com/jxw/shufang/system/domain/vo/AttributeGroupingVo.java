package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.system.domain.AttributeGrouping;

import java.io.Serial;
import java.io.Serializable;


/**
 * 属性分组视图对象 attribute_grouping
 *
 * @date 2024-03-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AttributeGrouping.class)
public class AttributeGroupingVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性组ID
     */
    @ExcelProperty(value = "属性组ID")
    private Long attributeGroupingId;

    /**
     * 组名
     */
    @ExcelProperty(value = "组名")
    private String attributeGroupingName;

    /**
     * 分店id（为-1时所有分店可以使用）
     */
    @ExcelProperty(value = "分店id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "为=-1时所有分店可以使用")
    private Long branchId;

    /**
     * 表名
     */
    @ExcelProperty(value = "表名")
    private String type;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 属性数量
     */
    @ExcelProperty(value = "属性数量")
    private Integer attributeCount;

    /**
     * 分店
     */
    private RemoteBranchVo branch;


}
