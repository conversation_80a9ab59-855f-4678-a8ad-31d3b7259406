package com.jxw.shufang.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.domain.AttributeGrouping;
import com.jxw.shufang.system.domain.vo.AttributeGroupingVo;

import java.util.List;

/**
 * 属性分组Mapper接口
 *
 * @date 2024-03-26
 */
public interface AttributeGroupingMapper extends BaseMapperPlus<AttributeGrouping, AttributeGroupingVo> {


    /**
     * 分页查询属性组列表
     *
     * @param page 页
     * @param lqw  查询条件
     */
    //@DataPermission({
    //    @DataColumn(key = "deptName", value = "create_dept")
    //})
    Page<AttributeGroupingVo> selectAttrGroupPage(@Param("page") Page<AttributeGrouping> page,@Param(Constants.WRAPPER) LambdaQueryWrapper<AttributeGrouping> lqw);

    /**
     * 查询属性组列表
     *
     * @param typeId
     * @param type
     * @return
     */
    List<CourseAttributeDetailDTO> queryCourseAttributeDetail(@Param("typeIds") List<Long> typeId, @Param("type") String type);
}
