package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 属性关系对象 attribute_relation
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attribute_relation")
public class AttributeRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 属性ID
     */
    private Long attributeId;

    /**
     * 类型（直接填表名）
     */
    private String type;

    /**
     * 类型ID
     */
    private Long typeId;

    /**
     * 值（|||隔开）
     */
    private String value;


}
