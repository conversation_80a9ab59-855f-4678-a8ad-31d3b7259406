package com.jxw.shufang.system.dubbo;

import com.jxw.shufang.system.api.RemoteNotifyMessageService;
import com.jxw.shufang.system.api.domain.bo.RemoteNotifyMessageBo;
import com.jxw.shufang.system.factory.NotifyMessageFactory;
import com.jxw.shufang.system.service.NotifyMessageStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteNotifyMessageServiceImpl implements RemoteNotifyMessageService {

    private final NotifyMessageFactory notifyMessageFactory;

    @Override
    public boolean sendMessage(RemoteNotifyMessageBo messageBo) {
        NotifyMessageStrategy notifyMessageStrategy = notifyMessageFactory.getStrategy(messageBo.getNoticeType());
        if (Objects.isNull(notifyMessageStrategy)) {
            log.error("通知消息类型异常，无法发送{}", messageBo);
            return false;
        }
        return notifyMessageStrategy.sendMessage(messageBo);
    }

}
