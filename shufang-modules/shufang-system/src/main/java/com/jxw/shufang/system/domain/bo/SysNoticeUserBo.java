package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.SysNoticeUser;

/**
 * noticeUser业务对象 sys_notice_user
 *
 * @date 2024-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysNoticeUser.class, reverseConvertGenerate = false)
public class SysNoticeUserBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long noticeUserId;

    /**
     * 接收人用户ID
     */
    @NotNull(message = "接收人用户ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long userId;

    /**
     * 读取状态（0已读 2未读）
     */
    @NotBlank(message = "读取状态（0已读 2未读）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String readStatus;

    /**
     * 公告ID
     */
    @NotNull(message = "公告ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long noticeId;

    /**
     * 查询公告信息，不携带公告主体内容
     */
    private Boolean withNoticeInfo;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String noticeStatus;


}
