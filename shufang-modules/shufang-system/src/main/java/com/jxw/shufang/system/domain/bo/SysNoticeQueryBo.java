package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SysNoticeQueryBo extends BaseEntity {

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告类型（1站内 2产研 3教研）
     */
    private String noticeType;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 操作人员id
     */
    private Long createBy;

    /**
     * 操作人员
     */
    private String createByName;

    /**
     * 状态 0-正常 1-撤回
     */
    private String status;

    /**
     * 是否是超级管理员 1-是 0-否
     */
    private Integer isAdmin = 0;

    /**
     * 组织id列表
     */
    private List<Long> toDeptList;

    /**
     * 拥有者id
     */
    private Long ownerId;

}
