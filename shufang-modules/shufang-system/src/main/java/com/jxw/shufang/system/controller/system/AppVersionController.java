package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.extresource.api.domain.vo.RemoteAppInfoVo;
import com.jxw.shufang.system.domain.vo.AppVersionV2Vo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.AppVersionBo;
import com.jxw.shufang.system.domain.vo.AppVersionVo;
import com.jxw.shufang.system.service.IAppVersionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * APP版本管理
 * 前端访问路由地址为:/system/version
 *
 * @date 2024-07-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/version")
public class AppVersionController extends BaseController {

    private final IAppVersionService appVersionService;

    /**
     * 查询APP版本管理列表
     */
    @SaCheckPermission("system:version:list")
    @GetMapping("/list")
    public TableDataInfo<AppVersionVo> list(AppVersionBo bo, PageQuery pageQuery) {
        return appVersionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出APP版本管理列表
     */
    @SaCheckPermission("system:version:export")
    @Log(title = "APP版本管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AppVersionBo bo, HttpServletResponse response) {
        List<AppVersionVo> list = appVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "APP版本管理", AppVersionVo.class, response);
    }

    /**
     * 获取APP版本管理详细信息
     *
     * @param appVersionId 主键
     */
    @SaCheckPermission("system:version:query")
    @GetMapping("/{appVersionId}")
    public R<AppVersionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long appVersionId) {
        return R.ok(appVersionService.queryById(appVersionId));
    }

    /**
     * 新增APP版本管理
     */
    @SaCheckPermission("system:version:add")
    @Log(title = "APP版本管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AppVersionBo bo) {
        return toAjax(appVersionService.insertByBo(bo));
    }

    /**
     * 修改APP版本管理
     */
    @SaCheckPermission("system:version:edit")
    @Log(title = "APP版本管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AppVersionBo bo) {
        return toAjax(appVersionService.updateByBo(bo));
    }

    /**
     * 删除APP版本管理
     *
     * @param appVersionIds 主键串
     */
    @SaCheckPermission("system:version:remove")
    @Log(title = "APP版本管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{appVersionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] appVersionIds) {
        return toAjax(appVersionService.deleteWithValidByIds(List.of(appVersionIds), true));
    }

    /**
     *  获取最新发布的app信息
     */
    @GetMapping("/latestVerInfo")
    public R<AppVersionVo> latestVerInfo() {
        return R.ok(appVersionService.queryLatestVerInfo());
    }


    /**
     *  获取最新发布的app信息 支持尾标和SN号
     *  不传默认查询最新的app信息
     * @param sn 不传默认是-1，保证可以访问老平台的接口
     */
    @GetMapping("/latestVerInfo/v2")
    public R<AppVersionV2Vo> latestVerInfoV2(@RequestParam(defaultValue = "xwsfos") String extraIsbn,
                                           @RequestParam(defaultValue = "-1") String sn) {
        return R.ok(appVersionService.queryLatestVerInfoV2(extraIsbn,sn));
    }
}
