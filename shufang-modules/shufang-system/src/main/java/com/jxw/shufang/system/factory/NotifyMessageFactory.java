package com.jxw.shufang.system.factory;

import com.jxw.shufang.system.api.enums.NoticeTypeEnum;
import com.jxw.shufang.system.service.NotifyMessageStrategy;
import com.jxw.shufang.system.service.impl.InternalNotifyMessageStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class NotifyMessageFactory {

    private final InternalNotifyMessageStrategy internalNotifyMessageStrategy;

    public NotifyMessageStrategy getStrategy(Integer notifyType) {
        NoticeTypeEnum typeEnum = NoticeTypeEnum.getEnum(notifyType);
        if (NoticeTypeEnum.INTERNAL.equals(typeEnum)) {
            return internalNotifyMessageStrategy;
        }
        return null;
    }

}
