package com.jxw.shufang.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.api.enums.NoticeBizTypeEnum;
import com.jxw.shufang.system.domain.NotifyMessage;
import com.jxw.shufang.system.domain.NotifyMessageItem;
import com.jxw.shufang.system.domain.NotifyTemplate;
import com.jxw.shufang.system.domain.bo.NoticeMessageBo;
import com.jxw.shufang.system.domain.vo.NoticeBizTypeVo;
import com.jxw.shufang.system.domain.vo.NoticeMessageVo;
import com.jxw.shufang.system.domain.vo.NotifyTemplateVo;
import com.jxw.shufang.system.mapper.NotifyMessageItemMapper;
import com.jxw.shufang.system.mapper.NotifyMessageMapper;
import com.jxw.shufang.system.mapper.NotifyTemplateMapper;
import com.jxw.shufang.system.service.INotifyMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class NotifyMessageServiceImpl implements INotifyMessageService {

    private final NotifyTemplateMapper notifyTemplateMapper;
    private final NotifyMessageMapper notifyMessageMapper;
    private final NotifyMessageItemMapper notifyMessageItemMapper;

    @Override
    public NotifyTemplateVo getNotifyTemplateVo(String templateCode) {
        LambdaQueryWrapper<NotifyTemplate> wrapper = Wrappers.<NotifyTemplate>lambdaQuery()
                .eq(NotifyTemplate::getDelFlag, 0)
                .eq(NotifyTemplate::getStatus, 1)
                .eq(NotifyTemplate::getCode, templateCode);
        return notifyTemplateMapper.selectVoOne(wrapper, NotifyTemplateVo.class);
    }

    @Override
    public int saveNotifyMessage(NotifyMessage notifyMessage) {
        return notifyMessageMapper.insert(notifyMessage);
    }

    @Override
    public int saveNotifyMessageItem(NotifyMessageItem notifyMessageItem) {
        return notifyMessageItemMapper.insert(notifyMessageItem);
    }

    @Override
    public TableDataInfo<NoticeMessageVo> queryNoticeMessage(NoticeMessageBo messageBo) {
        Long userId = LoginHelper.getUserId();
        if (Objects.isNull(userId)) {
            log.error("通知中心查询异常{}", messageBo);
            return TableDataInfo.build();
        }

        messageBo.setToUserId(userId);
        IPage<NoticeMessageVo> page = new Page<>(messageBo.getPageNum(), messageBo.getPageSize());
        List<NoticeMessageVo> noticeMessageVos = notifyMessageMapper.selectNoticeMessageList(page, messageBo);
        if (CollectionUtils.isNotEmpty(noticeMessageVos)) {
            Set<Integer> templateIds = noticeMessageVos.stream().map(NoticeMessageVo::getTemplateId).collect(Collectors.toSet());
            Map<Integer, NotifyTemplate> templateMap = notifyTemplateMapper.selectBatchIds(templateIds)
                    .stream().collect(Collectors.toMap(NotifyTemplate::getId, Function.identity()));
            noticeMessageVos.forEach(vo -> {
                vo.setLinkUrlList(getLinkUrlList(vo, templateMap));
                String processUrl = getProcessUrl(vo, templateMap);
                if (StringUtils.isBlank(processUrl)) {
                    if (CollectionUtils.isNotEmpty(vo.getLinkUrlList())) {
                        vo.setProcessUrl(vo.getLinkUrlList().get(vo.getLinkUrlList().size() - 1));
                    }
                } else {
                    vo.setProcessUrl(processUrl);
                }
            });
            return TableDataInfo.build(noticeMessageVos, page.getTotal());
        }

        return TableDataInfo.build();
    }

    @Override
    public List<NoticeBizTypeVo> getBizTypeList() {
        List<NoticeBizTypeVo> list = Lists.newArrayList();
        for (NoticeBizTypeEnum noticeBizTypeEnum : NoticeBizTypeEnum.values()) {
            NoticeBizTypeVo noticeBizTypeVo = new NoticeBizTypeVo();
            noticeBizTypeVo.setCode(noticeBizTypeEnum.getCode());
            noticeBizTypeVo.setName(noticeBizTypeEnum.getDesc());
            list.add(noticeBizTypeVo);
        }
        return list;
    }

    @Transactional
    @Override
    public boolean updateReadStatus(List<Integer> messageIds, Long toUserId) {
        LambdaQueryWrapper<NotifyMessageItem> wrapper = Wrappers.lambdaQuery();
        if (CollectionUtils.isNotEmpty(messageIds)) {
            wrapper.in(NotifyMessageItem::getMessageId, messageIds);
        }
        if (Objects.nonNull(toUserId)) {
            wrapper.eq(NotifyMessageItem::getToUserId, toUserId);
            wrapper.eq(NotifyMessageItem::getReadTime, 0);
        }
        List<NotifyMessageItem> itemList = notifyMessageItemMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(itemList)) {
            return false;
        }
        itemList.forEach(item -> item.setReadTime(new Date().getTime()));
        notifyMessageItemMapper.updateBatchById(itemList);
        return true;
    }

    @Override
    public TableDataInfo<NoticeMessageVo> queryTodayNoticeMessage(Long toUserId, Integer pageNum, Integer pageSize) {
        log.info("【书房站内信】查询用户{}的通知消息", toUserId);
        IPage<NoticeMessageVo> page = new Page<>(pageNum, pageSize);
        Page<NoticeMessageVo> noticeMessagePage = notifyMessageMapper.selectTodayNoticeMessage(page, toUserId, LocalDate.now(), null);
        if (CollectionUtils.isEmpty(noticeMessagePage.getRecords())) {
            return TableDataInfo.build(noticeMessagePage);
        }

        List<NoticeMessageVo> noticeMessageVoList = noticeMessagePage.getRecords();
        List<Integer> templateIdList = noticeMessageVoList.stream().map(NoticeMessageVo::getTemplateId).toList();
        Map<Integer, NotifyTemplate> templateMap = notifyTemplateMapper.selectBatchIds(templateIdList)
                .stream().collect(Collectors.toMap(NotifyTemplate::getId, Function.identity()));

        noticeMessageVoList.forEach(vo -> {
            NotifyTemplate template = templateMap.get(vo.getTemplateId());
            if (Objects.nonNull(template) && StringUtils.isNotBlank(vo.getParams())) {
                vo.setLinkUrlList(getLinkUrlList(vo, templateMap));
                vo.setContent(replaceATagsWithSpan(buildMessageContent(template, JSONObject.parseObject(vo.getParams()))));
            }
        });
        return TableDataInfo.build(noticeMessageVoList, page.getTotal());
    }

    @Override
    public long getUnreadMessageCount(Long userId) {
        IPage<NoticeMessageVo> page = new Page<>(1, 1);
        Page<NoticeMessageVo> noticeMessageVoPage = notifyMessageMapper.selectTodayNoticeMessage(page, userId, LocalDate.now(), 0);
        return noticeMessageVoPage.getTotal();
    }

    private String buildMessageContent(NotifyTemplate template, Map<String, Object> paramMap) {
        return StrUtil.format(template.getContent(), paramMap);
    }

    // 共用的正则表达式常量
    private static final Pattern A_TAG_PATTERN = Pattern.compile("<a\\s+([^>]{0,1000})>");
    private static final Pattern HREF_PATTERN = Pattern.compile("href=(['\"])([^'\"]*)['\"]");

    private List<String> getLinkUrlList(NoticeMessageVo vo, Map<Integer, NotifyTemplate> templateMap) {
        String content = getProcessedContent(vo, templateMap);
        if (content == null) {
            return null;
        }
        return extractAllHrefAttributes(content);
    }

    private String getProcessUrl(NoticeMessageVo vo, Map<Integer, NotifyTemplate> templateMap) {
        NotifyTemplate template = templateMap.get(vo.getTemplateId());
        if (Objects.isNull(template)) {
            return null;
        }

        Map<String, Object> paramMap = parseParams(vo.getParams());
        String content = template.getContent();

        Matcher matcher = A_TAG_PATTERN.matcher(content);
        while (matcher.find()) {
            String aTagAttributes = matcher.group(1);

            // 检查是否包含process属性
            if (aTagAttributes.contains("process")) {
                String hrefValue = extractHrefFromAttributes(aTagAttributes);
                if (hrefValue != null) {
                    // 如果有参数，则进行参数替换
                    if (paramMap != null && !paramMap.isEmpty()) {
                        hrefValue = StrUtil.format(hrefValue, paramMap);
                    }
                    return hrefValue;
                }
            }
        }
        return null;
    }

    /**
     * 获取处理后的内容（包含参数替换）
     */
    private String getProcessedContent(NoticeMessageVo vo, Map<Integer, NotifyTemplate> templateMap) {
        NotifyTemplate template = templateMap.get(vo.getTemplateId());
        if (Objects.isNull(template)) {
            return null;
        }

        Map<String, Object> paramMap = parseParams(vo.getParams());
        if (paramMap != null && !paramMap.isEmpty()) {
            return buildMessageContent(template, paramMap);
        }
        return template.getContent();
    }

    /**
     * 解析参数字符串为Map
     */
    private Map<String, Object> parseParams(String params) {
        if (StringUtils.isNotBlank(params)) {
            return JSON.parseObject(params, new TypeReference<>() {});
        }
        return null;
    }

    /**
     * 提取内容中所有的href属性值
     */
    private List<String> extractAllHrefAttributes(String content) {
        List<String> list = Lists.newArrayList();
        Matcher matcher = HREF_PATTERN.matcher(content);
        while (matcher.find()) {
            list.add(matcher.group(2));
        }
        return list;
    }

    /**
     * 从a标签属性中提取href值
     */
    private String extractHrefFromAttributes(String attributes) {
        Matcher matcher = HREF_PATTERN.matcher(attributes);
        return matcher.find() ? matcher.group(2) : null;
    }

    public static String replaceATagsWithSpan(String html) {
        // 1. 替换 <a ...> 为 <span ...>（移除 href）
        Pattern openTagPattern = Pattern.compile("<a\\s+(.*?)>");
        Matcher openTagMatcher = openTagPattern.matcher(html);
        StringBuilder result = new StringBuilder();

        while (openTagMatcher.find()) {
            String attributes = openTagMatcher.group(1);
            // 移除 href 属性
            String newAttributes = attributes.replaceAll("href\\s*=\\s*[\"'][^\"']*[\"']", "");
            newAttributes = newAttributes.trim(); // 清理多余空格

            String replacement;
            if (!newAttributes.isEmpty()) {
                replacement = "<span " + newAttributes + ">";
            } else {
                replacement = "<span>";
            }
            openTagMatcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        openTagMatcher.appendTail(result);

        // 2. 替换 </a> 为 </span>
        String intermediateResult = result.toString();
        intermediateResult = intermediateResult.replaceAll("</a>", "</span>");

        return intermediateResult;
    }

}
