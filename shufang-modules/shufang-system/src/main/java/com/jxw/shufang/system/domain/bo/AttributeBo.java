package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.Attribute;

import java.util.List;

/**
 * 属性业务对象 attribute
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Attribute.class, reverseConvertGenerate = false)
public class AttributeBo extends BaseEntity {

    /**
     * 属性ID
     */
    @NotNull(message = "属性ID不能为空", groups = { EditGroup.class })
    private Long attributeId;

    /**
     * 属性分组IDs
     */
    @NotBlank(message = "属性分组IDs不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeGroupingIds;

    /**
     * 属性名
     */
    @NotBlank(message = "属性名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeName;

    /**
     * 输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)
     */
    @NotBlank(message = "输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String inputType;

    /**
     * 属性可选值
     */
    //@NotBlank(message = "属性可选值不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeValues;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer attributeSort;

    /**
     * 是否可以为空(0是 2否)
     */
    @NotBlank(message = "是否可以为空(0是 2否)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String canNullStatus;

    /**
     * 父属性IDs
     */
    //@NotBlank(message = "父属性IDs不能为空", groups = { AddGroup.class, EditGroup.class })
    private String parentAttributeIds;

    /**
     * 是否需要列表展示（0是 2否）
     */
    @NotBlank(message = "是否需要列表展示（0是 2否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String showList;

    /**
     * 是否需要展示为搜索词（0是 2否）
     */
    @NotBlank(message = "是否需要展示为搜索词（0是 2否）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String search;

    /**
     * 页面列表中width
     */
    //@NotNull(message = "页面列表中width不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long width;

    /**
     * 页面列表中fixed
     */
    //@NotBlank(message = "页面列表中fixed不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fixed;

    /**
     * 页面列表中sort
     */
    //@NotBlank(message = "页面列表中sort不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sort;

    /**
     * 页面列表中align
     */
    //@NotBlank(message = "页面列表中align不能为空", groups = { AddGroup.class, EditGroup.class })
    private String align;

    /**
     * 显示位置，自定义某个字符串来达成限制的条件
     */
    private String showLocation;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;


    /**
     * 属性分组ID
     */
    private List<Long> attributeGroupingIdList;


}
