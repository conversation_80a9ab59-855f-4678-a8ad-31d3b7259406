package com.jxw.shufang.system.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteRoleService;
import com.jxw.shufang.system.api.domain.vo.RemoteRoleVo;
import com.jxw.shufang.system.domain.vo.SysRoleVo;
import com.jxw.shufang.system.service.ISysRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteRoleServiceImpl implements RemoteRoleService {

    private final ISysRoleService roleService;


    @Override
    public List<RemoteRoleVo> getRoleByMenuIds(List<Long> menuIds, Long userId) {
        List<SysRoleVo> roleByMenuIds = roleService.getRoleByMenuIds(menuIds, userId);
        return MapstructUtils.convert(roleByMenuIds, RemoteRoleVo.class);
    }
}
