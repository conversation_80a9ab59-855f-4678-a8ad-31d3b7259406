package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.domain.SysNoticeUser;
import com.jxw.shufang.system.domain.bo.SysNoticeBo;
import com.jxw.shufang.system.domain.bo.SysNoticeUserBo;
import com.jxw.shufang.system.domain.vo.SysNoticeUserVo;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;
import com.jxw.shufang.system.mapper.SysNoticeUserMapper;
import com.jxw.shufang.system.service.ISysNoticeService;
import com.jxw.shufang.system.service.ISysNoticeUserService;
import org.springdoc.core.utils.PropertyResolverUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * noticeUserService业务层处理
 *
 * @date 2024-06-30
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service
public class SysNoticeUserServiceImpl implements ISysNoticeUserService, BaseService {

    private final SysNoticeUserMapper baseMapper;

    private final ISysNoticeService sysNoticeService;
    private final PropertyResolverUtils propertyResolverUtils;

    /**
     * 查询noticeUser
     */
    @Override
    public SysNoticeUserVo queryById(Long noticeUserId){
        return baseMapper.selectVoById(noticeUserId);
    }

    /**
     * 查询noticeUser列表
     */
    @Override
    public TableDataInfo<SysNoticeUserVo> queryPageList(SysNoticeUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNoticeUser> lqw = buildLambdaQueryWrapper(bo);
        Page<SysNoticeUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询noticeUser列表
     */
    @Override
    public List<SysNoticeUserVo> queryList(SysNoticeUserBo bo) {
        QueryWrapper<SysNoticeUser> lqw = buildQueryWrapper(bo);
        List<SysNoticeUserVo> sysNoticeUserVos = baseMapper.selectNoticeUserVoList(lqw);
        //if (Boolean.TRUE.equals(bo.getWithNoticeInfo())){
        //    putNoticeInfo(sysNoticeUserVos);
        //}
        return sysNoticeUserVos;
    }

    private void putNoticeInfo(List<SysNoticeUserVo> sysNoticeUserVos) {
        if (CollUtil.isEmpty(sysNoticeUserVos)){
            return;
        }
        List<Long> list = StreamUtils.toList(sysNoticeUserVos, SysNoticeUserVo::getNoticeId);
        if (CollUtil.isEmpty(list)){
            return;
        }
        SysNoticeBo sysNoticeBo = new SysNoticeBo();
        sysNoticeBo.setNoticeIdList(list);
        sysNoticeBo.setNotWithContentInfo(Boolean.TRUE);
        List<SysNoticeVo> sysNoticeVos = sysNoticeService.selectNoticeList(sysNoticeBo);
        if (CollUtil.isEmpty(sysNoticeVos)){
            return;
        }

        Map<Long, SysNoticeVo> map = StreamUtils.toIdentityMap(sysNoticeVos, SysNoticeVo::getNoticeId);
        sysNoticeUserVos.forEach(sysNoticeUserVo -> {
            sysNoticeUserVo.setNotice(map.get(sysNoticeUserVo.getNoticeId()));
        });

    }

    private QueryWrapper<SysNoticeUser> buildQueryWrapper(SysNoticeUserBo bo) {
        QueryWrapper<SysNoticeUser> lqw = Wrappers.query();
        lqw.eq(bo.getUserId() != null, "t.user_id", bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), "t.read_status", bo.getReadStatus());
        lqw.eq(bo.getNoticeId() != null, "t.notice_id", bo.getNoticeId());
        lqw.eq(bo.getCreateDept() != null, "t.create_dept", bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, "t.create_by", bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, "t.create_time", bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, "t.update_by", bo.getUpdateBy());
        lqw.eq(bo.getUpdateTime() != null, "t.update_time", bo.getUpdateTime());
        lqw.eq(StringUtils.isNotBlank(bo.getNoticeStatus()), "sn.status", bo.getNoticeStatus());
        lqw.orderByDesc("t.read_status", "t.create_time");
        return lqw;
    }


    private LambdaQueryWrapper<SysNoticeUser> buildLambdaQueryWrapper(SysNoticeUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysNoticeUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, SysNoticeUser::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getReadStatus()), SysNoticeUser::getReadStatus, bo.getReadStatus());
        lqw.eq(bo.getNoticeId() != null, SysNoticeUser::getNoticeId, bo.getNoticeId());
        lqw.eq(bo.getCreateDept() != null, SysNoticeUser::getCreateDept, bo.getCreateDept());
        lqw.eq(bo.getCreateBy() != null, SysNoticeUser::getCreateBy, bo.getCreateBy());
        lqw.eq(bo.getCreateTime() != null, SysNoticeUser::getCreateTime, bo.getCreateTime());
        lqw.eq(bo.getUpdateBy() != null, SysNoticeUser::getUpdateBy, bo.getUpdateBy());
        lqw.eq(bo.getUpdateTime() != null, SysNoticeUser::getUpdateTime, bo.getUpdateTime());
        lqw.orderByDesc(SysNoticeUser::getReadStatus, SysNoticeUser::getCreateTime);
        return lqw;
    }

    /**
     * 新增noticeUser
     */
    @Override
    public Boolean insertByBo(SysNoticeUserBo bo) {
        SysNoticeUser add = MapstructUtils.convert(bo, SysNoticeUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setNoticeUserId(add.getNoticeUserId());
        }
        return flag;
    }

    /**
     * 修改noticeUser
     */
    @Override
    public Boolean updateByBo(SysNoticeUserBo bo) {
        SysNoticeUser update = MapstructUtils.convert(bo, SysNoticeUser.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysNoticeUser entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除noticeUser
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public int deleteNoticeUserByNoticeIds(Long[] noticeIds) {
        LambdaQueryWrapper<SysNoticeUser> queryWrapper = Wrappers.lambdaQuery(SysNoticeUser.class);
        queryWrapper.in(SysNoticeUser::getNoticeId, List.of(noticeIds));
        return baseMapper.delete(queryWrapper);
    }

    @Override
    public boolean insertNoticeUser(Long noticeId, List<Long> noticeUserIdList) {
        List<SysNoticeUser> insertList = new ArrayList<>();
        for (Long l : noticeUserIdList) {
            SysNoticeUser sysNoticeUser = new SysNoticeUser();
            sysNoticeUser.setNoticeId(noticeId);
            sysNoticeUser.setUserId(l);
            sysNoticeUser.setReadStatus(UserConstants.NOTICE_READ_STATUS_UNREAD);
            insertList.add(sysNoticeUser);
        }
        return baseMapper.insertBatch(insertList);
    }

    @Override
    public Long getUnreadCount(Long userId) {
        return baseMapper.getUnreadCount(userId);

    }

    @Override
    public SysNoticeVo detail(Long noticeUserId) {
        SysNoticeUserVo sysNoticeUserVo = baseMapper.selectVoById(noticeUserId);
        Long noticeId = sysNoticeUserVo.getNoticeId();
        SysNoticeVo sysNoticeVo = sysNoticeService.selectNoticeById(noticeId);

        // 标记已读
        LambdaUpdateWrapper<SysNoticeUser> updateWrapper = Wrappers.lambdaUpdate(SysNoticeUser.class);
        updateWrapper.set(SysNoticeUser::getReadStatus, UserConstants.NOTICE_READ_STATUS_READ)
            .eq(SysNoticeUser::getNoticeUserId, noticeUserId);
        baseMapper.update(null, updateWrapper);
        return sysNoticeVo;
    }

    /**
     * 初始化操作
     */
    @Override
    public void init() {
    }

}
