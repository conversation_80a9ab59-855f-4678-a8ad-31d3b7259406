package com.jxw.shufang.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * @author: cyj
 * @date: 2025/5/7
 */
@Data
@AllArgsConstructor
public class PayMerchantConfigBranchVo {

    /**
     * 分店名称
     */
    private String branchName;

    /**
     * 分店状态（0正常 1停用）
     */
    private String branchStatus;

    /**
     * 门店授权类型
     */
    private String branchAuthTypeName;

    /**
     * 创建时间
     */
    private Date createTime;
}
