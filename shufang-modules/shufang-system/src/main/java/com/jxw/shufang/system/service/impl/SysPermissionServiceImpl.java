package com.jxw.shufang.system.service.impl;

import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.TenantConstants;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.domain.vo.SysRoleVo;
import com.jxw.shufang.system.service.ISysMenuService;
import com.jxw.shufang.system.service.ISysPermissionService;
import com.jxw.shufang.system.service.ISysRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysPermissionServiceImpl implements ISysPermissionService {

    private static final Logger log = LoggerFactory.getLogger(SysPermissionServiceImpl.class);
    private final ISysRoleService roleService;
    private final ISysMenuService menuService;

    /**
     * 获取角色数据权限
     *
     * @param userId  用户id
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermission(Long userId) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId)) {
            roles.add(TenantConstants.SUPER_ADMIN_ROLE_KEY);
        } else {
            roles.addAll(roleService.selectRolePermissionByUserId(userId));
        }
        return roles;
    }

    /**
     * 获取角色权限通过角色id
     *
     * @param roleId  用户id
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermissionByRoleId(Long roleId) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdminRole(roleId)) {
            roles.add(TenantConstants.SUPER_ADMIN_ROLE_KEY);
        } else {
            SysRoleVo sysRoleVo =null;
            if (LoginHelper.isLogin()) {
                sysRoleVo = roleService.selectRoleById(roleId);
            }else {
                sysRoleVo = DataPermissionHelper.ignore(() -> { return roleService.selectRoleById(roleId); });
            }
            roles.add(sysRoleVo.getRoleKey());
        }
        return roles;
    }


    /**
     * 获取菜单数据权限
     *
     * @param userId  用户id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(Long userId) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdmin(userId)) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByUserId(userId));
        }
        return perms;
    }

    /**
     * 获取菜单数据权限通过角色id
     *
     * @param roleId 角色Id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermissionByRoleId(Long roleId) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (LoginHelper.isSuperAdminRole(roleId)) {
            perms.add("*:*:*");
        } else {
            perms.addAll(menuService.selectMenuPermsByRoleId(roleId));
        }
        return perms;
    }
}
