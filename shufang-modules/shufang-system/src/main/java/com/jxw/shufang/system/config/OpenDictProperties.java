package com.jxw.shufang.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "open.dict")
public class OpenDictProperties {

    /**
     * 允许无登录调用的 key 白名单
     */
    private List<String> allowedKeys;

    /**
     * 判断某个 key 是否被允许访问
     */
    public boolean isKeyAllowed(String key) {
        return allowedKeys != null && allowedKeys.contains(key);
    }
}
