package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 参数配置表 sys_config
 *

 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dept_config")
public class SysDeptConfig extends TenantEntity {

    /**
     * 参数主键
     */
    @TableId(value = "config_id")
    private Long configId;

    private Long deptId;

    /**
     * 0-在线作答，1-打印作答
     */
    private Integer answerPatternType;

}
