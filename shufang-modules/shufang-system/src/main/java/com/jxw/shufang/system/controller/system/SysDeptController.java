package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.resource.api.RemoteFileService;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.SysDeptBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.service.ISysDeptService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门信息
 *

 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dept")
public class SysDeptController extends BaseController {

    private final ISysDeptService deptService;
    @DubboReference
    private RemoteFileService remoteFileService;
    /**
     * 获取部门列表
     */
    @SaCheckPermission("system:dept:list")
    @GetMapping("/list")
    public R<List<SysDeptVo>> list(SysDeptBo dept) {
        List<SysDeptVo> depts = deptService.selectDeptList(dept);
        return R.ok(depts);
    }

    /**
     * 查询部门列表（排除节点）
     *
     * @param deptId 部门ID
     */
    @SaCheckPermission("system:dept:list")
    @GetMapping("/list/exclude/{deptId}")
    public R<List<SysDeptVo>> excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDeptVo> depts = deptService.selectDeptList(new SysDeptBo());
        depts.removeIf(d -> d.getDeptId().equals(deptId)
            || StringUtils.splitList(d.getAncestors()).contains(Convert.toStr(deptId)));
        return R.ok(depts);
    }

    /**
     * 根据部门编号获取详细信息
     *
     * @param deptId 部门ID
     */
    @SaCheckPermission("system:dept:query")
    @GetMapping(value = "/{deptId}")
    public R<SysDeptVo> getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        SysDeptVo dept = deptService.selectDeptById(deptId);
        //由于是私有桶限时，需要实时刷新
        if (!(dept.getSystemLogo() == null || dept.getSystemLogo() == 0)) {
            dept.setSystemLogoUrl(remoteFileService.selectUrlByIds(String.valueOf(dept.getSystemLogo())));
        }
        return R.ok(dept);
    }

    /**
     * 新增部门
     */
    @SaCheckPermission("system:dept:add")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysDeptBo dept) throws Exception {
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @SaCheckPermission("system:dept:edit")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysDeptBo dept) {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept)) {
            return R.fail("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            return R.fail("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (StringUtils.equals(UserConstants.DEPT_DISABLE, dept.getStatus())) {
            if (deptService.selectNormalChildrenDeptById(deptId) > 0) {
                return R.fail("该部门包含未停用的子部门!");
            } else if (deptService.checkDeptExistUser(deptId)) {
                return R.fail("该部门下存在已分配用户，不能禁用!");
            }
        }
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     */
    @SaCheckPermission("system:dept:remove")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public R<Void> remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return R.warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return R.warn("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 查询某个节点下的所有分店数据（深层）
     *
     */
    @GetMapping("/list/store/{parentId}")
    public R<List<SysDeptVo>> storeList(@PathVariable(value = "parentId", required = false) Long parentId) {
        SysDeptBo dept = new SysDeptBo();
        dept.setIsStore(true);
        List<SysDeptVo> depts = DataPermissionHelper.ignore(() -> deptService.selectDeptList(dept));
        // 删除父节点不是指定组织ID 且 祖级列表不包含指定组织ID的
        depts.removeIf(d -> !d.getParentId().equals(parentId)
            && !StringUtils.splitList(d.getAncestors()).contains(Convert.toStr(parentId)));
        return R.ok(depts);
    }


    /**
     * 查询部门列表（排除分店）
     *
     */
    @GetMapping("/list/excludeStore")
    public R<List<Tree<Long>>> excludeStoreChild() {
        SysDeptBo dept = new SysDeptBo();
        dept.setIsStore(false);
        List<SysDeptVo> depts = DataPermissionHelper.ignore(() -> deptService.selectDeptList(dept));
        List<Tree<Long>> trees = deptService.buildDeptTreeSelect(depts);
        return R.ok(trees);
    }

    @GetMapping("/deptConfig")
    public R<SysDeptVo> deptConfig() {
        Long deptId = LoginHelper.getDeptId();
        SysDeptVo deptSystemConfig = deptService.getDeptSystemConfig(deptId);
        return R.ok(deptSystemConfig);
    }
}
