package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.AttributeRelation;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttributeRelationVo;
import com.jxw.shufang.system.mapper.AttributeRelationMapper;
import com.jxw.shufang.system.service.IAttributeRelationService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 属性关系Service业务层处理
 *
 *
 * @date 2024-03-26
 */
@RequiredArgsConstructor
@Service
public class AttributeRelationServiceImpl implements IAttributeRelationService {

    private final AttributeRelationMapper baseMapper;


    /**
     * 查询属性关系
     */
    @Override
    public AttributeRelationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询属性关系列表
     */
    @Override
    public TableDataInfo<AttributeRelationVo> queryPageList(AttributeRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AttributeRelation> lqw = buildQueryWrapper(bo);
        Page<AttributeRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询属性关系列表
     */
    @Override
    public List<AttributeRelationVo> queryList(AttributeRelationBo bo) {
        LambdaQueryWrapper<AttributeRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    private LambdaQueryWrapper<AttributeRelation> buildQueryWrapper(AttributeRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AttributeRelation> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAttributeId() != null, AttributeRelation::getAttributeId, bo.getAttributeId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AttributeRelation::getType, bo.getType());
        lqw.eq(bo.getTypeId() != null, AttributeRelation::getTypeId, bo.getTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getValue()), AttributeRelation::getValue, bo.getValue());
        lqw.in(CollUtil.isNotEmpty(bo.getAttributeIdList()), AttributeRelation::getAttributeId, bo.getAttributeIdList());
        lqw.in(CollUtil.isNotEmpty(bo.getTypeIdList()), AttributeRelation::getTypeId, bo.getTypeIdList());
        return lqw;
    }

    /**
     * 新增属性关系
     */
    @Override
    public Boolean insertByBo(AttributeRelationBo bo) {
        AttributeRelation add = MapstructUtils.convert(bo, AttributeRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改属性关系
     */
    @Override
    public Boolean updateByBo(AttributeRelationBo bo) {
        AttributeRelation update = MapstructUtils.convert(bo, AttributeRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AttributeRelation entity) {
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除属性关系
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public boolean insertRelationBatch(List<AttributeRelationBo> convert) throws ServiceException {
        return baseMapper.insertBatch(MapstructUtils.convert(convert, AttributeRelation.class));
    }

    @Override
    public boolean updateRelationBatch(List<AttributeRelationBo> updateList, Boolean updateNullValue) {
        if (updateNullValue) {
            updateList.forEach(item->{
                if (StringUtils.isBlank(item.getValue())) {
                    item.setValue("");
                }
            });
            return baseMapper.updateBatchById(MapstructUtils.convert(updateList, AttributeRelation.class));
        } else {
            return baseMapper.updateBatchById(MapstructUtils.convert(updateList, AttributeRelation.class));
        }

    }

    @Override
    public List<CourseAttributeDetailDTO> batchQueryAttributeValueByIdAndTypeId(List<Long> courseIds, List<Long> typeAttributeIds, String type) {
        return baseMapper.queryCourseAttributeDetailByIdAndTypeId(courseIds, typeAttributeIds, type);
    }


}
