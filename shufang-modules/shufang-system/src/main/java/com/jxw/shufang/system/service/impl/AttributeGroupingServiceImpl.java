package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.bo.RemoteBranchBo;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.web.core.BaseService;
import com.jxw.shufang.system.domain.AttributeGrouping;
import com.jxw.shufang.system.domain.bo.AttributeGroupingBo;
import com.jxw.shufang.system.domain.vo.AttributeGroupingVo;
import com.jxw.shufang.system.mapper.AttributeGroupingMapper;
import com.jxw.shufang.system.service.IAttributeGroupingService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 属性分组Service业务层处理
 *
 *
 * @date 2024-03-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AttributeGroupingServiceImpl implements IAttributeGroupingService, BaseService {

    private final AttributeGroupingMapper baseMapper;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    /**
     * 查询属性分组
     */
    @Override
    public AttributeGroupingVo queryById(Long attributeGroupingId){
        return baseMapper.selectVoById(attributeGroupingId);
    }

    /**
     * 查询属性分组列表
     */
    @Override
    public TableDataInfo<AttributeGroupingVo> queryPageList(AttributeGroupingBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AttributeGrouping> lqw = buildLambdaQueryWrapper(bo);
        Page<AttributeGroupingVo> result = baseMapper.selectAttrGroupPage(pageQuery.build(), lqw);
        if (Boolean.TRUE.equals(bo.getWithBranchInfo())){
            putBranchInfo(result.getRecords());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询属性分组列表
     */
    @Override
    public List<AttributeGroupingVo> queryList(AttributeGroupingBo bo) {
        LambdaQueryWrapper<AttributeGrouping> lqw = buildLambdaQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AttributeGrouping> buildLambdaQueryWrapper(AttributeGroupingBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AttributeGrouping> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getAttributeGroupingName()), AttributeGrouping::getAttributeGroupingName, bo.getAttributeGroupingName());
        lqw.eq(bo.getBranchId() != null, AttributeGrouping::getBranchId, bo.getBranchId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AttributeGrouping::getType, bo.getType());
        lqw.eq(bo.getSortOrder() != null, AttributeGrouping::getSortOrder, bo.getSortOrder());
        lqw.in(CollUtil.isNotEmpty(bo.getBranchIds()), AttributeGrouping::getBranchId, bo.getBranchIds());
        lqw.eq(StringUtils.isNotBlank(bo.getDelFlag()), AttributeGrouping::getDelFlag, bo.getDelFlag());
        return lqw;
    }

    /**
     * 新增属性分组
     */
    @Override
    public Boolean insertByBo(AttributeGroupingBo bo) {
        AttributeGrouping add = MapstructUtils.convert(bo, AttributeGrouping.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            RedisUtils.deleteObject("attributeGroupListByType");
            bo.setAttributeGroupingId(add.getAttributeGroupingId());
        }
        return flag;
    }

    /**
     * 修改属性分组
     */
    @Override
    public Boolean updateByBo(AttributeGroupingBo bo) {
        AttributeGrouping update = MapstructUtils.convert(bo, AttributeGrouping.class);
        validEntityBeforeSave(update);
        if (baseMapper.updateById(update) > 0) {
            RedisUtils.deleteObject("attributeGroupListByType");
            return true;
        }
        return false;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AttributeGrouping entity){
        //做一些数据校验,如唯一约束
    }

    /**
     * 批量删除属性分组
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Cacheable(value = "attributeGroupListByType", key = "#type",condition = "#type != null")
    @Override
    public List<AttributeGroupingVo> getByType(String type) {
        LambdaQueryWrapper<AttributeGrouping> lqw = Wrappers.lambdaQuery();
        lqw.eq(AttributeGrouping::getType, type);
        lqw.eq(AttributeGrouping::getDelFlag, UserConstants.DEL_FLAG_NO);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 清除所有缓存
     *
     *
     * @date 2024/04/02 05:47:43
     */
    @Override
    public void cleanAllCache() {
        log.info("===========attributeGroupingService cleanAllCache===========");
        RedisUtils.deleteObject("attributeGroupListByType");
        log.info("===========attributeGroupingService cleanAllCache end===========");
    }

    @Override
    public List<CourseAttributeDetailDTO> batchQueryAttributeValueByTypeId(List<Long> typeId, String type) {
        if (CollectionUtil.isEmpty(typeId)) {
            return Collections.emptyList();
        }
        return baseMapper.queryCourseAttributeDetail(typeId, type);
    }

    @Override
    public void init() {
        log.info("===========attributeGroupingService init start===========");

        IAttributeGroupingService aopBean = SpringUtils.getBean(IAttributeGroupingService.class);
        aopBean.cleanAllCache();

        log.info("===========attributeGroupingService init attributeGroupListByType===========");
        QueryWrapper<AttributeGrouping> lqw = Wrappers.query();
        lqw.eq("del_flag", UserConstants.DEL_FLAG_NO);
        lqw.select("DISTINCT type as `type`");
        List<AttributeGrouping> attributeGroupings = baseMapper.selectList(lqw);
        log.info("===========attributeGroupingService init attributeGroupListByType size {}===========", attributeGroupings.size());
        for (AttributeGrouping attributeGrouping : attributeGroupings) {
            aopBean.getByType(attributeGrouping.getType());
        }
        log.info("===========attributeGroupingService init  end===========");
    }

    private void putBranchInfo(List<AttributeGroupingVo> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        voList = voList.stream().filter(vo -> vo.getBranchId() != null && vo.getBranchId() != -1).collect(Collectors.toList());
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        RemoteBranchBo remoteBranchBo = new RemoteBranchBo();
        remoteBranchBo.setBranchIds(voList.stream().map(AttributeGroupingVo::getBranchId).distinct().collect(Collectors.toList()));
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.selectBranchList(remoteBranchBo);
        if (CollUtil.isEmpty(remoteBranchVos)) {
            return;
        }
        Map<Long, RemoteBranchVo> remoteBranchVoMap = remoteBranchVos.stream().collect(Collectors.toMap(RemoteBranchVo::getBranchId, vo -> vo));
        voList.forEach(vo -> {
            vo.setBranch(remoteBranchVoMap.get(vo.getBranchId()));
        });
    }











}
