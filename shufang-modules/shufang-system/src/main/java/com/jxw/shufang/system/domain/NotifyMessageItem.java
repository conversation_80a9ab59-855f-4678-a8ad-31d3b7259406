package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode
@TableName("notify_message_item")
public class NotifyMessageItem {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息id
     */
    private Integer messageId;

    /**
     * 发送人
     */
    private Long fromUserId;

    /**
     * 发送人名字
     */
    private String fromUserName;

    /**
     * 接收人
     */
    private Long toUserId;

    /**
     * 接收人名字
     */
    private String toUserName;

    /**
     * 阅读时间
     */
    private Long readTime;

    /**
     * 删除标识
     */
    private Integer delFlag;

}
