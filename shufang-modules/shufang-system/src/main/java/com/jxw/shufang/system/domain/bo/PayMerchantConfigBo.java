package com.jxw.shufang.system.domain.bo;

import java.io.Serial;
import java.util.Set;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import com.jxw.shufang.system.domain.PayMerchantConfig;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/5/6
 */
@Data
@AutoMapper(target = PayMerchantConfig.class, reverseConvertGenerate = false)
public class PayMerchantConfigBo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "数据异常", groups = {EditGroup.class})
    private Long id;
    /**
     * 公户名称 商户名称
     */
    @NotBlank(message = "公户名称不为空", groups = {AddGroup.class, EditGroup.class})
    private String merchantName;
    /**
     * 接入方名称
     */
    @NotBlank(message = "接入方名称不为空", groups = {AddGroup.class, EditGroup.class})
    private String channelCode;

    /**
     * 是否启用
     */
    @NotNull(message = "请选择状态", groups = {AddGroup.class, EditGroup.class})
    private Boolean enable;
    /**
     * 应用ID
     */
    @NotBlank(message = "唯一标识不为空", groups = {AddGroup.class, EditGroup.class})
    private String appId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType;
    /**
     * "unionpay",支付方式(unionpay-银联聚合支付 ）
     */
    private String payCode;
    /**
     * 聚合二维码的方式
     */
    private String wayCode;

    /**
     * 批量同步的部门id
     */
    private Set<Long> deptIdList;
}
