package com.jxw.shufang.system.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.system.api.RemoteConfigService;
import com.jxw.shufang.system.service.ISysConfigService;
import org.springframework.stereotype.Service;

/**
 * 配置服务
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteConfigServiceImpl implements RemoteConfigService {

    private final ISysConfigService configService;

    /**
     * 获取注册开关
     */
    @Override
    public boolean selectRegisterEnabled(String tenantId) {
        return configService.selectRegisterEnabled(tenantId);
    }

    @Override
    public Integer selectStudentExpireTime() {
        return configService.selectStudentExpireTime();
    }

    @Override
    public void editStudentExpireTime(Integer expireDays) {
        configService.editStudentExpireTime(expireDays);
    }

    @Override
    public String selectCourseDetailSeparator() {
        return configService.selectCourseDetailSeparator();
    }

    @Override
    public String selectAttrValueSeparator() {
        return configService.selectAttrValueSeparator();
    }

    @Override
    public String selectSmsCaptchaTemplateId(){
        return configService.selectSmsCaptchaTemplateId();
    }

    @Override
    public Integer select24hSmsCaptchaMaxCount() {
        return configService.select24hSmsCaptchaMaxCount();
    }

    @Override
    public Integer select24hSmsCaptchaErrorMaxCount() {
        return configService.select24hSmsCaptchaErrorMaxCount();
    }

    @Override
    public Long selectSmsCaptchaExpireTime() {
       return configService.selectSmsCaptchaExpireTime();
    }

    @Override
    public Integer selectPrintRecordExpireTime() {
        return configService.selectPrintRecordExpireTime();
    }

    @Override
    public Integer selectStudentExpireWarningDays() {
        return configService.selectStudentExpireWarningDays();
    }


}
