package com.jxw.shufang.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.domain.*;
import com.jxw.shufang.system.domain.bo.SysUserSignBo;
import com.jxw.shufang.system.domain.vo.SysUserSignVo;
import com.jxw.shufang.system.mapper.*;
import com.jxw.shufang.system.service.ISysUserSignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * 用户签署 业务层处理
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserSignServiceImpl implements ISysUserSignService {

    private final SysUserSignMapper baseMapper;

    /**
     *  用户签署接口
     * @param user
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOrUpdateUser(SysUserSignBo user) {
        SysUserSign convert = MapstructUtils.convert(user, SysUserSign.class);
        LambdaQueryWrapper<SysUserSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUserSign::getUserId, user.getUserId())
            .eq(SysUserSign::getType, user.getType())
            .eq(SysUserSign::getVersion, user.getVersion());
        lqw.last("LIMIT 1");
        SysUserSign sysUserSign = baseMapper.selectOne(lqw);
        if (!ObjectUtils.isEmpty(sysUserSign)) {
            return baseMapper.update(convert, lqw);
        } else {
            return baseMapper.insert(convert);
        }
    }

    @Override
    public SysUserSignVo getSysUserSign(SysUserSignBo user) {
        LambdaQueryWrapper<SysUserSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUserSign::getUserId, user.getUserId())
            .eq(SysUserSign::getType, user.getType())
            .eq(SysUserSign::getVersion, user.getVersion());
        lqw.last("LIMIT 1");
        return baseMapper.selectVoOne(lqw);
    }


}
