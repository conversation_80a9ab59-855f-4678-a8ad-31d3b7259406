package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.api.model.LoginUser;
import com.jxw.shufang.system.domain.bo.SysUserSignBo;
import com.jxw.shufang.system.domain.vo.SysUserSignVo;
import com.jxw.shufang.system.service.ISysUserSignService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户签名
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userSign")
public class SysUserSignController extends BaseController {

    private final ISysUserSignService userService;


    /**
     * 新增用户
     */
    @SaCheckPermission("system:userSign:add")
    @Log(title = "用户签名", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Void> add(@Validated @RequestBody SysUserSignBo user) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        user.setUserId(loginUser.getUserId());
        return toAjax(userService.saveOrUpdateUser(user));
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getSysUserSign")
    public R<SysUserSignVo> getSysUserSign(SysUserSignBo user) {
        return R.ok(userService.getSysUserSign(user));
    }


}
