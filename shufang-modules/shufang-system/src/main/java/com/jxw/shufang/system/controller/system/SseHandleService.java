package com.jxw.shufang.system.controller.system;

import com.jxw.shufang.system.domain.bo.NoticeMessagePushBo;
import com.jxw.shufang.system.domain.bo.SseNoticeMessageBo;
import com.jxw.shufang.system.service.INotifyMessageService;
import com.jxw.shufang.system.service.ISseHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Service
@Slf4j
public class SseHandleService implements ISseHandleService {

    private final ConcurrentHashMap<Long, ConcurrentLinkedDeque<String>> userMap = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();
    private final INotifyMessageService notifyMessageService;

    @Override
    public SseEmitter noticeMessageHandler(SseNoticeMessageBo messageBo) {

        SseEmitter sseEmitter = new SseEmitter(60_000L);
        String userKey = String.join("_", messageBo.getUserId().toString(),
            messageBo.getTimestamp().toString(), messageBo.getLabel());
        sseEmitterMap.put(userKey, sseEmitter);

        // 完成时清理
        sseEmitter.onCompletion(() -> {
            sseEmitterMap.remove(userKey);
            removeUserId(userKey);
            log.info("SSE连接正常关闭[{}]_[{}]-[{}]", userKey, userMap.size(), sseEmitterMap.size());
        });
        // 超时清理
        sseEmitter.onTimeout(() -> {
            sseEmitter.complete();
            sseEmitterMap.remove(userKey);
            removeUserId(userKey);
            log.info("SSE连接超时关闭[{}]_[{}]-[{}]", userKey, userMap.size(), sseEmitterMap.size());
        });
        // 错误清理
        sseEmitter.onError(e -> {
            sseEmitterMap.remove(userKey);
            removeUserId(userKey);
            log.error("SSE连接错误[{}]_[{}]-[{}]", userKey, userMap.size(), sseEmitterMap.size(), e);
        });

        if (CollectionUtils.isEmpty(userMap.get(messageBo.getUserId()))) {
            ConcurrentLinkedDeque<String> newDeque = new ConcurrentLinkedDeque<>();
            newDeque.addLast(userKey);
            userMap.put(messageBo.getUserId(), newDeque);
        } else {
            ConcurrentLinkedDeque<String> curDeque = userMap.get(messageBo.getUserId());
            curDeque.remove(userKey);
            curDeque.addLast(userKey);
        }
        log.info("SSE客户端连接成功[{}]", userKey);
        connectPush(sseEmitter, messageBo.getUserId());
        return sseEmitter;
    }

    @Override
    public void noticeMessagePush(NoticeMessagePushBo pushBo) {
        ConcurrentLinkedDeque<String> deque = userMap.get(pushBo.getToUserId());
        if (CollectionUtils.isEmpty(deque)) {
            log.info("通知消息接收人{}[{}]未成功连接", pushBo.getToUserName(), pushBo.getToUserId());
            return;
        }
        deque.forEach(i -> {
            SseEmitter sseEmitter = sseEmitterMap.get(i);
            if (Objects.nonNull(sseEmitter)) {
                try {
                    sseEmitter.send(pushBo.getContent());
                } catch (Exception e) {
                    log.error("通知消息推送异常{}", i, e);
                    deque.remove(i);
                }
            }
        });
    }

    private void removeUserId(String userKey) {
        String[] split = userKey.split("_");
        Optional.ofNullable(userMap.get(Long.parseLong(split[0])))
            .ifPresent(deque -> deque.remove(userKey));
    }

    private void connectPush(SseEmitter sseEmitter, Long userId) {
        CompletableFuture.runAsync(() -> {
            try {
                TimeUnit.SECONDS.sleep(1);
                sseEmitter.send(notifyMessageService.getUnreadMessageCount(userId));
            } catch (IOException | InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("通知消息连接推送异常{}", userId, e);
            }
        });
    }

}
