package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.system.domain.Attribute;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 属性视图对象 attribute
 *
 * @date 2024-03-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Attribute.class)
public class AttributeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    @ExcelProperty(value = "属性ID")
    private Long attributeId;

    /**
     * 属性分组IDs
     */
    @ExcelProperty(value = "属性分组ID算")
    private String attributeGroupingIds;

    /**
     * 属性名
     */
    @ExcelProperty(value = "属性名")
    private String attributeName;

    /**
     * 输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)
     */
    @ExcelProperty(value = "输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)")
    private String inputType;

    /**
     * 属性可选值
     */
    @ExcelProperty(value = "属性可选值")
    private String attributeValues;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    private Integer attributeSort;

    /**
     * 是否可以为空(0是 2否)
     */
    @ExcelProperty(value = "是否可以为空(0是 2否)")
    private String canNullStatus;

    /**
     * 父属性IDs
     */
    @ExcelProperty(value = "父属性IDs")
    private String parentAttributeIds;

    /**
     * 是否需要列表展示（0是 2否）
     */
    @ExcelProperty(value = "是否需要列表展示", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=是,2=否")
    private String showList;

    /**
     * 是否需要展示为搜索词（0是 2否）
     */
    @ExcelProperty(value = "是否需要展示为搜索词", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=是,2=否")
    private String search;

    /**
     * 页面列表中width
     */
    @ExcelProperty(value = "页面列表中width")
    private Long width;

    /**
     * 页面列表中fixed
     */
    @ExcelProperty(value = "页面列表中fixed")
    private String fixed;

    /**
     * 页面列表中sort
     */
    @ExcelProperty(value = "页面列表中sort")
    private String sort;

    /**
     * 页面列表中align
     */
    @ExcelProperty(value = "页面列表中align")
    private String align;

    /**
     * 显示位置，自定义某个字符串来达成限制的条件
     */
    @ExcelProperty(value = "显示位置")
    private String showLocation;

    private AttributeRelationVo attributeRelation;


    /**
     * 创建时间
     */
    private Date createTime;



}
