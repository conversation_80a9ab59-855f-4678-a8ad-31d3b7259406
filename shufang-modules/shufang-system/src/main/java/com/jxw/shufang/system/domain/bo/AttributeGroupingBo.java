package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.AttributeGrouping;

import java.util.List;

/**
 * 属性分组业务对象 attribute_grouping
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AttributeGrouping.class, reverseConvertGenerate = false)
public class AttributeGroupingBo extends BaseEntity {

    /**
     * 属性组ID
     */
    @NotNull(message = "属性组ID不能为空", groups = { EditGroup.class })
    private Long attributeGroupingId;

    /**
     * 组名
     */
    @NotBlank(message = "组名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String attributeGroupingName;

    /**
     * 分店id（为-1时所有分店可以使用）
     */
    @NotNull(message = "分店id（为-1时所有分店可以使用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long branchId;

    /**
     * 表名
     */
    @NotBlank(message = "表名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sortOrder;

    /**
     * 门店id集合
     */
    private List<Long> branchIds;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 携带分店信息
     */
    private Boolean withBranchInfo;


}
