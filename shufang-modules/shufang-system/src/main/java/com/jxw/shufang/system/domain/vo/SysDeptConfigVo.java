package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.jxw.shufang.system.api.domain.vo.RemoteSysDeptConfigVo;
import com.jxw.shufang.system.domain.SysDeptConfig;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 部门视图对象 sys_dept
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMappers({
    @AutoMapper(target = SysDeptConfig.class),
    @AutoMapper(target = RemoteSysDeptConfigVo.class)
})
public class SysDeptConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 0-在线作答，1-打印作答
     */
    private Integer answerPatternType;

    /**
     * 部门id
     */
    private Long deptId;
    private Long configId;


}
