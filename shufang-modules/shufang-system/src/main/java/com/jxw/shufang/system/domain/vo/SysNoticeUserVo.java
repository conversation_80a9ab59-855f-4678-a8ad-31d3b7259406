package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.system.domain.SysNoticeUser;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * noticeUser视图对象 sys_notice_user
 *
 * @date 2024-06-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysNoticeUser.class)
public class SysNoticeUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long noticeUserId;

    /**
     * 接收人用户ID
     */
    @ExcelProperty(value = "接收人用户ID")
    private Long userId;

    /**
     * 读取状态（0已读 2未读）
     */
    @ExcelProperty(value = "读取状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=已读,2=未读")
    private String readStatus;

    /**
     * 公告ID
     */
    @ExcelProperty(value = "公告ID")
    private Long noticeId;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long createDept;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    private SysNoticeVo notice;


}
