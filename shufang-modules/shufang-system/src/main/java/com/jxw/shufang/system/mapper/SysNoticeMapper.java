package com.jxw.shufang.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxw.shufang.system.domain.bo.SysNoticeQueryBo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.domain.SysNotice;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;

import java.util.List;

/**
 * 通知公告表 数据层
 *

 */
@Mapper
public interface SysNoticeMapper extends BaseMapperPlus<SysNotice, SysNoticeVo> {

    Page<SysNoticeVo> selectNoticePage(@Param("page") Page<SysNotice> build, @Param(Constants.WRAPPER) QueryWrapper<SysNotice> lqw);

    Page<SysNoticeVo> pageQuery(IPage<SysNoticeVo> page, SysNoticeQueryBo bo);

    List<Long> selectUnreadNotice(@Param("userId") Long userId,
                                  @Param("list") List<Long> deptIds);

}
