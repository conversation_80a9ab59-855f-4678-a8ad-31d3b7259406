package com.jxw.shufang.system.domain.convert;

import io.github.linpeilie.BaseMapper;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.system.domain.vo.SysUserVo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * 用户信息转换器
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RemoteUserVoConvert extends BaseMapper<RemoteUserVo,SysUserVo> {

}
