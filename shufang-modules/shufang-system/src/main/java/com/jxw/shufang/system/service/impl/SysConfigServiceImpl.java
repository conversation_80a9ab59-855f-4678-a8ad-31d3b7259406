package com.jxw.shufang.system.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.constant.CacheNames;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.redis.utils.CacheUtils;
import com.jxw.shufang.common.tenant.helper.TenantHelper;
import com.jxw.shufang.system.domain.SysConfig;
import com.jxw.shufang.system.domain.bo.SysConfigBo;
import com.jxw.shufang.system.domain.vo.SysConfigVo;
import com.jxw.shufang.system.mapper.SysConfigMapper;
import com.jxw.shufang.system.service.ISysConfigService;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 参数配置 服务层实现
 *

 */
@RequiredArgsConstructor
@Service
public class SysConfigServiceImpl implements ISysConfigService {

    private final SysConfigMapper baseMapper;

    @Override
    public TableDataInfo<SysConfigVo> selectPageConfigList(SysConfigBo config, PageQuery pageQuery) {
        LambdaQueryWrapper<SysConfig> lqw = buildQueryWrapper(config);
        Page<SysConfigVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    @Override
    @DS("master")
    public SysConfigVo selectConfigById(Long configId) {
        return baseMapper.selectVoById(configId);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Cacheable(cacheNames = CacheNames.SYS_CONFIG, key = "#configKey")
    @Override
    public String selectConfigByKey(String configKey) {
        SysConfig retConfig = baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
            .eq(SysConfig::getConfigKey, configKey));
        if (ObjectUtil.isNotNull(retConfig)) {
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 获取注册开关
     *
     * @param tenantId 租户id
     * @return true开启，false关闭
     */
    @Override
    public boolean selectRegisterEnabled(String tenantId) {
        SysConfig retConfig = TenantHelper.dynamic(tenantId, () -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "sys.account.registerUser"));
        });
        if (ObjectUtil.isNull(retConfig)) {
            return false;
        }
        return Convert.toBool(retConfig.getConfigValue());
    }

    /**
     * 获取过期迁移时限
     *
     * @return true开启，false关闭
     */
    @Override
    public Integer selectStudentExpireTime() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "student.expireTime"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())) {
            return 0;
        }
        return Convert.toInt(retConfig.getConfigValue());
    }

    @Override
    public void editStudentExpireTime(Integer expireDays) throws ServiceException {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "student.expireTime"));
        });
        retConfig.setConfigValue(expireDays.toString());
        int row = baseMapper.updateById(retConfig);
        if (row <= 0) {
            throw new ServiceException("操作失败");
        }
    }


    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public List<SysConfigVo> selectConfigList(SysConfigBo config) {
        LambdaQueryWrapper<SysConfig> lqw = buildQueryWrapper(config);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysConfig> buildQueryWrapper(SysConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysConfig> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getConfigName()), SysConfig::getConfigName, bo.getConfigName());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigType()), SysConfig::getConfigType, bo.getConfigType());
        lqw.like(StringUtils.isNotBlank(bo.getConfigKey()), SysConfig::getConfigKey, bo.getConfigKey());
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            SysConfig::getCreateTime, params.get("beginTime"), params.get("endTime"));
        lqw.orderByAsc(SysConfig::getConfigId);
        return lqw;
    }

    /**
     * 新增参数配置
     *
     * @param bo 参数配置信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_CONFIG, key = "#bo.configKey")
    @Override
    public String insertConfig(SysConfigBo bo) {
        SysConfig config = MapstructUtils.convert(bo, SysConfig.class);
        int row = baseMapper.insert(config);
        if (row > 0) {
            return config.getConfigValue();
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 修改参数配置
     *
     * @param bo 参数配置信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_CONFIG, key = "#bo.configKey")
    @Override
    public String updateConfig(SysConfigBo bo) {
        int row = 0;
        SysConfig config = MapstructUtils.convert(bo, SysConfig.class);
        if (config.getConfigId() != null) {
            SysConfig temp = baseMapper.selectById(config.getConfigId());
            if (!StringUtils.equals(temp.getConfigKey(), config.getConfigKey())) {
                CacheUtils.evict(CacheNames.SYS_CONFIG, temp.getConfigKey());
            }
            row = baseMapper.updateById(config);
        } else {
            row = baseMapper.update(config, new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, config.getConfigKey()));
        }
        if (row > 0) {
            return config.getConfigValue();
        }
        throw new ServiceException("操作失败");
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(Long[] configIds) {
        for (Long configId : configIds) {
            SysConfig config = baseMapper.selectById(configId);
            if (StringUtils.equals(UserConstants.YES, config.getConfigType())) {
                throw new ServiceException(String.format("内置参数【%1$s】不能删除 ", config.getConfigKey()));
            }
            CacheUtils.evict(CacheNames.SYS_CONFIG, config.getConfigKey());
        }
        baseMapper.deleteBatchIds(Arrays.asList(configIds));
    }

    /**
     * 重置参数缓存数据
     */
    @Override
    public void resetConfigCache() {
        CacheUtils.clear(CacheNames.SYS_CONFIG);
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(SysConfigBo config) {
        long configId = ObjectUtil.isNull(config.getConfigId()) ? -1L : config.getConfigId();
        SysConfig info = baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getConfigKey, config.getConfigKey()));
        if (ObjectUtil.isNotNull(info) && info.getConfigId() != configId) {
            return false;
        }
        return true;
    }

    @Override
    public String selectCourseDetailSeparator() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "course.detailSeparator"));
        });
        //不用isBlank,避免我使用空格作为分隔符，他没办法正确走下去
        if (ObjectUtil.isNull(retConfig) || ObjectUtil.isNull(retConfig.getConfigValue())) {
            return " | ";
        }
        return retConfig.getConfigValue();
    }

    @Override
    public String selectAttrValueSeparator() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "system.attrValueSeparator"));
        });
        //不用isBlank,避免我使用空格作为分隔符，他没办法正确走下去
        if (ObjectUtil.isNull(retConfig) || ObjectUtil.isNull(retConfig.getConfigValue())) {
            return "|||";
        }
        return retConfig.getConfigValue();
    }

    @Override
    public String selectSmsCaptchaTemplateId() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "sms.captcha.templateId"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return null;
        }
        return retConfig.getConfigValue();
    }

    @Override
    public Integer select24hSmsCaptchaMaxCount() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "sms.captcha.24hMaxCount"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return 10;
        }
        return Integer.parseInt(retConfig.getConfigValue());
    }

    @Override
    public Integer select24hSmsCaptchaErrorMaxCount() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "sms.captcha.24hErrorMaxCount"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return 5;
        }
        return Integer.parseInt(retConfig.getConfigValue());
    }

    @Override
    public Long selectSmsCaptchaExpireTime() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "sms.captcha.expireTime"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return 300L;
        }
        return Long.parseLong(retConfig.getConfigValue());
    }

    @Override
    public Integer selectPrintRecordExpireTime() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "printRecord.expireTime"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return 30;
        }
        return Integer.parseInt(retConfig.getConfigValue());
    }

    @Override
    public Integer selectStudentExpireWarningDays() {
        SysConfig retConfig = TenantHelper.ignore(() -> {
            return baseMapper.selectOne(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getConfigKey, "student.expirationWarningDays"));
        });
        if (ObjectUtil.isNull(retConfig) || StringUtils.isBlank(retConfig.getConfigValue())){
            return null;
        }
        return Integer.parseInt(retConfig.getConfigValue());
    }

}
