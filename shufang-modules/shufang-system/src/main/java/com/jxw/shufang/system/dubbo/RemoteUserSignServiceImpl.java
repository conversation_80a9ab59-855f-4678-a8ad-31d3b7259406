package com.jxw.shufang.system.dubbo;

import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteUserSignService;
import com.jxw.shufang.system.api.model.RemoteSysUserSignBo;
import com.jxw.shufang.system.domain.bo.SysUserSignBo;
import com.jxw.shufang.system.domain.vo.SysUserSignVo;
import com.jxw.shufang.system.service.ISysUserSignService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 用户服务
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserSignServiceImpl implements RemoteUserSignService {

    private final ISysUserSignService iSysUserSignService;


    /**
     * true 为不存在
     */
    @Override
    public Boolean checkSignIsNotExist(RemoteSysUserSignBo bo) {
        SysUserSignBo convert = MapstructUtils.convert(bo, SysUserSignBo.class);
        SysUserSignVo sysUserSign = iSysUserSignService.getSysUserSign(convert);
        if (ObjectUtils.isEmpty(sysUserSign)) {
            return Boolean.TRUE;
        }
        if (0 == sysUserSign.getSignStatus()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    @Override
    public Integer saveOrUpdate(RemoteSysUserSignBo bo) {
        return iSysUserSignService.saveOrUpdateUser(MapstructUtils.convert(bo, SysUserSignBo.class));
    }

    @Override
    public Integer sign(Long userId, Integer type, String version) {
        SysUserSignBo sysUserSignBo = new SysUserSignBo();
        sysUserSignBo.setUserId(userId);
        sysUserSignBo.setType(type);
        sysUserSignBo.setVersion(version);
        sysUserSignBo.setSignStatus(1);
        return iSysUserSignService.saveOrUpdateUser(sysUserSignBo);
    }
}
