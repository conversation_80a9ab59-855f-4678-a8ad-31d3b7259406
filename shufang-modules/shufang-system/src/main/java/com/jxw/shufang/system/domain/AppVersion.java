package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * APP版本管理对象 app_version
 *
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_version")
public class AppVersion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * APP版本管理ID
     */
    @TableId(value = "app_version_id")
    private Long appVersionId;

    /**
     * APP下载地址ossId
     */
    private Long appOssId;



    /**
     * APP版本号（版本号如：1.1.1 版本升级时必须高于上一次设置的值）
     */
    private String appVersion;

    /**
     * APP真实版本号
     */
    private Integer appVersionCode;

    /**
     * APP版本更新说明
     */
    private String appVersionRemarks;

    /**
     * APP状态（0：正常 2：删除）
     */
    @TableLogic
    private String delFlag;


}
