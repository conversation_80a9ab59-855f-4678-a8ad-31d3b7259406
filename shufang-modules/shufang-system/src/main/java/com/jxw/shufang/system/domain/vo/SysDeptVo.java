package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMappers;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.system.domain.SysDept;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 部门视图对象 sys_dept
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMappers({
        @AutoMapper(target = SysDept.class),
})
public class SysDeptVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 父部门名称
     */
    private String parentName;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String deptName;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 负责人ID
     */
    private Long leader;

    /**
     * 负责人
     */
    @ExcelProperty(value = "负责人")
    private String leaderName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 部门状态（0正常 1停用）
     */
    @ExcelProperty(value = "部门状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 是否门店（ 0：不是 1：是）
     */
    @ExcelProperty(value = "是否门店")
    private Boolean isStore;


    private Long systemLogo;
    /**
     * 机构logo对应resource文件ID
     */
    private String systemLogoUrl;

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 支付商户配置id
     */
    private Long payMerchantConfigId;

    @ExcelProperty(value = "剩余天数")
    private Integer remainTime;
    @ExcelProperty(value = "负责人名称")
    private String adminNickName;
    @ExcelProperty(value = "负责人id")
    private Long adminUserId;
    @ExcelProperty(value = "负责人账号")
    private String adminUserName;

    @ExcelIgnore
    /**
     * logoUrl
     */
    private String logoUrl;
    private Long logo;
}
