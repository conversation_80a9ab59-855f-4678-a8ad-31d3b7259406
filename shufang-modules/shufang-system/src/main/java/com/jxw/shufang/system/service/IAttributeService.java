package com.jxw.shufang.system.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.bo.AttributeBo;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttrRelationResultVo;
import com.jxw.shufang.system.domain.vo.AttributeVo;

import java.util.Collection;
import java.util.List;

/**
 * 属性Service接口
 *
 *
 * @date 2024-03-26
 */
public interface IAttributeService {

    /**
     * 查询属性
     */
    AttributeVo queryById(Long attributeId);

    /**
     * 查询属性列表
     */
    TableDataInfo<AttributeVo> queryPageList(AttributeBo bo, PageQuery pageQuery);

    /**
     * 查询属性列表
     */
    List<AttributeVo> queryList(AttributeBo bo);

    /**
     * 新增属性
     */
    Boolean insertByBo(AttributeBo bo);

    /**
     * 修改属性
     */
    Boolean updateByBo(AttributeBo bo);

    /**
     * 校验并批量删除属性信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据分组类型获取属性列表（不带值，只是拿到该有的属性列表）
     *
     * @param groupType 分组类型（表名）
     * @param branchId 分店id
     * @param withUniversally 是否包含通用属性
     * @return 属性列表
     */
    List<AttributeVo> getAttributeByGroupType(String groupType, Long branchId, boolean withUniversally);

    /**
     * 按组类型获取具有值attr
     *
     * @param typeIdList          表主键idList
     * @param groupType       组类型
     * @param branchId        分店id
     * @param withUniversally 是否包含通用属性
     *
     * @date 2024/03/28 03:57:43
     */
    List<AttrRelationResultVo> getAttrWithValueByGroupType(List<Long> typeIdList, String groupType, Long branchId, boolean withUniversally);

    List<AttributeVo> getListByGroupId(Long groupId);

    AttributeVo getById(Long attributeId);

    void cleanAllCache();

    /**
     * 查询搜索属性
     *
     * @param type            类型
     * @param branchId        分店id
     * @param withUniversally 是否包含通用属性
     *
     * @date 2024/04/10 12:54:07
     */
    List<AttributeVo> querySearchAttr(String type,String showLocation, Long branchId, boolean withUniversally);

    /**
     * 查询添加时的属性列表
     *
     * @param type            类型
     * @param branchId        分店id
     * @param withUniversally 是否包含通用属性
     *
     * @date 2024/04/10 12:54:07
     */
    List<AttributeVo> queryAddAttr(String type,String showLocation, Long branchId, boolean withUniversally);


    List<Long> search(String searchJson,String groupType);

    /**
     * 更新类型关系
     *
     * @param type     类型
     * @param courseId 课程id
     * @param list     列表
     *
     * @date 2024/04/16 08:13:41
     */
    void updateTypeRelation(String type, Long courseId, List<AttributeRelationBo> list);
}
