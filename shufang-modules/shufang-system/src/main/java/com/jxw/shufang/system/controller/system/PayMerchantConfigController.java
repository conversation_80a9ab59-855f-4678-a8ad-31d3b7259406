package com.jxw.shufang.system.controller.system;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.domain.bo.QueryMerchantBo;
import com.jxw.shufang.system.domain.bo.SaveMerchantConfigBo;
import com.jxw.shufang.system.domain.bo.UpdateMerchantConfigBo;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigBranchVo;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigVo;
import com.jxw.shufang.system.service.IPayMerchantConfigService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;

/**
 * @author: cyj
 * @date: 2025/5/6
 * 支付商户配置管理
 */
@RestController
@RequestMapping("/pay/merchant/config")
public class PayMerchantConfigController {

    @Value("${branch.pay.name.prefix:xuewangshufang-}")
    private String branchPayNamePrefix;

    @Resource
    private  IPayMerchantConfigService payMerchantConfigService;

    /**
     * 查询支付商户配置列表
     * @param queryMerchantBo
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("system:pay:merchant:list")
    @GetMapping("list")
    public TableDataInfo<PayMerchantConfigVo> listPayMerchantConfig(QueryMerchantBo queryMerchantBo, PageQuery pageQuery) {
        return TableDataInfo.build(payMerchantConfigService.listConfig(queryMerchantBo, pageQuery));
    }

    /**
     * 查询关联门店列表
     * @param merchantConfigId
     * @param pageQuery
     * @return
     */
    @SaCheckPermission("system:pay:merchant:list")
    @GetMapping("listBranch")
    public TableDataInfo<PayMerchantConfigBranchVo> listConfigBranch(Long merchantConfigId, PageQuery pageQuery) {
        if (null == merchantConfigId) {
            return TableDataInfo.build(new ArrayList<>(1));
        }
        return payMerchantConfigService.listConfigBranch(merchantConfigId, pageQuery);
    }

    /**
     * 查询支付商户配置详情
     * @param merchantConfigId
     * @return
     */
    @SaCheckPermission("system:pay:merchant:query")
    @GetMapping("{merchantConfigId}")
    public R<PayMerchantConfigVo> queryConfig(@PathVariable Long merchantConfigId) {
        return R.ok(payMerchantConfigService.queryVoById(merchantConfigId));
    }

    /**
     * 新增支付商户配置
     * @param bo
     * @return
     */
    @SaCheckPermission("system:pay:merchant:add")
    @PostMapping
    @RepeatSubmit()
    public R<Void> addConfig(@Validated(AddGroup.class) @RequestBody SaveMerchantConfigBo bo) {
        Long deptId = LoginHelper.getDeptId();
        Long userId = LoginHelper.getUserId();
        return payMerchantConfigService.saveConfig(bo,deptId,userId) ? R.ok() : R.fail("新增失败");
    }

    /**
     * 修改支付商户配置
     * @param bo
     * @return
     */
    @SaCheckPermission("system:pay:merchant:add")
    @PutMapping
    @RepeatSubmit()
    public R<Void> updateConfig(@Validated(EditGroup.class) @RequestBody UpdateMerchantConfigBo bo) {
        if (null == bo.getId()) {
            return R.fail("请选择配置后操作。");
        }
        return payMerchantConfigService.updateConfig(bo) ? R.ok() : R.fail("修改失败");
    }

    /**
     * 删除支付商户配置
     * @param id
     * @return
     */
    @SaCheckPermission("system:pay:merchant:delete")
    @DeleteMapping("{id}")
    public R<Void> deleteConfig(@PathVariable Long id) {
        if (null == id) {
            return R.fail("请选择配置后操作。");
        }
        return payMerchantConfigService.deleteConfig(id) ? R.ok() : R.fail("删除失败");
    }

    /**
     * 获取一键生成的商户唯一标识
     */
    @GetMapping("/createMerchantCode")
    public R<String> getMerchantCode(String merchantName) {
        if (StringUtils.isEmpty(merchantName)) {
            return R.fail("请输入商户名称");
        }
        String result = branchPayNamePrefix.concat(this.getFirstLetters(merchantName));
        return R.ok(result);
    }

    public  String getFirstLetters(String chinese) {
        return PinyinUtil.getFirstLetter(chinese, "");
    }
}
