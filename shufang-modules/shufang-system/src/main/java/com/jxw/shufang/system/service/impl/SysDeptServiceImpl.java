package com.jxw.shufang.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.jxw.shufang.common.core.constant.CacheNames;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.*;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.helper.DataBaseHelper;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.redis.utils.CacheUtils;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.system.domain.SysDept;
import com.jxw.shufang.system.domain.SysRole;
import com.jxw.shufang.system.domain.SysUser;
import com.jxw.shufang.system.domain.bo.SysDeptBo;
import com.jxw.shufang.system.domain.bo.SysUserBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.domain.vo.SysUserVo;
import com.jxw.shufang.system.mapper.SysDeptMapper;
import com.jxw.shufang.system.mapper.SysRoleMapper;
import com.jxw.shufang.system.mapper.SysUserMapper;
import com.jxw.shufang.system.service.ISysDeptService;
import com.jxw.shufang.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *

 */
@RequiredArgsConstructor
@Service
public class SysDeptServiceImpl implements ISysDeptService {

    private final SysDeptMapper baseMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserMapper userMapper;
    private final ISysUserService iSysUserService;
    @DubboReference
    private RemoteFileService remoteFileService;


    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDeptVo> selectDeptList(SysDeptBo dept) {
        LambdaQueryWrapper<SysDept> lqw = buildQueryWrapper(dept);
        List<SysDeptVo> sysDeptVos = baseMapper.selectDeptList(lqw);
        if (CollectionUtils.isEmpty(sysDeptVos)) {
            return sysDeptVos;
        }
        if (Boolean.TRUE.equals(dept.getOpenDetail())) {
            buildDeptVoDetail(sysDeptVos);
        }
        if (Boolean.TRUE.equals(dept.getOpenLogo())) {
            buildDeptUrl(sysDeptVos);
        }
        return sysDeptVos;
    }

    @Override
    public void buildDeptUrl(List<SysDeptVo> sysDeptVos) {
        if (CollectionUtils.isEmpty(sysDeptVos)){
            return;
        }
        List<Long> list = sysDeptVos.stream().map(SysDeptVo::getLogo).distinct().toList();
        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIdList(list);
        if (CollectionUtils.isEmpty(remoteFiles)){
            return;
        }
        Map<Long, RemoteFile> fileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, Function.identity()));
        for (SysDeptVo sysDeptVo : sysDeptVos) {
            Long logo = sysDeptVo.getLogo();
            if (ObjectUtils.isEmpty(logo)||!fileMap.containsKey(logo)){
                continue;
            }
            RemoteFile remoteFile = fileMap.get(logo);
            sysDeptVo.setLogoUrl(remoteFile.getUrl());
        }
    }

    /**
     * 查询部门树结构信息
     *
     * @param bo 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<Tree<Long>> selectDeptTreeList(SysDeptBo bo) {
        // 只查询未禁用部门
        bo.setStatus(UserConstants.DEPT_NORMAL);
        LambdaQueryWrapper<SysDept> lqw = buildQueryWrapper(bo);
        List<SysDeptVo> depts = baseMapper.selectDeptList(lqw);
        return buildDeptTreeSelect(depts);
    }

    private LambdaQueryWrapper<SysDept> buildQueryWrapper(SysDeptBo bo) {
        LambdaQueryWrapper<SysDept> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysDept::getDelFlag, "0");
        lqw.eq(ObjectUtil.isNotNull(bo.getDeptId()), SysDept::getDeptId, bo.getDeptId());
        lqw.eq(ObjectUtil.isNotNull(bo.getParentId()), SysDept::getParentId, bo.getParentId());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), SysDept::getDeptName, bo.getDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptNameExact()), SysDept::getDeptName, bo.getDeptNameExact());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysDept::getStatus, bo.getStatus());
        lqw.eq(bo.getIsStore()!=null, SysDept::getIsStore, bo.getIsStore());
        lqw.in(CollUtil.isNotEmpty(bo.getDeptIds()), SysDept::getDeptId, bo.getDeptIds());
        lqw.eq(bo.getPayMerchantConfigId() != null, SysDept::getPayMerchantConfigId, bo.getPayMerchantConfigId());
        lqw.in(CollUtil.isNotEmpty(bo.getDeptIdList()), SysDept::getDeptId, bo.getDeptIdList());
        lqw.orderByAsc(SysDept::getParentId);
        lqw.orderByAsc(SysDept::getOrderNum);
        lqw.orderByAsc(SysDept::getDeptId);
        return lqw;
    }

    private void buildDeptVoDetail(List<SysDeptVo> sysDeptVos) {
        List<Long> list = sysDeptVos.stream().filter(v -> Boolean.FALSE.equals(v.getIsStore())).map(SysDeptVo::getLeader).toList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        SysUserBo sysUserBo = new SysUserBo();
        sysUserBo.setUserIds(list);
        List<SysUserVo> sysUserVos = iSysUserService.selectUserList(sysUserBo);
        if (CollectionUtils.isEmpty(sysDeptVos)) {
            return;
        }
        Map<Long, SysUserVo> sysUserVoMap = sysUserVos.stream().collect(Collectors.toMap(SysUserVo::getUserId, Function.identity()));
        for (SysDeptVo sysDeptVo : sysDeptVos) {
            if (Boolean.TRUE.equals(sysDeptVo.getIsStore())) {
                continue;
            }
            boolean b = sysUserVoMap.containsKey(sysDeptVo.getLeader());
            if (Boolean.TRUE.equals(b)) {
                SysUserVo sysUserVo = sysUserVoMap.get(sysDeptVo.getLeader());
                sysDeptVo.setAdminUserId(sysUserVo.getUserId());
                sysDeptVo.setAdminUserName(sysUserVo.getUserName());
                sysDeptVo.setAdminNickName(sysUserVo.getNickName());
            }
        }
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Tree<Long>> buildDeptTreeSelect(List<SysDeptVo> depts) {
        if (CollUtil.isEmpty(depts)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.build(depts, (dept, tree) ->
            tree.setId(dept.getDeptId())
                .setParentId(dept.getParentId())
                .setName(dept.getDeptName())
                .setWeight(dept.getOrderNum())
                .putExtra("isStore", dept.getIsStore())
        );
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectById(roleId);
        return baseMapper.selectDeptListByRoleId(roleId, role.getDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
   // @Cacheable(cacheNames = CacheNames.SYS_DEPT, key = "#deptId")
    @Override
    public SysDeptVo selectDeptById(Long deptId) {
        SysDeptVo dept = baseMapper.selectVoById(deptId);
        if (ObjectUtil.isNull(dept)) {
            return null;
        }
        SysDeptVo parentDept = baseMapper.selectVoOne(new LambdaQueryWrapper<SysDept>()
            .select(SysDept::getDeptName).eq(SysDept::getDeptId, dept.getParentId()));
        dept.setParentName(ObjectUtil.isNotNull(parentDept) ? parentDept.getDeptName() : null);

        return dept;
    }

    /**
     * 查询部门信息根据ID集合
     *
     * @param deptIds
     */
    @Override
    public List<SysDeptVo> selectDeptByIds(List<Long> deptIds) {
        if (ObjectUtil.isEmpty(deptIds)) return ListUtil.toList();

        LambdaQueryWrapper<SysDept> lqw = Wrappers.lambdaQuery();
        lqw.in(SysDept::getDeptId, deptIds);
        List<SysDeptVo> sysDeptVos = baseMapper.selectVoList(lqw);
        if (ObjectUtil.isEmpty(sysDeptVos)) return ListUtil.toList();
        return sysDeptVos;
    }

    /**
     * 通过部门ID查询部门名称
     *
     * @param deptIds 部门ID串逗号分隔
     * @return 部门名称串逗号分隔
     */
    public String selectDeptNameByIds(String deptIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(deptIds, Convert::toLong)) {
            SysDeptVo vo = SpringUtils.getAopProxy(this).selectDeptById(id);
            if (ObjectUtil.isNotNull(vo)) {
                list.add(vo.getDeptName());
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    /**
     * 根据ID查询所有子部门数（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public long selectNormalChildrenDeptById(Long deptId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getStatus, UserConstants.DEPT_NORMAL)
            .apply(DataBaseHelper.findInSet(deptId, "ancestors")));
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        return baseMapper.exists(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getParentId, deptId));
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        return userMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getDeptId, deptId));
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(SysDeptBo dept) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptName, dept.getDeptName())
            .eq(SysDept::getParentId, dept.getParentId())
            .ne(ObjectUtil.isNotNull(dept.getDeptId()), SysDept::getDeptId, dept.getDeptId()));
        return !exist;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId) {
        if (ObjectUtil.isNull(deptId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        SysDeptVo dept = baseMapper.selectDeptById(deptId);
        if (ObjectUtil.isNull(dept)) {
            throw new ServiceException("没有权限访问部门数据！");
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDeptBo bo){
        SysDept parentDept = baseMapper.selectById(bo.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(parentDept.getStatus())) {
            throw new ServiceException("部门停用，不允许新增");
        }
        if (parentDept.getIsStore()) {
            throw new ServiceException(parentDept.getDeptName() + "已经是门店，不允许在门店下面新增任何组织");
        }
        SysDept dept = MapstructUtils.convert(bo, SysDept.class);
        dept.setAncestors(parentDept.getAncestors() + StringUtils.SEPARATOR + dept.getParentId());
        int insert = baseMapper.insert(dept);
        if (insert>0) {
            bo.setDeptId(dept.getDeptId());
        }
        return insert;
    }

    /**
     * 修改保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    @CacheEvict(cacheNames = CacheNames.SYS_DEPT, key = "#bo.deptId")
    @Override
    public int updateDept(SysDeptBo bo) {
        SysDept dept = MapstructUtils.convert(bo, SysDept.class);
        SysDept oldDept = baseMapper.selectById(dept.getDeptId());

        //判断是否有子节点，有子节点不允许修改为门店
        if (Boolean.TRUE.equals(bo.getIsStore())) {
            boolean exists = baseMapper.exists(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getParentId, dept.getDeptId())
                .eq(SysDept::getStatus, UserConstants.DEPT_NORMAL));
            Assert.isFalse(exists, () -> {
                throw new ServiceException("存在子节点，不允许修改为门店");
            });
        }

        if (!oldDept.getParentId().equals(dept.getParentId())) {
            // 如果是新父部门 则校验是否具有新父部门权限 避免越权
            this.checkDeptDataScope(dept.getParentId());
            SysDept newParentDept = baseMapper.selectById(dept.getParentId());
            if (ObjectUtil.isNotNull(newParentDept) && ObjectUtil.isNotNull(oldDept)) {
                String newAncestors = newParentDept.getAncestors() + StringUtils.SEPARATOR + newParentDept.getDeptId();
                String oldAncestors = oldDept.getAncestors();
                dept.setAncestors(newAncestors);
                updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
            }
        }
        int result = baseMapper.updateById(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
            && !StringUtils.equals(UserConstants.DEPT_NORMAL, dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        baseMapper.update(null, new LambdaUpdateWrapper<SysDept>()
            .set(SysDept::getStatus, UserConstants.DEPT_NORMAL)
            .in(SysDept::getDeptId, Arrays.asList(deptIds)));
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    private void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = baseMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .apply(DataBaseHelper.findInSet(deptId, "ancestors")));
        List<SysDept> list = new ArrayList<>();
        for (SysDept child : children) {
            SysDept dept = new SysDept();
            dept.setDeptId(child.getDeptId());
            dept.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            list.add(dept);
        }
        if (CollUtil.isNotEmpty(list)) {
            if (baseMapper.updateBatchById(list)) {
                list.forEach(dept -> CacheUtils.evict(CacheNames.SYS_DEPT, dept.getDeptId()));
            }
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @CacheEvict(cacheNames = CacheNames.SYS_DEPT, key = "#deptId")
    @Override
    public int deleteDeptById(Long deptId) {
        return baseMapper.deleteById(deptId);
    }

    @Override
    public List<SysDeptVo> getSelfAndChildShopList(Long deptId) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getStatus, UserConstants.DEPT_NORMAL);
        wrapper.eq(SysDept::getDelFlag, "0");
        wrapper.eq(SysDept::getIsStore, Boolean.TRUE);
        wrapper.and(i -> i.eq(SysDept::getDeptId, deptId).or().apply(DataBaseHelper.findInSet(deptId, "ancestors")));

        if (LoginHelper.isLogin()) {
            List<SysDeptVo> sysDeptVos = baseMapper.selectDeptList(wrapper);
            return sysDeptVos;
        }
        return DataPermissionHelper.ignore(() -> {
            List<SysDeptVo> sysDeptVos = baseMapper.selectDeptList(wrapper);
            return sysDeptVos;
        });
    }

    @Override
    public List<SysDeptVo> getSelfAndChildShopListByDeptIds(Collection<Long> deptIds) {
        List<SysDeptVo> list = new ArrayList<>();
        for (Long deptId : deptIds) {
            LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysDept::getStatus, UserConstants.DEPT_NORMAL);
            wrapper.eq(SysDept::getDelFlag, "0");
            wrapper.eq(SysDept::getIsStore, Boolean.TRUE);
            wrapper.and(i -> i.eq(SysDept::getDeptId, deptId).or().apply(DataBaseHelper.findInSet(deptId, "ancestors")));

            if (LoginHelper.isLogin()) {
                List<SysDeptVo> sysDeptVos = baseMapper.selectDeptList(wrapper);
                list.addAll(sysDeptVos);
                continue;
            }
            list.addAll(DataPermissionHelper.ignore(() -> {
                List<SysDeptVo> sysDeptVos = baseMapper.selectDeptList(wrapper);
                return sysDeptVos;
            }));
        }
        return list;

    }


    @Override
    public List<SysDeptVo> getDeptChildrenList(Long deptId) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getStatus, UserConstants.DEPT_NORMAL);
        wrapper.eq(SysDept::getDelFlag, "0");
        wrapper.apply(DataBaseHelper.findInSet(deptId, "ancestors"));
        return baseMapper.selectDeptList(wrapper);
    }

    @Override
    public Map<Long, String> getDeptAncestors2Map(Set<Long> branchDeptIds) {
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getStatus, UserConstants.DEPT_NORMAL);
        wrapper.eq(SysDept::getDelFlag, "0");
        wrapper.in(SysDept::getDeptId, branchDeptIds);
        List<SysDeptVo> sysDeptVos = DataPermissionHelper.ignore(() -> baseMapper.selectDeptList(wrapper));
        return StreamUtils.toMap(sysDeptVos, SysDeptVo::getDeptId, SysDeptVo::getAncestors);
    }

    @Override
    public List<Long> getAllParentDeptId(Long deptId) {
        SysDeptVo sysDeptVo = baseMapper.selectDeptById(deptId);
        if (sysDeptVo != null) {
            String ancestors = sysDeptVo.getAncestors();
            return Convert.toList(Long.class, ancestors.split(StringUtils.SEPARATOR));
        }
        return ListUtil.toList();
    }
    @Override
    public SysDeptVo getDeptSystemConfig(Long deptId) {
        SysDeptVo config = getConfig(deptId);
        if (ObjectUtils.isEmpty(config)) {
            return null;
        }
        if (!(config.getSystemLogo() == null || config.getSystemLogo() == 0)) {
            config.setSystemLogoUrl(remoteFileService.selectUrlByIds(String.valueOf(config.getSystemLogo())));
        }
        return config;

    }

    private SysDeptVo getConfig(Long deptId) {
        SysDeptVo sysDeptVo =
            DataPermissionHelper.ignore(()->baseMapper.selectDeptById(deptId));
        if (sysDeptVo == null) {
            return null;
        }
        if (checkBranchIsNotConfig(sysDeptVo)&&sysDeptVo.getParentId()!=0L) {
            return getConfig(sysDeptVo.getParentId());

        }
        return sysDeptVo;
    }

    private static boolean checkBranchIsNotConfig(SysDeptVo sysDeptVo) {
        return StringUtils.isEmpty(sysDeptVo.getBrandName()) && (sysDeptVo.getSystemLogo() == null || sysDeptVo.getSystemLogo() == 0L);
    }

    @Override
    public Map<Long, Integer> countPayMerchant(Set<Long> payMerchantConfigIdSet) {
        if (CollectionUtils.isEmpty(payMerchantConfigIdSet)) {
            return new HashMap<>(1);
        }
        List<SysDept> sysDeptList = baseMapper
            .selectList(Wrappers.lambdaQuery(SysDept.class)
                .in(SysDept::getPayMerchantConfigId, payMerchantConfigIdSet)
                .select(SysDept::getDeptId, SysDept::getPayMerchantConfigId));
        Map<Long, Integer> countMap = Maps.newHashMapWithExpectedSize(payMerchantConfigIdSet.size());
        sysDeptList.forEach(sysDept -> countMap.merge(sysDept.getPayMerchantConfigId(), 1, Integer::sum));
        return countMap;
    }

    @Override
    public boolean flushPayMerchantId(Set<Long> deptIdSet, Long payMerchantConfigId) {
        if (CollectionUtils.isEmpty(deptIdSet) || payMerchantConfigId == null) {
            return false;
        }
        return baseMapper.update(Wrappers.lambdaUpdate(SysDept.class).in(SysDept::getDeptId, deptIdSet)
            .eq(SysDept::getIsStore, Boolean.TRUE)
            .set(SysDept::getPayMerchantConfigId, payMerchantConfigId)) > 0;
    }

    @Override
    public void clearMerchantByPayMerchantConfigId(Long payMerchantConfigId) {
        if (null == payMerchantConfigId){
            throw new ServiceException("缺少商户配置ID");
        }
        baseMapper.update(Wrappers.lambdaUpdate(SysDept.class)
            .in(SysDept::getPayMerchantConfigId,payMerchantConfigId)
            .set(SysDept::getPayMerchantConfigId,null));
    }

    @Override
    public IPage<SysDeptVo> selectDeptPage(SysDeptBo sysDeptBo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysDept> wrapper = buildQueryWrapper(sysDeptBo);
        return baseMapper.selectVoPage(pageQuery.build(), wrapper);
    }

    @Override
    public void cancelDeptMerchantConfig(Long payMerchantConfigId) {
        if (payMerchantConfigId == null) {
            throw new ServiceException("参数错误");
        }
        SysDept sysDept = new SysDept();
        sysDept.setPayMerchantConfigId(null);

        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDept::getPayMerchantConfigId, payMerchantConfigId);
        baseMapper.update(sysDept, wrapper);
    }

    @Override
    public List<SysDeptVo> getDeptListByPayMerchantConfigIds(List<Long> payMerchantConfigId) {
        if (CollectionUtils.isEmpty(payMerchantConfigId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SysDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysDept::getPayMerchantConfigId, payMerchantConfigId);
        return baseMapper.selectVoList(wrapper);
    }

    @Override
    public List<SysDeptVo> getDeptListByPayMerchantConfigId(Long payMerchantConfigId) {
        return getDeptListByPayMerchantConfigIds(Collections.singletonList(payMerchantConfigId));
    }
    /**
     * 根据代理商ID查询代理商信息（无缓存接口）
     *
     * @param deptId
     * @return
     */
    @Override
    public SysDeptVo selectDeptByIdWithoutCache(Long deptId) {
        return this.selectDeptById(deptId);
    }

    @Override
    public List<SysDeptVo> selectDeptListWithoutPermission(SysDeptBo dept) {
        LambdaQueryWrapper<SysDept> lqw = buildQueryWrapper(dept);
        List<SysDeptVo> sysDeptVos = baseMapper.selectDeptListWithoutPermission(lqw);
        if (CollectionUtils.isEmpty(sysDeptVos)) {
            return sysDeptVos;
        }
        if (Boolean.TRUE.equals(dept.getOpenDetail())) {
            buildDeptVoDetail(sysDeptVos);
        }


        return sysDeptVos;
    }

    @Override
    public Boolean transfer(Long deptId, Integer amount) throws ServiceException {
        return baseMapper.updateMoney(amount, deptId) > 0;
    }

    @CacheEvict(cacheNames = CacheNames.SYS_DEPT, key = "#bo.deptId")
    @Override
    public int updateDeptLogo(SysDeptBo bo) {
        SysDept dept = MapstructUtils.convert(bo, SysDept.class);
        return baseMapper.updateById(dept);
    }

    /**
     * 根据部门ID查询上一级部门信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDeptVo selectPreDeptById(Long deptId) {
        if (ObjectUtil.isNull(deptId)) return null;
        //查询当前代理商信息
        SysDeptVo sysDeptVo = this.selectDeptById(deptId);
        if (ObjectUtil.isNull(sysDeptVo)) return null;

        //查询上一级代理商信息
        SysDeptVo preSysDeptVo = this.selectDeptById(sysDeptVo.getParentId());
        if (ObjectUtil.isNull(preSysDeptVo)) return null;
        return preSysDeptVo;
    }

}
