package com.jxw.shufang.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import com.jxw.shufang.common.excel.annotation.ExcelDictFormat;
import com.jxw.shufang.common.excel.convert.ExcelDictConvert;
import com.jxw.shufang.common.translation.annotation.Translation;
import com.jxw.shufang.common.translation.constant.TransConstant;
import com.jxw.shufang.system.domain.AppVersion;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * APP版本管理视图对象 app_version
 *
 * @date 2024-07-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AppVersion.class)
public class AppVersionV2Vo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * APP版本管理ID
     */
    @ExcelProperty(value = "APP版本管理ID")
    private Long appVersionId;

    /**
     * APP下载地址ossId
     */
    @ExcelProperty(value = "APP下载地址ossId")
    private Long appOssId;

    private String appOssUrl;

    /**
     * APP版本号（版本号如：1.1.1 版本升级时必须高于上一次设置的值）
     */
    @ExcelProperty(value = "APP版本号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "版=本号如：1.1.1,版=本升级时必须高于上一次设置的值")
    private String appVersion;

    /**
     * APP真实版本号
     */
    @ExcelProperty(value = "APP真实版本号")
    private Integer appVersionCode;

    /**
     * APP版本更新说明
     */
    @ExcelProperty(value = "APP版本更新说明")
    private String appVersionRemarks;

}
