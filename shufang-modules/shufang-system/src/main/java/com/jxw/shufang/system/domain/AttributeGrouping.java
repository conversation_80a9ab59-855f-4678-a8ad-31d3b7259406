package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 属性分组对象 attribute_grouping
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attribute_grouping")
public class AttributeGrouping extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性组ID
     */
    @TableId(value = "attribute_grouping_id")
    private Long attributeGroupingId;

    /**
     * 组名
     */
    private String attributeGroupingName;

    /**
     * 分店id（为-1时所有分店可以使用）
     */
    private Long branchId;

    /**
     * 表名
     */
    private String type;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;


}
