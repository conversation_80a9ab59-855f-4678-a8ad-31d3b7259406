package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.SysNoticeUserBo;
import com.jxw.shufang.system.domain.vo.SysNoticeUserVo;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;
import com.jxw.shufang.system.service.ISysNoticeUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * noticeUser
 *
 * @date 2024-06-30
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/noticeUser")
public class SysNoticeUserController extends BaseController {

    private final ISysNoticeUserService sysNoticeUserService;

    /**
     * 查询noticeUser列表
     */
    @SaCheckPermission("system:noticeUser:list")
    @GetMapping("/list")
    public TableDataInfo<SysNoticeUserVo> list(SysNoticeUserBo bo, PageQuery pageQuery) {
        return sysNoticeUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询noticeUser列表
     */
    @SaCheckPermission("system:noticeUser:list")
    @GetMapping("/queryList")
    public R<List<SysNoticeUserVo>> queryList(SysNoticeUserBo bo) {
        return R.ok(sysNoticeUserService.queryList(bo));
    }

    /**
     * 查询自己的公告列表
     */
    @GetMapping("/myNoticeList")
    public R<List<SysNoticeUserVo>> myNoticeList(SysNoticeUserBo bo) {
        bo.setUserId(LoginHelper.getUserId());
        bo.setNoticeStatus("0");
        return R.ok(sysNoticeUserService.queryList(bo));
    }

    /**
     * 查询某个公告详情，并标记为已读
     */
    @GetMapping("/detail/{noticeUserId}")
    public R<SysNoticeVo> detail(@PathVariable("noticeUserId") Long noticeUserId) {
        return R.ok(sysNoticeUserService.detail(noticeUserId));
    }

    /**
     * 导出noticeUser列表
     */
    @SaCheckPermission("system:noticeUser:export")
    @Log(title = "noticeUser", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysNoticeUserBo bo, HttpServletResponse response) {
        List<SysNoticeUserVo> list = sysNoticeUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "noticeUser", SysNoticeUserVo.class, response);
    }

    /**
     * 获取noticeUser详细信息
     *
     * @param noticeUserId 主键
     */
    @SaCheckPermission("system:noticeUser:query")
    @GetMapping("/{noticeUserId}")
    public R<SysNoticeUserVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long noticeUserId) {
        return R.ok(sysNoticeUserService.queryById(noticeUserId));
    }

    /**
     * 新增noticeUser
     */
    @SaCheckPermission("system:noticeUser:add")
    @Log(title = "noticeUser", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysNoticeUserBo bo) {
        return toAjax(sysNoticeUserService.insertByBo(bo));
    }

    /**
     * 修改noticeUser
     */
    @SaCheckPermission("system:noticeUser:edit")
    @Log(title = "noticeUser", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysNoticeUserBo bo) {
        return toAjax(sysNoticeUserService.updateByBo(bo));
    }

    /**
     * 删除noticeUser
     *
     * @param noticeUserIds 主键串
     */
    @SaCheckPermission("system:noticeUser:remove")
    @Log(title = "noticeUser", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeUserIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] noticeUserIds) {
        return toAjax(sysNoticeUserService.deleteWithValidByIds(List.of(noticeUserIds), true));
    }

    /**
     * 获取未读通知数量
     * 每个系统用户都应拥有该权限，所以不做权限判断
     */
    @GetMapping("/unreadCount")
    public R<Long> getUnreadCount() {
        return R.ok(sysNoticeUserService.getUnreadCount(LoginHelper.getUserId()));
    }


}
