package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2025/6/5 16:07
 * @Version 1
 * @Description
 */
@Data
public class SaveMerchantConfigBo{
    /**
     * 商户名称
     */
    @NotBlank(message = "商户名称不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 17, message = "商户名称长度不能超过17个字符", groups = {AddGroup.class, EditGroup.class})
    private String merchantName;
    /**
     * 接入方名称
     */
    @NotBlank(message = "接入方名称不为空", groups = {AddGroup.class, EditGroup.class})
    private String channelCode;

    /**
     * 是否启用
     */
    @NotNull(message = "请选择状态", groups = {AddGroup.class, EditGroup.class})
    private Boolean enable;
    /**
     * 应用ID
     */
    @NotBlank(message = "唯一标识不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 32, message = "唯一标识不能长度不能超过32个字符", groups = {AddGroup.class, EditGroup.class})
    private String appId;

    /**
     * 批量同步的部门id
     */
    @NotEmpty(message = "部门id不为空", groups = {AddGroup.class, EditGroup.class})
    private Set<Long> deptIdList;

    @NotEmpty(message = "银联平台appId不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "银联平台appId不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String unionAppId;

    @NotEmpty(message = "银联平台key不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "银联平台key不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String unionKey;

    @NotEmpty(message = "银联平台mid不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "银联平台mid不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String midNumber;

    @NotEmpty(message = "银联平台tid不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "银联平台tid不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String tidNumber;

    @NotEmpty(message = "银联平台来源编号不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 5, message = "银联平台来源编号不能长度不能超过5个字符", groups = {AddGroup.class, EditGroup.class})
    private String sourceNumber;

    @NotEmpty(message = "银联平台网付密钥不为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 50, message = "银联平台网付密钥不能长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String onlineSecretKey;
}
