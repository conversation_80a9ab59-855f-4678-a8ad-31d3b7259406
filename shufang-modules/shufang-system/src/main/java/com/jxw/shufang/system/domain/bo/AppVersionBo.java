package com.jxw.shufang.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.AppVersion;

/**
 * APP版本管理业务对象 app_version
 *
 * @date 2024-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AppVersion.class, reverseConvertGenerate = false)
public class AppVersionBo extends BaseEntity {

    /**
     * APP版本管理ID
     */
    @NotNull(message = "APP版本管理ID不能为空", groups = { EditGroup.class })
    private Long appVersionId;

    /**
     * APP下载地址ossId
     */
    @NotNull(message = "APP下载地址ossId不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long appOssId;

    /**
     * APP版本号（版本号如：1.1.1 版本升级时必须高于上一次设置的值）
     */
    @NotBlank(message = "APP版本号（版本号如：1.1.1 版本升级时必须高于上一次设置的值）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appVersion;

    /**
     * APP真实版本号
     */
    @NotNull(message = "APP真实版本号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer appVersionCode;

    /**
     * APP版本更新说明
     */
    @NotBlank(message = "APP版本更新说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String appVersionRemarks;


}
