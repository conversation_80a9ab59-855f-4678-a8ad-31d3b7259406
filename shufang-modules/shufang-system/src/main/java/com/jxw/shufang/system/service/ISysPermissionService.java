package com.jxw.shufang.system.service;

import java.util.Set;

/**
 * 用户权限处理
 *

 */
public interface ISysPermissionService {

    /**
     * 获取角色数据权限
     *
     * @param userId  用户id
     * @return 角色权限信息
     */
    Set<String> getRolePermission(Long userId);


    /**
     * 获取角色数据权限通过角色id
     *
     * @param roleId  角色id
     * @return 角色权限信息
     */
    Set<String> getRolePermissionByRoleId(Long roleId);

    /**
     * 获取菜单数据权限
     *
     * @param userId  用户id
     * @return 菜单权限信息
     */
    Set<String> getMenuPermission(Long userId);

    /**
     * 获取菜单数据权限通过角色id
     *
     * @param roleId 角色Id
     * @return 数据权限
     */
    Set<String>  getMenuPermissionByRoleId(Long roleId);


}
