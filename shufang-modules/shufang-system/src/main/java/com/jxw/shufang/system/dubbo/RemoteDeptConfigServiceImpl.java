package com.jxw.shufang.system.dubbo;

import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteDeptConfigService;
import com.jxw.shufang.system.api.domain.vo.RemoteSysDeptConfigVo;
import com.jxw.shufang.system.domain.bo.SysDeptConfigBo;
import com.jxw.shufang.system.domain.vo.SysDeptConfigVo;
import com.jxw.shufang.system.service.ISysDeptConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteDeptConfigServiceImpl implements RemoteDeptConfigService {

    private final ISysDeptConfigService service;

    @Override
    public RemoteSysDeptConfigVo selectDeptById(Long deptId) {
        SysDeptConfigVo sysDeptConfigVo = service.selectDeptById(deptId);
        if (ObjectUtils.isEmpty(sysDeptConfigVo)){
            SysDeptConfigBo sysDeptConfigBo = new SysDeptConfigBo();
            sysDeptConfigBo.setDeptId(deptId);
            service.saveOrUpdate(sysDeptConfigBo);
        }

        return MapstructUtils.convert(sysDeptConfigVo,RemoteSysDeptConfigVo.class);
    }
}
