package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 商户支付配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-13 14:00:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pay_merchant_config")
public class PayMerchantConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 公户名称
     */
    private String merchantName;

    /**
     * 接入方名称
     */
    private String channelCode;

    /**
     * 应用ID
     */
    private String appId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType;
    /**
     * 支付方式(unionpay-银联聚合支付)
     */
    private String payCode;
    /**
     * 聚合二维码的方式
     */
    private String wayCode;

    /**
     * 默认商户(0:否，1：是)
     */
    private Boolean defaultMerchant;

    /**
     * 配置json参数
     */
    private String configParamJson;

    /**
     * 删除标志
     */
    private Boolean isDelete;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否启用
     */
    private Boolean enable;
}
