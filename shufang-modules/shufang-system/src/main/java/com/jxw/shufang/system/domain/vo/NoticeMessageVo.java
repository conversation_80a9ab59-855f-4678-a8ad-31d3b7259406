package com.jxw.shufang.system.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class NoticeMessageVo {

    private Integer id;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 拼接参数
     */
    private String params;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 通知类型 1-站内信
     */
    private Integer noticeType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 发送人
     */
    private Long fromUserId;

    /**
     * 发送人姓名
     */
    private String fromUserName;

    /**
     * 接收人
     */
    private Long toUserId;

    /**
     * 接收人姓名
     */
    private String toUserName;

    /**
     * 阅读时间
     */
    private Long readTime;

    /**
     * 处理跳转链接
     */
    private List<String> linkUrlList;

    private String processUrl;

}
