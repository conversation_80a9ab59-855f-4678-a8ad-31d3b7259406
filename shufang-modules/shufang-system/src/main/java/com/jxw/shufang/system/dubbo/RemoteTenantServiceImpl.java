package com.jxw.shufang.system.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteTenantService;
import com.jxw.shufang.system.api.domain.vo.RemoteTenantVo;
import com.jxw.shufang.system.domain.bo.SysTenantBo;
import com.jxw.shufang.system.domain.vo.SysTenantVo;
import com.jxw.shufang.system.service.ISysTenantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteTenantServiceImpl implements RemoteTenantService {

    private final ISysTenantService tenantService;

    /**
     * 根据租户id获取租户详情
     */
    @Override
    public RemoteTenantVo queryByTenantId(String tenantId) {
        SysTenantVo vo = tenantService.queryByTenantId(tenantId);
        return MapstructUtils.convert(vo, RemoteTenantVo.class);
    }

    /**
     * 获取租户列表
     */
    @Override
    public List<RemoteTenantVo> queryList() {
        List<SysTenantVo> list = tenantService.queryList(new SysTenantBo());
        return MapstructUtils.convert(list, RemoteTenantVo.class);
    }

}
