package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * noticeUser对象 sys_notice_user
 *
 * @date 2024-06-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_notice_user")
public class SysNoticeUser extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "notice_user_id")
    private Long noticeUserId;

    /**
     * 接收人用户ID
     */
    private Long userId;

    /**
     * 读取状态（0已读 2未读）
     */
    private String readStatus;

    /**
     * 公告ID
     */
    private Long noticeId;


}
