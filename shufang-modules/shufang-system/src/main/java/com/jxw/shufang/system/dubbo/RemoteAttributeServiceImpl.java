package com.jxw.shufang.system.dubbo;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.service.IAttributeGroupingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.excel.utils.ExcelUtil;
import com.jxw.shufang.common.json.utils.JsonUtils;
import com.jxw.shufang.system.api.RemoteAttributeService;
import com.jxw.shufang.system.api.domain.bo.RemoteAttributeRelationBo;
import com.jxw.shufang.system.api.domain.vo.RemoteAttrRelationResultVo;
import com.jxw.shufang.system.api.domain.vo.RemoteAttributeVo;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttrRelationResultVo;
import com.jxw.shufang.system.domain.vo.AttributeVo;
import com.jxw.shufang.system.service.IAttributeRelationService;
import com.jxw.shufang.system.service.IAttributeService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteAttributeServiceImpl implements RemoteAttributeService {

    private final IAttributeService attributeService;

    private final IAttributeRelationService attributeRelationService;

    private final IAttributeGroupingService attributeGroupingService;

    @Override
    public List<RemoteAttributeVo> getAttributeByGroupType(String groupType, Long branchId, boolean withUniversally) {
        List<AttributeVo> attributeByGroupType = attributeService.getAttributeByGroupType(groupType, branchId, withUniversally);
        return MapstructUtils.convert(attributeByGroupType, RemoteAttributeVo.class);
    }

    @Override
    public List<RemoteAttrRelationResultVo> getAttrWithValueByGroupType(List<Long> typeIdList, String groupType, Long branchId, boolean withUniversally) {
        List<AttrRelationResultVo> attrRelationResultVoList = attributeService.getAttrWithValueByGroupType(typeIdList, groupType, branchId, withUniversally);
        return MapstructUtils.convert(attrRelationResultVoList, RemoteAttrRelationResultVo.class);
    }

    @Override
    public boolean insertRelationBatch(List<RemoteAttributeRelationBo> remoteAttributeRelationBoList) throws ServiceException {
        return attributeRelationService.insertRelationBatch(MapstructUtils.convert(remoteAttributeRelationBoList, AttributeRelationBo.class));
    }

    @Override
    public List<ExcelUtil.ExcelExportHeader> putAttrHeader(String groupType, Long branchId, boolean withUniversally, List<ExcelUtil.ExcelExportHeader> headerList, Integer insertAttrIndex) {
        List<AttributeVo> attributeByGroupType = attributeService.getAttributeByGroupType(groupType, branchId, withUniversally);
        if (attributeByGroupType != null) {
            for (AttributeVo attributeVo : attributeByGroupType) {
                ExcelUtil.ExcelExportHeader excelExportHeader = new ExcelUtil.ExcelExportHeader();
                excelExportHeader.setHeaderName(attributeVo.getAttributeName());
                excelExportHeader.setHeaderCode(attributeVo.getAttributeName());
                excelExportHeader.setIsRequired(UserConstants.ATTR_STATUS_NO.equals(attributeVo.getCanNullStatus()));
                headerList.add(insertAttrIndex++, excelExportHeader);
            }
        }
        return headerList;
    }

    @Override
    public List<Long> search(String searchJson,String groupType) {
        return attributeService.search(searchJson,groupType);
    }

    @Override
    public void updateTypeRelation(String type, Long courseId, String attrAddJson) throws ServiceException {
        Assert.notNull(type, ()->new ServiceException("type不能为空"));
        Assert.notNull(courseId, ()->new ServiceException("courseId不能为空"));
        Assert.notNull(attrAddJson, ()->new ServiceException("attrAddJson不能为空"));
        List<AttributeRelationBo> list = null;
        try {
            list = JsonUtils.parseObject(attrAddJson, new TypeReference<>() {});
        } catch (Exception e) {
            log.error("属性反序列化异常",e);
            throw new ServiceException("属性反序列化异常");
        }
        attributeService.updateTypeRelation(type, courseId, list);

    }

    @Override
    public List<CourseAttributeDetailDTO> batchQueryAttributeByType(List<Long> courseId, String type) {
        return attributeGroupingService.batchQueryAttributeValueByTypeId(courseId, type);
    }

    @Override
    public List<CourseAttributeDetailDTO> batchQueryAttributeByIdAndType(List<Long> courseIds, List<Long> typeAttributeIds, String type) {
        return attributeRelationService.batchQueryAttributeValueByIdAndTypeId(courseIds, typeAttributeIds, type);
    }
}
