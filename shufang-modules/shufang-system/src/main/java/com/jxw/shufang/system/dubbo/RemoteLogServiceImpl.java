package com.jxw.shufang.system.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.system.api.RemoteLogService;
import com.jxw.shufang.system.api.domain.bo.RemoteLogininforBo;
import com.jxw.shufang.system.api.domain.bo.RemoteOperLogBo;
import com.jxw.shufang.system.domain.bo.SysLogininforBo;
import com.jxw.shufang.system.domain.bo.SysOperLogBo;
import com.jxw.shufang.system.service.ISysLogininforService;
import com.jxw.shufang.system.service.ISysOperLogService;
import org.springframework.stereotype.Service;

/**
 * 操作日志记录
 *

 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteLogServiceImpl implements RemoteLogService {

    private final ISysOperLogService operLogService;
    private final ISysLogininforService logininforService;

    @Override
    public void saveLog(RemoteOperLogBo remoteOperLogBo) {
        SysOperLogBo sysOperLogBo = MapstructUtils.convert(remoteOperLogBo, SysOperLogBo.class);
        operLogService.insertOperlog(sysOperLogBo);
    }

    @Override
    public void saveLogininfor(RemoteLogininforBo remoteLogininforBo) {
        SysLogininforBo sysLogininforBo = MapstructUtils.convert(remoteLogininforBo, SysLogininforBo.class);
        logininforService.insertLogininfor(sysLogininforBo);
    }
}
