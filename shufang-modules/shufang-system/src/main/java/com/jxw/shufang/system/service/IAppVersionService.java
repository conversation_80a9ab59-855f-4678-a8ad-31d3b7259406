package com.jxw.shufang.system.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.extresource.api.domain.vo.RemoteAppInfoVo;
import com.jxw.shufang.system.domain.bo.AppVersionBo;
import com.jxw.shufang.system.domain.vo.AppVersionV2Vo;
import com.jxw.shufang.system.domain.vo.AppVersionVo;

import java.util.Collection;
import java.util.List;

/**
 * APP版本管理Service接口
 *
 *
 * @date 2024-07-11
 */
public interface IAppVersionService {

    /**
     * 查询APP版本管理
     */
    AppVersionVo queryById(Long appVersionId);

    /**
     * 查询APP版本管理列表
     */
    TableDataInfo<AppVersionVo> queryPageList(AppVersionBo bo, PageQuery pageQuery);

    /**
     * 查询APP版本管理列表
     */
    List<AppVersionVo> queryList(AppVersionBo bo);

    /**
     * 新增APP版本管理
     */
    Boolean insertByBo(AppVersionBo bo);

    /**
     * 修改APP版本管理
     */
    Boolean updateByBo(AppVersionBo bo);

    /**
     * 校验并批量删除APP版本管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    AppVersionVo queryLatestVerInfo();

    AppVersionV2Vo queryLatestVerInfoV2(String extraIsbn, String sn);
}
