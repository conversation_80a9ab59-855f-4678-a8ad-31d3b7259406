package com.jxw.shufang.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.system.domain.bo.SysDeptBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 部门管理 服务层
 *

 */
public interface ISysDeptService {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    List<SysDeptVo> selectDeptList(SysDeptBo dept);

    void buildDeptUrl(List<SysDeptVo> sysDeptVos);

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    List<Tree<Long>> selectDeptTreeList(SysDeptBo dept);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    List<Tree<Long>> buildDeptTreeSelect(List<SysDeptVo> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    List<Long> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDeptVo selectDeptById(Long deptId);

    /**
     * 查询部门信息根据ID集合
     */
    List<SysDeptVo> selectDeptByIds(List<Long> deptIds);

    /**
     * 通过部门ID查询部门名称
     *
     * @param deptIds 部门ID串逗号分隔
     * @return 部门名称串逗号分隔
     */
    String selectDeptNameByIds(String deptIds);

    /**
     * 根据ID查询所有子部门数（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    long selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    boolean checkDeptNameUnique(SysDeptBo dept);

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    void checkDeptDataScope(Long deptId);

    /**
     * 新增保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    int insertDept(SysDeptBo bo) throws ServiceException;

    /**
     * 修改保存部门信息
     *
     * @param bo 部门信息
     * @return 结果
     */
    int updateDept(SysDeptBo bo);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    int deleteDeptById(Long deptId);

    /**
     * 通过部门id查询本身部门及子部门中的门店部门
     *
     * @param deptId 部门id
     *
     * @date 2024/02/19 03:17:57
     */
    List<SysDeptVo> getSelfAndChildShopList(Long deptId);

    /**
     * 通过部门ids查询本身部门及子部门中的门店部门
     *
     *
     * @date 2024/02/19 03:17:57
     */
    List<SysDeptVo> getSelfAndChildShopListByDeptIds(Collection<Long> deptIds);

    /**
     * 获取部门的子部门列表
     *
     * @param deptId 部门id
     *
     * @date 2024/02/22 11:38:08
     */
    List<SysDeptVo> getDeptChildrenList(Long deptId);


    /**
     * 获取dept ancestors2地图
     *
     * @param branchDeptIds 店铺部门IDS
     *
     * @date 2024/02/23 01:06:18
     */
    Map<Long, String>  getDeptAncestors2Map(Set<Long> branchDeptIds);

    List<Long> getAllParentDeptId(Long deptId);

    SysDeptVo getDeptSystemConfig(Long deptId);

    /**
     * 获取设置了商户配置的部门数量
     *
     * @param payMerchantConfigIdSet 配置id set
     * @return
     */
    Map<Long, Integer> countPayMerchant(Set<Long> payMerchantConfigIdSet);

    /**
     * 更新部门的支付配置
     *
     * @param deptIdSet
     * @param payMerchantConfigId
     * @return
     */
    boolean flushPayMerchantId(Set<Long> deptIdSet, Long payMerchantConfigId);

    void clearMerchantByPayMerchantConfigId(Long payMerchantConfigId);

    IPage<SysDeptVo> selectDeptPage(SysDeptBo sysDeptBo, PageQuery pageQuery);

    /**
     * 取消部门商户配置
     * @param payMerchantConfigId
     */
    void cancelDeptMerchantConfig(Long payMerchantConfigId);

    List<SysDeptVo> getDeptListByPayMerchantConfigIds(List<Long> payMerchantConfigId);

    List<SysDeptVo> getDeptListByPayMerchantConfigId (Long payMerchantConfigId);
    /**
     * 根据代理商ID查询代理商信息（无缓存接口）
     * @param deptId
     * @return
     */
    SysDeptVo selectDeptByIdWithoutCache(Long deptId);


    List<SysDeptVo> selectDeptListWithoutPermission(SysDeptBo dept);


    Boolean transfer(Long deptId, Integer amount) throws ServiceException;

    int updateDeptLogo(SysDeptBo bo);

    /**
     * 根据部门ID查询上一级部门信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    SysDeptVo selectPreDeptById(Long deptId);

}
