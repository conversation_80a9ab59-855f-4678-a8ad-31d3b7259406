package com.jxw.shufang.system.mapper;

import com.jxw.shufang.common.mybatis.core.mapper.BaseMapperPlus;
import com.jxw.shufang.system.domain.SysUserRole;

import java.util.List;

/**
 * 用户与角色关联表 数据层
 *

 */
public interface SysUserRoleMapper extends BaseMapperPlus<SysUserRole, SysUserRole> {

    List<Long> selectUserIdsByRoleId(Long roleId);

    List<Long> selectUserIdsByRoleIdList(List<Long> roleIdList);

}
