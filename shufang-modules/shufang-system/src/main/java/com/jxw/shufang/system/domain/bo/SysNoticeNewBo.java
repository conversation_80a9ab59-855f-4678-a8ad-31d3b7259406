package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.common.core.xss.Xss;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;
import com.jxw.shufang.system.domain.SysNotice;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysNotice.class, reverseConvertGenerate = false)
public class SysNoticeNewBo extends BaseEntity {

    /**
     * 公告ID
     */
    @NotNull(groups = {EditGroup.class}, message = "公告id不能为空")
    private Long noticeId;

    /**
     * 公告标题
     */
    @Xss(message = "公告标题不能包含脚本字符")
    @NotBlank(groups = {AddGroup.class}, message = "公告标题不能为空")
    @Size(min = 0, max = 50, message = "公告标题不能超过{max}个字符")
    private String noticeTitle;

    /**
     * 公告类型（1站内通知 2产研团队 3教研团队）
     */
    @NotBlank(groups = {AddGroup.class}, message = "公告类型不能为空")
    private String noticeType;

    /**
     * 接收范围（组织id）
     */
    @NotNull(groups = {AddGroup.class}, message = "接收范围不能为空")
    private Long toDept;

    /**
     * 公告内容
     */
    @NotBlank(groups = {AddGroup.class}, message = "公告内容不能为空")
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
