package com.jxw.shufang.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.PayMerchantConfig;
import com.jxw.shufang.system.domain.bo.QueryMerchantBo;
import com.jxw.shufang.system.domain.bo.SaveMerchantConfigBo;
import com.jxw.shufang.system.domain.bo.UpdateMerchantConfigBo;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigBranchVo;
import com.jxw.shufang.system.domain.vo.PayMerchantConfigVo;

/**
 * @author: cyj
 * @date: 2025/5/6
 */
public interface IPayMerchantConfigService {
    /**
     * 获取config
     *
     * @param payMerchantConfigId
     * @return
     */
    PayMerchantConfig queryConfigById(Long payMerchantConfigId);

    /**
     * 获取配置列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    IPage<PayMerchantConfigVo> listConfig(QueryMerchantBo bo, PageQuery pageQuery);

    /**
     * 查询配置
     *
     * @param payMerchantConfigId
     * @return
     */
    PayMerchantConfigVo queryVoById(Long payMerchantConfigId);

    Boolean saveConfig(SaveMerchantConfigBo bo, Long deptId, Long userId);
    /**
     * 新增或者更新配置
     *
     * @param bo
     * @return
     */
    Boolean updateConfig(UpdateMerchantConfigBo bo);

    boolean deleteConfig(Long id);

    TableDataInfo<PayMerchantConfigBranchVo> listConfigBranch(Long merchantConfigId, PageQuery pageQuery);

    /**
     * 获取部门的支付配置
     *
     * @param deptId
     * @return
     */
    PayMerchantConfig getConfigByDeptId(Long deptId);
}
