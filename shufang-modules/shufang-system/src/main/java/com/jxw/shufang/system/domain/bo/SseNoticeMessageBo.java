package com.jxw.shufang.system.domain.bo;

import com.jxw.shufang.common.core.validate.AddGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SseNoticeMessageBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接收人
     */
    @NotNull(message = "登录用户不能为空", groups = {AddGroup.class})
    private Long userId;

    /**
     * 时间戳
     */
    @NotNull(message = "时间戳不能为空", groups = {AddGroup.class})
    private Long timestamp;

    /**
     * 设备标签，默认WEB
     */
    private String label = "WEB";

}
