package com.jxw.shufang.system.service;

import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.domain.bo.SysNoticeUserBo;
import com.jxw.shufang.system.domain.vo.SysNoticeUserVo;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;

import java.util.Collection;
import java.util.List;

/**
 * noticeUserService接口
 *
 *
 * @date 2024-06-30
 */
public interface ISysNoticeUserService {

    /**
     * 查询noticeUser
     */
    SysNoticeUserVo queryById(Long noticeUserId);

    /**
     * 查询noticeUser列表
     */
    TableDataInfo<SysNoticeUserVo> queryPageList(SysNoticeUserBo bo, PageQuery pageQuery);

    /**
     * 查询noticeUser列表
     */
    List<SysNoticeUserVo> queryList(SysNoticeUserBo bo);

    /**
     * 新增noticeUser
     */
    Boolean insertByBo(SysNoticeUserBo bo);

    /**
     * 修改noticeUser
     */
    Boolean updateByBo(SysNoticeUserBo bo);

    /**
     * 校验并批量删除noticeUser信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    int deleteNoticeUserByNoticeIds(Long[] noticeIds);

    boolean insertNoticeUser(Long noticeId, List<Long> noticeUserIdList);

    Long getUnreadCount(Long userId);

    SysNoticeVo detail(Long noticeUserId);
}
