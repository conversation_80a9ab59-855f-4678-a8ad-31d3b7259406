package com.jxw.shufang.system.consumer;

import com.alibaba.fastjson.JSONObject;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.constant.RocketMQConstant;
import com.jxw.shufang.system.domain.bo.NoticeMessagePushBo;
import com.jxw.shufang.system.service.ISseHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = RocketMQConstant.NOTICE_MESSAGE_TOPIC,
    consumerGroup = RocketMQConstant.NOTICE_CONSUMER_GROUP,
    messageModel = MessageModel.BROADCASTING,
    consumeMode = ConsumeMode.CONCURRENTLY
)
public class NotifyMessageListener implements RocketMQListener<String> {

    private final ISseHandleService sseHandleService;

    @Override
    public void onMessage(String s) {
        log.info("接收到通知消息{}", s);
        JSONObject jsonObject = JSONObject.parseObject(s);
        String toUserId = jsonObject.getString(NotifyMessageConstant.TO_USER_ID);
        String toUserName = jsonObject.getString(NotifyMessageConstant.TO_USER_NAME);
        String content = jsonObject.getString(NotifyMessageConstant.CONTENT);
        if (StringUtils.isNotBlank(toUserId)) {
            NoticeMessagePushBo pushBo = new NoticeMessagePushBo();
            pushBo.setToUserId(Long.parseLong(toUserId));
            pushBo.setToUserName(toUserName);
            pushBo.setContent(content);
            sseHandleService.noticeMessagePush(pushBo);
        }
    }

}
