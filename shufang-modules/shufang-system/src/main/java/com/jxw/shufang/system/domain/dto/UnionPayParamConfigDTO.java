package com.jxw.shufang.system.domain.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/5 16:38
 * @Version 1
 * @Description 银联统一参数配置
 */
@Data
public class UnionPayParamConfigDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -7118977151945735202L;
    /**
     * 银联appId
     */
    private String unionAppId;
    /**
     * 银联key
     */
    private String unionKey;
    /**
     * 银联商户号
     */
    private String midNumber;
    /**
     * 银联终端号
     */
    private String tidNumber;
    /**
     * 银联来源编号
     */
    private String sourceNumber;
    /**
     * 银联网付密钥
     */
    private String onlineSecretKey;

    @Override
    public boolean equals(Object o) {
        // 1. 地址相同则直接返回true（自反性）[6,8](@ref)
        if (this == o) return true;

        // 2. 非空检查和类型检查
        if (o == null || getClass() != o.getClass()) return false; // 严格类匹配[7,8](@ref)

        // 3. 类型转换
        UnionPayParamConfigDTO that = (UnionPayParamConfigDTO) o;

        // 4. 逐个字段比较（含null安全处理）
        return Objects.equals(unionAppId, that.unionAppId) && // 引用类型用Objects.equals[4,8](@ref)
            Objects.equals(unionKey, that.unionKey) &&
            Objects.equals(midNumber, that.midNumber) &&
            Objects.equals(tidNumber, that.tidNumber) &&
            Objects.equals(sourceNumber, that.sourceNumber) &&
            Objects.equals(onlineSecretKey, that.onlineSecretKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            unionAppId,
            unionKey,
            midNumber,
            tidNumber,
            sourceNumber,
            onlineSecretKey
        );
    }
}
