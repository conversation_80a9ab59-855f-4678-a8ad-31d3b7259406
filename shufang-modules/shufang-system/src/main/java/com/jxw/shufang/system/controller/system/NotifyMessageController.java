package com.jxw.shufang.system.controller.system;

import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.api.RemoteNotifyMessageService;
import com.jxw.shufang.system.api.domain.bo.RemoteNotifyMessageBo;
import com.jxw.shufang.system.domain.bo.NoticeMessageBo;
import com.jxw.shufang.system.domain.bo.SseNoticeMessageBo;
import com.jxw.shufang.system.domain.vo.NoticeBizTypeVo;
import com.jxw.shufang.system.domain.vo.NoticeMessageVo;
import com.jxw.shufang.system.service.INotifyMessageService;
import com.jxw.shufang.system.service.ISseHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Objects;

@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/notify/message")
public class NotifyMessageController extends BaseController {

    private final RemoteNotifyMessageService remoteNotifyMessageService;
    private final INotifyMessageService notifyMessageService;
    private final ISseHandleService sseHandleService;

    @PostMapping("/send")
    public R<Boolean> sendMessage(@Validated(AddGroup.class) @RequestBody RemoteNotifyMessageBo messageBo) {
        log.info("通知消息发送入参{}", messageBo);
        return R.ok(remoteNotifyMessageService.sendMessage(messageBo));
    }

    @GetMapping(path = "/sse/connect", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter sseHandle(@Validated(AddGroup.class) SseNoticeMessageBo messageBo) {
        log.info("通知消息SSE连接参数{}", messageBo);
        return sseHandleService.noticeMessageHandler(messageBo);
    }

    @PostMapping("/query")
    public R<TableDataInfo<NoticeMessageVo>> queryNoticeMessage(@RequestBody NoticeMessageBo messageBo) {
        log.info("通知中心查询入参{}", messageBo);
        return R.ok(notifyMessageService.queryNoticeMessage(messageBo));
    }

    @GetMapping("/bizType/list")
    public R<List<NoticeBizTypeVo>> getBizTypeList() {
        return R.ok(notifyMessageService.getBizTypeList());
    }

    @GetMapping("/update/read")
    public R<Boolean> updateReadStatus(@RequestParam(required = false) List<Integer> messageIds,
                                       @RequestParam(required = false) Long toUserId) {
        log.info("通知更新已读状态入参{}_{}", messageIds, toUserId);
        if (CollectionUtils.isEmpty(messageIds) && Objects.isNull(toUserId)) {
            return R.ok(false);
        }
        return R.ok(notifyMessageService.updateReadStatus(messageIds, toUserId));
    }

    @GetMapping("/query/today")
    public R<TableDataInfo<NoticeMessageVo>> queryTodayNoticeMessage(@RequestParam Integer pageNum,
                                                                     @RequestParam Integer pageSize) {
        return R.ok(notifyMessageService.queryTodayNoticeMessage(LoginHelper.getUserId(), pageNum, pageSize));
    }

}
