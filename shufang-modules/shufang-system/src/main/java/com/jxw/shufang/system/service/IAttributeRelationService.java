package com.jxw.shufang.system.service;

import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO;
import com.jxw.shufang.system.domain.bo.AttributeRelationBo;
import com.jxw.shufang.system.domain.vo.AttributeRelationVo;

import java.util.Collection;
import java.util.List;

/**
 * 属性关系Service接口
 *
 *
 * @date 2024-03-26
 */
public interface IAttributeRelationService {

    /**
     * 查询属性关系
     */
    AttributeRelationVo queryById(Long id);

    /**
     * 查询属性关系列表
     */
    TableDataInfo<AttributeRelationVo> queryPageList(AttributeRelationBo bo, PageQuery pageQuery);

    /**
     * 查询属性关系列表
     */
    List<AttributeRelationVo> queryList(AttributeRelationBo bo);

    /**
     * 新增属性关系
     */
    Boolean insertByBo(AttributeRelationBo bo);

    /**
     * 修改属性关系
     */
    Boolean updateByBo(AttributeRelationBo bo);

    /**
     * 校验并批量删除属性关系信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 插入关系批
     *
     * @param convert 转换
     * @throws Exception 例外
     *
     * @date 2024/04/01 10:33:31
     */
    boolean insertRelationBatch(List<AttributeRelationBo> convert) throws ServiceException;


    /**
     * 更新关系批处理
     *
     * @param updateList 更新列表
     * @param updateNullValue 是否更新空值
     *
     * @date 2024/04/17 02:51:26
     */
    boolean updateRelationBatch(List<AttributeRelationBo> updateList,Boolean updateNullValue);

    List<CourseAttributeDetailDTO> batchQueryAttributeValueByIdAndTypeId(List<Long> courseIds, List<Long> typeAttributeId, String type);
}
