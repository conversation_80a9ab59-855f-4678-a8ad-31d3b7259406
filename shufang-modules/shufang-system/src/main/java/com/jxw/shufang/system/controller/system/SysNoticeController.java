package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jxw.shufang.common.core.validate.AddGroup;
import com.jxw.shufang.common.core.validate.EditGroup;
import com.jxw.shufang.system.domain.bo.SysNoticeNewBo;
import com.jxw.shufang.system.domain.bo.SysNoticeQueryBo;
import com.jxw.shufang.system.domain.vo.NoticeTypeVo;
import lombok.RequiredArgsConstructor;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.idempotent.annotation.RepeatSubmit;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.mybatis.core.page.PageQuery;
import com.jxw.shufang.common.mybatis.core.page.TableDataInfo;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.system.domain.bo.SysNoticeBo;
import com.jxw.shufang.system.domain.vo.SysNoticeVo;
import com.jxw.shufang.system.service.ISysNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 *

 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/notice")
public class SysNoticeController extends BaseController {

    private final ISysNoticeService noticeService;
    //private final DictService dictService;

    //@DubboReference
    //private  RemoteMessageService remoteMessageService;

    /**
     * 获取通知公告列表
     */
    @SaCheckPermission("system:notice:list")
    @GetMapping("/list")
    public TableDataInfo<SysNoticeVo> list(SysNoticeBo notice, PageQuery pageQuery) {
        return noticeService.selectPageNoticeList(notice, pageQuery);
    }

    @GetMapping("/page")
    public TableDataInfo<SysNoticeVo> pageQuery(SysNoticeQueryBo queryBo, PageQuery pageQuery) {
        log.info("【书房公告】查询入参{}_{}", queryBo, pageQuery);
        return noticeService.pageQuery(queryBo, pageQuery);
    }

    /**
     * 根据通知公告编号获取详细信息
     *
     * @param noticeId 公告ID
     */
    @SaCheckPermission("system:notice:query")
    @GetMapping(value = "/{noticeId}")
    public R<SysNoticeVo> getInfo(@PathVariable Long noticeId) {
        return R.ok(noticeService.selectNoticeById(noticeId));
    }

    @GetMapping(value = "/info")
    public R<SysNoticeVo> getNoticeInfo(@RequestParam Long noticeId) {
        return R.ok(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @SaCheckPermission("system:notice:add")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @RepeatSubmit
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysNoticeBo notice) {
        int rows = noticeService.insertNotice(notice);
        if (rows <= 0) {
            return R.fail();
        }
        //废除，采用用户消息代替，而非websocket
        //String type = dictService.getDictLabel("sys_notice_type", notice.getNoticeType());
        //remoteMessageService.publishAll("[" + type + "] " + notice.getNoticeTitle());
        return R.ok();
    }

    @PostMapping("/new/add")
    public R<Boolean> addNew(@Validated(AddGroup.class) @RequestBody SysNoticeNewBo notice) {
        log.info("【书房公告】新增入参{}", notice);
        return R.ok(noticeService.addNew(notice));
    }

    /**
     * 修改通知公告
     */
    @SaCheckPermission("system:notice:edit")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysNoticeBo notice) {
        return toAjax(noticeService.updateNotice(notice));
    }

    @PostMapping("/new/edit")
    public R<Boolean> editNew(@Validated(EditGroup.class) @RequestBody SysNoticeNewBo notice) {
        log.info("【书房公告】修改入参{}", notice);
        return R.ok(noticeService.editNew(notice));
    }

    /**
     * 删除通知公告
     *
     * @param noticeIds 公告ID串
     */
    @SaCheckPermission("system:notice:remove")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public R<Void> remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    @GetMapping("/new/delete")
    public R<Void> removeNew(@RequestParam Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    /**
     * 修改公告状态
     */
    @SaCheckPermission("system:notice:edit")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysNoticeBo notice) {
        return toAjax(noticeService.changeStatus(notice));
    }


    @GetMapping("/list/type")
    public R<List<NoticeTypeVo>> getNoticeType() {
        return R.ok(noticeService.getNoticeType());
    }

    @GetMapping("/role/type")
    public R<List<NoticeTypeVo>> getNoticeTypeOfRole() {
        return R.ok(noticeService.getNoticeTypeOfRole());
    }

    @GetMapping("/unread")
    public R<Boolean> hasUnread() {
        return R.ok(noticeService.hasUnread());
    }

    @GetMapping("/read")
    public R<Boolean> doRead() {
        return R.ok(noticeService.doRead());
    }


}
