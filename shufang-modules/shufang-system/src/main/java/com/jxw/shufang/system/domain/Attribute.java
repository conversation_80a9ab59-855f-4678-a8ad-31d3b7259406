package com.jxw.shufang.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.jxw.shufang.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 属性对象 attribute
 *
 * @date 2024-03-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("attribute")
public class Attribute extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 属性ID
     */
    @TableId(value = "attribute_id")
    private Long attributeId;

    /**
     * 属性分组IDs
     */
    private String attributeGroupingIds;

    /**
     * 属性名
     */
    private String attributeName;

    /**
     * 输入方式(1单选 2多选 3输入 4多级联动 5上传图片 6上传附件 7多行输入 8日期)
     */
    private String inputType;

    /**
     * 属性可选值
     */
    private String attributeValues;

    /**
     * 删除标志（0存在 2删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 排序
     */
    private Integer attributeSort;

    /**
     * 是否可以为空(0是 2否)
     */
    private String canNullStatus;

    /**
     * 父属性IDs
     */
    private String parentAttributeIds;

    /**
     * 是否需要列表展示（0是 2否）
     */
    private String showList;

    /**
     * 是否需要展示为搜索词（0是 2否）
     */
    private String search;

    /**
     * 页面列表中width
     */
    private Long width;

    /**
     * 页面列表中fixed
     */
    private String fixed;

    /**
     * 页面列表中sort
     */
    private Integer sort;

    /**
     * 页面列表中align
     */
    private String align;

    /**
     * 显示位置，自定义某个字符串来达成限制的条件
     */
    private String showLocation;


}
