package com.jxw.shufang.system.domain.vo;

import com.jxw.shufang.system.domain.NotifyMessage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.util.Date;

@Data
@AutoMapper(target = NotifyMessage.class)
public class NotifyMessageVo {

    private Integer id;

    /**
     * 模板id
     */
    private Integer template_id;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 拼接参数
     */
    private String params;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 通知类型 1-站内信
     */
    private Integer noticeType;

    /**
     * 创建时间
     */
    private Date createTime;

}
