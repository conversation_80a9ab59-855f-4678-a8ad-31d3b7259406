package com.jxw.shufang.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.jxw.shufang.system.api.constant.NotifyMessageConstant;
import com.jxw.shufang.system.api.constant.RocketMQConstant;
import com.jxw.shufang.system.api.domain.bo.RemoteNotifyMessageBo;
import com.jxw.shufang.system.domain.NotifyMessage;
import com.jxw.shufang.system.domain.NotifyMessageItem;
import com.jxw.shufang.system.domain.convert.RemoteNotifyMessageBoConvert;
import com.jxw.shufang.system.domain.vo.NotifyTemplateVo;
import com.jxw.shufang.system.service.INotifyMessageService;
import com.jxw.shufang.system.service.NotifyMessageStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component("internalMessageStrategy")
@Slf4j
@RequiredArgsConstructor
public class InternalNotifyMessageStrategy implements NotifyMessageStrategy {

    private final INotifyMessageService notifyMessageService;
    private final RocketMQTemplate rocketMQTemplate;

    @Transactional
    @Override
    public boolean sendMessage(RemoteNotifyMessageBo messageBo) {
        NotifyTemplateVo template = notifyMessageService.getNotifyTemplateVo(messageBo.getTemplateCode());
        if (Objects.isNull(template)) {
            log.error("通知消息模板不存在，发送失败{}", messageBo);
            return false;
        }
        if (validateParams(template, messageBo.getParamMap())) {
            log.error("通知消息模板参数校验失败{}", messageBo);
            return false;
        }

        NotifyMessage notifyMessage = RemoteNotifyMessageBoConvert.INSTANCE.toNotifyMessage(messageBo);
        notifyMessage.setTemplate_id(template.getId());
        if (Objects.nonNull(messageBo.getParamMap())) {
            notifyMessage.setParams(JSON.toJSONString(messageBo.getParamMap()));
        }
        notifyMessageService.saveNotifyMessage(notifyMessage);

        NotifyMessageItem notifyMessageItem = RemoteNotifyMessageBoConvert.INSTANCE.toNotifyMessageItem(messageBo);
        notifyMessageItem.setMessageId(notifyMessage.getId());
        notifyMessageService.saveNotifyMessageItem(notifyMessageItem);

        long newMsgCount = notifyMessageService.getUnreadMessageCount(messageBo.getToUserId());

        // 发送MQ消息
        Map<String, String> map = new HashMap<>();
        map.put(NotifyMessageConstant.TO_USER_ID, String.valueOf(messageBo.getToUserId()));
        map.put(NotifyMessageConstant.TO_USER_NAME, messageBo.getToUserName());
        map.put(NotifyMessageConstant.CONTENT, String.valueOf(newMsgCount));
        rocketMQTemplate.convertAndSend(RocketMQConstant.NOTICE_MESSAGE_TOPIC, map);

        return true;
    }

    private boolean validateParams(NotifyTemplateVo template, Map<String, Object> paramMap) {
        if (CollectionUtils.isNotEmpty(template.getParams())) {
            return template.getParams().stream().anyMatch(i -> !paramMap.containsKey(i));
        }
        return false;
    }

}
