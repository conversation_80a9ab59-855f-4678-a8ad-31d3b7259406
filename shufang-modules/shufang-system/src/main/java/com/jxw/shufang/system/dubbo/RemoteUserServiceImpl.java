package com.jxw.shufang.system.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxw.shufang.order.api.RemoteStudentMembershipService;
import com.jxw.shufang.order.api.domain.bo.RemoteStudentMembershipBo;
import com.jxw.shufang.order.api.domain.vo.RemoteStudentMembershipCardVo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserStudentInfoVo;
import com.jxw.shufang.system.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.common.core.constant.CacheConstants;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.StaffRole;
import com.jxw.shufang.common.core.enums.UserStatus;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.exception.user.UserException;
import com.jxw.shufang.common.core.utils.DateUtils;
import com.jxw.shufang.common.core.utils.MapstructUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import com.jxw.shufang.common.tenant.helper.TenantHelper;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.staff.api.RemoteStaffService;
import com.jxw.shufang.staff.api.domain.vo.RemoteStaffVo;
import com.jxw.shufang.student.api.RemoteStudentParentRecordService;
import com.jxw.shufang.student.api.RemoteStudentService;
import com.jxw.shufang.student.api.domain.bo.RemoteStudentParentRecordBo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentParentRecordVo;
import com.jxw.shufang.student.api.domain.vo.RemoteStudentVo;
import com.jxw.shufang.system.api.RemoteUserService;
import com.jxw.shufang.system.api.domain.bo.RemoteUserBo;
import com.jxw.shufang.system.api.domain.vo.RemoteUserVo;
import com.jxw.shufang.system.api.model.LoginUser;
import com.jxw.shufang.system.api.model.RoleDTO;
import com.jxw.shufang.system.api.model.XcxLoginUser;
import com.jxw.shufang.system.domain.SysUser;
import com.jxw.shufang.system.domain.bo.SysUserBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.domain.vo.SysRoleVo;
import com.jxw.shufang.system.domain.vo.SysUserVo;
import com.jxw.shufang.system.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户服务
 *

 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserServiceImpl implements RemoteUserService {

    private static final Logger log = LoggerFactory.getLogger(RemoteUserServiceImpl.class);
    private final ISysUserService userService;
    private final ISysPermissionService permissionService;
    private final ISysConfigService configService;
    private final SysUserMapper userMapper;
    private final ISysDeptService deptService;
    private final ISysDictDataService dictDataService;

    @DubboReference
    private RemoteBranchService remoteBranchService;

    @DubboReference
    private RemoteStudentService remoteStudentService;

    @DubboReference
    private RemoteStaffService remoteStaffService;

    @DubboReference
    private RemoteFileService remoteFileService;

    @DubboReference
    private RemoteStudentParentRecordService remoteStudentParentRecordService;

    @DubboReference
    private RemoteStudentMembershipService remoteStudentMembershipService;


    @Override
    public LoginUser getUserInfo(String username, String tenantId, boolean getBranchInfo) throws UserException,ServiceException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserName, SysUser::getStatus)
                .eq(SysUser::getUserName, username));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", username);
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", username);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(userMapper.selectUserByUserName(username), getBranchInfo);
        });
    }

    @Override
    public LoginUser getSpecifyUserInfo(Long userId) throws ServiceException {
        SysUser sysUser = userMapper.selectById(userId);
        if (ObjectUtil.isNull(sysUser)) {
            throw new ServiceException("用户不存在");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("用户已停用");
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserById(userId));
    }


    @Override
    public LoginUser getUserInfo(String username, String tenantId, Long coverRoleId, Long coverDeptId) throws UserException, ServiceException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserName, SysUser::getStatus)
                .eq(SysUser::getUserName, username));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", username);
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", username);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildSelectRoleLoginUser(userMapper.selectUserByUserName(username), coverRoleId, coverDeptId);
        });
    }

    @Override
    public LoginUser getSpecifyUserInfo(Long userId, Long coverRoleId, Long coverDeptId) throws UserException, ServiceException {
        SysUser sysUser = userMapper.selectById(userId);
        if (ObjectUtil.isNull(sysUser)) {
            throw new ServiceException("用户不存在");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("用户已停用");
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildSelectRoleLoginUser(userMapper.selectUserById(userId), coverRoleId, coverDeptId);
    }


    @Override
    public LoginUser getUserInfo(Long userId, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getUserName, SysUser::getStatus)
                .eq(SysUser::getUserId, userId));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", "");
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", sysUser.getUserName());
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(userMapper.selectUserByUserName(sysUser.getUserName()));
        });
    }

    @Override
    public LoginUser getUserInfoByPhonenumber(String phonenumber, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhonenumber, SysUser::getStatus)
                .eq(SysUser::getPhonenumber, phonenumber));
            if (ObjectUtil.isNull(sysUser)) {
                throw new UserException("user.not.exists", phonenumber);
            }
            if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
                throw new UserException("user.blocked", phonenumber);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(userMapper.selectUserByPhonenumber(phonenumber));
        });
    }

    @Override
    public LoginUser getUserInfoByEmail(String email, String tenantId) throws UserException {
        return TenantHelper.dynamic(tenantId, () -> {
            SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getEmail, SysUser::getStatus)
                .eq(SysUser::getEmail, email));
            if (ObjectUtil.isNull(user)) {
                throw new UserException("user.not.exists", email);
            }
            if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
                throw new UserException("user.blocked", email);
            }
            // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
            // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
            return buildLoginUser(userMapper.selectUserByEmail(email));
        });
    }

    @Override
    public XcxLoginUser getUserInfoByKeyId(String keyId) throws UserException {

        String openid = null;
        if (StringUtils.isNotBlank(keyId)) {
            openid = RedisUtils.getCacheObject(CacheConstants.WX_OPEN_ID + keyId);
        }
        if (StringUtils.isBlank(openid)) {
            //拿不到openid
            throw new ServiceException("您已掉线，请退出后重进");
        }
        SysUser sysUser = new SysUser();
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setUserId(sysUser.getUserId());
        loginUser.setUsername("微信用户-" + openid);
        loginUser.setNickname("微信用户");
        loginUser.setUserType(sysUser.getUserType());
        loginUser.setOpenid(openid);
        return loginUser;
    }

    @Override
    public XcxLoginUser getUserInfoByOpenid(String openid) throws UserException {
        if (StringUtils.isBlank(openid)) {
            //拿不到openid
            throw new ServiceException("您已掉线，请退出后重进");
        }

        RemoteStudentParentRecordBo bo = new RemoteStudentParentRecordBo();
        bo.setParentWechatOpenId(openid);
        List<RemoteStudentParentRecordVo> voList = remoteStudentParentRecordService.queryList(bo);

        if(null != voList && voList.size()>0){ }else {
            remoteStudentParentRecordService.insertByBo(bo);
            voList = remoteStudentParentRecordService.queryList(bo);
        }

        XcxLoginUser loginUser = new XcxLoginUser();
        loginUser.setUserId(voList.get(0).getStudentParentRecordId());
        loginUser.setUsername(voList.get(0).getParentWechatNo());
        loginUser.setNickname(voList.get(0).getParentWechatNickname());
        loginUser.setUserType("officialaccount");

        loginUser.setOpenid(openid);
        return loginUser;
    }

    @Override
    public Boolean registerUserInfo(RemoteUserBo remoteUserBo) throws UserException, ServiceException {
        SysUserBo sysUserBo = MapstructUtils.convert(remoteUserBo, SysUserBo.class);
        String username = sysUserBo.getUserName();
        boolean exist = TenantHelper.dynamic(remoteUserBo.getTenantId(), () -> {
            if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
                throw new ServiceException("当前系统没有开启注册功能");
            }
            return userMapper.exists(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, sysUserBo.getUserName())
                .ne(ObjectUtil.isNotNull(sysUserBo.getUserId()), SysUser::getUserId, sysUserBo.getUserId()));
        });
        if (exist) {
            throw new UserException("user.register.save.error", username);
        }
        return userService.registerUser(sysUserBo, remoteUserBo.getTenantId());
    }

    @Override
    public String selectUserNameById(Long userId) {
        return userService.selectUserNameById(userId);
    }

    @Override
    public String selectNicknameById(Long userId) {
        return userService.selectNicknameById(userId);
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUserVo userVo, boolean getBranchInfo) {
        LoginUser loginUser = new LoginUser();
        loginUser.setIsBranchAdmin(false);
        loginUser.setIsBranchStaff(false);
        loginUser.setIsConsultant(false);
        loginUser.setIsSaleConsultant(false);
        loginUser.setFaceBound(userVo.getFaceBound());
        loginUser.setTenantId(userVo.getTenantId());
        loginUser.setUserId(userVo.getUserId());
        loginUser.setDeptId(userVo.getDeptId());
        loginUser.setUsername(userVo.getUserName());
        loginUser.setNickname(userVo.getNickName());
        loginUser.setPassword(userVo.getPassword());
        loginUser.setUserType(userVo.getUserType());
        loginUser.setMenuPermission(permissionService.getMenuPermission(userVo.getUserId()));
        loginUser.setRolePermission(permissionService.getRolePermission(userVo.getUserId()));
        loginUser.setDeptName(ObjectUtil.isNull(userVo.getDept()) ? "" : userVo.getDept().getDeptName());
        List<RoleDTO> roles = BeanUtil.copyToList(userVo.getRoles(), RoleDTO.class);
        loginUser.setRoles(roles);
        if (getBranchInfo) {
            switch (UserType.getUserType(userVo.getUserType())) {
                case APP_STU_USER:
                    //会员登录,记录会员id
                    RemoteStudentVo studentVo = remoteStudentService.queryStudentByUserId(userVo.getUserId());
                    if (studentVo.getExpireTime()==null){
                        throw new ServiceException("无有效会员卡");
                    }
                    if (studentVo.getExpireTime().getTime()<System.currentTimeMillis()){
                        throw new ServiceException("会员卡已过期");
                    }
                    this.checkEarlyActivationVipDate(studentVo.getStudentId());
                    loginUser.setStudentId(studentVo.getStudentId());
                    loginUser.setBranchId(studentVo.getBranchId());
                    RemoteBranchVo branchVo = remoteBranchService.selectBranchById(studentVo.getBranchId());
                    if (branchVo == null) {
                        throw new ServiceException("门店不存在");
                    }
                    if (UserConstants.BRANCH_DISABLE.equals(branchVo.getBranchStatus())) {
                        throw new ServiceException("门店已停用");
                    }
                    break;
                case STAFF_USER:
                    //门店员工登录,记录门店id
                    loginUser.setIsBranchStaff(true);
                    RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchByDeptId(userVo.getDeptId());
                    if (remoteBranchVo == null) {
                        throw new ServiceException("门店不存在");
                    }
                    if (UserConstants.BRANCH_DISABLE.equals(remoteBranchVo.getBranchStatus())) {
                        throw new ServiceException("门店已停用");
                    }
                    loginUser.setBranchId(remoteBranchVo.getBranchId());
                    loginUser.setIsBranchStaff(true);
                    RemoteStaffVo remoteStaffVo = remoteStaffService.queryStaffByUserId(userVo.getUserId());
                    if (remoteStaffVo == null) {
                        throw new ServiceException("门店员工不存在");
                    }
                    loginUser.setStaffId(remoteStaffVo.getBranchStaffId());

                    //判断是不是仅拥有会员顾问的权限
                    roles.stream().filter(roleDTO -> StaffRole.MEMBER_CONSULTANT.getRoleId().equals(roleDTO.getRoleId()))
                        .findFirst().ifPresent(roleDTO -> loginUser.setIsConsultant(true));
                    //执行店长不仅仅拥有会员顾问的权限
                    roles.stream().filter(roleDTO -> StaffRole.EXECUTIVE_STORE_MANAGER.getRoleId().equals(roleDTO.getRoleId()))
                        .findFirst().ifPresent(roleDTO -> {
                            loginUser.setIsConsultant(false);
                            loginUser.setIsExecutiveStoreManager(true);
                        });
                    //判断是不是销售顾问
                    roles.stream().filter(roleDTO -> StaffRole.SALES_CONSULTANT.getRoleId().equals(roleDTO.getRoleId())).findFirst().ifPresent(roleDTO -> {
                        loginUser.setIsSaleConsultant(true);
                    });

                    RemoteStudentVo remoteStudentVo = remoteStudentService.queryStudentByUserId(userVo.getUserId());
                    if (remoteStudentVo != null) {
                        loginUser.setStudentId(remoteStudentVo.getStudentId());
                    }
                    break;
                case SYS_USER:
                    //门店管理员登录,记录门店idList
                    roles.stream()
                        .filter(roleDTO -> UserConstants.AUTHORIZE_BY_BRANCH_ROLE_SET.contains(roleDTO.getRoleId()))
                        .findFirst().ifPresent(roleDTO -> {
                        List<SysDeptVo> selfAndChildShopList = deptService.getSelfAndChildShopList(userVo.getDeptId());
                        if (CollUtil.isEmpty(selfAndChildShopList)) {
                            throw new ServiceException("未绑定门店");
                        }
                        List<Long> deptIdList = selfAndChildShopList.stream().map(SysDeptVo::getDeptId).toList();
                        //RemoteBranchVo remoteBranch = remoteBranchService.selectBranchByDeptId(userVo.getDeptId());
                        Map<Long, Long> deptId2BranchIdMap = remoteBranchService.selectBranchIdMapByDeptIdList(deptIdList, true);
                        if (CollUtil.isEmpty(deptId2BranchIdMap)) {
                            throw new ServiceException("未绑定门店或门店已停用");
                        }
                        loginUser.setBranchIdList(new ArrayList<>(deptId2BranchIdMap.values()));
                        loginUser.setIsBranchAdmin(true);
                    });
                    break;
                default:
                    //nothing to do
            }


        }
        return loginUser;
    }

    private void checkEarlyActivationVipDate(Long studentId) {
        RemoteStudentMembershipCardVo earlyActivationVipCard = this.getStudentLessStartTimeVipCard(studentId);
        if(null == earlyActivationVipCard){
            throw new ServiceException("无有效会员卡");
        }
        if(earlyActivationVipCard.getProductBeginDate().after(new Date())){
            throw new ServiceException("未到会员卡生效时间");
        }
    }

    private RemoteStudentMembershipCardVo getStudentLessStartTimeVipCard(Long studentId){
        RemoteStudentMembershipBo studentMembershipBo = new RemoteStudentMembershipBo();
        studentMembershipBo.setStudentId(studentId);
        List<RemoteStudentMembershipCardVo> membershipCardVos = remoteStudentMembershipService.listMembershipCard(studentMembershipBo);
        if(CollectionUtil.isEmpty(membershipCardVos)){
            return null;
        }
        Date today = new Date();
        return membershipCardVos.stream()
            // 过滤出结束时间大于等于今天的记录
            .filter(card -> !card.getProductEndDate().before(today))
            // 按照开始时间升序排序（最早的开始时间排在最前面）
            .sorted(Comparator.comparing(RemoteStudentMembershipCardVo::getProductBeginDate))
            // 获取第一条记录（即开始时间最短的记录）
            .findFirst().orElse(null);
    }

    /**
     * 构建登录用户
     */
    private LoginUser buildLoginUser(SysUserVo userVo) {
        return buildLoginUser(userVo, true);
    }


    /**
     * 构建选角登录用户(业务需求: 选择角色登录时使用选择的角色来决定部门ID,角色权限)
     */
    private LoginUser buildSelectRoleLoginUser(SysUserVo userVo, Long coverRoleId, Long coverDeptId) throws ServiceException {
        LoginUser loginUser = new LoginUser();
        loginUser.setIsBranchAdmin(false);
        loginUser.setIsSaleConsultant(false);
        loginUser.setIsBranchStaff(false);
        loginUser.setSelectRoleId(coverRoleId);
        loginUser.setSelectDeptId(coverDeptId);
        loginUser.setTenantId(userVo.getTenantId());
        loginUser.setUserId(userVo.getUserId());
        // 使用选择的角色来决定部门ID
        loginUser.setDeptId(userVo.getDeptId());
        loginUser.setUsername(userVo.getUserName());
        loginUser.setNickname(userVo.getNickName());
        loginUser.setPassword(userVo.getPassword());
        loginUser.setUserType(userVo.getUserType());
        // 使用选择的角色来决定有哪些菜单权限
        loginUser.setMenuPermission(permissionService.getMenuPermission(userVo.getUserId()));
        // 使用选择的角色来决定有哪些角色权限
        loginUser.setRolePermission(permissionService.getRolePermission(userVo.getUserId()));
        // 使用选择的角色来决定部门名称
        SysDeptVo sysDeptVo = deptService.selectDeptById(coverDeptId);
        loginUser.setDeptName(ObjectUtil.isNull(sysDeptVo) ? "" : sysDeptVo.getDeptName());
        List<RoleDTO> roles = BeanUtil.copyToList(userVo.getRoles(), RoleDTO.class);
        //除去非选择的角色
//        if (CollUtil.isNotEmpty(roles)) {
//            roles.removeIf(roleDTO -> !coverRoleId.equals(roleDTO.getRoleId()));
//        }
        //如果是门店管理员，那么需要获取他的门店id
        if (roles.stream().anyMatch(roleDTO -> UserConstants.BRANCH_ADMIN_ROLE_ID.equals(roleDTO.getRoleId()))) {
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchByDeptId(coverDeptId);
            if (remoteBranchVo == null) {
                throw new ServiceException("门店不存在");
            }
            if (UserConstants.BRANCH_DISABLE.equals(remoteBranchVo.getBranchStatus())) {
                throw new ServiceException("门店已停用");
            }
            loginUser.setBranchId(remoteBranchVo.getBranchId());
            loginUser.setIsBranchAdmin(true);
        } else if (roles.stream()
            .anyMatch(roleDTO -> UserConstants.AUTHORIZE_BY_BRANCH_ROLE_SET.contains(roleDTO.getRoleId()))) {
            List<SysDeptVo> selfAndChildShopList = deptService.getSelfAndChildShopList(userVo.getDeptId());
            if (CollUtil.isEmpty(selfAndChildShopList)) {
                throw new ServiceException("未绑定门店");
            }
            List<Long> deptIdList = selfAndChildShopList.stream().map(SysDeptVo::getDeptId).toList();
            Map<Long, Long> deptId2BranchIdMap = remoteBranchService.selectBranchIdMapByDeptIdList(deptIdList, true);
            if (CollUtil.isEmpty(deptId2BranchIdMap)) {
                throw new ServiceException("未绑定门店或门店已停用");
            }
            loginUser.setBranchIdList(new ArrayList<>(deptId2BranchIdMap.values()));
            loginUser.setIsBranchAdmin(true);
        }

        //如果是门店员工，处理
        //先处理角色里面就已经有员工角色的(存在一种情况，是员工，但是没有给他角色，像这种登录上去连菜单都看不到，不鸟他了)
        if (UserType.STAFF_USER.getUserType().equals(userVo.getUserType())) {
            Long deptId = userVo.getDeptId();
            if (coverDeptId!=null) {
                deptId= coverDeptId;
            }
            RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchByDeptId(deptId);
            if (remoteBranchVo == null) {
                throw new ServiceException("门店不存在");
            }
            if (UserConstants.BRANCH_DISABLE.equals(remoteBranchVo.getBranchStatus())) {
                throw new ServiceException("门店已停用");
            }
            loginUser.setBranchId(remoteBranchVo.getBranchId());
            loginUser.setIsBranchStaff(true);
            RemoteStaffVo remoteStaffVo = remoteStaffService.queryStaffByUserId(userVo.getUserId());
            if (remoteStaffVo == null) {
                throw new ServiceException("门店员工不存在");
            }
            loginUser.setStaffId(remoteStaffVo.getBranchStaffId());
            //判断是不是仅拥有会员顾问的权限
            roles.stream().filter(roleDTO -> StaffRole.MEMBER_CONSULTANT.getRoleId().equals(roleDTO.getRoleId()))
                .findFirst().ifPresent(roleDTO -> loginUser.setIsConsultant(true));
            //执行店长不仅仅拥有会员顾问的权限
            roles.stream().filter(roleDTO -> StaffRole.EXECUTIVE_STORE_MANAGER.getRoleId().equals(roleDTO.getRoleId()))
                .findFirst().ifPresent(roleDTO -> {
                    loginUser.setIsConsultant(false);
                    loginUser.setIsExecutiveStoreManager(true);
                });

            //判断是不是销售顾问
            roles.stream().filter(roleDTO -> StaffRole.SALES_CONSULTANT.getRoleId().equals(roleDTO.getRoleId())).findFirst().ifPresent(roleDTO -> {
                loginUser.setIsSaleConsultant(true);
            });
        }


        loginUser.setRoles(roles);
        return loginUser;
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param ip     IP地址
     */
    @Override
    public void recordLoginInfo(Long userId, String ip) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(ip);
        sysUser.setLoginDate(DateUtils.getNowDate());
        sysUser.setUpdateBy(userId);
        DataPermissionHelper.ignore(() -> userMapper.updateById(sysUser));
    }

    @Override
    public void updateUserInfo(RemoteUserBo remoteUserBo) throws ServiceException {
        DataPermissionHelper.ignore(() -> userService.updateUser(MapstructUtils.convert(remoteUserBo, SysUserBo.class)));
    }

    @Override
    public void batchUpdateUserInfo(List<RemoteUserBo> remoteUserBoList) {
        List<SysUserBo> sysUserBoList = MapstructUtils.convert(remoteUserBoList, SysUserBo.class);
        DataPermissionHelper.ignore(() -> {
            for (SysUserBo sysUserBo : sysUserBoList) {
                userService.updateUser(sysUserBo);
            }
        });
    }


    @Override
    public RemoteUserVo getSimpleUserInfoByUsername(String username) {
        SysUserVo sysUserVo = userMapper.selectUserByUserName(username);
        if (ObjectUtil.isNull(sysUserVo)) {
            return null;
        }
        RemoteUserVo convert = MapstructUtils.convert(sysUserVo, RemoteUserVo.class);
        List<SysRoleVo> roles = sysUserVo.getRoles();
        if (CollUtil.isNotEmpty(roles)) {
            convert.setRoleIds(roles.stream().map(SysRoleVo::getRoleId).toArray(Long[]::new));
        }
        return convert;
    }

    @Override
    public RemoteUserVo getSimpleUserInfoByUserId(Long userId) {
        SysUserVo sysUserVo = userMapper.selectUserByUserId(userId);
        if (ObjectUtil.isNull(sysUserVo)) {
            return null;
        }
        RemoteUserVo convert = MapstructUtils.convert(sysUserVo, RemoteUserVo.class);
        List<SysRoleVo> roles = sysUserVo.getRoles();
        if (CollUtil.isNotEmpty(roles)) {
            convert.setRoleIds(roles.stream().map(SysRoleVo::getRoleId).toArray(Long[]::new));
        }
        return convert;
    }

    @Override
    public Long insertUser(RemoteUserBo bo) throws ServiceException {
        SysUserBo sysUserBo = MapstructUtils.convert(bo, SysUserBo.class);
        //校验用户名是否存在
        if (!userService.checkUserNameUnique(sysUserBo)) {
            throw new ServiceException("新增用户'" + sysUserBo.getUserName() + "'失败，登录账号已存在");
        }
        userService.insertUser(sysUserBo);
        return sysUserBo.getUserId();
    }

    @Override
    public Map<Long, RemoteUserVo> getBranchAdmin(Set<Long> branchDeptIds) {
        //首先先获取自己的部门的最后一个添加人员（店铺管理角色），看一下有没有，
        List<SysUserVo> list = userService.getBranchAdmin(branchDeptIds, UserConstants.BRANCH_ADMIN_ROLE_ID);
        Map<Long, RemoteUserVo> map = StreamUtils.toMap(list, SysUserVo::getDeptId, t -> MapstructUtils.convert(t, RemoteUserVo.class));
        //移除掉自己的部门不存在管理的，那么剩下的就要考虑是不是在上级了
        branchDeptIds.removeIf(deptId -> list.stream().anyMatch(user -> user.getDeptId().equals(deptId)));
        if (branchDeptIds.isEmpty()) {
            return map;
        }

        //剩下的先获取他们对应的父级节点ids
        Map<Long, String> deptAncestors2Map = deptService.getDeptAncestors2Map(branchDeptIds);

        //把他们的值拿出来，平铺，然后拿去查
        Set<Long> parentDeptIds = deptAncestors2Map.values().stream()
            .map(s -> s.split(","))
            .flatMap(strings -> CollUtil.newArrayList(strings).stream())
            .map(Long::valueOf)
            .collect(Collectors.toSet());

        List<SysUserVo> list2 = userService.getBranchAdmin(parentDeptIds, UserConstants.BRANCH_ADMIN_ROLE_ID);

        //组装数据
        list2.forEach(item -> {
            Long deptId = item.getDeptId();
            Optional<Map.Entry<Long, String>> first = deptAncestors2Map.entrySet().stream()
                .filter(t -> Arrays.asList(t.getValue().split(",")).contains(deptId.toString()))
                .findFirst();
            if (first.isPresent()) {
                Map.Entry<Long, String> entry = first.get();
                RemoteUserVo remoteUserVo = MapstructUtils.convert(item, RemoteUserVo.class);
                map.put(entry.getKey(), remoteUserVo);
            }
        });
        return map;
    }

    @Override
    public Set<Long> getAdminBranchDeptIds(String username, String nickname) {
        //先获取有可能是门店管理的人,要求是店铺管理角色
        SysUserBo sysUserBo = new SysUserBo();
        sysUserBo.setUserName(username);
        sysUserBo.setNickName(nickname);
        sysUserBo.setStatus(UserConstants.USER_NORMAL);
        sysUserBo.setRoleId(UserConstants.BRANCH_ADMIN_ROLE_ID);
        List<SysUserVo> userList = DataPermissionHelper.ignore(() -> userService.selectAllocatedList(sysUserBo));
        if (CollUtil.isEmpty(userList)) {
            return Set.of();
        }
        //获取他们自己的部門
        Set<Long> deptIds = userList.stream().map(SysUserVo::getDeptId).collect(Collectors.toSet());
        //获取他们他们自己部门和子集部门中的门店列表
        List<SysDeptVo> selfAndChildShopListByDeptIds = deptService.getSelfAndChildShopListByDeptIds(deptIds);

        Set<Long> collect = selfAndChildShopListByDeptIds.stream().map(SysDeptVo::getDeptId).collect(Collectors.toSet());

        //获取真实的管理员
        Map<Long, RemoteUserVo> branchAdmin = getBranchAdmin(collect);

        //过滤管理员
        branchAdmin.entrySet().removeIf(entry -> !userList.stream().anyMatch(user -> user.getUserName().equals(entry.getValue().getUserName())));
        return branchAdmin.keySet();
    }

    @Override
    public List<RemoteUserVo> queryUserList(RemoteUserBo bo, boolean ignoreDataPermission) {
        SysUserBo convert = MapstructUtils.convert(bo, SysUserBo.class);
        List<SysUserVo> list = null;
        if (ignoreDataPermission) {
            list = DataPermissionHelper.ignore(() -> userService.selectUserList(convert));
        } else {
            list = userService.selectUserList(convert);
        }
        if (bo.isNeedUserFaceImg()) {
            // 需要登录人脸url
            putUserFaceUrl(list);
        }
        List<RemoteUserVo> resList = MapstructUtils.convert(list, RemoteUserVo.class);
        if (Boolean.TRUE.equals(bo.getGetAvatarUrl())) {
            putUserAvatar(resList);
        }
        return resList;
    }

    /**
     * 放入人脸ossUrl
     *
     * @param userVoList
     */
    private void putUserFaceUrl(List<SysUserVo> userVoList) {
        if (CollectionUtil.isEmpty(userVoList)) {
            return;
        }
        List<RemoteFile> remoteFiles = remoteFileService
            .selectFileByIds(userVoList.stream().filter(SysUserVo::isFaceBound).map(SysUserVo::getFace)
                .filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(",")));
        if (CollectionUtil.isEmpty(remoteFiles)) {
            return;
        }
        Map<Long, RemoteFile> remoteFileMap =
            remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, Function.identity()));
        userVoList.forEach(item -> {
            RemoteFile remoteFile = remoteFileMap.get(item.getFace());
            if (remoteFile != null) {
                item.setFaceUrl(remoteFile.getUrl());
            }
        });
    }

    @Override
    public List<Long> queryUserIds(RemoteUserBo bo, boolean ignoreDataPermission) {
        SysUserBo convert = MapstructUtils.convert(bo, SysUserBo.class);
        return ignoreDataPermission ? DataPermissionHelper.ignore(() -> userService.selectUserIdList(convert))
            : userService.selectUserIdList(convert);
    }

    @Override
    public Map<Long, String> selectNicknameByIds(List<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        for (Long userId : userIds) {
            String nickname = userService.selectNicknameById(userId);
            map.put(userId, nickname);
        }
        return map;
    }

    @Override
    public List<RemoteUserVo> queryUserOfRole(Long deptId, Long roleId) {
        List<SysUserVo> sysUserVos = userService.selectUserOfRole(deptId, roleId);
        return MapstructUtils.convert(sysUserVos, RemoteUserVo.class);
    }

    @Override
    public  List<RemoteUserVo> queryUserList(List<String> nickNames,Long deptId) {
        List<SysUserVo> sysUserVos = userService.queryUserByNickNames(nickNames, deptId);
        return MapstructUtils.convert(sysUserVos, RemoteUserVo.class);

    }
    @Override
    public LoginUser getSpecifyAgentUserInfo(Long userId) throws ServiceException {
        SysUser sysUser = userMapper.selectById(userId);
        if (ObjectUtil.isNull(sysUser)) {
            throw new ServiceException("用户不存在");
        }
        if (UserStatus.DISABLE.getCode().equals(sysUser.getStatus())) {
            throw new ServiceException("用户已停用");
        }
        // 框架登录不限制从什么表查询 只要最终构建出 LoginUser 即可
        // 此处可根据登录用户的数据不同 自行创建 loginUser 属性不够用继承扩展就行了
        return buildLoginUser(userMapper.selectUserById(userId), false);
    }

    @Override
    public RemoteUserStudentInfoVo getUserInfoForStudent(Long userId) {
        RemoteStudentVo remoteStudentVo = remoteStudentService.queryStudentByUserId(userId, true);
        RemoteUserStudentInfoVo vo = new RemoteUserStudentInfoVo();
        vo.setUserId(userId);
        vo.setStudentId(remoteStudentVo.getStudentId());
        vo.setStudentName(remoteStudentVo.getStudentName());
        vo.setStudentGender(remoteStudentVo.getStudentSex());
        vo.setStudentAccount(remoteStudentVo.getStudentAccount());
        vo.setGrade(remoteStudentVo.getStudentGrade());
        if (null != remoteStudentVo.getStudentInfo()) {
            vo.setStudentInfo(
                new RemoteUserStudentInfoVo.StudentInfoVo(remoteStudentVo.getStudentInfo().getHasKuaidingPrivilege(),
                    remoteStudentVo.getStudentInfo().getKuaidingPrivilegeExpireTime()));
        }
        RemoteBranchVo remoteBranchVo = remoteBranchService.selectBranchById(remoteStudentVo.getBranchId());
        vo.setBranchName(remoteBranchVo.getBranchName());
        vo.setBranchId(remoteBranchVo.getBranchId());
        return vo;
    }

    /**
     * 放置用户头像
     */
    private void putUserAvatar(List<RemoteUserVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        String ossIds = list.stream().map(RemoteUserVo::getAvatar).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.isBlank(ossIds)) {
            return;
        }
//        List<RemoteFile> remoteFiles = remoteFileService.selectFileByIds(ossIds);
//        if (CollUtil.isEmpty(remoteFiles)) {
//            return;
//        }
//        Map<Long, RemoteFile> remoteFileMap = remoteFiles.stream().collect(Collectors.toMap(RemoteFile::getOssId, Function.identity()));
//        list.forEach(item -> {
//            RemoteFile remoteFile = remoteFileMap.get(item.getAvatar());
//            if (remoteFile != null) {
//                item.setAvatarUrl(remoteFile.getUrl());
//            }
//        });
    }

}
