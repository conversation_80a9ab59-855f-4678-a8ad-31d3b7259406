package com.jxw.shufang.system.domain.vo;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import com.jxw.shufang.system.domain.PayMerchantConfig;
import com.jxw.shufang.system.domain.dto.UnionPayParamConfigDTO;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/5/6
 */
@Data
@AutoMapper(target = PayMerchantConfig.class)
public class PayMerchantConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 公户名称 商户名称
     */
    private String merchantName;
    /**
     * 接入方名称
     */
    private String channelCode;

    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType;
    /**
     * "unionpay",支付方式(unionpay-银联聚合支付 ）
     */
    private String payCode;
    /**
     * 聚合二维码的方式
     */
    private String wayCode;
    /**
     * 部门数量
     */
    private Integer deptNum;

    private UnionPayParamConfigDTO unionPayParamConfig;

    /**
     * 是否默认商户
     */
    private Boolean defaultMerchant;

    private List<SysDeptVo> deptList;
}
