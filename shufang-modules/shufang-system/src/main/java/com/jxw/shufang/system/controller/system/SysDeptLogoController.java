package com.jxw.shufang.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.io.FileUtil;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.core.utils.file.MimeTypeUtils;
import com.jxw.shufang.common.log.annotation.Log;
import com.jxw.shufang.common.log.enums.BusinessType;
import com.jxw.shufang.common.web.core.BaseController;
import com.jxw.shufang.resource.api.RemoteFileService;
import com.jxw.shufang.resource.api.domain.RemoteFile;
import com.jxw.shufang.system.domain.bo.SysDeptBo;
import com.jxw.shufang.system.domain.vo.SysDeptVo;
import com.jxw.shufang.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dept/logo")
public class SysDeptLogoController extends BaseController {

    private final ISysDeptService deptService;
    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 获取部门列表
     */
    @SaCheckPermission("system:dept:logo:list")
    @GetMapping("/list")
    public R<List<SysDeptVo>> list(SysDeptBo dept) {
        dept.setIsStore(false);
        dept.setOpenLogo(true);
        List<SysDeptVo> depts = deptService.selectDeptList(dept);
        return R.ok(depts);
    }


    /**
     * 修改部门
     */
    @SaCheckPermission("system:dept:logo:edit")
    @Log(title = "部门管理LOGO", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody SysDeptBo dept) {
        Long deptId = dept.getDeptId();
        Long logo = dept.getLogo();
        SysDeptBo sysDeptBo = new SysDeptBo();
        sysDeptBo.setLogo(logo);
        sysDeptBo.setDeptId(deptId);
        return toAjax(deptService.updateDeptLogo(dept));
    }

    /**
     * 头像上传
     *
     * @param file 用户头像
     */
    @SaCheckPermission("system:dept:logo:edit")
    @Log(title = "部门LOGO图片上传", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<RemoteFile> logo(@RequestPart("file") MultipartFile file) throws IOException {
        if (!file.isEmpty()) {
            String extension = FileUtil.extName(file.getOriginalFilename());
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
                return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
          return R.ok( remoteFileService.upload(file.getName(), file.getOriginalFilename(), file.getContentType(), file.getBytes()));
        }
        return R.fail("上传图片异常，请联系管理员");
    }
}
