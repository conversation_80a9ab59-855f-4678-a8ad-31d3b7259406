<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.SysPostMapper">

    <resultMap type="com.jxw.shufang.system.domain.vo.SysPostVo" id="SysPostResult">
    </resultMap>

    <select id="selectPostListByUserId" parameterType="Long" resultType="Long">
        select p.post_id
        from sys_post p
                 left join sys_user_post up on up.post_id = p.post_id
                 left join sys_user u on u.user_id = up.user_id
        where u.user_id = #{userId}
    </select>

    <select id="selectPostsByUserName" parameterType="String" resultMap="SysPostResult">
        select p.post_id, p.post_name, p.post_code
        from sys_post p
                 left join sys_user_post up on up.post_id = p.post_id
                 left join sys_user u on u.user_id = up.user_id
        where u.user_name = #{userName}
    </select>

</mapper>
