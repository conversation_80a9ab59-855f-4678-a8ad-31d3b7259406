<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.SysMenuMapper">

    <resultMap type="com.jxw.shufang.system.domain.SysMenu" id="SysMenuResult">
    </resultMap>

    <select id="selectMenuListByUserId" resultMap="SysMenuResult">
        select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.query_param, m.visible, m.status,
        m.perms, m.is_frame, m.is_cache, m.menu_type, m.icon, m.order_num, m.create_time,m.ascription
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        left join sys_user_role sur on rm.role_id = sur.role_id
        left join sys_role ro on sur.role_id = ro.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="SysMenuResult">
        select distinct m.menu_id,
                        m.parent_id,
                        m.menu_name,
                        m.path,
                        m.component,
                        m.query_param,
                        m.visible,
                        m.status,
                        m.perms,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time,
                        m.ascription
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
                 left join sys_role ro on sur.role_id = ro.role_id
                 left join sys_user u on sur.user_id = u.user_id
        where u.user_id = #{userId}
          and m.menu_type in ('M', 'C')
          and m.status = '0'
          and ro.status = '0'
          <if test="selectRoleId!=null">
              and ro.role_id = #{selectRoleId}
          </if>
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuListByRoleId" resultType="Long">
        select m.menu_id
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        <if test="menuCheckStrictly">
            and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id =
            rm.menu_id and rm.role_id = #{roleId})
        </if>
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuPerms" resultType="String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
    </select>

    <select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
                 left join sys_role r on r.role_id = sur.role_id
        where m.status = '0'
          and r.status = '0'
          and sur.user_id = #{userId}
    </select>

    <select id="selectMenuPermsByRoleId" parameterType="Long" resultType="String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
        where m.status = '0' and rm.role_id = #{roleId}
    </select>

</mapper>
