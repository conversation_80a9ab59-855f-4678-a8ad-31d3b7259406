<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.PayMerchantConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jxw.shufang.system.domain.PayMerchantConfig">
        <id column="id" property="id" />
        <result column="merchant_name" property="merchantName" />
        <result column="channel_code" property="channelCode" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="pay_type" property="payType" />
        <result column="pay_code" property="payCode" />
        <result column="way_code" property="wayCode" />
        <result column="default_merchant" property="defaultMerchant" />
        <result column="config_param_json" property="configParamJson" />
        <result column="is_delete" property="isDelete" />
        <result column="create_dept" property="createDept" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, merchant_name, channel_code, app_id, app_name, pay_type, pay_code, way_code, default_merchant, config_param_json, is_delete, create_dept, create_by, create_time, update_by, update_time, enable
    </sql>

</mapper>
