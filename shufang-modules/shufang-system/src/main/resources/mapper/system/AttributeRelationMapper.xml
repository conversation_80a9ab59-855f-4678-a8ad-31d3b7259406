<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.AttributeRelationMapper">

    <select id="queryCourseAttributeDetailByIdAndTypeId"
            resultType="com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO">
        select relation.attribute_id,
        relation.type_id,
        relation.value
        from attribute_relation relation
        <where>
            and relation.type_id in
            <foreach collection="typeIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            and relation.attribute_id in
            <foreach collection="typeAttributeIdList" item="attributeId" separator="," open="(" close=")">
                #{attributeId}
            </foreach>
            and relation.type = #{type}
        </where>
    </select>
</mapper>
