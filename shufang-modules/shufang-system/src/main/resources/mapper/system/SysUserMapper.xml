<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.SysUserMapper">

    <!-- 多结构嵌套自动映射需带上每个实体的主键id 否则映射会失败 -->
    <resultMap type="com.jxw.shufang.system.domain.vo.SysUserVo" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <association property="dept" column="dept_id" resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
        <collection property="posts" javaType="java.util.List" resultMap="PostResult"/>
    </resultMap>

    <resultMap id="deptResult" type="com.jxw.shufang.system.domain.vo.SysDeptVo">
        <id property="deptId" column="dept_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="email" column="dept_email"/>
        <result property="status" column="dept_status"/>
        <result property="createTime" column="dept_create_time"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.jxw.shufang.system.domain.vo.SysRoleVo">
        <id property="roleId" column="role_id"/>
        <result property="status" column="role_status"/>
        <result property="createTime" column="role_create_time"/>
    </resultMap>

    <resultMap id="PostResult" type="com.jxw.shufang.system.domain.vo.SysPostVo">
        <id property="postId" column="post_id"/>
    </resultMap>


    <sql id="selectUserVo">
        select u.user_id,
               u.tenant_id,
               u.dept_id,
               u.user_name,
               u.nick_name,
               u.user_type,
               u.email,
               u.avatar,
               u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.face_bound,
               d.dept_id,
               d.parent_id,
               d.ancestors,
               d.dept_name,
               d.order_num,
               d.leader,
               d.status as dept_status,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status as role_status
        from sys_user u
            left join sys_dept d on u.dept_id = d.dept_id
            left join sys_user_role sur on u.user_id = sur.user_id
            left join sys_role r on r.role_id = sur.role_id
    </sql>

    <select id="selectPageUserList" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.sex,
               u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark,
               d.dept_name, d.leader, u1.user_name as leaderName
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user u1 on u1.user_id = d.leader
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserList" resultMap="SysUserResult">
        select u.user_id,
               u.dept_id,
               u.nick_name,
               u.user_name,
               u.user_type,
               u.email,
               u.avatar,
               u.phonenumber,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.remark,
               u.face_bound,
               u.face,
               d.dept_name,
               d.leader,
               d.ancestors,
               u1.user_name as leaderName,
               r.role_id,
               r.role_name,
               r.role_key,
               r.role_sort,
               r.data_scope,
               r.status     as role_status,
               p.post_id,
               p.post_name,
               p.post_code
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
                 left join sys_user u1 on u1.user_id = d.leader
                 left join sys_user_post sup on u.user_id = sup.user_id
                 left join sys_post p on p.post_id = sup.post_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedPageList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUnallocatedList" resultMap="SysUserResult">
        select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phonenumber, u.status, u.create_time
        from sys_user u
             left join sys_dept d on u.dept_id = d.dept_id
             left join sys_user_role sur on u.user_id = sur.user_id
             left join sys_role r on r.role_id = sur.role_id
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_name = #{userName}
    </select>

    <select id="selectUserByPhonenumber" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.phonenumber = #{phonenumber}
    </select>

    <select id="selectUserByEmail" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.email = #{email}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_id = #{userId}
    </select>
    <select id="selectUserByIdTemp" resultType="com.jxw.shufang.system.domain.vo.SysUserVo">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.user_id = #{userId}
    </select>


    <select id="getBranchAdmin" resultMap="SysUserResult">
        SELECT su.*
        FROM sys_user su
        LEFT JOIN sys_user_role sur ON su.user_id = sur.user_id
        WHERE su.del_flag = 0
         AND su.dept_id IN
        <foreach collection="branchDeptIds" item="branchDeptId" open="(" separator="," close=")">
            #{branchDeptId}
        </foreach>
         AND sur.role_id = #{storeAdminRoleId}
         AND su.create_time = (
            SELECT MAX(su_inner.create_time)
            FROM sys_user su_inner
            LEFT JOIN sys_user_role sur_inner ON su_inner.user_id = sur_inner.user_id

            WHERE su_inner.dept_id = su.dept_id AND sur_inner.role_id = #{storeAdminRoleId}
         )
    </select>

    <select id="selectUserByUserId" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>
    <select id="getByIdContainsDel" resultType="com.jxw.shufang.system.domain.vo.SysUserVo">
        select * from sys_user where user_id = #{userId}
    </select>

    <select id="selectSysAndStaffUserList" resultType="com.jxw.shufang.system.domain.vo.SysUserVo">
        select user_id,
               tenant_id,
               dept_id,
               user_name,
               nick_name,
               user_type,
               email,
               phonenumber,
               sex,
               avatar,
               status,
               del_flag,
               login_ip,
               login_date,
               create_dept,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sys_user
        where user_type in ('sys_user', 'staff_user')
    </select>

    <select id="selectUserOfRole" resultType="com.jxw.shufang.system.domain.vo.SysUserVo">
        select su.*
        from sys_user su
                 inner join sys_user_role sur on sur.user_id = su.user_id
        where su.dept_id = #{deptId}
          and sur.role_id = #{roleId}
    </select>

    <select id="selectUserOfDept" resultType="com.jxw.shufang.system.domain.vo.SysUserVo">
        select distinct su.*
        from sys_user su
                 inner join sys_user_role sur on sur.user_id = su.user_id
        where su.dept_id = #{deptId}
    </select>
    <select id="selectUserIdList" resultType="java.lang.Long">
        select distinct u.user_id
        from sys_user u
                 left join sys_dept d on u.dept_id = d.dept_id
                 left join sys_user_role sur on u.user_id = sur.user_id
                 left join sys_role r on r.role_id = sur.role_id
                 left join sys_user u1 on u1.user_id = d.leader
                 left join sys_user_post sup on u.user_id = sup.user_id
                 left join sys_post p on p.post_id = sup.post_id
            ${ew.getCustomSqlSegment}
    </select>

</mapper>
