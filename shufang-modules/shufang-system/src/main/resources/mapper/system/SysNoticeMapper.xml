<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.SysNoticeMapper">

    <sql id="base_column_id">
        notice_id, tenant_id, notice_title, notice_type, notice_content, status, create_dept, create_by, create_time, update_by, update_time, remark, to_dept
    </sql>

    <select id="selectNoticePage" resultType="com.jxw.shufang.system.domain.vo.SysNoticeVo">
        select t.*,
               (select count(distinct s.user_id) from sys_notice_user s where s.notice_id = t.notice_id) as `send_user_count`,
               (select count(distinct s.user_id) from sys_notice_user s where s.notice_id = t.notice_id and s.read_status='0') as `read_user_count`
        from sys_notice t
        ${ew.customSqlSegment}
    </select>

    <select id="pageQuery" resultType="com.jxw.shufang.system.domain.vo.SysNoticeVo">
        select <include refid="base_column_id"/>
            from shufang.sys_notice sn
        <where>
            <if test="bo.isAdmin != null and bo.isAdmin != 1">
                and (
                sn.to_dept in
                <foreach collection="bo.toDeptList" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                or sn.create_by = #{bo.ownerId})
            </if>
            <if test="bo.status != null and bo.status != ''">
                and sn.status = #{bo.status}
            </if>
            <if test="bo.noticeTitle != null and bo.noticeTitle != ''">
                and sn.notice_title like concat('%', concat(#{bo.noticeTitle}, '%'))
            </if>
            <if test="bo.noticeContent != null and bo.noticeContent != ''">
                and sn.notice_content like concat('%', concat(#{bo.noticeContent}, '%'))
            </if>
            <if test="bo.searchValue != null and bo.searchValue != ''">
                and (sn.notice_title like concat('%', concat(#{bo.searchValue}, '%'))
                    or sn.notice_content like concat('%', concat(#{bo.searchValue}, '%')))
            </if>
            <if test="bo.createBy != null">
                and sn.create_by = #{bo.createBy}
            </if>
            <if test="bo.noticeType != null and bo.noticeType != ''">
                and sn.notice_type = #{bo.noticeType}
            </if>
        </where>
        order by sn.update_time desc
    </select>

    <select id="selectUnreadNotice" resultType="long">
        select sn.notice_id from sys_notice sn
            left join sys_notice_user snu on snu.notice_id = sn.notice_id and snu.user_id = #{userId}
        where sn.to_dept in
            <foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            and sn.status = '0'
            and snu.user_id is null
    </select>
</mapper>
