<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.NotifyMessageMapper">

    <select id="selectNoticeMessageList" resultType="com.jxw.shufang.system.domain.vo.NoticeMessageVo">
        select msg.id,
               msg.template_id,
               msg.content,
               msg.params,
               msg.biz_type,
               msg.event_type,
               msg.notice_type,
               msg.create_time,
               item.from_user_id,
               item.from_user_name,
               item.to_user_id,
               item.to_user_name,
               item.read_time
        from shufang.notify_message msg
                 inner join shufang.notify_message_item item on item.message_id = msg.id
        where item.to_user_id = #{bo.toUserId}
            <if test="bo.fromUserName != null and bo.fromUserName != ''">
                and item.from_user_name = #{bo.fromUserName}
            </if>
            <if test="bo.bizType != null">
                and biz_type = #{bo.bizType}
            </if>
            <if test="bo.readStatus == 0">
                and item.read_time = 0
            </if>
            <if test="bo.readStatus == 1">
                and item.read_time != 0
            </if>
        order by msg.create_time desc
    </select>

    <select id="selectBizTypeList" resultType="String">
        select distinct msg.biz_type
        from shufang.notify_message msg
                 inner join shufang.notify_message_item item on item.message_id = msg.id
        where item.to_user_id = #{bo.toUserId}
    </select>

    <select id="selectTodayNoticeMessage" resultType="com.jxw.shufang.system.domain.vo.NoticeMessageVo">
        select msg.id,
               msg.template_id,
               msg.content,
               msg.params,
               msg.biz_type,
               msg.event_type,
               msg.notice_type,
               msg.create_time,
               item.read_time
        from shufang.notify_message msg
                 inner join shufang.notify_message_item item on item.message_id = msg.id
        where item.to_user_id = #{toUserId}
            and msg.create_time &gt;= #{today}
            <if test="readStatus != null and readStatus == 0">
                and item.read_time = 0
            </if>
        order by msg.create_time desc
    </select>

</mapper>
