<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.AttributeGroupingMapper">

    <resultMap id="attributeGroupingResult" type="com.jxw.shufang.system.domain.vo.AttributeGroupingVo">

    </resultMap>

    <select id="selectAttrGroupPage" resultMap="attributeGroupingResult">
        select attribute_grouping_id,
               attribute_grouping_name,
               branch_id,
               type,
               sort_order,
               del_flag,
               create_dept,
               create_by,
               create_time,
               update_by,
               update_time,
               (select count(*) from attribute where find_in_set(attribute_grouping_ids,attribute_grouping_id)) as `attribute_count`
        from attribute_grouping
    </select>

    <select id="queryCourseAttributeDetail" resultType="com.jxw.shufang.system.api.domain.vo.CourseAttributeDetailDTO">
        select relation.type_id,
               relation.value
        from attribute_grouping attribute_grouping
            left join attribute attribute on attribute.attribute_grouping_ids = attribute_grouping.attribute_grouping_id
            left join attribute_relation relation on relation.attribute_id = attribute.attribute_id
        <where>
          and attribute_grouping.branch_id = -1
          and relation.type_id in
              <foreach collection="typeIds" item="item" separator="," open="(" close=")">
                  #{item}
              </foreach>
          and attribute_grouping.type = #{type}
          and attribute.del_flag = 0
          and attribute_grouping.del_flag = 0
        </where>
        order by attribute.sort asc
    </select>
</mapper>
