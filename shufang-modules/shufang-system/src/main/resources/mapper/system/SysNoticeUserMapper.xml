<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxw.shufang.system.mapper.SysNoticeUserMapper">

    <resultMap id="noticeUserVoResultMap" type="com.jxw.shufang.system.domain.vo.SysNoticeUserVo">
        <id property="noticeUserId" column="notice_user_id" />
        <result property="noticeId" column="notice_id" />
        <association property="notice" column="notice_id" resultMap="noticeResultMap" />
    </resultMap>

    <resultMap id="noticeResultMap" type="com.jxw.shufang.system.domain.vo.SysNoticeVo">
        <id property="noticeId" column="notice_id"/>
    </resultMap>

    <select id="getUnreadCount" resultType="java.lang.Long">
        select count(distinct t.notice_id)
        from sys_notice_user t
                 left join sys_notice sn on t.notice_id = sn.notice_id
        where sn.status = '0' and t.read_status = '2' and  t.user_id = #{userId}
    </select>

    <select id="selectNoticeUserVoList" resultMap="noticeUserVoResultMap">
        select t.*,
               sn.notice_id,
               sn.tenant_id,
               sn.notice_title,
               sn.notice_type,
               sn.status,
               sn.create_dept,
               sn.create_by,
               sn.create_time,
               sn.update_by,
               sn.update_time,
               sn.remark
        from sys_notice_user t
                 left join sys_notice sn on t.notice_id = sn.notice_id
        ${ew.customSqlSegment}
    </select>

    <insert id="saveBatch" parameterType="com.jxw.shufang.system.domain.SysNoticeUser">
        INSERT INTO sys_notice_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            notice_user_id,
            <if test="list[0].userId != null">
                user_id,
            </if>
            <if test="list[0].readStatus != null and item.readStatus != ''">
                read_status,
            </if>
            <if test="list[0].noticeId != null">
                notice_id,
            </if>
            <if test="list[0].createDept != null">
                create_dept,
            </if>
            <if test="list[0].createBy != null">
                create_by,
            </if>
            <if test="list[0].createTime != null">
                create_time,
            </if>
        </trim>
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.noticeUserId},
                <if test="item.userId != null">
                    #{item.userId},
                </if>
                <if test="item.readStatus != null and item.readStatus != ''">
                    #{item.readStatus},
                </if>
                <if test="item.noticeId != null">
                    #{item.noticeId},
                </if>
                <if test="item.createDept != null">
                    #{item.createDept},
                </if>
                <if test="item.createBy != null">
                    #{item.createBy},
                </if>
                <if test="item.createTime != null">
                    #{item.createTime},
                </if>
            </trim>
        </foreach>
    </insert>

</mapper>
