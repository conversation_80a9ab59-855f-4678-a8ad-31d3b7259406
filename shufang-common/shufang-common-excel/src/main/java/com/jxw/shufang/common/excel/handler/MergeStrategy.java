package com.jxw.shufang.common.excel.handler;

import cn.hutool.core.lang.Pair;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.*;

public class MergeStrategy<T> extends AbstractMergeStrategy {

    private final List<T> dataList;
    /**
     * 要合并的列index
     */
    private final List<Integer> mergeColumnIndexes;
    private final Map<Integer, Pair<Integer, Integer>> mergeRowIndexMap;

    public MergeStrategy(List<T> dataList, List<Integer> mergeColumnIndexes, List<Integer> mergeRowIndexes) {
        this.dataList = dataList;
        this.mergeColumnIndexes = mergeColumnIndexes;
        mergeRowIndexMap = new HashMap<>();
        ListIterator<Integer> mergeRowIterator = mergeRowIndexes.listIterator();
        while (mergeRowIterator.hasNext()) {
            Integer mergeStart = mergeRowIterator.next();
            Integer mergeEnd = mergeRowIterator.hasNext() ? mergeRowIterator.next() : null;
            if (mergeEnd != null) {
                mergeRowIndexMap.put(mergeStart, Pair.of(mergeStart, mergeEnd));
            }
        }
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer relativeRowIndex) {
        int columnIndex = cell.getColumnIndex();
        int currentRow = cell.getRowIndex();

        if (!mergeColumnIndexes.contains(columnIndex))
            return;

        if (currentRow == 0 || currentRow >= dataList.size())
            return;
        Pair<Integer, Integer> mergePair;
        if ((mergePair = getMergeRegionByRow(currentRow)) != null) {
            CellRangeAddress region =
                new CellRangeAddress(mergePair.getKey(), mergePair.getValue(), columnIndex, columnIndex);
            sheet.addMergedRegionUnsafe(region); // 防止重复合并异常
        }
    }

    /**
     * 获取当前行是否需要合并
     *
     * @param currentRow
     * @return
     */
    private Pair<Integer, Integer> getMergeRegionByRow(Integer currentRow) {
        return mergeRowIndexMap.get(currentRow);
    }
}
