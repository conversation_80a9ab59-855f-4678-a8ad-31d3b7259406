package com.jxw.shufang.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.translation.annotation.TranslationType;
import com.jxw.shufang.common.translation.constant.TransConstant;
import com.jxw.shufang.common.translation.core.TranslationInterface;
import com.jxw.shufang.system.api.RemoteDeptService;

/**
 * 部门翻译实现
 *

 */
@AllArgsConstructor
@TranslationType(type = TransConstant.DEPT_ID_TO_NAME)
public class DeptNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteDeptService remoteDeptService;

    @Override
    public String translation(Object key, String other) {
        return remoteDeptService.selectDeptNameByIds(key.toString());
    }
}
