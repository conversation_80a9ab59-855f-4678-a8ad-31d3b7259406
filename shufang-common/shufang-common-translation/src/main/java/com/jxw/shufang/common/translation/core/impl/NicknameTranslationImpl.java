package com.jxw.shufang.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.common.translation.annotation.TranslationType;
import com.jxw.shufang.common.translation.constant.TransConstant;
import com.jxw.shufang.common.translation.core.TranslationInterface;
import com.jxw.shufang.system.api.RemoteUserService;

/**
 * 用户昵称翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_NICKNAME)
public class NicknameTranslationImpl implements TranslationInterface<String> {

    @DubboReference
    private RemoteUserService remoteUserService;

    @Override
    public String translation(Object key, String other) {
        return remoteUserService.selectNicknameById((Long) key);
    }
}
