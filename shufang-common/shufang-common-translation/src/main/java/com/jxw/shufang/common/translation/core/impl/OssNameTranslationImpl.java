package com.jxw.shufang.common.translation.core.impl;

import com.jxw.shufang.common.translation.annotation.TranslationType;
import com.jxw.shufang.common.translation.constant.TransConstant;
import com.jxw.shufang.common.translation.core.TranslationInterface;
import com.jxw.shufang.resource.api.RemoteFileService;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * OSS翻译实现
 *

 */
@AllArgsConstructor
@TranslationType(type = TransConstant.OSS_ID_TO_NAME)
public class OssNameTranslationImpl implements TranslationInterface<String> {

    @DubboReference(mock = "true")
    private RemoteFileService ossService;

    @Override
    public String translation(Object key, String other) {
        return ossService.selectNameById(key.toString());
    }
}
