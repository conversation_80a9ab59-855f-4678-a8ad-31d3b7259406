package com.jxw.shufang.common.dict.enums;

public enum CourseLabelDictEnum {
    COURSE_STAGE("course_stage", "学段"),
    COURSE_GRADE("course_grade", "年级"),
    COURSE_AFFILIATION_SUBJECT("course_affiliation_subject", "归属学科"),
    COURSE_SPECIAL_TOPIC("course_special_topic", "课程专题"),
    COURSE_QUARTER_TYPE("course_quarter_type", "季度类型");

    private String code;
    private String name;

    CourseLabelDictEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
