<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-common-dict</artifactId>

    <description>
        shufang-common-dict 字典
    </description>

    <dependencies>

        <!-- jxw Common Security -->
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

    </dependencies>
</project>
