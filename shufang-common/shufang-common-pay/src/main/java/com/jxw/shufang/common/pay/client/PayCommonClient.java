package com.jxw.shufang.common.pay.client;

import com.dtflys.forest.annotation.*;
import com.jxw.shufang.common.pay.domain.dto.request.*;
import com.jxw.shufang.common.pay.domain.dto.response.ApiResp;
import com.jxw.shufang.common.pay.domain.dto.response.CheckOrderResponseDTO;
import com.jxw.shufang.common.pay.domain.dto.response.RefundResponseDTO;
import com.jxw.shufang.common.pay.domain.dto.response.UnifiedOrderResponseDTO;

/**
 * @author: cyj
 * @date: 2025/4/25
 */
@BaseRequest(baseURL = "#{common.pay.baseUrl}")
public interface PayCommonClient {

    /**
     * 请求统一下单接口
     *
     * @return
     */
    @Post("/pay/unified-order")
    ApiResp<UnifiedOrderResponseDTO> unifiedOrder(@JSONBody UnifiedOrderRequestDTO unifiedOrderDTO);

    /**
     * 查询订单
     *
     * @return
     */
    @Get("/pay/query")
    ApiResp<CheckOrderResponseDTO> checkOrder(@Query String appId, @Query String wayCode, @Query String payOrderId);

    /**
     * 提交退款申请
     *
     * @return
     */
    @Post("/pay/refund")
    ApiResp<RefundResponseDTO> refundOrder(@JSONBody RefundRequestDTO refundRequestDTO);

    /**
     * 取消订单
     *
     * @param closeOrderDTO
     * @return
     */
    @Post("/pay/close")
    ApiResp<String> closeOrder(@JSONBody CloseOrderRequestDTO closeOrderDTO);

    /**
     * 保存商户配置
     *
     * @param closeOrderDTO
     * @return
     */
    @Post("/pay/merchant-config/unionpay/save")
    ApiResp<String> saveConfig(@JSONBody MerchantConfigDTO closeOrderDTO);

    /**
     * 更新商户配置
     * @param closeOrderDTO
     * @return
     */
    @Post("/pay/merchant-config/unionpay/update")
    ApiResp<String> updateConfig(@JSONBody UpdateUnionpayConfigBO closeOrderDTO);

    /**
     * 删除商户配置
     *
     * @param deleteMerchantConfigRequest
     * @return
     */
    @Post("/pay/merchant-config/unionpay/delete")
    ApiResp<String> deleteConfig(@JSONBody DeleteMerchantConfigRequest deleteMerchantConfigRequest);
}
