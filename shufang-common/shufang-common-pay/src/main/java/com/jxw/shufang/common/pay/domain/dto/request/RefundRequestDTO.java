package com.jxw.shufang.common.pay.domain.dto.request;

import java.math.BigDecimal;


import com.jxw.shufang.common.pay.constant.CommonPayConstant;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/4/28
 */
@Data
public class RefundRequestDTO extends CommonPayBaseRequestDTO {
    /**
     * 退款业务单号
     */
    private String refundOrderId;
    /**
     * 支付订单号
     */
    private String payOrderId;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    private String extParam;
    /**
     * 回调配置
     */
    private CallBackConfigDTO callbackConfig;
}
