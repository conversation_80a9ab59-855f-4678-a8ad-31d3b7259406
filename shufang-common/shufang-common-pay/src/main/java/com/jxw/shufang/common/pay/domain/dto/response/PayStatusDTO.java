package com.jxw.shufang.common.pay.domain.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * @author: cyj
 * @date: 2025/4/28
 */
@Data
public class PayStatusDTO {

    /**
     * 支付状态 0-待支付，1-支付中，2-成功,3-失败
     */
    private Integer payStatus;

    public boolean isPay() {
        return PayStatusEnum.PAID.getStatus().equals(payStatus);
    }

    @Getter
    @AllArgsConstructor
    public enum PayStatusEnum {

        WAIT_PAY(0), PAYING(1), PAID(2), PAY_FAIL(3);

        private Integer status;
    }

}
