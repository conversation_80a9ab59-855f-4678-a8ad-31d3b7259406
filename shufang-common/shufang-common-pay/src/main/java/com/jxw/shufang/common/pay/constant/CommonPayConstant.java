package com.jxw.shufang.common.pay.constant;

/**
 * @author: cyj
 * @date: 2025/4/25
 */
public interface CommonPayConstant {
    /**
     *业务系统类型
     */
    String DEFAULT_SYSTEM_CODE = "xuewangshufang";
    /**
     * 默认应用名称
     */
    String DEFAULT_APP_NAME = "学王书房";

    /**
     * 默认支付类型 1-聚合支付,2-普通支付
     */
    Integer DEFAULT_PAY_TYPE = 1;
    /**
     * 默认支付方式
     */
    String DEFAULT_PAY_CODE = "unionpay";
    /**
     * 聚合二维码的方式
     */
    String DEFAULT_WAY_CODE = "UNIONPAY_QR";

    String DEFAULT_CHANNEL_CODE = "tm_unionpay";

    String IGNORE_TOKEN_ATTR_NAME = "ignoreToken";

    String TOKEN_REDIS_KEY = "shufang:open:token";

    String TOKEN_REDIS_LOCK_KEY = "shufang:tokenLock";

}
