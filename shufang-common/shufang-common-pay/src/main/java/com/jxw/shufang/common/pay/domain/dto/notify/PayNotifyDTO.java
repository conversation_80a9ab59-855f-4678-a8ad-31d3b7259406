package com.jxw.shufang.common.pay.domain.dto.notify;

import java.math.BigDecimal;

import com.jxw.shufang.common.pay.domain.dto.response.PayStatusDTO;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/4/27
 */
@Data
public class PayNotifyDTO extends PayStatusDTO {
    /**
     * 商户应用ID
     */
    private String appId;
    /**
     * 商户业务订单号
     */
    private String bizOrderId;
    /**
     * 支付订单号
     */
    private String payOrderId;

    /**
     * 支付总金额（单位：分）
     */
    private BigDecimal totalPayAmount;

    /**
     * 支付时间（时间戳）
     */
    private Long payTimeStamp;

    /**
     * 支付渠道 1-天满银联
     */
    private Integer payChannel;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType;

    /**
     * 支付类型名称
     */
    private String payTypeName;

    /**
     * 支付方式 0-其他,1-微信，2-支付宝，3-银行卡
     */
    private Integer payMethod;

    /**
     * 支付方式名称
     */
    private String payMethodName;

    /**
     * 异常支付标识 0-正常，1-异常
     */
    private Integer abnormalPayFlag;

    private String channelOrderId;
}
