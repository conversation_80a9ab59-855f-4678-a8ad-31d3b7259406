package com.jxw.shufang.common.pay.domain.dto.response;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class ApiResp<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    //0-表示成功， 1-为业务报错的异常， 401-token无效，请重新登录， 500-为系统错误
    public final static Integer successCode = 0;
    public final static Integer failCode = 1;
    public final static Integer tokenInvalidCode = 401;
    public final static Integer sysErrCode = 500;

    private Integer code;

    private String msg;

    private String baseUrl;

    private T data;

    private String requestId;


    public boolean isSuccess() {
        return successCode.equals(code);
    }
}
