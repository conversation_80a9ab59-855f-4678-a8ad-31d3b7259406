package com.jxw.shufang.common.pay.domain.dto.request;

import com.jxw.shufang.common.pay.constant.CommonPayConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/10 14:54
 * @Version 1
 * @Description
 */
@Data
public class UpdateUnionpayConfigBO {
    private String appId;
    /**
     * 应用名称
     */
    private String appName = CommonPayConstant.DEFAULT_APP_NAME;

    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType = CommonPayConstant.DEFAULT_PAY_TYPE;
    /**
     * 支付商家(unionpay-银联聚合支付)
     */
    private String payCode = CommonPayConstant.DEFAULT_PAY_CODE;

    /**
     * 支付方式名称(UNIONPAY-QR-银联聚合二维码支付)
     */
    private String wayCode = CommonPayConstant.DEFAULT_WAY_CODE;

    /**
     * 支付接入方 tm_unionpay
     */
    private String channelCode= CommonPayConstant.DEFAULT_CHANNEL_CODE;

    /**
     * 支付接入方参数
     */
    private String configParamJson;

    public UpdateUnionpayConfigBO(String appId,String configParamJson) {
        this.appId = appId;
        this.configParamJson = configParamJson;
    }
}
