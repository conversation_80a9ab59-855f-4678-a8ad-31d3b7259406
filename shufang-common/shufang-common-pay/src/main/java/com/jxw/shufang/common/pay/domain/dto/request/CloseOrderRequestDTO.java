package com.jxw.shufang.common.pay.domain.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/4/28
 */
@Data
@AllArgsConstructor
public class CloseOrderRequestDTO extends CommonPayBaseRequestDTO {

    /**
     * 支付订单id
     */
    private String payOrderId;

    public CloseOrderRequestDTO(String appId, String payOrderId) {
        setAppId(appId);
        this.payOrderId = payOrderId;
    }
}
