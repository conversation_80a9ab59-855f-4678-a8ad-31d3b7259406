package com.jxw.shufang.common.pay.interceptor;

import com.jxw.shufang.common.pay.config.SecretProperties;
import com.jxw.shufang.common.pay.constant.CommonPayConstant;
import com.jxw.shufang.common.pay.service.ILoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.jxw.shufang.common.core.exception.ServiceException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class HeaderInterceptor<T> implements Interceptor<T>{

    @Autowired
    private SecretProperties secretProperties;

    @Autowired
    private ILoginService loginService;

    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {}

    @Override
    public boolean beforeExecute(ForestRequest request) throws ServiceException {
        Boolean attribute = getAttribute(request, CommonPayConstant.IGNORE_TOKEN_ATTR_NAME, Boolean.class);
        if (attribute != null && attribute) {
            log.debug("忽略token");
            return true;
        }
        log.debug("添加token");
        String tokenHeaderName = secretProperties.getTokenHeaderName();
        String token = loginService.getToken();
        request.addHeader(tokenHeaderName, token);
        return true;
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        Interceptor.super.afterExecute(request, response);
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        Interceptor.super.onError(ex, request, response);
    }
}
