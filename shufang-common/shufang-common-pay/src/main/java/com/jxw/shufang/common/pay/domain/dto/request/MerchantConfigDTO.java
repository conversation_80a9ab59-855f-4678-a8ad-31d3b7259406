package com.jxw.shufang.common.pay.domain.dto.request;

import com.jxw.shufang.common.pay.constant.CommonPayConstant;

import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/5/7
 */
@Data
public class MerchantConfigDTO {
    /**
     * 应用ID
     */
    private String appId;
    private String configParamJson;
    /**
     * 应用名称
     */
    private String appName = CommonPayConstant.DEFAULT_APP_NAME;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private Integer payType = 1;
    /**
     * "unionpay", //支付方式(unionpay-银联聚合支付
     */
    private String payCode = "unionpay";
    /**
     * "UNIONPAY_QR" //聚合二维码的方式
     */
    private String wayCode = "UNIONPAY_QR";
    /**
     * 接入渠道 tm_unionpay-天满银联商务
     */
    private String channelCode = "tm_unionpay";

    public MerchantConfigDTO(String appId,String configParamJson) {
        this.appId = appId;
        this.configParamJson = configParamJson;
    }
}
