package com.jxw.shufang.common.pay.domain.dto.response;

import java.math.BigDecimal;

import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/4/28
 */
@Data
public class CheckOrderResponseDTO extends PayStatusDTO {
    /**
     * 唯一标识
     */
    private String appId;
    /**
     * 商户业务订单号
     */
    private String bizOrderId;
    /**
     * 支付订单号
     */
    private String payOrderNo;
    /**
     * 支付总金额
     */
    private BigDecimal totalPayAmount;
    /**
     * 支付时间（时间戳）
     */
    private String payTimeStamp;
    /**
     * 支付渠道 1-天满银联
     */
    private String payChannel;
    /**
     * 支付渠道名称
     */
    private String payChannelName;
    /**
     * 支付类型 1-聚合支付,2-普通支付
     */
    private String payType;
    /**
     * 支付类型名称
     */
    private String payTypeName;
    /**
     * 支付方式 0-其他,1-微信，2-支付宝，3-银行卡
     */
    private String payMethod;
    /**
     * 支付方式名称
     */
    private String payMethodName;

}
