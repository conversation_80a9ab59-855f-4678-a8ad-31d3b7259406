package com.jxw.shufang.common.pay.domain.dto.notify;

import java.math.BigDecimal;

import lombok.Data;

/**
 * @author: cyj
 * @date: 2025/4/27
 */
@Data
public class RefundNotifyDTO {
    /**
     * 商户应用ID
     */
    private String appId;

    /**
     * 退款订单号
     */
    private String refundOrderNo;

    /**
     * 业务订单号
     */
    private String bizOrderId;


    /**
     * 退款金额（单位：元）
     */
    private BigDecimal refundAmount;

    /**
     * 退款状态0-退款中,1-成功,2-失败
     */
    private Integer refundStatus;

    /**
     * 退款状态名称
     */
    private String refundStatusName;

    /**
     * 退款时间
     */
    private Long refundTimeStamp;

}
