<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>shufang-common-pay</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.dtflys.forest</groupId>
            <artifactId>forest-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-json</artifactId>
        </dependency>
    </dependencies>
</project>
