package com.jxw.shufang.common.asynchandler.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import com.jxw.shufang.asynctask.api.RemoteAsyncTaskService;
import com.jxw.shufang.asynctask.api.domain.RemoteAsyncTask;
import com.jxw.shufang.common.asynchandler.annotation.AsyncResult;
import com.jxw.shufang.common.core.domain.R;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.ServletUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.json.utils.JsonUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;
import java.util.Objects;

import static com.jxw.shufang.common.asynchandler.constants.BaseConstant.GLOBAL_TASK_CODE_HEADER;

/**
 * 异步处理程序拦截器
 * TODO 文件上传类的请求暂不支持，需优化
 *
 *
 * @date 2024/02/07 03:14:47
 */

public class AsyncHandlerInterceptor implements HandlerInterceptor {

    @DubboReference
    private  RemoteAsyncTaskService remoteAsyncTaskService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            AsyncResult annotation = handlerMethod.getMethodAnnotation(AsyncResult.class);
            if (annotation != null) {
                //仅支持get和post
                if (!"GET".equalsIgnoreCase(request.getMethod()) && !"POST".equalsIgnoreCase(request.getMethod())) {
                    throw new ServiceException("不支持的请求方式");
                }
                //第二次进来了，不做处理
                if (remoteAsyncTaskService.isPass(ServletUtils.getHeaderMap(request))) {
                    return true;
                }

                String code = remoteAsyncTaskService.getCode();
                if (StringUtils.isBlank(code)) {
                    throw new ServiceException("异步任务标识生成错误");
                }
                String taskName = StringUtils.defaultIfBlank(annotation.taskName(), handlerMethod.getMethod().getName());
                Integer taskType = annotation.type().getValue();
                String queryString = request.getQueryString();
                String body = ServletUtils.getBody(request);
                Map<String, String> headerMap = ServletUtils.getHeaderMap(request);
                //把code放到header中，持续传递
                headerMap.put(GLOBAL_TASK_CODE_HEADER, code);

                RemoteAsyncTask remoteAsyncTask = new RemoteAsyncTask()
                        .setTaskName(taskName)
                        .setTaskParams(queryString)
                        .setTaskBody(body)
                        .setHeaderMap(JsonUtils.toJsonString(headerMap))
                        .setTaskCode(code)
                        .setType(taskType)
                        .setUrl(request.getRequestURL().toString())
                        .setMethod(request.getMethod());
                remoteAsyncTaskService.addTask(remoteAsyncTask);
                //最后再返回等待信息，避免在异步任务中出现异常的时候response提前close
                respWriteWaitRes(response, code);
                return false; // 返回 false 阻止 Controller 方法执行
            }
        }
        return true; // 继续执行后续操作（包括 Controller 方法）
    }


    private void respWriteWaitRes(HttpServletResponse response, String code)  {
        R<String> wait = R.wait("任务已加入队列，请耐心等待任务完成", code);
        ServletUtils.renderString(response, Objects.requireNonNull(JsonUtils.toJsonString(wait)));
    }

}
