package com.jxw.shufang.common.asynchandler.config;

import com.jxw.shufang.common.asynchandler.interceptor.AsyncHandlerInterceptor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@AutoConfiguration
public class AsyncHandlerConfig implements WebMvcConfigurer {



    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        AsyncHandlerInterceptor asyncHandlerInterceptor = new AsyncHandlerInterceptor();
        registry.addInterceptor(asyncHandlerInterceptor);
    }
}
