package com.jxw.shufang.common.asynchandler.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.asynctask.api.RemoteAsyncTaskService;
import com.jxw.shufang.common.asynchandler.entry.StepUtilEntry;
import com.jxw.shufang.common.core.utils.ServletUtils;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StringUtils;

import java.util.concurrent.locks.ReentrantLock;

import static com.jxw.shufang.common.asynchandler.constants.BaseConstant.GLOBAL_TASK_CODE_HEADER;

/**
 * 任务步骤util  这里面的业务错误都不会往外抛，因为这个工具的目的就是只有在有@AsyncResult注解的方法中才会生效，
 * 而且这里面顶多只会修改任务的完成度，跟任务最终是否完成并没有关系，所以不会更改任务的状态
 *
 *
 * @date 2024/02/08 11:31:42
 */
@Slf4j
public class TaskStepUtil {

    private static final ThreadLocal<StepUtilEntry> TEMP_DYNAMIC_TASK = new TransmittableThreadLocal<>();

    private static final ThreadLocal<ReentrantLock> LOCK = new TransmittableThreadLocal<>();

    //获取dobbo服务
    private static final  RemoteAsyncTaskService remoteAsyncTaskService = SpringUtils.getBean(RemoteAsyncTaskService.class);

    public static synchronized void setSize(Long size) {
        if (TEMP_DYNAMIC_TASK.get() == null) {
            init();
        }
        if (TEMP_DYNAMIC_TASK.get() != null) {
            TEMP_DYNAMIC_TASK.get().setTaskSize(size);
            //远程调用服务，设置任务总数
            remoteAsyncTaskService.updateTaskSizeByTaskCode(TEMP_DYNAMIC_TASK.get().getTaskCode(), size);
        }
    }

    /**
     * 任务进度步进,高频率方法，需要父子线程锁(父子线程持有同一把锁)，并且只有当任务每次步进5%的时候才会调用远程服务
     */
    public static void step() {
        if (TEMP_DYNAMIC_TASK.get() == null
            || TEMP_DYNAMIC_TASK.get().getTaskSize() == null
            || LOCK.get() == null) {
            log.error("任务进度步进工具类未初始化");
        }
        ReentrantLock reentrantLock = LOCK.get();
        reentrantLock.lock();
        try {
            StepUtilEntry stepUtilEntry = TEMP_DYNAMIC_TASK.get();
            Long taskSize = stepUtilEntry.getTaskSize();
            Long doCount = stepUtilEntry.getDoCount();
            if (doCount == null) {
                doCount = 0L;
            }
            doCount++;
            stepUtilEntry.setDoCount(doCount);
            long step = doCount * 100 / taskSize;
            if (step % 5 == 0) {
                //远程调用服务，设置任务已完成数
                remoteAsyncTaskService.updateDoCountByTaskCode(TEMP_DYNAMIC_TASK.get().getTaskCode(), doCount);
            }
            if (doCount >= taskSize) {
                finish();
            }
        } finally {
            reentrantLock.unlock();
        }
    }

    private static void finish() {
        if (TEMP_DYNAMIC_TASK.get() != null) {
            TEMP_DYNAMIC_TASK.remove();
        }
        if (LOCK.get() != null) {
            LOCK.remove();
        }
    }


    private static synchronized void init() {
        if (remoteAsyncTaskService == null) {
           log.error("远程任务服务未初始化");
            return;
        }

        StepUtilEntry stepUtilEntry = TEMP_DYNAMIC_TASK.get();
        if (stepUtilEntry == null) {
            HttpServletRequest request = ServletUtils.getRequest();
            if (request == null) {
                log.error("请求为空，无法初始化任务进度步进工具类");
                return;
            }
            String taskCode = ServletUtils.getHeaderIgnoreCase(request, GLOBAL_TASK_CODE_HEADER);
            if (StringUtils.isBlank(taskCode)) {
                log.error("任务CODE为空，无法初始化任务进度步进工具类");
                return;
            }
            stepUtilEntry = new StepUtilEntry();
            stepUtilEntry.setTaskCode(taskCode);
            TEMP_DYNAMIC_TASK.set(stepUtilEntry);
        }
        ReentrantLock reentrantLock = LOCK.get();
        if (reentrantLock == null) {
            reentrantLock = new ReentrantLock();
            LOCK.set(reentrantLock);
        }
    }


}
