package com.jxw.shufang.common.core.enums;

import com.jxw.shufang.common.core.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: cyj
 * @date: 2025/3/8
 */
@Getter
@AllArgsConstructor
public enum PreferentialModifyTypeEnum {

    STUDENT_INTRODUCE(1, "转介绍"),
    STUDENT_TRANSFER(2, "会员转赠"),
    STUDENT_USED(3, "会员消费");

    private final Integer type;
    private final String desc;

    public static PreferentialModifyTypeEnum getByType(Integer type){
        for (PreferentialModifyTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new ServiceException("优惠额度类型异常");
    }
}
