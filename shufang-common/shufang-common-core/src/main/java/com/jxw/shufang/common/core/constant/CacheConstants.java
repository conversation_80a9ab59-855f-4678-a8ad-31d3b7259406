package com.jxw.shufang.common.core.constant;

/**
 * 缓存的key 常量
 *

 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 短信验证码发送次数 cache key
     */
    String SMS_CAPTCHA_COUNT_KEY = "sms_captcha_count:";

    /**
     * 短信验证码错误次数 cache key
     */
    String SMS_CAPTCHA_ERR_COUNT_KEY = "sms_captcha_err_count:";

    /**
     * 短信验证码 cache key
     */
    String SMS_CAPTCHA_KEY = "sms_captcha:";


    /**
     * ai视频学习记录分布式锁 cache key
     */
    String AI_STUDY_VIDEO_RECORD_LOCK_KEY = "ai_study_video_record_lock:";

    /**
     * 学习规划视频学习记录分布式锁 cache key
     */
    String STUDY_VIDEO_RECORD_LOCK_KEY = "study_video_record_lock:";

    /**
     * 题目视频学习记录分布式锁 cache key
     */
    String QUESTION_VIDEO_RECORD_LOCK_KEY = "question_video_record_lock:";


    /**
     * 微信code对应的access_token
     */
    String WX_CODE = "wx_code:";


    /**
     * keyId对应的微信openId
     */
    String WX_OPEN_ID = "wx_open_id:";

    /**
     * 退款申请分布式锁 key prefix
     */
    String REFUND_APPLY_LOCK_KEY = "refund_apply_lock:";
}
