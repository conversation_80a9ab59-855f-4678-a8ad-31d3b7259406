package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 菜单归属
 * @date 2024/02/19 09:53:58
 */
@Getter
@AllArgsConstructor
public enum MenuAscription {
    /**
     * 管理端
     */
    MANAGEMENT(1, "管理端"),
    /**
     * 店铺端
     */
    SHOP(2, "店铺端"),
    /**
     * 其他,预留出来，如果以后有其他端，可以添加
     */
    OTHER(3, "其他");

    private final Integer code;
    private final String info;

    public static MenuAscription getMenuAscription(Integer code) {
        for (MenuAscription menuAscription : values()) {
            if (menuAscription.getCode().equals(code)) {
                return menuAscription;
            }
        }
        return null;
    }
}
