package com.jxw.shufang.common.core.constant;

import com.jxw.shufang.common.core.enums.SysRole;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public interface UserConstants {

    /**
     * 顶级部门ID
     */
    Long TOP_DEPT_ID = 100L;

    /**
     * 平台内系统用户的唯一标志
     */
    String SYS_USER = "SYS_USER";

    /**
     * 没有删除标识
     */
    String DEL_FLAG_NO = "0";

    /**
     * 已删除标识
     */
    String DEL_FLAG_YES = "2";

    /**
     * 正常状态
     */
    String NORMAL = "0";

    /**
     * 异常状态
     */
    String EXCEPTION = "1";

    /**
     * 用户正常状态
     */
    String USER_NORMAL = "0";

    /**
     * 用户封禁状态
     */
    String USER_DISABLE = "1";

    /**
     * 角色正常状态
     */
    String ROLE_NORMAL = "0";

    /**
     * 角色封禁状态
     */
    String ROLE_DISABLE = "1";

    /**
     * 部门正常状态
     */
    String DEPT_NORMAL = "0";

    /**
     * 部门停用状态
     */
    String DEPT_DISABLE = "1";

    /**
     * 岗位正常状态
     */
    String POST_NORMAL = "0";

    /**
     * 岗位停用状态
     */
    String POST_DISABLE = "1";

    /**
     * 字典正常状态
     */
    String DICT_NORMAL = "0";

    /**
     * 是否为系统默认（是）
     */
    String YES = "Y";

    String NO = "N";

    /**
     * 是否菜单外链（是）
     */
    String YES_FRAME = "0";

    /**
     * 是否菜单外链（否）
     */
    String NO_FRAME = "1";

    /**
     * 菜单正常状态
     */
    String MENU_NORMAL = "0";

    /**
     * 菜单停用状态
     */
    String MENU_DISABLE = "1";

    /**
     * 菜单类型（目录）
     */
    String TYPE_DIR = "M";

    /**
     * 菜单类型（菜单）
     */
    String TYPE_MENU = "C";

    /**
     * 菜单类型（按钮）
     */
    String TYPE_BUTTON = "F";

    /**
     * Layout组件标识
     */
    String LAYOUT = "Layout";

    /**
     * ParentView组件标识
     */
    String PARENT_VIEW = "ParentView";

    /**
     * InnerLink组件标识
     */
    String INNER_LINK = "InnerLink";

    /**
     * 用户名长度限制
     */
    int USERNAME_MIN_LENGTH = 2;
    int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    int PASSWORD_MIN_LENGTH = 5;
    int PASSWORD_MAX_LENGTH = 20;

    /**
     * 超级管理员ID
     */
    Long SUPER_ADMIN_ID = 1L;

    /**
     * 超级管理员角色ID
     */
    Long SUPER_ADMIN_ROLE_ID = 1L;

    /**
     * 门店管理员角色Id
     */
    Long BRANCH_ADMIN_ROLE_ID = 1759783526829535233L;
    Long AGENT_ADMIN_ROLE_ID = 1831541886617100289L;

    /**
     * 授权所属部门及以下部门的角色ID
     */
    Set<Long> AUTHORIZE_BY_BRANCH_ROLE_SET =
        new HashSet<>(List.of(BRANCH_ADMIN_ROLE_ID, SysRole.PLATFORM_ADMINISTRATOR.getRoleId(),
            SysRole.TEACHING_SUPERVISOR.getRoleId(), SysRole.COMPANY_FINANCE.getRoleId()));


    /**
     * 产品上架状态
     */
    String PRODUCT_STATUS_UP = "0";

    /**
     * 产品下架状态
     */
    String PRODUCT_STATUS_DOWN = "1";

    /**
     * 轮播图上架状态
     */
    String BANNER_STATUS_UP = "1";

    /**
     * 轮播图下架状态
     */
    String BANNER_STATUS_DOWN = "2";

    /**
     * 默认会员类型arr
     */
    String[] DEFAULT_STUDENT_TYPE_ARR = {"体验卡"};


    /**
     * 属性里面各种状态值表示yes
     */
    String ATTR_STATUS_YES = "0";

    /**
     * 属性里面各种状态值表示no
     */
    String ATTR_STATUS_NO = "2";


    /**
     * 门店正常状态
     */
    String BRANCH_NORMAL = "0";

    /**
     * 门店停用状态
     */
    String BRANCH_DISABLE = "1";

    /**
     * 课程顶层类型（就是课程）
     */
    int TOP_COURSE_TYPE = 1;


    //积分减少类型
    String INTEGRAL_CHANGE_TYPE_REDUCE = "1";

    //积分增加类型
    String INTEGRAL_CHANGE_TYPE_ADD = "0";

    //积分变更方式：自动
    String INTEGRAL_CHANGE_STATE_AUTO = "0";

    //积分变更方式：手动
    String INTEGRAL_CHANGE_STATE_MANUAL = "1";

    //积分商品 上架
    String INTEGRAL_GOOD_STATUS_UP = "1";

    //积分商品 下架
    String INTEGRAL_GOOD_STATUS_DOWN = "2";

    //积分任务 上架
    String INTEGRAL_TASK_STATUS_UP = "1";

    //积分任务 下架
    String INTEGRAL_TASK_STATUS_DOWN = "2";


    //学习记录状态 0正常
    String STUDY_RECORD_STATUS_NORMAL = "0";

    //学习记录状态 1覆盖
    String STUDY_RECORD_STATUS_COVER = "1";

    //学习记录状态 2删除
    String STUDY_RECORD_STATUS_DELETE = "2";

    //学习规划保存状态1暂存
    String STUDY_PLANNING_STATUS_TEMPORARY = "1";

    //学习规划保存状态 2完成
    String STUDY_PLANNING_STATUS_COMPLETE = "2";

    //学习规划保存状态 3删除
    String STUDY_PLANNING_STATUS_DELETE = "3";

    //自助审批申请状态 0待审核 1允许 2拒绝
    String APPLY_CORRECTION_RECORD_STATUS_WAIT = "0";

    //自助审批申请状态 0待审核 1允许 2拒绝
    String APPLY_CORRECTION_RECORD_STATUS_ALLOW = "1";

    //自助审批申请状态 0待审核 1允许 2拒绝
    String APPLY_CORRECTION_RECORD_STATUS_REFUSE = "2";

    //批改类型（1测试 2练习 3预习 4反讲）不能为空
    String CORRECTION_TYPE_TEST = "1";

    //批改类型（1测试 2练习 3预习 4反讲）不能为空
    String CORRECTION_TYPE_PRACTICE = "2";
    String CORRECTION_TYPE_PREVIEW = "3";
    String CORRECTION_TYPE_SPEAK = "4";

    //    批改人类型（1顾问 2会员）
    String CORRECTION_PERSON_TYPE_STAFF = "1";

    //    批改人类型（1顾问 2会员）
    String CORRECTION_PERSON_TYPE_STUDENT = "2";

    // 批改状态 待批改
    String CORRECTION_STATUS_WAIT = "1";


    // 批改状态 批改中
    String CORRECTION_STATUS_DOING = "2";

    // 批改状态 已批改
    String CORRECTION_STATUS_DONE = "3";

    //打印试卷,1代表原卷
    String PRINT_PAPER_TYPE_ORIGINAL = "1";

    //打印试卷,2代表解析卷
    String PRINT_PAPER_TYPE_PARSE = "2";

    //打印试卷,3代表原卷+解析卷
    String PRINT_PAPER_TYPE_ORIGINAL_PARSE = "3";

    //暂定视频分片大小为10秒
    long VIDEO_SPLIT_SIZE = 10L;


    /**
     * 发布状态 1暂存 2已发布
     */
    String FEEDBACK_STATUS_TEMPORARY = "1";

    /**
     * 发布状态 1暂存 2已发布
     */
    String FEEDBACK_STATUS_PUBLISHED = "2";

    /**
     * 消息发送者类型 1 员工(或门店管理员) 2会员
     */
    String MESSAGE_SENDER_TYPE_STAFF = "1";
    /**
     * 消息发送者类型 1 员工(或门店管理员) 2会员
     */
    String MESSAGE_SENDER_TYPE_STUDENT = "2";

    /**
     * 消息类型 1文本 2图片
     */
    String MESSAGE_TYPE_TEXT = "1";

    String MESSAGE_TYPE_IMAGE = "2";

    /**
     * 消息读取状态（1已读 2未读）
     */
    String MESSAGE_READ_STATUS_READ = "1";

    String MESSAGE_READ_STATUS_UNREAD = "2";

    /**
     *  发送状态（1发送成功 2被过滤）
     */

    String MESSAGE_SEND_STATUS_SUCCESS = "1";

    String MESSAGE_SEND_STATUS_FILTER = "2";


    /**
     * 通知已读状态
     */
    String NOTICE_READ_STATUS_READ = "0";

    /**
     * 通知未读状态
     */
    String NOTICE_READ_STATUS_UNREAD = "2";

    //微信反馈状态 1已通知 2未通知(默认) 3通知但失败
    String FEEDBACK_PARENT_NOTIFICATION_STATUS_YES = "1";

    String FEEDBACK_PARENT_NOTIFICATION_STATUS_NO = "2";

    String FEEDBACK_PARENT_NOTIFICATION_STATUS_FAILURE = "3";

    //微信反馈状态 1已通知 2未通知(默认) 3通知但失败
    String FEEDBACK_PARENT_READ_STATUS_NO = "1";
    String FEEDBACK_PARENT_READ_STATUS_YES = "2";

    /**
     * YES（是）
     */
    String SYS_YES = "Y";
    /**
     * NO（N）
     */
    String SYS_NO = "N";
}

