package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.jxw.shufang.common.core.utils.StringUtils;

/**
 * 设备类型
 * 针对多套 用户体系
 */
@Getter
@AllArgsConstructor
public enum UserType {

    /**
     * pc端
     */
    SYS_USER("sys_user"),

    /**
     * 员工用户
     */
    STAFF_USER("staff_user"),

    /**
     * app会员端
     */
    APP_STU_USER("app_stu_user");



    private final String userType;

    public static UserType getUserType(String str) {
        for (UserType value : values()) {
            if (StringUtils.contains(str, value.getUserType())) {
                return value;
            }
        }
        throw new RuntimeException("'UserType' not found By " + str);
    }
}
