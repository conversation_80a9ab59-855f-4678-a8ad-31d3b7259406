package com.jxw.shufang.common.core.utils;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * Validator 校验框架工具
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidatorUtils {

    private static final Validator VALID = SpringUtils.getBean(Validator.class);

    public static <T> void validate(T object, Class<?>... groups) {
        Set<ConstraintViolation<T>> validate = VALID.validate(object, groups);
        if (!validate.isEmpty()) {
            throw new ConstraintViolationException("参数校验异常", validate);
        }
    }

    /**
     * 校验并返回校验结果
     *
     * @param object 对象
     * @param groups 组
     * @date 2024/04/01 06:03:58
     */
    public static <T> Set<ConstraintViolation<T>> validateReturnRes(T object,  Class<?>... groups) {
        return VALID.validate(object, groups);
    }

}
