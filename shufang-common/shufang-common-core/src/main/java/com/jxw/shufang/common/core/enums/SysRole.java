package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: cyj
 * @date: 2025/6/17
 * @Description 与业务息息相关的一些系统角色
 */
@Getter
@AllArgsConstructor
public enum SysRole {
    PLATFORM_ADMINISTRATOR(1796486328163135489L, "平台管理员", "platformAdministrator"),
    TEACHING_RESEARCH_ADMINISTRATOR(1840393241785991169L, "教研管理员", "jiaoyan"),
    TEACHING_SUPERVISOR(1935645219653722113L, "教学督导", "teachingSupervisor"),
    COMPANY_FINANCE(1846066150693076994L, "公司财务", "companyfinance"),
    ;
    final Long roleId;
    final String roleName;
    final String roleKey;
}
