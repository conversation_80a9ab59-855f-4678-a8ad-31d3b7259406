package com.jxw.shufang.common.core.utils;

import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class VideoSlicesUtils {

    private static final String LIMIT_SECONDS_REGEX = "(\\d+)''";

    /**
     * 视频切片格式：10:20-30:40
     */
    private static final String VIDEO_SLICE = "(\\d+:\\d+-\\d+:\\d+)";


    /**
     * 合并视频片段,保留顺序
     * tips:如果同一个时间区间的视频，但是倍数不一样，取列表最靠后的那个,秒数忽略
     *
     * @param oldSlices
     * @param newSlices
     * @return
     */
    public static String mergeVideoSlices(String oldSlices, String newSlices) {
        if (StringUtils.isBlank(oldSlices) && StringUtils.isBlank(newSlices)) {
            return "";
        }
        if (StringUtils.isBlank(newSlices)) {
            return delExcessCommas(oldSlices);
        }
        if (StringUtils.isBlank(oldSlices)) {
            return delExcessCommas(newSlices);
        }
        oldSlices = delExcessCommas(oldSlices);
        newSlices = delExcessCommas(newSlices);

        String[] oldSliceArray = oldSlices.split(",");
        String[] newSliceArray = newSlices.split(",");

        //用有序集合去重，先旧后新
        Set<String> set = new LinkedHashSet<>();
        //先合并两个数组
        set.addAll(Arrays.asList(oldSliceArray));
        set.addAll(Arrays.asList(newSliceArray));

        //然后按照时间区间形成一个map，如果有相同的区间，取列表最靠后的那个
        Map<String, String> bestMap = new HashMap<>();
        for (String slice : set) {
            Pattern pattern = Pattern.compile(VIDEO_SLICE);
            Matcher matcher = pattern.matcher(slice);
            if (matcher.find()) {
                String key = matcher.group(1);
                //后来者居上
                bestMap.put(key, slice);
            }
        }
        Collection<String> values = bestMap.values();

        set.removeIf(next -> !values.contains(next));
        return String.join(",", set);

    }

    /**
     * 清理多余逗号，去重(普通去重)
     *
     * @param slices
     * @return
     */
    public static String delExcessCommas(String slices) {
        if (StringUtils.isBlank(slices)) {
            return "";
        }
        String[] sliceArray = slices.split(",");
        return Arrays.stream(sliceArray).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(","));
    }


    public static Long calVideoSliceSeconds(String slices) {
        return calVideoSliceSeconds(slices, "");
    }

    /**
     * 计算视频片段的秒数,如 00:18-00:41(23’’1.0),00:42-00:45(3’’1.5),表示23秒+3秒
     *
     * @param slices        视频分片
     * @param excludeSlices 排除片段，存在时，计算时排除该片段的秒数
     * @return
     */
    public static Long calVideoSliceSeconds(String slices, String excludeSlices) {
        if (StringUtils.isBlank(slices)) {
            return 0L;
        }
        if (excludeSlices == null) {
            excludeSlices = "";
        }
        Pattern secondPattern = Pattern.compile(LIMIT_SECONDS_REGEX);
        Pattern videoLimitPattern = Pattern.compile(VIDEO_SLICE);

        Set<String> excludeSliceList = Arrays.stream(excludeSlices.split(",")).map(e -> {
            Matcher matcher = videoLimitPattern.matcher(e);
            if (matcher.find()) {
                return matcher.group(1);
            }
            return "";
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());

        String[] sliceArray = slices.split(",");
        long seconds = 0L;
        for (String slice : sliceArray) {
            Matcher secondMatcher = secondPattern.matcher(slice);
            Matcher videoLimitMatcher = videoLimitPattern.matcher(slice);
            if (!videoLimitMatcher.find()||!secondMatcher.find()) {
                continue;
            }
            if (!excludeSliceList.contains(videoLimitMatcher.group(1))) {
                seconds += Long.parseLong(secondMatcher.group(1));
            }
        }
        return seconds;
    }

    public static String videoSliceConcatMultiple(String slice, Double multiple) {
        if (StringUtils.isBlank(slice)) {
            return "";
        }
        if (multiple == null || multiple == 0 || multiple < 0) {
            multiple = 1.0;
        }
        //先计算区间秒数

        //先判断是否符合格式
        Pattern pattern = Pattern.compile(VIDEO_SLICE);
        Matcher matcher = pattern.matcher(slice);
        if (!matcher.find()) {
            throw new ServiceException("视频片段格式错误");
        }

        //计算出时间差
        String[] split = slice.split("-");

        long startTime = DateUtils.timeToSeconds(split[0]);
        long endTime = DateUtils.timeToSeconds(split[1]);

        long limitSeconds = endTime - startTime;

        //然后拼接片段
        return slice + "(" + limitSeconds + "''" + multiple + ")";
    }

    public static void main(String[] args) {
        Long s = calVideoSliceSeconds("00:18-00:41(23''1.0),00:42-00:45(3''1.5)", "00:42-00:45(3''1.5)");
        System.out.println("s = " + s);
    }

    public static String getEndTime(String slice) {
        if (StringUtils.isBlank(slice)) {
            return "";
        }
        Pattern pattern = Pattern.compile(VIDEO_SLICE);
        Matcher matcher = pattern.matcher(slice);
        if (!matcher.find()) {
            throw new ServiceException("视频片段格式错误");
        }
        String[] split = matcher.group(1).split("-");
        return split[1];
    }

    public static String getStartTime(String slice) {
        if (StringUtils.isBlank(slice)) {
            return "";
        }
        Pattern pattern = Pattern.compile(VIDEO_SLICE);
        Matcher matcher = pattern.matcher(slice);
        if (!matcher.find()) {
            throw new ServiceException("视频片段格式错误");
        }
        String[] split = matcher.group(1).split("-");
        return split[0];
    }

    /**
     * 合并相邻的分片，00:00-01:10(10''1.0)01:10-01:20(10''1.0) 合并成00:00-01:20(10''1.0)
     * @param slices 时间分片字符串
     * @return 合并后的时间分片
     */
    public static String mergeConsecutiveSlices(String slices) {
        if (StringUtils.isBlank(slices)) {
            return "";
        }

        String[] sliceArray = slices.split(",");
        // 如果只有一个那么就直接返回不合并
        if (sliceArray.length < 2) {
            return slices;
        }

        List<String> result = new ArrayList<>();
        String currentSlice = sliceArray[0];

        for (int i = 1; i < sliceArray.length; i++) {
            String nextSlice = sliceArray[i];

            // 检查是否可以合并
            if (canMerge(currentSlice, nextSlice)) {
                currentSlice = doMerge(currentSlice, nextSlice);
            } else {
                // 不能合并，可能存在参数不同，或者时间片段不同
                result.add(currentSlice);
                // 定义新的时间分片
                currentSlice = nextSlice;
            }
        }
        result.add(currentSlice);

        return String.join(",", result);
    }

    /**
     * 检查是否可以合并
     * @param currentSlice 当前时间片段
     * @param nextSlice 下一个时间片段
     * @return 是否可以合并
     */
    private static boolean canMerge(String currentSlice, String nextSlice) {
        try {
            // 获取当前时间片段的结束部分
            String endTime1 = getEndTime(currentSlice);
            // 获取下一个片段的开始部分
            String startTime2 = getStartTime(nextSlice);


            // 获取参数部分
            String param1 = currentSlice.substring(currentSlice.indexOf("("));
            String param2 = nextSlice.substring(nextSlice.indexOf("("));

            // 如果参数的部分和两个时间相同，则可以合并
            return endTime1.equals(startTime2) && param1.equals(param2);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 合并两个片段
     * @param currentSlice 当前时间片段
     * @param nextSlice 下一个时间片段
     * @return 合并后的片段
     */
    private static String doMerge(String currentSlice, String nextSlice) {
        String startTime = getStartTime(currentSlice);
        String endTime = getEndTime(nextSlice);
        String param = currentSlice.substring(currentSlice.indexOf("("));
        return startTime + "-" + endTime + param;
    }

    public String convert(Map<String, String> mapStart, Map<String, String> mapEnd) {
        StringBuilder result = new StringBuilder();
        mapStart.forEach((k, v) -> {
            if (mapEnd.containsKey(k)) {
                // 截取
                String[] split = mapStart.get(k).split("-");
                String str = VideoSlicesUtils.getStartTime(mapEnd.get(k)) + "-" + split[1];
                result.append(",").append(str);
            }
        });

        return result.toString();
    }

    /**
     * 忽略重复片段
     * @param oldSlice
     * @param newSlice
     * @return
     */
    public static Boolean ignoreRepeatSlice(String oldSlice,String newSlice) {
       return oldSlice.equals(mergeVideoSlices(oldSlice,newSlice));
    }
}
