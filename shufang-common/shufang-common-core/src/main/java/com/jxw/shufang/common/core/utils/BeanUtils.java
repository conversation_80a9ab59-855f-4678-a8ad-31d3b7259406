package com.jxw.shufang.common.core.utils;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.io.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BeanUtils extends BeanUtil {

    /**
     * 深拷贝一个对象
     * @param source 要拷贝的对象
     * @param <T> 对象的类型
     * @return 深拷贝后的对象
     * @throws IOException 如果序列化或反序列化过程中发生 I/O 错误
     * @throws ClassNotFoundException 如果找不到对象的类定义
     */
    public static <T extends Serializable> T copyBean(T source)  {
        try {
            // 序列化对象
            byte[] bytes = serialize(source);
            // 反序列化对象
            return deserialize(bytes);
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }

    }

    /**
     * 将对象序列化为字节数组
     * @param obj 要序列化的对象
     * @return 序列化后的字节数组
     * @throws IOException 如果序列化过程中发生 I/O 错误
     */
    private static byte[] serialize(Serializable obj) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ObjectOutputStream objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
        objectOutputStream.writeObject(obj);
        objectOutputStream.close();
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 将字节数组反序列化为对象
     * @param bytes 包含对象序列化内容的字节数组
     * @param <T> 对象的类型
     * @return 反序列化后的对象
     * @throws IOException 如果反序列化过程中发生 I/O 错误
     * @throws ClassNotFoundException 如果找不到对象的类定义
     */
    @SuppressWarnings("unchecked")
    private static <T extends Serializable> T deserialize(byte[] bytes) throws IOException, ClassNotFoundException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
        ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);
        T obj = (T) objectInputStream.readObject();
        objectInputStream.close();
        return obj;
    }

}
