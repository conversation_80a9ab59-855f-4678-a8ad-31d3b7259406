package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: cyj
 * @date: 2025/7/14
 */
@Getter
@AllArgsConstructor
public enum PurchasedCardFlag {

    NON(0, "未购卡"), NEW(1, "新生购卡"), OLD(2, "老生续费");

    /**
     * 标识code
     */
    private Integer code;
    /**
     * 标识名称
     */
    private String desc;

    /**
     * 根据code获取flag
     *
     * @param code
     * @return
     */
    public static PurchasedCardFlag getFlagByCode(Integer code) {
        for (PurchasedCardFlag flag : values()) {
            if (flag.getCode().equals(code)) {
                return flag;
            }
        }
        return NON;
    }
}
