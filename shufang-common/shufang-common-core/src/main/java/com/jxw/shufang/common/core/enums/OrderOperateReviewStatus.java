package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderOperateReviewStatus {
    WAIT_REVIEW(0, "待审核"),
    REVIEWED(1, "已审核"),
    REJECT(2, "已驳回");
    private final Integer code;
    private final String info;

    public static OrderOperateReviewStatus getByCode(Integer code) {
        for (OrderOperateReviewStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
