package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AppTypeEnum {

    WXMP(0, "公众号"),
    APP(1, "小程序"),
    ;

    private final Integer code;
    private final String info;

    public static AppTypeEnum getByCode(Integer code) {
        for (AppTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
