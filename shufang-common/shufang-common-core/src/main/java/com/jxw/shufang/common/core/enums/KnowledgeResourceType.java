package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KnowledgeResourceType {
    HANDOUT("handout",Boolean.FALSE, "讲义",0),

    PRACTICE("practice",Boolean.TRUE,  "练习试卷",1),

    PRACTICE_ANALYSIS("practice_analysis",Boolean.FALSE, "练习试卷带解析",2),

    TEST("test",Boolean.TRUE, "测验试卷",3),

    TEST_ANALYSIS("test_analysis",Boolean.FALSE, "测验试卷带解析",4);


    private final String type;
    private final Boolean isQuestionType;
    private final String description;
    private final Integer sortNum;

    public static KnowledgeResourceType getByType(String type) {
        for (KnowledgeResourceType value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}



