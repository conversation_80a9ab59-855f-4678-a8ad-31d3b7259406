package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.YearMonth;

/**
 * 季度枚举
 * 定义学年的四个时期：寒假、春季、暑假、秋季
 * 与Quarter维护表的数据保持一致
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Getter
@AllArgsConstructor
public enum QuarterlyEnum {
    
    /**
     * 寒假：1-2月（寒暑模式，按日规划）
     */
    WINTER(0, "寒假", 1, 2),
    
    /**
     * 春季：3-6月（春秋模式，按周规划）
     */
    SPRING(1, "春季", 3, 6),
    
    /**
     * 暑假：7-8月（寒暑模式，按日规划）
     */
    SUMMER(2, "暑假", 7, 8),
    
    /**
     * 秋季：9-12月（春秋模式，按周规划）
     */
    AUTUMN(3, "秋季", 9, 12);

    /**
     * 季度编码
     */
    private final Integer code;
    
    /**
     * 季度描述
     */
    private final String desc;
    
    /**
     * 开始月份
     */
    private final Integer startMonth;
    
    /**
     * 结束月份
     */
    private final Integer endMonth;

    /**
     * 根据编码获取季度枚举
     * 
     * @param code 季度编码
     * @return 季度枚举，如果找不到返回null
     */
    public static QuarterlyEnum getQuarterlyEnum(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (QuarterlyEnum quarterlyEnum : values()) {
            if (quarterlyEnum.getCode().equals(code)) {
                return quarterlyEnum;
            }
        }
        return null;
    }

    /**
     * 根据当前日期获取对应的季度枚举
     * 
     * @param currentDay 当前日期
     * @return 季度枚举，如果找不到返回null
     */
    public static QuarterlyEnum getCurrentQuarterEnum(LocalDate currentDay) {
        if (currentDay == null) {
            return null;
        }
        
        for (QuarterlyEnum quarterlyEnum : values()) {
            LocalDate startDate = YearMonth.of(currentDay.getYear(), quarterlyEnum.startMonth).atDay(1);
            LocalDate endDate = YearMonth.of(currentDay.getYear(), quarterlyEnum.endMonth).atEndOfMonth();
            
            if ((currentDay.isAfter(startDate) || currentDay.isEqual(startDate)) && 
                (currentDay.isBefore(endDate) || currentDay.isEqual(endDate))) {
                return quarterlyEnum;
            }
        }
        return null;
    }

    /**
     * 判断是否为春秋模式
     * 春季和秋季为春秋模式（按周规划）
     * 寒假和暑假为寒暑模式（按日规划）
     * 
     * @return true-春秋模式，false-寒暑模式
     */
    public boolean isSpringAutumnMode() {
        return this == SPRING || this == AUTUMN;
    }

    /**
     * 判断是否为寒暑模式
     * 寒假和暑假为寒暑模式（按日规划）
     * 春季和秋季为春秋模式（按周规划）
     * 
     * @return true-寒暑模式，false-春秋模式
     */
    public boolean isSummerWinterMode() {
        return this == WINTER || this == SUMMER;
    }

    /**
     * 获取季度的模式类型
     * 
     * @return 1-春秋模式，2-寒暑模式
     */
    public Integer getModeType() {
        return isSpringAutumnMode() ? 1 : 2;
    }

    /**
     * 根据当前日期判断是否为春秋模式
     * 
     * @param currentDay 当前日期
     * @return true-春秋模式，false-寒暑模式
     */
    public static boolean isCurrentSpringAutumnMode(LocalDate currentDay) {
        QuarterlyEnum currentQuarter = getCurrentQuarterEnum(currentDay);
        if (currentQuarter == null) {
            // 如果无法确定季度，按月份判断
            int month = currentDay.getMonthValue();
            return (month >= 3 && month <= 6) || (month >= 9 && month <= 12);
        }
        return currentQuarter.isSpringAutumnMode();
    }

    /**
     * 根据当前日期判断是否为寒暑模式
     * 
     * @param currentDay 当前日期
     * @return true-寒暑模式，false-春秋模式
     */
    public static boolean isCurrentSummerWinterMode(LocalDate currentDay) {
        return !isCurrentSpringAutumnMode(currentDay);
    }
}
