package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Getter
@AllArgsConstructor
public enum StaffRole {
    //会员顾问
    MEMBER_CONSULTANT(1762697582909513730L, "会员顾问", "memberConsultant"),
    //资料下载打印

    DATA_DOWNLOAD_PRINT(1762697651238920194L, "资料下载打印", "dataDownloadPrint"),
    //门店财务
    STORE_FINANCE(1762697713616609282L, "门店财务", "storeFinance"),
    //运营物料
    OPERATION_MATERIAL(1762697774417240065L, "运营物料", "operationMaterial"),
    //执行店长
    EXECUTIVE_STORE_MANAGER(1762697814422511617L, "执行店长", "executiveStoreManager"),
    //销售顾问
    SALES_CONSULTANT(1762697814425784567L, "销售顾问", "salesConsultant"),
    // crm市场角色
    CRM_MARKETER(3L, "CRM市场销售人员", "PUBLIC_STAFF"),
    ;
    final Long roleId;
    final String roleName;
    final String roleKey;

    public static <T> StaffRole getStaffPost(T s, Function<StaffRole, T> function) {
        for (StaffRole value : values()) {
            if (function.apply(value).toString().equals(s.toString())) {
                return value;
            }
        }
        throw new ServiceException("'StaffRole' not found By " + s);
    }

    public static List<Map<String,Object>> getStaffRoleInfo(){
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        for (StaffRole value : values()) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("roleId",value.getRoleId());
            map.put("roleName",value.getRoleName());
            map.put("roleKey",value.getRoleKey());
            map.put("enumKey",value);
            maps.add(map);
        }
        return maps;
    }

    public static List<Map<String, Object>> getStaffRoleInfo(List<StaffRole> staffRoles) {
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        for (StaffRole value : staffRoles) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("roleId", value.getRoleId());
            map.put("roleName", value.getRoleName());
            map.put("roleKey", value.getRoleKey());
            map.put("enumKey", value);
            maps.add(map);
        }
        return maps;
    }

    public static List<Long> getRoleIdList(){
        List<Long> list = new ArrayList<>();
        for (StaffRole value : values()) {
            list.add(value.getRoleId());
        }
        return list;
    }
}
