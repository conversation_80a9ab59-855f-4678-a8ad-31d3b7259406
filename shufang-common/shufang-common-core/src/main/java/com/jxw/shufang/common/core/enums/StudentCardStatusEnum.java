package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StudentCardStatusEnum {

    EXPIRED(0, "已退款"),
    PAYER(1, "已支付");

    private final int code;
    private final String info;

    public static StudentCardStatusEnum getByCode(Integer code) {
        for (StudentCardStatusEnum value : values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }
}
