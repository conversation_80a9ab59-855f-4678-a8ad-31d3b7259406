package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RechargeOrderStatusEnum {

    WAIT_PAY(1, "待支付"),
    PAYED(2, "已支付"),
    CANCEL(3, "已取消"),
    REFUNDING(4, "退款中"),
    REFUNDED(5, "已退款");

    private final Integer code;
    private final String info;

    public static RechargeOrderStatusEnum getByCode(Integer code) {
        for (RechargeOrderStatusEnum value : values()) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }
}
