package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderTypeEnum {

    BUY_NEW_CARD(0, "购买新卡"),
    OLD_CARD_UPGRADE(1, "旧卡升级"),
    CHARGE(0, "充值订单"),
    STUDENT(1, "会员订单"),
    ;

    private final Integer code;
    private final String info;

    public static OrderTypeEnum getByCode(Integer code) {
        for (OrderTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
