package com.jxw.shufang.common.core.validate.customize.handler;

import cn.hutool.core.util.ObjectUtil;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.validate.customize.annotation.CustomizeValidate;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;

/**
 * 抽象参数校验处理器父类 - 用来校验通用场景
 * 不同的校验场景，只需要提供子类继承该类即可
 */
@Slf4j
public class CustomizeValidateHandler implements ConstraintValidator<CustomizeValidate, Object> {

    /**
     * 获取自定义参数校验注解对象
     */
    private CustomizeValidate customizeValidate;

    @Override
    public void initialize(CustomizeValidate constraintAnnotation) {
        this.customizeValidate = constraintAnnotation;
    }

    @Override
    public boolean isValid(Object val, ConstraintValidatorContext constraintValidatorContext) {

        //默认如果校验数据为空，则直接放行，不做校验
        //用@NotNull注解来校验空值
        if (ObjectUtil.isNull(val) || ObjectUtil.isNull(customizeValidate)) return true;

        try {
            //获取自定义校验处理器
            Class<? extends CustomizeValidateHandler> validateHandlerClass = customizeValidate.handler();

            //从Spring容器中获取对应的校验处理器Bean对象
            CustomizeValidateHandler validateHandler = SpringUtils.getBean(validateHandlerClass);
            log.info("【通用校验处理器】获取对应的校验处理器：{}", validateHandler);
            //如果没有获取该处理器的Bean，则直接放行
            if (ObjectUtil.isNull(validateHandler)) return true;

            //执行真正的校验逻辑，并返回校验结果，具体校验逻辑取决于响应的子类
            return validateHandler.doValid(val, customizeValidate);
        } catch (Exception e) {
            log.error("【自定义参数校验处理器】校验逻辑异常！", e);
            //校验出现问题，默认直接放行
            return true;
        }
    }

    /**
     * 校验执行方法，具体的逻辑取决于子类实现
     * @param val
     * @return
     */
    public boolean doValid(Object val, CustomizeValidate customizeValidate) {
        return true;
    }
}
