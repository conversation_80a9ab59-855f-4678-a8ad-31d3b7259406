package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountTradeTypeEnum {

    AGENT(0, "代理商"),
    BRANCH(1, "门店"),
    STUDENT(2, "会员"),
    ;

    private final Integer code;
    private final String info;

    public static AccountTradeTypeEnum getByCode(Integer code) {
        for (AccountTradeTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
