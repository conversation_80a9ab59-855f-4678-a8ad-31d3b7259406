package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QuestionType {
    TEST("test", "测验试卷"),
    PRACTICE("practice", "练习试卷");

    private final String type;
    private final String description;

    public static QuestionType getByType(String type) {
        for (QuestionType value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}



