package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 书房CRM固定的RoleKey
 *
 * 市场归属=市场人员
 * 销售归属=体验人员
 * 销售归属=伴学人员
 *
 */
@Getter
@AllArgsConstructor
public enum CrmRoleKey {

    /**
     * 市场人员
     */
    PUBLIC_STAFF("市场人员"),

    /**
     * 销售人员
     */
    WITH_LEARN_STAFF("销售人员"),

    /**
     * 伴学人员
     */
    FORMAL_STAFF("伴学人员"),

    /**
     * 财务人员
     */
    ACCOUNT_STAFF("财务人员"),

    /**
     * 督学人员
     */
    SUPERINTENDENT_STAFF("督学人员"),

    /**
     * 伴销人员
     */
    WITH_LEARN_SALE_STAFF("伴销人员"),

    /**
     * 店长人员
     */
    administrator("店长人员"),

    /**
     * 区域管理员
     */
    region("区域管理员");

    /**
     * 描述
     */
    private final String desc;

}
