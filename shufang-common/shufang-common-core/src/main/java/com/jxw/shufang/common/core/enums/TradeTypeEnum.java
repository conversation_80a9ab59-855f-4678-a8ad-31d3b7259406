package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TradeTypeEnum {

    PAY(0, "支出"),
    INCOME (1, "收入"),
    ;

    private final Integer code;
    private final String info;

    public static TradeTypeEnum getByCode(Integer code) {
        for (TradeTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
