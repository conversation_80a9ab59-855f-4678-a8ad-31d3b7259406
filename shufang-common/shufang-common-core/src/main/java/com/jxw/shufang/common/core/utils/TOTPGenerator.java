package com.jxw.shufang.common.core.utils;

import cn.hutool.core.codec.Base32;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Date;

public class TOTPGenerator {

    private static final int TIME_STEP = 60; // 时间步长60秒
    private static final int CODE_DIGITS = 6; // 6位验证码
    private static final String base32Key = "7e9ccZ0Loechce1jWho4euYNs0Y="; // 6位验证码

    public static String generateTOTP(String base32Key) throws NoSuchAlgorithmException, InvalidKeyException {
        return generateTOTP(base32Key, TIME_STEP, CODE_DIGITS);
    }

    public static String generateTOTP() throws NoSuchAlgorithmException, InvalidKeyException {
        return generateTOTP(base32Key, TIME_STEP, CODE_DIGITS);
    }

    public static String generateTOTP(String base32Key, int TIME_STEP,int CODE_DIGITS) throws NoSuchAlgorithmException, InvalidKeyException {
        // Base32解码密钥
        Base32 base32 = new Base32();
        byte[] key = base32.decode(base32Key);

        // 计算时间计数器
        long currentTime = new Date().getTime() / 1000; // 当前时间戳（秒）
        long timeCounter = currentTime / TIME_STEP;

        // 将时间计数器转换为8字节大端数组
        byte[] counterBytes = ByteBuffer.allocate(8)
            .putLong(timeCounter)
            .array();

        // 使用HmacSHA1计算哈希
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(key, "HmacSHA1"));
        byte[] hmacResult = mac.doFinal(counterBytes);

        // 动态截断处理
        int offset = hmacResult[hmacResult.length - 1] & 0x0F;
        int binary = ((hmacResult[offset]     & 0x7F) << 24 |
            ((hmacResult[offset + 1] & 0xFF) << 16) |
            ((hmacResult[offset + 2] & 0xFF) << 8) |
            (hmacResult[offset + 3] & 0xFF)) ;

        int otp = binary % (int) Math.pow(10, CODE_DIGITS);

        // 格式化为6位字符串，补前导零
        return String.format("%0" + CODE_DIGITS + "d", otp);
    }

    public static void main(String[] args) {
        try {
//            System.out.println(generateSecretKey());
            String secretKey = "7e9ccZ0Loechce1jWho4euYNs0Y=";
            while (true) {
                String code = generateTOTP(secretKey);
                System.out.println("生成的TOTP验证码: " + code);
                Thread.sleep(1000); // 暂停1秒
            }
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static String generateSecretKey() {
        SecureRandom random = new SecureRandom();
        byte[] keyBytes = new byte[20]; // 160 bits
        random.nextBytes(keyBytes);
        return Base64.getEncoder().encodeToString(keyBytes);
    }

}
