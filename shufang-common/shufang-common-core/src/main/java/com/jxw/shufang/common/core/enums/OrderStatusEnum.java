package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    WAIT_PAY("1", "待支付"),
    PAYED("2", "已支付"),
    CANCEL("3", "已取消"),
    REFUNDING("4", "退款中"),
    REFUNDED("5", "已退款"),
    PENDING_PAY("6","待补缴");

    private final String code;
    private final String info;

    public static OrderStatusEnum getByCode(String code) {
        for (OrderStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<OrderStatusEnum> queryOrderStatusList() {
        return Arrays.asList(values())
            .stream()
            .collect(Collectors.toList());
    }

    public static List<String> getExistPendingOrderStatus() {
        List<String> existPendingOrderStatus = Arrays.asList(OrderStatusEnum.PENDING_PAY)
            .stream()
            .map(OrderStatusEnum::getCode)
            .toList();
        return existPendingOrderStatus;
    }

    public static List<String> noExistPendingOrderStatus() {
        return Arrays.stream(OrderStatusEnum.values())
            .filter(f -> !OrderStatusEnum.PENDING_PAY.equals(f))
            .map(OrderStatusEnum::getCode)
            .toList();
    }
}
