package com.jxw.shufang.common.core.utils;

import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import com.jxw.shufang.common.core.exception.ServiceException;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.BiFunction;

/**
 * 时间工具类
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final String YYYY = "yyyy";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String HH_MM_SS = "HH:mm:ss";

    private static final String[] PARSE_PATTERNS = {
        "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
        "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
        "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM",
        "yyyy-MM-dd HH:mm:ss.SSS"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String getTime(Date date) {
        return parseDateToStr(HH_MM_SS, date);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), PARSE_PATTERNS);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date parseDateByPattern(Date date, String pattern) {
        String dateToStr = parseDateToStr(pattern, date);
        try {
            return parseDate(dateToStr, pattern);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 00:00或00:00:00格式的时间转化为秒数
     * @param timeStr
     * @return
     */
    public static long timeToSeconds(String timeStr) {
        String[] parts = timeStr.split(":");
        if (parts.length == 2) {
            long minutes = Long.parseLong(parts[0]);
            long seconds = Long.parseLong(parts[1]);
            return minutes * 60 + seconds;
        } else if (parts.length == 3) {
            long hours = Long.parseLong(parts[0]);
            long minutes = Long.parseLong(parts[1]);
            long seconds = Long.parseLong(parts[2]);
            return hours * 3600 + minutes * 60 + seconds;
        } else {
            throw new ServiceException("Invalid time format");
        }
    }

    /**
     * date的时间(时分秒部分)转化为秒数，舍弃毫秒部分
     * @param date
     * @return
     */
    public static long timeToSeconds(Date date) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            int hours = calendar.get(Calendar.HOUR_OF_DAY);
            int minutes = calendar.get(Calendar.MINUTE);
            int seconds = calendar.get(Calendar.SECOND);
            return hours * 3600 + minutes * 60 + seconds;
        } catch (Exception e) {
            throw new ServiceException("Invalid time");
        }
    }

    //本日的开始
    public static Date getStartOfToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //本日的结束
    public static Date getEndOfToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //昨天的开始
    public static Date getStartOfYesterday() {
        Calendar calendar = Calendar.getInstance();
        // 将日期回退一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //昨天的结束
    public static Date getEndOfYesterday() {
        Calendar calendar = Calendar.getInstance();
        // 将日期回退一天
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //本周的开始
    public static Date getStartOfThisWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //本周的结束
    public static Date getEndOfThisWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.add(Calendar.WEEK_OF_YEAR, 1);
        calendar.add(Calendar.DAY_OF_WEEK, -3); // 减去一天，因为add已经增加了一周
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //上周的开始
    public static Date getStartOfLastWeek() {
        Calendar calendar = Calendar.getInstance();
        // 设置日历为本周的第一天（假设是周日）
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 回退一周
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        calendar.add(Calendar.DAY_OF_WEEK, 1);
        // 设置时间为当天的凌晨00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //上周的结束
    public static Date getEndOfLastWeek() {
        Calendar calendar = Calendar.getInstance();
        // 设置日历为本周的第一天（假设是周日）
        calendar.setFirstDayOfWeek(Calendar.SUNDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 不需要回退，因为接下来我们会将其设置为上周的周六
        // 设置为本周的周六
        calendar.add(Calendar.DAY_OF_WEEK, 7);
        // 设置时间为当天的晚上23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        // 回退一周，得到上周的周六
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        return calendar.getTime();
    }

    //本月的开始
    public static Date getStartOfThisMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //本月的结束
    public static Date getEndOfThisMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //上月的开始
    public static Date getStartOfLastMonth() {
        Calendar calendar = Calendar.getInstance();
        // 获取当前月份
        int currentMonth = calendar.get(Calendar.MONTH);
        // 将月份减1，以获取上个月的开始日期
        calendar.set(Calendar.MONTH, currentMonth - 1);
        // 设置日期为1号
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 设置时间为当天的凌晨00:00:00
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //上月的结束
    public static Date getEndOfLastMonth() {
        Calendar calendar = Calendar.getInstance();
        // 获取当前月份
        int currentMonth = calendar.get(Calendar.MONTH);
        // 将月份减1，以获取上个月的日期
        calendar.set(Calendar.MONTH, currentMonth - 1);
        // 获取上个月的天数
        int lastDayOfMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日期为上个月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, lastDayOfMonth);
        // 设置时间为当天的晚上23:59:59
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    //获得最大的连续天数
    public static int findLongestConsecutiveDays(List<String> datesStr) {
        // 定义日期格式并转换字符串为LocalDate对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate[] dates = datesStr.stream()
            .map(dateStr -> LocalDate.parse(dateStr, formatter))
            .toArray(LocalDate[]::new);

        // 对日期进行排序（如果需要的话，通常来自字符串的LocalDate已经按字典序排序，但最好显式排序）
        Arrays.sort(dates);

        int maxConsecutive = 0;
        int currentConsecutive = 1;

        // 遍历日期数组，寻找最长的连续序列
        for (int i = 1; i < dates.length; i++) {
            // 检查当前日期和前一个日期是否连续
            if (dates[i].isAfter(dates[i-1].plusDays(1)) == false) { // 注意：isAfter用于检查不连续的情况，但这里我们实际上是在检查相等或连续
                // 如果连续，则递增当前连续天数
                currentConsecutive++;
                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
            } else {
                // 如果不连续，重置当前连续天数
                currentConsecutive = 1;
            }
        }

        return maxConsecutive;
    }

    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
    public static  LocalDateTime parseLocalDateTime(String dateStr) {
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    public static  Date getCreateTimeAsDate(LocalDateTime date) {
        return Date.from(date.atZone(ZoneId.systemDefault()).toInstant());
    }
    public static Date setStartOfDay(Date date) {
        if (date == null) {
            return null;
        }
        // 1. 转为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();
        // 2. 设置时间为 00:00:00
        LocalDateTime startDay = localDateTime
            .withHour(0)
            .withMinute(0)
            .withSecond(0)
            .withNano(0);
        // 3. 转回 Date
        return Date.from(startDay.atZone(ZoneId.systemDefault()).toInstant());
    }
    public static Date setEndOfDay(Date date) {
        if (date == null) {
            return null;
        }
        // 1. 转为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();
        // 2. 设置时间为 23:59:59
        LocalDateTime endOfDay = localDateTime
            .withHour(23)
            .withMinute(59)
            .withSecond(59)
            .withNano(999);
        // 3. 转回 Date
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断当前时间是否在指定时间段内
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean isCurrentTimeBetweenDates(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false; // 如果日期为空，默认不合法
        }
        Date now = new Date(); // 当前时间
        return !now.before(startDate) && !now.after(endDate);
    }
    /**
     * 获取本周一的日期时间
     * @return LocalDateTime 本周一的日期时间
     */
    public static LocalDateTime getStartOfWeek() {
        return LocalDate.now()
            .with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
            .atStartOfDay();
    }

    /**
     * 获取本周日的日期时间
     * @return LocalDateTime 本周日的日期时间
     */
    public static LocalDateTime getEndOfWeek() {
        return LocalDate.now()
            .with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY))
            .atTime(23, 59, 59);
    }
}
