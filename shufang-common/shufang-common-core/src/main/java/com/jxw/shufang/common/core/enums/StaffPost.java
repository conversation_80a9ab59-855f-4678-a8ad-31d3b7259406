package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import com.jxw.shufang.common.core.exception.ServiceException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Getter
@AllArgsConstructor
public enum StaffPost {
    //专职老师
    FULL_TIME_TEACHER(1762697929707151361L, "专职老师", "fullTimeTeacher"),
    //兼职老师
    PART_TIME_TEACHER(1762697990738468866L, "兼职老师", "partTimeTeacher"),
    ;
    final Long postId;
    final String postName;
    final String postCode;

    public static <T>  StaffPost getStaffPost(T s, Function<StaffPost, T> function) {
        for (StaffPost value : values()) {
            if (function.apply(value).toString().equals(s)) {
                return value;
            }
        }
        throw new ServiceException("'StaffPost' not found By " + s);
    }

    public static List<Map<String,Object>> getStaffPostInfo(){
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        for (StaffPost value : values()) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("postId",value.getPostId());
            map.put("postName",value.getPostName());
            map.put("postCode",value.getPostCode());
            map.put("enumKey",value);
            maps.add(map);
        }
        return maps;
    }

    public static List<Long> getPostIdList(){
        List<Long> list = new ArrayList<>();
        for (StaffPost value : values()) {
            list.add(value.getPostId());
        }
        return list;
    }

}
