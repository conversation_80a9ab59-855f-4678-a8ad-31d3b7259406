package com.jxw.shufang.common.core.validate.customize.annotation;

import com.jxw.shufang.common.core.validate.customize.handler.CustomizeValidateHandler;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 指定参数校验注解
 */
@Target({ElementType.TYPE, ElementType.METHOD, ElementType.FIELD, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CustomizeValidateHandler.class)
public @interface CustomizeValidate {

    /**
     * 异常提醒信息
     * @return
     */
    String message() default "";

    /**
     * 分组校验
     * @return
     */
    Class<?>[] groups() default { };

    /**
     * 校验等级
     * @return
     */
    Class<? extends Payload>[] payload() default { };

    /**
     * 自定义参数校验逻辑处理器
     * @return
     */
    Class<? extends CustomizeValidateHandler> handler();
}
