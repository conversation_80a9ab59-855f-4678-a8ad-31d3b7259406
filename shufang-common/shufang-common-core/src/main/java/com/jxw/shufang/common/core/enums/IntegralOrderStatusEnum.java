package com.jxw.shufang.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IntegralOrderStatusEnum {
    //订单状态（1待兑换 2已兑换 3已取消）
    WAIT_EXCHANGE("1", "待兑换"),
    EXCHANGED("2", "已兑换"),
    CANCELLED("3", "已取消");
    private final String code;
    private final String info;

    public static IntegralOrderStatusEnum getByCode(String code) {
        for (IntegralOrderStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
