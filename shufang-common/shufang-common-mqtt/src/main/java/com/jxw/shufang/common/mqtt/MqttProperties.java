package com.jxw.shufang.common.mqtt;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "mqtt")
public class MqttProperties {

    /**
     * mqtt服务器主机地址
     */
    private String broker;
    /**
     * mqtt服务tcp协议连接地址
     */
    private String tcpUrl;
    /**
     * mqtt服务ws协议连接地址
     */
    private String wsUrl;
    /**
     * mqtt服务wss协议连接地址
     */
    private String wssUrl;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 客户端
     */
    private String clientId;
    /**
     * 超时时间
     */
    private Integer timeout;
    /**
     * 心跳时间
     */
    private Integer keepAlive;
    /**
     * 消息质量
     */
    private Integer qos;
    /**
     * 默认topic
     */
    private String topic;

    /**
     * 设备端账号
     */
    private String clientAccount;

    /**
     * 设备端密码
     */
    private String clientSecret;

}
