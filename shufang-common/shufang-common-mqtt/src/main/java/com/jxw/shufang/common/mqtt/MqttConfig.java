package com.jxw.shufang.common.mqtt;

import lombok.RequiredArgsConstructor;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;
import java.util.Optional;

@RequiredArgsConstructor
@Configuration
public class MqttConfig {

    private final MqttProperties mqttProperties;

    @Bean
    public MqttClient mqttClient() throws MqttException {
        String clientId = String.join("", mqttProperties.getClientId(), String.valueOf(new Date().getTime()));
        // 初始化客户端
        MqttClient client = new MqttClient(mqttProperties.getTcpUrl(), clientId, new MemoryPersistence());
        // 配置客户端连接参数
        MqttConnectOptions options = new MqttConnectOptions();
        //是否清空session，设置false表示服务器会保留客户端的连接记录（订阅主题，qos）,客户端重连之后能获取到服务器在客户端断开连接期间推送的消息
        //设置为true表示每次连接服务器都是以新的身份
        options.setCleanSession(true);
        // 配置用户名和密码
        options.setUserName(mqttProperties.getUsername());
        options.setPassword(mqttProperties.getPassword().toCharArray());
        // 设置超时时间，单位为秒
        options.setConnectionTimeout(Optional.ofNullable(mqttProperties.getTimeout()).orElse(60));
        // 设置心跳时间 单位为秒，表示服务器每隔 1.5*30秒的时间向客户端发送心跳判断客户端是否在线
        options.setKeepAliveInterval(Optional.ofNullable(mqttProperties.getKeepAlive()).orElse(30));
        // 设置遗嘱消息的话题，若客户端和服务器之间的连接意外断开，服务器将发布客户端的遗嘱信息
        options.setWill("mqtt客户端", (mqttProperties.getClientId() + "与服务器断开连接").getBytes(), 0, false);
        // 设置是否自动重连
        options.setAutomaticReconnect(true);
        // 执行连接
        client.connect(options);
        //返回Mqtt连接对象
        return client;
    }

}
