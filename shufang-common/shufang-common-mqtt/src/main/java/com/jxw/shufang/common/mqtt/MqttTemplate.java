package com.jxw.shufang.common.mqtt;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@RequiredArgsConstructor
@Slf4j
@Service
public class MqttTemplate implements MqttCallbackExtended {

    private final MqttClient mqttClient;
    private final MqttProperties mqttProperties;

    @PostConstruct
    public void init() throws MqttException {
        mqttClient.setCallback(this);
        mqttClient.subscribe(mqttProperties.getTopic());
    }

    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("【MQTT服务器连接】连接成功....服务器：{}，是否重连：{}", serverURI, reconnect);
    }

    @Override
    public void connectionLost(Throwable throwable) {
        log.error("【MQTT服务器连接】连接断开...", throwable);
    }

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) {
        log.info("【MQTT消息处理】接收到消息----------------------------------");
        log.info("【MQTT消息处理】消息主题：{}", topic);
        log.info("【MQTT消息处理】消息Qos：{}", mqttMessage.getQos());
        log.info("【MQTT消息处理】消息内容：{}", new String(mqttMessage.getPayload()));
        log.info("【MQTT消息处理】是否为保留消息：{}", mqttMessage.isRetained());
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        try {
            MqttMessage message = iMqttDeliveryToken.getMessage();
            log.info("【MQTT消息处理】发送消息：{}, 成功：{}...", message, iMqttDeliveryToken.isComplete());
        } catch (MqttException e) {
            log.error("【MQTT消息处理】消息到达MQTT服务器失败...", e);
        }
    }

    public boolean publish(String message) {
        return publish(mqttProperties.getTopic(), message);
    }

    public boolean publish(String message, int qos) {
        return publish(mqttProperties.getTopic(), message, qos);
    }

    public boolean publish(String message, int qos, boolean retained) {
        return publish(mqttProperties.getTopic(), message, qos, retained);
    }

    public boolean publish(String topic, String message) {
        return publish(topic, message, mqttProperties.getQos());
    }

    public boolean publish(String topic, String message, int qos) {
        return publish(topic, message, qos, false);
    }

    public boolean publish(String topic, String message, int qos, boolean retained) {
        MqttMessage mqttMessage = new MqttMessage();
        MqttTopic mqttTopic = mqttClient.getTopic(topic);
        mqttMessage.setPayload(message.getBytes());
        mqttMessage.setQos(qos);
        mqttMessage.setRetained(retained);
        MqttDeliveryToken token;
        try {
            token = mqttTopic.publish(mqttMessage);
            token.waitForCompletion();
            return true;
        } catch (MqttException e) {
            log.error("mqtt消息发送失败{}_{}", topic, message, e);
            return false;
        }
    }

}
