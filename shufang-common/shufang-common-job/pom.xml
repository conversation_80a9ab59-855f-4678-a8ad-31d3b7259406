<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-common-job</artifactId>

    <description>
        shufang-common-job 定时任务
    </description>

	<dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <!-- 服务发现组件 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>

        <!--PowerJob-->
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>powerjob-remote-impl-akka</artifactId>
                    <groupId>tech.powerjob</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-official-processors</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-core</artifactId>
        </dependency>

    </dependencies>
</project>
