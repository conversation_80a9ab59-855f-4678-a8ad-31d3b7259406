package com.jxw.shufang.tencentFace.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "tencent.cloud.face-detection")
public class FaceDetectionProperties {

    private Credentials credentials = new Credentials();
    private String region = "ap-shanghai";
    private PersonGroup personGroup = new PersonGroup();

    @Data
    public static class Credentials {
        private String secretId = "AKIDe9PPAgww84kAKDrFNF8wVOAi28yZw50g";
        private String secretKey = "fDVXJqxpSr2yg9PRDk2VnrkSwCG7kBQS";
    }

    @Data
    public static class PersonGroup {
        private String groupName = "学王书房会员库";
        private String groupId = "xuewangshufang";
    }
}
