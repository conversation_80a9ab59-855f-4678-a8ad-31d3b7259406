package com.jxw.shufang.tencentFace;

import com.jxw.shufang.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;

import com.jxw.shufang.tencentFace.config.FaceDetectionProperties;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.iai.v20200303.IaiClient;
import com.tencentcloudapi.iai.v20200303.models.*;

/**
 * @author: cyj
 * @date: 2025/4/17
 */
@AllArgsConstructor
public class FaceDetectionClient {

    private FaceDetectionProperties faceDetectionProperties;

    /**
     * 查找人脸
     *
     * @return
     * @throws TencentCloudSDKException
     */
    public SearchPersonsResponse searchFace(String faceUrl, String faceBase64) throws TencentCloudSDKException {
        SearchPersonsRequest req = new SearchPersonsRequest();
        String[] groupIds1 = {faceDetectionProperties.getPersonGroup().getGroupId()};
        req.setGroupIds(groupIds1);
        if (StringUtils.isNotBlank(faceBase64)) {
            req.setImage(faceBase64);
        } else {
            req.setUrl(faceUrl);
        }
        // 图片质量控制，2: 一般的质量要求，图像存在偏亮，偏暗，模糊或一般模糊，眉毛遮挡，脸颊遮挡，下巴遮挡，至少其中三种的情况；
        req.setQualityControl(3L);
        // 出参Score中，只有超过FaceMatchThreshold值的结果才会返回。默认为0。
        req.setFaceMatchThreshold(60f);
        return getIaiClient().SearchPersons(req);
    }

    /**
     * 添加人员库
     *
     * @param groupName
     * @param groupId
     * @return
     * @throws Exception
     */
    public CreateGroupResponse createGroup(String groupName, String groupId) throws Exception {
        CreateGroupRequest createGroupRequest = new CreateGroupRequest();
        createGroupRequest.setGroupName(groupName);
        createGroupRequest.setGroupId(groupId);
        return getIaiClient().CreateGroup(createGroupRequest);
    }

    /**
     * 获取人员库列表
     *
     * @return
     * @throws TencentCloudSDKException
     */
    public GetGroupListResponse getGroupList() throws TencentCloudSDKException {
        return getIaiClient().GetGroupList(new GetGroupListRequest());
    }

    /**
     * 创建人员
     *
     * @param personId
     * @param personName
     * @param url
     * @return
     * @throws TencentCloudSDKException
     */
    public CreatePersonResponse createPerson(String personId, String personName, String url)
        throws TencentCloudSDKException {
        CreatePersonRequest createPersonRequest = new CreatePersonRequest();
        createPersonRequest.setGroupId(faceDetectionProperties.getPersonGroup().getGroupId());
        createPersonRequest.setPersonId(personId);
        createPersonRequest.setPersonName(personName);
        createPersonRequest.setUrl(url);
        // 此参数用于控制判断 Image 或 Url 中图片包含的人脸，是否在人员库中已有疑似的同一人。2: 一般的同一人判断要求（千一误识别率）；
        createPersonRequest.setUniquePersonControl(2L);
        // 图片质量控制。2: 一般的质量要求，图像存在偏亮，偏暗，模糊或一般模糊，眉毛遮挡，脸颊遮挡，下巴遮挡，至少其中三种的情况；
        createPersonRequest.setQualityControl(2L);
        return getIaiClient().CreatePerson(createPersonRequest);
    }

    /**
     * 删除人员
     *
     * @param personId
     * @return
     * @throws TencentCloudSDKException
     */
    public DeletePersonFromGroupResponse deletePersonFromGroup(String personId) throws TencentCloudSDKException {
        DeletePersonFromGroupRequest createPersonRequest = new DeletePersonFromGroupRequest();
        createPersonRequest.setGroupId(faceDetectionProperties.getPersonGroup().getGroupId());
        createPersonRequest.setPersonId(personId);
        return getIaiClient().DeletePersonFromGroup(createPersonRequest);
    }

    /**
     * 人脸验证
     *
     * @param personId
     * @param faceUrl
     * @return
     * @throws Exception
     */
    public VerifyFaceResponse verifyFace(String personId, String faceUrl) throws Exception {
        VerifyFaceRequest verifyFaceRequest = new VerifyFaceRequest();
        verifyFaceRequest.setPersonId(personId);
        verifyFaceRequest.setUrl(faceUrl);
        return getIaiClient().VerifyFace(verifyFaceRequest);
    }

    /**
     * 获取腾讯云人脸识别client
     *
     * @return
     */
    private IaiClient getIaiClient() {
        // 密钥
        Credential cred = new Credential(faceDetectionProperties.getCredentials().getSecretId(),
            faceDetectionProperties.getCredentials().getSecretKey());
        // 初始化请求client
        return new IaiClient(cred, faceDetectionProperties.getRegion());
    }

}
