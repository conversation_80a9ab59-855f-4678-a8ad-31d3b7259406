<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-common-websocket</artifactId>

    <description>
        shufang-common-websocket 模块
    </description>

    <dependencies>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-satoken</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
    </dependencies>
</project>
