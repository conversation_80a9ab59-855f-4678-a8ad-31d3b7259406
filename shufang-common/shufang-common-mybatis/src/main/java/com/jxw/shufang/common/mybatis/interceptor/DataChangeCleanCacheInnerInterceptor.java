package com.jxw.shufang.common.mybatis.interceptor;


import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.config.properties.MysqlCacheCleanProperties;
import com.jxw.shufang.common.redis.utils.RedisUtils;

import java.sql.Connection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DataChangeCleanCacheInnerInterceptor implements InnerInterceptor {

    private final MysqlCacheCleanProperties mysqlCacheCleanProperties;

    public DataChangeCleanCacheInnerInterceptor(MysqlCacheCleanProperties mysqlCacheCleanProperties) {
        this.mysqlCacheCleanProperties = mysqlCacheCleanProperties;
    }

    //private static final Map<String, String> TABLE_PK_MAP = new ConcurrentHashMap<>();

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        final BoundSql boundSql = mpSh.boundSql();
        if (sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
            PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
            String tableName = "";
            try {
                Statement statement = JsqlParserGlobal.parse(mpBs.sql());
                if (statement instanceof Update update) {
                    tableName = update.getTable().getName();
                } else if (statement instanceof Delete delete) {
                    tableName = delete.getTable().getName();
                } else {
                    return;
                }
                if (mysqlCacheCleanProperties.getExcludes().contains(tableName)) {
                    return;
                }
                String cacheKey = tableName2CacheKey(tableName);
                String idValue = getIdValue(tableName, boundSql, statement);
//                cleanCache(cacheKey, idValue);
                //清除缓存
            } catch (JSQLParserException e) {
                throw new ServiceException(e.getMessage());
            }
        }
    }

    private void cleanCache(String cacheKey, String idValue) {
        //没有就清除全部缓存
        if (StringUtils.isBlank(idValue)){
            RedisUtils.deleteKeys(cacheKey);
            return;
        }
        RedisUtils.delCacheMapValue(cacheKey,idValue);
    }

    //private String getTablePk(String tableName) {
    //    if (TABLE_PK_MAP.containsKey(tableName)) {
    //        return TABLE_PK_MAP.get(tableName);
    //    }
    //    //如果没有，尝试从mybatis中的TableInfo中获取
    //    for (TableInfo tableInfo : TableInfoHelper.getTableInfos()) {
    //        if (tableName.equalsIgnoreCase(tableInfo.getTableName())) {
    //            Column pk = new Column(tableInfo.getKeyColumn());
    //            TABLE_PK_MAP.put(tableName, underlineToCamel(pk.getColumnName()));
    //            return TABLE_PK_MAP.get(tableName);
    //        }
    //    }
    //    //如果还是没有，尝试从数据库中获取
    //    SimpleMapper mapper = SpringUtils.getBean(SimpleMapper.class);
    //    String pk = mapper.selectTablePk(tableName);
    //    TABLE_PK_MAP.put(tableName, underlineToCamel(pk));
    //    return TABLE_PK_MAP.get(tableName);
    //}

    private String getPkColumnName(String tableName) {
        for (TableInfo tableInfo : TableInfoHelper.getTableInfos()) {
            if (tableName.equalsIgnoreCase(tableInfo.getTableName())) {
                return tableInfo.getKeyProperty();
            }
        }
        return null;
    }

    private String tableName2CacheKey(String tableName) {
        tableName = tableName.replace("`", "");
        return underlineToCamel(tableName);
    }

    /**
     * 下划线转驼峰
     */
    private String underlineToCamel(String str) {
        //下划线转驼峰
        Pattern linePattern = Pattern.compile("_(\\w)");
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private String getIdValue(String tableName, BoundSql updateSql, Statement statement) {
        String pkColumnName = getPkColumnName(tableName);
        if (StringUtils.isBlank(pkColumnName)){
            return null;
        }
        MetaObject metaObject = SystemMetaObject.forObject(updateSql.getParameterObject());
        for (ParameterMapping parameterMapping : updateSql.getParameterMappings()) {
            String property = "";
            if (parameterMapping.getProperty().contains(".")){
                property = parameterMapping.getProperty().split("\\.")[1];
            }else {
                property = parameterMapping.getProperty();
            }

            String propertyName = parameterMapping.getProperty();
            if (property.equals(pkColumnName)) {
                try {
                    return String.valueOf(metaObject.getValue(propertyName));
                } catch (Exception e) {
                    try {
                        return String.valueOf(metaObject.getOriginalObject());
                    }catch (Exception e1){
                        throw new ServiceException(e1.getMessage());
                    }
                }
            }
        }
        return null;
    }



}


