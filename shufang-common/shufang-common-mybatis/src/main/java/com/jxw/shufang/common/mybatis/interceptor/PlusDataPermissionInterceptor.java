package com.jxw.shufang.common.mybatis.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.config.properties.DataPermissionProperties;
import com.jxw.shufang.common.mybatis.handler.PlusDataPermissionHandler;
import com.jxw.shufang.common.satoken.utils.LoginHelper;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

/**
 * 数据权限拦截器
 *

 * @version 3.5.0
 */
@Slf4j
public class PlusDataPermissionInterceptor extends JsqlParserSupport implements InnerInterceptor {

    private final PlusDataPermissionHandler dataPermissionHandler = new PlusDataPermissionHandler();
    /**
     * 无效注解方法缓存用于快速返回
     */
    private final Set<String> invalidCacheSet = new ConcurrentHashSet<>();

    private final DataPermissionProperties dataPermissionProperties;

    public PlusDataPermissionInterceptor() {
        this.dataPermissionProperties = SpringUtils.getBean(DataPermissionProperties.class);
    }

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        // 检查忽略注解
        if (InterceptorIgnoreHelper.willIgnoreDataPermission(ms.getId())) {
            return;
        }
        // 检查是否无效 无数据权限注解
        if (invalidCacheSet.contains(ms.getId())) {
            return;
        }


        DataColumn[] dataColumns = dataPermissionHandler.findDataColumnAnnotation(ms.getId());
        BranchColumn branchColumn = dataPermissionHandler.findBranchColumnAnnotation(ms.getId());
        if (ArrayUtil.isEmpty(dataColumns) && branchColumn == null) {
            invalidCacheSet.add(ms.getId());
            return;
        }

        // 不需要数据权限的user_type
        if (CollUtil.isNotEmpty(dataPermissionProperties.getExcludesUserType())){
            try {
                UserType userType = LoginHelper.getUserType();
                if (dataPermissionProperties.getExcludesUserType().contains(userType.getUserType())) {
                    return;
                }
            }catch (Exception e){
                log.error("获取用户类型失败", e);
                // ignore
            }
        }

        //特殊的忽略数据权限
        if (LoginHelper.isIgnoreDataScope()){
            log.debug("当前用户忽略数据权限");
            return;
        }

        // 解析 sql 分配对应方法，这里看了底层代码，貌似是会走 JsqlParserSupport的processSelect
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        mpBs.sql(parserSingle(mpBs.sql(), ms.getId()));
    }

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        if (sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
            if (InterceptorIgnoreHelper.willIgnoreDataPermission(ms.getId())) {
                return;
            }
            // 检查是否无效 无数据权限注解
            if (invalidCacheSet.contains(ms.getId())) {
                return;
            }
            DataColumn[] dataColumns = dataPermissionHandler.findDataColumnAnnotation(ms.getId());
            BranchColumn branchColumn = dataPermissionHandler.findBranchColumnAnnotation(ms.getId());
            if (ArrayUtil.isEmpty(dataColumns) && branchColumn == null) {
                invalidCacheSet.add(ms.getId());
                return;
            }
            PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
            mpBs.sql(parserMulti(mpBs.sql(), ms.getId()));
        }
    }

    @Override
    protected void processSelect(Select select, int index, String sql, Object obj) {
        SelectBody selectBody = select.getSelectBody();
        if (selectBody instanceof PlainSelect plainSelect) {
            this.setWhere(plainSelect, (String) obj);
        } else if (selectBody instanceof SetOperationList setOperationList) {
            List<SelectBody> selectBodyList = setOperationList.getSelects();
            selectBodyList.forEach(s -> this.setWhere((PlainSelect) s, (String) obj));
        }
    }

    @Override
    protected void processUpdate(Update update, int index, String sql, Object obj) {
        Expression sqlSegment = dataPermissionHandler.getSqlSegment(update.getWhere(), (String) obj, false);
        if (null != sqlSegment) {
            update.setWhere(sqlSegment);
        }
    }

    @Override
    protected void processDelete(Delete delete, int index, String sql, Object obj) {
        Expression sqlSegment = dataPermissionHandler.getSqlSegment(delete.getWhere(), (String) obj, false);
        if (null != sqlSegment) {
            delete.setWhere(sqlSegment);
        }
    }

    /**
     * 设置 where 条件
     *
     * @param plainSelect       查询对象
     * @param mappedStatementId 执行方法id
     */
    protected void setWhere(PlainSelect plainSelect, String mappedStatementId) {
        Expression sqlSegment = dataPermissionHandler.getSqlSegment(plainSelect.getWhere(), mappedStatementId, true);
        if (null != sqlSegment) {
            plainSelect.setWhere(sqlSegment);
        }
    }

}

