package com.jxw.shufang.common.mybatis.service;

import cn.hutool.core.collection.CollUtil;
import com.jxw.shufang.branch.api.RemoteBranchService;
import com.jxw.shufang.branch.api.domain.vo.RemoteBranchVo;
import com.jxw.shufang.system.api.RemoteDataScopeService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据权限 实现
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *

 */
@Service("sdss")
public class SysDataScopeService {

    @DubboReference
    private RemoteDataScopeService remoteDataScopeService;
    @DubboReference
    private RemoteBranchService remoteBranchService;

    public String getRoleCustom(Long roleId) {
        return remoteDataScopeService.getRoleCustom(roleId);
    }

    public String getDeptAndChild(Long deptId) {
        return remoteDataScopeService.getDeptAndChild(deptId);
    }

    public String queryStaffBranchList(Long userId) {
        List<RemoteBranchVo> remoteBranchVos = remoteBranchService.queryStaffBranchList(userId);
        if (CollUtil.isEmpty(remoteBranchVos)) {
            return null;
        }
        List<Long> list = remoteBranchVos.stream().map(RemoteBranchVo::getCreateDept).toList();
        if (CollUtil.isEmpty(list)){
            return null;
        }
        return   remoteDataScopeService.getDeptList(list);
    }
}
