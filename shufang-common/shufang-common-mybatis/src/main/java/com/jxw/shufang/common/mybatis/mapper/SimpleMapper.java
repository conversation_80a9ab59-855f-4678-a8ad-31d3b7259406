package com.jxw.shufang.common.mybatis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

@Mapper
public interface SimpleMapper extends BaseMapper<Map> {
    @Select("SELECT COLUMN_NAME  FROM information_schema.COLUMNS WHERE  TABLE_NAME = #{tableName} AND COLUMN_KEY = 'PRI' limit 1")
    String selectTablePk(String tableName);
}
