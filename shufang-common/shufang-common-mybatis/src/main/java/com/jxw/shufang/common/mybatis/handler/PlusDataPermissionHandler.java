package com.jxw.shufang.common.mybatis.handler;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.core.utils.SpringUtils;
import com.jxw.shufang.common.core.utils.StreamUtils;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.mybatis.annotation.BranchColumn;
import com.jxw.shufang.common.mybatis.annotation.DataColumn;
import com.jxw.shufang.common.mybatis.annotation.DataPermission;
import com.jxw.shufang.common.mybatis.enums.DataScopeType;
import com.jxw.shufang.common.mybatis.helper.DataPermissionHelper;
import com.jxw.shufang.common.satoken.utils.LoginHelper;
import com.jxw.shufang.system.api.model.LoginUser;
import com.jxw.shufang.system.api.model.RoleDTO;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.expression.BeanResolver;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 数据权限过滤
 *

 * @version 3.5.0
 */
@Slf4j
public class PlusDataPermissionHandler {

    /**
     * 方法或类(名称) 与 注解的映射关系缓存
     */
    private final Map<String, DataPermission> dataPermissionCacheMap = new ConcurrentHashMap<>();

    /**
     * 方法(名称) 与 门店权限注解的映射关系缓存
     */
    private final Map<String, BranchColumn> branchColumnCacheMap = new ConcurrentHashMap<>();

    /**
     * spel 解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParserContext parserContext = new TemplateParserContext();
    /**
     * bean解析器 用于处理 spel 表达式中对 bean 的调用
     */
    private final BeanResolver beanResolver = new BeanFactoryResolver(SpringUtils.getBeanFactory());


    public Expression getSqlSegment(Expression where, String mappedStatementId, boolean isSelect) {
        DataColumn[] dataColumns = findDataColumnAnnotation(mappedStatementId);
        BranchColumn branchColumnAnnotation = findBranchColumnAnnotation(mappedStatementId);
        LoginUser currentUser = DataPermissionHelper.getVariable("user");

        if (ObjectUtil.isNull(currentUser)) {
            currentUser = LoginHelper.getLoginUser();
            DataPermissionHelper.setVariable("user", currentUser);
        }
        // 如果是超级管理员或租户管理员，则不过滤数据
        if (LoginHelper.isSuperAdmin() || LoginHelper.isTenantAdmin()) {
            return where;
        }
        String dataFilterSql = buildDataFilter(dataColumns, branchColumnAnnotation, isSelect);
        if (StringUtils.isBlank(dataFilterSql)) {
            return where;
        }
        try {
            Expression expression = CCJSqlParserUtil.parseExpression(dataFilterSql);
            // 数据权限使用单独的括号 防止与其他条件冲突
            Parenthesis parenthesis = new Parenthesis(expression);
            if (ObjectUtil.isNotNull(where)) {
                return new AndExpression(where, parenthesis);
            } else {
                return parenthesis;
            }
        } catch (JSQLParserException e) {
            throw new ServiceException("数据权限解析异常 => " + e.getMessage());
        }
    }

    /**
     * 构造数据过滤sql
     */
    private String buildDataFilter(DataColumn[] dataColumns, BranchColumn branchColumn, boolean isSelect) {
        // 更新或删除需满足所有条件
        String joinStr = isSelect ? " OR " : " AND ";
        LoginUser user = DataPermissionHelper.getVariable("user");
        //表达式解析上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setBeanResolver(beanResolver);
        DataPermissionHelper.getContext().forEach(context::setVariable);
        Set<String> conditions = new HashSet<>();
        for (RoleDTO role : user.getRoles()) {
            user.setRoleId(role.getRoleId());
            // 获取角色权限泛型
            DataScopeType type = DataScopeType.findCode(role.getDataScope());
            if (ObjectUtil.isNull(type)) {
                throw new ServiceException("角色数据范围异常 => " + role.getDataScope());
            }
            // 全部数据权限直接返回
            if (type == DataScopeType.ALL) {
                return "";
            }
            boolean isSuccess = false;
            // 如果是门店管理员，执行branchColumn，不执行dataColumn
            boolean branchAdmin = user.getIsBranchAdmin();
            Boolean isExecutiveStoreManager = user.getIsExecutiveStoreManager();
            if (Boolean.TRUE.equals(isExecutiveStoreManager)) {
                type = DataScopeType.SHOP;
            }
            Long selectDeptId = user.getSelectDeptId();
            Long selectRoleId = user.getSelectRoleId();
            //是门店管理员，但是没有选角登录，也不用使用门店权限注解
            if ((branchAdmin||isExecutiveStoreManager) && branchColumn != null && selectDeptId!=null && StringUtils.isNotBlank(branchColumn.key()) && StringUtils.isNotBlank(branchColumn.value())) {
                context.setVariable(branchColumn.key(), branchColumn.value());
                String sqlTemplate = type.getSqlTemplate();
                if (sqlTemplate.contains("#{#user.deptId}")) {
                    sqlTemplate = sqlTemplate.replace("#{#user.deptId}", "'" + selectDeptId + "'");
                }
                if (sqlTemplate.contains("#{@sdss.getRoleCustom( #user.roleId )}")) {
                    sqlTemplate = sqlTemplate.replace("#{@sdss.getRoleCustom( #user.roleId )}", "'" + selectRoleId + "'");
                }
                if (sqlTemplate.contains("#{@sdss.getDeptAndChild( #user.deptId )}")) {
                    sqlTemplate = sqlTemplate.replace("#{@sdss.getDeptAndChild( #user.deptId )}", "'" + selectDeptId + "'");
                }
                if (sqlTemplate.contains("#user.deptId")){
                    sqlTemplate = sqlTemplate.replace("#user.deptId", "'" + selectDeptId + "'");
                }
                if (sqlTemplate.contains("#user.roleId")){
                    sqlTemplate = sqlTemplate.replace("#user.roleId", "'" + selectRoleId + "'");
                }
                if (sqlTemplate.contains("#{@sdss.queryStaffBranchList( #user.userId )}")){
                    sqlTemplate = sqlTemplate.replace("#{@sdss.queryStaffBranchList( #user.userId )}", "'" + selectDeptId + "'");
                }
                String sql = parser.parseExpression(sqlTemplate, parserContext).getValue(context, String.class);
                conditions.add(joinStr + sql);
                isSuccess = true;
            } else {
                for (DataColumn dataColumn : dataColumns) {
                    if (dataColumn.key().length != dataColumn.value().length) {
                        throw new ServiceException("角色数据范围异常 => key与value长度不匹配");
                    }
                    // 不包含 key 变量 则不处理
                    if (!StringUtils.containsAny(type.getSqlTemplate(),
                        Arrays.stream(dataColumn.key()).map(key -> "#" + key).toArray(String[]::new)
                    )) {
                        continue;
                    }
                    // 设置注解变量 key 为表达式变量 value 为变量值
                    for (int i = 0; i < dataColumn.key().length; i++) {
                        context.setVariable(dataColumn.key()[i], dataColumn.value()[i]);
                    }

                    // 解析sql模板并填充
                    String sql = parser.parseExpression(type.getSqlTemplate(), parserContext).getValue(context, String.class);
                    conditions.add(joinStr + sql);
                    isSuccess = true;
                }
            }
            // 未处理成功则填充兜底方案
            if (!isSuccess && StringUtils.isNotBlank(type.getElseSql())) {
                conditions.add(joinStr + type.getElseSql());
            }
        }

        if (CollUtil.isNotEmpty(conditions)) {
            String sql = StreamUtils.join(conditions, Function.identity(), "");
            return sql.substring(joinStr.length());
        }
        return "";
    }


    public DataColumn[] findDataColumnAnnotation(String mappedStatementId) {
        StringBuilder sb = new StringBuilder(mappedStatementId);
        int index = sb.lastIndexOf(".");
        String clazzName = sb.substring(0, index);
        String methodName = sb.substring(index + 1, sb.length());
        Class<?> clazz;
        try {
            clazz = ClassUtil.loadClass(clazzName);
        } catch (Exception e) {
            return null;
        }
        List<Method> methods = Arrays.stream(ClassUtil.getDeclaredMethods(clazz))
            .filter(method -> method.getName().equals(methodName)).toList();
        DataPermission dataPermission;
        // 获取方法注解
        for (Method method : methods) {
            dataPermission = dataPermissionCacheMap.get(mappedStatementId);
            if (ObjectUtil.isNotNull(dataPermission)) {
                return dataPermission.value();
            }
            if (AnnotationUtil.hasAnnotation(method, DataPermission.class)) {
                dataPermission = AnnotationUtil.getAnnotation(method, DataPermission.class);
                dataPermissionCacheMap.put(mappedStatementId, dataPermission);
                return dataPermission.value();
            }
        }
        dataPermission = dataPermissionCacheMap.get(clazz.getName());
        if (ObjectUtil.isNotNull(dataPermission)) {
            return dataPermission.value();
        }
        // 获取类注解
        if (AnnotationUtil.hasAnnotation(clazz, DataPermission.class)) {
            dataPermission = AnnotationUtil.getAnnotation(clazz, DataPermission.class);
            dataPermissionCacheMap.put(clazz.getName(), dataPermission);
            return dataPermission.value();
        }
        return null;
    }

    public BranchColumn findBranchColumnAnnotation(String mappedStatementId) {
        StringBuilder sb = new StringBuilder(mappedStatementId);
        int index = sb.lastIndexOf(".");
        String clazzName = sb.substring(0, index);
        String methodName = sb.substring(index + 1, sb.length());
        Class<?> clazz;
        try {
            clazz = ClassUtil.loadClass(clazzName);
        } catch (Exception e) {
            return null;
        }
        List<Method> methods = Arrays.stream(ClassUtil.getDeclaredMethods(clazz))
            .filter(method -> method.getName().equals(methodName)).toList();
        BranchColumn branchColumn;
        // 获取方法注解
        for (Method method : methods) {
            branchColumn = branchColumnCacheMap.get(mappedStatementId);
            if (ObjectUtil.isNotNull(branchColumn)) {
                return branchColumn;
            }
            if (AnnotationUtil.hasAnnotation(method, BranchColumn.class)) {
                branchColumn = AnnotationUtil.getAnnotation(method, BranchColumn.class);
                branchColumnCacheMap.put(mappedStatementId, branchColumn);
                return branchColumn;
            }
        }
        return branchColumnCacheMap.get(clazz.getName());
    }


}
