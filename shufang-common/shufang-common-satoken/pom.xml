<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jxw.shufang</groupId>
        <artifactId>shufang-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shufang-common-satoken</artifactId>

    <description>
        shufang-common-satoken
    </description>

    <dependencies>

        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-core</artifactId>
        </dependency>

        <!-- Sa-Token 整合 jwt -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-jwt</artifactId>
            <version>${satoken.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
        </dependency>

        <!-- jxw Api System -->
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-api-system</artifactId>
        </dependency>

        <!-- jxw Common Redis-->
        <dependency>
            <groupId>com.jxw.shufang</groupId>
            <artifactId>shufang-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>
