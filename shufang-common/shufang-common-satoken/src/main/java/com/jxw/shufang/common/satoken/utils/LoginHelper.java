package com.jxw.shufang.common.satoken.utils;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.model.SaStorage;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.jxw.shufang.common.core.enums.CrmRoleKey;
import com.jxw.shufang.system.api.model.RoleDTO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import com.jxw.shufang.common.core.constant.TenantConstants;
import com.jxw.shufang.common.core.constant.UserConstants;
import com.jxw.shufang.common.core.enums.UserType;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.system.api.model.LoginUser;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;

import static com.jxw.shufang.common.core.enums.CrmRoleKey.*;

/**
 * 登录鉴权助手
 * <p>
 * user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app
 * deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios
 * 可以组成 用户类型与设备类型多对多的 权限灵活控制
 * <p>
 * 多用户体系 针对 多种用户类型 但权限控制不一致
 * 可以组成 多用户类型表与多设备类型 分别控制权限
 *

 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";
    public static final String TENANT_KEY = "tenantId";
    public static final String USER_KEY = "userId";
    //用户登录时选择的部门ID(业务需求)
    public static final String SELECT_DEPT_KEY = "deptIdSelect";
    public static final String DEPT_KEY = "deptId";

    //用户登录时选择的角色ID(业务需求)
    public static final String SELECT_ROLE_KEY = "roleIdSelect";
    public static final String CLIENT_KEY = "clientid";
    public static final String TENANT_ADMIN_KEY = "isTenantAdmin";
    public static final String BRANCH_ID = "branchId";//当登录者为门店管理员（选角）或者学员时，该值为门店ID
    public static final String BRANCH_ID_LIST = "branchIdList"; //当登录者为门店管理员时，未选择门店时，该值为旗下所有门店ID的集合
    public static final String STUDENT_ID = "studentId";
    public static final String IS_BRANCH_ADMIN = "isBranchAdmin";
    public static final String IS_BRANCH_STAFF = "isBranchStaff";
    public static final String SELECT_ROLE_SIGN_KEY = "selectRoleSignKey";
    public static final String IGNORE_DATA_SCOPE = "ignoreDataScope"; //是否忽略数据权限
    public static final String BRANCH_STAFF_ID = "branchStaffId";//门店员工ID
    public static final String IS_CONSULTANT = "isConsultant"; //是否是会员顾问
    public static final String IS_SALES_CONSULTANT = "isSalesConsultant"; //是否是销售顾问
    public static final String IS_EXECUTIVE_STORE_MANAGER = "isExecutiveStoreManager"; // 是否是门店执行店长

    //第三方标记
    public static final String WECHAT_OPEN_ID = "wechatOpenId"; //微信openId

    public static final String ROLES = "roles";
    /**
     * 登录系统 基于 设备类型
     * 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     * @param model     配置参数
     */
    public static void login(LoginUser loginUser, SaLoginModel model) {
        SaStorage storage = SaHolder.getStorage();
        storage.set(LOGIN_USER_KEY, loginUser);
        storage.set(TENANT_KEY, loginUser.getTenantId());
        storage.set(USER_KEY, loginUser.getUserId());
        storage.set(DEPT_KEY, loginUser.getDeptId());
        storage.set(SELECT_DEPT_KEY, loginUser.getSelectDeptId());
        storage.set(SELECT_ROLE_KEY, loginUser.getSelectRoleId());
        storage.set(BRANCH_ID, loginUser.getBranchId());
        storage.set(STUDENT_ID, loginUser.getStudentId());
        storage.set(IS_BRANCH_ADMIN, loginUser.getIsBranchAdmin());
        storage.set(SELECT_ROLE_SIGN_KEY, loginUser.getSelectRoleSignKey());
        storage.set(IS_BRANCH_STAFF, loginUser.getIsBranchStaff());
        storage.set(IGNORE_DATA_SCOPE, loginUser.getIgnoreDataScope());
        storage.set(BRANCH_STAFF_ID, loginUser.getStaffId());
        storage.set(BRANCH_ID_LIST, loginUser.getBranchIdList());
        storage.set(IS_CONSULTANT, loginUser.getIsConsultant());
        storage.set(IS_SALES_CONSULTANT, loginUser.getIsSaleConsultant());
        storage.set(IS_EXECUTIVE_STORE_MANAGER, loginUser.getIsExecutiveStoreManager());
        storage.set(WECHAT_OPEN_ID,loginUser.getOpenId());
        storage.set(ROLES, loginUser.getRoles());

        model = ObjectUtil.defaultIfNull(model, new SaLoginModel());
        StpUtil.login(loginUser.getLoginId(),
            model.setExtra(TENANT_KEY, loginUser.getTenantId())
                .setExtra(USER_KEY, loginUser.getUserId())
                .setExtra(DEPT_KEY, loginUser.getDeptId())
                .setExtra(SELECT_ROLE_KEY, loginUser.getSelectRoleId())
                .setExtra(SELECT_DEPT_KEY, loginUser.getSelectDeptId())
                .setExtra(BRANCH_ID, loginUser.getBranchId())
                .setExtra(STUDENT_ID, loginUser.getStudentId())
                .setExtra(IS_BRANCH_ADMIN, loginUser.getIsBranchAdmin())
                .setExtra(SELECT_ROLE_SIGN_KEY, loginUser.getSelectRoleSignKey())
                .setExtra(IS_BRANCH_STAFF, loginUser.getIsBranchStaff())
                .setExtra(IGNORE_DATA_SCOPE, loginUser.getIgnoreDataScope())
                .setExtra(BRANCH_STAFF_ID, loginUser.getStaffId())
                .setExtra(BRANCH_ID_LIST, loginUser.getBranchIdList())
                .setExtra(IS_CONSULTANT, loginUser.getIsConsultant())
                .setExtra(IS_SALES_CONSULTANT, loginUser.getIsSaleConsultant())
                .setExtra(IS_EXECUTIVE_STORE_MANAGER, loginUser.getIsExecutiveStoreManager())
                .setExtra(WECHAT_OPEN_ID,loginUser.getOpenId())
                .setExtra(ROLES, loginUser.getRoles())
        );
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.updateTimeout(model.getTimeout());
        tokenSession.set(LOGIN_USER_KEY, loginUser);
        // loginSession 供oauth2根据登录id获取信息
        SaSession loginSession = StpUtil.getSessionByLoginId(loginUser.getLoginId());
        loginSession.set(TENANT_KEY, loginUser.getTenantId()).set(USER_KEY, loginUser.getUserId());
        if (loginUser.getStudentId() != null) {
            loginSession.set(STUDENT_ID, loginUser.getStudentId());
        }
    }


    /**
     * 获取用户(多级缓存)
     */
    public static LoginUser getLoginUser() {
        return (LoginUser) getStorageIfAbsentSet(LOGIN_USER_KEY, () -> {
            SaSession session = StpUtil.getTokenSession();
            if (ObjectUtil.isNull(session)) {
                return null;
            }
            return session.get(LOGIN_USER_KEY);
        });
    }

    /**
     * 获取用户基于token
     */
    public static LoginUser getLoginUser(String token) {
        SaSession session = StpUtil.getTokenSessionByToken(token);
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (LoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取用户id
     */
    public static Long getUserId() {
        return Convert.toLong(getExtra(USER_KEY));
    }

    /**
     * 获取选中的dept id
     *
     *
     * @date 2024/02/21 10:27:42
     */
    public static Long getSelectDeptId() {
        Object extra = getExtra(SELECT_DEPT_KEY);
        if (ObjectUtil.isNull(extra)) {
            return null;
        }
        return Convert.toLong(extra);
    }

    /**
     * 获取选中的role id
     */
    public static Long getSelectRoleId() {
        Object extra = getExtra(SELECT_ROLE_KEY);
        if (ObjectUtil.isNull(extra)) {
            return null;
        }
        return Convert.toLong(extra);
    }

    public static Long getStudentId() {
        Object extra = getExtra(STUDENT_ID);
        if (ObjectUtil.isNull(extra)) {
            throw new ServiceException("登录用户不是会员");
        }
        return Convert.toLong(extra);
    }

    /**
     * 获取租户ID
     */
    public static String getTenantId() {
        return Convert.toStr(getExtra(TENANT_KEY));
    }

    /**
     * 获取部门ID
     */
    public static Long getDeptId() {
        return Convert.toLong(getExtra(DEPT_KEY));
    }

    /**
     * 获取门店Id
     */
    public static Long getBranchId() {
        return Convert.toLong(getExtra(BRANCH_ID));
    }

    /**
     * 获取微信openId
     */
    public static String getWechatOpenId() {
        return Convert.toStr(getExtra(WECHAT_OPEN_ID));
    }

    /**
     * 获取门店ID集合(当登录者为门店管理员时，未选择门店时，该值为旗下所有门店ID的集合)
     */
    public static List<Long> getBranchIdList() {
        Object extra = getExtra(BRANCH_ID_LIST);
        if (ObjectUtil.isNull(extra)) {
            return null;
        }
        return Convert.toList(Long.class, extra);

    }

    private static Object getExtra(String key) {
        return getStorageIfAbsentSet(key, () -> StpUtil.getExtra(key));
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return getLoginUser().getUsername();
    }

    /**
     * 获取用户类型
     */
    public static UserType getUserType() {
        String loginId = StpUtil.getLoginIdAsString();
        return UserType.getUserType(loginId);
    }

    /**
     * 是否为超级管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isSuperAdmin(Long userId) {
        return UserConstants.SUPER_ADMIN_ID.equals(userId);
    }

    public static boolean isSuperAdminRole(Long roleId) {
        return UserConstants.SUPER_ADMIN_ROLE_ID.equals(roleId);
    }

    public static boolean isSuperAdmin() {
        return isSuperAdmin(getUserId());
    }

    /**
     * 是否为超级管理员
     *
     * @param rolePermission 角色权限标识组
     * @return 结果
     */
    public static boolean isTenantAdmin(Set<String> rolePermission) {
        return rolePermission.contains(TenantConstants.TENANT_ADMIN_ROLE_KEY);
    }

    public static boolean isTenantAdmin() {
        try {
            Object value = getStorageIfAbsentSet(TENANT_ADMIN_KEY, () -> {
                return isTenantAdmin(getLoginUser().getRolePermission());
            });
            return Convert.toBool(value);
        } catch (Exception e) {
            return false;
        }

    }

    public static boolean isLogin() {
        return getLoginUser() != null;
    }

    public static Object getStorageIfAbsentSet(String key, Supplier<Object> handle) {
        try {
            Object obj = SaHolder.getStorage().get(key);
            if (ObjectUtil.isNull(obj)) {
                obj = handle.get();
                SaHolder.getStorage().set(key, obj);
            }
            return obj;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 当前的登录态是否为门店管理员
     */
    public static boolean isBranchAdmin() {
        Object extra = getExtra(IS_BRANCH_ADMIN);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }

    /**
     * 当前的登录态是否为会员顾问
     */
    public static boolean isConsultant() {
        Object extra = getExtra(IS_CONSULTANT);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }

    /**
     * 当前的登录态是否为销售顾问
     */
    public static boolean isSaleConsultant() {
        Object extra = getExtra(IS_SALES_CONSULTANT);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }

    /**
     * 当前的登录态是否为销售顾问
     */
    public static boolean isExecutiveStoreManager() {
        Object extra = getExtra(IS_EXECUTIVE_STORE_MANAGER);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }

    /**
     * 当前的登录态是否为门店员工，注意，当员工没有角色时，这里不会生效
     *
     * @return
     */
    public static boolean isBranchStaff() {
        Object extra = getExtra(IS_BRANCH_STAFF);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }

    public static boolean isBranchUser() {
        return isBranchAdmin() || isBranchStaff();
    }

    public static boolean isStudent() {
        try {
            return getStudentId() != null;
        } catch (Exception e) {
            return false;
        }
    }

    public static Long getBranchStaffId() {
        Object extra = getExtra(BRANCH_STAFF_ID);
        if (ObjectUtil.isNull(extra)) {
            return null;
        }
        return Convert.toLong(extra);
    }

    public static String getSelectRoleSignKey() {
        Object extra = getExtra(SELECT_ROLE_SIGN_KEY);
        if (ObjectUtil.isNull(extra)) {
            return null;
        }
        return Convert.toStr(extra);
    }

    public static boolean isIgnoreDataScope() {
        Object extra = getExtra(IGNORE_DATA_SCOPE);
        if (ObjectUtil.isNull(extra)) {
            return false;
        }
        return Convert.toBool(extra);
    }
    /**
     * 获取用户的所有权限
     * @return
     */
    public static List<RoleDTO> getRoles() {
        return BeanUtil.copyToList((List<Object>)getExtra(ROLES), RoleDTO.class);
    }


    /**
     * 当前登录人是否是 公海人员
     * @return
     */
    public static boolean isPublicStaff() {
        if (null == getRoles()) {
            return false;
        }
        return getRoles().stream().filter(Objects::nonNull).anyMatch(item -> item.getRoleKey().equals(CrmRoleKey.PUBLIC_STAFF.name()));
    }

    /**
     * 当前登录人是否是店长
     * @return
     */
    public static boolean isAdministrator() {
        return getRoles().stream().anyMatch(item -> item.getRoleKey().equals(administrator.name()));
    }
    /**
     * 当前登录人是否是区域管理员
     * @return
     */
    public static boolean isRegion() {
        return getRoles().stream().anyMatch(item -> item.getRoleKey().equals(region.name()));
    }

    /**
     * 当前登录人是否是 销售人员
     * @return
     */
    public static boolean isWithLearnStaff() {
        return getRoles().stream().anyMatch(item -> item.getRoleKey().equals(WITH_LEARN_STAFF.name()));
    }

    /**
     * 当前登录人是否是 伴学人员
     * @return
     */
    public static boolean isFormalStaff() {
        return getRoles().stream().anyMatch(item -> item.getRoleKey().equals(FORMAL_STAFF.name()));
    }


}
