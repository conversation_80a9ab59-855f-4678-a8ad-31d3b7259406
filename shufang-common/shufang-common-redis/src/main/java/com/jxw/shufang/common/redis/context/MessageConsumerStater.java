package com.jxw.shufang.common.redis.context;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.utils.StringUtils;
import com.jxw.shufang.common.redis.annotation.MessageConsumer;
import com.jxw.shufang.common.redis.annotation.MessageHandler;
import com.jxw.shufang.common.redis.annotation.MessageListener;
import com.jxw.shufang.common.redis.handler.AbstractMessageHandler;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 启动配置，在项目启动时自动运行，主要用于消费者的注册
 */
@Component
@Slf4j
public class MessageConsumerStater implements ApplicationRunner, ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * topic -> handlerMode
     */
    public static final HashMap<String, MessageListener.HandlerMode> HANDLER_MODE_MAP = new HashMap<>();

    /**
     * 项目启动时，获取所有的 标注 MessageConsumer 注解的类 ，开启消息监听逻辑
     *
     * @param args ApplicationArguments
     */
    @Override
    public void run(ApplicationArguments args) {
        log.info("开始注册redis消息队列");
        // 获取所有的消息处理器
        Map<MessageListener.HandlerMode, AbstractMessageHandler> invokers = getMessageHandlers();

        // 遍历所有的消费者类，找到标注 MessageConsumer 注解的方法，并开启消息监听逻辑
        Map<String, Object> beansWithAnnotation = applicationContext.getBeansWithAnnotation(MessageConsumer.class);
        beansWithAnnotation.values().forEach(consumer -> {
            Method[] methods = consumer.getClass().getMethods();
            if (methods.length > 0) {
                Arrays.stream(methods).forEach(method -> startMessageListener(method, invokers));
            }
        });
    }

    /**
     * 找到对应的处理方式来处理消息的消费逻辑
     *
     * @param method     消费者方法 -> 本案例对应的是 MqConsumer 中的方法
     * @param handlerMap 所有的处理方式集合
     */
    private void startMessageListener(Method method, Map<MessageListener.HandlerMode, AbstractMessageHandler> handlerMap) {
        MessageListener listener = method.getAnnotation(MessageListener.class);
        if (null == listener) {
            return;
        }

        //找对应mode的中转处理器
        MessageListener.HandlerMode mode = listener.mode();
        String topic = StringUtils.isNotEmpty(listener.topic()) ? listener.topic() : listener.value();
        if (!HANDLER_MODE_MAP.containsKey(topic)) {
            HANDLER_MODE_MAP.put(topic, mode);
        }
        AbstractMessageHandler handler = handlerMap.get(mode);
        if (handler == null) {
            log.error("redis消息队列处理器未找到，mode: {}", mode);
            throw new RuntimeException("redis消息队列处理器未找到，mode:" + mode);
        }
        handler.invokeMessage(method);
    }

    /**
     * 获取消息处理程序
     */
    private Map<MessageListener.HandlerMode, AbstractMessageHandler> getMessageHandlers() {
        return applicationContext.getBeansWithAnnotation(MessageHandler.class).values().stream().collect(Collectors
            .toMap(k -> k.getClass().getAnnotation(MessageHandler.class).value(), k -> (AbstractMessageHandler) k));
    }


    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
