package com.jxw.shufang.common.redis.annotation;

import java.lang.annotation.*;

/**
 * 监听注解
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MessageListener {

    String value() default "";

    String topic() default "";

    HandlerMode mode() default HandlerMode.TOPIC;

    DataMode dataMode() default DataMode.SINGLE;

    /**
     * 数据大小，默认10条，仅在dataMode为MULTI时生效
     * @return
     */
    int dataSize() default 10;


    enum HandlerMode {
        /**
         * topic 模式，主题订阅 (基于 list 的 lPush/brPop 的订阅)
         * 支持持久化，性能也还行，但是在大数据量下可能会有性能问题，但是基本的使用场景下应该足够了
         */
        TOPIC()

        /**
         * 还有sub订阅模式和stream模式，先不考虑写
         * sub 模式，订阅模式 不支持持久化
         * stream 模式，流模式 支持持久化，但是需要redis 5.0以上版本，并且较为复杂，暂时不考虑
         */
    }

    enum DataMode {
        /**
         * 单条数据，默认模式，只处理收到的一条数据
         */
        SINGLE(),

        /**
         * 多条数据，处理收到的所有数据
         */
        MULTI()
    }
}
