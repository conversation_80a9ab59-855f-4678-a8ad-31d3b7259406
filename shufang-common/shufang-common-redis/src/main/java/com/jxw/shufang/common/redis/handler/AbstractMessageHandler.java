package com.jxw.shufang.common.redis.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharsetUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.redis.pojo.Message;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * 处理接口
 */
@Slf4j
public abstract class AbstractMessageHandler implements ApplicationContextAware {

    protected ApplicationContext applicationContext;

    protected RedissonConnectionFactory connectionFactory;

    protected  ObjectMapper objectMapper;

    /**
     * 通过反射 执行 Method 方法
     * @param method 方法
     * @param message 消息
     * @param bean bean 对象 本案例中是 RedisMqConsumer
     */
    protected void invokeMethod(Method method, Message<?> message, Object bean) {
        try {
            method.invoke(bean, message);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    /**
     * 通过反射 执行 Method 方法
     * @param method 方法
     * @param messageList 消息列表
     * @param bean bean 对象 本案例中是 RedisMqConsumer
     */
    protected void invokeMethod(Method method, List<Message<?>> messageList, Object bean) {
        try {
            method.invoke(bean, messageList);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    protected Message<?> getMessage(byte[] bytes) {
        String s = new String(bytes, CharsetUtil.CHARSET_UTF_8);
        try {
            return objectMapper.readValue(s, Message.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public AbstractMessageHandler(RedissonConnectionFactory connectionFactory, ObjectMapper objectMapper) {
        this.connectionFactory = connectionFactory;
        this.objectMapper = objectMapper;
    }

    /**
     * 执行消息监听逻辑
     * @param method 方法
     */
    public abstract void invokeMessage(Method method);

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    protected void consumer(Method method, Set<String> consumers, Object bean, Message<?> msg) {
        //Message<?> msg = getMessage(message);
        if (consumers.add(msg.getId())) {
            invokeMethod(method, msg, bean);
        } else {
            log.error("消息已经被消费 {}", msg);
        }
    }
    protected void consumer(Method method, Set<String> consumers, Object bean, List<Message<?>> msgList) {
        //Message<?> msg = getMessage(message);
        if (CollUtil.isEmpty(msgList)){
            return;

        }
        Iterator<Message<?>> iterator = msgList.iterator();
        while (iterator.hasNext()){
            Message<?> msg = iterator.next();
            if (!consumers.add(msg.getId())) {
                log.error("消息已经被消费 {}", msg);
                iterator.remove();
            }
        }
        if (CollUtil.isEmpty(msgList)){
            return;
        }
        invokeMethod(method, msgList, bean);

    }
}
