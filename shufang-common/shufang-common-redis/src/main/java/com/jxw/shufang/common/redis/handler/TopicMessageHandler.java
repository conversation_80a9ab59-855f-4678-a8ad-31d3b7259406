package com.jxw.shufang.common.redis.handler;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import com.jxw.shufang.common.core.exception.ServiceException;
import com.jxw.shufang.common.redis.annotation.MessageHandler;
import com.jxw.shufang.common.redis.annotation.MessageListener;
import com.jxw.shufang.common.redis.pojo.Message;
import com.jxw.shufang.common.redis.utils.RedisUtils;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.redisson.spring.data.connection.RedissonConnectionFactory;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 基于list实现的消息处理器
 */
@MessageHandler(value = MessageListener.HandlerMode.TOPIC)
@Slf4j
public class TopicMessageHandler extends AbstractMessageHandler {

    public TopicMessageHandler(ObjectMapper objectMapper, RedissonConnectionFactory connectionFactory) {
        super(connectionFactory,objectMapper);
    }

    @Override
    public void invokeMessage(Method method) {
        Set<String> consumers = new ConcurrentHashSet<>();
        MessageListener annotation = method.getAnnotation(MessageListener.class);
        String topic = getTopic(annotation);
        MessageListener.DataMode dataMode = annotation.dataMode();
        int i = annotation.dataSize();
        Class<?> declaringClass = method.getDeclaringClass();
        Object bean = applicationContext.getBean(declaringClass);
        RedissonClient client = RedisUtils.getClient();
        new Thread(() -> {
            while (true) {
                try {
                    RBlockingQueue<Message<?>> blockingQueue = client.getBlockingQueue(topic);
                    if (dataMode == MessageListener.DataMode.SINGLE){
                        Message<?> message = singleHandler(blockingQueue);
                        consumer(method,consumers,bean,message);
                    }else if (dataMode == MessageListener.DataMode.MULTI){
                        List<Message<?>> messages = multiHandler(blockingQueue, i);
                        consumer(method,consumers,bean,messages);
                    }

                } catch (InterruptedException e) {
                    log.error("topic message handler error", e);
                    throw new ServiceException(e.getMessage());
                }
            }
        }).start();
    }

    private Message<?> singleHandler(RBlockingQueue<Message<?>> blockingDeque) throws InterruptedException {
        return blockingDeque.take();
    }

    private List<Message<?>> multiHandler(RBlockingQueue<Message<?>> blockingDeque, int size) throws InterruptedException {
        List<Message<?>> resList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            //仅仅持久堵塞第一个元素
            if (i == 0) {
                resList.add(blockingDeque.take());
            }else {
                //等待一秒以后没有，直接返回
                Message<?> poll = blockingDeque.poll(1, TimeUnit.SECONDS);
                if (poll == null) {
                    break;
                }
                resList.add(poll);
            }
        }
        return resList;
    }

    private String getTopic(MessageListener annotation) {
        String value = annotation.value();
        String topic = annotation.topic();
        return StrUtil.isBlank(topic) ? value : topic;
    }

}
