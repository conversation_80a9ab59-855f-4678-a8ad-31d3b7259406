<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jxw.shufang</groupId>
    <artifactId>shufang-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        shufang-common-bom common依赖项
    </description>

    <properties>
        <revision>2.1.2</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-xxljob</artifactId>
                <version>${revision}</version>
            </dependency>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 字典 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-dict</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-dubbo</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-seata</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-loadbalancer</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-logstash</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-sentinel</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-skylog</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-prometheus</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-img</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-asynchandler</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-wxma</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-faceDetection</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.jxw.shufang</groupId>
                <artifactId>shufang-common-pay</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
