package com.jxw.shufang.gateway.destory;

import com.alibaba.nacos.client.naming.event.InstancesChangeEvent;
import com.alibaba.nacos.common.notify.Event;
import com.alibaba.nacos.common.notify.NotifyCenter;
import com.alibaba.nacos.common.notify.listener.Subscriber;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cache.Cache;
import org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheManager;
import org.springframework.cloud.loadbalancer.core.CachingServiceInstanceListSupplier;

/**
 * 注册中心-服务实例变更事件监听
 * <p>
 *     服务实例变更时清除LB客户端缓存和Dubbo服务缓存，解决服务发布时将流量打到一个已下线实例的问题
 * </p>
 */
@Slf4j
@AllArgsConstructor
public class ServiceInstanceChangeListener extends Subscriber<InstancesChangeEvent> implements InitializingBean, DisposableBean {

    private LoadBalancerCacheManager loadBalancerCacheManager;

    @Override
    public void onEvent(InstancesChangeEvent event) {
        String serviceName = event.getServiceName();
        log.info("【服务实例变更监听】需要刷新缓存的服务名称: {}", serviceName);
        Cache cache = loadBalancerCacheManager.getCache(CachingServiceInstanceListSupplier.SERVICE_INSTANCE_CACHE_NAME);
        if (cache == null) {
            log.warn("cache not found");
            return;
        }
        cache.evict(event.getServiceName());
    }

    @Override
    public Class<? extends Event> subscribeType() {
        return InstancesChangeEvent.class;
    }

    @Override
    public void destroy() throws Exception {
        NotifyCenter.deregisterSubscriber(this);
        log.info("【服务实例变更监听】实例销毁:{}", this.getClass().getName());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        NotifyCenter.registerSubscriber(this);
        log.info("【服务实例变更监听】实例初始化:{}", this.getClass().getName());
    }
}
