package com.jxw.shufang.gateway.destory;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.cloud.loadbalancer.cache.LoadBalancerCacheManager;
import org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter({LoadBalancerCacheAutoConfiguration.class})
public class ServiceInstanceChangeAutoConfig {

    @Bean
    public ServiceInstanceChangeListener serviceInstanceChangeListener(ApplicationContext applicationContext) {
        LoadBalancerCacheManager loadBalancerCacheManager = applicationContext.getBean(LoadBalancerCacheManager.class);
        return new ServiceInstanceChangeListener(loadBalancerCacheManager);
    }

}


